﻿using CommonLib;
using System;
using System.Collections.Generic;
using System.Collections.Specialized;
using System.Security.Cryptography;
using System.Text;
//using TencentCloud.Common.Profile;
//using TencentCloud.Common;
//using TencentCloud.Ocr.V20181119.Models;
//using TencentCloud.Ocr.V20181119;

namespace HanZiOcr
{
    /// <summary>
    /// https://cloud.tencent.com/document/product/866/33526
    /// </summary>
    public class TencentAPIAccurateRec : BaseOcrRec
    {
        public TencentAPIAccurateRec()
        {
            OcrGroup = OcrGroupType.腾讯;
            OcrType = HanZiOcrType.腾讯高精度;
            MaxExecPerTime = 23;

            LstJsonPreProcessArray = new List<object>() { "Response", "TextDetections" };
            IsSupportVertical = true;
            IsSupportUrlOcr = true;
            DicDeserializeVerticalJson = new Dictionary<string, string> { { "left", "X" }, { "top", "Y" }, { "height", "Height" }, { "width", "Width" }, { "location", "ItemPolygon" }, { "words", "DetectedText" } };
        }

        protected override string GetHtml(OcrContent content)
        {
            var postStr = string.IsNullOrEmpty(content.url) ? "{\"ImageBase64\":\"" + content.strBase64 + "\"}" : "{\"ImageUrl\":\"" + content.url + "\"}";
            var result = TencentCloudSignHelper.DoRequest("ocr", "ocr.tencentcloudapi.com", "ap-shanghai", "GeneralAccurateOCR", "2018-11-19", postStr);
            return result;
        }

        public override string GetHtmlByUrl(OcrContent content)
        {
            return GetHtml(content);
        }

    }
}