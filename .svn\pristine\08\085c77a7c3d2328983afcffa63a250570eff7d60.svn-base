﻿using System;
using System.Text;
using System.Net;
using System.Windows.Forms;
using System.Drawing;

namespace AccountTool
{
    class ValidateCode
    {
        private static bool isLoad = false;
        static byte[] m_buffer = new byte[4096];

        public static string RefreshCode(Image bitMap)
        {
            if (!isLoad)
            {
                isLoad = true;
                LoadLib();
            }
            string strCode = string.Empty;
            StringBuilder codeBuilder = new StringBuilder(8, 8);
            try
            {
                Image photo = new Bitmap(bitMap);

                using (System.IO.MemoryStream ms = new System.IO.MemoryStream())
                {
                    photo.Save(ms, System.Drawing.Imaging.ImageFormat.Gif);
                    byte[] imagedata = ms.GetBuffer();
                    if (imagedata != null)
                    {
                        codeBuilder.Length = 0;
                        if (SundayAPI.GetCodeFromBuffer(1, imagedata, imagedata.Length, codeBuilder))
                        {
                            strCode = codeBuilder.ToString();
                        }
                    }
                    ms.Close();
                    imagedata = null;
                }
                photo = null;
                photo.Dispose();
            }
            catch (Exception ex)
            {
            }
            return strCode;
        }

        private static void LoadLib()
        {
            byte[] buffer = Properties.Resources.data;
            if (!SundayAPI.LoadLibFromBuffer(buffer, buffer.Length, "123"))
            {
                MessageBox.Show("初始化API失败！", "警告", MessageBoxButtons.OK, MessageBoxIcon.Warning);
            }
        }

        public static string strRegImg = "https://dynamic.12306.cn/otsweb/passCodeNewAction.do?module=regist&rand=rrand";

        public static Image GetImgCode(out string strCode, ref CookieContainer cookie)
        {
            Image img = null;
            strCode = string.Empty;
            //验证码尝试10次，如果获取不到或者解析不了，让用户手动输入
            for (int j = 0; j < 10; j++)
            {
                try
                {
                    img = HTTP.RequestNoSyncImage(new HttpRequest()
                    {
                        Url = strRegImg,
                        OperationName = "获取验证码"
                    }, ref cookie);
                    if (img != null)
                    {
                        strCode = RefreshCode(img);
                        img = null;
                        img.Dispose();
                    }
                }
                catch (Exception ex)
                {
                    img = null;
                }
                if (!string.IsNullOrEmpty(strCode) && strCode.Length > 3)
                    break;
            }
            if (string.IsNullOrEmpty(strCode))
            {
                //todo 用户输入验证码
            }
            return img;
        }
    }
}
