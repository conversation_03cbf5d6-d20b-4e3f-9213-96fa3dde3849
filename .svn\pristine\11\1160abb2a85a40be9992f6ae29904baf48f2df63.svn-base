﻿using CommonLib;
using System;
using System.Collections.Generic;
using System.Collections.Specialized;
using System.IO;
using System.Web;

namespace TableOcr
{
    /// <summary>
    /// 腾讯优图-服务器计算Sign版
    /// https://open.youtu.qq.com/#/open/experience/table
    /// https://open.youtu.qq.com/#/open/developer/table
    /// </summary>
    public class YouTuLiteAppRec : BaseTableRec
    {
        public YouTuLiteAppRec()
        {
            OcrGroup = OcrGroupType.腾讯;
            OcrType = TableOcrType.腾讯优图Lite;
            MaxExecPerTime = 18;
            //IsSupportUrlOcr = true;

            LstJsonPreProcessArray = new List<object>() { "tableRes", "tables", 0, "cells" };
            LstJsonResultProcessArray = new List<object>() { "cell_content_text" };
            LstRowIndex = new List<object>() { "tl_row", "br_row" };
            LstColumnIndex = new List<object>() { "tl_col", "br_col" };
            RowIndexIsArray = false;
            IsRowIndexAddOne = true;
        }

        string strAppId = "10165442";
        string secretId = "AKIDY4VlTVQ6hAJxTrknYAOXwMSSRdkjMfQL";
        string strUserId = "10000";

        protected override string GetHtml(OcrContent content)
        {
            var byt = Convert.FromBase64String(content.strBase64);
            var result = GetOcrResult(byt, strAppId);
            return result;
        }

        //public override string GetHtmlByUrl(OcrContent content)
        //{
        //    string strPost = "{\"app_id\":\"" + strAppId + "\",\"url\":\"" + imgUrl + "\"}";
        //    var strTmp = WebClientSyncExt.GetHtml("https://open.youtu.qq.com/youtu/ocrapi/tableocr", strPost, ExecTimeOutSeconds);
        //    return strTmp;
        //}

        private string GetOcrResult(byte[] content, string appId)
        {
            var result = "";
            try
            {
                var url = "https://open.youtu.qq.com/proxyapi/ocrapi/tableocr";
                var file = new UploadFileInfo()
                {
                    Name = "image",
                    Filename = "test.png",
                    ContentType = "image/jpeg",
                    Stream = new MemoryStream(content)
                };
                NameValueCollection values = new NameValueCollection() { { "appid", appId }, { "cookie", "svc_openid=123123213213;svc_svctoken=12312321312312;" } };
                result = PostFile(url, new[] { file }, values);
            }
            catch (Exception)
            {

            }
            return result;
        }

    }
}