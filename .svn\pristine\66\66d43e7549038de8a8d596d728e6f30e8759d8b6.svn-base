﻿

using System;
using System.IO;
using System.Net.Sockets;
using NewTicket.Core;
namespace NewTicket.Tcp.Passive
{
    internal class PassiveStreamTcpEngine : PassiveBaseTcpEngine
    {
        private byte[] byte_0;
        private IStreamContractHelper istreamContractHelper_0;

        public PassiveStreamTcpEngine(AgileTcpClient server, IStreamContractHelper helper)
        {
            base.ServerIPEndPoint = server;
            this.istreamContractHelper_0 = helper;
        }
        /*
        public IStreamContractHelper method_3()
        {
            return this.istreamContractHelper_0;
        }

        public void method_4(IStreamContractHelper istreamContractHelper_1)
        {
            this.istreamContractHelper_0 = istreamContractHelper_1;
        }*/
        public IStreamContractHelper StreamContractHelper
        {
            get
            {
                return this.istreamContractHelper_0;
            }
            set
            {
                this.istreamContractHelper_0 = value;
            }
        }

        private void method_5(IAsyncResult iasyncResult_0)
        {
            NetworkStream asyncState = (NetworkStream)iasyncResult_0.AsyncState;
            try
            {
                int num = asyncState.EndRead(iasyncResult_0);
                if (num == 0)
                {
                    base.Dispose(asyncState, true);
                }
                else
                {
                    int messageHeaderLength = this.istreamContractHelper_0.MessageHeaderLength;
                    NetworkHelper.ReceiveData(asyncState, this.byte_0, num, messageHeaderLength - num);
                    int num3 = this.istreamContractHelper_0.ParseMessageBodyLength(this.byte_0);
                    byte[] dst = null;
                    if (num3 <= 0)
                    {
                        dst = (byte[])this.byte_0.Clone();
                    }
                    else
                    {
                        dst = new byte[this.byte_0.Length + num3];
                        Buffer.BlockCopy(this.byte_0, 0, dst, 0, this.byte_0.Length);
                        NetworkHelper.ReceiveData(asyncState, dst, this.byte_0.Length, num3);
                    }
                    asyncState.BeginRead(this.byte_0, 0, this.byte_0.Length, new AsyncCallback(this.method_5), asyncState);
                    base.AsynHandleMessage(dst);
                }
            }
            catch (Exception exception)
            {
                if ((exception is IOException) || (exception is ObjectDisposedException))
                {
                    base.Dispose(asyncState, true);
                }
                else if (!base.Disposed)
                {
                    base.iAgileLogger.Log(exception, "StriveEngine.Tcp.Passive.PassiveStreamTcpEngine.ReceiveCallback", ErrorLevel.High);
                }
            }
        }

        protected override void StartReceive()
        {
            this.byte_0 = new byte[this.istreamContractHelper_0.MessageHeaderLength];
            base.networkStream.BeginRead(this.byte_0, 0, this.byte_0.Length, new AsyncCallback(this.method_5), base.networkStream);
        }

        public override ContractFormatStyle ContractFormatStyle
        {
            get
            {
                return ContractFormatStyle.Stream;
            }
        }
    }

}