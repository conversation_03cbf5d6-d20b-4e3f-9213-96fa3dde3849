﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using System.IO;
using System.Threading.Tasks;
using System.Threading;
using NewTicket.MyControl;
using System.Runtime.InteropServices;
using NewTicket.Common;
using System.Linq;

namespace NewTicket
{
    public partial class FormMain : Form
    {

        #region 初始化

        //双缓冲
        public static void EnableDoubleBuffering(Control ctrl)
        {
            try
            {
                // Set the value of the double-buffering style bits to true.
                System.Reflection.PropertyInfo info = ctrl.GetType().GetProperty("DoubleBuffered", System.Reflection.BindingFlags.Instance | System.Reflection.BindingFlags.NonPublic);
                info.SetValue(ctrl, true, null);

                foreach (Control subCtrl in ctrl.Controls)
                {
                    EnableDoubleBuffering(subCtrl);
                }
            }
            catch { }
        }

        public FormMain()
        {
            InitializeComponent();
            //EnableDoubleBuffering(this);
            CheckForIllegalCrossThreadCalls = false;
            Control.CheckForIllegalCrossThreadCalls = false;
            SetTitle();
            cmbSeat.StrSpilt = "|";
            ddlSeat.StrSpilt = "|";
        }

        //响应和处理自定义消息
        protected override void DefWndProc(ref System.Windows.Forms.Message m)
        {
            switch (m.Msg)
            {
                case NativeMethods.TicketMsgType:
                    try
                    {
                        var message = Marshal.PtrToStringAnsi(m.WParam);
                        var ticket = JsonHelper.From<TicketEntity>(message);
                        if (ticket != null && !string.IsNullOrEmpty(ticket.strSubCode))
                        {
                            CommonMethod.SetLocalTicket(ticket.IsMobile, ticket);
                            //ShowMSG("收到本地查票信息：" + ticket.AttentionItem.Key);
                        }
                    }
                    catch
                    {
                        // ignored
                    }
                    break;
                default:
                    base.DefWndProc(ref m);
                    break;
            }
        }

        void speed_StatusChanged(object sender, NoticeArgs e)
        {
            try
            {
                DetermineCall(delegate
                {
                    if (e != null)
                        lblAuto.Text = e.From;
                });
            }
            catch (Exception oe)
            {
                Log.WriteError(oe);
            }
        }

        private void txtFrom_TxtChanged(object sender, TxtEvenargs e)
        {
            DropTextBox drp = sender as DropTextBox;
            if (drp == null)// || e.txt.Equals(cbxFrom.Text.Trim())
                return;
            if (isAuto)
            {
                isAuto = false;
            }
            else
            {
                string txt = e.txt.Trim(); //参数e中包含了textBox当前的文本内容  
                List<string> lstTmp = new List<string>();
                if (e.txt.Length >= drp.Minletters) //如果文本内容小于Minletters不查询数据库  
                {
                    try
                    {
                        var items = CommonString.Stations.Where(v =>
                            v.ShortCut.StartsWith(txt, StringComparison.OrdinalIgnoreCase)
                            || v.Name.StartsWith(txt, StringComparison.OrdinalIgnoreCase)
                            || v.Pinyin.StartsWith(txt, StringComparison.OrdinalIgnoreCase)
                            || v.Sipinyin.StartsWith(txt, StringComparison.OrdinalIgnoreCase)).ToList();
                        if (items != null && items.Count > 0)
                        {
                            foreach (TrainStation station in items)
                            {
                                lstTmp.Add(station.Name);
                            }
                        }
                    }
                    catch (Exception oe)
                    {
                        //MessageBox.Show("Test");
                    }
                }
                drp.DropItems = lstTmp.ToArray();//赋值给DropItems  
            }
            try
            {
                if (drp.Name != "cbxFrom")
                    return;
                string strTmp = CommonMethod.GetPreSellingTimeStr(cbxFrom.Text.Trim());
                lblTime.Text = string.Format("{0}起售", string.IsNullOrEmpty(strTmp) ? "--" : strTmp.ToString());
                decimal nMSecond = nQueryMSecond.Value;
                dtRefreshTick.Value = CommonMethod.GetPreSellingTimeDateByTime(strTmp, ref nMSecond, 10);
                nQueryMSecond.Value = nMSecond;
                if (dtRefreshTick.Checked)
                {
                    dtRefreshTick.Checked = false;
                }
                //dtSubTick.Value = CommonMethod.GetPreSellingTimeDate(cbxFrom.Text.Trim()).AddSeconds(5);
                //if (dtSubTick.Checked)
                //{
                //    dtSubTick.Checked = false;
                //}
                //if (rdoLimit.Checked)
                //{
                //    rdoNoLimit.Checked = true;
                //}
                //if (!string.IsNullOrEmpty(cbxFrom.Text))
                //{
                //    var fromSation = Array.Find(CommonString.Stations, v => v.Name == cbxFrom.Text);
                //    if (fromSation != null && !string.IsNullOrEmpty(fromSation.Name))
                //    {
                //        CommonMethod.SortLstIP(fromSation.Name);
                //    }
                //}
            }
            catch (Exception oe)
            {
                Log.WriteError(oe);
                throw;
            }
        }

        void dvLog_DataError(object sender, DataGridViewDataErrorEventArgs e)
        {
            //if (CommonString.isDebug)
            //{
            //    MessageBox.Show(e.Context.ToString());
            //}
        }

        private void FormMain_Shown(object sender, EventArgs e)
        {
            ShowMSG("欢迎使用" + Settings.Default.AppName + "！");
            bgPublic.RunWorkerAsync();
            bgSaveInfo.RunWorkerAsync();
            //CommonMSG.SetIndex();
            Init();
            if ((!File.Exists(CommonString.strIPListPath) && CommonString.IsComCanUse)
                || (!File.Exists(CommonString.strMobileIPListPath) && CommonString.IsMobileCanUse))
            {
                MessageBox.Show(this, "为保证订票速度稳定，第一次使用时，请先对12306服务器进行一次测速。", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                网络加速QToolStripMenuItem_Click(null, null);
            }
            try
            {
                nMaxCodeWait.Value = CommonString.NMaxWaitCodeTime;
            }
            catch (Exception oe)
            {
                Console.WriteLine(oe.Message);
            }
            NewTicketHelper.CheckOrder();
        }

        private void BindData()
        {
            try
            {
                LoadCheckCode();
            }
            catch (Exception oe)
            {
                Log.WriteError(oe);
            }
            try
            {
                DetermineCall(delegate
                {
                    dtGoDate.Tag = CommonMethod.MaxDate.ToString("yyyy-MM-dd");
                    dtAttention.TreeDropDown.Nodes.Clear();
                    dtGoDate.MinDate = DateTime.Today;
                    dtGoDate.MaxDate = CommonMethod.MaxDate.AddDays(3);
                    //dtAttention.TreeDropDown.TreeViewNodeSorter = null;
                    for (DateTime i = dtGoDate.MinDate; i <= dtGoDate.MaxDate; )
                    {
                        dtAttention.TreeDropDown.Nodes.Add(i.ToString("yyyy-MM-dd"));
                        i = i.AddDays(1);
                    }
                });
            }
            catch (Exception oe)
            {
                Log.WriteError(oe);
            }
        }

        private void LoadCheckCode()
        {
            ThreadPool.QueueUserWorkItem((object obj) =>
            {
                string strFile = Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData).TrimEnd('\\') + "\\" + System.Web.Security.FormsAuthentication.HashPasswordForStoringInConfigFile(CommonString.serverTime.ToString("yyyyMMdd"), "MD5") + ".sconfig";
                try
                {
                    if (!CommonMethod.isCheckCodeCanUse())
                    {
                        try
                        {
                            if (!string.IsNullOrEmpty(strFile) && File.Exists(strFile))
                            {
                                File.Delete(strFile);
                            }
                        }
                        catch { }
                    }
                }
                catch (Exception oe)
                {
                    Log.WriteError(oe);
                }
                try
                {
                    if (isFirst)
                    {
                        DetermineCall(delegate
                        {
                            isFirst = false;
                            cmsOpType.Enabled = CommonMethod.isCheckCodeCanUse();
                            if (cmsOpType.Enabled)
                            {
                                //rdoMobile.Checked = true;
                                //电脑版默认暂时不可用
                                //tdoShuangHe.Checked = true;
                            }
                        });
                    }
                }
                catch (Exception oe)
                {
                    Log.WriteError(oe);
                }
            });
        }

        private void Init()
        {
            try
            {
                //chkAutoNoCode.Checked = CommonString.IsCanNoCode;
                btnJSEval.Visible = CommonString.isDebug;
                cmsOpType.SelectedIndex = 1;
                _BindingLeftTicketStatus = new BindingList<TicketEntity>(_LeftTicketStatus);

                dvLog.DataError += new DataGridViewDataErrorEventHandler(dvLog_DataError);

                dgQuery.AutoGenerateColumns = false;
                dgQuery.DataError += new DataGridViewDataErrorEventHandler(dvLog_DataError);
                dgQuery.DataSource = _BindingLeftTicketStatus;
                dgQuery.Columns["TrainNo"].DefaultCellStyle.Font = new Font("微软雅黑", 12, FontStyle.Bold);
                dgQuery.Columns["TrainNo"].DefaultCellStyle.ForeColor = Color.Blue;

                tipTypeInfo.SetToolTip(btnImport, @"文本格式如下，满足任一条件即可：

★姓名,证件号,席别[可为空],车票类型[可为空],证件类型[可为空],分配账号[可为空]
如：
  王华,310225195702230016
  王华,310225195702230016,硬卧|二等座
  王华,310225195702230016,硬卧|二等座,残军
  王华,310225195702230016,硬卧|二等座,成人,二代
  王华,310225195702230016,硬卧|二等座,成人,二代,分配账号

★席别为空时，默认硬卧
车票类型为空时，默认成人票
证件类型为空时，默认二代身份证");
                cmsStart.Image = imgQiangPiao.Images[0];
                lnkCode.Visible = CommonString.isDebug;
                srcTxt.Visible = true;// !CommonString.isDebug;


                //chkNMore.Checked = Settings.Default.IsNMode;
                //chkAutoCancel.Checked = Settings.Default.IsAutoCancel;


                //nMaxSubCount.Enabled = CommonString.isDebug;
                chkAutoIP.Checked = Settings.Default.IsAutoChangIP;
                //chkTongCheng.Checked = CommonString.IsAutoTongCheng;
                //BindData();
                chkAllTCode.Checked = true;
                isAuto = true;
                cbxFrom.Text = Settings.Default.LastFrom;
                isAuto = true;
                cbxTo.Text = Settings.Default.LastTo;
                //chkAutoPassenger.Checked = CommonString.IsAutoPassenger;
                isAuto = true;
                cmbCodeType.SelectedIndex = Settings.Default.IsUserAutoCode ? 0 : 1;
                isAuto = false;
                nMaxQueryTick.Value = CommonString.NMaxQueryTimeOut;
                //cbxFrom.TxtChanged += new DropTextBox.TxtChangedHandle(txtFrom_TxtChanged);
                //cbxTo.TxtChanged += new DropTextBox.TxtChangedHandle(txtFrom_TxtChanged);
                DateTime dtTemp = CommonString.serverTime;
                if (!string.IsNullOrEmpty(Settings.Default.LastDate))
                {
                    DateTime.TryParse(Settings.Default.LastDate, out dtTemp);
                }
                if (dtTemp > dtGoDate.MaxDate || dtTemp.Year <= 1900 || dtTemp.Date < DateTime.Now.Date)
                {
                    dtTemp = dtGoDate.MaxDate;
                    Settings.Default.LastDate = dtTemp.ToString("yyyy-MM-dd");
                }
                dtGoDate.Value = dtTemp;

                nMaxLogNum.Value = CommonString.NLogCount;
                QueryTick.Value = Settings.Default.NQueryTick;
                isAuto = true;
                chkTC.Checked = true;
                chkRepeatLog.Checked = Settings.Default.IsRepeatLog;
                isAuto = false;
                if (chkTC.Checked)
                {
                    List<string> lstTm = new List<string>();
                    lstTm.AddRange(Settings.Default.LastFromTC.Split(new string[] { "|" }, StringSplitOptions.RemoveEmptyEntries));
                    BindListViewGroup(lvStartN, lstTm);
                    lstTm = new List<string>();
                    lstTm.AddRange(Settings.Default.LastToTC.Split(new string[] { "|" }, StringSplitOptions.RemoveEmptyEntries));
                    BindListViewGroup(lvEndN, lstTm);
                }
                txtTrainNo.Text = Settings.Default.LastTrainNo;
                rdoLimit.Checked = !string.IsNullOrEmpty(txtTrainNo.Text.Trim());
            }
            catch (Exception oe)
            {
                Log.WriteError(oe);
            }

            try
            {
                //ImageColumn icon = new ImageColumn("", 15);
                //ComboBoxColumn cmbUser = new ComboBoxColumn("用 户", 85);
                //TextColumn txtName = new TextColumn("乘 客", 45);
                //ComboBoxColumn cmbType = new ComboBoxColumn("类 别", 60);
                //ComboBoxColumn cmbXiBie = new ComboBoxColumn("席 别", 60);
                //ComboBoxColumn cmbCardType = new ComboBoxColumn("证件类型", 80);
                //TextColumn txtCardNo = new TextColumn("证件号码", 120);
                ////ImageColumn btnDelete = new ImageColumn("", 25);

                //ComboBoxCellEditor vcUser = new XPTable.Editors.ComboBoxCellEditor();
                //vcUser.DropDownStyle = XPTable.Editors.DropDownStyle.DropDownList;
                ////vcUser.Items.AddRange(CommonString.UserTypeNames.ToArray());
                //cmbUser.Editor = vcUser;

                //ComboBoxCellEditor vcType = new XPTable.Editors.ComboBoxCellEditor();
                //vcType.DropDownStyle = XPTable.Editors.DropDownStyle.DropDownList;
                //vcType.Items.AddRange(CommonString.UserTypeNames.ToArray());
                //cmbType.Editor = vcType;
                //ComboBoxCellEditor vcCardType = new XPTable.Editors.ComboBoxCellEditor();
                //vcCardType.DropDownStyle = XPTable.Editors.DropDownStyle.DropDownList;
                //vcCardType.Items.AddRange(CommonString.IDCardTypeNames.ToArray());
                //cmbCardType.Editor = vcCardType;
                //ComboBoxCellEditor vcTicketType = new XPTable.Editors.ComboBoxCellEditor();
                //vcTicketType.DropDownStyle = XPTable.Editors.DropDownStyle.DropDownList;
                //vcTicketType.Items.AddRange(CommonString.SeatTypeNames);
                //cmbXiBie.Editor = vcTicketType;

                //this.table.ColumnModel = new ColumnModel(new Column[] { icon, cmbUser, txtName, cmbType, cmbXiBie, cmbCardType, txtCardNo });
                //this.table.TableModel = new TableModel(new Row[] { });

                //table.ContextMenuStrip = cmsPassenger;
            }
            catch (Exception oe)
            {
                Log.WriteError(oe);
            }
            try
            {
                foreach (string str in CommonString.SeatTypeNames)
                {
                    ToolStripMenuItem item = new ToolStripMenuItem();
                    item.Text = str;
                    item.Click += new EventHandler(ticketType_Click);
                    cmsTicketTypes.DropDownItems.Add(item);
                }
                foreach (string str in CommonString.UserTypeNames)
                {
                    ToolStripMenuItem item = new ToolStripMenuItem();
                    item.Text = "全部" + str;
                    item.Click += new EventHandler(userType_Click);
                    cmsTypes.DropDownItems.Add(item);
                }
                cmsTypes.DropDownItems.Add(new ToolStripSeparator());
                foreach (string str in CommonString.UserTypeNames)
                {
                    ToolStripMenuItem item = new ToolStripMenuItem();
                    item.Text = str;
                    item.Click += new EventHandler(userType_Click);
                    cmsTypes.DropDownItems.Add(item);
                    ddlUType.Items.Add(str);
                }
                foreach (var str in CommonString.IDCardTypes)
                {
                    ToolStripMenuItem item = new ToolStripMenuItem();
                    item.Text = "全部" + str.Key;
                    item.Click += new EventHandler(userCardType_Click);
                    cmsCardTypes.DropDownItems.Add(item);
                }
                cmsCardTypes.DropDownItems.Add(new ToolStripSeparator());
                foreach (var str in CommonString.IDCardTypes)
                {
                    ToolStripMenuItem item = new ToolStripMenuItem();
                    item.Text = str.Key;
                    item.Click += new EventHandler(userCardType_Click);
                    cmsCardTypes.DropDownItems.Add(item);
                }
                foreach (var item in CommonString.SeatTypeNames)
                {
                    cmbSeat.TreeDropDown.Nodes.Add(item);
                    ddlSeat.TreeDropDown.Nodes.Add(item);
                }
            }
            catch (Exception oe)
            {
                Log.WriteError(oe);
            }
            try
            {
                SetNowQuery();
                chkAutoPassenger_CheckedChanged(null, null);
                if (File.Exists(Application.StartupPath.TrimEnd('\\') + "\\last.txt"))
                {
                    string msg = string.Empty;
                    LoadPassFormFile(Application.StartupPath.TrimEnd('\\') + "\\last.txt", ref  msg);
                }
            }
            catch (Exception oe)
            {
                Log.WriteError(oe);
            }
            if (srcTxt.Visible)
            {
                bgMSG.RunWorkerAsync();
            }
            //捕获控件的错误
            //wbQQ.ScriptErrorsSuppressed = true;
            //wbQQ.Document.Window.Error += new HtmlElementErrorEventHandler(Window_Error);

            CommonThreadInfo.Start();
            //ThreadPool.QueueUserWorkItem(new WaitCallback(obj =>
            //{
            //    IPHelper.LoadAllIPList();
            //}));
            ThreadPool.QueueUserWorkItem((object obj) =>
            {
                while (!CommonString.isExit)
                {
                    try
                    {
                        DetermineCall(delegate
                        {
                            lblDateTime.Text = CommonString.serverTime.ToString("yyyy-MM-dd HH:mm:ss");
                            if (lblCDN.Text != string.Format("电[{0}]手[{1}]", IPHelper.lstComEnableIP.Count, IPHelper.lstMobileEnableIP.Count))
                                lblCDN.Text = string.Format("电[{0}]手[{1}]", IPHelper.lstComEnableIP.Count, IPHelper.lstMobileEnableIP.Count);
                        });
                        if (CommonString.serverTime.Minute == 5 && CommonString.serverTime.Second == 0)
                        {
                            CommonMethod.RemoveForbiden();
                        }
                        ShowMSG();
                    }
                    catch (Exception oe)
                    {
                        Log.WriteError(oe);
                    }
                    System.Threading.Thread.Sleep(500);
                }
            });
            //new System.Threading.Thread(delegate()
            //{
            //    while (!CommonString.isExit)
            //    {
            //        try
            //        {
            //            CommonMethod.GetQueryTicketURL();
            //        }
            //        catch (Exception oe)
            //        {
            //            Log.WriteError(oe);
            //        }
            //        System.Threading.Thread.Sleep(10000);
            //    }
            //}) { Priority = System.Threading.ThreadPriority.Highest, IsBackground = true }.Start();
            btnQuery_Click(null, null);
        }

        void ticketType_Click(object sender, EventArgs e)
        {
            ToolStripMenuItem seleItem = (ToolStripMenuItem)sender;
            if (seleItem != null)
            {
                seleItem.Checked = !seleItem.Checked;
                if (lvPassenger.SelectedItems != null && lvPassenger.SelectedItems.Count > 0)
                {
                    string strTmp = "";
                    foreach (ToolStripMenuItem item in cmsTicketTypes.DropDownItems)
                    {
                        if (item.Checked)
                        {
                            strTmp += string.Format("{0}|", item.Text);
                        }
                    }
                    strTmp = strTmp.TrimEnd('|');
                    foreach (ListViewItem lv in lvPassenger.SelectedItems)
                    {
                        //姓名,类别,席别,证件,证件号
                        lv.SubItems[2].Text = strTmp;
                    }
                }
            }
            //if (table.SelectedItems != null && table.SelectedItems.Length > 0)
            //    table.SelectedItems[0].Cells[4].Text = item.Text;
        }

        void userType_Click(object sender, EventArgs e)
        {
            ToolStripMenuItem item = (ToolStripMenuItem)sender;
            if (item.Text.StartsWith("全部"))
            {
                if (lvPassenger.Items != null && lvPassenger.Items.Count > 0)
                {
                    foreach (ListViewItem lv in lvPassenger.Items)
                    {
                        //姓名,类别,席别,证件,证件号
                        lv.SubItems[1].Text = item.Text.Replace("全部", "");
                    }
                }
                //if (table.TableModel != null && table.TableModel.Rows.Count > 0)
                //{
                //    foreach (Row row in table.TableModel.Rows)
                //    {
                //        row.Cells[3].Text = item.Text.Replace("全部", "");
                //    }
                //}
            }
            else
            {
                if (lvPassenger.SelectedItems != null && lvPassenger.SelectedItems.Count > 0)
                {
                    foreach (ListViewItem lv in lvPassenger.SelectedItems)
                    {
                        //姓名,类别,席别,证件,证件号
                        lv.SubItems[1].Text = item.Text;
                    }
                }
                //if (table.SelectedItems != null && table.SelectedItems.Length > 0)
                //    table.SelectedItems[0].Cells[3].Text = item.Text;
            }
        }

        void userCardType_Click(object sender, EventArgs e)
        {
            ToolStripMenuItem item = (ToolStripMenuItem)sender;
            if (item.Text.StartsWith("全部"))
            {
                if (lvPassenger.Items != null && lvPassenger.Items.Count > 0)
                {
                    foreach (ListViewItem lv in lvPassenger.Items)
                    {
                        //姓名,类别,席别,证件,证件号
                        lv.SubItems[3].Text = item.Text.Replace("全部", "");
                    }
                }
            }
            else
            {
                if (lvPassenger.SelectedItems != null && lvPassenger.SelectedItems.Count > 0)
                {
                    foreach (ListViewItem lv in lvPassenger.SelectedItems)
                    {
                        //姓名,类别,席别,证件,证件号
                        lv.SubItems[3].Text = item.Text;
                    }
                }
            }
        }

        private void FormMain_Load(object sender, EventArgs e)
        {
            //姓名,类别,席别,证件,证件号
            ColumnHeader columnHeader0 = new ColumnHeader();
            columnHeader0.Text = "姓名";
            columnHeader0.Width = 45;
            ColumnHeader columnHeader1 = new ColumnHeader();
            columnHeader1.Text = "类别";
            columnHeader1.Width = 50;
            ColumnHeader columnHeader2 = new ColumnHeader();
            columnHeader2.Text = "席别";
            columnHeader2.Width = 80;
            ColumnHeader columnHeader3 = new ColumnHeader();
            columnHeader3.Text = "证件";
            columnHeader3.Width = 45;
            ColumnHeader columnHeader4 = new ColumnHeader();
            columnHeader4.Text = "证件号";
            columnHeader4.Width = 125;
            ColumnHeader columnHeader5 = new ColumnHeader();
            columnHeader5.Text = "状态";
            columnHeader5.Width = 50;
            lvPassenger.Columns.AddRange(new ColumnHeader[] { columnHeader0, columnHeader1, columnHeader2, columnHeader3, columnHeader4, columnHeader5 });

            Check12306Cert();

            //this.cpuCounter = new System.Diagnostics.PerformanceCounter();
            ////this.ramCounter = new System.Diagnostics.PerformanceCounter("Memory", "Available MBytes");

            //this.cpuCounter.CategoryName = "Processor";
            //this.cpuCounter.CounterName = "% Processor Time";
            //this.cpuCounter.InstanceName = "_Total";
        }

        //protected override void OnPaintBackground(PaintEventArgs e)
        //{
        //    base.OnPaintBackground(e);

        //    System.Drawing.Drawing2D.GraphicsPath buttonPath = new System.Drawing.Drawing2D.GraphicsPath();
        //    Rectangle newRectangle = btnQuery.ClientRectangle;
        //    newRectangle.Inflate(-7, -4);
        //    e.Graphics.DrawEllipse(Pens.SkyBlue, newRectangle);
        //    buttonPath.AddEllipse(newRectangle);
        //    btnQuery.Region = new Region(buttonPath);
        //}

        private void FormMain_FormClosing(object sender, FormClosingEventArgs e)
        {
            if (CommonString.isExit)
                SaveLastInfo();
            CommonString.isExit = true;
            this.Hide();
            CommonMethod.Exit();
        }

        /// <summary>
        /// 验证12306.cn自签名的根证书
        /// </summary>
        private void Check12306Cert()
        {
            return;
            //X509Store store = new X509Store(StoreName.Root, StoreLocation.CurrentUser);
            //store.Open(OpenFlags.ReadWrite);
            //var certs = store.Certificates.Find(X509FindType.FindByThumbprint, "AE3F2E66D48FC6BD1DF131E89D768D505DF14302", true);
            //if (certs.Count == 0)
            //{
            //    var srcaRootCa = new X509Certificate2(Properties.Resources.srca);
            //    MessageBox.Show(this, "没有安装12306.cn自签名的根证书，将自动为您安装。\n请在接下来弹出的安装提示中，点击“是”以安装证书。", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
            //    try
            //    {
            //        store.Add(srcaRootCa);
            //    }
            //    catch
            //    {

            //    }
            //}
            //store.Close();
        }
        #endregion

        #region 日志显示部分

        private void btnClearLog_Click(object sender, EventArgs e)
        {
            try
            {
                DetermineCall(delegate
                {
                    dvLog.Rows.Clear();
                    rtbNewLog.Clear();
                });
            }
            catch { }
        }

        /// <summary>
        /// 显示提示信息
        /// </summary>
        /// <param name="strMsg">提示信息</param>
        /// <param name="type">消息类别，0：正常消息，1：错误信息</param>
        private void ShowMSG(string strMsg, string strFrom = "助手", int type = 0)
        {
            ShowMSG(strMsg, CommonString.serverTime, string.IsNullOrEmpty(strFrom) ? "--" : strFrom, type);
        }

        public static List<NoticeArgs> lstNotice = new List<NoticeArgs>();

        /// <summary>
        /// 共享锁
        /// </summary>
        private static readonly object lockkey = new object();

        public static void AddMSG(NoticeArgs[] lstTmp)
        {
            try
            {
                if (CommonString.IsNoLog)
                    return;
                lock (lockkey)
                {
                    foreach (var item in lstTmp)
                    {
                        try
                        {
                            lstNotice.Add(item);
                        }
                        catch (Exception ex)
                        {
                            Console.WriteLine(ex.Message);
                        }
                    }
                }
            }
            catch (Exception oe)
            {
                Log.WriteError(oe);
            }
            finally
            {
                lstTmp = null;
            }
        }

        public static void AddMSG(NoticeArgs notice)
        {
            try
            {
                lock (lockkey)
                {
                    lstNotice.Add(notice);
                }
            }
            catch (Exception oe)
            {
                Log.WriteError(oe);
            }
        }

        private string GetNowMSG()
        {
            StringBuilder sbMSG = new StringBuilder();
            var lstTmpMSG = new List<NoticeArgs>();
            try
            {
                lock (lockkey)
                {
                    if (lstNotice != null && lstNotice.Count > 0)
                    {
                        lstNotice.ForEach(delegate(NoticeArgs not)
                        {
                            try
                            {
                                lstTmpMSG.Add(not);
                            }
                            catch (Exception oe)
                            {
                                Log.WriteError(oe);
                            }
                        });
                        //lstTmp.AddRange(lstNotice.ToArray());
                        lstNotice = new List<NoticeArgs>();
                    }
                }
                if (lstTmpMSG != null && lstTmpMSG.Count > 0)
                {
                    try
                    {
                        lstTmpMSG.Sort(CommonMethod.CompareTimeStr);
                    }
                    catch { }
                    lstTmpMSG.ForEach(delegate(NoticeArgs no)
                    {
                        try
                        {
                            if (no != null && !string.IsNullOrEmpty(no.Context))
                                sbMSG.AppendFormat("{0} {1}:{2}\n", no.dtLog, (string.IsNullOrEmpty(no.From) ? "助手" : no.From), no.Context);
                            //if (CommonString.IsAutoLog)
                            //    LogManager.WriteLog(string.Format("{0}:{1}", (string.IsNullOrEmpty(no.From) ? "助手" : no.From), no.Context), no.dtLog);
                        }
                        catch (Exception oe)
                        {
                            Log.WriteError(oe);
                        }
                    });
                }
            }
            catch (Exception oe)
            {
                Log.WriteError(oe);
            }
            finally
            {
                lstTmpMSG = null;
            }
            return sbMSG.ToString();
        }

        private void ShowMSG()
        {
            string strMsg = "";
            try
            {
                strMsg = GetNowMSG();
                if (!string.IsNullOrEmpty(strMsg))
                {
                    UpdateMsgMethod(strMsg);
                }
            }
            catch (Exception oe)
            {
                Log.WriteError(oe);
            }
            finally
            {
                strMsg = null;
            }
        }
        private void UpdateMsgMethod(string strMsg)
        {
            if (!string.IsNullOrEmpty(strMsg))
            {
                DetermineCall(delegate
                {
                    //DetermineCall(delegate
                    //{
                    if (rtbNewLog.Lines.Length >= nMaxLogNum.Value)//rtbLog.Lines.Length >= nMaxLogNum.Value)
                    {
                        //rtbLog.Clear();
                        rtbNewLog.Clear();
                        strMsg = strMsg.Trim();
                        //Application.DoEvents();
                    }
                    //rtbLog.AppendText(strMSG);
                    //rtbLog.ScrollToCaret();
                    //if (rtbLog.SelectionStart != 0)
                    //    rtbLog.SelectionStart = 0;
                    //rtbLog.SelectedText = strMSG;
                    //if (rtbNewLog.SelectionStart != 0)
                    rtbNewLog.SelectionStart = 0;
                    rtbNewLog.SelectedText = strMsg;
                    //rtbNewLog.AppendText(strMSG);
                    //rtbNewLog.SelectionStart = 0;
                    //rtbNewLog.InsertText(strMSG);
                    //rtbNewLog.Text = strMSG + rtbNewLog.Text;
                    //rtbNewLog.SelectionStart = 0;
                    //nLogCount++;
                    //});
                    Application.DoEvents();
                });
            }
        }

        /// <summary>
        /// 显示提示信息
        /// </summary>
        /// <param name="strMsg">提示信息</param>
        /// <param name="type">消息类别，0：正常消息，1：错误信息</param>
        private void ShowMSG(string strMsg, DateTime dtLog, string strFrom = "助手", int type = 0)
        {
            if (strMsg.HorspoolIndex("系统忙") >= 0 || strMsg.HorspoolIndex("该联系人已存在") >= 0 || strMsg.HorspoolIndex("已添加") >= 0)
                return;
            try
            {
                //if (CommonString.IsAutoLog)
                //    LogManager.WriteLog(string.Format("{0}:{1}", (string.IsNullOrEmpty(strFrom) ? "助手" : strFrom), strMSG), dtLog);
                strMsg = string.Format("{0} {1}:{2}\n", dtLog.ToString("HH:mm:ss fff"), (string.IsNullOrEmpty(strFrom) ? "助手" : strFrom), strMsg);

                UpdateMsgMethod(strMsg);
            }
            catch
            {
                // ignored
            }
            strMsg = null;
            strFrom = null;
            //lvMSG.Items.Add("连接服务器……");
            //Application.DoEvents();
        }
        #endregion

        #region 通用方法

        private void DetermineCall(MethodInvoker method)
        {
            if (InvokeRequired)
            {
                //BeginInvoke(method);
                Invoke(method);
            }
            else
            {
                method();
            }
        }

        private void SetTitle()
        {
            lblVersion.Text = string.Format("到期：{0}", CommonReg.DtExpired.ToString("yyyy-MM-dd HH:mm"));
            //lblVersion.Text = string.Format("类型:【{0}】", CommonReg.NowUserType);//CommonString.strNowVersion,
            this.Text = string.Format("{0} --【{1}】{2}", Settings.Default.AppName + (CommonString.IsBeta ? "Beta版" : "")
                , CommonReg.NowUserType
                , CommonString.isDebug ? string.Format("#{0}", CommonString.NowIndex) : "");
            lblUpdate.Text = string.Format("检查更新({0})", CommonString.dtNowDate.ToString("yyyy-MM-dd HH:mm:ss"));
        }
        #endregion

        #region 自动登录部分

        private void cmsLogin_Click(object sender, EventArgs e)
        {
            FormLogin login = new FormLogin();
            login.lstLoginUser = new List<UserEntity>();
            login.lstLoginUser.AddRange(CommonString.LstUser);
            if (login.ShowDialog(this) == System.Windows.Forms.DialogResult.Yes)
            {
                foreach (UserEntity user in login.lstLoginUser)
                {
                    if (CommonString.LstUser.Exists((UserEntity uu) => uu.StrNowUserName.Equals(user.StrNowUserName)))
                    {
                        if (CommonString.LstUser.Exists((UserEntity uu) => uu.StrNowUserName.Equals(user.StrNowUserName) && uu.StrNowUserPWD.Equals(user.StrNowUserPWD)))
                        {
                            continue;
                        }
                        //lstUser.Find((UserEntity uu) => uu.StrNowUserName.Equals(user.StrNowUserName)).IsMobile = CommonString.IsMobile;
                        CommonString.LstUser.Find((UserEntity uu) => uu.StrNowUserName.Equals(user.StrNowUserName)).StrNowUserPWD = user.StrNowUserPWD;
                        CommonString.LstUser.Find((UserEntity uu) => uu.StrNowUserName.Equals(user.StrNowUserName)).NeedRelogin();
                        CommonString.LstUser.Find((UserEntity uu) => uu.StrNowUserName.Equals(user.StrNowUserName)).isAccountError = false;
                    }
                    else
                    {
                        CommonString.LstUser.Add(new UserEntity()
                        {
                            StrNowUserName = user.StrNowUserName
                            ,
                            StrNowUserPWD = user.StrNowUserPWD
                            ,
                            //IsMobile = CommonString.IsMobile
                        });
                    }
                }
                //移除没有的账户
                for (int i = 0; i < CommonString.LstUser.Count; i++)
                {
                    //lstUser[i].StatusChanged -= new NoticeEvent(user_StatusChanged);
                    if (!login.lstLoginUser.Exists((UserEntity uu) => uu.StrNowUserName.Equals(CommonString.LstUser[i].StrNowUserName)))
                    {
                        CommonString.LstUser[i].IsExit = true;
                        CommonString.LstUser.RemoveAt(i);
                        i--;
                        continue;
                    }
                    //lstUser[i].StatusChanged += new NoticeEvent(user_StatusChanged);
                }
                if (CommonString.LstUser == null || CommonString.LstUser.Count <= 0)
                    IPHelper.nowComIndex = -1;
            }
            try
            {
                cmbEditUser.DropDownItems.Clear();
                {
                    ToolStripMenuItem item = new ToolStripMenuItem();
                    item.Text = "未分配";
                    item.Click += new EventHandler(cmbEditUser_Click);
                    cmbEditUser.DropDownItems.Add(item);
                }
                if (CommonString.LstUser != null && CommonString.LstUser.Count > 0)
                {
                    foreach (UserEntity str in CommonString.LstUser)
                    {
                        ToolStripMenuItem item = new ToolStripMenuItem();
                        item.Text = str.StrNowUserName;
                        item.Click += new EventHandler(cmbEditUser_Click);
                        cmbEditUser.DropDownItems.Add(item);
                    }
                    //new System.Threading.Thread(delegate()
                    //{
                    //    NewTicketHelper.DoLoginAll();
                    //}) { IsBackground = true, Priority = System.Threading.ThreadPriority.Highest }.Start();
                }
                //foreach (UserEntity uu in lstUser)
                //{
                //    vcUser.Items.Add(uu.StrNowUserName);
                //}
            }
            catch (Exception oe)
            {
                Log.WriteError(oe);
            }
            try
            {
                login.lstLoginUser = null;
                login.Close();
                login.Dispose();
                login = null;
            }
            catch { }
            //try
            //{
            //    //if (lvPassenger.SelectedItems != null && lvPassenger.SelectedItems.Count > 0)
            //    //{
            //    //    foreach (ListViewItem lv in lvPassenger.Items)
            //    //    {
            //    //        //姓名,类别,席别,证件,证件号
            //    //        lv.SubItems[1].Text = item.Text;
            //    //    }
            //    //}
            //    //foreach (Row row in table.TableModel.Rows)
            //    //{
            //    //    //icon, cmbUser, txtName, cmbType, cmbCardType, txtCardNo, cmbXiBie
            //    //    if (!lstUser.Exists(pp => pp.StrNowUserName.Equals(row.Cells[1].Text)))
            //    //    {
            //    //        row.Cells[1].Text = "";
            //    //    }
            //    //}
            //}
            //catch (Exception oe)
            //{
            //    Log.WriteError(oe);
            //}
        }

        void user_StatusChanged(object sender, NoticeArgs e)
        {
            try
            {
                if (e != null)
                {
                    if (e.Context.HorspoolIndex("系统忙") >= 0 || e.Context.HorspoolIndex("该联系人已存在") >= 0 || e.Context.HorspoolIndex("已添加") >= 0)
                        return;
                    AddMSG(e);
                    //ShowMSG(e.Context, e.TimeStr, e.From, e.NType);
                }
            }
            catch { }
        }
        #endregion

        #region 选择乘车日期

        private void lnkToday_LinkClicked(object sender, LinkLabelLinkClickedEventArgs e)
        {
            LinkLabel lnk = sender as LinkLabel;
            if (lnk == null || lnk.Tag == null)
                return;
            dtGoDate.Value = CommonString.serverTime.Date.AddDays(int.Parse(lnk.Tag.ToString()));
        }
        #endregion

        #region 查询余票部分

        private void dgQuery_MouseDoubleClick(object sender, MouseEventArgs e)
        {
            if (dgQuery.SelectedRows.Count <= 0)
                return;
            try
            {
                TicketEntity ticket = dgQuery.SelectedRows[0].DataBoundItem as TicketEntity;
                if (ticket == null || string.IsNullOrEmpty(ticket.TrainNo))
                    return;
                txtTrainNo.Text = ("," + txtTrainNo.Text + ",").ToUpper().Replace("," + ticket.TrainNo.ToUpper() + ",", ",");
                txtTrainNo.Text += "," + ticket.TrainNo.ToUpper() + ",";
                txtTrainNo.Text = txtTrainNo.Text.Replace(",,", ",").TrimStart(',').TrimEnd(',').Trim();
                if (!rdoLimit.Checked)
                    rdoLimit.Checked = true;
            }
            catch (Exception oe)
            {
                Log.WriteError(oe);
            }
            //btnQuery_Click(sender, null);
        }

        private void rdoNoLimit_CheckedChanged(object sender, EventArgs e)
        {
            if (rdoNoLimit.Checked)
            {
                txtTrainNo.Text = "";
                btnQuery_Click(sender, e);
            }
        }

        private void btnQuery_Click(object sender, EventArgs e)
        {
            try
            {
                bgQuery.RunWorkerAsync();
            }
            catch (Exception oe)
            {
                Log.WriteError(oe);
            }
        }

        private void SetNowQuery()
        {
            if (string.IsNullOrEmpty(Settings.Default.StrLastQuery))
                return;

            DetermineCall(() =>
            {
                string[] strQuery = Settings.Default.StrLastQuery.Split(new string[] { "|" }, StringSplitOptions.RemoveEmptyEntries);
                if (strQuery != null && strQuery.Length > 0)
                {
                    Settings.Default.StrLastQuery = string.Empty;
                    flLastQuery.Controls.Clear();
                    int nowCount = 0;
                    foreach (string str in strQuery)
                    {
                        if (nowCount >= 15)
                        {
                            break;
                        }
                        Settings.Default.StrLastQuery += string.Format("{0}|", str);
                        //{0}-{1}|
                        if (str.HorspoolIndex("-") > 0)
                        {
                            nowCount++;
                            LinkLabel lnk = new LinkLabel();
                            lnk.Text = str;
                            lnk.TabStop = false;
                            lnk.Click += new EventHandler(lnk_Click);
                            flLastQuery.Controls.Add(lnk);
                        }
                    }
                    //Settings.Default.Save();
                }
            });
        }

        void lnk_Click(object sender, EventArgs e)
        {
            LinkLabel lnk = sender as LinkLabel;
            if (lnk.Text.HorspoolIndex("-") > 0)
            {
                isAuto = true;
                cbxTo.Text = CommonMethod.SubString(lnk.Text, "-");
                isAuto = true;
                cbxFrom.Text = CommonMethod.SubString(lnk.Text, "", "-");
            }
        }

        private List<TicketEntity> _LeftTicketStatus = new List<TicketEntity>();
        private BindingList<TicketEntity> _BindingLeftTicketStatus;

        private void dgQuery_DataBindingComplete(object sender, DataGridViewBindingCompleteEventArgs e)
        {
            DetermineCall(delegate
            {
                if (dgQuery.DataSource != null && dgQuery.Rows.Count > 0)
                {
                    try
                    {
                        int key = 0;
                        for (int i = 0; i < dgQuery.Rows.Count; i++)
                        {
                            if (dgQuery.Rows[i].Cells["图标"].Tag != null)
                                continue;
                            try
                            {
                                key = -1;
                                dgQuery.Rows[i].Cells["图标"].Tag = "";
                                if (dgQuery.Rows[i].Cells["TrainNo"] != null
                                    && dgQuery.Rows[i].Cells["TrainNo"].Value != null
                                    && !string.IsNullOrEmpty(dgQuery.Rows[i].Cells["TrainNo"].Value.ToString()))
                                    key = imgIcon.Images.IndexOfKey(dgQuery.Rows[i].Cells["TrainNo"].Value.ToString().ToLower().Substring(0, 1) + ".png");
                                if (key < 0)
                                    key = imgIcon.Images.IndexOfKey("p.png");
                                dgQuery.Rows[i].Cells["图标"].Value = imgIcon.Images[key];
                                if (dgQuery.Rows[i].Cells["LeftTicketDescription"].Value.ToString().StartsWith("--"))
                                {
                                    dgQuery.Rows[i].Cells["TrainNo"].Style.ForeColor = Color.DimGray;
                                }
                            }
                            catch (Exception oe)
                            {
                                Console.Write(oe.Message);
                            }
                        }
                    }
                    catch (Exception oe)
                    {
                        Console.Write(oe.Message);
                    }
                }
            });
        }
        #endregion

        #region 订票相关

        private void DoCheckTicket(bool isQiang = true)
        {
            CommonString.IsPlayedMusic = !isQiang;
            CommonString.IsQiangIng = isQiang;
            if (!isQiang)
            {
                return;
            }
            List<Passenger> lstNoPass = lstPassenger.FindAll(pp => string.IsNullOrEmpty(pp.User));
            if (CommonString.LstUser != null && CommonString.LstUser.Count > 0)
            {
                ShowMSG(string.Format("准备订票信息…"));
                for (int i = 0; i < CommonString.LstUser.Count; i++)
                {
                    UserEntity user = CommonString.LstUser[i];
                    if (user.isAccountError)
                    {
                        continue;
                    }
                    user.Init();
                    user.LstTmpPassage.AddRange(lstPassenger.FindAll(pp => pp.User.Equals(user.StrNowUserName)));
                    if (user.LstTmpPassage != null && user.LstTmpPassage.Count > 5)
                    {
                        while (user.LstTmpPassage.Count > 5)
                        {
                            user.LstTmpPassage.RemoveAt(user.LstTmpPassage.Count - 1);
                        }
                    }
                    else if (lstNoPass != null && lstNoPass.Count > 0)
                    {
                        foreach (var item in lstNoPass)
                        {
                            if (user.LstTmpPassage.Count < 5)
                            {
                                if (!user.LstTmpPassage.Exists(p =>
                                    p.Card.Value.Equals(item.Card.Value)
                                    && p.IDCard.Equals(item.IDCard)
                                    && p.Name.Equals(item.Name)))
                                {
                                    user.LstTmpPassage.Add(item);
                                }
                            }
                            else
                            {
                                break;
                            }
                        }
                        ////如果未分配账号个数小于原有5-乘客数量
                        //if (lstNoPass.Count <= 5 - user.lstTMPPassage.Count)
                        //{
                        //    user.lstTMPPassage.AddRange(lstNoPass);
                        //}
                        //else
                        //{
                        //    user.lstTMPPassage.AddRange(lstNoPass.GetRange(0, 5 - user.lstTMPPassage.Count));
                        //}
                    }
                    if (user.LstTmpPassage != null && user.LstTmpPassage.Count > 1 && user.IsNMode)
                    {
                        user.LstTmpPassage[0].SetSeatTypes(user.FirstSeat);
                        user.LstTmpPassage[0].SetUserType(user.FirstUType);
                    }
                    if (user.IsLogined)
                        user.StrNowUserStatus = "登录成功";
                    if (user.LstTmpPassage != null && user.LstTmpPassage.Count > 0)
                    {
                        user.StrFirstIdCard = user.LstTmpPassage[0].IDCard;
                    }
                }
            }
        }

        private bool PreparePassenger()
        {
            if (lvPassenger.Items == null || lvPassenger.Items.Count <= 0)
                return false;
            //CommonString.isAutoPassenger = chkAutoPassenger.Checked;
            bool result = true;
            //table.TableModel.Selections.Clear();
            lstPassenger = new List<Passenger>();
            foreach (ListViewItem item in lvPassenger.Items)
            {
                //姓名,类别,席别,证件,证件号
                // icon,cmbUser, txtName, cmbType, cmbCardType, txtCardNo, cmbXiBie
                Passenger pass = new Passenger();
                if (string.IsNullOrEmpty(item.SubItems[0].Text) || string.IsNullOrEmpty(item.SubItems[3].Text)
                    || string.IsNullOrEmpty(item.SubItems[4].Text) || string.IsNullOrEmpty(item.SubItems[2].Text)
                    || string.IsNullOrEmpty(item.SubItems[1].Text))
                {
                    lvPassenger.Items[item.Index].Selected = true;
                    MessageBox.Show(this, string.Format("乘客列表第【{0}】行中有空内容，请填写后重试！", item.Index + 1), "提示", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    result = false;
                    break;
                }
                pass.User = item.Tag == null || string.IsNullOrEmpty(item.Tag.ToString()) ? "" : item.Tag.ToString();
                pass.Name = item.SubItems[0].Text;
                pass.IDCard = item.SubItems[4].Text;
                pass.SetCardType(item.SubItems[3].Text);
                pass.SetUserType(item.SubItems[1].Text);
                pass.SetSeatTypes(item.SubItems[2].Text);
                if (!string.IsNullOrEmpty(item.SubItems[5].Text) && !item.SubItems[5].Text.Equals("--"))
                    pass.Status = item.SubItems[5].Text;
                if (pass.Card.Key.StartsWith("二代") && !CommonMethod.CheckCardNO(pass.IDCard))
                {
                    lvPassenger.Items[item.Index].Selected = true;
                    MessageBox.Show(this, string.Format("第【{1}】行中身份证号【{0}】错误，请修正后重试！", pass.IDCard, item.Index + 1), "提示", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    result = false;
                    break;
                }
                //if (string.IsNullOrEmpty(pass.User))
                //{
                //    if (!lstNoPass.Exists(pp => pp.IDCard.Equals(pass.IDCard)))
                //    {
                //        lstNoPass.Add(pass);
                //    }
                //}
                //else
                {
                    if (pass.TicType.Key.Contains("儿童") || !lstPassenger.Exists(pp => pp.IDCard.Equals(pass.IDCard) && pp.User.Equals(pass.User)))
                    {
                        lstPassenger.Add(pass);
                    }
                }
            }
            if (!result)
            {
                //lstNoPass = new List<Passenger>();
                lstPassenger = new List<Passenger>();
            }
            //else
            //{
            //    if (lstNoPass != null && lstNoPass.Count > 5)
            //    {
            //        MessageBox.Show(this, string.Format("自动分配模式乘客数量不能超过5个，当前已有{0}个，超出部分不参与订票！", lstNoPass.Count), "提示"
            //            , MessageBoxButtons.OK, MessageBoxIcon.Information);
            //    }
            //}
            //table.Focus();
            //购买残疾军人（伤残警察）优待票需使用中华人民共和国居民身份证！
            if (result && lstPassenger != null && lstPassenger.Count > 0)
            {
                if (lstPassenger.Exists(p => p.Card.Key == "护照" && p.TicType.Key.StartsWith("残军")))
                {
                    MessageBox.Show(this, string.Format("护照不能购买残军票，请修正后重试！"), "提示", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    result = false;
                }
            }
            return result;
        }

        private List<AttentionItem> GetNowAttention()
        {
            List<AttentionItem> lstAttention = null;
            List<string> lstStartStation = null;
            List<string> lstEndStation = null;
            try
            {
                lstStartStation = GetlstStationByListView(lvStartN);
                lstEndStation = GetlstStationByListView(lvEndN);
                if (!lstStartStation.Contains(cbxFrom.Text.Trim()))
                {
                    lstStartStation.Insert(0, cbxFrom.Text.Trim());
                }
                if (!lstEndStation.Contains(cbxTo.Text.Trim()))
                {
                    lstEndStation.Insert(0, cbxTo.Text.Trim());
                }
                List<string> lstTmoStation = new List<string>();
                bool isTurn = false;
                if (lstStartStation.Count < lstEndStation.Count)
                {
                    isTurn = true;
                    lstTmoStation = lstEndStation;
                    lstEndStation = lstStartStation;
                    lstStartStation = lstTmoStation;
                }
                lstTmoStation = null;
                List<string> lstTmpTrainNo = new List<string>();
                if (rdoNoLimit.Checked)
                {
                    if (dgQuery.DataSource != null && dgQuery.Rows.Count > 0)
                    {
                        foreach (DataGridViewRow row in dgQuery.Rows)
                        {
                            if (!lstTmpTrainNo.Contains(row.Cells["TrainNo"].Value.ToString()))
                            {
                                lstTmpTrainNo.Add(row.Cells["TrainNo"].Value.ToString());
                            }
                        }
                    }
                    if (lstTmpTrainNo == null || lstTmpTrainNo.Count <= 0)
                    {
                        MessageBox.Show(this, "(不限车次)请先查询出所有需要预定的车次后重试！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Error);
                        return lstAttention;
                    }
                    if (MessageBox.Show(this, "(不限车次)将添加以下车次:\n" + string.Join("、", lstTmpTrainNo) + "\n请确定是否正确！", "提示", MessageBoxButtons.YesNo, MessageBoxIcon.Question) == System.Windows.Forms.DialogResult.No)
                        return lstAttention;
                }
                else
                {
                    txtTrainNo.Text = txtTrainNo.Text.Replace(",,", ",").TrimStart(',').TrimEnd(',').Trim().ToUpper();
                    lstTmpTrainNo.AddRange(txtTrainNo.Text.Trim().Split(new string[] { "," }, StringSplitOptions.RemoveEmptyEntries));
                    if (lstTmpTrainNo.Count <= 0)
                    {
                        MessageBox.Show(this, "请设置好抢票的车次后重试（双击查询列表中车次添加）！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Error);
                        return lstAttention;
                    }
                }
                lstAttention = new List<AttentionItem>();
                foreach (string strStart in lstStartStation)
                {
                    foreach (string strEnd in lstEndStation)
                    {
                        var fromSation = CommonString.Stations.FirstOrDefault(v => v.Name == strStart || v.ShortCut == strStart);
                        if (fromSation == null)
                        {
                            MessageBox.Show(this, "起始站点有误(" + strStart + ")，请确认后重试！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Error);
                            return null;
                        }
                        var toStation = CommonString.Stations.FirstOrDefault(v => v.Name == strEnd || v.ShortCut == strEnd);
                        if (toStation == null)
                        {
                            MessageBox.Show(this, "起始站点有误(" + strStart + ")，请确认后重试！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Error);
                            return null;
                        }
                        foreach (string str in dtAttention.CheckedItems)
                        {
                            AttentionItem item = new AttentionItem();
                            //item.IsMobile = CommonString.IsMobile;
                            item.FromStation = isTurn ? toStation : fromSation;
                            item.ToStation = isTurn ? fromSation : toStation;
                            item.Date = DateTime.Parse(str);
                            item.LstTrainNo = lstTmpTrainNo;
                            item.LstGroupStation = NewTicketHelper.GetStationCodes(item.FromStation.Code, item.ToStation.Code);
                            item.PurposeCode = rdoAdult.Checked ? CommonString.strAdultType : CommonString.strStudentType;
                            //(CommonString.IsMobile ? CommonString.strMobileAdultType : CommonString.strAdultType)
                            //: (CommonString.IsMobile ? CommonString.strMobileStudentType : CommonString.strStudentType);
                            if (!lstAttention.Exists(oo =>
                                oo.FromStation.Code.Equals(item.FromStation.Code)
                                && oo.ToStation.Code.Equals(item.ToStation.Code)
                                && oo.LstTrainNo.Equals(item.LstTrainNo)
                                && oo.Date.Equals(item.Date)))
                                lstAttention.Add(item);
                        }
                    }
                }
            }
            catch (Exception oe)
            {
                Log.WriteError(oe);
            }
            finally
            {
                lstStartStation = null;
                lstEndStation = null;
            }
            return lstAttention;
        }

        private DateTime GetTickQuery(bool isGetTask = false)
        {
            DateTime dtTmp = DateTime.MinValue;
            bool isTickQuery = (dtRefreshTick.Checked || chkNextDay.Checked);
            if (!isGetTask && chkNextDay.Checked && (CommonString.serverTime.Hour >= CommonMethod.NWebStartHour && CommonString.serverTime.Hour <= CommonMethod.NWebEndHour))
            {
                ThreadPool.QueueUserWorkItem((object obj) =>
                {
                    System.Threading.Thread.Sleep(50);
                    IntPtr hwnd = WindowsAPI.FindWindow(null, "定时抢票设置");
                    if (hwnd != null && hwnd.ToInt32() > 0)
                    {
                        IntPtr hbtn1 = WindowsAPI.FindWindowEx(hwnd, IntPtr.Zero, "BUTTON", null);
                        IntPtr hbtn2 = WindowsAPI.FindWindowEx(hwnd, hbtn1, "BUTTON", null);
                        WindowsAPI.SendMessage(hbtn1, WindowsAPI.WM_SETTEXT, IntPtr.Zero, "继续操作");
                        WindowsAPI.SendMessage(hbtn2, WindowsAPI.WM_SETTEXT, IntPtr.Zero, "抢今天票");
                    }
                });
                if (MessageBox.Show(this, string.Format("你选择了【抢明天票】,请确定是否正确？\n错误请点'抢【今天】票'。"), "定时抢票设置", MessageBoxButtons.YesNo, MessageBoxIcon.Question) == System.Windows.Forms.DialogResult.No)
                    chkNextDay.Checked = false;
            }
            if (chkNextDay.Checked)
            {
                if (dtRefreshTick.Checked)
                {
                    if (dtRefreshTick.Value.Hour <= CommonMethod.NWebStartHour - 1)
                    {
                        ThreadPool.QueueUserWorkItem((object obj) =>
                        {
                            System.Threading.Thread.Sleep(50);
                            IntPtr hwnd = WindowsAPI.FindWindow(null, "定时抢票设置");
                            if (hwnd != null && hwnd.ToInt32() > 0)
                            {
                                IntPtr hbtn1 = WindowsAPI.FindWindowEx(hwnd, IntPtr.Zero, "BUTTON", null);
                                IntPtr hbtn2 = WindowsAPI.FindWindowEx(hwnd, hbtn1, "BUTTON", null);
                                IntPtr hbtn3 = WindowsAPI.FindWindowEx(hwnd, hbtn2, "BUTTON", null);
                                WindowsAPI.SendMessage(hbtn1, WindowsAPI.WM_SETTEXT, IntPtr.Zero, "继续操作");
                                WindowsAPI.SendMessage(hbtn2, WindowsAPI.WM_SETTEXT, IntPtr.Zero, CommonMethod.NWebStartHour + "点开始");
                                WindowsAPI.SendMessage(hbtn3, WindowsAPI.WM_SETTEXT, IntPtr.Zero, "现在开始");
                            }
                        });
                        DialogResult res = MessageBox.Show(this, string.Format("确定开始抢票时间为：【{0}】？\n官网【" + CommonMethod.NWebStartHour + "点】开张，如果提前过多会影响正常订票！\n请确定是否继续？", dtRefreshTick.Value.ToString("HH:mm:ss")), "定时抢票设置", MessageBoxButtons.YesNoCancel, MessageBoxIcon.Question);
                        if (res == System.Windows.Forms.DialogResult.Cancel)
                            return dtTmp;
                        else if (res == System.Windows.Forms.DialogResult.No)
                        {
                            dtRefreshTick.Value = DateTime.Parse(DateTime.Now.ToString("yyyy-MM-dd " + CommonMethod.NWebStartHour.ToString("00") + ":00:00"));
                        }
                    }
                    dtTmp = DateTime.Parse(CommonString.serverTime.AddDays(CommonString.serverTime.Hour >= CommonMethod.NWebStartHour ? 1 : 0).ToString("yyyy-MM-dd") + dtRefreshTick.Value.ToString(" HH:mm:ss"));
                }
                else
                    dtTmp = DateTime.Parse(CommonString.serverTime.AddDays(CommonString.serverTime.Hour >= CommonMethod.NWebStartHour ? 1 : 0).ToString("yyyy-MM-dd " + CommonMethod.NWebStartHour.ToString("00") + ":00:00"));
            }
            else
            {
                if (dtRefreshTick.Checked)
                {
                    dtTmp = DateTime.Parse(string.Format("{0} {1}"
                        , CommonString.serverTime.ToString("yyyy-MM-dd"),
                        dtRefreshTick.Value.ToString("HH:mm:ss")));
                    dtTmp = dtTmp.AddMilliseconds((double)nQueryMSecond.Value);
                }
            }
            if (!isGetTask && isTickQuery && dtTmp <= CommonString.serverTime)
            {
                dtTmp = DateTime.MinValue;
                isTickQuery = false;
            }
            return dtTmp;
        }

        private void cmsStart_Click(object sender, EventArgs e)
        {
            if (CommonReg.NowUserIsExpired)
            {
                return;
            }
            CommonMethod.Stop();
            if (cmsStart.Text.StartsWith("开始"))
            {
                //if (!CommonMethod.isCanOperate(true))
                //{
                //    MessageBox.Show(this, "早点洗洗睡吧，12306打烊了，明天再搞吧！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Error);
                //    return;
                //}
                if (CommonString.IsComCanUse)
                {
                    if ((IPHelper.lstComEnableIP == null || IPHelper.lstComEnableIP.Count <= 0) && MessageBox.Show(this, "服务器列表还在加载……\n你确定要继续刷票，可能造成本地IP被封！\n点‘是’继续刷票操作\n点‘否’取消操作，可以稍等几分钟后重试！", "", MessageBoxButtons.YesNo, MessageBoxIcon.Warning) == System.Windows.Forms.DialogResult.No)
                    {
                        return;
                    }
                }
                else if (CommonString.IsMobileCanUse)
                {
                    if ((IPHelper.lstMobileEnableIP == null || IPHelper.lstMobileEnableIP.Count <= 0) && MessageBox.Show(this, "服务器列表还在加载……\n你确定要继续刷票，可能造成本地IP被封！\n点‘是’继续刷票操作\n点‘否’取消操作，可以稍等几分钟后重试！", "", MessageBoxButtons.YesNo, MessageBoxIcon.Warning) == System.Windows.Forms.DialogResult.No)
                    {
                        return;
                    }
                }
                if (CommonString.LstUser == null || CommonString.LstUser.Count <= 0)
                {
                    //MessageBox.Show(this, "请添加抢票账户后重试！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    cmsLogin_Click(null, null);
                    if (CommonString.LstUser == null || CommonString.LstUser.Count <= 0)
                        return;
                }
                //CommonString.isAutoPassenger = chkAutoPassenger.Checked;
                if (!PreparePassenger())
                {
                    //MessageBox.Show(this, "请检查乘客信息无误后重试！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return;
                }
                if (lstPassenger == null || lstPassenger.Count <= 0)
                {
                    MessageBox.Show(this, "乘客列表不能为空，请先添加乘客！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return;
                }
                if (dtAttention.CheckedItems == null || dtAttention.CheckedItems.Count <= 0)
                {
                    MessageBox.Show(this, "请设置订票日期！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return;
                }
                //if (chkAdvanceCode.Visible)
                //    CommonString.IsAdvanceCode = chkAdvanceCode.Checked;
                //else
                //    CommonString.IsAdvanceCode = false;
                foreach (var item in CommonString.LstUser)
                {
                    item.FirstSeat = ddlSeat.Value;
                    if (ddlUType.SelectedIndex > -1)
                        item.FirstUType = ddlUType.Text;
                    item.IsNMode = chkNMore.Checked;
                    item.IsAutoCancel = chkAutoCancel.Checked;
                    if (item.IsAutoCancel && !rdoWuZuo.Checked)
                    {
                        rdoWuZuo.Checked = true;
                    }
                    item.IsJianLou = rdoJianLou.Checked;
                    item.NQueryTick = (int)QueryTick.Value;
                    item.IsNoControlSeat = chkNoSeat.Checked;
                    //item.IsTickSub = dtSubTick.Checked;
                    //item.dtTickSub = dtSubTick.Value;
                    item.NQueryTick = (int)QueryTick.Value;
                    item.NSubTick = (int)nSubSleepTine.Value;
                    item.NSubStopTime = (int)nSubStopTime.Value;
                    item.IsWuZuo = rdoWuZuo.Checked;
                    item.IsAutoRemovePassenger = chkAutoRemovePassenger.Checked;

                    DateTime dtTmp = GetTickQuery();
                    item.dtTickQuery = dtTmp;
                    item.IsTickQuery = dtTmp != DateTime.MinValue;
                    if (item.IsTickQuery)
                    {
                        if (item.IsJianLou)
                        {
                            if (CommonString.serverTime.Date == item.dtTickQuery.Date
                                && ((item.dtTickQuery.Minute == 59 || item.dtTickQuery.Minute == 0)
                                || (item.dtTickQuery.Minute >= 29 && item.dtTickQuery.Minute <= 30)))
                            {
                                DialogResult res = MessageBox.Show(this
                                    , string.Format("进入捡漏模式比较耗时，如果当前为预售票，最好提前几分钟开始！\n点'是'继续操作,点'否'将定时时间【提前三分钟】,点'取消'返回修改。"), "设置错误警告", MessageBoxButtons.YesNoCancel, MessageBoxIcon.Question);
                                if (res == System.Windows.Forms.DialogResult.Cancel)
                                    return;
                                else if (res == System.Windows.Forms.DialogResult.No)
                                    item.dtTickQuery = item.dtTickQuery.AddMinutes(-3);
                            }
                        }
                        if (item.dtTickQuery <= CommonString.serverTime)
                        {
                            item.IsTickQuery = false;
                        }
                        else
                            item.IsTickQuery = true;
                    }
                    item.IsTickSub = chkTickSub.Checked;
                    if (item.IsTickSub)
                    {
                        item.dtTickSub = DateTime.Parse(string.Format("{0} {1}"
                                , CommonString.serverTime.ToString("yyyy-MM-dd"),
                                dtSubTick.Value.ToString("HH:mm:ss"))).AddMilliseconds((double)nSubMSecond.Value);
                        if (item.IsTickQuery)
                            item.IsTickSub = item.dtTickSub > item.dtTickQuery;
                        else
                            item.IsTickSub = item.dtTickSub > CommonString.serverTime;
                    }
                }
                //CommonString.dtTicketDate = dtAttention.Value;

                List<AttentionItem> lstAttention = GetNowAttention();
                if (lstAttention == null || lstAttention.Count <= 0)
                {
                    return;
                }

                //if (CommonString.NSubSleepTime > 0 || CommonString.NSubStopTime > 0)
                //{
                //    string strTmp = "确定当前设置：\n";
                //    if (CommonString.NSubStopTime > 0)
                //    {
                //        strTmp += string.Format("\n查到票提交{0}次后停止;", CommonString.NSubStopTime);
                //    }
                //    if (CommonString.NSubSleepTime > 0)
                //    {
                //        strTmp += string.Format("\n各个账户提交间隔为{0}ms;", CommonString.NSubSleepTime);
                //    }
                //    DialogResult res = MessageBox.Show(this
                //        , string.Format(strTmp + "\n\n点'是'继续操作,点'否'返回修改。"), "抢票助手", MessageBoxButtons.YesNo, MessageBoxIcon.Question);
                //    if (res == System.Windows.Forms.DialogResult.No)
                //        return;
                //}

                //if (CommonString.IsAdvanceCode)
                //{
                //    #region 打码提前
                //    //if (CommonString.IsJianLou)
                //    //{
                //    //    chkAdvanceCode.Checked = false;
                //    //    CommonMSG.AddMSG("【捡漏模式】不支持提前打码，自动取消！");
                //    //}
                //    //else
                //    {
                //        if (!CommonString.IsTickQuery)
                //        {
                //            CommonMSG.AddMSG("用户选择【提前打码】，自动启用定时查票(延时3分钟)！");
                //            dtRefreshTick.Checked = true;
                //            CommonString.IsTickQuery = true;
                //            CommonString.dtTickQuery = CommonString.serverTime.AddMinutes(3).AddMilliseconds(-CommonString.serverTime.Millisecond);
                //            dtRefreshTick.Value = CommonString.dtTickQuery;
                //            nQueryMSecond.Value = CommonString.dtTickQuery.Millisecond;
                //        }
                //        else if (new TimeSpan(CommonString.dtTickQuery.Ticks - CommonString.serverTime.Ticks).TotalMinutes < 2)
                //        {
                //            ThreadPool.QueueUserWorkItem(delegate(object obj)
                //            {
                //                System.Threading.Thread.Sleep(50);
                //                IntPtr hwnd = WindowsAPI.FindWindow(null, "设置错误警告");
                //                if (hwnd != null && hwnd.ToInt32() > 0)
                //                {
                //                    IntPtr hbtn1 = WindowsAPI.FindWindowEx(hwnd, IntPtr.Zero, "BUTTON", null);
                //                    IntPtr hbtn2 = WindowsAPI.FindWindowEx(hwnd, hbtn1, "BUTTON", null);
                //                    IntPtr hbtn3 = WindowsAPI.FindWindowEx(hwnd, hbtn2, "BUTTON", null);
                //                    WindowsAPI.SendMessage(hbtn1, WindowsAPI.WM_SETTEXT, IntPtr.Zero, "延时3分钟");
                //                    WindowsAPI.SendMessage(hbtn2, WindowsAPI.WM_SETTEXT, IntPtr.Zero, "取消打码");
                //                    WindowsAPI.SendMessage(hbtn3, WindowsAPI.WM_SETTEXT, IntPtr.Zero, "返回修改");
                //                }
                //            });
                //            DialogResult res = MessageBox.Show(this
                //                , string.Format("由于提前打码比较耗时，\n稳妥起见，至少预留3分钟！\n\n请选择是否【延迟定时查票3分钟】。"), "设置错误警告", MessageBoxButtons.YesNoCancel, MessageBoxIcon.Question);
                //            if (res == System.Windows.Forms.DialogResult.Cancel)
                //                return;
                //            else if (res == System.Windows.Forms.DialogResult.No)
                //            {
                //                chkAdvanceCode.Checked = false;
                //                CommonMSG.AddMSG("用户选择【取消打码】，取消提前打码！");
                //                CommonString.IsAdvanceCode = false;
                //            }
                //            else
                //            {
                //                CommonMSG.AddMSG("用户选择【延时3分钟】，定时查票延后3分钟！");
                //                dtRefreshTick.Checked = true;
                //                CommonString.IsTickQuery = true;
                //                CommonString.dtTickQuery = CommonString.serverTime.AddMinutes(3).AddMilliseconds(-CommonString.serverTime.Millisecond);
                //                dtRefreshTick.Value = CommonString.dtTickQuery;
                //                nQueryMSecond.Value = CommonString.dtTickQuery.Millisecond;
                //            }
                //        }
                //    }

                //    #endregion
                //}


                //如果有交叉，就提示进入模式失败，按之前的正常模式刷票
                //第一个设置1个席别，与第二个无交叉
                //第一个设置多个席别，与第二个席别无交叉
                //1、只有第一个席别有票，且第二个席别有票时，才会提交
                //2、第二个有票，第一个无票时，第一个就用第二个的席别
                //if (CommonString.LstUser[0].IsNMode)
                //{
                //    DateTime dtTick = CommonString.LstUser[0].IsTickQuery ? CommonString.LstUser[0].dtTickQuery : CommonString.serverTime;
                //    bool isNMode = CommonMethod.isZhengDian(dtTick);
                //    if (isNMode)
                //    {
                //        if (dtAttention.CheckedItems[0].ToString() !=
                //            CommonString.serverTime.AddDays(CommonMethod.GetYuShouDate(CommonString.serverTime) - 1).Date.ToString("yyyy-MM-dd"))
                //        {
                //            if (MessageBox.Show(this, "当前可能为非预售票，请确定是否继续使用【1+N】模式？", "【1+N】设置", MessageBoxButtons.YesNo, MessageBoxIcon.Question) == System.Windows.Forms.DialogResult.No)
                //            {
                //                isNMode = false;
                //            }
                //        }
                //    }
                //    if (!isNMode)
                //    {
                //        chkNMore.Checked = false;
                //        ShowMSG("非整点预售票，自动取消【1+N】模式！");
                //    }
                //}
                if (CommonString.LstUser[0].IsNMode)
                {
                    if (string.IsNullOrEmpty(CommonString.LstUser[0].FirstSeat))
                    {
                        MessageBox.Show(this, "使用[1+N]模式，请先设置第一个乘客的席别！", "[1+N]设置提醒", MessageBoxButtons.OK, MessageBoxIcon.Error);
                        return;
                    }
                    if (string.IsNullOrEmpty(CommonString.LstUser[0].FirstUType))
                    {
                        MessageBox.Show(this, "使用[1+N]模式，请先设置第一个乘客的票类型！", "[1+N]设置提醒", MessageBoxButtons.OK, MessageBoxIcon.Error);
                        return;
                    }
                }
                DoCheckTicket();

                var lstAllSeat = new List<string>();
                //购买残疾军人（伤残警察）优待票需使用中华人民共和国居民身份证！
                CommonString.LstUser.ForEach(delegate(UserEntity user)
                {
                    if (user.LstTmpPassage.Exists(p => p.Card.Key == "护照" && p.TicType.Key.StartsWith("残军")))
                    {
                        MessageBox.Show(this, string.Format("护照不能购买残军票，请修正后重试！"), "提示", MessageBoxButtons.OK, MessageBoxIcon.Error);
                        return;
                    }
                    else
                    {
                        user.LstTmpPassage.ForEach(delegate(Passenger pss)
                        {
                            pss.LstSeatType.ForEach(delegate(KeyValueEntity obj)
                            {
                                if (!lstAllSeat.Contains(obj.Key))
                                {
                                    lstAllSeat.Add(obj.Key);
                                }
                            });
                        });
                    }
                });
                //if (Settings.Default.IsNMode)
                //{
                //    UserEntity uTmp = CommonString.LstUser.Find(u => u.lstTMPPassage != null && u.lstTMPPassage.Count > 1
                //        && u.lstTMPPassage.GetRange(1, u.lstTMPPassage.Count - 1)
                //        .Exists(p => p.LstSeatType.Exists(l => u.lstTMPPassage[0].LstSeatType.Exists(k => k.Key == l.Key))));
                //    if (uTmp != null && !string.IsNullOrEmpty(uTmp.StrNowUserName))
                //    {
                //        DialogResult res = MessageBox.Show(this, string.Format("[1+N]模式下，账号【{0}】中第一个乘客\n的席别必须与其他乘客的席别不同！\n点'是'退出[1+N]模式,并继续订票,点'否'返回修改。\n\n请确定是否继续？", uTmp.StrNowUserName), "【1+N】设置提醒", MessageBoxButtons.YesNo, MessageBoxIcon.Question);
                //        if (res == System.Windows.Forms.DialogResult.No)
                //        {
                //            return;
                //        }
                //        else
                //        {
                //            chkNMore.Checked = false;
                //        }
                //    }
                //}
                //if (Settings.Default.IsNMode)
                //{
                //    foreach (var item in Settings.Default.FirstSeat.Split(new string[] { "|" }, StringSplitOptions.RemoveEmptyEntries))
                //    {
                //        lstAllSeat.Remove(item);
                //    }
                //}
                CommonMethod.ClearCommonTicket("", "", "", "", true);
                //NewTicketHelper.InitPool();
                string strTmpQueryKey = "";
                lstAttention.ForEach(delegate(AttentionItem item)
                {
                    item.LstAllSeatType = lstAllSeat;
                    foreach (var key in item.GetAllKey())
                    {
                        if (!strTmpQueryKey.Contains(key))
                            strTmpQueryKey += key + ",";
                    }
                });
                //try
                //{
                //    if (CommonCacheHelper.IsServeCacheEnable)
                //    {
                //        CommonCacheHelper.StrKey = strTmpQueryKey.TrimEnd(',');
                //        if (CommonString.LstUser[0].IsTickQuery && strTmpQueryKey.Length > 5)
                //        {
                //            CommonCacheHelper.StrHelpKey = CommonString.LstUser[0].dtTickQuery.ToString("yyyy-MM-dd HH:mm:ss")
                //                + "|" + strTmpQueryKey.TrimEnd(',');
                //        }
                //    }
                //}
                //catch { }
                cmsStart.Enabled = false;
                cmsStart.Text = "准备刷票";
                //开始刷票
                ShowMSG("准备开始订票…");
                try
                {
                    Parallel.ForEach<UserEntity>(CommonString.LstUser, user =>
                    {
                        try
                        {
                            var lstTmpAttion = new List<AttentionItem>();
                            lstTmpAttion.AddRange(lstAttention);
                            user.LstNowAttetion = CommonMethod.GetLstByIndex(lstTmpAttion, CommonString.LstUser.IndexOf(user));
                            //user.lstNowAttetion = lstAttention;
                            if (user.LstTmpPassage != null && user.LstTmpPassage.Count > 0)
                            {
                                //user.IsQueryCache = true;
                                user.IsOrderIng = true;
                            }
                            else
                            {
                                user.StrNowStatus = "账户未分配乘客，自动停止！";
                                user.IsOrderIng = false;
                            }
                        }
                        catch { }
                    });
                }
                catch (AggregateException oe)
                {
                    foreach (Exception item in oe.InnerExceptions)
                    {
                        Log.WriteError("cmsStart_Click异常", item);
                    }
                }
                catch (Exception oe)
                {
                    Log.WriteError("cmsStart_Click异常", oe);
                }
                btnPassenger_Click(null, null);
                if (CommonString.IsQiangIng && !CommonString.isExit)
                {
                    if (CommonString.LstUser == null || CommonString.LstUser.Count <= 0)
                        return;
                    if (CommonString.LstUser[0].IsTickQuery)
                        CommonMSG.AddMSG(string.Format("等待[{0}]查票…", CommonString.LstUser[0].dtTickQuery.ToString("yyyy-MM-dd HH:mm:ss fff")));
                    NewTicketHelper.StartTask(CommonString.LstUser);
                    //ThreadPool.QueueUserWorkItem(new WaitCallback(obj =>
                    //{
                    //    //NewTicketHelper.CheckCache(CommonAttetion);
                    //    NewTicketHelper.CheckTicket(lstAttention, lstUser);
                    //}));
                    //NewTicketHelper.QueryCache(CommonString.LstUser[0].IsTickQuery, CommonString.LstUser[0].dtTickQuery);
                    //开启刷票线程及下单线程
                    grpSet.Enabled = false;
                    pnlTongDao.Enabled = true;
                    grpPassenger.Enabled = false;
                    pnlInfo.BringToFront();
                    cmsStart.Image = imgQiangPiao.Images[1];
                    cmsStart.Enabled = true;
                    cmsStart.Text = "停止刷票";
                }
            }
            else
            {
                cmsStart.Enabled = false;
                cmsStart.Text = "正在停止";
                ThreadPool.QueueUserWorkItem((object obj) =>
                    {
                        //停止刷票
                        DoCheckTicket(false);
                        Parallel.ForEach<UserEntity>(CommonString.LstUser, user =>
                        {
                            try
                            {
                                user.IsOrderIng = false;
                                user.Init(false);
                            }
                            catch { }
                        });
                        CommonCacheHelper.StrKey = "";
                        ShowMSG("刷票停止，用户终止！");
                        DetermineCall(delegate
                        {
                            cmsStart.Image = imgQiangPiao.Images[0];
                            cmsStart.Enabled = true;
                            cmsStart.Text = "开始刷票";
                            grpPassenger.Enabled = true;
                            grpSet.Enabled = true;
                        });
                        CloseImgCodes();
                        //NewTicketHelper.ShutDownTicketPool();
                    });
            }
        }

        private void CloseImgCodes()
        {
            DetermineCall(delegate
            {
                try
                {
                    for (int i = 0; i < Application.OpenForms.Count; i++)
                    {
                        if (Application.OpenForms[i].Text.Equals("验证码输入"))
                        {
                            Application.OpenForms[i].DialogResult = System.Windows.Forms.DialogResult.Cancel;
                            //Application.OpenForms[i].Dispose();
                            //i--;
                        }
                    }
                }
                catch { }
            });
        }
        #endregion

        private void bgPublic_DoWork(object sender, DoWorkEventArgs e)
        {
            while (!CommonString.isExit)
            {
                CommonMSG.SendAllURL();
                if (!_isSavedPassenger && CommonMethod.isCanOperate(false, false))
                {
                    bgSaveWeb.RunWorkerAsync();
                }
                //CommonMethod.IsOnLine();
                if (dtGoDate.Tag == null || !dtGoDate.MinDate.ToString("yyyy-MM-dd").Equals(CommonString.serverTime.Date.ToString("yyyy-MM-dd"))
                    || (dtGoDate.Tag != null && !dtGoDate.Tag.ToString().Equals(CommonMethod.MaxDate.ToString("yyyy-MM-dd"))))
                {
                    BindData();
                    CommonMethod.GetYuShouDate(CommonString.serverTime, true);
                }
                if ((Settings.Default.IsUserAutoCode && cmbCodeType.SelectedIndex != 0) ||
                    (!Settings.Default.IsUserAutoCode && cmbCodeType.SelectedIndex != 1))
                {
                    cmbCodeType.SelectedIndex = Settings.Default.IsUserAutoCode ? 0 : 1;
                    cmbCodeType_SelectedIndexChanged(null, null);
                }
                if (CommonMSG.lstTipMSG != null && CommonMSG.lstTipMSG.Count > 0)
                {
                    try
                    {
                        //if (!(Form.ActiveForm is FormMain))
                        CommonMethod.FlashWindow(this.Handle, true);
                        string strNowStatus = CommonMSG.GetNowSuccessMSG();
                        if (!string.IsNullOrEmpty(strNowStatus))
                        {
                            ShowTipMsg(strNowStatus);
                            if (!CommonString.isDebug)
                            {
                                CommonMSG.NowEmail.SendMessage(strNowStatus);
                                CommonMSG.NowFetion.SendMessage(strNowStatus);
                                CommonMSG.NowQQ.SendMessage(strNowStatus);
                            }
                        }
                    }
                    catch (Exception oe)
                    {
                        Log.WriteError(oe);
                    }
                }
                if (CommonString.LstUser != null && CommonString.LstUser.Count > 0)
                {
                    if (!cmsStart.Text.StartsWith("开始") && !cmsStart.Text.StartsWith("准备"))
                    {
                        if (!CommonString.LstUser.Exists(uu => !uu.isAccountError && uu.IsOrderIng))
                        {
                            CommonString.IsQiangIng = false;
                            CommonCacheHelper.StrKey = "";
                            DetermineCall(delegate
                            {
                                grpSet.Enabled = true;
                                pnlTongDao.Enabled = true;
                                grpPassenger.Enabled = true;
                                ShowMSG("抢票任务全部完成！");
                                cmsStart.Image = imgQiangPiao.Images[0];
                                cmsStart.Enabled = true;
                                cmsStart.Text = "开始刷票";
                            });
                            CloseImgCodes();
                            //NewTicketHelper.ShutDownTicketPool();
                        }
                    }
                    else if (!CommonString.IsQiangIng)
                    {
                        CommonString.LstUser.ForEach(user =>
                        {
                            if (user.StrNowUserStatus.Equals("正在刷票") || user.StrNowUserStatus.Equals("正在查票") || user.StrNowUserStatus.Equals("无票模式"))
                                user.StrNowUserStatus = "刷票停止";
                        });
                    }
                }
                System.Threading.Thread.Sleep(2000);
            }
        }

        private List<ScrollEntity> lstMSG = new List<ScrollEntity>();
        private void cmsUserManager_Click(object sender, EventArgs e)
        {
            FormNowUser nowUser = new FormNowUser();
            nowUser.ShowDialog(this);
        }

        #region 乘客管理
        private List<Passenger> lstPassenger = new List<Passenger>();

        private void BindPassengers(bool isFromFile = false)
        {
            lvPassenger.Items.Clear();
            lvPassenger.Groups.Clear();
            if (isFromFile)
            {
                List<string> lstUserTmp = new List<string>();
                lstPassenger.ForEach(delegate(Passenger pp)
                {
                    if (!string.IsNullOrEmpty(pp.User) && !lstUserTmp.Contains(pp.User))
                    {
                        lstUserTmp.Add(pp.User);
                        ListViewGroup lvUser = new ListViewGroup();  //创建分组  
                        lvUser.HeaderAlignment = HorizontalAlignment.Left;   //设置组标题文本的对齐方式。（默认为Left） 
                        List<Passenger> lstPass = lstPassenger.FindAll(tmp => !string.IsNullOrEmpty(tmp.User) && pp.User.Equals(tmp.User));
                        lvUser.Header = pp.User + "(共" + lstPass.Count + "个)";  //设置组的标题。  
                        lvPassenger.Groups.Add(lvUser);    //把分组添加到listview中
                        int count = 0;
                        if (lstPass != null && lstPass.Count > 0)
                        {
                            lstPass.ForEach(delegate(Passenger pInfo)
                            {
                                count++;
                                if (count < 6)
                                {
                                    ListViewItem item = new ListViewItem(new string[]
                                    { 
                                        pInfo.Name
                                        , pInfo.TicType.Key
                                        , pInfo.GetAllTypeName()
                                        , pInfo.Card.Key.Replace("身份证","").Replace("通行证","")
                                        , pInfo.IDCard
                                        ,string.IsNullOrEmpty(pInfo.Status)?"--":pInfo.Status
                                    }, 0, lvUser);
                                    item.Tag = pInfo.User;
                                    lvPassenger.Items.Add(item);
                                }
                            });
                        }
                        lstPass.Clear();
                        lstPass = null;
                    }
                });
                lstUserTmp = null;
            }
            else
            {
                foreach (UserEntity user in CommonString.LstUser)
                {
                    ListViewGroup lvUser = new ListViewGroup();  //创建分组  
                    lvUser.HeaderAlignment = HorizontalAlignment.Left;   //设置组标题文本的对齐方式。（默认为Left） 
                    List<Passenger> lstPass = lstPassenger.FindAll(pp => !string.IsNullOrEmpty(pp.User) && pp.User.Equals(user.StrNowUserName));
                    lvUser.Header = user.StrNowUserName + "(共" + lstPass.Count + "个)";  //设置组的标题。  
                    lvPassenger.Groups.Add(lvUser);    //把分组添加到listview中
                    if (lstPass != null && lstPass.Count > 0)
                    {
                        int count = 0;
                        lstPass.ForEach(delegate(Passenger pp)
                        {
                            count++;
                            if (count <= 5)
                            {
                                ListViewItem item = new ListViewItem(new string[]
                        { 
                            pp.Name
                            , pp.TicType.Key
                            , pp.GetAllTypeName()
                            , pp.Card.Key.Replace("身份证","").Replace("通行证","")
                            , pp.IDCard
                            ,string.IsNullOrEmpty(pp.Status)?"--":pp.Status
                        }, 0, lvUser);
                                item.Tag = pp.User;
                                lvPassenger.Items.Add(item);
                            }
                        });
                    }
                }
            }
            List<Passenger> lstTmpPass = lstPassenger.FindAll(pp => string.IsNullOrEmpty(pp.User) || (!isFromFile && !CommonString.LstUser.Exists(uu => uu.StrNowUserName.Equals(pp.User))));

            if (lstTmpPass != null && lstTmpPass.Count > 0)
            {
                ListViewGroup lvNoGroup = new ListViewGroup();  //创建分组  
                lvNoGroup.Header = "未分配" + "(共" + lstTmpPass.Count + "个)";  //设置组的标题。  
                lvNoGroup.HeaderAlignment = HorizontalAlignment.Left;   //设置组标题文本的对齐方式。（默认为Left） 
                lvPassenger.Groups.Add(lvNoGroup);    //把分组添加到listview中
                lstTmpPass.ForEach(delegate(Passenger pp)
                {
                    ListViewItem item = new ListViewItem(new string[]
                        { 
                            pp.Name
                            ,pp.TicType.Key
                            , pp.GetAllTypeName()
                            ,  pp.Card.Key.Replace("身份证","").Replace("通行证","")
                            , pp.IDCard
                            ,string.IsNullOrEmpty(pp.Status)?"--":pp.Status
                        }, 0, lvNoGroup);
                    item.Tag = "";
                    lvPassenger.Items.Add(item);
                    //lvPassenger.Items.Add(new ListViewItem(new string[]
                    //{ pp.Name
                    //    , string.IsNullOrEmpty(pp.TypeName) ? "成人票" : pp.TypeName
                    //    , string.IsNullOrEmpty(pp.SeatName) ? "硬卧" : pp.SeatName
                    //    , string.IsNullOrEmpty(pp.CardName) ? "二代身份证" : pp.CardName
                    //    , pp.IDCard 
                    //    ,"--"
                    //}, 0, lvNoGroup));
                });
            }
        }

        private bool LoadPassFormFile(string fileName, ref string msg)
        {
            bool result = true;
            try
            {
                List<Passenger> lstTmp = NewTicketHelper.LoadPassFormFile(fileName, false, ref msg);
                if (lstTmp != null && lstTmp.Count > 0)
                {
                    foreach (var pass in lstTmp)
                    {
                        if (!lstPassenger.Exists(item => item.User == pass.User && item.IDCard == pass.IDCard
                                && item.TicType.Key.Equals(pass.TicType.Key)))
                        {
                            if (!string.IsNullOrEmpty(pass.User)
                                && lstPassenger.Exists(item => item.User == pass.User)
                                && lstPassenger.FindAll(item => item.User == pass.User).Count >= 5)
                                continue;
                            lstPassenger.Add(pass);
                        }
                    }
                }
            }
            catch { }
            BindPassengers(true);
            return result;
        }

        private void btnImport_Click(object sender, EventArgs e)
        {
            if (open.ShowDialog(this) == System.Windows.Forms.DialogResult.OK)
            {
                string msg = string.Empty;
                LoadPassFormFile(open.FileName, ref msg);
                if (!string.IsNullOrEmpty(msg))
                    MessageBox.Show(this, "导入过程中发生错误，\n" + msg + "\n请检查文件内容是否符合\n【姓名,证件类型,证件号,手机号,票种,席别】\n确认无误后重试！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void btnClear_Click(object sender, EventArgs e)
        {
            lvPassenger.Items.Clear();
            lvPassenger.Groups.Clear();
            lstPassenger = new List<Passenger>();
            //table.TableModel.Rows.Clear();
        }

        private void btnOutPut_Click(object sender, EventArgs e)
        {
            if (!PreparePassenger() || (lstPassenger == null || lstPassenger.Count <= 0))
            {
                //MessageBox.Show(this, "请检查乘客信息无误后重试！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return;
            }
            save.FileName = string.Format("Passenger_{0}.txt", DateTime.Now.ToString("yyyy-MM-dd-HH-mm-ss"));
            if (save.ShowDialog(this) == System.Windows.Forms.DialogResult.OK)
            {
                if (NewTicketHelper.SavePassage(lstPassenger.ToArray(), save.FileName))
                    MessageBox.Show(this, "操作已成功！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                else
                    MessageBox.Show(this, "操作失败，助手没有写本地文件的权限！\n是否有360等安全软件拦截提示，请添加信任或关闭防护后重试！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
        }

        List<string> lstUserFromWeb = new List<string>();
        private void btnFrom12306_Click(object sender, EventArgs e)
        {
            lstUserFromWeb = new List<string>();
            btnFrom12306.Enabled = false;
            btnFrom12306.Text = "…";
            FormNowUser nowUser = new FormNowUser();
            nowUser.isFromWeb = true;
            if (nowUser.ShowDialog(this) == System.Windows.Forms.DialogResult.OK)
            {
                lstUserFromWeb = nowUser.lstSelected;
            }
            bgLoadFrom12306.RunWorkerAsync();
        }

        private void 删除ToolStripMenuItem_Click(object sender, EventArgs e)
        {
            if (lvPassenger.SelectedItems != null && lvPassenger.SelectedItems.Count > 0)
            {
                foreach (ListViewItem item in lvPassenger.SelectedItems)
                {
                    lvPassenger.Items.RemoveAt(item.Index);
                }
            }
            //if (table.SelectedItems != null && table.SelectedItems.Length > 0)
            //{
            //    lstPassenger.RemoveAll(pass =>
            //        pass.IDCard.Equals(table.SelectedItems[0].Cells[6].Text)
            //        && !table.SelectedItems[0].Cells[1].Text.Equals(pass.User));
            //    table.TableModel.Rows.RemoveAt(table.SelectedItems[0].Index);
            //}
        }

        private void 修改ToolStripMenuItem_Click(object sender, EventArgs e)
        {
            if (lvPassenger.SelectedItems != null && lvPassenger.SelectedItems.Count > 0)
            {
                FormPassenger pass = new FormPassenger();
                pass.NowPassenger = new Passenger();
                pass.NowPassenger.Name = lvPassenger.SelectedItems[0].SubItems[0].Text;
                pass.NowPassenger.IDCard = lvPassenger.SelectedItems[0].SubItems[4].Text;
                pass.NowPassenger.SetUserType(lvPassenger.SelectedItems[0].SubItems[1].Text);
                pass.NowPassenger.SetCardType(lvPassenger.SelectedItems[0].SubItems[3].Text);
                //pass.NowPassenger.SetSeatTypes(pass.NowPassenger.Seat.Key);
                if (pass.ShowDialog(this) == System.Windows.Forms.DialogResult.Yes)
                {
                    try
                    {
                        //姓名,类别,席别,证件,证件号
                        lvPassenger.SelectedItems[0].SubItems[0].Text = pass.NowPassenger.Name;
                        lvPassenger.SelectedItems[0].SubItems[1].Text = pass.NowPassenger.TicType.Key;
                        lvPassenger.SelectedItems[0].SubItems[2].Text = pass.NowPassenger.GetAllTypeName();
                        lvPassenger.SelectedItems[0].SubItems[3].Text = pass.NowPassenger.Card.Key;
                        lvPassenger.SelectedItems[0].SubItems[4].Text = pass.NowPassenger.IDCard;
                    }
                    catch { }
                }
            }
        }

        private void 添加ToolStripMenuItem_Click(object sender, EventArgs e)
        {
            if (lvPassenger.Items.Count > 0 && !PreparePassenger())
            {
                return;
            }
            FormPassenger pass = new FormPassenger();
            if (pass.ShowDialog(this) == System.Windows.Forms.DialogResult.Yes)
            {
                lstPassenger.RemoveAll(pp => pp.IDCard.Equals(pass.NowPassenger.IDCard));
                lstPassenger.Add(pass.NowPassenger);
                BindPassengers();
            }
        }

        #endregion

        private void nRefreshTime_ValueChanged(object sender, EventArgs e)
        {
            //CommonString.nRefreshTick = (int)QueryTick.Value;
        }

        private void lblJuZhu_Click(object sender, EventArgs e)
        {
            if (!File.Exists(CommonReg.StrRegFilePath) || !RegNowNew() || CommonReg.GetNetRegInfoNew() || !CommonReg.NowUserIsReg || CommonReg.NowUserIsExpired)
            {
                CommonReg.SetNoReg();
            }
            FormJuanZhu juanzhun = new FormJuanZhu();
            juanzhun.ShowDialog(this);
        }

        private bool isShowUpdate = false;
        private void lblUpdate_Click(object sender, EventArgs e)
        {
            if (isShowUpdate)
            {
                return;
            }
            try
            {
                isShowUpdate = true;
                FormUpdate update = new FormUpdate();
                if (update.isHasNew())
                {
                    update.ShowDialog();
                }
                else
                {
                    MessageBox.Show(this, "您使用的已经是最新版本！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception oe)
            {
                MessageBox.Show(this, "检查更新失败，请联系客服协助！\n详细信息：" + oe.Message, "提示", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                isShowUpdate = false;
            }
        }

        private void lnkSetFetion_LinkClicked(object sender, LinkLabelLinkClickedEventArgs e)
        {
            FormSet set = new FormSet();
            set.nowTabType = 0;
            set.ShowDialog(this);
        }

        private void lbDateTime_Click(object sender, EventArgs e)
        {
            CommonMethod.GetNtpTime();
            CommonMSG.AddMSG("同步北京时间成功！");
        }

        protected override void OnPaint(PaintEventArgs e)
        {
            try
            {
                pnlInfo.SuspendLayout();
                pnlSet.SuspendLayout();
                pnlTicket.SuspendLayout();
                base.OnPaint(e);
                pnlInfo.ResumeLayout();
                pnlSet.ResumeLayout();
                pnlTicket.ResumeLayout();
            }
            catch (Exception oe)
            {
                Log.WriteError(oe);
            }
        }

        private void cmbSeat_SelectedValueChanged(object sender, EventArgs e)
        {
            foreach (ListViewItem item in lvPassenger.Items)
            {
                //姓名,类别,席别,证件,证件号
                item.SubItems[2].Text = cmbSeat.Value;
            }
        }

        private void btnCopy_Click(object sender, EventArgs e)
        {
            if (!string.IsNullOrEmpty(rtbNewLog.Text.Trim()))
            {
                try
                {
                    Clipboard.SetDataObject(rtbNewLog.Text.Replace("\n", "\r\n").Trim());
                }
                catch (Exception oe)
                {
                    MessageBox.Show(this, "复制日志到粘贴板失败！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return;
                }
                MessageBox.Show(this, "已成功复制日志到粘贴板！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
        }

        private bool isAuto = false;
        private void btnChange_Click(object sender, EventArgs e)
        {
            string strTmp = cbxFrom.Text.Trim();
            isAuto = true;
            cbxFrom.Text = cbxTo.Text;
            isAuto = true;
            cbxTo.Text = strTmp;
            if (chkTC.Checked)
            {
                List<string> lstStart = GetlstStationByListView(lvStartN);
                List<string> lstEnd = GetlstStationByListView(lvEndN);
                BindListViewGroup(lvEndN, lstStart);
                BindListViewGroup(lvStartN, lstEnd);
            }
        }

        private void dtGoDate_ValueChanged(object sender, EventArgs e)
        {
            if (!CommonString.IsQiangIng)
            {
                if (string.IsNullOrEmpty(dtAttention.Value) || dtAttention.CheckedItems.Count == 1)
                    dtAttention.Value = dtGoDate.Value.ToString("yyyy-MM-dd");
            }
        }

        private void bgLoadFrom12306_DoWork(object sender, DoWorkEventArgs e)
        {
            if (lstUserFromWeb != null && lstUserFromWeb.Count > 0)
            {
                frmPassenger pss = new frmPassenger();
                pss.lstUser = CommonString.LstUser.FindAll(pp => lstUserFromWeb.Contains(pp.StrNowUserName));
                if (pss.lstUser != null && pss.lstUser.Count > 0)
                {
                    if (pss.ShowDialog(this) == System.Windows.Forms.DialogResult.Yes && pss.lstPassenger != null && pss.lstPassenger.Count > 0)
                    {
                        lstPassenger = new List<Passenger>();
                        lstPassenger.AddRange(pss.lstPassenger);
                        //pss.lstPassenger.ForEach(delegate(Passenger pass)
                        //{
                        //    if (!lstPassenger.Exists(pp => pp.IDCard.Equals(pass.IDCard) && pass.User.Equals(pp.User)))
                        //    {
                        //        lstPassenger.Add(pass);
                        //    }
                        //});
                        DetermineCall(delegate
                        {
                            BindPassengers();
                        });
                    }
                }
            }
        }

        private void bgLoadFrom12306_RunWorkerCompleted(object sender, RunWorkerCompletedEventArgs e)
        {
            lblNowCount_Click(null, null);
            btnFrom12306.Enabled = true;
            btnFrom12306.Text = "官网";
        }

        private void 清空无效乘客ToolStripMenuItem_Click(object sender, EventArgs e)
        {
            if (MessageBox.Show(this, "确定要清除所有未分配的乘客信息？", "提示", MessageBoxButtons.YesNo, MessageBoxIcon.Question) == System.Windows.Forms.DialogResult.Yes)
            {
                try
                {
                    PreparePassenger();
                    if (lstPassenger != null && lstPassenger.Count > 0)
                    {
                        lstPassenger.RemoveAll(pp => string.IsNullOrEmpty(pp.User));
                    }
                    BindPassengers();
                }
                catch (Exception oe)
                {
                    Log.WriteError(oe);
                }
            }
            //lstPassenger = new List<Passenger>();
            //table.TableModel.Rows.Clear();
        }

        private void chkAutoPassenger_CheckedChanged(object sender, EventArgs e)
        {
            //table.ColumnModel.Columns[1].Visible = !chkAutoPassenger.Checked;
            //CommonString.isAutoPassenger = chkAutoPassenger.Checked;
        }

        private void cmbCodeType_SelectedIndexChanged(object sender, EventArgs e)
        {
            Settings.Default.IsUserAutoCode = cmbCodeType.SelectedIndex == 0;
            if (!isAuto)
                Settings.Default.Save();
            if (Settings.Default.IsUserAutoCode)
            {
                CloseImgCodes();
            }
        }

        private void bgQuery_DoWork(object sender, DoWorkEventArgs e)
        {
            try
            {
                if (!btnQuery.Enabled)
                    return;
                QueryLeftTicketStatus();
            }
            catch { }
        }

        private void SaveLastInfo()
        {
            CommonString.NLogCount = (long)nMaxLogNum.Value;
            Settings.Default.IsUserAutoCode = cmbCodeType.SelectedIndex == 0;
            Settings.Default.LastFromTC = string.Join("|", GetlstStationByListView(lvStartN).ToArray());
            Settings.Default.LastToTC = string.Join("|", GetlstStationByListView(lvEndN).ToArray());
            Settings.Default.LastTrainNo = txtTrainNo.Text.Trim();
            Settings.Default.LastFrom = cbxFrom.Text;
            Settings.Default.LastTo = cbxTo.Text;
            Settings.Default.LastDate = dtGoDate.Value.ToString("yyyy-MM-dd");
            //CommonString.IsAutoPassenger = chkAutoPassenger.Checked;
            Settings.Default.Save();

            if (lstPassenger == null || lstPassenger.Count <= 0)
                return;
            NewTicketHelper.SavePassage(lstPassenger.ToArray(), Application.StartupPath.TrimEnd('\\') + "\\last.txt");
        }

        private string strCookie = "";

        private void QueryLeftTicketStatus()
        {
            if (string.IsNullOrEmpty(cbxFrom.Text))
            {
                cbxFrom.Focus();
                return;
            }
            if (string.IsNullOrEmpty(cbxTo.Text))
            {
                cbxTo.Focus();
                return;
            }
            var fromSation = CommonString.Stations.FirstOrDefault(v => v.Name == cbxFrom.Text || v.ShortCut == cbxFrom.Text);
            if (fromSation == null)
            {
                cbxFrom.Text = "";
                cbxFrom.Focus();
                return;
            }
            var toStation = CommonString.Stations.FirstOrDefault(v => v.Name == cbxTo.Text || v.ShortCut == cbxTo.Text);
            if (toStation == null)
            {
                cbxTo.Text = "";
                cbxTo.Focus();
                return;
            }
            if (cbxFrom.Text != fromSation.Name)
            {
                cbxFrom.Text = fromSation.Name;
            }
            if (cbxTo.Text != toStation.Name)
            {
                cbxTo.Text = toStation.Name;
            }
            if (!Settings.Default.StrLastQuery.StartsWith(string.Format("{0}-{1}|", Settings.Default.LastFrom, Settings.Default.LastTo)))
            {
                Settings.Default.StrLastQuery = string.Format("{0}-{1}|", Settings.Default.LastFrom, Settings.Default.LastTo) +
                    Settings.Default.StrLastQuery.Replace(string.Format("{0}-{1}|", Settings.Default.LastFrom, Settings.Default.LastTo), "");
                SetNowQuery();
            }
            SaveLastInfo();
            AttentionItem nowAttention = new AttentionItem();
            nowAttention.FromStation = fromSation;
            nowAttention.ToStation = toStation;
            nowAttention.Date = dtGoDate.Value;
            nowAttention.PurposeCode = rdoAdult.Checked ? CommonString.strAdultType : CommonString.strStudentType;
            //if (NowQueryTrain != null && !string.IsNullOrEmpty(NowQueryTrain.TrainID))
            //{
            //    nowAttention.TrainID = NowQueryTrain.TrainID;
            //    nowAttention.TrainNo = NowQueryTrain.TrainNo;
            //}
            string strMSG = string.Empty;
            DetermineCall(delegate
            {
                btnQuery.Text = "查询中…";
                btnQuery.Enabled = false;
            });
            try
            {
                _LeftTicketStatus.Clear();
                bool isCache = false;
                List<TicketEntity> lstTmp = new List<TicketEntity>();
                bool isNoValidate = false;
                string strIP = CommonString.StrLocalComIP;

                strCookie = CommonMethod.GetCompleteCookie(false, strCookie, nowAttention);

                lstTmp = NewTicketHelper.QueryTmpTicket(strIP, nowAttention, ref strCookie, out strMSG, out isCache, ref isNoValidate);

                try
                {
                    if (rdoLimit.Checked)
                    {
                        txtTrainNo.Text = txtTrainNo.Text.Replace(",,", ",").TrimStart(',').TrimEnd(',').Trim();
                        if (!string.IsNullOrEmpty(txtTrainNo.Text))
                        {
                            string[] strTmp = txtTrainNo.Text.Trim().Split(new string[] { "," }, StringSplitOptions.RemoveEmptyEntries);
                            if (strTmp.Length > 0)
                            {
                                List<string> lstTrainNo = new List<string>();
                                lstTrainNo.AddRange(strTmp);
                                lstTmp.RemoveAll(ll => !lstTrainNo.Exists(nn => nn.Equals(ll.TrainNo)));
                            }
                        }
                    }
                }
                catch (Exception oe)
                {
                    Log.WriteError(oe);
                }
                if (lstTmp != null && lstTmp.Count > 0)
                {
                    List<string> lstType = GetNoCheckedType();
                    if (lstType != null && lstType.Count > 0)
                    {
                        foreach (string str in lstType)
                        {
                            switch (str)
                            {
                                case "D":
                                    lstTmp.RemoveAll(p => p.TrainNo.StartsWith("C") || p.TrainNo.StartsWith("D") || p.TrainNo.StartsWith("G"));
                                    break;
                                case "K":
                                    lstTmp.RemoveAll(p => p.TrainNo.StartsWith("K"));
                                    break;
                                case "Z":
                                    lstTmp.RemoveAll(p => p.TrainNo.StartsWith("Z"));
                                    break;
                                case "T":
                                    lstTmp.RemoveAll(p => p.TrainNo.StartsWith("T"));
                                    break;
                                case "QT":
                                    lstTmp.RemoveAll(p => !p.TrainNo.StartsWith("C") && !p.TrainNo.StartsWith("D") && !p.TrainNo.StartsWith("G")
                                        && !p.TrainNo.StartsWith("K") && !p.TrainNo.StartsWith("Z") && !p.TrainNo.StartsWith("T"));
                                    break;
                                default:
                                    break;
                            }
                        }
                    }
                }
                _LeftTicketStatus.AddRange(lstTmp);
                if (!string.IsNullOrEmpty(strMSG))
                {
                    if (strMSG.Contains("空") || strMSG.Contains("失败") || strMSG.Contains("网络"))
                    {
                        //if (strIP.Equals(CommonString.StrLocalComIP) && strMSG.Contains("网络"))
                        //    CommonString.StrLocalComIP = IPHelper.AutoChangeIP(false, CommonString.StrLocalComIP);
                        if (!CommonString.isDebug)
                            strMSG = "";
                        else
                            strMSG += " IP:" + strIP;
                    }
                }
                if (!string.IsNullOrEmpty(strMSG))
                {
                    DetermineCall(delegate
                    {
                        ShowMSG(strMSG);
                    });
                }
                else
                {
                    DetermineCall(delegate
                    {
                        ShowMSG(string.Format("查到【{0}】趟车{1}。", _BindingLeftTicketStatus.Count, CommonString.isDebug && isCache ? "(缓存)" : ""));
                    });
                }
                lstTmp = null;
                strMSG = null;
            }
            catch (Exception ex)
            {
                Log.WriteError(ex);
                DetermineCall(delegate
                {
                    ShowMSG(string.Format("服务器繁忙，请稍后重试！"));
                });
            }
            fromSation = null;
            toStation = null;
            nowAttention = null;
        }

        private void bgQuery_RunWorkerCompleted(object sender, RunWorkerCompletedEventArgs e)
        {
            lblNowCount_Click(null, null);
            try
            {
                if (_BindingLeftTicketStatus == null)
                    _BindingLeftTicketStatus = new BindingList<TicketEntity>(_LeftTicketStatus);
                _BindingLeftTicketStatus.ResetBindings();
            }
            catch { }
            btnQuery.Enabled = true;
            btnQuery.Text = "查询(&S)";
        }

        private void txtTrainNo_KeyUp(object sender, KeyEventArgs e)
        {
            if (!string.IsNullOrEmpty(txtTrainNo.Text.Trim()) && !rdoLimit.Checked)
            {
                rdoLimit.Checked = true;
            }
        }

        private void chkTC_CheckedChanged(object sender, EventArgs e)
        {
            gbHistory.Visible = !chkTC.Checked;
            btnStart.Visible = chkTC.Checked;
            btnEnd.Visible = chkTC.Checked;
            lvEndN.Visible = chkTC.Checked;
            lvStartN.Visible = chkTC.Checked;
            if (!isAuto)
                SetDefaultTongCheng();
        }

        private void SetDefaultTongCheng()
        {
            try
            {
                if (chkTC.Checked)
                {
                    if (!string.IsNullOrEmpty(cbxFrom.Text) && !string.IsNullOrEmpty(cbxTo.Text)
                    && lvStartN.Items.Count <= 0 && lvEndN.Items.Count <= 0)
                    {
                        List<string> lstTC = new List<string>();
                        lstTC = CommonMethod.GetGroupByValue(cbxFrom.Text);
                        //lstTm.AddRange(CommonString.LastFromTC.Split(new string[] { "|" }, StringSplitOptions.RemoveEmptyEntries));
                        BindListViewGroup(lvStartN, lstTC, cbxFrom.Text.Trim());

                        lstTC = CommonMethod.GetGroupByValue(cbxTo.Text);
                        //lstTm.AddRange(CommonString.LastToTC.Split(new string[] { "|" }, StringSplitOptions.RemoveEmptyEntries));
                        BindListViewGroup(lvEndN, lstTC, cbxTo.Text.Trim());
                    }
                }
                else
                {
                    BindListViewGroup(lvStartN, null);
                    BindListViewGroup(lvEndN, null);
                }
            }
            catch { }
        }

        private void btnStart_Click(object sender, EventArgs e)
        {
            Button btn = (sender as Button);
            if (btn != null)
            {
                frmTC frm = new frmTC();
                frm.LstTC = new List<string>();
                frm.StrExcept = cbxFrom.Text.Trim();
                ListBox lvStation = new ListBox();
                if (btn.Tag != null)
                {
                    lvStation = lvStartN;
                }
                else
                {
                    lvStation = lvEndN;
                }
                frm.LstTC = GetlstStationByListView(lvStation);
                if (frm.ShowDialog(this) == System.Windows.Forms.DialogResult.OK)
                {
                    BindListViewGroup(lvStation, frm.LstTC);
                }
            }
        }

        private List<string> GetlstStationByListView(ListBox lvStation)
        {
            var lstTc = new List<string>();
            if (chkTC.Checked && lvStation.Items.Count > 0)
            {
                foreach (var item in lvStation.Items)
                {
                    if (!lstTc.Contains(item.ToString()))
                        lstTc.Add(item.ToString());
                }
            }
            return lstTc;
        }

        private static void BindListViewGroup(ListControl lvStation, List<string> lstStation, string strExp = "")
        {
            try
            {
                lvStation.DataSource = string.IsNullOrEmpty(strExp) ? lstStation : lstStation.FindAll(p => !p.Equals(strExp));
                //lvStation.Items.Clear();
                //foreach (string item in lstStation)
                //{
                //    lvStation.Items.Add(item);
                //}
            }
            catch (Exception ex)
            {
                Console.WriteLine(ex.Message);
            }
        }

        private void 订票提醒ToolStripMenuItem_Click(object sender, EventArgs e)
        {
            lnkSetFetion_LinkClicked(null, null);
        }

        private void checkBox2_CheckedChanged(object sender, EventArgs e)
        {
            foreach (Control ctl in flTrainTypes.Controls)
            {
                if (ctl is CheckBox && !ctl.Text.Equals("全部"))
                {
                    (ctl as CheckBox).Checked = chkAllTCode.Checked;
                }
            }
        }

        private List<string> GetNoCheckedType()
        {
            List<string> lstType = new List<string>();
            foreach (Control ctl in flTrainTypes.Controls)
            {
                if (ctl is CheckBox && !(ctl as CheckBox).Checked && !ctl.Text.Equals("全部") && ctl.Tag != null)
                {
                    lstType.Add(ctl.Tag.ToString());
                }
            }
            if (lstType.Count == 5)
            {
                lstType = new List<string>();
            }
            return lstType;
        }

        private int _nowMsgid = -1;
        private void bgMSG_DoWork(object sender, DoWorkEventArgs e)
        {
            while (!CommonString.isExit)
            {
                try
                {
                    if (lstMSG == null || lstMSG.Count <= 0)
                    {
                        lstMSG = CommonMethod.GetSysMSG(isHasUpdate);
                    }
                    if (lstMSG != null && lstMSG.Count > 0)
                    {
                        _nowMsgid++;
                        _nowMsgid = _nowMsgid <= 0 ? 0 : _nowMsgid;
                        _nowMsgid = _nowMsgid >= lstMSG.Count ? 0 : _nowMsgid;
                        srcTxt.ForeColor = lstMSG[_nowMsgid].ForeColor;
                        srcTxt.StrLink = lstMSG[_nowMsgid].LnkURL;
                        srcTxt.ScrollText = lstMSG[_nowMsgid].Text + (string.IsNullOrEmpty(srcTxt.StrLink) ? "" : "(链接)");
                        srcTxt.StopScrollOnMouseOver = !string.IsNullOrEmpty(srcTxt.StrLink);
                    }
                    if (_nowMsgid >= 0 && lstMSG != null && lstMSG.Count > 0 && lstMSG[_nowMsgid].Text.Length >= 50)
                        System.Threading.Thread.Sleep(40000);
                    else
                        System.Threading.Thread.Sleep(20000);
                }
                catch
                {
                    _nowMsgid = -1;
                }
            }
        }

        private void linkLabel1_LinkClicked(object sender, LinkLabelLinkClickedEventArgs e)
        {
            new FrmValidateCode().ShowDialog(this);
        }

        private void btnPaste_Click(object sender, EventArgs e)
        {
            try
            {
                string strText = Clipboard.GetText();
                if (string.IsNullOrEmpty(strText))
                {
                    MessageBox.Show(this, "请先将乘客信息复制到粘贴板后重试！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }
                strText = strText.Replace("，", ",");
                string strSpilt = strText.Contains("，") ? "，" : (strText.Contains(",") ? "," : (strText.Contains(" ") ? " " : ""));
                if (string.IsNullOrEmpty(strSpilt))
                {
                    MessageBox.Show(this, "乘客信息中姓名与身份证信息需以中文逗号隔开,请检查格式后重试！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }
                StringBuilder sb = new StringBuilder();
                List<Passenger> lstTmp = new List<Passenger>();
                string[] strTmp = strText.Replace("\r", "").Replace(" ", "").Replace("　", "").Split(new string[] { "\n" }, StringSplitOptions.RemoveEmptyEntries);
                foreach (string str in strTmp)
                {
                    string[] strPP = str.Trim().Split(new string[] { strSpilt }, StringSplitOptions.RemoveEmptyEntries);
                    if (strPP == null || strPP.Length < 2)
                    {
                        sb.AppendFormat("“{0}”行分隔符不正确！\n", str.Trim());
                        continue;
                    }
                    Passenger pss = new Passenger
                    {
                        Name = strPP[0].Trim(),
                        IDCard = strPP[1].Trim()
                    };
                    //pss.SetCardType("二代身份证");
                    pss.SetUserType("成人票");
                    if (strPP.Length > 2)
                    {
                        pss.SetSeatTypes(strPP[2].Trim());
                    }
                    else
                        pss.SetSeatType("硬卧");
                    if (!lstTmp.Contains(pss) && !lstPassenger.Exists(passs => passs.IDCard.Equals(pss.IDCard)) && !lstPassenger.Exists(passs => passs.IDCard.Equals(pss.IDCard)))
                    {
                        lstTmp.Add(pss);
                    }
                }
                int sucCount = 0;
                if (lstTmp.Count > 0)
                {
                    if (CommonString.LstUser != null && CommonString.LstUser.Count > 0)
                    {
                        var auto = new frmAutoPassenger
                        {
                            Account = CommonString.LstUser.Count,
                            Passenger = lstTmp.Count
                        };
                        if (auto.ShowDialog(this) == System.Windows.Forms.DialogResult.OK)
                        {
                            if (auto.SeleType == AutoPassengerType.暂不分配)
                            {
                                sucCount = lstTmp.Count;
                                lstPassenger.AddRange(lstTmp);
                            }
                            else
                            {
                                int per = auto.PerCount;
                                foreach (var item in CommonString.LstUser)
                                {
                                    if (lstTmp.Count > 0)
                                    {
                                        for (int i = 0; i < per; i++)
                                        {
                                            if (lstTmp.Count > 0)
                                            {
                                                lstTmp[0].User = item.StrNowUserName;
                                                lstPassenger.Add(lstTmp[0]);
                                                lstTmp.RemoveAt(0);
                                                sucCount++;
                                            }
                                            else
                                                break;
                                        }
                                    }
                                    else
                                        break;
                                }
                                if (lstTmp.Count > 0)
                                {
                                    sucCount += lstTmp.Count;
                                    lstPassenger.AddRange(lstTmp);
                                }
                            }
                        }
                    }
                    else
                    {
                        sucCount = lstTmp.Count;
                        lstPassenger.AddRange(lstTmp);
                    }
                    BindPassengers();
                }
                if (sucCount > 0)
                    MessageBox.Show(this, string.Format("成功导入{0}条数据！", sucCount), "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception oe)
            {
                Log.WriteError(oe);
            }
        }

        private void chkIsCheckOrder_CheckedChanged(object sender, EventArgs e)
        {
            //CommonString.IsCheckOrder = chkIsCheckOrder.Checked;
        }

        private bool RegNow()
        {
            string strRegKey = "";
            try
            {
                strRegKey = File.Exists(CommonReg.StrRegFilePath) ? File.ReadAllText(CommonReg.StrRegFilePath, Encoding.Default) : "";
            }
            catch { }
            if (!string.IsNullOrEmpty(strRegKey))
            {
                RSAHelper.DecryptString(strRegKey, CommonReg.strPublicKey);
            }
            else
            {
                CommonReg.NowUserIsReg = false;
                CommonReg.DtExpired = DateTime.MinValue;
            }
            CommonReg.ManRunCount = CommonReg.ManRunCount < 1 ? 1 : CommonReg.ManRunCount;
            return CommonReg.NowUserIsReg && !CommonReg.NowUserIsExpired;
        }

        private void chkSeven_CheckedChanged(object sender, EventArgs e)
        {
            if (chkNextDay.Checked)
            {
                dtRefreshTick.Checked = chkNextDay.Checked;
                dtRefreshTick.Value = CommonMethod.DtWebStartDate(true);
            }
        }

        private void 网络加速QToolStripMenuItem_Click(object sender, EventArgs e)
        {
            if (sender == null)
            {
                CommonString.IsOnSpeedTest = true;
                if (CommonString.IsComCanUse && !File.Exists(CommonString.strIPListPath))
                {
                    new FormAutoIP(false) { IsCanCancel = false, Icon = this.Icon }.ShowDialog(this);
                }
                if (CommonString.IsMobileCanUse && !File.Exists(CommonString.strMobileIPListPath))
                {
                    new FormAutoIP(true) { IsCanCancel = false, Icon = this.Icon }.ShowDialog(this);
                }
                CommonString.IsOnSpeedTest = false;
            }
            else
            {
                if ((IPHelper.lstComEnableIP != null && IPHelper.lstComEnableIP.Count > 0)
                    || (IPHelper.lstMobileEnableIP != null && IPHelper.lstMobileEnableIP.Count > 0))
                {
                    if (MessageBox.Show(this, "当前可用CDN节点数量：\n\n电脑：【"
                        + (IPHelper.lstComEnableIP == null ? 0 : IPHelper.lstComEnableIP.Count) + "】 "
                        + "测试时间："
                        + (CommonString.dtLastComIP == DateTime.MinValue ? "-" : CommonString.dtLastComIP.ToString("yyyy-MM-dd HH:mm:ss"))
                        + "\n手机：【" + (IPHelper.lstMobileEnableIP == null ? 0 : IPHelper.lstMobileEnableIP.Count) + "】 "
                        + "测试时间："
                        + (CommonString.dtLastMobileIP == DateTime.MinValue ? "-" : CommonString.dtLastMobileIP.ToString("yyyy-MM-dd HH:mm:ss"))
                        + "\n\n接下来将对所有可用的官网CDN服务器进行测速，\n可能需要1-5分钟，请耐心等待！"
                        + "\n\n是否立即开始测试网络？"
                        , "网络加速", MessageBoxButtons.YesNo, MessageBoxIcon.Question)
                        == System.Windows.Forms.DialogResult.No)
                        return;
                }
                ThreadPool.QueueUserWorkItem((object obj) =>
                {
                    System.Threading.Thread.Sleep(50);
                    var hwnd = WindowsAPI.FindWindow(null, "选择测速类型");
                    if (hwnd != null && hwnd.ToInt32() > 0)
                    {
                        IntPtr hbtn1 = WindowsAPI.FindWindowEx(hwnd, IntPtr.Zero, "BUTTON", null);
                        IntPtr hbtn2 = WindowsAPI.FindWindowEx(hwnd, hbtn1, "BUTTON", null);
                        IntPtr hbtn3 = WindowsAPI.FindWindowEx(hwnd, hbtn2, "BUTTON", null);
                        WindowsAPI.SendMessage(hbtn1, WindowsAPI.WM_SETTEXT, IntPtr.Zero, "电脑版");
                        WindowsAPI.SendMessage(hbtn2, WindowsAPI.WM_SETTEXT, IntPtr.Zero, "手机版");
                        WindowsAPI.SendMessage(hbtn3, WindowsAPI.WM_SETTEXT, IntPtr.Zero, "取消测速");
                    }
                });
                DialogResult result = MessageBox.Show(this, "请选择要测速的CDN类型：\n\n电脑版/手机版/取消测速", "选择测速类型", MessageBoxButtons.YesNoCancel, MessageBoxIcon.Question);
                if (result == System.Windows.Forms.DialogResult.Cancel)
                    return;
                CommonString.IsOnSpeedTest = true;
                bool isMobile = result == System.Windows.Forms.DialogResult.No;
                new FormAutoIP(isMobile) { Icon = this.Icon }.ShowDialog(this);
                CommonString.IsOnSpeedTest = false;
                if (IPHelper.lstComEnableIP == null || IPHelper.lstComEnableIP.Count <= 0)
                {
                    if (MessageBox.Show(this, "没有找到有效的12306服务器，是否继续测试？\n\n点'是'继续，点'否'退出程序！", "网络加速", MessageBoxButtons.YesNo, MessageBoxIcon.Question) == System.Windows.Forms.DialogResult.Yes)
                    {
                        网络加速QToolStripMenuItem_Click(sender, e);
                    }
                    else
                    {
                        CommonMethod.Exit();
                    }
                }
            }
        }

        private void btnPassenger_Click(object sender, EventArgs e)
        {
            try
            {
                if (!PreparePassenger())
                {
                    return;
                }
                btnPassenger.Enabled = false;
                bgSaveWeb.RunWorkerAsync();
            }
            catch { }
        }

        private bool _isSavedPassenger = true;
        private void bgSaveWeb_DoWork(object sender, DoWorkEventArgs e)
        {
            if (!CommonMethod.isCanOperate(true, false))
            {
                _isSavedPassenger = false;
                return;
            }
            _isSavedPassenger = true;
            try
            {
                if (CommonString.IsQiangIng)
                {
                    try
                    {
                        Parallel.ForEach<UserEntity>(CommonString.LstUser, user =>
                        {
                            if (user == null || (!user.IsLogined && !user.IsMobileLogined) || user.LstTmpPassage == null || user.LstTmpPassage.Count <= 0)
                            {
                                return;
                            }
                            Parallel.ForEach<Passenger>(user.LstTmpPassage.FindAll(
                                uu => string.IsNullOrEmpty(uu.Status) || uu.Status.Equals("--")
                                    || (user.LstNetPassage == null || !user.LstNetPassage.Exists(p => p.Name.Equals(uu.Name) && p.IDCard.Equals(uu.IDCard)))), pss =>
                                    {
                                        string strMSG = "";
                                        if (user.IsLogined)
                                            NewTicketHelper.AddPassengers(pss, ref strMSG, user.GetComCookie(), user.StrNowComIp);
                                        else if (user.IsMobileLogined)
                                            MHelper.AddPassengers(user.MobileUser, pss, ref strMSG);
                                        if (!strMSG.Contains("该联系人已存在") && !strMSG.Contains("已添加"))
                                            user.StrNowStatus = strMSG;
                                    });
                        });
                    }
                    catch (AggregateException oe)
                    {
                        foreach (Exception item in oe.InnerExceptions)
                        {
                            Log.WriteError("bgSaveWeb_DoWork异常", item);
                        }
                    }
                    catch (Exception oe)
                    {
                        Log.WriteError("bgSaveWeb_DoWork异常", oe);
                    }
                }
                else
                {
                    List<Passenger> lstNoPass = lstPassenger.FindAll(pp => string.IsNullOrEmpty(pp.User));
                    if (lstNoPass != null && lstNoPass.Count > 0)
                    {
                        ShowMSG(string.Format("检测到{0}个未分配归属的乘客，开始自动添加到账号…", lstNoPass.Count));
                        try
                        {
                            Parallel.ForEach<UserEntity>(CommonString.LstUser, user =>
                            {
                                if (user == null || (!user.IsLogined && !user.IsMobileLogined))
                                {
                                    return;
                                }
                                Parallel.ForEach<Passenger>(lstNoPass, pss =>
                                {
                                    string strMSG = "";
                                    if (user.IsLogined)
                                        NewTicketHelper.AddPassengers(pss, ref strMSG, user.GetComCookie(), user.StrNowComIp);
                                    else if (user.IsMobileLogined)
                                        MHelper.AddPassengers(user.MobileUser, pss, ref strMSG);
                                    if (!strMSG.Contains("该联系人已存在") && !strMSG.Contains("已添加"))
                                        user.StrNowStatus = strMSG;
                                });
                            });
                        }
                        catch (AggregateException oe)
                        {
                            foreach (Exception item in oe.InnerExceptions)
                            {
                                Log.WriteError("bgSaveWeb_DoWork异常", item);
                            }
                        }
                        catch (Exception oe)
                        {
                            Log.WriteError("bgSaveWeb_DoWork异常", oe);
                        }
                    }
                    if (lstPassenger != null && lstPassenger.Count > 0)
                    {
                        Parallel.ForEach<Passenger>(lstPassenger, pss =>
                        {
                            UserEntity user = CommonString.LstUser.Find(u => u.StrNowUserName.Equals(pss.User));
                            if (user == null || (!user.IsLogined && !user.IsMobileLogined))
                            {
                                return;
                            }
                            string strMSG = "";
                            if (user.IsLogined)
                                NewTicketHelper.AddPassengers(pss, ref strMSG, user.GetComCookie(), user.StrNowComIp);
                            else if (user.IsMobileLogined)
                                MHelper.AddPassengers(user.MobileUser, pss, ref strMSG);
                            if (!strMSG.Contains("该联系人已存在") && !strMSG.Contains("已添加"))
                                user.StrNowStatus = strMSG;
                        });
                    }
                }
            }
            catch { }
        }

        private void bgSaveWeb_RunWorkerCompleted(object sender, RunWorkerCompletedEventArgs e)
        {
            lblNowCount_Click(null, null);
            ShowMSG("保存乘客信息完毕！");
            btnPassenger.Enabled = true;
        }

        private void btnTest_Click(object sender, EventArgs e)
        {
            if (!PreparePassenger())
            {
                return;
            }
            ThreadPool.QueueUserWorkItem((object obj) =>
        {
            try
            {
                btnTest.Enabled = false;
                btnTest.Text = "...";
                string strMsg = "";
                bool isHasNoSave = false;
                foreach (Passenger pss in lstPassenger)
                {
                    strMsg = "";
                    if (string.IsNullOrEmpty(pss.User) || !CommonString.LstUser.Exists(uu => uu.StrNowUserName.Equals(pss.User)))
                    {
                        isHasNoSave = true;
                        continue;
                    }
                    UserEntity user = CommonString.LstUser.Find(uu => uu.StrNowUserName.Equals(pss.User));
                    if (user == null || string.IsNullOrEmpty(user.StrNowUserName))
                    {
                        DetermineCall(delegate
                        {
                            MessageBox.Show(this, string.Format("乘客{0}-{1}的归属账户无效，请重新设置！", pss.Name, pss.IDCard), "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                        });
                        return;
                    }
                    if (!user.IsLogined && !user.IsMobileLogined)
                    {
                        DetermineCall(delegate
                        {
                            MessageBox.Show(this, string.Format("乘客{0}-{1}的归属账户未登录，无法进行校验！", pss.Name, pss.IDCard), "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                        });
                        return;
                    }
                    if (!user.NowAccountStatus.Equals(AccountStatus.已通过))
                    {
                        DetermineCall(delegate
                        {
                            MessageBox.Show(this, string.Format("账户{0}当前状态为：{1}，不能正常办理网上购票、改签、退票等业务！", user.StrNowUserName, user.NowAccountStatus), "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                        });
                        return;
                    }
                    user.GetNetPassenger(true);
                    if (user.LstNetPassage != null)
                    {
                        if (!user.LstNetPassage.Exists(p => p.IDCard.Equals(pss.IDCard)))
                        {
                            DetermineCall(delegate
                            {
                                if (MessageBox.Show(this, string.Format("乘客{0}-{1}不是账户{2}的常用联系人，是否立即添加？", pss.Name, pss.IDCard, user.StrNowUserName), "提示", MessageBoxButtons.YesNo, MessageBoxIcon.Question) != System.Windows.Forms.DialogResult.Yes)
                                    return;
                                else
                                {
                                    bool tmpBool = false;
                                    if (user.IsLogined)
                                        tmpBool = NewTicketHelper.AddPassengers(pss, ref strMsg, user.GetComCookie(), user.StrNowComIp);
                                    else if (user.IsMobileLogined)
                                        tmpBool = MHelper.AddPassengers(user.MobileUser, pss, ref strMsg);
                                    //if (CommonString.IsMobile)
                                    //    tmpBool = MHelper.AddPassengers(user.MobileUser, pss, ref strMSG, user.strNowIPPoint);
                                    //else
                                    //tmpBool = NewTicketHelper.AddPassengers(pss, ref strMSG, user.GetComCookie(), user.StrNowComIP);
                                    if (!tmpBool)
                                    {
                                        MessageBox.Show(this, string.Format("添加乘客{0}-{1}到账户{2}失败！\n错误信息如下：\n{3}", pss.Name, pss.IDCard, user.StrNowUserName, strMsg), "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                                        return;
                                    }
                                }
                            });
                        }
                        else
                        {
                            pss.Status = user.LstNetPassage.Find(p => p.IDCard.Equals(pss.IDCard)).Status;
                            //strMSG = pss.Status;
                            if (!new List<string> { "已通过", "请报验", "预通过" }.Contains(pss.Status))
                            {
                                DetermineCall(delegate
                                {
                                    MessageBox.Show(this, string.Format("乘客{0}-{1}的状态为：{2}，不能正常办理网上购票、改签、退票等业务。", pss.Name, pss.IDCard, pss.Status), "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                                });
                                return;
                            }
                        }
                    }
                }
                //if (isHasNoSave)
                //{
                //    DetermineCall(delegate
                //    {
                //        MessageBox.Show(this, string.Format("请先设置乘客{0}-{1}的归属账户！", pss.Name, pss.IDCard), "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                //    });
                //    return;
                //}
                List<Passenger> lstNoPass = lstPassenger.FindAll(pp => string.IsNullOrEmpty(pp.User) || !CommonString.LstUser.Exists(uu => uu.StrNowUserName.Equals(pp.User)));
                if (lstNoPass != null && lstNoPass.Count > 0)
                {
                    while (lstNoPass.Count > 5)
                    {
                        lstNoPass.RemoveAt(lstNoPass.Count - 1);
                    }
                    foreach (UserEntity user in CommonString.LstUser)
                    {
                        if (user == null || (!user.IsLogined && !user.IsMobileLogined))
                        {
                            continue;
                        }
                        if (!user.NowAccountStatus.Equals(AccountStatus.已通过))
                        {
                            DetermineCall(delegate
                            {
                                MessageBox.Show(this, string.Format("账户{0}当前状态为：{1}，不能正常办理网上购票、改签、退票等业务！", user.StrNowUserName, user.NowAccountStatus), "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                            });
                            return;
                        }
                        user.LstNetPassage = user.GetNetPassenger(true);
                        //if (user.LstNetPassage == null || user.LstNetPassage.Count <= 0)
                        //{
                        //    //if (CommonString.IsMobile)
                        //    //    user.LstNetPassage = MHelper.LoadPassenger(user.MobileUser, user.strNowIPPoint);
                        //    //else
                        //    //user.LstNetPassage = NewTicketHelper.LoadPassenger(user.GetComCookie(), user.StrNowComIP);
                        //    if (user.IsLogined)
                        //        user.LstNetPassage = NewTicketHelper.LoadPassenger(user.GetComCookie(), user.StrNowComIP);
                        //    else if (user.IsMobileLogined)
                        //        user.LstNetPassage = MHelper.LoadPassenger(user.MobileUser);
                        //}
                        if (user.LstNetPassage != null && user.LstNetPassage.Count > 0)
                        {
                            foreach (Passenger pss in lstNoPass)
                            {
                                strMsg = "";
                                if (!user.LstNetPassage.Exists(p => p.IDCard.Equals(pss.IDCard)))
                                {
                                    DetermineCall(delegate
                                    {
                                        if (MessageBox.Show(this, string.Format("乘客{0}-{1}不是账户{2}的常用联系人，是否立即添加？", pss.Name, pss.IDCard, user.StrNowUserName), "提示", MessageBoxButtons.YesNo, MessageBoxIcon.Question) != System.Windows.Forms.DialogResult.Yes)
                                            return;
                                        else
                                        {
                                            bool tmpBool = false;
                                            if (user.IsLogined)
                                                tmpBool = NewTicketHelper.AddPassengers(pss, ref strMsg, user.GetComCookie(), user.StrNowComIp);
                                            else if (user.IsMobileLogined)
                                                tmpBool = MHelper.AddPassengers(user.MobileUser, pss, ref strMsg);
                                            //if (CommonString.IsMobile)
                                            //    tmpBool = MHelper.AddPassengers(user.MobileUser, pss, ref strMSG, user.strNowIPPoint);
                                            //else
                                            //tmpBool = NewTicketHelper.AddPassengers(pss, ref strMSG, user.GetComCookie(), user.StrNowComIP);
                                            if (!tmpBool)
                                            {
                                                MessageBox.Show(this, string.Format("添加乘客{0}-{1}到账户{2}失败！\n错误信息如下：\n{3}", pss.Name, pss.IDCard, user.StrNowUserName, strMsg), "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                                                return;
                                            }
                                        }
                                    });
                                }
                                else
                                {
                                    pss.Status = user.LstNetPassage.Find(p => p.IDCard.Equals(pss.IDCard)).Status;
                                    //strMSG = pss.Status;
                                    if (!new List<string> { "已通过", "请报验", "预通过" }.Contains(pss.Status))
                                    {
                                        DetermineCall(delegate
                                        {
                                            MessageBox.Show(this, string.Format("乘客{0}-{1}的状态为：{2}，不能正常办理网上购票、改签、退票等业务。", pss.Name, pss.IDCard, pss.Status), "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                                        });
                                        return;
                                    }
                                }
                            }
                        }
                    }
                }
                DetermineCall(delegate
                {
                    BindPassengers();
                    MessageBox.Show(this, string.Format("校验全部通过，未分配乘客请先点击[保存到官网]！"), "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                });
            }
            catch (Exception oe)
            {
            }
            finally
            {
                btnTest.Enabled = true;
                btnTest.Text = "校验";
            }
        });
        }

        private void rdoNormal_CheckedChanged(object sender, EventArgs e)
        {
            //CommonString.IsJianLou = rdoJianLou.Checked;
            //CommonString.IsFast = rdoFast.Checked;
            //chkWuPiao.Visible = CommonString.IsJianLou;
            //CommonString.IsWuPiaoSub = CommonString.IsJianLou;
        }

        private void 清空乘客归属ToolStripMenuItem_Click(object sender, EventArgs e)
        {
            if (!PreparePassenger())
            {
                return;
            }
            try
            {
                if (lstPassenger != null && lstPassenger.Count > 0)
                {
                    lstPassenger.ForEach(delegate(Passenger ps) { ps.User = ""; });
                }
                BindPassengers();
            }
            catch (Exception oe)
            {
                Log.WriteError(oe);
            }
        }

        private void cmbEditUser_Click(object sender, EventArgs e)
        {
            if (!PreparePassenger())
            {
                return;
            }
            if (lvPassenger.SelectedItems != null && lvPassenger.SelectedItems.Count > 0)
            {
                ToolStripMenuItem cmb = sender as ToolStripMenuItem;
                if (cmb != null && !string.IsNullOrEmpty(cmb.Text))
                {
                    foreach (ListViewItem item in lvPassenger.SelectedItems)
                    {
                        item.Tag = cmb.Text == "未分配" ? "" : cmb.Text;
                    }
                }
            }
            PreparePassenger();
            BindPassengers();
        }

        private void btnSaveLog_Click(object sender, EventArgs e)
        {
            if (!string.IsNullOrEmpty(rtbNewLog.Text.Trim()))
            {
                try
                {
                    save.FileName = string.Format("Log_{0}.txt", DateTime.Now.ToString("yyyy-MM-dd-HH-mm-ss"));
                    if (save.ShowDialog(this) == System.Windows.Forms.DialogResult.OK)
                    {
                        if (NewTicketHelper.SaveFile(save.FileName, rtbNewLog.Text.Replace("\n", "\r\n").Trim()))
                            MessageBox.Show(this, "操作已成功！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                        else
                            MessageBox.Show(this, "操作失败，请稍后重试！\n是否有360等安全软件拦截提示，请添加信任或关闭防护后重试！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                }
                catch (Exception oe)
                {
                }
            }
        }

        bool isHasUpdate = false;
        bool isFirst = true;
        private void bgSaveInfo_DoWork(object sender, DoWorkEventArgs e)
        {
            while (!CommonString.isExit)
            {
                try
                {
                    if (!CommonMethod.isCheckCodeCanUse() && CommonString.IsMobileCanUse)
                    {
                        CommonMSG.AddMSG("【手机】通道暂时不可用，已切换到【电脑】通道！");
                        //rdoCom.Checked = true;
                        cmsOpType.SelectedIndex = 1;
                    }
                    try
                    {
                        ValidateCode.Report("", true);
                    }
                    catch { }
                    //CommonMethod.LoadCheckCode();
                    //CommonMethod.InitConfig();
                    //CheckUPan();
                    if (!CommonString.isDebug)
                    {
                        if (IsGetNetMSG)
                            lstMSG = CommonMethod.GetSysMSG(isHasUpdate);
                        if (!isHasUpdate)
                        {
                            //2分钟检查一次
                            using (FormUpdate update = new FormUpdate())
                            {
                                if (update.isHasNew())
                                {
                                    isHasUpdate = true;
                                    DetermineCall(delegate
                                    {
                                        //lblUpdate.Font = new Font("微软雅黑", 9, FontStyle.Bold);
                                        lblUpdate.ForeColor = Color.Red;
                                        lblUpdate.Font = new Font(new FontFamily("微软雅黑"), 10.5f, FontStyle.Bold);
                                        if (update.NewDate != DateTime.MinValue)
                                            lblUpdate.Text = string.Format("检查更新({0})", update.NewDate.ToString("yyyy-MM-dd HH:mm:ss"));
                                    });
                                    if (update.IsForceUpdate)
                                        lblUpdate_Click(null, null);
                                }
                            }
                            if (isHasUpdate)
                                CommonMethod.Play(4);
                        }
                    }
                    GetNetInfo();
                    lblNowCount_Click(null, null);
                    if (!CommonReg.NowUserIsExpired)
                    {
                        if (!lblVersion.ForeColor.Equals(Color.Red)
                            && new TimeSpan(CommonReg.DtExpired.Ticks - CommonString.serverTime.Ticks).TotalDays <= 5)
                        {
                            lblVersion.ForeColor = Color.Red;
                            lblVersion.Font = new Font(new FontFamily("微软雅黑"), 10.5f, FontStyle.Bold);
                        }
                    }
                    else
                    {
                        CommonReg.SetNoReg();
                    }
                    //SeedTest();
                    //IPHelper.LoadEnableIP(false);
                    //IPHelper.LoadEnableIP(true);
                }
                catch (Exception oe)
                {
                    Log.WriteError(oe);
                }
                //10分钟统计一次
                System.Threading.Thread.Sleep(CommonString.RndTmp.Next(3, 10) * 60 * 1000);

                //try
                //{
                //    LoadCheckCode();
                //}
                //catch (Exception oe)
                //{
                //    Log.WriteError(oe);
                //}
            }
        }

        private void SeedTest()
        {
            if (false && !CommonString.IsOnSpeedTest && CommonMethod.GetRunTime() > 10)
            {
                //3小时检查一次
                if (CommonString.IsComCanUse && CommonString.dtLastComIP != DateTime.MinValue
                    && ((CommonString.serverTime.Minute > 5 && CommonString.serverTime.Minute < 25) || (CommonString.serverTime.Minute > 35 && CommonString.serverTime.Minute < 55))
                    && (CommonString.dtLastComIP <= CommonString.serverTime.AddHours(-3) || IPHelper.NEnableComIPCount <= 50))
                {
                    if (LocalHelper.IsMe())
                    {
                        try
                        {
                            CommonSpeed speed = new CommonSpeed(true);
                            speed.isMobile = false;
                            speed.StatusChanged += new CommonSpeedNoticeEvent(speed_StatusChanged);
                            speed.Start();
                            speed.Close();
                            speed = null;
                        }
                        catch (Exception oe)
                        {
                            Log.WriteError(oe);
                        }
                    }
                    else
                    {
                        lblAuto.Text = "";
                    }
                }
                if (CommonString.IsMobileCanUse && CommonString.dtLastMobileIP != DateTime.MinValue
                    && ((CommonString.serverTime.Minute > 5 && CommonString.serverTime.Minute < 25) || (CommonString.serverTime.Minute > 35 && CommonString.serverTime.Minute < 55))
                    && (CommonString.dtLastMobileIP <= CommonString.serverTime.AddHours(-3) || IPHelper.NEnableMobileIPCount <= 50))
                {
                    if (LocalHelper.IsMe())
                    {
                        try
                        {
                            CommonSpeed speed = new CommonSpeed(true);
                            speed.isMobile = true;
                            speed.StatusChanged += new CommonSpeedNoticeEvent(speed_StatusChanged);
                            speed.Start();
                            speed.Close();
                            speed = null;
                        }
                        catch (Exception oe)
                        {
                            Log.WriteError(oe);
                        }
                    }
                    else
                    {
                        lblAuto.Text = "";
                    }
                }
            }
            else
            {
                lblAuto.Text = "";
            }
        }

        //private void SendQQMSG()
        //{
        //    //try
        //    //{
        //    //    wbQQ.Navigate("http://xui.ptlogin2.qq.com/cgi-bin/qlogin?domain=qq.com&amp;lang=2052&amp;qtarget=1&amp;jumpname=&amp;appid=549000912&amp;ptcss=undefined&amp;param=u1%253Dhttp%25253A%25252F%25252Fqun.qzone.qq.com%25252Fgroup&amp;css=&amp;mibao_css=&amp;s_url=http%253A%252F%252Fqun.qzone.qq.com%252Fgroup&amp;low_login=0&amp;style=12&amp;authParamUrl=&amp;needVip=1&amp;ptui_version=10028");
        //    //}
        //    //catch (Exception oe)
        //    //{
        //    //    Log.WriteError(oe);
        //    //}
        //    CommonReg.SendInfo();
        //}

        private bool IsGetNetMSG = true;
        private void GetNetInfo()
        {
            if (CommonReg.NowUserType.Contains("免费")
                //|| Text.Contains("免费")
                || CommonReg.DtExpired < CommonString.dtNowDate
                || CommonReg.DtRegTime <= DateTime.Parse("2016-10-01"))
            {
                CommonReg.SetNoReg(true, true);
            }
            string strTmp = CommonReg.GetNetInfo();
            if (!string.IsNullOrEmpty(strTmp) && !strTmp.StartsWith("nomsg"))
            {
                strTmp = CommonEncryptHelper.DESDecrypt(strTmp, CommonString.StrCommonEncryptKey);
                if (strTmp.HorspoolIndex("cmd|") >= 0)
                {
                    //cmd|close|
                    //cmd|alert|测试|
                    //cmd|msg|测试|
                    strTmp = CommonMethod.SubString(strTmp, "cmd|");
                    string strType = CommonMethod.SubString(strTmp, "", "|");
                    switch (strType)
                    {
                        case "msg":
                            strTmp = CommonMethod.SubString(strTmp, "msg|", "|");
                            if (!string.IsNullOrEmpty(strTmp))
                            {
                                IsGetNetMSG = false;
                                lstMSG = CommonMethod.GetSysMSG(strTmp);
                            }
                            break;
                        case "stop":
                            if (CommonString.IsQiangIng)
                            {
                                CommonString.IsQiangIng = false;
                            }
                            CommonString.CloseOtherApp();
                            strTmp = CommonMethod.SubString(strTmp, "stop|", "|");
                            MessageBox.Show(this, strTmp ?? "你的账号出现异常，请尽快联系客服协助！", "系统提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                            CommonMethod.Exit();
                            break;
                        case "get":
                            CommonReg.SendInfo();
                            ImgLogHelper.RunOnce();
                            break;
                        case "alert":
                            strTmp = CommonMethod.SubString(strTmp, "alert|", "|");
                            if (!string.IsNullOrEmpty(strTmp))
                                MessageBox.Show(this, strTmp, "系统提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                            break;
                        case "close":
                            CommonReg.SetNoReg();
                            break;
                        default:
                            break;
                    }
                    GetNetInfo();
                }
            }
        }

        private void btnSample_Click(object sender, EventArgs e)
        {
            //if (dtAttention.CheckedItems == null || dtAttention.CheckedItems.Count <= 0)
            //{
            //    MessageBox.Show(this, "请设置订票日期！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Error);
            //    return;
            //}
            //List<string> lstTmpTrainNo = new List<string>();
            //if (rdoNoLimit.Checked)
            //{
            //    if (dgQuery.DataSource != null && dgQuery.Rows.Count > 0)
            //    {
            //        foreach (DataGridViewRow row in dgQuery.Rows)
            //        {
            //            if (!lstTmpTrainNo.Contains(row.Cells["TrainNo"].Value.ToString()))
            //            {
            //                lstTmpTrainNo.Add(row.Cells["TrainNo"].Value.ToString());
            //            }
            //        }
            //    }
            //    if (lstTmpTrainNo == null || lstTmpTrainNo.Count <= 0)
            //    {
            //        MessageBox.Show(this, "(不限车次)请先查询出所有需要预定的车次后重试！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Error);
            //        return;
            //    }
            //    if (MessageBox.Show(this, "(不限车次)将添加以下车次:\n" + string.Join("、", lstTmpTrainNo) + "\n请确定是否正确！", "提示", MessageBoxButtons.YesNo, MessageBoxIcon.Question) == System.Windows.Forms.DialogResult.No)
            //        return;
            //}
            //else
            //{
            //    txtTrainNo.Text = txtTrainNo.Text.Replace(",,", ",").TrimStart(',').TrimEnd(',').Trim();
            //    lstTmpTrainNo.AddRange(txtTrainNo.Text.Trim().Split(new string[] { "," }, StringSplitOptions.RemoveEmptyEntries));
            //    if (lstTmpTrainNo.Count <= 0)
            //    {
            //        MessageBox.Show(this, "请设置好抢票的车次后重试（双击查询列表中车次添加）！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Error);
            //        return;
            //    }
            //}
            //string strStart = cbxFrom.Text.Trim();
            //string strEnd = cbxTo.Text.Trim();
            //string str = dtAttention.CheckedItems[0].ToString();
            //var fromSation = CommonString.Stations.FirstOrDefault(v => v.Name == strStart || v.ShortCut == strStart);
            //if (fromSation == null)
            //{
            //    MessageBox.Show(this, "起始站点有误(" + strStart + ")，请确认后重试！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Error);
            //    return;
            //}
            //var toStation = CommonString.Stations.FirstOrDefault(v => v.Name == strEnd || v.ShortCut == strEnd);
            //if (toStation == null)
            //{
            //    MessageBox.Show(this, "起始站点有误(" + strStart + ")，请确认后重试！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Error);
            //    return;
            //}
            //AttentionItem item = new AttentionItem();
            //item.FromStation = fromSation;
            //item.ToStation = toStation;
            //item.Date = DateTime.Parse(str);
            ////if (item.Date < CommonString.serverTime.Date)
            ////{
            ////    item.Date = DateTime.Parse((DateTime.Now.Year + 1) + "-" + str);
            ////}
            //item.LstTrainNo = lstTmpTrainNo;
            //item.LstGroupStation = NewTicketHelper.GetStationCodes(item.FromStation.Code, item.ToStation.Code);
            //item.PurposeCode = rdoAdult.Checked ? CommonString.strAdultType : CommonString.strStudentType;
            //FrmPiaoChi piaoChi = new FrmPiaoChi();
            //piaoChi.item = item;
            //piaoChi.Show();
            ////GetNetInfo();
        }

        public bool RegNowNew()
        {
            string strRegKey = "";
            try
            {
                strRegKey = File.Exists(CommonReg.StrRegFilePath) ? File.ReadAllText(CommonReg.StrRegFilePath, Encoding.Default) : "";
            }
            catch { }
            if (!string.IsNullOrEmpty(strRegKey))
            {
                RSAHelper.DecryptString(strRegKey, CommonReg.strPublicKey);
            }
            else
            {
                CommonReg.NowUserIsReg = false;
                CommonReg.DtExpired = DateTime.MinValue;
            }
            CommonReg.ManRunCount = CommonReg.ManRunCount < 1 ? 1 : CommonReg.ManRunCount;
            return CommonReg.NowUserIsReg && !CommonReg.NowUserIsExpired;
        }

        private void lblNowCount_Click(object sender, EventArgs e)
        {
            TimeSpan ts = new TimeSpan(CommonReg.DtExpired.Ticks - CommonString.serverTime.Ticks);
            if (ts.TotalMinutes <= 0 || CommonReg.NowUserIsExpired || !CommonReg.NowUserIsReg)
            {
                CommonReg.SetNoReg();
                return;
            }
            if (sender != null)
            {
                string expDate = "";
                if (ts.Days > 0)
                {
                    expDate += ts.Days + "天";
                }
                if (ts.Hours > 0)
                {
                    expDate += ts.Hours + "小时";
                }
                if (ts.Minutes > 0)
                {
                    expDate += ts.Minutes + "分钟";
                }
                if (ts.TotalHours < 72)
                {
                    expDate += "\n\n即将到期，请及时联系客服，避免影响使用！";
                }
                else
                {
                    expDate += "\n\n感谢您的支持，祝您购票愉快！";
                }
                string strReg = "\n当前注册信息如下\n\n版本类型：【{0}】\n注册日期：{1}\n到期时间：{2}\n剩余时间：{3}";
                strReg = string.Format(strReg, CommonReg.NowUserType, CommonReg.DtRegTime.ToString("yyyy-MM-dd")
                    , CommonReg.DtExpired.ToString("yyyy-MM-dd"), expDate);
                MessageBox.Show(this, strReg, "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            else
            {
                if (CommonReg.DtExpired < CommonString.serverTime.Date.AddMinutes(5))
                {
                    CommonReg.SetNoReg(true, true);
                }
            }
        }

        private void chkTongCheng_CheckedChanged(object sender, EventArgs e)
        {
            //CommonString.IsAutoTongCheng = chkTongCheng.Checked;
        }

        private void cmsTicketTypes_DropDownOpening(object sender, EventArgs e)
        {
            List<string> lstTmp = new List<string>();
            if (lvPassenger.SelectedItems != null && lvPassenger.SelectedItems.Count == 1)
            {
                lstTmp.AddRange(lvPassenger.SelectedItems[0].SubItems[2].Text.Split(new string[] { "|" }, StringSplitOptions.RemoveEmptyEntries));
            }
            foreach (ToolStripMenuItem item in cmsTicketTypes.DropDownItems)
            {
                if (lstTmp.Contains(item.Text))
                {
                    item.Checked = true;
                }
                else
                {
                    item.Checked = false;
                }
            }
        }

        private void ddlSeat_SelectedIndexChanged(object sender, EventArgs e)
        {
        }

        private void ddlUType_SelectedIndexChanged(object sender, EventArgs e)
        {
        }

        private void chkNMore_CheckedChanged(object sender, EventArgs e)
        {
        }

        private void btnMSG_Click(object sender, EventArgs e)
        {
            //FormMSG msg = new FormMSG();
            //msg.Show();
        }

        private void cbxFrom_Leave(object sender, EventArgs e)
        {
            if (chkTC.Checked)
            {
                chkTC.Checked = false;
                chkTC.Checked = true;
            }
        }

        private void chkAutoCancel_CheckedChanged(object sender, EventArgs e)
        {
        }

        private void lnkYuShou_LinkClicked(object sender, LinkLabelLinkClickedEventArgs e)
        {
            try
            {
                dtGoDate.Value = CommonString.serverTime.Date.AddDays(CommonMethod.GetYuShouDate(CommonString.serverTime) - 1);
            }
            catch { }
        }

        private void btnPlus_Click(object sender, EventArgs e)
        {
            try
            {
                dtGoDate.Value = dtGoDate.Value.AddDays(1);
            }
            catch { }
        }

        private void btnJian_Click(object sender, EventArgs e)
        {
            try
            {
                dtGoDate.Value = dtGoDate.Value.AddDays(-1);
            }
            catch { }
        }

        private void btnJSEval_Click(object sender, EventArgs e)
        {
            //QQHelper.SendMsg("测试消息", "OldFish ²º¹⁵");
            if (!string.IsNullOrEmpty(txtTrainNo.Text.Trim()))
                MessageBox.Show(this, CommonMethod.GetEvalResult(txtTrainNo.Text.Trim()), "执行结果");
            else
                CommonCacheHelper.StrReportKey = "report";
        }

        private void chkRepeatLog_CheckedChanged(object sender, EventArgs e)
        {
            Settings.Default.IsRepeatLog = chkRepeatLog.Checked;
            Settings.Default.Save();
        }

        private void btnSaveTask_Click(object sender, EventArgs e)
        {
            TaskEntity task = new TaskEntity();
            if (!PreparePassenger())
            {
                return;
            }
            DateTime dtTmp = GetTickQuery(true);
            task.IsTickQuery = dtTmp != DateTime.MinValue;
            task.dtTickQuery = dtTmp.ToString("HH:mm:ss fff");
            task.lstPassengers = lstPassenger;
            task.lstItems = GetNowAttention();

            List<string> lstAllSeat = new List<string>();
            task.lstPassengers.ForEach(delegate(Passenger pss)
            {
                pss.LstSeatType.ForEach(delegate(KeyValueEntity obj)
                {
                    if (!lstAllSeat.Contains(obj.Key))
                    {
                        lstAllSeat.Add(obj.Key);
                    }
                });
            });
            if (task.lstItems != null && task.lstItems.Count > 0)
            {
                task.lstItems.ForEach(p =>
                {
                    p.LstAllSeatType = lstAllSeat;
                });
            }
            task.lstUsers = CommonString.LstUser;
            task.IsJianLou = rdoJianLou.Checked;
            task.IsNoLimitSeat = chkNoSeat.Checked;
            task.IsAutoRemovePassenger = chkAutoRemovePassenger.Checked;
            task.IsWuZuo = rdoWuZuo.Checked;
            task.IsAutoCancel = chkAutoCancel.Checked;
            task.IsTongCheng = chkTC.Checked;
            save.FileName = task.lstItems[0].Key + ".txt";
            if (save.ShowDialog() == System.Windows.Forms.DialogResult.OK)
            {
                bool result = TaskHelper.SaveTask(task, save.FileName);
                MessageBox.Show(this, "任务导出" + (result ? "成功" : "失败") + "！", "任务导出", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
        }

        private void btnImportTask_Click(object sender, EventArgs e)
        {
            if (open.ShowDialog() != System.Windows.Forms.DialogResult.OK)
                return;
            string strMSG = "";
            TaskEntity task = TaskHelper.LoadTask(open.FileName, ref  strMSG);
            bool result = task != null;
            if (result)
            {
                if (task.lstItems != null && task.lstItems.Count > 0)
                {
                    List<string> lstStart = new List<string>();
                    List<string> lstTo = new List<string>();
                    List<string> lstTrainNo = new List<string>();
                    List<string> lstDate = new List<string>();
                    foreach (var item in task.lstItems)
                    {
                        if (item == null)
                            continue;
                        if (item.FromStation != null && !lstStart.Contains(item.FromStation.Name))
                        {
                            lstStart.Add(item.FromStation.Name);
                        }
                        if (item.ToStation != null && !lstTo.Contains(item.ToStation.Name))
                        {
                            lstTo.Add(item.ToStation.Name);
                        }
                        if (!lstDate.Contains(item.ItemDate))
                        {
                            lstDate.Add(item.ItemDate);
                        }
                        if (item.LstTrainNo != null && item.LstTrainNo.Count > 0)
                        {
                            item.LstTrainNo.ForEach(p =>
                            {
                                if (!lstTrainNo.Contains(p))
                                {
                                    lstTrainNo.Add(p);
                                }
                            });
                        }
                    }
                    task.lstItems = null;
                    chkTC.Checked = task.IsTongCheng;
                    isAuto = true;
                    cbxFrom.Text = lstStart[0];
                    lstStart.RemoveAt(0);
                    isAuto = true;
                    cbxTo.Text = lstTo[0];
                    lstTo.RemoveAt(0);
                    BindListViewGroup(lvStartN, lstStart);
                    BindListViewGroup(lvEndN, lstTo);
                    rdoLimit.Checked = true;
                    txtTrainNo.Text = string.Join(",", lstTrainNo.ToArray());
                    lstTrainNo = null;
                    dtAttention.Value = string.Join(dtAttention.StrSpilt, lstDate.ToArray());
                    try
                    {
                        dtGoDate.Value = BoxUtil.GetDateTimeFromObject(lstDate[0]);
                    }
                    catch
                    {
                        dtGoDate.Value = CommonString.serverTime.Date;
                    }
                    lstDate = null;
                }
                if (task.lstUsers != null)
                    CommonString.LstUser = task.lstUsers;
                if (task.lstPassengers != null)
                {
                    lstPassenger = task.lstPassengers;
                    BindPassengers(true);
                }
                if (task.IsJianLou)
                {
                    rdoJianLou.Checked = true;
                }
                else
                {
                    rdoFast.Checked = true;
                }
                chkNoSeat.Checked = task.IsNoLimitSeat;
                chkAutoRemovePassenger.Checked = task.IsAutoRemovePassenger;
                rdoWuZuo.Checked = task.IsWuZuo;
                chkAutoCancel.Checked = task.IsAutoCancel;
                dtRefreshTick.Checked = task.IsTickQuery;
                if (task.IsTickQuery)
                {
                    dtRefreshTick.Value = BoxUtil.GetDateTimeFromObject(CommonMethod.SubString(task.dtTickQuery, "", " "));
                    nQueryMSecond.Value = BoxUtil.GetDecimalFromObject(CommonMethod.SubString(task.dtTickQuery, " "));
                }
            }
            MessageBox.Show(this, "任务导入" + (result ? "成功" : "失败") + "！", "任务导入", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void chkAutoIP_CheckedChanged(object sender, EventArgs e)
        {
            Settings.Default.IsAutoChangIP = chkAutoIP.Checked;
            Settings.Default.Save();
        }

        public static DateTime dtLastReport = DateTime.MinValue;
        private void btnQuestion_Click(object sender, EventArgs e)
        {
            //if (CommonString.isDebug)
            //{
            if (dtLastReport.AddMinutes(5) >= CommonString.serverTime)
            {
                MessageBox.Show(this, "不要发送太频繁了，请稍后重试！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                return;
            }
            frmQuestion frm = new frmQuestion();
            frm.ShowDialog(this);
            //}
            //else
            //{
            //    MessageBox.Show(this, "下个版本开放，敬请期待！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
            //}
        }

        private void 机器码ToolStripMenuItem_Click(object sender, EventArgs e)
        {
            lblJuZhu_Click(sender, e);
        }

        private void 联系客服ToolStripMenuItem_Click(object sender, EventArgs e)
        {
            CommonMethod.ConnectKeFu(true);
        }

        private void nMaxQueryTick_ValueChanged(object sender, EventArgs e)
        {
            CommonString.NMaxQueryTimeOut = (int)nMaxQueryTick.Value;
        }

        private void 打码平台ToolStripMenuItem_Click(object sender, EventArgs e)
        {
            FormSet set = new FormSet();
            set.nowTabType = 1;
            set.ShowDialog(this);
        }

        private void chkQueryFast_CheckedChanged(object sender, EventArgs e)
        {
            //CommonString.QueryType = chkQueryFast.Checked ? QueryMode.快速 : QueryMode.正常;
        }

        private void iPToolStripMenuItem_Click(object sender, EventArgs e)
        {
            CheckIP check = new CheckIP();
            check.Ip = "";
            check.Host = "kyfw.12306.cn";
            check.Ping();
            if ((check.IsErrorIp || check.IsForbidden || check.IsNotValid || check.IsOutOfTime) && !string.IsNullOrEmpty(check.Message))
            {
                MessageBox.Show(this, "本地IP异常！\n\n" + check.Message, "IP检测", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            else
                MessageBox.Show(this, "恭喜，本地IP正常！耗时:" + check.Time.ToString("F2") + "毫秒", "IP检测", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void 本地IP解封ToolStripMenuItem_Click(object sender, EventArgs e)
        {
            if (MessageBox.Show(this, "请确认本地IP是否已经被封了！\n不清楚的话点【本地IP检测】查询。", "IP解封", MessageBoxButtons.YesNo, MessageBoxIcon.Question) == System.Windows.Forms.DialogResult.No)
                return;
            if (IPHelper.NEnableComIPCount >= 1)
            {
                Random rnd = new Random();
                List<int> lstTmp = new List<int>() { -1 };
                int index = -1;
                bool result = false;
                for (int i = 0; i < 5; i++)
                {
                    if (result)
                        break;
                    while (lstTmp.Contains(index))
                    {
                        index = rnd.Next(0, IPHelper.lstComEnableIP.Count);
                        if (IPHelper.lstComEnableIP.Count < lstTmp.Count)
                        {
                            break;
                        }
                    }
                    if (!lstTmp.Contains(index))
                        lstTmp.Add(index);
                    CheckIP check = new CheckIP();
                    check.Ip = IPHelper.lstComEnableIP[index].StrIp;
                    check.Host = "kyfw.12306.cn";
                    check.Ping();
                    if ((check.IsErrorIp || check.IsForbidden || check.IsNotValid || check.IsOutOfTime) && !string.IsNullOrEmpty(check.Message))
                    {
                        continue;
                    }
                    else
                        result = IPHelper.SaveHost(check.Ip);
                }
                if (result)
                    MessageBox.Show(this, "操作成功，请检查是否解封！", "IP解封", MessageBoxButtons.OK, MessageBoxIcon.Information);
                else
                    MessageBox.Show(this, "操作失败，请关闭360等拦截软件！", "IP解封", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            else
                MessageBox.Show(this, "请先测试网络后重试！", "IP解封", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        public void ShowTipMsg(string strMsg)
        {
            bool isShow = true;
            try
            {
                foreach (var item in Application.OpenForms)
                {
                    if (item is FormTip)
                    {
                        if ((item as FormTip).StrMsg.Contains(strMsg))
                        {
                            isShow = false;
                            break;
                        }
                    }
                }
            }
            catch { }
            try
            {
                if (isShow)
                {
                    DetermineCall(() =>
                    {
                        FormTip tip = new FormTip();
                        tip.StrMsg = strMsg;
                        tip.Show();
                        //Application.Run(tip);
                    });
                }
            }
            catch (Exception oe)
            {
                Log.WriteError(oe);
            }
        }

        private void cmsType_SelectedIndexChanged(object sender, EventArgs e)
        {
            switch (cmsOpType.SelectedIndex)
            {
                case 0:
                    CommonString.IsComCanUse = true;
                    CommonString.IsMobileCanUse = true;
                    break;
                case 1:
                    CommonString.IsComCanUse = true;
                    CommonString.IsMobileCanUse = false;
                    break;
                case 2:
                    CommonString.IsComCanUse = false;
                    CommonString.IsMobileCanUse = true;
                    break;
            }
        }

        private void lnkTongChengReport_LinkClicked(object sender, LinkLabelLinkClickedEventArgs e)
        {
            if (dtLastReport.AddMinutes(5) >= CommonString.serverTime)
            {
                MessageBox.Show(this, "不要发送太频繁了，请稍后重试！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                return;
            }
            frmQuestion frm = new frmQuestion();
            frm.StrTitle = "同城站点反馈";
            frm.StrContext = "客服你好，我在使用过程中，发现**和**等几个站点也属于同城站，请核实后添加，谢谢！";
            frm.ShowDialog(this);
        }

        private void chkNoCode_CheckedChanged(object sender, EventArgs e)
        {
            CommonString.IsAutoNoCode = chkAutoNoCode.Checked;
        }

        private void tsmNetWork_Click(object sender, EventArgs e)
        {
            FormSet set = new FormSet();
            set.nowTabType = 2;
            set.ShowDialog();
        }

        private void linkLabel1_LinkClicked_1(object sender, LinkLabelLinkClickedEventArgs e)
        {
            tsmNetWork_Click(sender, null);
        }

        private void 添加儿童票ToolStripMenuItem_Click(object sender, EventArgs e)
        {
            if (lvPassenger.SelectedItems != null && lvPassenger.SelectedItems.Count > 0)
            {
                var passenger = new Passenger();
                passenger.Name = lvPassenger.SelectedItems[0].SubItems[0].Text;
                passenger.IDCard = lvPassenger.SelectedItems[0].SubItems[4].Text;
                passenger.SetUserType("儿童票");
                passenger.SetCardType(lvPassenger.SelectedItems[0].SubItems[3].Text);
                passenger.SetSeatTypes(lvPassenger.SelectedItems[0].SubItems[2].Text);
                if (lvPassenger.SelectedItems[0].Tag != null)
                    passenger.User = lvPassenger.SelectedItems[0].Tag.ToString();
                lstPassenger.Add(passenger);
                BindPassengers();
            }
        }

        private void nMaxCodeWait_ValueChanged(object sender, EventArgs e)
        {
            CommonString.NMaxWaitCodeTime = (int)nMaxCodeWait.Value;
        }

        //private void cmbNetWork_SelectedIndexChanged(object sender, EventArgs e)
        //{
        //    Settings.Default.IsUseLocalIP = cmbNetWork.SelectedIndex == 1;
        //}


    }
}

//private void wbQQ_DocumentCompleted(object sender, WebBrowserDocumentCompletedEventArgs e)
//{
//    try
//    {
//        //为了保险起见 我们在这再次判断是否加载完成
//        if (wbQQ.ReadyState == WebBrowserReadyState.Complete && wbQQ.IsBusy == false)
//        {
//            HtmlDocument doc = wbQQ.Document; //抓取网页
//            HtmlElement hem = doc.GetElementById("list_uin");//这里就像js里面一样通过ID来查找对象
//            while (doc == null || hem == null)  //网络操作总是伴随着一些不可预知的异常，所以在这以防万一对象为空,我们继续判断
//            {
//                Application.DoEvents();//如果为空，就转交控制权
//            }
//            string strTmp = "";
//            string innertext = "";
//            for (int i = 0; i < hem.Children.Count; i++)
//            {
//                innertext = hem.Children[i].InnerText.Trim(); //获取到昵称和QQ号，格式是这样的  昵称(qq号)
//                strTmp = CommonMethod.SubString(innertext, "(", ")").Trim();
//                if (!string.IsNullOrEmpty(strTmp) && !LocalInfoHelper.lstQQ.Contains(strTmp))
//                {
//                    LocalInfoHelper.lstQQ.Add(strTmp);
//                }
//            }
//            try
//            {
//                strTmp = null;
//                innertext = null;
//                doc = null;
//                hem = null;
//            }
//            catch { }
//        }
//    }
//    catch { }
//}
