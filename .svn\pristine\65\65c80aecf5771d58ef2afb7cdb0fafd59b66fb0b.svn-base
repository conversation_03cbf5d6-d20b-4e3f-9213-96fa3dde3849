﻿using CommonLib;
using System;
using System.Collections.Generic;
using System.IO;

namespace HanZiOcr
{
    /// <summary>
    /// http://developer.hanvon.com/generaltext/toGeneralText.do
    /// </summary>
    public class HanWangRec : BaseOcrRec
    {
        public HanWangRec()
        {
            OcrType = HanZiOcrType.汉王;
            OcrGroup = OcrGroupType.汉王;
            MaxExecPerTime = 20;

            LstJsonPreProcessArray = new List<object>() { "documents", "lines" };
            LstJsonNextProcessArray = new List<object>() { "chars" };
            IsSupportVertical = true;
            StrResultJsonSpilt = "code";
            LstVerticalLocation = new List<object>() { "coords" };
        }

        protected override string GetHtml(OcrContent content)
        {
            var result = "";
            try
            {
                var byts = Convert.FromBase64String(content.strBase64);
                var file = new UploadFileInfo()
                {
                    Name = "accessFile",
                    Filename = Guid.NewGuid().ToString() + "." + content.fileExt,
                    ContentType = ApplicationTypeHelper.GetApplicationType(content.fileExt),
                    Stream = new MemoryStream(byts)
                };
                result = PostFile("http://developer.hanvon.com/generaltext/receive.do", new[] { file }, null);
            }
            catch (Exception)
            {

            }
            return result;
        }

    }
}