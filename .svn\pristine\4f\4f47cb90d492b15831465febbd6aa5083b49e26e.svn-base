﻿using CommonLib;
using System.Collections.Generic;
using System.Collections.Specialized;

namespace HanZiOcr
{
    /// <summary>
    /// 合合API
    /// https://ai.intsig.com/api/vision/text_recognize_3d1
    /// AppKey:
    /// var DEMO_APP_SECRET = "ai_demo_" + service;
    /// https://static-c.intsig.net/dps/js/compile/ai-api-template-c-12ae68.js
    /// 
    /// API文档
    /// https://ai.intsig.com/doc/api/text_recognize_3d1/v1.0
    /// </summary>
    public class HeHeChinaEnglishRec : BaseOcrRec
    {
        public HeHeChinaEnglishRec()
        {
            OcrType = HanZiOcrType.合合中英;

            MaxExecPerTime = 20;

            LstJsonPreProcessArray = new List<object>() { "result", "lines" };
            IsSupportVertical = true;
            StrResultJsonSpilt = "text";
            LstVerticalLocation = new List<object>() { "position" };
            //IsSupportUrlOcr = true;
        }

        protected override string GetHtml(OcrContent content)
        {
            var url = "https://ocr-api.ccint.com/cci_ai/service/v1/text_recog_ch_en_coordinate";
            var strPost = content.strBase64;
            var strTmp = WebClientSyncExt.GetHtml(url, "", strPost, "https://ai.intsig.com/", ExecTimeOutSeconds
                , new NameValueCollection() {
                    { "App-Key", "ai_demo_text_recog_ch_en_coordinate" },
                    { "App-Secret","ai_demo_text_recog_ch_en_coordinate"}
                });

            return strTmp;
        }

    }
}