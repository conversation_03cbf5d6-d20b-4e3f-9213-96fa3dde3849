<!DOCTYPE HTML>
<html>
    <head>
        <title>Simple Layout</title>
        <style>
        BODY {
            background-color:white;
            color:#000000;
            font-family:Verdana;
            margin: 0;            
        }
        H1 {
            background-color: #036;
            color: #FFF;
            font-family: Tahoma;
            font-size: 26px;
            font-weight: normal;
            margin: 0;
            padding: 10px 0 3px 15px;
        }
        #content {
            padding: 15px;
        }
        #content-page {
            max-width: 1100px;
        }
        #sidebar {
            float:right;
        }
        </style>
    </head>
    <body>        
        <h1>@ViewBag.Title</h1>
        
        <div id="content">
            @RenderBody()
        </div>
    </body>
</html>

<!--template:SimpleLayout.cshtml-->
