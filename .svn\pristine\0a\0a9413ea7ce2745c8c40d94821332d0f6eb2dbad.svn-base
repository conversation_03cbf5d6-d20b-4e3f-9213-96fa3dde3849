﻿using CommonLib;
using log4net;
using Newtonsoft.Json;
using System;
using System.Collections.Specialized;
using System.Diagnostics;
using System.Threading;
using System.Threading.Tasks;
using System.Web;

namespace Code.Process.Common
{
    public class ProcessNew
    {
        #region 构造函数

        static ProcessNew()
        {
            _Log = LogManager.GetLogger("ServicesManager");
        }

        #endregion

        protected static ILog _Log;
        public static bool IsExit = false;

        public static void InitOcrEngine()
        {
            HanZiOcr.ConstHelper.Init();
            MathOcr.ConstHelper.Init();
            TableOcr.ConstHelper.Init();
            DocOcr.ConstHelper.Init();
            TransOcr.ConstHelper.Init();
            Task.Factory.StartNew(() =>
            {
                BaseRecHelper.ReportToServer();
            });
        }

        /// <summary>
        /// 启动进程
        /// </summary>
        public static void StartProcess(bool isSync = true)
        {
            //var strInfo = "";
            //strInfo +="=============\n"+ HanZiOcr.BaiDuAIRec.GetRecImg();
            //strInfo += "=============\n" + HanZiOcr.BaiDuAPIRec.GetRecImg();
            //strInfo += "=============\n" + HanZiOcr.BaiDuRec.GetRecImg();
            //strInfo += "=============\n" + HanZiOcr.BaiDuTuShuRec.GetRecImg();
            //strInfo += "=============\n" + HanZiOcr.BingImageRec.GetRecImg();
            //strInfo += "=============\n" + HanZiOcr.MicroSoftRec.GetRecImg();
            _Log.Info("消息接收主线程开启成功");
            Task.Factory.StartNew(() =>
            {
                BeginReceiveFileStatusMessage();
            });
            if (isSync)
            {
                Task.Factory.StartNew(() =>
                {
                    BeginReceiveMessage();
                });
            }
            else
            {
                BeginReceiveMessage();
            }
        }

        public static void SendOcrResult(OcrContent content)
        {
            try
            {
                if (!string.IsNullOrEmpty(content?.result?.autoText) || content.result?.files?.Count > 0)
                {
                    Task.Factory.StartNew(() =>
                    {
                        content.Server = ConfigHelper.OcrServer;
                        Stopwatch stopwatch = Stopwatch.StartNew();
                        var strPost = JsonConvert.SerializeObject(content);
                        var headers = new NameValueCollection
                        {
                            { "Content-Encoding", "gzip" },
                            { "Content-Type", "application/octet-stream" }
                        };
                        var result = WebClientSyncExt.GetHtml(ServerInfo.HostAccount?.FullUrl + "code.ashx?op=ocrresult", strPost, 5, headers);
                        _Log.Info(
                            $"添加OCR结果-{content.processName}:{result},耗时:{stopwatch.ElapsedMilliseconds.ToString("F2")}ms");
                    });
                }
            }
            catch (Exception oe)
            {
                _Log.Error("添加OCR结果异常！" + oe.Message, oe);
            }
        }

        public static void ProcessFileResult(ProcessStateEntity content)
        {
            try
            {
                Task.Factory.StartNew(() =>
                {
                    Stopwatch stopwatch = Stopwatch.StartNew();
                    var strPost = JsonConvert.SerializeObject(content);
                    var headers = new NameValueCollection
                        {
                            { "Content-Encoding", "gzip" },
                            { "Content-Type", "application/octet-stream" }
                        };
                    var result = WebClientSyncExt.GetHtml(ServerInfo.HostAccount?.FullUrl + "code.ashx?op=filestateresult", strPost, 5, headers);
                    _Log.Info(
                        $"添加文件状态结果:{result},耗时:{stopwatch.ElapsedMilliseconds.ToString("F2")}ms");
                });
            }
            catch (Exception oe)
            {
                _Log.Error("添加OCR结果异常！" + oe.Message, oe);
            }
        }

        /// <summary>
        /// 停止进程
        /// </summary>
        public static void StopProgress()
        {
            try
            {
                IsExit = true;
                _Log.Info("消息接收主线程停止");
            }
            catch (Exception ex)
            {
                _Log.Error(ex.Message);
            }
        }

        /// <summary>
        /// 接收消息
        /// </summary>
        public static void BeginReceiveMessage()
        {
            //System.Threading.Thread.Sleep(30 * 1000);
            //var bytes = System.IO.File.ReadAllBytes(@"D:\助手\Image\0108\Old\11.jpg");//10584306645.jpg");
            //var tmp = DaMaLib.ZhuShouDaMaHelper.GetCodeByBytes(bytes, false, SiteFlag.助手);

            Console.WriteLine($"处理线程ID：{Thread.CurrentThread.ManagedThreadId}\n");
            var timeOutSec = 30;
            while (!IsExit)
            {
                try
                {
                    //int count = (int)RdsCacheHelper.ImageQueue.GetMessageCount();
                    //if (count <= 0)
                    //{
                    //    Thread.Sleep(1);
                    //    continue;
                    //}
                    ////_Log.InfoFormat("当前列表中有{0}个消息待处理", count);
                    ////long count = 1;
                    //Stopwatch stop = Stopwatch.StartNew();
                    //count = Math.Abs(Math.Min(count, MaxThreadCount - CurrentThreadCount));

                    #region 多线程并行处理

                    ////DateTime dtGetMsg = DateTime.Now;

                    //var lstImg = RdsCacheHelper.ImageQueue.PopListMsg(count);
                    ////lstImg.ForEach(p => { p.DtAdd = dtGetMsg; });

                    //if (lstImg != null && lstImg.Count > 0)
                    //{
                    //    Task.Factory.StartNew(() =>
                    //    {
                    //        Parallel.ForEach(lstImg, img =>
                    //        {
                    //            if (CurrentThreadCount < MaxThreadCount)
                    //            {
                    //                try
                    //                {
                    //                    if (img == null)
                    //                        return;

                    //                    img.DtReceived = DateTime.Now;
                    //                    AddThreadCount();

                    //                    //ThreadPool.QueueUserWorkItem(p =>
                    //                    //{
                    //                    CommonProcess.AddToProcess(img);
                    //                    //});
                    //                    //Task.Factory.StartNew(() =>
                    //                    //{
                    //                    //    CommonProcess.AddToProcess(img);
                    //                    //});
                    //                }
                    //                catch (Exception ex)
                    //                {
                    //                    _Log.Error("接收消息消息并分配线程时出错,错误原因如下:", ex);
                    //                }
                    //            }
                    //        });
                    //    });
                    //}
                    ////lstImg.ForEach(p => { p.DisposeStrs(); p = null; });

                    #endregion

                    #region 单线程轮流处理

                    ////有数据有空闲的线程
                    //int i = 0;
                    //while (i < count && CurrentThreadCount < MaxThreadCount)
                    //{
                    //    try
                    //    {
                    //        var img = RdsCacheHelper.ImageQueue.PopImage();

                    //        if (img == null)
                    //            break;

                    //        img.DtReceived = DateTime.Now;
                    //        Task.Factory.StartNew(() =>
                    //        {
                    //            CommonProcess.AddToProcess(img);
                    //        });

                    //        AddThreadCount();
                    //        i++;
                    //    }
                    //    catch (Exception ex)
                    //    {
                    //        string msg = string.Format("接收消息消息并分配线程时出错,错误原因如下:{0}", ex.Message);
                    //        _Log.Error(msg, ex);
                    //    }
                    //}

                    #endregion

                    #region 单线程Block

                    _Log.InfoFormat("{0} 开始接收消息", ServerTime.DateTime.ToString("HH:mm:ss"));
                    try
                    {
                        var dtStart = DateTime.Now.Ticks;
                        CusImageEntity img = null;
                        var strWaitOcr = WebClientSyncExt.GetHtml(ServerInfo.HostAccount?.FullUrl + "code.ashx?op=waitocr&timeout=" + timeOutSec
                            + "&server=" + HttpUtility.UrlEncode(ConfigHelper.OcrServer), "", timeOutSec);
                        if (!string.IsNullOrEmpty(strWaitOcr) && strWaitOcr.Length > 50)
                        {
                            try
                            {
                                img = JsonConvert.DeserializeObject<CusImageEntity>(strWaitOcr);
                            }
                            catch (Exception oe)
                            {
                                _Log.Error("反序列化失败！", oe);
                            }
                        }

                        //    Console.WriteLine(string.Format("Now:{0},DtAdd:{1},Expire:{2}"
                        //, ServerTime.DateTime.ToString("HH:mm:ss fff"), new DateTime(img.DtAdd).ToString("HH:mm:ss fff"), new DateTime(img.DtExpired).ToString("HH:mm:ss fff")));
                        if (img == null || !img.IsValidate)
                        {
                            var maxSleepSec = (int)(timeOutSec * 0.5);
                            if (new TimeSpan(DateTime.Now.Ticks - dtStart).TotalSeconds < maxSleepSec)
                            {
                                _Log.Info("等待时间不足，强制休息！");
                                Thread.Sleep(maxSleepSec);
                            }
                            else
                            {
                                Thread.Sleep(1000);
                            }
                            continue;
                        }

                        ////ThreadPool.QueueUserWorkItem(obj =>
                        ////{
                        ////CommonProcess.AddToProcess(img);
                        ////});
                        ////if (DateTime.Now.Second % 5 == 0)
                        ////    GC.Collect();
                        ////Task.Factory.StartNew(() =>
                        ////{
                        ////    CommonProcess.AddToProcess(img);
                        ////});
                        //Task.Factory.StartNew(() =>
                        //{

                        //AsyncHelpers.RunSync(() => CommonProcess.AddToProcess(img));
                        //});
                        Task.Run(() =>
                        {
                            CommonProcess.AddToProcess(img);
                        });
                        strWaitOcr = WebClientSyncExt.GetHtml(ServerInfo.HostAccount?.FullUrl + "code.ashx?op=reportbeginprocess&server=" + HttpUtility.UrlEncode(ConfigHelper.OcrServer) + "&id=" + img.StrIndex, "", 3);
                        _Log.InfoFormat("反馈Ocr Server Id:{0},Result:{1}", img.StrIndex, strWaitOcr);
                    }
                    catch (Exception ex)
                    {
                        _Log.Error("接收消息消息并分配线程时出错,错误原因如下:", ex);
                        System.Threading.Thread.Sleep(1000);
                    }

                    #endregion

                    //_Log.InfoFormat("接收{0}条消息，总耗时：{1}ms，平均耗时：{2}ms", count, stop.ElapsedMilliseconds.ToString("F0"), (stop.ElapsedMilliseconds / count).ToString("F0"));
                    //stop.Stop();
                }
                catch (Exception ex)
                {
                    _Log.Error("轮询消息接收消息时出错,错误原因如下:", ex);
                }
            }
        }

        /// <summary>
        /// 接收消息
        /// </summary>
        public static void BeginReceiveFileStatusMessage()
        {
            Console.WriteLine(string.Format("处理线程ID：{0}\n", System.Threading.Thread.CurrentThread.ManagedThreadId));
            var timeOutSec = 30;
            while (!IsExit)
            {
                try
                {

                    #region 单线程Block

                    try
                    {
                        var dtStart = DateTime.Now.Ticks;
                        CusFileStatusEntity img = null;
                        var strWaitFileState = WebClientSyncExt.GetHtml(ServerInfo.HostAccount?.FullUrl + "code.ashx?op=waitfilestate&timeout=" + timeOutSec
                            + "&server=" + HttpUtility.UrlEncode(ConfigHelper.OcrServer), "", timeOutSec);
                        if (!string.IsNullOrEmpty(strWaitFileState) && strWaitFileState.Length > 10)
                        {
                            try
                            {
                                img = JsonConvert.DeserializeObject<CusFileStatusEntity>(strWaitFileState);
                            }
                            catch (Exception oe)
                            {
                                _Log.Error("反序列化失败！", oe);
                            }
                        }

                        if (img == null)
                        {
                            var maxSleepSec = (int)(timeOutSec * 0.5);
                            if (new TimeSpan(DateTime.Now.Ticks - dtStart).TotalSeconds < maxSleepSec)
                            {
                                _Log.Info("等待时间不足，强制休息！");
                                Thread.Sleep(maxSleepSec);
                            }
                            else
                            {
                                Thread.Sleep(1000);
                            }
                            continue;
                        }
                        CommonProcess.AddToFileStatusProcess(img);
                    }
                    catch (Exception ex)
                    {
                        _Log.Error("接收消息消息并分配线程时出错,错误原因如下:", ex);
                        System.Threading.Thread.Sleep(1000);
                    }

                    #endregion
                }
                catch (Exception ex)
                {
                    _Log.Error("轮询消息接收消息时出错,错误原因如下:", ex);
                }
            }
        }

    }
}
