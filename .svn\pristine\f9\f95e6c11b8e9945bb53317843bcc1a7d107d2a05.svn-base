﻿using CommonLib;
using System;
using System.Collections.Generic;
using System.IO;

namespace HanZiOcr
{
    /// <summary>
    /// https://translate.yandex.com/ocr?
    /// </summary>
    public class YandexRec : BaseOcrRec
    {
        public YandexRec()
        {
            OcrType = HanZiOcrType.Yandex;

            MaxExecPerTime = 15;

            LstJsonPreProcessArray = new List<object>() { "data", "blocks" };
            LstJsonNextProcessArray = new List<object>() { "boxes", "words" };
            IsSupportVertical = true;
            //StrResultJsonSpilt = "text";
            IsDesrializeVerticalByLocation = true;
            DicDeserializeVerticalJson = new Dictionary<string, string> { { "left", "x" }, { "top", "y" }, { "width", "w" }, { "height", "h" }, { "words", "text" } };
        }

        protected override void SetProcessArray(string html)
        {
            var isEnglish = !string.IsNullOrEmpty(html) && Equals(CommonLib.OcrProcessor.OcrUtils.GetLang(html), "en");
            StrContactCell = isEnglish ? " " : "";
        }

        protected override string GetHtml(OcrContent content)
        {
            var byt = Convert.FromBase64String(content.strBase64);
            var result = PostFileResult(byt);
            return result;
        }

        private string PostFileResult(byte[] content)
        {
            var result = "";
            try
            {
                var url = "https://translate.yandex.net/ocr/v1.1/recognize?srv=tr-image&sid=&lang=*";
                var file = new UploadFileInfo()
                {
                    Name = "file",
                    Filename = "blob",
                    ContentType = "image/jpeg",
                    Stream = new MemoryStream(content)
                };
                result = PostFile(url, new[] { file }, null);
            }
            catch (Exception)
            {

            }
            return result;
        }

    }
}