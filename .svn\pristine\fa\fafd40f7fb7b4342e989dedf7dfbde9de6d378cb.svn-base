﻿using CommonLib;
using System;
using System.Collections.Generic;
using System.Collections.Specialized;
using System.IO;
using System.Web;

namespace HanZiOcr
{
    /// <summary>
    /// 薪火OCR网页版
    /// https://www.xinhuokj.com/ocr/table
    /// https://ocr.xinhuokj.com/
    /// </summary>
    public class XinHuoRec : BaseOcrRec
    {
        public XinHuoRec()
        {
            OcrType = HanZiOcrType.薪火;
            MaxExecPerTime = 10;
            //IsSupportUrlOcr = true;

            LstJsonPreProcessArray = new List<object>() { "data", "words_result" };

            IsSupportVertical = true;
            LstVerticalLocation = new List<object>() { "locale" };
            StrResultJsonSpilt = "words";
        }


        protected override string GetHtml(OcrContent content)
        {
            content.strBase64 = "data:image/jpeg;base64," + content.strBase64;
            var strCookie = "PHPSESSID=" + Guid.NewGuid().ToString().Replace("-", "");
            string strPost = "image1Base64=" + HttpUtility.UrlEncode(content.strBase64) + "&type=0";
            var result = WebClientSyncExt.GetHtml("https://www.xinhuokj.com/ocr/ocrCommonPost", strCookie, strPost, "https://www.xinhuokj.com/ocr.html", ExecTimeOutSeconds);
            return result;
        }

        /*
         {
          "left_top": [
            8,
            6
          ],
          "right_bottom": [
            166,
            20
          ]
        }
         */
        protected override LocationInfo GetLocationByStr(string locationInfoStr)
        {
            locationInfoStr = locationInfoStr?
                      .Replace("left_top", "").Replace("right_bottom", "")
                      .Replace("[", "").Replace("]", "")
                      .Replace("{", "").Replace("}", "")
                      .Replace("\"", "").Replace(":", "")
                      .Replace("\r", "").Replace("\t", "").Replace("\n", "")
                      .Replace(" ", "").Trim();
            LocationInfo location = base.GetLocationInfoByStr(locationInfoStr, false, true);

            return location;
        }
    }
}