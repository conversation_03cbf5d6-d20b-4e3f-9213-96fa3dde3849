.register-section {
  background-image: none !important;
}
.register-section .my-button {
  width: 160px !important;
  height: 48px !important;
  line-height: 48px !important;
}

.page-footer .line {
  margin-top: 0 !important;
}
.home-container .page-footer .foot-cont {
  padding-top: 0 !important;
}
.home-container .page-footer .item-foot {
  display: none;
}
.h2-tip-xj {
  width: 10px;
  transition: all 0.3s ease-out;
}
/* v4-home-banner */
/* .top-banner-section */
.top-banner-section .pro-sms div {
  color: #fff;
  /* height: 34px; */
  line-height: 34px;
  display: flex;
  align-items: center;
  position: relative;
  background: linear-gradient(to right, #28bfe0 80%, transparent);
  width: 36%;
}
.pro-sms .fav span {
  position: relative;
  z-index: 1;
}

@media (max-width: 1397px) {
  .top-banner-section .pro-sms div {
    width: 45%;
  }
}
@media (max-width: 1189px) {
  .top-banner-section .pro-sms div {
    width: 50%;
  }
}
@media (max-width: 992px) {
  .top-banner-section .pro-sms div {
    width: 80%;
  }
}

.top-banner-section .pro-sms div a {
  position: relative;
}
.top-banner-section .pro-sms div .line-1 {
  display: inline-block;
  width: 1px;
  height: 16px;
  background: #fff;
  transform: translateY(4px);
  margin: 0 5px;
}
.top-banner-section .pro-sms .icon-jiantou_1 {
  transition: all 0.3s ease;
  transform: rotate(0) scale(0.7);
  display: inline-block;
  font-weight: 300;
  font-size: 12px !important;
}
.top-banner-section .pro-sms div:hover .icon-jiantou_1 {
  transform: rotate(45deg) scale(0.75);
}

@media (min-width: 1400px) {
  .home-container .container {
    max-width: 1280px;
  }
}
.color-grey {
  color: #41464f;
}
.home-section {
  position: relative;
  color: #1a1c1f;
}
.home-section img {
  -webkit-user-drag: none;
  user-select: none;
}
.home-section .container > h1 {
  width: fit-content;
  margin: 0 auto;
  letter-spacing: 2px;
}
@media screen and (max-width: 426px) {
  .send_free_btn {
    margin-top: 12px;
  }
}
@media screen and (max-width: 576px) {
  .home-section .container > h1 {
    font-size: 20px;
  }
}

.home-section .container > h1 span {
  color: #1764ff;
}

.top-banner-section {
  background-color: #f1f4fd;
  position: relative;
}

.top-banner-section .top-banner-bg {
  position: absolute;
  top: 0;
  right: 0;
  pointer-events: none;
  user-select: none;
  object-fit: cover;
  width: 100%;
  height: 100%;
}
.top-img-style1 {
  position: absolute;
  top: 27% !important;
  right: 0 !important;
  pointer-events: none !important;
  user-select: none !important;
  object-fit: cover !important;
  width: 40% !important;
  height: 55% !important;
}
.mb-banner-img {
  display: none;
}
@media (max-width: 768px) {
  .pc-banner-img {
    display: none;
  }
  .mb-banner-img {
    display: block;
  }
}
/* .top-banner-section .top-banner-bg {
  position: absolute;
  bottom: 0%;
  right: -12%;
  width: 600px;
} */
.top-banner-section .lanxi-sw:hover {
  background: #dee8fa !important;
}

.top-banner-section .banner {
  height: 540px;
  position: relative;
  width: 100%;
}

.top-banner-section .banner .banner-item {
  display: flex;
  justify-content: center;
  flex-direction: column;
  width: 100%;
  height: 100%;
}

.top-banner-section .banner .banner-item h3.title {
  font-size: 42px;
}

.top-banner-section .banner .banner-item .text {
  color: #4d5565;
  overflow: hidden;
  white-space: normal;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  width: 80%;
}

.top-banner-section .banner .banner-item .button {
  user-select: none;
  text-decoration: none;
  width: 158px;
  text-align: center;
  background-color: #1764ff;
  border-radius: 4px;
  color: #fff;
  cursor: pointer;
  height: 50px;
  line-height: 50px;
}

.top-banner-section .banner .banner-dots {
  position: absolute;
  left: 0;
  bottom: 24px;
  display: flex;
}

.top-banner-section .banner .banner-dots .dot-item {
  width: fit-content;
  margin-right: 8px;
  display: flex;
  flex-direction: column;
  cursor: pointer;
  font-size: 14px;
}

.top-banner-section .banner .banner-dots .dot-item .dot-text:hover,
.top-banner-section .banner .banner-dots .dot-item.active .dot-text {
  color: #1a1c1f;
}
.top-banner-section .banner .banner-dots .dot-item.active .dot-text {
  font-weight: bold;
}

.top-banner-section .banner .banner-dots .dot-item .dot-text {
  color: #41464f;
  transition: color 0.3s ease-out;
  padding-right: 4px;
}

.top-banner-section .banner .banner-dots .dot-item .dot-line {
  margin-top: 8px;
  width: 100%;
  height: 1px;
  background-color: #c5c5c5;
  position: relative;
}

.top-banner-section .banner .banner-dots .dot-item .dot-line::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  height: 2px;
  width: 0;
  will-change: width;
}

.top-banner-section .banner .banner-dots .dot-item.active .dot-line::after {
  width: 100%;
  background-color: #1a1c1f;
  animation: step 6s linear both;
}

@media (max-width: 768px) {
  .top-banner-section .banner .banner-dots .dot-item .dot-text {
    opacity: 0;
  }
}
@keyframes step {
  0% {
    width: 0%;
  }

  100% {
    width: 100%;
  }
}

/* v4-home-section-2 */
.section-2 {
  width: 100%;
  font-size: 12px;
  padding: 16px 0;
  z-index: 1;
}
.section-2 .mod-item {
  display: flex;
  align-items: center;
  position: relative;
  box-sizing: border-box;
}

.section-2 .line {
  width: 1px;
  height: 100%;
  background-color: #ddd;
  margin: 0 62px;
}
.section-2 .mod-item img {
  width: 42px;
  height: 42px;
  margin-right: 16px;
}
.section-2 .mod-item .mod-title {
  font-size: 16px;
  color: #1a1c1f;
}
.section-2 .mod-item:hover .mod-title {
  color: #1764ff;
}
.section-2 .mod-item .mod-text {
  font-size: 14px;
  color: #606875;
}
.section-2 .free-bg {
  z-index: -1;
  position: absolute;
  top: 0;
  right: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(to right, #1764ff, #0081ff);
  transform: skew(-7deg, 0) translate(50%, -4px);
  pointer-events: none;
}
.section-2 .free-title {
  position: relative;
  margin-right: 32px;
  color: #fff;
  font-size: 16px;
  line-height: 1.2;
}
.section-2 .free-title img {
  height: 100%;
  object-fit: contain;
  user-select: none;
  pointer-events: none;
  -webkit-user-drag: none;
}
.section-2 .right-free {
  position: absolute;
  right: -104px;
  top: -20px;
  padding: 24px 0;
  border-radius: 4px;
  background-repeat: no-repeat;
  background-position: 100% 100%;
  background-image: url(/libraries_v4/view/home/<USER>/free-bg.jpg);
  background-size: cover;
  width: 58%;
}
.section-2 .free-svg {
  width: 72px;
}
.section-2 .input-wrap {
  border-left: 1px solid #ddd;
  color: #fff;
}
.section-2 input.free-input {
  width: 100%;
  height: 40px;
  border-color: #6d9aed;
  background-color: transparent;
  color: #fff;
  font-size: 14px;
  border: 1px solid #6d9aed;
  border-radius: 2px;
  padding: 0 12px;
}
.section-2 input.test_code {
  width: 0px;
  padding: 0;
  opacity: 0;
  vertical-align: middle;
  transition: all 0.3s ease-out;
}
.section-2 input.test_code.active {
  width: 80px;
  padding: 0 12px;
  opacity: 1;
}
.section-2 .code-show > img {
  vertical-align: middle;
  height: 40px;
  border: solid 1px #ddd;
  opacity: 0;
  width: 0;
  transition: all 0.3s ease-out;
}
.section-2 .code-show > img.active {
  opacity: 1;
  width: 80px;
}
.section-2 input.free-input:hover,
.section-2 input.free-input:focus {
  box-shadow: none;
  border: 1px solid #6d9aed;
  outline: none;
}
.section-2 input.free-input::placeholder {
  color: #cdd9ff;
}
.section-2 .free-btn {
  background-color: #fff;
  color: #1764ff;
  border: none;
  height: 40px;
  line-height: 40px;
  padding: 0 24px;
  font-size: 14px;
  border-radius: 2px;
  cursor: pointer;
  font-weight: bold;
}
.section-2 .free-btn:hover {
  background-color: #e5f4ff;
}
/* 手机端适配 */
@media screen and (max-width: 768px) {
  .home-section .container > h1 {
    font-size: 24px;
  }
  .top-banner-section .banner {
    height: 390px;
  }
  .top-banner-section .banner .banner-item h3.title {
    font-size: 28px;
    margin: 0 auto;
    transform: translateY(-70px);
  }
  .top-banner-section .banner .banner-item .text {
    margin: 0 auto;
    font-size: 14px;
    transform: translateY(-60px);
  }
  .top-banner-section .banner .banner-dots {
    width: 100%;
    justify-content: center;
  }
  .top-banner-section .banner .banner-item {
    text-align: center;
  }
  .nineYearImg {
    margin: 0 auto;
  }
  .haoli {
    margin-top: 5rem !important;
  }
  .top-banner-section .konw-detail,
  .konw-detail,
  .nine-txt1 {
    display: none !important;
  }
  .section-2 .free-bg {
    display: none;
  }
  .section-2 .free-wrap {
    background: linear-gradient(to right, #1764ff, #0081ff);
  }
  .section-2 .free-title {
    width: 14%;
  }
  .section-2 .free-title span {
    display: flex;
    justify-content: center;
  }
  .section-2 .input-wrap {
    border-left: none;
    padding-left: 0 !important;
    /* max-width: 100px !important; */
  }
  .section-2 input.free-input {
    width: 182px;
  }
  .section-2 .free-wrap {
    justify-content: space-between !important;
  }
  .section-2 .right-free {
    padding: 10px 6px;
  }
  .section-2 .free-wrap {
    flex-wrap: wrap;
  }
  .section-2 input.test_code {
    width: 116px !important;
  }
  .section-2 .code-show > img {
    opacity: 1;
    width: 95px;
  }
  .huoban {
    font-size: 24px !important;
  }
  .he-zuo {
    font-size: 14px !important;
  }
  .product-faq {
    padding: 40px 0 0px;
  }
  .section-5 .card-list {
    margin-top: 5rem !important;
  }
  .section-5 .card-list li {
    margin-bottom: 3rem !important;
  }

  .section-6 {
    padding: 0 !important;
  }
  .section-6 .bg-yuan {
    display: none;
  }
  .section-7 .title {
    font-size: 24px !important;
  }

  .section-8 .shuju-anqun {
    font-size: 24px !important;
  }
}
@media screen and (max-width: 576px) {
  .section-2 .input-wrap {
    flex: 0 0 100%;
    max-width: 100%;
  }
  .section-2 .input-wrap input {
    width: 94%;
    margin: auto;
    display: block;
  }
  .section-2 .free-info {
    width: 94%;
    margin: auto;
  }
  .section-2 .free-btn {
    width: 120px;
    text-align: center;
  }
}
@media screen and (max-width: 991px) {
  .section-2 .right-free {
    position: static;
    top: 0;
    background-color: #1764ff;
    background-image: url("");
  }
  .section-2 {
    padding: 0;
  }
  .section-2 .free-bg {
    z-index: -1;
    position: absolute;
    top: auto;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 50%;
    background: linear-gradient(to right, #1764ff, #0081ff);
    transform: skew(0, 0) translate(0);
    pointer-events: none;
  }
  .section-2 .mod-list {
    padding: 0 !important;
  }
  .section-2 .mod-list .mod-item {
    padding: 4px 0 !important;
    margin: 0 !important;
  }
  .section-2 .mod-list .mod-item .mod-title {
    font-size: 14px;
  }
  .section-2 .mod-list .mod-item .mod-text {
    font-size: 12px;
  }
  .section-2 .mod-list .mod-item img {
    margin-right: 8px;
    width: 32px;
    height: 32px;
  }
  .section-2 .free-wrap {
    padding: 4px 0 !important;
  }
  .section-2 .free-title {
    margin-right: 12px !important;
    font-size: 14px !important;
  }
  .section-2 .input-wrap {
    padding-left: 0px !important;
  }
  .section-2 .input-wrap input {
    padding: 0 0 0 2px !important;
  }
  .section-2 .free-btn {
    /* height: 28px;
    line-height: 26px; */
    padding: 0 6px;
  }
}
@media screen and (max-width: 992px) {
  .section-7 .souce-right {
    padding-top: 36px;
  }
  .section-2 .mod-list .mod-item .mod-text,
  .section-2 .free-title,
  .card-1-img {
    display: none;
  }
  .section-2 .input-wrap {
    border-left: none;
  }
  .section-2 .free-wrap {
    background: none !important;
  }
  .section-2 input.test_code {
    margin-right: 12px;
  }
  .section-2 input.test_code,
  .code-show > img {
    opacity: 1;
    width: 80px;
  }
  .section-2 .free-exper {
    margin-right: 1rem !important;
  }
  .section-7 .blue-bg {
    height: 53% !important;
    transform: translate(0%, 0%) skew(0deg, 0) !important;
  }
}
@media (max-width: 1000px) {
  .top-banner-bg {
    display: none;
  }
}
@media screen and (max-width: 1198px) {
  .section-2 .mod-list {
    justify-content: space-around !important;
  }
  .section-2 .line {
    display: none;
  }
}
@media screen and (max-width: 1390px) {
  .section-2 .line {
    margin: 0 20px;
  }
}
@media screen and (max-width: 1400px) {
  .section-2 .right-free .free-wrap {
    justify-content: center;
  }
  .section-2 .right-free {
    position: static;
    width: 100%;
  }
  .section-2 .mod-list {
    width: 100% !important;
    justify-content: space-around !important;
  }
}
@media screen and (min-width: 992px) and (max-width: 1199px) {
  .section-2 .free-title {
    width: 10%;
    margin: 0 !important;
  }
  .section-2 .free-title span {
    display: flex;
  }
  .section-2 .input-wrap {
    border-left: none;
    padding-left: 0 !important;
  }
}

/* v4-home-section-3 */
.section-3 {
  padding: 56px 0 80px;
  background: #f5f7fc;
}

.section-3 .card-wrap {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
}
.section-3 .card-wrap .left-card {
  height: 100%;
}
.section-3 .card-wrap .right-card {
  padding: 0;
  position: relative;
  display: flex;
  flex-wrap: wrap;
  flex-direction: row;
  min-width: 345px;
}
.section-3 .card-wrap .card {
  border: 2px solid #fff;
  box-shadow: 8px 8px 20px 0 rgba(55, 99, 170, 0.1);
  background: linear-gradient(to bottom, #f3f5f9, #fff);
}
.section-3 .card-pad-width {
  width: 85%;
}

.section-3 .card-wrap .card.card-1 .mini-content .tips:hover,
.section-3 .card-wrap .card.card-2 .mini-content .tips:hover {
  color: #1748ff;
}

.section-3 .card-wrap .card.card-1 .mini-content a > b,
.section-3 .card-wrap .card.card-1 .mini-content > p,
.section-3 .card-wrap .card.card-1 .mini-content .btn-group,
.section-3 .card-wrap .card.card-1 .mini-content .tips,
.section-3 .card-wrap .card.card-2 .mini-content a > b,
.section-3 .card-wrap .card.card-2 .mini-content > p,
.section-3 .card-wrap .card.card-2 .mini-content .btn-group,
.section-3 .card-wrap .card.card-2 .mini-content .tips,
.section-3 .card-wrap .right-card .card-mini .card-title,
.section-3 .card-wrap .card .btn-group,
.section-3 .card-wrap .right-card .card-mini .detail {
  padding: 0px 20px 0px 20px;
}

.section-3 .card-wrap .card .card-text {
  padding: 0px 20px 0px 20px;
}
.section-3 .card-wrap .card.card-1 .mini-content a,
.section-3 .card-wrap .card.card-2 .mini-content a {
  color: initial;
}
.section-3 .card-wrap .card.card-1 .mini-content .card-title,
.section-3 .card-wrap .card.card-2 .mini-content .card-title,
.section-3 .card-wrap .right-card .card-mini .card-title {
  padding-bottom: 10px;
  border-bottom: 1px solid #e1e6ed;
}
.section-3 .card-wrap .right-card .card-mini .card-title {
  padding-top: 10px;
}

.section-3 .card-wrap .card.card-1 .mini-content,
.section-3 .card-wrap .card.card-2 .mini-content {
  padding: 15px 0px 24px 0px;
  position: relative;
}
.section-3 .card-1-img {
  position: absolute;
  top: 8rem;
  right: -8rem;
  width: 80%;
}

.section-3 .card-wrap .card.card-1 {
  background: linear-gradient(to bottom, #f3f5f9, #ffffff);
  border: 2px solid #fff;
  margin-bottom: 16px;
  overflow: hidden;
}

.section-3 .h2-tip-ico {
  width: 32px;
}

.section-3 .card-wrap .card.card-1 .card-text {
  color: #41464f;
}

.section-3 .card-wrap .card.card-1,
.section-3 .card-wrap .card.card-2 {
  position: relative;
  top: 0;
  left: 0;
  transition: top 0.3s ease-out;
}
.section-3 .card-wrap .card.card-1:hover,
.section-3 .card-wrap .card.card-2:hover {
  top: -10px;
}

.section-3 .card-wrap .card .card-title {
  font-size: 18px;
  font-weight: bold;
}
.section-3 .card-wrap .card .card-text {
  line-height: 1.3;
  font-size: 14px;
  color: #41464f;
}
.section-3 .card-wrap .card .btn-group {
  display: flex;
  flex-wrap: wrap;
}
.section-3 .items-pro .card-pad {
  margin-bottom: 2rem;
}
.section-3 .btn-group-pos {
  position: absolute;
  bottom: 0.3rem;
}

.section-3 .card-wrap .card .btn-group .btn-item {
  min-width: 72px;
  height: 28px;
  line-height: 28px;
  text-align: center;
  background-color: #eef0f4;
  cursor: text;
  border-radius: 2px;
  padding: 0 12px;
  margin-right: 8px;
  margin-bottom: 8px;
  font-size: 12px;
  user-select: text;
  color: #414855;
  will-change: background-color;
  transition: background-color 0.15s ease-out;
}
.section-3 .card-wrap .card .btn-group .btn-item:last-child {
  margin-right: 0;
}

.section-3 .card-wrap .card.card-1:hover .card-title,
.section-3 .card-wrap .card.card-2:hover .card-title,
.section-3 .card-wrap .right-card .card-mini .mini-content:hover .card-title {
  color: #1764ff;
}

.section-3 .card-wrap .card.card-1 .detail:hover .h2-tip-xj,
.section-3 .card-wrap .card.card-2 .detail:hover .h2-tip-xj,
.section-3 .card-wrap .right-card .card-mini .detail:hover .h2-tip-xj {
  transform: rotate(45deg);
}
.section-3 .card-wrap .card .tips {
  font-size: 14px;
}
.section-3 .card-wrap .card .tips > b {
  transform: translateY(-1px);
  font-size: 14px;
  font-weight: bold;
}
.section-3 .card-wrap .card.card-1 .card-btn {
  color: #000;
  background-color: #f5f9ff;
}
.section-3 .card-wrap .card.card-1 .detail,
.section-3 .card-wrap .card.card-2 .detail {
  color: #fff;
  position: absolute;
  left: 20px;
  bottom: 18px;
  opacity: 0;
  will-change: opacity;
  font-size: 12px;
  cursor: pointer;
}
.section-3 .card-wrap .card.card-1 .detail,
.section-3 .card-wrap .card.card-2 .detail,
.section-3 .card-wrap .right-card .card-mini .detail {
  color: #fff !important;
  display: block;
  width: 180px;
  height: 36px;
  line-height: 36px;
  background-color: #1764ff;
  text-align: center;
  border-radius: 2px;
  font-size: 14px !important;
}
.section-3 .card-wrap .right-card .card-mini .detail {
  margin-left: 20px;
}

.section-3 .card-wrap .card.card-1:hover .detail,
.section-3 .card-wrap .card.card-2:hover .detail {
  opacity: 1;
}
.section-3 .card-wrap .card.card-1:hover .tips,
.section-3 .card-wrap .card.card-2:hover .tips {
  cursor: pointer;
}
.section-3 .card-wrap .right-card .card-mini {
  width: 32%;
  position: relative;
  top: 0;
  transition: all 0.2s ease-out;
}
.section-3 .card-wrap .right-card .card-mini:hover {
  top: -10px;
}
.section-3 .card-wrap .right-card .card-mini .mini-line {
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  border: 1px solid transparent;
  position: relative;
}
.section-3 .card-wrap .right-card .card-mini1:hover .detail {
  bottom: 0px !important;
}
.section-3 .card-wrap .right-card .card-mini:hover .mini-content .detail {
  opacity: 1;
  bottom: 12px;
  cursor: pointer;
}
.section-3 .card-wrap .right-card .card-mini .mini-content {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
  padding: 24px 24px 36px;
  position: relative;
  z-index: 1;
  top: 0;
  left: 0;
  transition: top 0.3s ease-out, left 0.3s ease-out;
  background-color: transparent;
}

.section-3 .card-wrap .right-card .card-mini .tip-btn {
  display: inline-block;
  border: 1px solid #1748ff;
  background-color: transparent;
  border-radius: 2px;
  color: #1764ff;
  font-size: 12px;
  text-align: center;
  height: 20px;
  line-height: 18px;
  padding: 0 8px;
  margin-left: 8px;
  box-sizing: border-box;
  font-weight: bold;
  transform: translateY(-2px);
}

.section-3 .top-card-mini .card-mini {
  border: 2px solid #fff;
  width: 49% !important;
  background: linear-gradient(to bottom, #f3f5f9, #fff);
}

.section-3 .card-wrap .right-card .card-mini:nth-child(3n + 1) {
  padding-left: 24px;
}
.section-3 .card-wrap .right-card .card-mini:nth-child(3n + 3) {
  padding-right: 24px;
}
.section-3 .card-wrap .right-card .card-mini:nth-child(3n + 1) .mini-content {
  padding-left: 0;
}
.section-3 .card-wrap .right-card .card-mini:nth-child(1),
.section-3 .card-wrap .right-card .card-mini:nth-child(2),
.section-3 .card-wrap .right-card .card-mini:nth-child(3) {
  padding-top: 24px;
}
.section-3 .card-wrap .right-card .card-mini:nth-child(1) .mini-content,
.section-3 .card-wrap .right-card .card-mini:nth-child(2) .mini-content,
.section-3 .card-wrap .right-card .card-mini:nth-child(3) .mini-content {
  padding-top: 4px;
}
.section-3 .card-wrap .right-card .card-mini:nth-child(7),
.section-3 .card-wrap .right-card .card-mini:nth-child(8),
.section-3 .card-wrap .right-card .card-mini:nth-child(9) {
  padding-bottom: 24px;
}
.section-3 .card-wrap .right-card .card-mini:nth-child(7) .mini-content,
.section-3 .card-wrap .right-card .card-mini:nth-child(8) .mini-content,
.section-3 .card-wrap .right-card .card-mini:nth-child(9) .mini-content {
  padding-bottom: 4px;
}
.section-3 .card-wrap .right-card .card-mini:nth-child(1) .mini-line,
.section-3 .card-wrap .right-card .card-mini:nth-child(2) .mini-line,
.section-3 .card-wrap .right-card .card-mini:nth-child(3) .mini-line {
  border-top: transparent;
}
.section-3 .card-wrap .right-card .card-mini:nth-child(7) .mini-line,
.section-3 .card-wrap .right-card .card-mini:nth-child(8) .mini-line,
.section-3 .card-wrap .right-card .card-mini:nth-child(9) .mini-line {
  border-bottom: transparent;
}
.section-3 .card-wrap .right-card .card-mini:nth-child(7) .detail,
.section-3 .card-wrap .right-card .card-mini:nth-child(8) .detail,
.section-3 .card-wrap .right-card .card-mini:nth-child(9) .detail {
  bottom: -24px;
}
.section-3 .card-wrap .right-card .card-mini:nth-child(7):hover .detail,
.section-3 .card-wrap .right-card .card-mini:nth-child(8):hover .detail,
.section-3 .card-wrap .right-card .card-mini:nth-child(9):hover .detail {
  bottom: -12px;
}
.section-3 .card-wrap .right-card .card-mini .btn-item {
  min-width: 60px;
  padding: 0 12px;
  font-size: 12px;
  height: 28px;
  line-height: 28px;
}
.section-3 .h2-tip {
  text-align: center;
  font-size: 16px;
  color: #4d5565;
}
@media screen and (max-width: 576px) {
  .section-3 .card-wrap .card.card-1 .mini-content .card-title,
  .section-3 .card-wrap .card.card-2 .mini-content .card-title {
    /* padding-top: 5px;
    padding-bottom: 6px; */
    padding: 5px 0 6px 0;
  }
  .section-3 .top-card-mini .card-mini:nth-child(2) {
    margin-top: 1rem;
  }
  .section-3 .top-card-mini {
    flex-wrap: wrap;
  }
  .section-3 .card-wrap .right-card .card-mini,
  .section-3 .top-card-mini .card-mini {
    width: 100% !important;
  }

  .section-3 .card-wrap .left-card {
    padding-right: 0 !important;
  }
  .section-3 .card-wrap .card.card-1 .mini-content,
  .section-3 .card-wrap .card.card-2 .mini-content {
    margin-top: 0 !important;
    padding: 20px 16px;
  }
  .section-3 .card-wrap .right-card {
    justify-content: space-between;
    box-shadow: none;
    border-radius: 0;
    background-color: transparent;
    border: none;
  }

  .section-3 .card-wrap .right-card .card-mini {
    width: 50%;
    padding: 0 !important;
  }
  .section-3 .card-wrap .right-card .card-mini .mini-line {
    border: none !important;
  }
  .section-3 .card-wrap .right-card .card-mini .mini-line .mini-content {
    padding: 0 8px 8px 18px !important;
  }
  .section-3 .card-wrap .right-card .card-mini .card-title {
    padding-bottom: 6px !important;
  }
  .section-3 .card-wrap .right-card .card-mini:hover {
    background: transparent;
  }
  .section-3 .card-mini:hover::after {
    display: none !important;
  }
  .section-3 .card-wrap .card-mini:hover .mini-content {
    top: 0 !important;
  }
  .section-3 .card-wrap .card.card-2:hover {
    background: linear-gradient(to bottom, #f3f5f9, #fff);
  }
  .section-3 .card-wrap .card-mini .mini-content .tips {
    opacity: 1 !important;
    margin-top: 16px !important;
  }
  .section-3 .card-wrap .right-card .card-mini:hover .card-title {
    color: #1764ff;
  }
}
@media screen and (max-width: 768px) {
  .section-3 {
    background: linear-gradient(0deg, #f9fbfd, #f9fbfd), #fff;
    padding: 40px 0 16px;
  }
  .section-3 .h2-tip {
    font-size: 14px;
  }

  .section-3 .card-wrap .right-card .card-mini,
  .section-3 .top-card-mini .card-mini {
    width: 49%;
  }
}
@media screen and (max-width: 992px) {
  .section-3 .btn-group-pos {
    bottom: -1.5rem !important;
  }
  .section-3 .card-wrap .card.card-1::after {
    background-image: none;
  }
}
@media screen and (max-width: 1200px) {
  .section-3 .card-wrap .card.card-1 .mini-content > b,
  .section-3 .card-wrap .card.card-2 .mini-content > b {
    padding: 6px 0 !important;
  }
  .section-3 .btn-group-pos {
    bottom: -0.5rem;
  }
  .section-3 .card-pad-width {
    width: 100%;
  }
  .section-3 .card-wrap .right-card .card-mini .detail {
    margin-left: 0px !important;
  }
  .section-3 .mini-content {
    padding-top: 0px !important;
  }
  .section-3 .card-wrap .right-card .card-mini .mini-line {
    padding: 5px 0px 24px 0px;
  }
  .section-3 .card-wrap .card.card-1 .mini-content > b,
  .section-3 .card-wrap .card.card-1 .mini-content > p,
  .section-3 .card-wrap .card.card-1 .mini-content .btn-group,
  .section-3 .card-wrap .card.card-1 .mini-content .tips,
  .section-3 .card-wrap .card.card-2 .mini-content > b,
  .section-3 .card-wrap .card.card-2 .mini-content > p,
  .section-3 .card-wrap .card.card-2 .mini-content .btn-group,
  .section-3 .card-wrap .card.card-2 .mini-content .tips,
  .section-3 .card-wrap .right-card .card-mini .card-title,
  .section-3 .card-wrap .card .card-text,
  .section-3 .card-wrap .card .btn-group,
  .section-3 .card-wrap .right-card .card-mini .detail {
    padding: 0;
  }
}
@media screen and (min-width: 992px) and (max-width: 1199px) {
  .section-3 .card-wrap .card.card-1 .mini-content,
  .section-3 .card-wrap .card.card-2 .mini-content {
    padding: 20px 16px;
  }
  .section-3 .mini-content {
    padding: 14px !important;
  }
  .section-3 .card-wrap .right-card .card-mini:hover .mini-content .detail {
    left: 14px;
  }
}

@media screen and (max-width: 991px) and (min-width: 576px) {
  .section-3 .card-wrap .left-card {
    padding-right: 0 !important;
  }
  .section-3 .mini-content {
    padding: 6px !important;
  }

  .section-3 .card-wrap .right-card .card-mini:nth-child(3n + 1) {
    padding-left: 16px;
  }
  .section-3 .card-wrap .right-card .card-mini:nth-child(3n + 3) {
    padding-right: 16px;
  }
  .section-3 .card-wrap .right-card .card-mini:nth-child(1),
  .section-3 .card-wrap .right-card .card-mini:nth-child(2),
  .section-3 .card-wrap .right-card .card-mini:nth-child(3) {
    padding-top: 16px;
  }
  .section-3 .card-wrap .right-card .card-mini:nth-child(7),
  .section-3 .card-wrap .right-card .card-mini:nth-child(8),
  .section-3 .card-wrap .right-card .card-mini:nth-child(9) {
    padding-bottom: 16px;
  }
  .section-3 .card-wrap .right-card .card-mini:hover::after {
    display: none;
  }
  .section-3 .card-wrap .right-card .card-mini:hover {
    background: transparent;
  }
  .section-3 .card-wrap .card-mini:hover::after {
    display: none;
  }
  .section-3 .card-wrap .card-mini .mini-content .tips {
    opacity: 1 !important;
    margin-top: 16px !important;
  }
  .section-3 .card-wrap .card-mini:hover .mini-content {
    top: 0 !important;
  }
  .section-3 .card-wrap .card.card-2:hover {
    background: linear-gradient(to bottom, #f3f5f9, #fff);
  }
  .section-3 .card-wrap .right-card .card-mini:hover .card-title {
    color: #1764ff;
  }
}

/* v4-home-section-4 */
#product-faq {
  display: none;
}
#product-faq .header-content .icon-han {
  width: 34px;
  height: 34px;
  margin-right: 6px;
}
.section-4 {
  padding: 56px 0 0;
}
.section-4 .container {
  position: relative;
}
.section-4 .nav-top {
  width: calc(136px + 60% - 60px);
  height: 50px;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  justify-content: space-between;
  margin: 0;
}
.section-4 .nav-item {
  color: #41464f;
  font-size: 16px;
  cursor: pointer;
  font-weight: bold;
}
.section-4 .nav-top li:nth-child(5),
.section-4 .nav-top li:nth-child(6) {
  color: #c5c5c5;
  pointer-events: none;
}
.section-4 .nav-item.active {
  color: #1764ff;
}
.section-4 .nav-content {
  position: relative;
  width: 100%;
  padding: 52px 0;
}
.section-4 .nav-content .bg {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  pointer-events: none;
  user-select: none;
  border-radius: 0 16px 16px 0;
  overflow: hidden;
}

.section-4 .nav-content .bg img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  display: none;
}
.section-4 .nav-content .bg img:nth-child(1) {
  display: block;
}

/* .section-4 .bg {
  display: none;
}
.section-4 .bg:nth-child(1) {
  display: block;
} */

.section-4 .nav-content .mobile-case {
  position: absolute;
  top: -100px;
  right: 260px;
  z-index: 1;
}
.section-4 .nav-content .mobile-case .phone-mode {
  position: absolute;
  top: 0;
  left: -60px;
  width: 400px;
  height: 682px;
  z-index: 2;

  background: url("./imgs/section_4/phone_bg.png") no-repeat 0 0 / contain;
}
.section-4 .nav-content .mobile-case .phone-content {
  position: relative;
}
.section-4 .nav-content .mobile-case .content-img {
  /* position: absolute;
  top: 17px;
  left: -52px;
  width: 262px;
  height: 560px;
  z-index: 1;
  border-radius: 20px;
  overflow: hidden; */
  /* position: absolute;
  top: 2px;
  left: -60px;
  width: 422px;
  height: 677px;
  z-index: 1;
  border-radius: 20px;
  overflow: hidden; */
  position: absolute;
  top: 16px;
  left: -47px;
  width: 240px;
  height: 523px;
  z-index: 1;
  border-radius: 20px;
  overflow: hidden;
}
.section-4 .nav-content .mobile-case .content-img img {
  width: 100%;
  height: 100%;
  object-fit: fill;
  animation-name: myzoomIn;
  animation-duration: 0.4s;
  animation-timing-function: cubic-bezier(0.25, 0.1, 0.25, 1);
  animation-delay: 0s;
  animation-iteration-count: 1;
  animation-direction: normal;
  animation-fill-mode: both;
  animation-play-state: running;
}
.section-4 .nav-content .mobile-case .content-bg {
  background-color: #fff;
  position: absolute;
  top: 17px;
  /* left: 8px; */
  left: -51px;
  width: 296px;
  height: 560px;
  z-index: 0;
  border-radius: 20px;
}

@keyframes myzoomIn {
  0% {
    /* transform: scale(1.05); */
    transform: scale(0.98);
    opacity: 0;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}
/* section4 动画 */
.section-4 .pingtai-shop {
  width: 380px !important;
}
.section-4 .phone-mode img {
  width: 320px;
  transition: all 0.3s ease-out;
}
.section-4 .news-img1.gove-g2b,
.section-4 .jiaoyi-img {
  width: 360px !important;
}
.section-4 .news-img1.gove-g2g {
  width: 260px !important;
  transform: translate(4rem, 11rem);
}
.section-4 .news-img1.factorimg {
  width: 230px !important;
  transform: translate(1.6rem, 9rem);
}

.section-4 .news-img1 {
  opacity: 0;
  transform: translate(-1.5rem, 8.4rem);
}

.section-4 .news-img1 {
  animation: anim1 0.3s;
  animation-delay: 0.4s;
  animation-fill-mode: forwards;
}
.section-4 .gove-g2g {
  animation: anim2 0.3s;
  animation-delay: 0.4s;
  animation-fill-mode: forwards;
}
.section-4 .factorimg {
  animation: anim3 0.3s;
  animation-delay: 0.4s;
  animation-fill-mode: forwards;
}

@keyframes anim1 {
  0% {
    transform: translate(-2rem, 11.4rem);
  }
  100% {
    opacity: 1;
    transform: translate(-2rem, 8.4rem);
  }
}
@keyframes anim2 {
  0% {
    transform: translate(4rem, 14rem);
  }
  100% {
    opacity: 1;
    transform: translate(4rem, 11rem);
  }
}
@keyframes anim3 {
  0% {
    transform: translate(1.6rem, 12rem);
  }
  100% {
    opacity: 1;
    transform: translate(1.6rem, 9rem);
  }
}

.section-4 .img-txt {
  opacity: 0;
  width: 0;
  position: absolute;
  top: 4rem;
  left: 11rem;
  z-index: 1;
  color: #fff;
  background: rgba(0, 0, 0, 0.8);
  height: 38px;
  line-height: 38px;
  padding-left: 14px;
  border-radius: 26px;
  overflow: hidden;
}
.section-4 .img-txt-style1,
.section-4 .img-txt-style3 {
  left: 5rem;
  top: 10.6rem;
  width: 235px;
}
.section-4 .img-txt-style2 {
  left: 12rem;
  top: 21rem;
  width: 120px;
}
.section-4 .img-txt-style4 {
  left: -1rem;
  top: 28rem;
  width: 120px;
}
.section-4 .img-txt-style5 {
  left: -1rem;
  top: 24rem;
  width: 194px;
}
.section-4 .img-txt-style6 {
  left: 11rem;
  top: 13rem;
  width: 120px;
}
.section-4 .img-txt-style7 {
  left: -2rem;
  top: 6rem;
  width: 116px;
}
.section-4 .img-txt-style8 {
  left: 10rem;
  top: 21rem;
  width: 180px;
}
.section-4 .img-txt-style9 {
  left: 10.4rem;
  top: 12rem;
  width: 114px;
}
.section-4 .img-txt-style10 {
  left: -2rem;
  top: 24.4rem;
  width: 205px;
}
.section-4 .img-txt-style11 {
  left: 6rem;
  top: 17rem;
  width: 200px;
}
.section-4 .img-txt-style12 {
  left: -1rem;
  top: 9rem;
  width: 165px;
}
.section-4 .img-txt-style13 {
  left: 12rem;
  top: 27rem;
  width: 110px;
}
.section-4 .img-txt-style14 {
  left: -1rem;
  top: 10rem;
  width: 150px;
}
.section-4 .img-txt-style15 {
  left: 12rem;
  top: 20rem;
  width: 116px;
}
.section-4 .img-txt-style16 {
  left: -1rem;
  top: 16rem;
  width: 214px;
}
.section-4 .img-txt-style17 {
  left: 12rem;
  top: 26rem;
  width: 120px;
}
.section-4 .img-txt-style18 {
  left: -1rem;
  top: 12rem;
  width: 184px;
}
.section-4 .img-txt-style19 {
  left: 9rem;
  top: 26rem;
  width: 150px;
}
.section-4 .img-txt-style20 {
  left: 9rem;
  top: 23rem;
  width: 210px;
}

.section-4 .img-txt-style1,
.section-4 .img-txt-style3 {
  animation: text1 0.5s;
  animation-delay: 0.7s;
  animation-fill-mode: forwards;
}
.section-4 .img-txt-style2 {
  animation: text2 0.5s;
  animation-delay: 0.7s;
  animation-fill-mode: forwards;
}
.section-4 .img-txt-style4 {
  animation: text4 0.5s;
  animation-delay: 0.7s;
  animation-fill-mode: forwards;
}
.section-4 .img-txt-style5 {
  animation: text5 0.5s;
  animation-delay: 0.7s;
  animation-fill-mode: forwards;
}
.section-4 .img-txt-style6 {
  animation: text6 0.5s;
  animation-delay: 0.7s;
  animation-fill-mode: forwards;
}
.section-4 .img-txt-style7 {
  animation: text7 0.5s;
  animation-delay: 0.7s;
  animation-fill-mode: forwards;
}
.section-4 .img-txt-style8 {
  animation: text8 0.5s;
  animation-delay: 0.7s;
  animation-fill-mode: forwards;
}
.section-4 .img-txt-style9 {
  animation: text9 0.5s;
  animation-delay: 0.7s;
  animation-fill-mode: forwards;
}
.section-4 .img-txt-style10 {
  animation: text10 0.5s;
  animation-delay: 0.7s;
  animation-fill-mode: forwards;
}
.section-4 .img-txt-style11 {
  animation: text11 0.5s;
  animation-delay: 0.7s;
  animation-fill-mode: forwards;
}
.section-4 .img-txt-style12 {
  animation: text12 0.5s;
  animation-delay: 0.7s;
  animation-fill-mode: forwards;
}
.section-4 .img-txt-style13 {
  animation: text13 0.5s;
  animation-delay: 0.7s;
  animation-fill-mode: forwards;
}
.section-4 .img-txt-style14 {
  animation: text14 0.5s;
  animation-delay: 0.7s;
  animation-fill-mode: forwards;
}
.section-4 .img-txt-style15 {
  animation: text15 0.5s;
  animation-delay: 0.7s;
  animation-fill-mode: forwards;
}
.section-4 .img-txt-style16 {
  animation: text16 0.5s;
  animation-delay: 0.7s;
  animation-fill-mode: forwards;
}
.section-4 .img-txt-style17 {
  animation: text17 0.5s;
  animation-delay: 0.7s;
  animation-fill-mode: forwards;
}
.section-4 .img-txt-style18 {
  animation: text18 0.5s;
  animation-delay: 0.7s;
  animation-fill-mode: forwards;
}
.section-4 .img-txt-style19 {
  animation: text19 0.5s;
  animation-delay: 0.7s;
  animation-fill-mode: forwards;
}
.section-4 .img-txt-style20 {
  animation: text20 0.5s;
  animation-delay: 0.7s;
  animation-fill-mode: forwards;
}

@keyframes text1 {
  0% {
    width: 0;
    opacity: 0;
  }
  100% {
    width: 235px;
    opacity: 1;
  }
}
@keyframes text2 {
  0% {
    width: 0;
    opacity: 0;
  }
  100% {
    width: 120px;
    opacity: 1;
  }
}
@keyframes text4 {
  0% {
    width: 0;
    opacity: 0;
  }
  100% {
    width: 120px;
    opacity: 1;
  }
}
@keyframes text5 {
  0% {
    width: 0;
    opacity: 0;
  }
  100% {
    width: 194px;
    opacity: 1;
  }
}
@keyframes text6 {
  0% {
    width: 0;
    opacity: 0;
  }
  100% {
    width: 120px;
    opacity: 1;
  }
}
@keyframes text7 {
  0% {
    width: 0;
    opacity: 0;
  }
  100% {
    width: 116px;
    opacity: 1;
  }
}
@keyframes text8 {
  0% {
    width: 0;
    opacity: 0;
  }
  100% {
    width: 180px;
    opacity: 1;
  }
}
@keyframes text9 {
  0% {
    width: 0;
    opacity: 0;
  }
  100% {
    width: 114px;
    opacity: 1;
  }
}
@keyframes text10 {
  0% {
    width: 0;
    opacity: 0;
  }
  100% {
    width: 205px;
    opacity: 1;
  }
}
@keyframes text11 {
  0% {
    width: 0;
    opacity: 0;
  }
  100% {
    width: 200px;
    opacity: 1;
  }
}
@keyframes text12 {
  0% {
    width: 0;
    opacity: 0;
  }
  100% {
    width: 165px;
    opacity: 1;
  }
}
@keyframes text13 {
  0% {
    width: 0;
    opacity: 0;
  }
  100% {
    width: 110px;
    opacity: 1;
  }
}
@keyframes text14 {
  0% {
    width: 0;
    opacity: 0;
  }
  100% {
    width: 150px;
    opacity: 1;
  }
}
@keyframes text15 {
  0% {
    width: 0;
    opacity: 0;
  }
  100% {
    width: 116px;
    opacity: 1;
  }
}
@keyframes text16 {
  0% {
    width: 0;
    opacity: 0;
  }
  100% {
    width: 214px;
    opacity: 1;
  }
}
@keyframes text17 {
  0% {
    width: 0;
    opacity: 0;
  }
  100% {
    width: 120px;
    opacity: 1;
  }
}
@keyframes text18 {
  0% {
    width: 0;
    opacity: 0;
  }
  100% {
    width: 184px;
    opacity: 1;
  }
}
@keyframes text19 {
  0% {
    width: 0;
    opacity: 0;
  }
  100% {
    width: 150px;
    opacity: 1;
  }
}
@keyframes text20 {
  0% {
    width: 0;
    opacity: 0;
  }
  100% {
    width: 210px;
    opacity: 1;
  }
}

/*  */

.section-4 .nav-info {
  width: 100%;
  display: flex;
  background-color: #fff;
  border-radius: 8px 0 0 8px;
  overflow: hidden;
  z-index: 0;
  position: relative;
  min-height: 360px;
}
.section-4 .sub-nav {
  display: flex;
  flex-direction: column;
  width: 136px;
  margin: 0;
  min-height: 360px;
  justify-content: space-between;
}
.section-4 .sub-nav-item {
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background-color: transparent;
  transition: all 0.3s ease-out;
  cursor: pointer;
  font-size: 14px;
}
.section-4 .sub-nav-item.active {
  background-color: #f5f7fb;
  color: #1a1c1f;
  font-weight: bold;
}
.section-4 .sub-nav-item img {
  width: 34px;
  height: 34px;
  object-fit: contain;
}
.section-4 .sub-nav-content {
  width: 60%;
  padding: 24px 60px;
  box-sizing: border-box;
}
.section-4 .sub-nav-title {
  margin-top: 12px;
}
.section-4 .sub-nav-text {
  font-size: 14px;
  color: #41464f;
}
.section-4 .case {
  background: linear-gradient(to bottom, #f3f5f9, #ffffff);
  padding: 6px 14px;
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  justify-content: space-between;
  font-size: 14px;
  border: 1px solid #e1e6ed;
  transition: all 0.4s ease-out;
  border-radius: 4px;
}
.section-4 .case a {
  display: inline-block;
  color: #212529;
}
.section-4 .case:hover {
  border: 1px solid #ced4dd;
}
.section-4 .case:hover a {
  color: #1764ff !important;
}
.section-4 .case-left {
  display: flex;
  align-items: center;
}
.section-4 .case-logo {
  width: 60px;
  height: 60px;
}
.section-4 .case-logo img {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.section-4 .case-info .case-name {
  font-size: 16px;
  font-weight: bold;
}
.section-4 .case-info .case-text {
  color: #41464f;
  max-width: 270px;
}
.section-4 .up-down {
  width: 14px;
  height: 16px;
}
.section-4 .roat-deg {
  transform: rotate(180deg);
}
.section-4 .number {
  color: #212529 !important;
  font-size: 48px;
  font-weight: 400;
  font-family: "BarlowSemiCondensedSemiBold";
}
.section-4 .num-info {
  transform: translate(0px, 5px);
}
.section-4 .case-rate {
  display: flex;
}
.section-4 .case-rate .rate-item {
  color: #1764ff;
}
.section-4 .case-rate .rate-item:first-child {
  width: 75px;
}
.section-4 .case-rate .rate-item:last-child {
  color: #1764ff;
  width: 85px;
  margin-left: 5px !important;
}
.section-4 .case-rate .rate-item .rate-num {
  font-size: 24px;
  font-weight: bold;
}
.section-4 .case-rate .rate-item .rate-num .shang-wrap {
  display: inline-block;
  width: 20px;
  height: 20px;
  overflow: hidden;
  vertical-align: text-bottom;
  transform: translateX(-4px);
}
.section-4 .case-rate .rate-item .rate-num .shang-wrap img {
  width: 100%;
  height: 100%;
  object-fit: contain;
  transform: translateX(-20px);
  filter: drop-shadow(#1764ff 20px 0);
  -webkit-filter: drop-shadow(#1764ff 20px 0);
  -moz-filter: drop-shadow(#1764ff 20px 0);
  -ms-filter: drop-shadow(#1764ff 20px 0);
  -o-filter: drop-shadow(#1764ff 20px 0);
}
.section-4 .case-rate .rate-item .rate-name {
  color: #41464f;
  transform: translate(-4px, -8px);
}
.section-4 .about-title {
  font-size: 13px;
}
.section-4 .about-list {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  margin: 0;
}

.section-4 .about-item {
  width: 120px;
  max-width: 120px;
  height: 40px;
  line-height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid rgb(206, 212, 221);
  background-image: -moz-linear-gradient(
    90deg,
    rgb(255, 255, 255) 0%,
    rgb(243, 245, 249) 100%
  );
  background-image: -webkit-linear-gradient(
    90deg,
    rgb(255, 255, 255) 0%,
    rgb(243, 245, 249) 100%
  );
  background-image: -ms-linear-gradient(
    90deg,
    rgb(255, 255, 255) 0%,
    rgb(243, 245, 249) 100%
  );
  border-radius: 2px;
  text-align: center;
  position: relative;
  transition: all 0.3s;
  cursor: pointer;
  border: 1px solid #e1e6ed;
  font-size: 14px;
}

.section-4 .about-item:hover {
  color: #1764ff;
  border-color: #fff;
  box-shadow: 0px 8px 20px 0px rgba(0, 17, 86, 0.08);
}
.section-4 .about-item:last-child {
  margin-right: 0 !important;
}

@media screen and (min-width: 1400px) {
  .section-4 .nav-content .bg {
    width: calc(50% + 1280px / 2 - 15px);
  }
}
@media screen and (min-width: 1200px) and (max-width: 1399px) {
  .section-4 .nav-content .bg {
    width: calc(50% + 1140px / 2 - 15px);
  }
}
@media screen and (min-width: 992px) and (max-width: 1199px) {
  .section-4 .nav-content .bg {
    width: calc(50% + 960px / 2 - 15px);
  }
}
@media screen and (max-width: 991px) {
  .section-4 .nav-content .mobile-case {
    display: none;
  }
  .section-4 .nav-content .bg {
    width: 100%;
    border-radius: 0;
  }
  .section-4 .sub-nav {
    width: 96px;
    flex-shrink: 0;
  }
  .section-4 .nav-top {
    width: 100%;
  }
  .section-4 .nav-item {
    font-size: 14px;
  }
  .section-4 .sub-nav-content {
    padding: 16px;
    width: calc(100% - 96px);
  }
  .section-4 .nav-info {
    border-radius: 8px;
  }
  .section-4 .sub-nav-text {
    font-size: 13px;
  }
  .section-4 .case-logo {
    width: 60px;
    height: 60px;
    margin-right: 8px !important;
    flex-shrink: 0;
  }
  .section-4 .nav-content .case {
    padding: 16px;
  }
  .section-4 .about-list .about-item {
    width: calc(33.3% - 0.5rem);
    min-width: 100px;
  }
  .section-4 .case-rate .rate-item {
    margin-left: 0.5rem !important;
  }
}
@media screen and (max-width: 768px) {
  #faq-colls .case {
    padding: 14px;
  }
  #faq-colls .body-content {
    padding: 0 1rem;
  }
  #faq-colls .case-rate {
    display: none;
  }
  #sction_4 {
    display: none;
  }
  #product-faq {
    display: block;
  }
  .section-4 .number {
    font-size: 38px;
  }
}
@media screen and (max-width: 575px) {
  .section-4 .sub-nav {
    width: 80px;
  }
  .section-4 .nav-top {
    width: 100%;
  }
  .section-4 .nav-item {
    font-size: 14px;
  }
  .section-4 .sub-nav-content {
    padding: 16px;
    width: calc(100% - 80px);
  }
  .section-4 .case-rate {
    margin-top: 12px;
  }
  .section-4 .case-rate .rate-item .rate-num {
    font-size: 20px;
  }
  .section-4 .case-rate .rate-item .rate-num .shang-wrap {
    width: 18px;
    height: 18px;
  }
  .section-4 .case-logo {
    width: 52px;
    height: 52px;
    margin-right: 8px !important;
    flex-shrink: 0;
  }
  .section-4 .nav-content .case {
    padding: 12px;
    flex-direction: column;
  }
}

/* v4-home-section-5 */
.section-5 {
  padding: 86px 0 12px;
  background: #fff;
  position: relative;
}
.section-5 .card-list {
  display: flex;
  justify-content: space-between;
  margin: 0 -0.5rem;
  flex-wrap: wrap;
  margin-top: 7rem;
}
.section-5 .card-list .card-item {
  position: relative;
  padding: 36px 30px !important;
  border-radius: 6px;
  width: 100%;
  height: 100%;
  background-color: #f3f5f9;
}
.section-5 .card-list .card-item .title {
  font-weight: bold;
  font-size: 18px;
  color: #1a1c1f;
}
.section-5 .card-list .card-item .text {
  font-size: 14px;
  color: #41464f;
}
.section-5 .card-list .card-item img {
  position: absolute;
  top: 0;
  right: 12px;
  transform: translateY(-50%);
  width: 140px;
  object-fit: contain;
  pointer-events: none;
  user-select: none;
}
.section-5 .card-list li {
  margin-bottom: 6rem;
}
.section-5 .card-item {
  padding: 20px 24px !important;
}
@media (max-width: 768px) {
  .section-5 .card-list .card-item .title {
    font-size: 18px !important;
  }
  .section-5 {
    padding: 40px 0 0px;
  }
}

/* v4-home-section-6 */
.section-6 .section6-p-icon,
.section-8 .section6-p-icon {
  width: 100px;
}
.section-6 .price-right {
  height: 680px;
}
.section-6 .section6-list {
  bottom: 80px;
  transform: translate3d(0px, 0px, 0px);
  user-select: none;
}
.section-6 .section6-list-item {
  width: 330px !important;
  min-height: 200px;
  background: rgba(255, 255, 255, 0.6);
  width: fit-content;
  border-radius: 8px;
  backdrop-filter: blur(70px);
  margin-right: 20px;
  padding: 20px;
}

.section-6 .section6-text {
  font-size: 20px;
}
.section-6 .section6-text > img {
  width: 96px;
  padding-top: 10px;
}
.section-6 .section6-name {
  font-weight: bold;
  font-size: 18px;
  padding: 20px 0px 8px 0px;
}

.section-6 .section6-tips {
  padding: 2px 6px;
  border-radius: 2px;
  font-weight: normal;
  background-color: #e7eaef;
}
.section-6 .bg-yuan {
  width: 105%;
  height: 170%;
  position: absolute;
  border-radius: 50%;
  background: #f0f2f8;
  top: 15%;
  left: 50%;
  transform: translate(-50%, 0%);
}
.section-6 .award-img {
  width: 54%;
  position: absolute;
  right: 8rem;
  bottom: 0;
}
.section-6 .section6-desc {
  font-size: 14px;
  color: #41464f;
}
.section-6 {
  padding: 24px 0 0;
  background-color: #f5f7fc;
  position: relative;
  overflow: hidden;
}

.section-6 .praise-item .item-wrap {
  border-radius: 8px;
  border: 2px solid #fff;
  box-shadow: 8px 8px 20px 0 rgba(55, 99, 170, 0.1);
  width: 100%;
  min-height: 188px;
  height: 100%;
  overflow: hidden;
  position: relative;
  background: linear-gradient(to bottom, #f3f5f9, #fff);
}

.section-6 .praise img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.section-6 .comment-content {
  flex: 5;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  box-sizing: border-box;
}
.section-6 .user-comment {
  font-size: 14px;
  position: relative;
}
.section-6 .user-comment::after {
  content: "，";
  position: absolute;
  top: 0;
  right: 0;
  color: #cecfd3;
  font-size: 24px;
  line-height: 1;
  pointer-events: none;
  user-select: none;
  transform: translate(calc(100% + 24px), -60%);
}
.section-6 .user-info {
  font-size: 14px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.section-6 .user-info .user-logo img {
  height: 40px;
  width: 80px;
  object-fit: contain;
}
.section-6 .user-info .user-name {
  position: relative;
}
.section-6 .user-info .user-name::after {
  content: "";
  position: absolute;
  top: 50%;
  left: 0;
  transform: translate(-150%, -50%);
  width: 40px;
  height: 1px;
  background-color: #a1abb6;
}
.section-6 .partner-title {
  position: relative;
  text-align: center;
  width: 100%;
  font-size: 13px;
  margin-top: 32px;
}
.section-6 .partner-title span {
  position: relative;
  z-index: 1;
  display: inline-block;
  background-color: #f5f7fc;
  padding: 0 16px;
}
.section-6 .partner-title::after {
  content: "";
  width: 100%;
  height: 1px;
  background-color: #c8d5e3;
  position: absolute;
  top: 50%;
  left: 0;
}
.section-6 .partner-list img {
  width: 60%;
  margin: 0 auto;
  object-fit: contain;
  -webkit-user-drag: none;
  pointer-events: none;
  user-select: none;
  display: block;
}
.section-6 .price-header {
  padding-top: 4rem;
}
@media screen and (max-width: 768px) {
  .section-6 .price-header {
    padding-top: 40px;
  }
}
@media screen and (max-width: 991px) {
  .section-6 .praise-item {
    padding-left: 15px !important;
    padding-right: 15px !important;
  }
}
@media screen and (max-width: 575px) {
  .section-6 .praise img {
    max-height: 160px;
  }
}

/* v4-home-section-7 */
.section-7 {
  padding-top: 86px;
}
.section-7 .black-content {
  background-color: #1a1c1f;
  width: 100%;
  margin-top: 70px;
  padding: 36px 0 32px;
  position: relative;
  color: #fff;
  z-index: 1;
}
.section-7 .blue-bg {
  width: 100%;
  height: 100%;
  background-color: #1764ff;
  position: absolute;
  top: 0;
  left: 0;
  transform: translate(-50%, 0px) skew(0deg, 0);
  z-index: -1;
}

.section-7 .souce-link {
  text-decoration: none;
  font-size: 14px;
  display: inline-flex;
  color: #fff;
}

.section-7 .title {
  font-size: 26px;
  font-weight: bold;
  margin-top: 12px;
  margin-bottom: 16px;
}
.section-7 .souce-content {
  display: flex;
  justify-content: space-between;
}

.section-7 .souce-left .left-card {
  display: flex;
}
.section-7 .souce-left .line {
  width: 100%;
  height: 1px;
  background-color: #5d93ff;
}
.section-7 .souce-left .left-card .colud-img {
  border-radius: 12px;
  overflow: hidden;
  width: 220px;
  height: 120px;
}
.section-7 .colud-img:hover .scal-img {
  transform: scale(1.1);
}
.section-7 .souce-left .left-card .card-name {
  font-size: 18px;
  font-weight: 500;
  color: #fff;
}
.section-7 .souce-left .left-card .card-text {
  font-size: 14px;
  margin-top: 4px;
  line-height: 1.4;
  color: #d6dffd;
}
.section-7 .souce-left .left-card .card-btn {
  margin-top: 16px;
  border: 1px solid #fff;
  border-radius: 4px;
  text-align: center;
  height: 28px;
  line-height: 28px;
  padding: 0 14px;
  font-size: 14px;
  display: inline-block;
  cursor: pointer;
  color: #fff;
  box-sizing: border-box;
}
.section-7 .souce-left .left-card .card-btn:hover {
  background-color: rgba(255, 255, 255, 0.2);
}

.section-7 .scal-img {
  transition: all 0.4s ease-out;
}
.section-7 .scal-img.active {
  transform: scale(1.1);
}
.section-7 .souce-right .card-list {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
}
.section-7 .souce-right .card-list .card-item {
  width: calc(50% - 6px);
  padding: 12px 16px;
  background: linear-gradient(to bottom, #24272d, #2c333d);
  border: 1px solid #4b5563;
  border-radius: 4px;
  margin-top: 12px;
  display: flex;
  flex-direction: column;
  position: relative;
}
.section-7 .souce-right .card-list .card-item .card-title {
  font-size: 18px;
  font-weight: 500;
  color: #fff;
}
.section-7 .souce-right .card-list .card-item .text_icon {
  height: 16px;
  width: fit-content;
  object-fit: contain;
  margin-bottom: 4px;
}
.section-7 .souce-right .card-list .card-item span {
  margin-top: 4px;
  font-size: 15px;
  color: #8d96a9;
}

.section-7 .souce-right .card-list .card-item .img_icon {
  position: absolute;
  top: 50%;
  right: 16px;
  transform: translateY(-50%);
  width: 52px;
  height: 52px;
  object-fit: contain;
}
.section-7 .souce-right .card-list .card-item:hover {
  border: 1px solid #606b7a;
  background: linear-gradient(to bottom, #393d45, #3e4651);
}
.section-7 .carousel-wrap {
  height: 40px;
  overflow: hidden;
}

.section-7 .souce-right .btm-tips {
  line-height: 32px;
  height: 32px;
  margin-top: 3.5rem;
}
.section-7 .souce-right .btm-tips .tips-text {
  color: #fff;
  width: 70px;
}
.section-7 .souce-right .btm-tips .article {
  font-size: 14px;
  color: #8d96a9;
  width: calc(100% - 80px);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.section-7 .souce-right .btm-tips .article:hover {
  text-decoration: underline;
}
.section-7 .souce-right .btm-tips .article:hover,
.section-7 .souce-right .btm-tips .see-more:hover {
  text-decoration: underline !important;
}
.section-7 .souce-right .btm-tips .see-more {
  cursor: pointer;
  font-size: 14px;
  color: #fff;
}

.section-7 .swiper-container {
  width: calc(100% - 120px);
  height: 100%;
  margin: 0;
}

@media screen and (max-width: 991px) {
  /* .section-7 .blue-bg {
    display: none;
  } */
  .section-7 .souce-left,
  .section-7 .souce-right {
    padding-left: 15px !important;
    padding-right: 15px !important;
  }
  .section-7 .souce-left .line {
    display: none;
  }
  .section-7 .title {
    margin-bottom: 12px;
  }
  .section-7 .souce-left .left-card {
    padding-top: 12px !important;
  }
  .section-7 .souce-left .left-card .card-text {
    width: 100%;
  }

  .section-7 .souce-right .card-list .card-item .img_icon {
    width: 28px;
    height: 28px;
    right: 8px;
  }
  .section-7 .souce-right .card-list .card-item {
    padding: 12px;
  }
}
@media screen and (max-width: 768px) {
  .section-7 {
    padding-top: 40px;
  }
}

/* v4-home-section-8 */
.section-8 {
  padding: 36px 0 56px 0;
  background: #f5f7fc;
  position: relative;
  overflow: hidden;
}
.section-8 .link-more {
  font-size: 12px;
  display: inline-block;
  position: relative;
  top: 0;
  left: 50%;
  transform: translateX(-50%);
}
.section-8 .left-item {
  display: flex;
  align-items: center;
  margin: 0;
  border: 1px solid transparent;
  font-weight: bold;
  font-size: 0.8rem;
}
.section-8 a:hover .h2-tip-xj {
  transform: rotate(45deg);
}
.section-8-quanxian {
  position: absolute;
}
.data-desc {
  font-size: 14px;
  color: #606875;
}
.section-8 .left-item img {
  width: 40px;
  height: 40px;
  object-fit: contain;
  user-select: none;
  -webkit-user-drag: none;
}
.section-8 .right-card {
  border-radius: 4px;
  display: flex;
  justify-content: start;
  align-items: center;
  padding: 24px 15px;
  font-size: 14px;
}
.section-8 .right-card img {
  width: 72px;
  height: 72px;
  object-fit: contain;
}
.section-8 .data-right .data-right-item:nth-child(2) {
  background: linear-gradient(to bottom, #f5f7fc, #ebeef4);
}
.section-8 .data-right .data-right-item:nth-child(3) {
  background: linear-gradient(to bottom, #ebeef4, #f5f7fc);
}
.gt {
  transition: all 0.3s ease;
  position: relative;
  top: 0;
  left: 0;
  transform: rotate(180deg);
  display: inline-block;
  font-weight: 300;
  font-size: 14px;
}
.section-8 a.link:hover {
  color: #1764ff;
}
.section-8 a.link:hover .gt {
  left: 3px;
}

.section-8 .data-right-item {
  position: relative;
  overflow: hidden;
  width: 350px;
  padding: 30px 15px;
}
.section-8 .data-right-item::before {
  content: "";
  position: absolute;
  top: 15px;
  right: 10px;
  height: 25%;
  width: 5px;
  border-radius: 3px;
  will-change: height;
  background: #ebeef4;
  opacity: 0;
}
.section-8 .data-right-item::after {
  content: "";
  position: absolute;
  top: 15px;
  right: 10px;
  height: 0;
  width: 5px;
  border-radius: 3px;
  will-change: height;
}
.section-8 .data-right-item.active {
  border: 1px solid #e1e6ed;
  background: linear-gradient(to bottom, #f3f5f9, #ffffff) !important;
}
.section-8 .data-right-item.active::before {
  opacity: 1;
}
.section-8 .data-right-item.active::after {
  height: 25%;
  background-color: #1764ff;
  animation: step2 12s linear both;
}

@keyframes step2 {
  0% {
    height: 0%;
  }

  100% {
    height: 25%;
  }
}

.section-8 .right-card > img,
.section-8 .right-card .r-item-title {
  transition: all 0.4s ease-out;
}
.section-8 .right-card > img.active {
  transform: translate(-28%, -68%);
  width: 40px;
  margin-left: 15px;
}
.section-8 .right-card .r-item-title.active {
  transform: translate(0%, -3rem);
}
.section-8 .r-item-title.active span {
  font-size: 16px !important;
}
.section-8 .r-item-title.active p {
  display: none;
}
.section-8 .is-show-txt {
  position: absolute;
  bottom: -5rem;
  opacity: 0;
  transition: all 0.4s ease-out;
  left: 6px;
  padding: 0 15px;
}
.section-8 .is-show-txt.active {
  bottom: 2rem;
  font-size: 14px;
  opacity: 1;
}

@media screen and (max-width: 576px) {
  .section-8 .data-right-item {
    width: 100%;
  }
}
@media screen and (max-width: 992px) {
  .section-8 .data-right,
  .section-8 .data-left {
    padding-left: 15px !important;
    padding-right: 15px !important;
  }
  .section-8 .data-right .p-2:nth-child(2n + 1) {
    padding-left: 0 !important;
  }
  .section-8 .data-right .p-2:nth-child(2n) {
    padding-right: 0 !important;
  }
  .section-8 .yinsi-cont {
    width: 100%;
    text-align: center;
  }
  .section-8 .yinsi-cont a {
    justify-content: center;
  }
}

/* v4-home-section-9 */
/* 邮件 */

/* 箭头动画 */
.section-2 .mod-item:hover .roda-arrow,
.section-3 .card-wrap .card.card-1 .mini-content .tips:hover .roda-arrow,
.section-3 .card-wrap .card.card-2 .mini-content .tips:hover .roda-arrow,
.section-4 .case .case-name:hover .roda-arrow,
.section-7 .souce-link:hover .roda-arrow,
.register-section .my-button:hover .roda-arrow {
  transform: rotate(45deg) scale(0.8);
}
