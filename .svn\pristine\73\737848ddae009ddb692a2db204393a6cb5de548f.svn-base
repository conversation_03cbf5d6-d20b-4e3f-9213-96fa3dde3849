﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.InteropServices;
using System.Text;

namespace NewTicket.HttpItem.CurlItem
{
	public class MultiPartForm
	{
		private IntPtr[] m_pItems;

		public MultiPartForm()
		{
			Curl.EnsureCurl();
			this.m_pItems = new IntPtr[2];
			this.m_pItems[0] = IntPtr.Zero;
			this.m_pItems[1] = IntPtr.Zero;
		}

		~MultiPartForm()
		{
			this.Dispose(false);
		}

		internal IntPtr GetHandle()
		{
			return this.m_pItems[0];
		}

		public CURLFORMcode AddSection(params object[] args)
		{
			int num = args.Length;
			int num2 = num;
			CURLFORMcode cURLFORMcode = CURLFORMcode.CURL_FORMADD_OK;
			if (num == 1 || num % 2 == 0)
			{
				return CURLFORMcode.CURL_FORMADD_INCOMPLETE;
			}
			CURLformoption cURLformoption = (CURLformoption)Convert.ToInt32(args.GetValue(num - 1));
			if (cURLformoption != CURLformoption.CURLFORM_END)
			{
				return CURLFORMcode.CURL_FORMADD_INCOMPLETE;
			}
			CurlForms[] array;
			for (int i = 0; i < num; i += 2)
			{
				cURLformoption = (CURLformoption)Convert.ToInt32(args.GetValue(i));
				CURLformoption cURLformoption2 = cURLformoption;
				if (cURLformoption2 == CURLformoption.CURLFORM_ARRAY)
				{
					array = (args.GetValue(i + 1) as CurlForms[]);
					if (array == null)
					{
						return CURLFORMcode.CURL_FORMADD_INCOMPLETE;
					}
					int num3 = array.Length;
					for (int j = 0; j < num3; j++)
					{
						CurlForms curlForms = array.GetValue(j) as CurlForms;
						if (curlForms == null)
						{
							return CURLFORMcode.CURL_FORMADD_INCOMPLETE;
						}
						if (j == num3 - 1)
						{
							if (curlForms.opt != CURLformoption.CURLFORM_END)
							{
								return CURLFORMcode.CURL_FORMADD_INCOMPLETE;
							}
						}
						else if (curlForms.opt == CURLformoption.CURLFORM_END)
						{
							return CURLFORMcode.CURL_FORMADD_INCOMPLETE;
						}
					}
					num2 += 2 * (num3 - 2);
				}
			}
			IntPtr[] array2 = new IntPtr[num2];
			for (int k = 0; k < num2 - 1; k++)
			{
				array2[k] = IntPtr.Zero;
			}
			array2[num2 - 1] = (IntPtr)17L;
			array = null;
			int num4 = 0;
			int num5 = 0;
			int num6 = 0;
			while (cURLFORMcode == CURLFORMcode.CURL_FORMADD_OK && num6 < num2)
			{
				object obj;
				if (array != null)
				{
					CurlForms curlForms2 = array.GetValue(num4++) as CurlForms;
					if (curlForms2 == null)
					{
						cURLFORMcode = CURLFORMcode.CURL_FORMADD_UNKNOWN_OPTION;
						break;
					}
					cURLformoption = curlForms2.opt;
					obj = curlForms2.val;
				}
				else
				{
					cURLformoption = (CURLformoption)Convert.ToInt32(args.GetValue(num5++));
					obj = ((cURLformoption == CURLformoption.CURLFORM_END) ? null : args.GetValue(num5++));
				}
				switch (cURLformoption)
				{
				case CURLformoption.CURLFORM_NOTHING:
					cURLFORMcode = CURLFORMcode.CURL_FORMADD_INCOMPLETE;
					continue;
				case CURLformoption.CURLFORM_COPYNAME:
				case CURLformoption.CURLFORM_COPYCONTENTS:
				case CURLformoption.CURLFORM_FILECONTENT:
				case CURLformoption.CURLFORM_FILE:
				case CURLformoption.CURLFORM_BUFFER:
				case CURLformoption.CURLFORM_CONTENTTYPE:
				case CURLformoption.CURLFORM_FILENAME:
				{
					array2[num6++] = (IntPtr)((long)cURLformoption);
					string text = obj as string;
					if (text == null)
					{
						cURLFORMcode = CURLFORMcode.CURL_FORMADD_UNKNOWN_OPTION;
						continue;
					}
					IntPtr intPtr = Marshal.StringToHGlobalAnsi(text);
					if (intPtr != IntPtr.Zero)
					{
						array2[num6++] = intPtr;
						continue;
					}
					cURLFORMcode = CURLFORMcode.CURL_FORMADD_MEMORY;
					continue;
				}
				case CURLformoption.CURLFORM_PTRNAME:
				case CURLformoption.CURLFORM_PTRCONTENTS:
				case CURLformoption.CURLFORM_BUFFERPTR:
				{
					byte[] array3 = obj as byte[];
					if (array3 == null)
					{
						cURLFORMcode = CURLFORMcode.CURL_FORMADD_UNKNOWN_OPTION;
						continue;
					}
					int num7 = array3.Length;
					IntPtr intPtr2 = Marshal.AllocHGlobal(num7);
					if (intPtr2 != IntPtr.Zero)
					{
						array2[num6++] = (IntPtr)((long)cURLformoption);
						for (int l = 0; l < num7; l++)
						{
							Marshal.WriteByte(intPtr2, array3[l]);
						}
						array2[num6++] = intPtr2;
						continue;
					}
					cURLFORMcode = CURLFORMcode.CURL_FORMADD_MEMORY;
					continue;
				}
				case CURLformoption.CURLFORM_NAMELENGTH:
				case CURLformoption.CURLFORM_CONTENTSLENGTH:
				case CURLformoption.CURLFORM_BUFFERLENGTH:
					array2[num6++] = (IntPtr)((long)cURLformoption);
					array2[num6++] = (IntPtr)Convert.ToInt32(obj);
					continue;
				case CURLformoption.CURLFORM_ARRAY:
					if (array != null)
					{
						cURLFORMcode = CURLFORMcode.CURL_FORMADD_ILLEGAL_ARRAY;
						continue;
					}
					array = (obj as CurlForms[]);
					if (array == null)
					{
						cURLFORMcode = CURLFORMcode.CURL_FORMADD_UNKNOWN_OPTION;
						continue;
					}
					continue;
				case CURLformoption.CURLFORM_CONTENTHEADER:
				{
					array2[num6++] = (IntPtr)((long)cURLformoption);
					Slist slist = obj as Slist;
					if (slist == null)
					{
						cURLFORMcode = CURLFORMcode.CURL_FORMADD_UNKNOWN_OPTION;
						continue;
					}
					array2[num6++] = slist.GetHandle();
					continue;
				}
				case CURLformoption.CURLFORM_END:
					if (array != null)
					{
						array = null;
						num4 = 0;
						continue;
					}
					array2[num6++] = (IntPtr)((long)cURLformoption);
					continue;
				}
				cURLFORMcode = CURLFORMcode.CURL_FORMADD_UNKNOWN_OPTION;
			}
			if (num6 != num2)
			{
				cURLFORMcode = CURLFORMcode.CURL_FORMADD_INCOMPLETE;
			}
			if (cURLFORMcode == CURLFORMcode.CURL_FORMADD_OK)
			{
				cURLFORMcode = (CURLFORMcode)External.curl_shim_formadd(this.m_pItems, array2, num2);
			}
			for (int m = 0; m < num2 - 1; m += 2)
			{
				switch ((int)array2[m])
				{
				case 1:
				case 2:
				case 4:
				case 5:
				case 7:
				case 10:
				case 11:
				case 12:
				case 14:
				case 16:
					if (array2[m + 1] != IntPtr.Zero)
					{
						Marshal.FreeHGlobal(array2[m + 1]);
					}
					break;
				}
			}
			return cURLFORMcode;
		}

		public void Free()
		{
			GC.SuppressFinalize(this);
			this.Dispose(true);
		}

		private void Dispose(bool disposing)
		{
			lock (this)
			{
				if (this.m_pItems[0] != IntPtr.Zero)
				{
					External.curl_formfree(this.m_pItems[0]);
				}
				this.m_pItems[0] = IntPtr.Zero;
				this.m_pItems[1] = IntPtr.Zero;
			}
		}
	}
}
