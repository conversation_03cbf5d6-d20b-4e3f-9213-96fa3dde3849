﻿
using CommonLib;
using System.Collections.Generic;

namespace TableOcr
{
    /// <summary>
    /// https://web.baimiaoapp.com/image-to-excel
    /// </summary>
    public class BaiMiaoYouDaoRec : BaseTableRec
    {
        public BaiMiaoYouDaoRec()
        {
            OcrGroup = OcrGroupType.有道;
            OcrType = TableOcrType.白描_有道;
            MaxExecPerTime = 25;

            LstJsonPreProcessArray = new List<object>() { "data", "ydResp", "Result", "tables" };
            LstJsonNextProcessArray = new List<object>() { "cells" };
            LstJsonResultProcessArray = new List<object>() { "lines|N|text" };
            LstRowIndex = new List<object>() { "rowRange" };
            LstColumnIndex = new List<object>() { "colRange" };
            RowIndexIsArray = false;
            IsRowIndexAddOne = true;
        }

        protected override string GetHtml(OcrContent content)
        {
            var result = BaiMiaoProcessHelper.GetHtml(content, "https://web.baimiaoapp.com/api/ocr/table/youdao", ExecTimeOutSeconds, false);
            return result;
        }

    }
}