﻿using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace CommonLib
{
    public class DisposeEmailHelper
    {
        static List<string> list = new List<string>();
        static string IndexUrl = "https://cdn.jsdelivr.net/gh/disposable/disposable-email-domains@master/domains.json";

        public static bool IsDisposeEmail(string email)
        {
            var hostname = email.Trim().ToLower();

            if (hostname.Contains('@'))
                hostname = hostname.Split("@".ToCharArray(), 2, StringSplitOptions.None)[1];

            return list.Any(o => hostname.EndsWith(o));
        }

        private static void InitData()
        {
            var content = WebClientSyncExt.GetHtml(IndexUrl + "?t=" + DateTime.Now.Ticks, 60);
            if (!string.IsNullOrEmpty(content) && content.StartsWith("["))
                list = JsonConvert.DeserializeObject<List<string>>(content);
        }

        public static void Init()
        {
            Task.Factory.StartNew(() =>
            {
                while (true)
                {
                    InitData();
                    System.Threading.Thread.Sleep(1 * 60 * 60);
                }
            });
        }
    }
}
