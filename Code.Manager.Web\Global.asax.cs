﻿using System;
using System.IO;
using System.Linq;
using System.Net;
using System.Threading;
using System.Web;
using CommonLib;
using log4net;
using log4net.Config;

namespace Code.Manager.Web
{
    public abstract class Global : HttpApplication
    {
        protected void Application_Start(object sender, EventArgs e)
        {
            lock (ConfigHelper.LockObj)
            {
                if (!ConfigHelper.IsOnInit)
                {
                    ConfigHelper.IsOnInit = true;
                    ConfigHelper.InitThread();
                    try
                    {
                        var file = new FileInfo(AppDomain.CurrentDomain.BaseDirectory + "Log4Net.config");
                        XmlConfigurator.Configure(file);
                    }
                    catch
                    {
                        // ignored
                    }

                    ConfigHelper.InitConfig();
                }
            }
        }

        protected void Session_Start(object sender, EventArgs e)
        {
        }

        protected void Application_BeginRequest(object sender, EventArgs e)
        {
        }

        protected void Application_AuthenticateRequest(object sender, EventArgs e)
        {
        }

        protected void Application_Error(object sender, EventArgs e)
        {
        }

        protected void Session_End(object sender, EventArgs e)
        {
        }

        protected void Application_End(object sender, EventArgs e)
        {
            ConfigHelper.IsExit = true;
            LogManager.GetLogger("IIS End").Info("【IIS挂了】 时间:" + ServerTime.LocalTime.ToString("yyyy-MM-dd HH:mm:ss"));
            var oe = Server.GetLastError();
            if (oe != null) //&& !oe.Message.Contains("超时"))
            {
                LogManager.GetLogger("IIS 错误").Error("【IIS挂了】 URL:" + Request.Url, Server.GetLastError());
                Server.ClearError();
            }
        }
    }
}