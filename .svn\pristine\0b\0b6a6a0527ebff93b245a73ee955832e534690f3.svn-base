﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Timers;

namespace CommonLib
{
    public class BaseRecHelper
    {
        static Dictionary<OcrType, Dictionary<int, BaseRec>> dicOcrs = new Dictionary<OcrType, Dictionary<int, BaseRec>>();
        static Dictionary<string, List<int>> ocrEnableTypeCache = new Dictionary<string, List<int>>();

        static BaseRecHelper()
        {
            Task.Factory.StartNew(() =>
            {
                LogHelper.Log.Info("【MaxExecTime】次数服务开启…");
                var dtLastReset = ServerTime.DateTime;
                var timer = new Timer() { Interval = 60 * 60 * 1000 };
                timer.Elapsed += (object sender, ElapsedEventArgs e) =>
                {
                    if (new TimeSpan(dtLastReset.Ticks - ServerTime.DateTime.Ticks).Hours >= 24)
                    {
                        dtLastReset = ServerTime.DateTime;
                        ResetOcrMaxExecTime();
                        LogHelper.Log.Info("【MaxExecTime】次数已重置！");
                    }
                };
                timer.Start();
            });
        }

        static void ResetOcrMaxExecTime()
        {
            try
            {
                foreach (var baseType in dicOcrs)
                {
                    try
                    {
                        foreach (var ocrType in baseType.Value)
                        {
                            try
                            {
                                ocrType.Value.Reset();
                            }
                            catch { }
                        }
                    }
                    catch { }
                }
            }
            catch { }
            ClearCache();
        }

        public static void InitOcrTypes(OcrType baseType, Dictionary<int, BaseRec> ocrTypes)
        {
            foreach (var item in ocrTypes.Values)
            {
                if (item.MaxExecPerTimeBack == 0)
                    item.MaxExecPerTimeBack = item.MaxExecPerTime;
            }
            if (!dicOcrs.ContainsKey(baseType))
            {
                dicOcrs.Add(baseType, ocrTypes);
            }
            else
            {
                var tmpValues = dicOcrs[baseType];
                if (tmpValues == null)
                {
                    tmpValues = new Dictionary<int, BaseRec>();
                }
                foreach (var item in ocrTypes)
                {
                    if (!tmpValues.ContainsKey(item.Key))
                    {
                        tmpValues.Add(item.Key, item.Value);
                    }
                }
                dicOcrs[baseType] = tmpValues;
            }
        }

        public static void DisableByType(OcrType baseType, int ocrType)
        {
            if (!dicOcrs.ContainsKey(baseType))
            {
                return;
            }
            if (!dicOcrs[baseType].ContainsKey(ocrType))
            {
                return;
            }
            dicOcrs[baseType].Remove(ocrType);
            ClearCache();
        }

        public static BaseRec GetInstance(OcrType baseType, int ocrType)
        {
            return dicOcrs[baseType][ocrType];
        }

        public static void ClearCache()
        {
            lock (ocrEnableTypeCache)
            {
                ocrEnableTypeCache.Clear();
            }
        }

        //public OcrGroupType OcrGroup { get; set; }

        //public int ProcessId { get; set; }

        public static List<int> GetEnableRecType(bool isEnable, OcrType baseType, OcrGroupType groupType, int? processId
            , bool? isSupportUrl, string fileExt, TransLanguageTypeEnum from, TransLanguageTypeEnum to)
        {
            if (baseType != OcrType.翻译)
            {
                from = TransLanguageTypeEnum.自动;
                to = TransLanguageTypeEnum.中文;
            }
            var key = string.Format("{0}_{1}_{2}_{3}_{4}_{5}_{6}", baseType, isEnable, isSupportUrl, groupType, processId, fileExt, to);
            if (!ocrEnableTypeCache.ContainsKey(key))
            {
                List<int> lstTmp;
                if (processId != null)
                {
                    lstTmp = dicOcrs[baseType].Values.Where(p => p.OcrType == processId).Select(p => p.OcrType).ToList();
                }
                else
                {
                    var lstSupport = dicOcrs[baseType].Values.Where(p =>
                    p.IsEnable() == isEnable
                    && (groupType == OcrGroupType.不限 || p.OcrGroup == groupType)
                    ).ToList();
                    if (isSupportUrl.HasValue && isSupportUrl.Value == true)
                    {
                        lstSupport = lstSupport.Where(p => p.IsSupportUrlOcr).ToList();
                    }
                    if (!string.IsNullOrEmpty(fileExt))
                    {
                        lstSupport = lstSupport.FindAll(p => p.AllowUploadFileTypes.Contains(fileExt)).ToList();
                    }
                    if (baseType == OcrType.翻译)
                    {
                        lstSupport = lstSupport.FindAll(p => p.TransLanguageDic.ContainsKey(to)).ToList();
                    }
                    lstTmp = lstSupport.OrderByDescending(p => p.MaxExecPerTime).Select(p => p.OcrType).ToList();
                }
                lock (ocrEnableTypeCache)
                {
                    ocrEnableTypeCache.Add(key, lstTmp);
                }
            }
            return ocrEnableTypeCache[key];
        }

        public static void Report()
        {
            try
            {
                StringBuilder sb = new StringBuilder();
                foreach (OcrType item in dicOcrs.Keys)
                {
                    sb.AppendLine(string.Format("==============================【" + item.ToString() + "】统计信息开始============================"));
                    foreach (var rec in dicOcrs[item].Values.OrderByDescending(p => p.MaxExecPerTime).ToList())
                    {
                        sb.AppendLine(BaseReport(rec));
                    }
                    sb.AppendLine();
                    //sb.AppendLine(string.Format("==============================【" + item.ToString() + "】统计信息结束============================"));
                }
                LogHelper.Log.Info(sb);
            }
            catch { }
        }

        private static string BaseReport(BaseRec ocr)
        {
            return string.Format("{0}Now/Max:{1}/{2}     Total:{3}       Forbid:{4}"
                , padRightEx(string.Format("【{0}】:{1}", ocr.GetOcrTypeName() + "-" + ocr.OcrType, ocr.State), 40), ocr.NowExecTimes.ToString().PadLeft(5), ocr.MaxExecPerTime.ToString().PadRight(10), ocr.TotalExecTimes.ToString().PadRight(10), ocr.ForbidTimes.ToString().PadRight(10));
        }

        private static string padRightEx(string str, int totalByteCount)
        {
            Encoding coding = Encoding.GetEncoding("gb2312");
            int dcount = str.ToCharArray().Select(p => { return coding.GetByteCount(p.ToString()) == 2 ? 1 : 0; }).Sum();
            return str.PadRight(totalByteCount - dcount);
        }
    }
}
