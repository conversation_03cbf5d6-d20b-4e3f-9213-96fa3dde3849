﻿using CommonLib;
using System.Collections.Generic;

namespace HanZiOcr
{
    /// <summary>
    /// https://www.volcengine.com/product/text-recognition
    /// https://www.volcengine.com/docs/6790/117730
    /// </summary>
    public class HuoShanNormalTextRec : BaseOcrRec
    {
        public HuoShanNormalTextRec()
        {
            OcrGroup = OcrGroupType.字节;
            OcrType = HanZiOcrType.火山文本;
            MaxExecPerTime = 28;
            LstJsonPreProcessArray = new List<object>() { "data", "chars" };
            LstJsonNextProcessArray = new List<object>() { "|Merge|" };

            IsSupportVertical = true;
            IsDesrializeVerticalByLocation = true;
            DicDeserializeVerticalJson = new Dictionary<string, string> { { "left", "x" }, { "top", "y" }, { "words", "char" } };
        }

        protected override string GetHtml(OcrContent content)
        {
            var strPost = "{\"Action\":\"OCRNormal\",\"ServiceName\":\"cv\",\"Version\":\"2020-08-26\",\"image_base64\":\"" + content.strBase64 + "\"}";
            var html = WebClientSyncExt.GetHtml("https://www.volcengine.com/proxy_ai_demo/invoke/proxy", strPost, ExecTimeOutSeconds);
            return html;
        }
    }
}