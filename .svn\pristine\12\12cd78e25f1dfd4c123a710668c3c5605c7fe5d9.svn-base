﻿using CommonLib;
using System.Collections.Generic;

namespace HanZiOcr
{
    /// <summary>
    /// https://www.volcengine.com/product/multi-language
    /// https://www.volcengine.com/docs/6790/117810
    /// </summary>
    public class HuoShanMultiLanguageRec : BaseOcrRec
    {
        public HuoShanMultiLanguageRec()
        {
            OcrGroup = OcrGroupType.字节;
            OcrType = HanZiOcrType.火山多语种;
            MaxExecPerTime = 28;
            LstJsonPreProcessArray = new List<object>() { "data", "ocr_infos" };

            IsSupportVertical = true;
            StrResultJsonSpilt = "text";
            LstVerticalLocation = new List<object>() { "rect" };
        }

        protected override string GetHtml(OcrContent content)
        {
            var strPost = "{\"Action\":\"MultiLanguageOCR\",\"ServiceName\":\"cv\",\"Version\":\"2022-08-31\",\"image_base64\":\"" + content.strBase64 + "\",\"mode\":\"default\"}";
            var html = WebClientSyncExt.GetHtml("https://www.volcengine.com/proxy_ai_demo/invoke/proxy", strPost, ExecTimeOutSeconds);
            return html;
        }
    }
}