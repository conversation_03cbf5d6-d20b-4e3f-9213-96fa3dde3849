﻿using CommonLib;
using System.Collections.Generic;

namespace DocOcr
{
    /// <summary>
    /// 火山PDF
    /// https://www.volcengine.com/product/ocr-file
    /// </summary>
    public class HuoShanPDFRec : BaseDocOcrRec
    {
        public HuoShanPDFRec()
        {
            OcrGroup = OcrGroupType.字节;
            OcrType = DocOcrType.火山PDF;
            ResultType = ResutypeEnum.网页;
            MaxExecPerTime = 20;
            //文件上限2M
            FileSizeLimit = 1024 * 1024 * 10;
            AllowUploadFileTypes = new List<string>() { "pdf" };

            LstJsonPreProcessArray = new List<object>() { "data", "page_infos" };
            LstJsonNextProcessArray = new List<object>() { "text" };
            IsJsonArrayString = true;
        }

        protected override string GetHtml(OcrContent content)
        {
            var strPost = "{\"Action\":\"OCRPdf\",\"ServiceName\":\"cv\",\"Version\":\"2021-08-23\",\"image_base64\":\"" + content.strBase64 + "\"}";
            var html = WebClientSyncExt.GetHtml("https://www.volcengine.com/proxy_ai_demo/invoke/proxy", strPost, ExecTimeOutSeconds);
            return html;
        }

    }
}