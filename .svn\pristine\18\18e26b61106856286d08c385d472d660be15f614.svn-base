<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Default"
         xmlns='http://schemas.microsoft.com/developer/msbuild/2003' ToolsVersion="4.0">

  <PropertyGroup>
    <BuildSolutionDir>$(MSBuildProjectDirectory)/..</BuildSolutionDir>
    <SrcDir>$(BuildSolutionDir)/src</SrcDir>
    <TestsDir>$(BuildSolutionDir)/tests</TestsDir>
    <Configuration Condition="$(Configuration) == ''">Release</Configuration>
    <NuGetPackageDir>$(BuildSolutionDir)/NuGet/</NuGetPackageDir>
  </PropertyGroup>

  <PropertyGroup>
    <DoBuildSolutionsDependsOn>
      BeforeBuildSolutions;
      BuildSolutions
    </DoBuildSolutionsDependsOn>
  </PropertyGroup>

  <Target Name="BeforeBuildSolutions">
    <Message Text="*****Before building solution*****" Importance="high"/>
  </Target>

  <Target Name="BuildSolutions">
    <MSBuild Projects="$(BuildSolutionDir)/src/ServiceStack.Redis.sln" Targets="Restore" />
    <MSBuild Projects="$(BuildSolutionDir)/src/ServiceStack.Redis.sln" Targets="Build"
             Properties="Configuration=$(Configuration)" />
  </Target>

  <Target Name="Default" DependsOnTargets="$(DoBuildSolutionsDependsOn)">

    <!-- ServiceStack.Redis -->
    <MSBuild Projects="$(BuildSolutionDir)/src/ServiceStack.Redis/ServiceStack.Redis.csproj"
             Targets="Build;Pack"
             Properties="Configuration=$(Configuration)" />


    <!-- Copy all *.nupkg to /NuGet -->
    <ItemGroup>
      <NugetPackagesToMove Include="$(BuildSolutionDir)/src/**/bin/$(Configuration)/*.nupkg"/>
    </ItemGroup>
    <Move SourceFiles="@(NugetPackagesToMove)" DestinationFolder="$(NuGetPackageDir)" />

  </Target>
</Project>