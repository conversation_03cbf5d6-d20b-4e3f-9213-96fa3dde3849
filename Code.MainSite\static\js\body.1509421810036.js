$(function() {
	if(!jQuery.easing.def) {
		jQuery.easing.def = jQuery.easing._default;
	}
	var mouse_over = false,
		$menu_li = $('.menu .has_menu'),
		menu_lock = false;
	$menu_li.mouseover(function() {
		if(mouse_over) {
			$menu_li.find("ul").hide();
			$(this).find('ul').show();
		} else {
			if(menu_lock) return;
			menu_lock = true;
			$(this).find('ul').slideDown({
				complete : function() {
					menu_lock = false;
				}
			});
		}
		$(this).find('em').addClass('hover');
	}).mouseleave(function() {
		$(this).find('em').removeClass('hover');

		$('.programme_submenu dd').removeClass('hover');
		$('.programme_bd .menu_tab').hide();
		$('.programme_bd .menu_tab').eq(5).show();

		$('.ocrsubmn dd').removeClass('hover');
		$('.ocrsubmn_bd .menu_tab').hide();
		$('.ocrsubmn_bd .menu_tab').eq(0).show();

    $('.product_submenu dd').removeClass('hover');
		$('.product_bd .menu_tab').hide();
		$('.product_bd .menu_tab').eq(0).show();

	});

	$('.menu').mouseover(function(e) {
		var on_menu = $(e.target).hasClass('has_menu') || $(e.target).parents('li').hasClass('has_menu');
		if(on_menu == false && mouse_over == true) {
			if(menu_lock) return;
			menu_lock = true;
			$menu_li.find("ul").slideUp(200,function(){
				menu_lock = false;
			});
		}
		mouse_over = on_menu;
	}).mouseleave(function() {
		mouse_over = false;
		if(menu_lock) return;
		menu_lock = true;
		$menu_li.find("ul").slideUp(200,function(){
			menu_lock = false;
		});
	});

	$('.menu .menu_close').click(function() {
		menu_lock = true;
		$(this).parents(".submenu").slideUp(200,function(){
			menu_lock = false;
		});
	});

	$('.ocrsubmn dd').mouseover(function() {
		var index = $(this).index();
			$('.ocrsubmn dd').removeClass('hover');
			$('.ocrsubmn_bd .menu_tab').hide();
			$('.ocrsubmn_bd .menu_tab').eq(index - 1).show();
			$(this).addClass('hover');
	});

	$('.programme_submenu dd').mouseover(function() {
			var index = $(this).index();
			$('.programme_submenu dd').removeClass('hover');
			$('.programme_bd .menu_tab').hide();
			$('.programme_bd .menu_tab').eq(index - 1).show();
			$(this).addClass('hover');
	});

	$('.product_submenu dd').mouseover(function() {
			var index = $(this).index();
			$('.product_submenu dd').removeClass('hover');
			$('.product_bd .menu_tab').hide();
			$('.product_bd .menu_tab').eq(index - 1).show();
			$(this).addClass('hover');
	});


	$('.gundong').mouseover(function() {
		$(this).find('.next,.prev').show();
	}).mouseout(function() {
		$(this).find(".next,.prev").hide();
	});
	
	
	$('.kkr').click(function() {
		if ($(".kkr").hasClass("up")) {
			$($('#fullContent').show());
			$($(this).removeClass("up").addClass("down").html('收起'));
		} else {
			$($('#fullContent').hide());
			$($(this).addClass("up")).removeClass("down").html('浏览更多');
		}
	});
	$(function() {
		$('.a_anli,.a_gongneng,.a_fangan,.a_ccc,.a_d,.a_e,.a_f,.a_g').click(function() {
			var cl = $(this).attr('class');
			$('html,body').animate({
				scrollTop: $('.' + cl + '_content').offset().top-66
			}, 500);
		});
	});
	$('.erwei').click(function() {
		if ($(this).hasClass("lll")) {
			$($(".erweima").show());
			$($(this).removeClass("lll"));
		} else {
			$($(".erweima").hide());
			$($(this).addClass("lll"));

		}
	});
	$('.erweix').click(function() {
		if ($(this).hasClass("lll")) {
			$($(".erweimax").show());
			$($(this).removeClass("lll"));
		} else {
			$($(".erweimax").hide());
			$($(this).addClass("lll"));

		}
	});
	$('.erwm').click(function() {
		if ($(this).hasClass("lll")) {
			$($(".erwm_a").show());
			$($(this).removeClass("lll"));
		} else {
			$($(".erwm_a").hide());
			$($(this).addClass("lll"));

		}
	});

	$('.fixed-recruit .recruit-btn').click(function() {
		$('.fixed-recruit').toggleClass('closed');
	});

	//$('#lang').bind('click', function() {
	//	var exdate = new Date();
	//	exdate.setDate(exdate.getDate()+ 1);
	//	document.cookie = "lang=en;expires=" + exdate.toGMTString() + ";domain=.ccint.com";
	//});
});