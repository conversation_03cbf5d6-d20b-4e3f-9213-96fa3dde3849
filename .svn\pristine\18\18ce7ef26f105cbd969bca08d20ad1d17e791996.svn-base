﻿using CommonLib;
using System;
using System.Collections.Generic;
using System.Collections.Specialized;
using System.IO;

namespace HanZiOcr
{
    /// <summary>
    /// 讯飞Lite-AI体验
    /// </summary>
    public class XunFeiPageRec : BaseOcrRec
    {
        public XunFeiPageRec()
        {
            OcrGroup = OcrGroupType.讯飞;
            OcrType = HanZiOcrType.讯飞文档;
            MaxExecPerTime = 18;

            LstJsonPreProcessArray = new List<object>() { "data", "pages" };
            LstJsonNextProcessArray = new List<object>() { "lines", "words" };
            StrResultJsonSpilt = "content";
            IsSupportVertical = true;
            LstVerticalLocation = new List<object>() { "coord" };
        }

        protected override void SetProcessArray(string html)
        {
            var value = OcrHtmlProcess.ProcessJsonResult(html, new List<object>() { "data" });
            var isEnglish = !string.IsNullOrEmpty(value) && Equals(CommonLib.OcrProcessor.OcrUtils.GetLang(value), "en");
            StrContactCell = isEnglish ? " " : "";
        }

        protected override string GetHtml(OcrContent content)
        {
            var byt = Convert.FromBase64String(content.strBase64);
            var result = PostFileResult(byt);//.Replace("\\\"", "\"").Replace("\\\\", "\\").Replace("\\r", "\r").Replace("\\n", "\n")
            //"{\"code\":0,\"msg\":\"成功\",\"data\":\"[{\\\"confidence\\\":1,\\\"word\\\":[{\\\"content\\\":\\\"请点击下图中所有的靴子\\\"},{\\\"content\\\":\\\"火柴\\\"}]}]\",\"content\":null,\"audioPath\":null}"
            return result;
        }

        private string PostFileResult(byte[] content)
        {
            var result = "";
            try
            {
                var url = "https://aistack.openspeech.cn/aistack/vision/ocr_parse";
                var file = new UploadFileInfo()
                {
                    Name = "file",
                    Filename = "test.png",
                    ContentType = "image/png",
                    Stream = new MemoryStream(content)
                };
                var values = new NameValueCollection { { "language", "cn" } };
                var headers = new NameValueCollection
                {
                    {"referer", "https://servicewechat.com/wxa7f07807b0f7f668/30/page-frame.html"}
                };
                result = PostFile(url, new[] { file }, values, headers);
            }
            catch (Exception)
            {

            }
            if (result.Contains(",\"tables\":["))
            {
                LstJsonNextProcessArray = new List<object>() { "tables", "cells", "lines", "words" };
            }
            else
            {
                LstJsonNextProcessArray = new List<object>() { "lines", "words" };
            }
            return result;
        }

    }
}