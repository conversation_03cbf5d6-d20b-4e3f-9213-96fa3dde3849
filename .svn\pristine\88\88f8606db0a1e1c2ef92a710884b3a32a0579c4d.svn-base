// Code generated by Microsoft (R) AutoRest Code Generator 1.0.1.0
// Changes may cause incorrect behavior and will be lost if the code is
// regenerated.

namespace AutorestClient.Models
{
    using Newtonsoft.Json;
    using System.Linq;

    /// <summary>
    /// UnAuthInfo
    /// </summary>
    /// <remarks>
    /// UnAuthInfo
    /// </remarks>
    public partial class UnAuthInfo
    {
        /// <summary>
        /// Initializes a new instance of the UnAuthInfo class.
        /// </summary>
        public UnAuthInfo()
        {
          CustomInit();
        }

        /// <summary>
        /// Initializes a new instance of the UnAuthInfo class.
        /// </summary>
        public UnAuthInfo(string customInfo = default(string))
        {
            CustomInfo = customInfo;
            CustomInit();
        }

        /// <summary>
        /// An initialization method that performs custom operations like setting defaults
        /// </summary>
        partial void CustomInit();

        /// <summary>
        /// </summary>
        [JsonProperty(PropertyName = "CustomInfo")]
        public string CustomInfo { get; set; }

    }
}
