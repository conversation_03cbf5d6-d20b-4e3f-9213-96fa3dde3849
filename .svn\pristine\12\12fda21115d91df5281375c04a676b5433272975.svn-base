﻿using CommonLib;
using System;
using System.Collections.Generic;
using System.Collections.Specialized;
using System.IO;
using System.Web;

namespace HanZiOcr
{
    /// <summary>
    /// https://cloud.baidu.com/doc/OCR/s/zjwvxzrw8/
    /// </summary>
    public class BitMainRec : BaseOcrRec
    {
        public BitMainRec()
        {
            OcrType = HanZiOcrType.比特大陆;

            LstJsonPreProcessArray = new List<object>() { "words_result" };
            StrResultJsonSpilt = "words";
            MaxExecPerTime = 15;
            IsSupportVertical = true;
            State = EnableState.禁用;
        }

        private const string clientId = "<EMAIL>";
        private const string clientSecret = "cw19860519";

        private const string strTokenSpilt = "\"token\":\"";

        private string strToken = "";

        private void InitToken()
        {
            if (string.IsNullOrEmpty(strToken))
            {
                strToken = GetToken();
            }
        }
        private string GetToken()
        {
            var token = WebClientSyncExt.GetHtml(string.Format("{0}?{1}", "https://aicloud.bitmain.com.cn:8443/auth/local", "email=" + clientId + "&password=" + clientSecret), ExecTimeOutSeconds);

            string result;
            if (!string.IsNullOrEmpty(token) && token.Contains(strTokenSpilt))
            {
                result = token.Substring(token.IndexOf(strTokenSpilt) + strTokenSpilt.Length);
                result = result.Substring(0, result.IndexOf("\""));
            }
            else
            {
                result = "4ec44e4a3ec2353586edb3b7acc1baf56a1666aa";
            }
            return result;
        }

        public override void Reset()
        {
            strToken = "";
            base.Reset();
        }

        protected override string GetHtml(OcrContent content)
        {
            return RequestHtmlContent(content.strBase64);
        }

        private string RequestHtmlContent(string strBase64)
        {
            InitToken();
            var result = "";
            if (!string.IsNullOrEmpty(strToken))
            {
                var url = "https://aicloud.bitmain.com.cn:8443/ocr/v1/general";
                var strPost = string.Format("image_base64={0}", HttpUtility.UrlEncode(strBase64));
                NameValueCollection heads = new NameValueCollection()
                {
                    { "Authorization","Bearer " + strToken}
                };
                result = WebClientSyncExt.GetHtml(url, "", strPost, url, ExecTimeOutSeconds, heads);
            }
            return result;
        }
    }
}