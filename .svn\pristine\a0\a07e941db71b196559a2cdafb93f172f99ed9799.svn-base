﻿using CommonLib;
using System;
using System.Collections.Generic;

namespace MathOcr
{
    /// <summary>
    /// https://ai.100tal.com/product/ocr-gs
    /// </summary>
    public class XueErSiGongShiRec : BaseMathRec
    {
        public XueErSiGongShiRec()
        {
            OcrGroup = OcrGroupType.学而思;
            OcrType = MathOcrType.学而思公式;
            MaxExecPerTime = 25;

            IsSupportUrlOcr = true;

            LstJsonPreProcessArray = new List<object>() { "data" };
            IsSupportVertical = false;

            StrResultJsonSpilt = "formula_result";
        }

        protected override string GetHtml(OcrContent content)
        {
            return processByContent(content.strBase64, null);
        }

        public override string GetHtmlByUrl(OcrContent content)
        {
            return processByContent(null, content.url);
        }

        private string processByContent(string strBase64, string imgUrl)
        {
            var strPost = "{\"method\":\"/aiimage/common-formula-reg\""
                            + ",\"image_base64\":" + (string.IsNullOrEmpty(strBase64) ? "null" : "[\"" + strBase64 + "\"]")
                            + ",\"reg_flag\":0"
                            + ",\"type\":0"
                            + ",\"image_url\":" + (string.IsNullOrEmpty(imgUrl) ? "null" : "[\"" + imgUrl + "\"]")
                            + "}";
            var result = WebClientSyncExt.GetHtml("https://ai.100tal.com/openapi/try/service", strPost, ExecTimeOutSeconds);
            //"{\"code\":0,\"msg\":\"成功\",\"data\":\"[{\\\"confidence\\\":1,\\\"word\\\":[{\\\"content\\\":\\\"请点击下图中所有的靴子\\\"},{\\\"content\\\":\\\"火柴\\\"}]}]\",\"content\":null,\"audioPath\":null}"
            return result;
        }

        /*
            "rectangle": [
              22,
              225,
              121,
              239
            ]
         */
        protected override LocationInfo GetLocationByStr(string locationInfoStr)
        {
            locationInfoStr = locationInfoStr?
                      .Replace("[", "").Replace("]", "")
                      .Replace("{", "").Replace("}", "")
                      .Replace("\"", "").Replace(":", "")
                      .Replace("\r", "").Replace("\t", "").Replace("\n", "")
                      .Replace(" ", "").Trim();
            var spilt = locationInfoStr.Split(new string[] { "," }, StringSplitOptions.RemoveEmptyEntries);

            var location = new LocationInfo()
            {
                left = BoxUtil.GetInt32FromObject(spilt[0]),
                top = BoxUtil.GetInt32FromObject(spilt[1]),
                width = BoxUtil.GetInt32FromObject(spilt[2]) - BoxUtil.GetInt32FromObject(spilt[0]),
                height = BoxUtil.GetInt32FromObject(spilt[3]) - BoxUtil.GetInt32FromObject(spilt[1]),
            };

            return location;
        }

    }
}