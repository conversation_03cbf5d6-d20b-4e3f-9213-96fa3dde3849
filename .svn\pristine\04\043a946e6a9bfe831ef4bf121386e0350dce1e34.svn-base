﻿using CommonLib;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Security.Cryptography;
using System.Text;

namespace MathOcr
{
    /// <summary>
    /// 阿里读光Demo
    /// https://duguang.aliyun.com/experience
    /// </summary>
    public class DuGuangRec : BaseMathRec
    {
        public DuGuangRec()
        {
            OcrGroup = OcrGroupType.阿里;
            OcrType = MathOcrType.阿里读光;
            MaxExecPerTime = 25;
            LstJsonPreProcessArray = new List<object>() { "data", "content" };
            IsProcessJsonResultByArray = false;

            IsSupportUrlOcr = true;
        }

        private const string strConst = "duguang.aliyun.com";

        protected override string GetHtml(OcrContent content)
        {
            var ocrType = "demo_math_rec";
            var strBase64 = "data:image/png;base64," + content.strBase64;
            var sign = Sign(strBase64, ocrType);
            var strTmp = WebClientSyncExt.GetHtml("https://duguang.aliyun.com/ocrdemo/ocrDemoSecondService.json", ""
                , string.Format("img={0}&type={1}&sign={2}"
                , System.Web.HttpUtility.UrlEncode(strBase64)
                , ocrType
                , sign
                ), "https://duguang.aliyun.com/experience", ExecTimeOutSeconds);

            return strTmp;
        }

        public override string GetHtmlByUrl(OcrContent content)
        {
            var ocrType = "demo_math_rec";
            var sign = Sign(content.url, ocrType, true);
            var strTmp = WebClientSyncExt.GetHtml("https://duguang.aliyun.com/ocrdemo/ocrDemoSecondService.json", ""
                , string.Format("url={0}&type={1}&sign={2}"
                , System.Web.HttpUtility.UrlEncode(content.url)
                , ocrType
                , sign
                ), "https://duguang.aliyun.com/experience", ExecTimeOutSeconds);

            return strTmp;
        }

        /// <summary>
        /// 签名
        /// </summary>
        /// <param name="pairs">参数字典</param>
        /// <param name="appkey">APPKEY</param>
        /// <param name="charset">编码格式</param>
        /// <returns></returns>
        string Sign(string content, string ocrType, bool isUrl = false)
        {
            var pair = string.Format("{0}{1}{2}", isUrl ? content : content.Length.ToString(), strConst, ocrType);

            var sign = MD5(pair).ToLower();

            return sign;
        }

        /// <summary>
        /// MD5加密（小写）
        /// </summary>
        /// <param name="s">字符串</param>
        /// <param name="len">长度</param>
        /// <returns></returns>
        string MD5(string s, int len = 32)
        {
            var md5Hasher = new MD5CryptoServiceProvider();
            byte[] data = md5Hasher.ComputeHash(Encoding.Default.GetBytes(s));
            StringBuilder sb = new StringBuilder();
            for (int i = 0; i < data.Length; i++)
            {
                sb.Append(data[i].ToString("x2"));
            }
            string result = sb.ToString();
            return len == 32 ? result : result.Substring(8, 16);
        }
    }
}