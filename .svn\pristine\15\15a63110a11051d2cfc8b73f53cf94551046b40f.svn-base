﻿
using System;
using CommonLib;

namespace MathOcr
{
    /// <summary>
    /// https://cloud.baidu.com/doc/OCR/OCR-Android-SDK/18.5COCR-UI.E6.A8.A1.E5.9D.97.html#.E9.80.9A.E7.94.A8.E6.96.87.E5.AD.97.E8.AF.86.E5.88.AB
    /// </summary>
    public class BaiDuCloudAIRec : BaseMathRec
    {
        public BaiDuCloudAIRec()
        {
            OcrGroup = OcrGroupType.百度;
            OcrType = MathOcrType.百度云;
            MaxExecPerTime = 10;

            LstJsonPreProcessArray = new System.Collections.Generic.List<object>() { "data", "words_result" };
            StrResultJsonSpilt = "words";
            IsSupportUrlOcr = true;
            //IsSupportVertical = true;
        }


        protected override string GetHtml(OcrContent content)
        {
            content.strBase64 = "data%3Aimage%2Fjpeg%3Bbase64%2C" + System.Web.HttpUtility.UrlEncode(content.strBase64);
            return RequestHtmlContent(content.strBase64);
        }

        public override string GetHtmlByUrl(OcrContent content)
        {
            return RequestHtmlContent(null, content.url);
        }

        private string RequestHtmlContent(string strBase64, string imgUrl = null)
        {
            EngineType = ConstHelper.lstBaiDuOcrTypeWithLocations.GetRndItem();

            var strPost = "type=" + EngineType.Value + "&image=" + strBase64 + "&image_url=" + System.Web.HttpUtility.UrlEncode(imgUrl) + "&language_type=CHN_ENG&detect_direction=true";

            var cookie = "BDAIUID=" + Guid.NewGuid().ToString().ToUpper().Replace("-", "")
                                    + ";BDAUIDD=" + Guid.NewGuid().ToString().ToUpper().Replace("-", "")
                                    + ";BAIDUID=" + Guid.NewGuid().ToString().ToUpper().Replace("-", "") + ":FG=1;max-age=31536000;version=1;";

            WebClientSyncExt.GetHtml("https://cloud.baidu.com/product/ocr/general", ref cookie);
            var strTmp = WebClientSyncExt.GetHtml("https://cloud.baidu.com/aidemo", ref cookie, strPost, "https://cloud.baidu.com/product/ocr/general", ExecTimeOutSeconds);

            if (strTmp.StartsWith("<html") || strTmp.StartsWith("<!DOCTYPE"))
            {
                strTmp = "";
            }

            return strTmp;
        }
    }
}