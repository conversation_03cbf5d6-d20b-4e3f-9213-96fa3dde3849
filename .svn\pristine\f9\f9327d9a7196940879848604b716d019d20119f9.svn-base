﻿using CommonLib;
using System;
using System.Collections.Generic;
using System.Collections.Specialized;
using System.IO;

namespace HanZiOcr
{
    /// <summary>
    /// 易道博识
    /// http://ai.exocr.com/v1_general.html?temp_code=108
    /// </summary>
    public class YiDaoRec : BaseOcrRec
    {
        public YiDaoRec()
        {
            OcrType = HanZiOcrType.易道博识;

            MaxExecPerTime = 22;

            LstJsonPreProcessArray = new List<object>() { "result" };
            IsSupportVertical = true;
            IsSupportUrlOcr = true;
            DicDeserializeVerticalJson = new Dictionary<string, string> { { "location", "position" } };
        }

        protected override string GetHtml(OcrContent content)
        {
            var result = PostFileResult(content.strBase64, null);
            return result;
        }

        public override string GetHtmlByUrl(OcrContent content)
        {
            return PostFileResult(null, content.url);
        }

        private string PostFileResult(string strBase64, string imgUrl)
        {
            var result = "";
            try
            {
                var url = "http://ai.exocr.com/apqi/api/reco/all";
                List<UploadFileInfo> lstFile = new List<UploadFileInfo>();
                if (string.IsNullOrEmpty(imgUrl))
                {
                    var file = new UploadFileInfo()
                    {
                        Name = "image_binary",
                        Filename = "1.jpg",
                        ContentType = "image/jpeg",
                        Stream = new MemoryStream(Convert.FromBase64String(strBase64))
                    };
                    lstFile.Add(file);
                }
                var values = new NameValueCollection() {
                    { "reco_id","108"},
                    { "image_url", imgUrl}
                };
                var header = new NameValueCollection() {
                    { "Refer","http://ai.exocr.com/ocr/"}
                };
                result = PostFile(url, lstFile, values, header);
                if (result.StartsWith("{\"code\":200,\"success\":true,\"data\""))
                {
                    LstJsonPreProcessArray = new List<object>() { "data", "result" };
                }
                else
                {
                    LstJsonPreProcessArray = new List<object>() { "result" };
                }
            }
            catch (Exception)
            {

            }
            return result;
        }

    }
}