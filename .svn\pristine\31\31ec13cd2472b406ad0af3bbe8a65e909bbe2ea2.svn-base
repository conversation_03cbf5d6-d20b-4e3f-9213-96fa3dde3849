﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;

namespace CommonLib.HttpCode
{
    public class HttpClientSync
    {
        private readonly HttpClient _httpClient;

        public string BASE_ADDRESS { get; set; }

        public HttpClientSync()
        {
            _httpClient = new HttpClient() { BaseAddress = new Uri(BASE_ADDRESS) };
            _httpClient.DefaultRequestHeaders.Connection.Add("keep-alive");

            //帮HttpClient热身
            _httpClient.SendAsync(new HttpRequestMessage
            {
                Method = new HttpMethod("HEAD"),
                RequestUri = new Uri(BASE_ADDRESS + "/")
            }).Result.EnsureSuccessStatusCode();
        }

        public async Task<string> GetAsync(string url)
        {
            var response = await _httpClient.GetAsync(url);

            return await response.Content.ReadAsStringAsync();
        }

        public async Task<string> PostAsync(string url, List<KeyValuePair<string, string>> parameters)
        {
            var response = await _httpClient.PostAsync("/", new FormUrlEncodedContent(parameters));

            return await response.Content.ReadAsStringAsync();
        }
    }
}
