﻿using CommonLib;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Web.Script.Serialization;

namespace HanZiOcr
{
    public class ConstHelper
    {

        public static JavaScriptSerializer JavaScriptSerializer = new JavaScriptSerializer();

        public static Dictionary<string, string> lstBaiDuOcrAPIs = new Dictionary<string, string>();
        public static Dictionary<string, string> lstBaiDuOcrWithLocationAPIs = new Dictionary<string, string>();
        public static Dictionary<string, string> lstBaiDuLiteOcrTypeWithLocations = new Dictionary<string, string>();

        public static Dictionary<string, string> lstXueEiSiOcrWithLocationAPIs = new Dictionary<string, string>();

        static ConstHelper()
        {
            //https://ai.baidu.com/tech/ocr/webimage
            //type:webimage 网络图片文字识别

            //https://ai.baidu.com/tech/ocr/general
            //type:commontext 通用文字识别
            //type:https://aip.baidubce.com/rest/2.0/ocr/v1/accurate_basic 通用文字识别（高精度版）
            //type:general_location 通用文字识别（含位置信息版）
            //type:https://aip.baidubce.com/rest/2.0/ocr/v1/accurate 通用文字识别（高精度含位置版）

            //https://ai.baidu.com/tech/ocr_others/handwriting
            //type:https://aip.baidubce.com/rest/2.0/ocr/v1/handwriting 通用文字识别（手写体识别）

            //type:webimage 网络图片文字识别
            //type:commontext 通用文字识别
            //type:general_location 通用文字识别（含位置信息版）
            //type:https%3A%2F%2Faip.baidubce.com%2Frest%2F2.0%2Focr%2Fv1%2Faccurate_basic 通用文字识别（高精度版）
            //type:https%3A%2F%2Faip.baidubce.com%2Frest%2F2.0%2Focr%2Fv1%2Faccurate 通用文字识别（高精度含位置版）
            //type:https%3A%2F%2Faip.baidubce.com%2Frest%2F2.0%2Focr%2Fv1%2Fhandwriting 通用文字识别（手写体识别）

            lstBaiDuLiteOcrTypeWithLocations.Add("含位置信息版", "https://ai.baidu.com/weapp/rest/2.0/ocr/v1/general");
            lstBaiDuLiteOcrTypeWithLocations.Add("高精度含位置版", "https://ai.baidu.com/weapp/rest/2.0/ocr/v1/accurate");
            lstBaiDuLiteOcrTypeWithLocations.Add("手写体识别", "https://ai.baidu.com/weapp/rest/2.0/ocr/v1/handwriting");

            lstBaiDuOcrAPIs.Add("网络图片文字识别", "https://aip.baidubce.com/rest/2.0/ocr/v1/webimage");
            lstBaiDuOcrAPIs.Add("通用文字识别", "https://aip.baidubce.com/rest/2.0/ocr/v1/general_basic");
            lstBaiDuOcrAPIs.Add("含位置信息版", "https://aip.baidubce.com/rest/2.0/ocr/v1/general");
            lstBaiDuOcrAPIs.Add("高精度版", "https://aip.baidubce.com/rest/2.0/ocr/v1/accurate_basic");
            lstBaiDuOcrAPIs.Add("高精度含位置版", "https://aip.baidubce.com/rest/2.0/ocr/v1/accurate");
            lstBaiDuOcrAPIs.Add("手写体识别", "https://aip.baidubce.com/rest/2.0/ocr/v1/handwriting");
            lstBaiDuOcrAPIs.Add("含生僻字版", "https://aip.baidubce.com/rest/2.0/ocr/v1/general_enhanced");

            lstBaiDuOcrWithLocationAPIs.Add("含位置信息版", "https://aip.baidubce.com/rest/2.0/ocr/v1/general");
            lstBaiDuOcrWithLocationAPIs.Add("高精度含位置版", "https://aip.baidubce.com/rest/2.0/ocr/v1/accurate");
            lstBaiDuOcrWithLocationAPIs.Add("手写体识别", "https://aip.baidubce.com/rest/2.0/ocr/v1/handwriting");

            lstXueEiSiOcrWithLocationAPIs.Add("文字识别", "http://openapiai.xueersi.com/v1/api/img/ocr/general");
            lstXueEiSiOcrWithLocationAPIs.Add("混合通用", "http://openapiai.xueersi.com/v1/api/img/ocr/universe");
        }


        public static void Init()
        {
            InitRecTypes();
        }

        static void InitRecTypes()
        {
            var dicHanZiOcrs = new Dictionary<int, BaseRec>();
            var dicVerticalOcrs = new Dictionary<int, BaseRec>();
            var dicTranslateOcrs = new Dictionary<int, BaseRec>();
            var allOCRS = Assembly.GetExecutingAssembly().GetTypes()
                .Where(t => !t.IsAbstract && t.BaseType.Equals(typeof(BaseOcrRec))).ToList();

            if (allOCRS != null && allOCRS.Any())
            {
                allOCRS.ForEach(p =>
                {
                    Type objtype = Type.GetType(p.FullName, true);
                    var obj = Activator.CreateInstance(objtype) as BaseOcrRec;
                    if (obj != null)
                    {
                        dicHanZiOcrs.Add(obj.OcrType.GetHashCode(), obj);
                        if (obj.IsSupportVertical)
                        {
                            dicVerticalOcrs.Add(obj.OcrType.GetHashCode(), obj);
                        }
                        if (obj.IsSupportTrans)
                        {
                            dicTranslateOcrs.Add(obj.OcrType.GetHashCode(), obj);
                        }
                    }
                });
            }
            BaseRecHelper.InitOcrTypes(OcrType.文本, dicHanZiOcrs);
            BaseRecHelper.InitOcrTypes(OcrType.竖排, dicVerticalOcrs);
            BaseRecHelper.InitOcrTypes(OcrType.翻译, dicTranslateOcrs);
        }
    }
}
