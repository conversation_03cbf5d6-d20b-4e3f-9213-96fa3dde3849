﻿using NewTicket.Common;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace NewTicket
{
    public class QQHelper
    {
        static QQHelper()
        {
            //Task.Factory.StartNew(() =>
            //{
            //    while (!CommonString.isExit)
            //    {
            //        try
            //        {
            //            lstTmp = new List<QQChatWindows>();
            //            NativeMethods.EnumDesktopWindows(IntPtr.Zero, (wnd, u) => GetAllWinodw(wnd, u), IntPtr.Zero);
            //        }
            //        catch { }
            //        System.Threading.Thread.Sleep(5000);
            //    }
            //});
        }

        private static List<QQChatWindows> lstTmp = new List<QQChatWindows>();

        public static List<QQChatWindows> LstTmp
        {
            get
            {
                if (lstTmp == null || lstTmp.Count <= 0)
                {
                    try
                    {
                        lstTmp = new List<QQChatWindows>();
                        NativeMethods.EnumDesktopWindows(IntPtr.Zero, (wnd, u) => GetAllWinodw(wnd, u), IntPtr.Zero);
                    }
                    catch
                    {
                    }
                }
                return lstTmp;
            }
            set { lstTmp = value; }
        }

        public static bool SendMsg(string strMsg, string strWindow)
        {
            var result = false;
            try
            {
                LstTmp = new List<QQChatWindows>();
                var intPtr = IntPtr.Zero;
                if (!LstTmp.Exists(p => p.Caption.Contains(strWindow)))
                {
                    if (LstTmp.Exists(p => p.Caption.Contains("选择号码")))
                    {
                        MessageBox.Show("当前窗口可能登陆了多个QQ，请选择要发送消息的QQ号码！", "发送QQ消息", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                    else
                    {
                        QQUtil.QQIvoke(strWindow);
                    }
                    LstTmp = new List<QQChatWindows>();
                }
                if (LstTmp.Exists(p => p.Caption.Contains(strWindow)))
                {
                    intPtr = LstTmp.Find(p => p.Caption.Contains(strWindow)).WindowHwnd;
                    if (intPtr == IntPtr.Zero || !NativeMethods.IsWindow(intPtr))
                    {
                        intPtr = IntPtr.Zero;
                    }
                }
                if (intPtr != IntPtr.Zero)
                {
                    if (SendMsg(intPtr, strWindow, strMsg))
                    {
                        result = true;
                    }
                }
            }
            catch
            {
            }
            return result;
        }

        private static bool SendMsg(IntPtr intptr_0, string string_8, string string_9)
        {
            NativeMethods.ShowWindow(intptr_0, NativeMethods.ShowWindowCommands.Normal);
            //NativeMethods.BringWindowToTop(intptr_0);
            SendQQMessage(intptr_0, string_9);
            //NativeMethods.ShowWindow(intptr_0, NativeMethods.ShowWindowCommands.Normal);
            //if (NativeMethods.BringWindowToTop(intptr_0))
            //{
            //    SendKeys.SendWait("^ +");
            //    SendKeys.Send(string_9);
            //    SendKeys.SendWait("{ENTER}");
            //    //SendKeys.SendWait("^{ENTER}");
            //    return true;
            //}
            return true;
        }


        public const int WM_CHAR = 258;
        public const int WM_KEYDOWN = 256;
        public const int WM_KEYUP = 257;
        public const int NULL = 0;
        public const int VK_ENTER = 13;
        private static void SendQQMessage(IntPtr hWnd, string value)
        {
            byte[] byGbkValue = Encoding.Default.GetBytes(value);
            foreach (byte byGbkNum in byGbkValue)
            {
                NativeMethods.PostMessage(hWnd, WM_CHAR, byGbkNum, NULL);
            }
            NativeMethods.PostMessage(hWnd, WM_KEYDOWN, VK_ENTER, NULL);
            NativeMethods.PostMessage(hWnd, WM_KEYUP, VK_ENTER, NULL);
        }

        private static bool GetAllWinodw(IntPtr intptr_0, uint uint_0)
        {
            var stringBuilder = new StringBuilder(256);
            NativeMethods.GetClassName(intptr_0, stringBuilder, stringBuilder.Capacity);
            if (stringBuilder.ToString().Equals("TXGuiFoundation"))
            {
                var processName = GetProcessName(intptr_0);
                if (!string.IsNullOrEmpty(processName))
                {
                    if ((processName.Contains("Timwp") || processName.Equals("QQ")))
                    {
                        var stringBuilder2 = new StringBuilder(NativeMethods.GetWindowTextLength(intptr_0) + 1);
                        NativeMethods.GetWindowText(intptr_0, stringBuilder2, stringBuilder2.Capacity);
                        if (!stringBuilder2.ToString().Equals(string.Empty) &&
                            !stringBuilder2.ToString().Equals("TXFloatingWnd") &&
                            !stringBuilder2.ToString().Equals("TXMenuWindow"))
                        {
                            if (!lstTmp.Exists(p => p.Caption.Equals(stringBuilder2.ToString()) && p.WindowHwnd.Equals(intptr_0)))
                            {
                                var item = new QQChatWindows(intptr_0, stringBuilder2.ToString());
                                lstTmp.Add(item);
                            }
                        }
                    }
                }
            }
            return true;
        }

        private static string GetProcessName(IntPtr hWnd)
        {
            var result = string.Empty;
            var num = 0;
            var windowThreadProcessId = NativeMethods.GetWindowThreadProcessId(hWnd, out num);
            if (windowThreadProcessId > 0u && num > 0)
            {
                try
                {
                    var processById = Process.GetProcessById(num);
                    result = processById.ProcessName;
                }
                catch
                {
                }
            }
            return result;
        }
    }

    public class QQChatWindows
    {
        public QQChatWindows(IntPtr windowhwnd, string caption)
        {
            WindowHwnd = IntPtr.Zero;
            Caption = string.Empty;
            WindowHwnd = windowhwnd;
            Caption = caption;
        }

        public IntPtr WindowHwnd { get; set; }

        public string Caption { get; set; }
    }
}