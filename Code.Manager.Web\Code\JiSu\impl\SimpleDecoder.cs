﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Net.Sockets;
using System.Net;
using System.Threading;
using System.Collections.Concurrent;
using monocaptcha;

namespace monocaptcha.impl
{
    class SimpleDecoder : ICaptchaDecoder
    {
        public bool IsUsed { get; set; }
        private const int tokenBtsMaxLen = 40;

        private Socket clientSocket = null;

        public SimpleDecoder()
        {
            reconnect();
        }
        private void reconnect()
        {
            try
            {
                if (clientSocket != null)
                {
                    clientSocket.Dispose();
                    clientSocket.Close(1);
                }
                clientSocket = new Socket(AddressFamily.InterNetwork, SocketType.Stream, ProtocolType.Tcp);
                clientSocket.Connect(Util.getRemoteAddress());
            }
            catch (Exception e)
            {
                Console.WriteLine(e.Message);
            }
        }
        public void decode(string token, IAnswerResult[] answers)
        {
            if (clientSocket == null)
            {
                throw new ApplicationException("Socket Client 建立失败");
            }

            if (answers == null || answers.Length == 0)
            {
                return;
            }
            if (!clientSocket.Connected)
            {
                Console.WriteLine("上一次链接失败");
            }
            foreach (IAnswerResult item in answers)
            {
                if (item.getPictureData() == null)
                {
                    Console.WriteLine("item.getpicturedata == null");
                    item.handle(ResponseEnum.FORMAT_ERROR, "picture data equals null");
                    continue;
                }
                try
                {
                    byte[] tokenBts = Encoding.ASCII.GetBytes(token);
                    if (tokenBts.Length > tokenBtsMaxLen)
                    {
                        item.handle(ResponseEnum.UNKNOWN_TOKEN, "token too long");
                        continue;
                    }
                    byte[] targetBts = new byte[tokenBtsMaxLen + item.getPictureData().Length];
                    Array.Copy(tokenBts, 0, targetBts, 0, tokenBts.Length);
                    Array.Copy(item.getPictureData(), 0, targetBts, tokenBtsMaxLen, item.getPictureData().Length);
                    byte[] send = Util.wrapPictureBuffer(targetBts);

                    clientSocket.Send(send);
                    byte[] bts = new byte[1000];
                    int reveiceLen = clientSocket.Receive(bts);
                    string res = Encoding.ASCII.GetString(bts, 0, reveiceLen);
                    Console.WriteLine(res);
                    if (res != null)
                    {
                        string[] rets = res.Split(new char[] { ':' }, System.StringSplitOptions.RemoveEmptyEntries);
                        if (rets.Length < 2)
                        {
                            item.handle(ResponseEnum.FORMAT_ERROR, "return value.Split(':').Length < 2 , format error ");
                            continue;
                        }
                        ResponseEnum status = ResponseEnum.OK;
                        string ret = null;
                        try
                        {
                            status = (ResponseEnum)Enum.Parse(typeof(ResponseEnum), rets[0], true);
                            ret = rets[1];
                        }
                        catch (ArgumentException ae)
                        {
                            Console.WriteLine(ae);
                            status = ResponseEnum.FORMAT_ERROR;
                            ret = "ArgumentException at SimpleDecoder";
                        }
                        catch (Exception e)
                        {
                            Console.WriteLine(e);
                            status = ResponseEnum.FORMAT_ERROR;
                            ret = "Exception at SimpleDecoder";
                        }
                        finally
                        {
                            item.handle(status, ret);
                        }
                    }
                    else
                    {
                        item.handle(ResponseEnum.SERVER_ERROR, "return value is null");
                    }
                }
                catch (SocketException se)
                {
                    item.handle(ResponseEnum.SERVER_ERROR, " unknow error:" + se.Message);
                    Console.WriteLine(se.Message);
                    continue;
                }
                catch (Exception e)
                {
                    item.handle(ResponseEnum.SERVER_ERROR, " unknow error:" + e.Message);
                    Console.WriteLine(e.StackTrace);
                    Console.WriteLine(e.Message);
                }
            }
        }

        public void Dispose()
        {
            if (clientSocket != null)
            {
                clientSocket.Dispose();
                clientSocket.Close();
            }
        }
    }
}