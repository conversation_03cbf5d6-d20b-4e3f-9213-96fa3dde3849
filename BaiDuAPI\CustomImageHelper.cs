﻿using System;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using CommonLib;
using HanZiOcr;
using ImageOCR;
using DaMaLib;

namespace Code.Client.Web
{
    public static class CustomImageHelper
    {
        //public static ConcurrentStack<CusImageEntity> LstImgPool = new ConcurrentStack<CusImageEntity>();

        //public static Thread ProcessOther = null;

        //private static long NowResCount;

        //public static void AddRecImg(string strImg, string strName)
        //{
        //    try
        //    {
        //        if (!string.IsNullOrEmpty(strImg) && !string.IsNullOrEmpty(strName))
        //        {
        //            var old = LstImgPool.FirstOrDefault(p => p.StrImg.Equals(strImg));
        //            if (old == null || string.IsNullOrEmpty(old.StrHanZi))
        //            {
        //                LstImgPool.Push(new CusImageEntity
        //                {
        //                    StrImg = strImg,
        //                    StrHanZi = strName,
        //                    StrIndex = RndCodeHelper.GetRndIndex(true, strName),
        //                    IsResult = true
        //                });
        //            }
        //        }
        //    }
        //    catch (Exception oe)
        //    {
        //        ConfigHelper._Log.Error("【图片池】接收图片出错！", oe);
        //    }
        //}

        public static string ReportToday()
        {
            var result = "";
            //try
            //{
            //    result = string.Format("【图片池】当前共{0}个图片待处理！{1}", RdsCacheHelper.CollectImageQueue.GetMessageCount(),
            //        Environment.NewLine);
            //    foreach (var site in Enum.GetNames(typeof(SiteFlag)))
            //    {
            //        var loginImgCount = RdsCacheHelper.CountCache.Get(ConfigHelper.GetFlagName("登陆图片识别", site));
            //        var loginHanZiCount = RdsCacheHelper.CountCache.Get(ConfigHelper.GetFlagName("登陆汉字识别", site));
            //        var orderImgCount = RdsCacheHelper.CountCache.Get(ConfigHelper.GetFlagName("下单图片识别", site));
            //        var orderHanZiCount = RdsCacheHelper.CountCache.Get(ConfigHelper.GetFlagName("下单汉字识别", site));
            //        var loginTotal = Math.Max(loginHanZiCount, loginImgCount);
            //        var orderTotal = Math.Max(orderHanZiCount, orderImgCount);
            //        var total = RdsCacheHelper.CountCache.Get(ConfigHelper.GetFlagName("验证码识别", site));
            //        result +=
            //            string.Format(
            //                "{0}【{11}】共{1}次打码，{12}个图片{0}{0}登陆：共{6}个   汉字:{2}  图片:{3}{0}下单：共{7}个  汉字:{4} 图片:{5}{0}{0}汉字识别率：{0}登陆：{8}%    下单：{9}%    全部：{10}%{0}{0}"
            //                , Environment.NewLine, total, loginHanZiCount, loginImgCount, orderHanZiCount, orderImgCount,
            //                loginTotal, orderTotal
            //                , (loginImgCount * 100.0 / loginTotal).ToString("F2")
            //                , (orderImgCount * 100.0 / orderTotal).ToString("F2")
            //                , ((loginImgCount + orderImgCount) * 100.0 / total).ToString("F2"), site,
            //                loginTotal + orderTotal);
            //    }
            //}
            //catch (Exception oe)
            //{
            //    result = oe.Message + Environment.NewLine;
            //}
            return result;
        }

        public static void RemoveToday()
        {
            //foreach (var item in Enum.GetNames(typeof(SiteFlag)))
            //{
            //    var site = item;
            //    try
            //    {
            //        RdsCacheHelper.CountCache.Remove(ConfigHelper.GetFlagName("验证码识别", site));
            //        RdsCacheHelper.CountCache.Remove(ConfigHelper.GetFlagName("登陆图片识别", site));
            //        RdsCacheHelper.CountCache.Remove(ConfigHelper.GetFlagName("登陆汉字识别", site));
            //        RdsCacheHelper.CountCache.Remove(ConfigHelper.GetFlagName("下单图片识别", site));
            //        RdsCacheHelper.CountCache.Remove(ConfigHelper.GetFlagName("下单汉字识别", site));
            //    }
            //    catch (Exception oe)
            //    {
            //        ConfigHelper._Log.Error("移除打码统计失败！", oe);
            //    }
            //}
            //RdsCacheHelper.LimitHelper.Clear();
        }

        public static void CommonProcess()
        {
            //LstImgPool = new ConcurrentStack<CusImageEntity>();
            //var lstTmp = new List<CusImageEntity>();
            var lastCount = CommonHanZi.lstHanZiReplace.Count;
            var isSaved = false;
            while (!ConfigHelper.IsExit)
            {
                Thread.Sleep(1000);
                try
                {
                    if (ServerTime.DateTime.Hour == 0 && ServerTime.DateTime.Minute == 0)
                    {
                        RemoveToday();
                    }
                    if (ServerTime.DateTime.Minute > 0 && ServerTime.DateTime.Minute % 8 == 0)
                    {
                        if (!isSaved)
                        {
                            MemoryManager.ClearMemory();
                            if (ConfigHelper.IsAutoFix)
                            {
                                if (lastCount != CommonHanZi.lstHanZiReplace.Count)
                                {
                                    var hanziPath = ConfigHelper.StrCodeBasePath + "\\User\\" +
                                                    ServerTime.DateTime.ToString("MMdd") + "\\";
                                    if (!Directory.Exists(hanziPath))
                                        Directory.CreateDirectory(hanziPath);
                                    try
                                    {
                                        var tt = string.Join("|",
                                            CommonHanZi.lstHanZiReplace.Select(
                                                p => string.Format("{0},{1}", p.Key, p.Value)).ToArray());
                                        File.WriteAllText(hanziPath + "UserFix.txt", tt, Encoding.UTF8);
                                        isSaved = true;
                                        lastCount = CommonHanZi.lstHanZiReplace.Count;
                                    }
                                    catch (Exception oe)
                                    {
                                        ConfigHelper._Log.Error("【AutoFix】保存文件出错！", oe);
                                        isSaved = false;
                                    }
                                }
                                try
                                {
                                    var lastDate = ServerTime.DateTime.AddDays(-1).ToString("MMdd");
                                    if (Directory.Exists(ConfigHelper.StrImagePath + lastDate))
                                    {
                                        if (!Directory.Exists(ConfigHelper.StrFixImagePath + lastDate)
                                            && !Directory.Exists(ConfigHelper.StrFixImageGuiDangPath + lastDate))
                                        {
                                            ImageOperate.SetTmpFiles(CommonHanZi.lstHanZi, lastDate);
                                        }
                                    }
                                }
                                catch (Exception oe)
                                {
                                    ConfigHelper._Log.Error("自动整理图片出错！", oe);
                                }
                            }
                            else
                            {
                                try
                                {
                                    CommonHanZi.LoadWordInfo(false);
                                    isSaved = true;
                                }
                                catch (Exception oe)
                                {
                                    ConfigHelper._Log.Error("【LoadFile】保存文件出错！", oe);
                                    isSaved = false;
                                }
                            }
                        }
                    }
                    else
                    {
                        if (isSaved)
                            isSaved = false;
                    }
                    Thread.Sleep(RandomHelper.GetRandomInt(30, 60) * 1000);

                }
                catch (Exception oe)
                {
                    ConfigHelper._Log.Error("【轮询】处理图片出错！", oe);
                }
            }
        }

        private static void ProcessSinglePic(string file, string fileName, string strLoc, string newPath, bool isLogin)
        {
            #region 处理单张图片

            //000001_1001_83.jpg
            //fileName = fileName.Substring(fileName.LastIndexOf("_") + 1);
            var lstResult =
                ImageHelper.GetCodeIndexStr(fileName.Substring(fileName.LastIndexOf("_") + 1).Replace(".jpg", ""));
            if (lstResult != null && lstResult.Count > 0)
            {
                try
                {
                    var newFileName = ServerTime.DateTime.ToString("HHmmssfff") + fileName.Substring(0, fileName.IndexOf("_")) +
                                      "-" + fileName.Substring(fileName.LastIndexOf("_") + 1);
                    //var newFilePath = newPath + newFileName;
                    //if (!Directory.Exists(Path.GetDirectoryName(ConfigHelper.StrImagePath + newFilePath)))
                    //{
                    //    Directory.CreateDirectory(Path.GetDirectoryName(ConfigHelper.StrImagePath + newFilePath));
                    //}
                    //File.Copy(file, ConfigHelper.StrImagePath + newFilePath);

                    using (var img = new Bitmap(CommonCompress.FastFromFile(file)))
                    {
                        var strOld = "";
                        var strHanZi = HanZiHelper.GetHanZiByImg(img, ref strOld, isLogin, SiteFlag.助手);

                        //RegHanZi.TestRegNew(strOld, strHanZi, isLogin);

                        if (!string.IsNullOrEmpty(strHanZi))
                        {
                            CustomImgProcess.SaveImage(img, newFileName.Substring(0, newFileName.IndexOf(".")),
                                lstResult, strHanZi,
                                strOld, isLogin);
                        }
                        img.Dispose();
                    }
                    newFileName = null;
                    newPath = null;
                }
                catch (Exception oe)
                {
                    ConfigHelper._Log.Error("【ProcessSinglePic】处理图片出错", oe);
                }
            }
            lstResult = null;

            #endregion
        }

        public static void ProcessTmpPic(string strLoc, bool isLogin = true)
        {
            new Thread(delegate()
            {
                try
                {
                    var parentPath = ConfigHelper.StrImagePath + strLoc + "\\";
                    var newPath = ServerTime.DateTime.ToString("MMdd") + "\\Old\\";

                    var files = Directory.GetFiles(parentPath);
                    if (files.Length > 0)
                    {
                        ConfigHelper._Log.InfoFormat("正在处理目录[{1}]中共 {0} 张…", files.Length, strLoc);
                        Parallel.ForEach(files, new ParallelOptions { MaxDegreeOfParallelism = 20 }, file =>
                        {
                            //foreach (var file in files)
                            {
                                var strHanZiPath = Path.GetFileName(file);
                                //_Log.InfoFormat("正在处理目录[{3}]中第 {0}/{1} 张 名称：{2}", index, files.Length, strHanZiPath, strLoc);

                                if (!file.EndsWith(".jpg"))
                                    return;
                                //continue;

                                try
                                {
                                    ProcessSinglePic(file, strHanZiPath, strLoc, newPath, isLogin);
                                }
                                catch (Exception oe)
                                {
                                    ConfigHelper._Log.Error("【批处理】处理单张图片报错！", oe);
                                }
                            }
                        });
                        ConfigHelper._Log.InfoFormat("处理目录[{1}]中共 {0} 张完毕！", files.Length, strLoc);
                    }
                    files = null;
                }
                catch (Exception oe)
                {
                    ConfigHelper._Log.Error("【批处理】处理图片报错！", oe);
                }
            }).Start();
        }

        //    {
        //    try
        //{
        //    bool isShowCount = false)

        //public static void SavePicByStr(bool isLogin, string strImg, string strName, string strIndex,
        //        if (string.IsNullOrEmpty(strIndex))
        //            strIndex = RndCodeHelper.GetRndIndex(isLogin, strName);

        //        var strHanZiPath = "";
        //        using (var img = CommonCompress.GetImageFromBase64(isLogin, strImg, strIndex, ref strHanZiPath, false))
        //        {
        //            if (!IsCanSave(strName, img))
        //                return;
        //            //分割小图
        //            var path = ConfigHelper.StrImagePath + DateTime.Now.ToString("MMdd") + "\\结果\\" + strName + "\\";
        //            if (!Directory.Exists(path))
        //            {
        //                Directory.CreateDirectory(path);
        //            }
        //            img.Save(path + strIndex + ".jpg", ImageFormat.Jpeg);

        //            img.Dispose();
        //        }
        //    }
        //    catch (Exception oe)
        //    {
        //        ConfigHelper._Log.Error("【处理】处理结果图片报错！", oe);
        //    }
        //    finally
        //    {
        //        strImg = null;
        //        strIndex = null;
        //    }
        //}
    }
}