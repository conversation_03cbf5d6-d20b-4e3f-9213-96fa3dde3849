﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Collections;

namespace ToolCommon
{
    public class CacheHelper
    {
        private static Hashtable lstTemp = new Hashtable();

        #region 保存对象到缓存中
        /// <summary>
        /// 保存对象到缓存中。
        /// </summary>
        public static void SetCache(string strCacheKey, object objContent)
        {
            SetCache(strCacheKey, objContent, 24);
        }

        /// <summary>
        /// 保存对象到缓存中。
        /// </summary>
        public static void SetCache(string strCacheKey, object objContent, double intExpireTime)
        {
            lock (lstTemp)
            {
                CacheItem cache = new CacheItem();
                cache.ObjKey = strCacheKey;
                cache.ObjValue = objContent;
                cache.ExpireTimes = intExpireTime;
                cache.DtStart = DateTime.Now;
                if (lstTemp.ContainsKey(strCacheKey))
                    lstTemp[strCacheKey] = cache;
                else
                    lstTemp.Add(strCacheKey, cache);
            }
        }
        #endregion

        #region 从缓存中取出对象

        /// <summary>
        /// 从缓存中取出对象。
        /// </summary>
        public static object GetCache(string strCacheKey)
        {
            if (lstTemp.ContainsKey(strCacheKey))
            {
                CacheItem cache = null;
                try
                {
                    cache = (CacheItem)lstTemp[strCacheKey];
                }
                catch { }
                if (cache != null)
                {
                    if (cache.ExpireTimes == -1 || cache.DtStart.AddHours(cache.ExpireTimes) > DateTime.Now)
                    {
                        return cache.ObjValue;
                    }
                    else
                    {
                        lstTemp.Remove(cache.ObjKey);
                    }
                }
            }
            return null;
        }
        #endregion

        #region 从缓存中清空对象
        /// <summary>
        /// 从缓存中清空对象。
        /// </summary>
        public static void Clear()
        {
            try
            {
                lstTemp.Clear();
                lstTemp = new Hashtable();
            }
            catch { }
        }

        /// <summary>
        /// 清除以指定字符串开头的缓存
        /// </summary>
        /// <param name="strKey"></param>
        public static void ClearStartCache(string strKey)
        {
            ClearCacheByString(strKey, true);
        }

        /// <summary>
        /// 清除以指定字符串结尾的缓存
        /// </summary>
        /// <param name="strKey"></param>
        public static void ClearEndCache(string strKey)
        {
            ClearCacheByString(strKey, false);
        }

        private static void ClearCacheByString(string strKey, bool isStart)
        {
            lock (lstTemp)
            {
                if (lstTemp != null && lstTemp.Count > 0)
                {
                    foreach (string str in lstTemp.Keys)
                    {
                        if (isStart)
                        {
                            if (str.StartsWith(strKey))
                            {
                                lstTemp[str] = null;
                            }
                        }
                        else
                        {
                            if (str.EndsWith(strKey))
                            {
                                lstTemp[str] = null;
                            }
                        }
                    }
                }
            }
        }
        #endregion

    }
}
