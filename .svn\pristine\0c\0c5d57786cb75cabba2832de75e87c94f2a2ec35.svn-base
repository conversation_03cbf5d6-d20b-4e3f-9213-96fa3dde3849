﻿using System;
using System.Runtime.InteropServices;
using Microsoft.Win32;

namespace NewTicket
{
    public class Ras
    {

        private bool DisConnCMDDial(string strEntryName, out string strError)
        {
            bool result = false;
            strError = "";
            try
            {
                KillDialProcess();
                string arg = string.Format("rasdial \"{0}\" /disconnect", strEntryName);
                arg = CommonMethod.ExecCmd(arg);
                if (arg.IndexOf("691") > 0)
                {
                    strError = "帐号或密码验证错误！";
                }
                else
                {
                    if (arg.IndexOf("651") > 0)
                    {
                        strError = "调制解调器(或其他连接设备)报告了一个错误！";
                    }
                    else if (arg.IndexOf("800") > 0)
                    {
                        strError = "未能连接到远程计算机！";
                    }
                    else
                    {
                        if (arg.IndexOf("已完成") >= 0)
                        {
                            result = true;
                        }
                    }
                }
            }
            catch (Exception oe)
            {
                strError = oe.Message;
            }
            return result;
        }

        private bool DoConnectCMDDial(string strEntryName, string strUserName, string strPassword, string Domin, string phonenumer, out string strError)
        {
            bool result = false;
            strError = "";
            try
            {
                string arg = string.Format("rasdial \"{0}\" {1} {2} {3} {4}", strEntryName, strUserName, strPassword, Domin, phonenumer);
                arg = CommonMethod.ExecCmd(arg);
                if (arg.IndexOf("691") > 0)
                {
                    strError = "帐号或密码验证错误！";
                }
                else
                {
                    if (arg.IndexOf("651") > 0)
                    {
                        strError = "调制解调器(或其他连接设备)报告了一个错误！";
                    }
                    else if (arg.IndexOf("800") > 0)
                    {
                        strError = "未能连接到远程计算机！";
                    }
                    else
                    {
                        if (arg.IndexOf("已连接 " + strEntryName.ToLower()) >= 0)
                        {
                            result = true;
                        }
                    }
                }
            }
            catch (Exception oe)
            {
                strError = oe.Message;
            }
            return result;
        }

        public bool DoCMDDial(string strEntryName, string strUserName, string strPassword, string Domin, string phonenumer, out string strError)
        {
            bool result = false;
            strError = "";
            for (int i = 0; i < 3; i++)
            {
                ConnectNotify("正在断开-" + (i + 1) + strEntryName + "...", 1);
                result = DisConnCMDDial(strEntryName, out strError);
                if (result)
                {
                    ConnectNotify(Settings.Default.NDialSleepTime + "秒后开始连接" + strEntryName + "...", 1);
                    System.Threading.Thread.Sleep((int)Settings.Default.NDialSleepTime * 1000);
                    ConnectNotify("正在连接" + strEntryName + "...", 1);
                    result = DoConnectCMDDial(strEntryName, strUserName, strPassword, Domin, phonenumer, out strError);
                    if (result)
                        break;
                }
            }
            ConnectNotify("连接" + strEntryName + (result ? "成功" : "失败:" + strError) + "！", 1);
            return result;
        }

        public void KillDialProcess()
        {
            //得到所有打开的进程
            foreach (System.Diagnostics.Process singleProc in System.Diagnostics.Process.GetProcesses())
            {
                try
                {
                    string ProcessName = singleProc.ProcessName.ToLower();
                    if (ProcessName.Contains("rasdial"))
                    {
                        singleProc.Kill();
                    }
                }
                catch { }
            }
        }
        // Fields
        private ConnectionNotify ConnectNotify;
        private int hrasconn;
        private RASCONN[] Rasconn;

        // Methods
        public Ras()
        {
        }

        public Ras(ConnectionNotify ConnectionDelegate, double interval)
        {
            ConnectNotify = ConnectionDelegate;
            Rasconn = new RASCONN[1];
            Rasconn[0].dwSize = Marshal.SizeOf(Rasconn[0]);
        }

        public bool DialUp(string strEntryName, out string strError)
        {
            bool flag = false;
            RASDIALPARAMS structure = new RASDIALPARAMS();
            structure.dwSize = Marshal.SizeOf(structure);
            structure.szEntryName = strEntryName;
            int nErrorValue = RasGetEntryDialParams(null, ref structure, ref flag);
            bool result;
            if (nErrorValue != 0)
            {
                strError = GetErrorString(nErrorValue);
                result = false;
            }
            else
            {
                ConnectNotify("正在连接" + structure.szEntryName + "...", 1);
                hrasconn = 0;
                nErrorValue = RasDial(0, null, ref structure, 0, 0, ref hrasconn);
                if (nErrorValue != 0)
                {
                    //uint INTERNET_AUTO_DIAL_UNATTENDED = 2;
                    //nErrorValue = InternetDial(IntPtr.Zero, structure.szEntryName, INTERNET_AUTO_DIAL_UNATTENDED, ref hrasconn, 0);
                    //if (nErrorValue != 0)
                    //{
                    //    strError = GetErrorString(nErrorValue);
                    //    ConnectNotify(strError, 3);
                    //    return false;
                    //}
                    //else
                    {
                        strError = GetErrorString(nErrorValue);
                        result = false;
                    }
                }
                else
                {
                    strError = null;
                    result = true;
                }
            }
            return result;
        }

        /// <summary>  
        /// 获取本机的拨号名称，也就是本机上所有的拨号  
        /// </summary>  
        /// <returns></returns>  
        public string[] GetASDLNames()
        {
            RegistryKey RegKey = Registry.CurrentUser.OpenSubKey("RemoteAccess\\Profile");
            if (RegKey != null)
                return RegKey.GetSubKeyNames();
            else
                return null;
        }

        public bool GetEntries(out string[] strEntryName, out string strError)
        {
            RASENTRYNAME[] lprasentryname = new RASENTRYNAME[1];
            lprasentryname[0].dwSize = Marshal.SizeOf(lprasentryname[0]);
            int lpcb = lprasentryname[0].dwSize;
            int lpcEntries = 0;
            int nErrorValue = RasEnumEntries(null, null, lprasentryname, ref lpcb, ref lpcEntries);
            if (nErrorValue == 632 || nErrorValue == 603)
            {
                lprasentryname = new RASENTRYNAME[1];
                lprasentryname[0].dwSize = Marshal.SizeOf(lprasentryname[0]);
                lpcb = lprasentryname[0].dwSize * 10;
                nErrorValue = RasEnumEntries(null, null, lprasentryname, ref lpcb, ref lpcEntries);
            }
            switch (nErrorValue)
            {
                case 0:
                    break;

                case 0x25b:
                    lprasentryname = new RASENTRYNAME[lpcEntries];
                    lprasentryname[0].dwSize = Marshal.SizeOf(lprasentryname[0]);
                    break;

                default:
                    strError = GetErrorString(nErrorValue);
                    strEntryName = null;
                    return false;
            }
            nErrorValue = RasEnumEntries(null, null, lprasentryname, ref lpcb, ref lpcEntries);
            if (nErrorValue != 0)
            {
                strError = GetErrorString(nErrorValue);
                strEntryName = null;
                return false;
            }
            strEntryName = new string[lpcEntries];
            for (int i = 0; i < lpcEntries; i++)
            {
                if (lprasentryname.Length < i + 1)
                    break;
                strEntryName[i] = lprasentryname[i].szEntryName;
            }
            strError = null;
            return true;
        }

        public bool GetEntryParams(string strEntryName, out string strPhoneNumber, out string strUserName, out string strPassword, out bool bRememberPassword, out string strError)
        {
            bool lpfPassword = false;
            RASDIALPARAMS structure = new RASDIALPARAMS();
            structure.dwSize = Marshal.SizeOf(structure);
            structure.szEntryName = strEntryName;
            int nErrorValue = RasGetEntryDialParams(null, ref structure, ref lpfPassword);
            if (nErrorValue != 0)
            {
                strError = GetErrorString(nErrorValue);
                strPhoneNumber = null;
                strUserName = null;
                strPassword = null;
                bRememberPassword = false;
                strError = null;
                return false;
            }
            strPhoneNumber = structure.szPhoneNumber;
            strUserName = structure.szUserName;
            if (lpfPassword)
            {
                strPassword = structure.szPassword;
            }
            else
            {
                strPassword = null;
            }
            bRememberPassword = lpfPassword;
            strError = null;
            return true;
        }

        internal string GetErrorString(int nErrorValue)
        {
            string result = null;
            if (nErrorValue >= 600 && nErrorValue < 758)
            {
                string text = new string(new char[256]);
                if (RasGetErrorString(nErrorValue, text, 256) != 0)
                {
                    text = null;
                }
                if (!string.IsNullOrEmpty(text))
                {
                    string arg_43_0 = text;
                    char[] trimChars = new char[1];
                    result = arg_43_0.TrimEnd(trimChars).Trim();
                }
            }
            return result;
        }

        public bool HangUp(out string strError)
        {
            bool result = false;
            KillDialProcess();
            result = hrasconn == 0;
            if (!result)
            {
                int nErrorValue = RasHangUp(hrasconn);
                if (nErrorValue != 0)
                {
                    strError = GetErrorString(nErrorValue);
                    ConnectNotify(strError, 0);
                }
            }
            if (Rasconn != null)
            {
                foreach (RASCONN rasconn in Rasconn)
                {
                    if (rasconn.hrasconn != 0)
                    {
                        int num2 = RasHangUp(rasconn.hrasconn);
                        result = num2 == 0;
                        if (!result)
                        {
                            strError = GetErrorString(num2);
                            ConnectNotify(strError, 0);
                            result = false;
                        }
                    }
                }
            }
            if (!result)
            {
                DisConnCMDDial("", out strError);
            }
            strError = null;
            ConnectNotify("连接中断.", 0);
            return true;
        }

        [DllImport("rasapi32.dll", CharSet = CharSet.Auto)]
        private static extern int RasCreatePhonebookEntry(int hwnd, string lpszPhonebook);
        [DllImport("rasapi32.dll", CharSet = CharSet.Auto)]
        private static extern int RasDeleteEntry(string lpszPhonebook, string lpszEntry);
        [DllImport("rasapi32.dll", CharSet = CharSet.Auto)]
        private static extern int RasDial(int lpRasDialExtensions, string lpszPhonebook, ref RASDIALPARAMS lpRasDialParams, int dwNotifierType, int lpvNotifier, ref int lphRasConn);

        [DllImport("wininet.dll", CharSet = CharSet.Auto)]
        public extern static int InternetDial(
            IntPtr hwnd,
            [In]string lpszConnectoid,
            uint dwFlags,
            ref int lpdwConnection,
            uint dwReserved
            );

        [DllImport("rasapi32.dll", CharSet = CharSet.Auto)]
        private static extern int RasEnumEntries(string reserved, string lpszPhonebook, [In, Out] RASENTRYNAME[] lprasentryname, ref int lpcb, ref int lpcEntries);
        [DllImport("rasapi32.dll", CharSet = CharSet.Auto)]
        private static extern int RasGetEntryDialParams(string lpszPhonebook, ref RASDIALPARAMS lprasdialparams, ref bool lpfPassword);
        [DllImport("rasapi32.dll", CharSet = CharSet.Auto)]
        private static extern int RasGetErrorString(int uErrorValue, string lpszErrorString, int cBufSize);
        [DllImport("rasapi32.dll", CharSet = CharSet.Auto)]
        private static extern int RasHangUp(int hrasconn);
        [DllImport("rasapi32.dll", CharSet = CharSet.Auto)]
        private static extern int RasSetEntryDialParams(string lpszPhonebook, ref RASDIALPARAMS lprasdialparams, bool fRemovePassword);

        public bool SetEntryParams(string strEntryName, string strPhoneNumber, string strUserName, string strPassword, bool bRememberPassword, out string strError)
        {
            RASDIALPARAMS structure = new RASDIALPARAMS();
            structure.dwSize = Marshal.SizeOf(structure);
            structure.szEntryName = strEntryName;
            structure.szPhoneNumber = strPhoneNumber;
            structure.szUserName = strUserName;
            structure.szPassword = strPassword;
            int nErrorValue = RasSetEntryDialParams(null, ref structure, !bRememberPassword);
            if (nErrorValue != 0)
            {
                strError = GetErrorString(nErrorValue);
                return false;
            }
            strError = null;
            return true;
        }

        // Nested Types
        public delegate void ConnectionNotify(string strNotify, int bConnect);

        [StructLayout(LayoutKind.Sequential, CharSet = CharSet.Auto)]
        private struct RASCONN
        {
            internal int dwSize;
            internal int hrasconn;
            [MarshalAs(UnmanagedType.ByValTStr, SizeConst = 0x101)]
            internal string szEntryName;
            [MarshalAs(UnmanagedType.ByValTStr, SizeConst = 0x11)]
            internal string szDeviceType;
            [MarshalAs(UnmanagedType.ByValTStr, SizeConst = 0x81)]
            internal string szDeviceName;
        }

        [StructLayout(LayoutKind.Sequential, CharSet = CharSet.Auto)]
        private struct RASDIALPARAMS
        {
            internal int dwSize;
            [MarshalAs(UnmanagedType.ByValTStr, SizeConst = 0x101)]
            internal string szEntryName;
            [MarshalAs(UnmanagedType.ByValTStr, SizeConst = 0x81)]
            internal string szPhoneNumber;
            [MarshalAs(UnmanagedType.ByValTStr, SizeConst = 0x81)]
            internal string szCallbackNumber;
            [MarshalAs(UnmanagedType.ByValTStr, SizeConst = 0x101)]
            internal string szUserName;
            [MarshalAs(UnmanagedType.ByValTStr, SizeConst = 0x101)]
            internal string szPassword;
            [MarshalAs(UnmanagedType.ByValTStr, SizeConst = 0x10)]
            internal string szDomain;
        }

        [StructLayout(LayoutKind.Sequential, CharSet = CharSet.Auto)]
        private struct RASENTRYNAME
        {
            internal int dwSize;
            [MarshalAs(UnmanagedType.ByValTStr, SizeConst = 0x101)]
            internal string szEntryName;
        }
    }
}
