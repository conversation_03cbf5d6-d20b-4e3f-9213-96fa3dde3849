﻿using System;
using System.IO;

namespace DhcpService
{
    public class CommonHelper
    {
        public static string SubString(string strSource, string strSpilt, string strEnd = "")
        {
            return SubStringHorspool(strSource, strSpilt, strEnd).Trim();
        }

        //Horspool匹配算法
        public static string SubStringHorspool(string str, string strStart, string strEnd = "")
        {
            int index = 0;
            if (!string.IsNullOrEmpty(strStart))
            {
                index = str.IndexOf(strStart);
                str = index >= 0 ? str.Substring(index + strStart.Length) : "";
            }
            if (!string.IsNullOrEmpty(str) && !string.IsNullOrEmpty(strEnd))
            {
                index = str.IndexOf(strEnd);
                if (index >= 0)
                {
                    str = str.Substring(0, index);
                }
                //else
                //    str = "";
            }
            strStart = null;
            strEnd = null;
            return str;
        }

        public static string GetPublicPicPath()
        {
            string strPath = Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData) + "\\Pic\\";
            if (!Directory.Exists(strPath))
                Directory.CreateDirectory(strPath);
            return strPath;
        }
    }
}
