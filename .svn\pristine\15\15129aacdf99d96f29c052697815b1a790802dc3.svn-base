﻿<%@ Page Title="VIP会员权益" Language="C#" MasterPageFile="~/Site.Master" AutoEventWireup="true" %>
<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="server">
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="server">
    <%Page.SetFrame("Desc.aspx?shownormal=1"); %>
    <iframe onload="this.height=this.contentWindow.document.body.scrollHeight+80" src="<%=Page.GetFrame() %>" width="80%" height="650px" style="margin-top: 60px;margin-left:10%;" frameborder="no" border="0" marginwidth="0" marginheight="0" scrolling="yes" allowtransparency="yes"></iframe>
    <script type="text/javascript">
        var iFrames = document.getElementsByTagName('iframe');
        function iResize() {
            for (var i = 0, j = iFrames.length; i < j; i++) {
                var bHeight = iFrames[i].contentWindow ? iFrames[i].contentWindow.document.body.scrollHeight : 0;
                var dHeight = iFrames[i].contentWindow ? iFrames[i].contentWindow.document.documentElement.scrollHeight : 0;
                var cHeight = iFrames[i].document ? iFrames[i].document.documentElement.scrollHeight : 0;
                var dHeight = window.innerHeight - 100;
                iFrames[i].style.height = Math.max(Math.max(Math.max(bHeight, dHeight), cHeight), dHeight) + 'px';
            }
        }
        window.setInterval("iResize()", 200);
    </script>
</asp:Content>
