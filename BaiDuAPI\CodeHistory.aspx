﻿<%@ Page Language="C#" AutoEventWireup="true" CodeBehind="CodeHistory.aspx.cs"
    Inherits="Code.Client.Web.CodeHistory" %>

<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head runat="server">
    <title></title>
</head>
<body>
    <form id="form1" runat="server">
        <div>
            <%--<asp:Label ID="Label1" runat="server" Text="日志列表："></asp:Label>--%>
            <asp:Label ID="lblCount" runat="server" Text="0"></asp:Label>
        </div>
        <div>
            <input type="button" value="开码" onclick="javascript:window.open('RCode.aspx')" />
        </div>
        <asp:GridView ID="gvDataSource" runat="server" BackColor="White" BorderColor="#CCCCCC"
            BorderStyle="None" BorderWidth="1px" CellPadding="3" EnableModelValidation="True">
            <FooterStyle BackColor="White" ForeColor="#000066" />
            <HeaderStyle BackColor="#006699" Font-Bold="True" ForeColor="White" />
            <PagerStyle BackColor="White" ForeColor="#000066" HorizontalAlign="Left" />
            <RowStyle ForeColor="#000066" />
            <SelectedRowStyle BackColor="#669999" Font-Bold="True" ForeColor="White" />
        </asp:GridView>
    </form>
</body>
</html>
