﻿using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;

namespace CommonLib
{
    public class OcrHtmlProcess
    {
        public delegate LocationInfo GetLocationByStrDelegate(string str);

        private static readonly Regex cnRegex = new Regex(@"[\u4e00-\u9fa5]", RegexOptions.Compiled | RegexOptions.CultureInvariant);
        private static readonly Regex jpRegex = new Regex(@"[\u0800-\u4e00]", RegexOptions.Compiled | RegexOptions.CultureInvariant);
        private static readonly Regex krRegex = new Regex(@"[\u3130-\u318f]|[\xAC00-\xD7A3]", RegexOptions.Compiled | RegexOptions.CultureInvariant);

        public static ResultEntity GetSpiltTextByJsonNew(string html, ResutypeEnum resultType,
            bool IsProcessJsonResultByArray
            , List<object> LstJsonPreProcessArray, List<object> LstJsonNextProcessArray,
            List<object> LstJsonLastProcessArray, List<object> LstJsonLocationProcessArray
            , string resultJsonSpilt, string resultTransJsonSpilt
            , string StrSpaceKey, string StrSpaceValue, string StrLineKey, string StrLineValue, string StrContactCell
            , bool IsMutliPage = false, bool IsAutoPageIndex = false, bool IsSupportVerticalOcr = false
            , bool IsJsonArrayString = false, bool IsJsonArrayStringWithLocation = false, string StrJsonArrayWordSpilt =
                null
            , bool isFromLeftToRight = false, bool isFromTopToDown = false, List<object> LstVerticalLocation = null
            , Dictionary<string, string> dicProperty = null, bool isDesrializeByLocation = false,
            GetLocationByStrDelegate getLocationByStrDelegate = null
            , bool isAutoFull2Half = true, bool isAutoSpace = true, bool isAutoSymbol = true, bool isAutoDuplicateSymbol = true
            , bool isPercentSize = false, Size size = new Size())
        {
            var result = new ResultEntity { html = html, resultType = resultType };
            var strJson = html;

            if (LstJsonPreProcessArray?.Count > 0)
            {
                strJson = JsonResultProcess.GetStrResultByHtml(html, LstJsonPreProcessArray, StrContactCell);
            }

            if (string.IsNullOrEmpty(strJson))
            {
                return result;
            }

            if (LstJsonNextProcessArray?.Count > 0)
            {
                strJson = ProcessJsonResult(strJson, LstJsonNextProcessArray, true);
                //strJson = JsonResultProcess.GetStrResultByHtml(strJson, LstJsonNextProcessArray);
            }

            if (!IsProcessJsonResultByArray)
            {
                result.spiltText = strJson;
                result.autoText = strJson;
                ProcessDecodeHtml(result);
                return result;
            }
            if (!string.IsNullOrEmpty(strJson))
            {
                if (strJson.EndsWith(","))
                {
                    strJson = strJson.TrimEnd(',');
                }
                if (!strJson.StartsWith("["))
                {
                    strJson = string.Format("[{0}]", strJson.TrimEnd(','));
                }
                result.resultHtml = strJson;
                var jArray = JArray.Parse(strJson);
                if (IsJsonArrayStringWithLocation)
                {
                    strJson = ProcessJsonArrayWithLocation(html, jArray, LstJsonLocationProcessArray,
                        StrContactCell, StrJsonArrayWordSpilt);
                    try
                    {
                        jArray = JArray.Parse(strJson);
                    }
                    catch (Exception oe)
                    {
                        return result;
                    }
                }
                if (IsJsonArrayString)
                {
                    StringBuilder sb = new StringBuilder();
                    foreach (var item in jArray)
                    {
                        sb.AppendLine(item.ToString());
                    }
                    strJson = sb.ToString();
                    result.spiltText = strJson;
                    result.autoText = strJson;
                }
                else
                {
                    if (IsSupportVerticalOcr)
                    {
                        try
                        {
                            List<TextCellInfo> lstCells = new List<TextCellInfo>();
                            if (IsMutliPage)
                            {
                                ProcessMutilPage(LstJsonLastProcessArray, resultJsonSpilt, resultTransJsonSpilt, IsAutoPageIndex, LstVerticalLocation, dicProperty, isDesrializeByLocation, getLocationByStrDelegate, jArray, lstCells, StrSpaceKey, StrSpaceValue, StrLineKey, StrLineValue);
                            }
                            else
                            {
                                lstCells = GetVerticalCells(resultJsonSpilt, resultTransJsonSpilt, LstVerticalLocation, dicProperty, strJson, jArray, isDesrializeByLocation, getLocationByStrDelegate, StrSpaceKey, StrSpaceValue, StrLineKey, StrLineValue);
                            }
                            if (isPercentSize && !size.IsEmpty)
                            {
                                lstCells.AsParallel().ForAll(p =>
                                {
                                    if (p.location != null)
                                    {
                                        p.location.width *= size.Width;
                                        p.location.left *= size.Width;
                                        p.location.left -= p.location.width;
                                        p.location.top *= size.Height;
                                        p.location.height *= size.Height;
                                    }
                                });
                            }
                            lstCells.AsParallel().ForAll(p =>
                            {
                                if (p.location == null) return;
                                p.location.width = (int)p.location.width;
                                p.location.left = (int)p.location.left;
                                p.location.top = (int)p.location.top;
                                p.location.height = (int)p.location.height;
                            });

                            ProcessByLstCells(isFromLeftToRight, isFromTopToDown, result, lstCells
                                , isAutoFull2Half, isAutoSpace, isAutoSymbol, isAutoDuplicateSymbol, StrContactCell);
                        }
                        catch (Exception oe)
                        {
                            Console.WriteLine(oe.Message);
                        }
                    }
                    if (string.IsNullOrEmpty(result.spiltText))
                    {
                        var strTrans = string.Empty;
                        result.spiltText = JsonResultProcess.ArrayToString(jArray, resultJsonSpilt, resultTransJsonSpilt, ref strTrans, StrContactCell);
                        result.transText = strTrans;
                    }
                }
                if (!string.IsNullOrEmpty(result.spiltText))
                {
                    result.spiltText = CommonStyle.ReplacePunctuationAuto(result.spiltText, GetLang(result.spiltText), isAutoFull2Half, isAutoSpace, isAutoSymbol, isAutoDuplicateSymbol);
                }
                result.autoText = string.IsNullOrEmpty(result.autoText) ? result.spiltText : result.autoText;
            }
            ProcessDecodeHtml(result);
            return result;
        }

        // 处理带位置的JSON数组
        private static string ProcessJsonArrayWithLocation(string html, JArray jArray, List<object> LstJsonLocationProcessArray,
            string StrContactCell, string StrJsonArrayWordSpilt)
        {
            var locations = JsonResultProcess.GetStrResultByHtml(html, LstJsonLocationProcessArray, StrContactCell);
            var locationArray = JArray.Parse(locations);
            var results = new ConcurrentBag<string>();

            Parallel.For(0, locationArray.Count, i =>
            {
                var locStr = locationArray[i].ToString().Trim().TrimEnd('}');
                if (locStr.StartsWith("["))
                {
                    locStr = "{\"position\":" + locStr;
                }

                var words = string.Empty;
                if (!string.IsNullOrEmpty(StrJsonArrayWordSpilt))
                {
                    words = jArray[i][StrJsonArrayWordSpilt]?.ToString() ?? string.Empty;
                }
                if (string.IsNullOrEmpty(words))
                {
                    words = jArray[i]?.ToString() ?? string.Empty;
                }
                results.Add($"{locStr},\"words\":\"{StringToJson(words)}\"}}");
            });

            return $"[{string.Join(",", results)}]";
        }

        public static string GetLang(string words)
        {
            var lang = cnRegex.IsMatch(words) ? "zh" : (jpRegex.IsMatch(words) ? "ja" : krRegex.IsMatch(words) ? "kr" : "en");
            return lang;
        }

        // 字符转义映射表
        private static readonly Dictionary<char, string> _jsonEscapeMap = new Dictionary<char, string>
        {
            { '\"', "\\\"" },
            { '\\', "\\\\" },
            { '/', "\\/" },
            { '\b', "\\b" },
            { '\f', "\\f" },
            { '\n', "\\n" },
            { '\r', "\\r" },
            { '\t', "\\t" }
        };

        /// <summary>
        /// json 对特殊字符的处理
        /// </summary>
        private static string StringToJson(string s)
        {
            if (string.IsNullOrEmpty(s))
                return string.Empty;

            StringBuilder sb = new StringBuilder(s.Length * 2);
            foreach (char c in s)
            {
                if (_jsonEscapeMap.TryGetValue(c, out string escaped))
                {
                    sb.Append(escaped);
                }
                else
                {
                    sb.Append(c);
                }
            }
            return sb.ToString();
        }

        private static void ProcessMutilPage(List<object> LstJsonLastProcessArray, string resultJsonSpilt, string resultTransJsonSpilt,
            bool IsAutoPageIndex, List<object> LstVerticalLocation, Dictionary<string, string> dicProperty, bool isDesrializeByLocation,
            GetLocationByStrDelegate getLocationByStrDelegate, JArray jArray, List<TextCellInfo> lstCells
            , string StrSpaceKey, string StrSpaceValue, string StrLineKey, string StrLineValue)
        {
            if (IsAutoPageIndex && jArray.Count > 0)
            {
                var tempCells = jArray[0].OfType<JProperty>()
                    .AsParallel()
                    .SelectMany(item =>
                    {
                        if (item == null) return Enumerable.Empty<TextCellInfo>();
                        var pageIndex = BoxUtil.GetInt32FromObject(item.Name, 1);
                        return ProcessMutilOnePage(LstJsonLastProcessArray, resultJsonSpilt, resultTransJsonSpilt,
                            LstVerticalLocation, dicProperty, isDesrializeByLocation, getLocationByStrDelegate,
                            item.Value?.ToString(), pageIndex, StrSpaceKey, StrSpaceValue, StrLineKey, StrLineValue);
                    }).ToList();

                lstCells.AddRange(tempCells);
            }
            else
            {
                int pageIndex = 1;
                foreach (var item in jArray)
                {
                    if (item == null)
                    {
                        continue;
                    }

                    var lstTmp = ProcessMutilOnePage(LstJsonLastProcessArray, resultJsonSpilt, resultTransJsonSpilt, LstVerticalLocation, dicProperty, isDesrializeByLocation, getLocationByStrDelegate, item.ToString(), pageIndex, StrSpaceKey, StrSpaceValue, StrLineKey, StrLineValue);

                    if (lstTmp != null)
                    {
                        lstCells.AddRange(lstTmp);
                    }
                    pageIndex++;
                }
            }
            if (lstCells.Any(p => p.PageIndex == 0))
            {
                lstCells.ForEach(p => { p.PageIndex += 1; });
            }
        }

        private static List<TextCellInfo> ProcessMutilOnePage(List<object> LstJsonLastProcessArray, string resultJsonSpilt,
            string resultTransJsonSpilt, List<object> LstVerticalLocation, Dictionary<string, string> dicProperty, bool isDesrializeByLocation,
            GetLocationByStrDelegate getLocationByStrDelegate, string strJson, int pageIndex
            , string StrSpaceKey, string StrSpaceValue, string StrLineKey, string StrLineValue)
        {
            if (LstJsonLastProcessArray?.Count > 0)
            {
                strJson = ProcessJsonResult(strJson, LstJsonLastProcessArray, true);
            }

            if (!strJson.StartsWith("["))
            {
                strJson = string.Format("[{0}]", strJson.TrimEnd(','));
            }

            var lstTmp = GetVerticalCells(resultJsonSpilt, resultTransJsonSpilt, LstVerticalLocation, dicProperty,
                strJson, JArray.Parse(strJson), isDesrializeByLocation, getLocationByStrDelegate
            , StrSpaceKey, StrSpaceValue, StrLineKey, StrLineValue)
                 .Select(cell => { cell.PageIndex = pageIndex; return cell; }).ToList();
            return lstTmp;
        }

        public static void ProcessDecodeHtml(ResultEntity result)
        {
            if (!string.IsNullOrEmpty(result.spiltText))
            {
                result.spiltText = System.Net.WebUtility.HtmlDecode(result.spiltText).TrimEnd();
            }
            if (!string.IsNullOrEmpty(result.transText))
            {
                result.transText = System.Net.WebUtility.HtmlDecode(result.transText).TrimEnd();
            }
            if (!string.IsNullOrEmpty(result.autoText))
            {
                result.autoText = System.Net.WebUtility.HtmlDecode(result.autoText).TrimEnd();
            }
        }

        public static void ProcessByLstCells(bool isFromLeftToRight, bool isFromTopToDown, ResultEntity result, List<TextCellInfo> lstCells
            , bool isAutoFull2Half, bool isAutoSpace, bool isAutoSymbol, bool isAutoDuplicateSymbol, string StrContactCell)
        {
            if (lstCells == null || lstCells.Count <= 0) return;

            foreach (var p in lstCells)
            {
                if (p.location != null)
                {
                    if (p.location.height < 5) p.location.height = 15;
                    if (p.location.width < 5) p.location.width = 5;
                }
            }
            if (lstCells.Any(p => p.location != null))
            {
                result.verticalText = JsonConvert.SerializeObject(lstCells);
            }
            //正常文字识别，从左到右,从上到下
            var lstLines = GetVerticalOcrResult(lstCells, isFromLeftToRight, isFromTopToDown, StrContactCell);
            var strStart = lstLines?.Count > 1 ? "\t" : "";

            result.spiltText = (strStart + string.Join("\n" + strStart, lstLines.Select(p => p.words?.TrimEnd()))).TrimEnd();
            result.transText = (strStart + string.Join("\n" + strStart, lstLines.Select(p => p.trans?.TrimEnd()))).TrimEnd();

            try
            {
                if (lstLines.Any(p => p.rectangle.IsEmpty))
                {
                    return;
                }
                var lines = new LineProcess(lstLines, isAutoFull2Half, isAutoSpace, isAutoSymbol, isAutoDuplicateSymbol);

                result.spiltLocText = lines.GetMergeResult(false, true);
                result.transLocText = lines.GetMergeResult(true, true);
                //OcrLineProcess.ProcessTextByLineLocation(lstLines);
            }
            catch { }
        }

        private static List<TextCellInfo> GetVerticalCells(string resultJsonSpilt, string resultTransJsonSpilt, List<object> LstVerticalLocation
            , Dictionary<string, string> dicProperty, string strJson, JArray jArray, bool isDesrializeByLocation, GetLocationByStrDelegate getLocationByStrDelegate
            , string StrSpaceKey, string StrSpaceValue, string StrLineKey, string StrLineValue)
        {
            List<TextCellInfo> lstCells;
            /*{
        "probability": 0.39386045932769775,
        "rectangle": {
        "x": 17,
        "width": 387,
        "y": 158,
        "height": 17
        },
        "text": "要不要搞多域名密户端发现超时或者无法解析时换个域名再调一遍接口"
        }*/
            if (!string.IsNullOrEmpty(resultJsonSpilt) && LstVerticalLocation?.Count > 0)
            {
                lstCells = new List<TextCellInfo>();
                foreach (var item in jArray)
                {
                    var cell = new TextCellInfo
                    {
                        words = item[resultJsonSpilt]?.ToString(),
                        trans = !string.IsNullOrEmpty(resultTransJsonSpilt) ? item[resultTransJsonSpilt]?.ToString() : null
                    };
                    if (!string.IsNullOrEmpty(StrSpaceKey))
                    {
                        var value = ProcessJsonResult(item.ToString(), new List<object>() { StrSpaceKey });
                        if (Equals(value, StrSpaceValue))
                        {
                            if (!string.IsNullOrEmpty(cell.words)) cell.words += " ";
                            if (!string.IsNullOrEmpty(cell.trans)) cell.trans += " ";
                        }
                    }
                    if (!string.IsNullOrEmpty(StrLineKey))
                    {
                        var value = ProcessJsonResult(item.ToString(), new List<object>() { StrLineKey });
                        if (Equals(value, StrLineValue))
                        {
                            if (!string.IsNullOrEmpty(cell.words)) cell.words += "\n";
                            if (!string.IsNullOrEmpty(cell.trans)) cell.trans += "\n";
                        }
                    }
                    var locationInfo = GetPreResultByList(item, LstVerticalLocation);
                    cell.location = getLocationByStrDelegate?.Invoke(locationInfo);
                    lstCells.Add(cell);
                }
            }
            else
            {
                JsonSerializerSettings propSettings = new JsonSerializerSettings();
                if (dicProperty?.Count > 0)
                {
                    propSettings.ContractResolver = new PropsContractResolver(dicProperty);
                }
                if (isDesrializeByLocation)
                {
                    lstCells = new List<TextCellInfo>();
                    var lstLocations = JsonConvert.DeserializeObject<List<VLocationInfo>>(strJson, propSettings);
                    if (lstLocations != null)
                    {
                        lstCells = lstLocations.Select(p => new TextCellInfo
                        {
                            words = p?.words,
                            location = p != null ? new LocationInfo
                            {
                                height = p.height,
                                left = p.left,
                                top = p.top,
                                width = p.width
                            } : null,
                            trans = p?.trans
                        }).ToList();
                        lstLocations.Clear();
                    }
                }
                else
                {
                    lstCells = JsonConvert.DeserializeObject<List<TextCellInfo>>(strJson, propSettings);
                }
            }

            return lstCells;
        }

        private static double floatPercentDown = 1.25;
        private static double floatPercentUp = 0.75;
        private static List<LineInfo> GetVerticalOcrResult(List<TextCellInfo> lstCells, bool isFromLeftToRight, bool isFromTopToDown, string StrContactCell)
        {
            List<LineInfo> lstLine = new List<LineInfo>();
            try
            {
                if (lstCells?.Count > 0)
                {
                    double? left = null;
                    double? top = null;
                    double? height = null;
                    double? width = null;
                    double? lastTop = null;
                    double? lastLeft = null;
                    double? lastTopTmp = null;
                    double? lastLeftTmp = null;
                    int? lastPageTmp = null;
                    TextCellInfo cell;
                    LineInfo line = null;
                    while ((cell =
                        isFromLeftToRight ?
                        GetNextProcessFromLeftToRight(lstCells, isFromTopToDown, ref lastTop) :
                         //GetNextProcessFromRightToLeft(lstCells, isFromTopToDown, ref lastTop)
                         GetNextProcessFromRightToLeft(lstCells, isFromTopToDown, ref lastLeft)
                        ) != null)
                    {
                        bool isNextLine = cell.location == null;
                        if (!isNextLine & left.HasValue)
                        {
                            if (cell.PageIndex != lastPageTmp || lastPageTmp == null)
                            {
                                isNextLine = true;
                            }
                            else
                            {
                                if (isFromLeftToRight)
                                {
                                    if (lastTopTmp != lastTop || !lastTop.HasValue)
                                    {
                                        //从上到下
                                        if (isFromTopToDown)
                                        {
                                            //如果高度偏离1/10以上
                                            if (cell.location.top > top && cell.location.top + cell.location.height > top + height * floatPercentDown)
                                                isNextLine = true;
                                        }
                                        //从下到上
                                        else
                                        {
                                            //如果高度偏离1/3以上
                                            if (cell.location.top < top && !CheckCross(new Rectangle { X = (int)cell.location.left, Y = (int)cell.location.top, Width = (int)cell.location.width, Height = (int)(cell.location.height * floatPercentDown) }, new Rectangle { X = (int)cell.location.left, Y = (int)top.Value, Width = (int)width.Value, Height = (int)height.Value }))
                                                //cell.rectangle.top + cell.rectangle.height < top + height * floatPercentUp
                                                isNextLine = true;

                                        }
                                        if (!isNextLine)
                                        {
                                            //从左向右
                                            //if (cell.rectangle.left > left && !CheckCross(new rectangle() { X = cell.rectangle.left, Y = cell.rectangle.top, Width = (int)(cell.rectangle.width * floatPercentDown), Height = cell.rectangle.height }, new rectangle() { X = left.Value, Y = cell.rectangle.top, Width = width.Value, Height = height.Value }))
                                            if (cell.location.left > left && cell.location.left + cell.location.width < left + width * floatPercentDown)
                                                isNextLine = true;
                                        }
                                    }
                                }
                                else
                                {
                                    if (lastLeftTmp != lastLeft || !lastLeft.HasValue)
                                    {
                                        //如果左右偏离1/4以上
                                        if (cell.location.left < left && cell.location.left + cell.location.width < left + width * floatPercentUp)
                                            isNextLine = true;
                                    }
                                }
                            }
                        }

                        if (line != null && isNextLine)
                        {
                            ProcessTextByLine(isFromLeftToRight, isFromTopToDown, line, lstLine, StrContactCell);
                            line = null;
                        }
                        if (line == null)
                        {
                            line = new LineInfo { lstCell = new List<TextCellInfo>(), words = string.Empty, trans = string.Empty };
                        }
                        if (isNextLine)
                        {
                            top = -1;
                            left = -1;
                            height = -1;
                            width = -1;
                        }
                        line.lstCell.Add(cell);
                        //if (!string.IsNullOrEmpty(cell.words))
                        //{
                        //    line.words += cell.words;
                        //}
                        //if (!string.IsNullOrEmpty(cell.trans))
                        //{
                        //    line.trans += cell.trans;
                        //}
                        if (cell.location != null)
                        {
                            top = cell.location.top;
                            left = cell.location.left;
                            height = cell.location.height;
                            width = cell.location.width;
                        }
                        if (lastTop.HasValue)
                        {
                            lastTopTmp = lastTop.Value;
                        }
                        if (lastLeft.HasValue)
                        {
                            lastLeftTmp = lastLeft.Value;
                        }
                        lastPageTmp = cell.PageIndex;
                        cell.IsProcessed = true;
                    }
                    if (line != null)
                    {
                        ProcessTextByLine(isFromLeftToRight, isFromTopToDown, line, lstLine, StrContactCell);
                    }
                }
            }
            catch (Exception oe)
            {
                Console.WriteLine(oe.Message);
            }

            foreach (var p in lstLine) p.Init();
            return lstLine;
        }

        private static void ProcessTextByLine(bool isFromLeftToRight, bool isFromTopToDown, LineInfo line, List<LineInfo> lstLine, string StrContactCell)
        {
            if (isFromLeftToRight)
            {
                if (isFromTopToDown)
                {
                    line.lstCell = line.lstCell.OrderBy(p => p.location.left).ToList();
                }
                else
                {
                    line.lstCell = line.lstCell.OrderByDescending(p => p.location.top).ToList();
                }
            }
            else
            {
                if (isFromTopToDown)
                {
                    line.lstCell = line.lstCell.OrderBy(p => p.location.top).ToList();
                }
                else
                {
                    line.lstCell = line.lstCell.OrderByDescending(p => p.location.left).ToList();
                }
            }

            MergeLine(line, StrContactCell, isFromLeftToRight, isFromTopToDown);
            lstLine.Add(JsonConvertClone<LineInfo>(line));
        }

        private static void MergeLine(LineInfo line, string StrContactCell, bool isFromLeftToRight, bool isFromTopToDown)
        {
            if (line.lstCell.Count > 1)
            {
                for (int i = 1; i < line.lstCell.Count; i++)
                {
                    var last = line.lstCell[i - 1];
                    var now = line.lstCell[i];
                    var distance = 0d;
                    var averageWidth = 0d;

                    if (isFromLeftToRight)
                    {
                        if (isFromTopToDown)
                        {
                            //白日依山尽 黄河入海流
                            //欲穷千里目 更上一层楼
                            distance = now.location.left - last.location.left - last.location.width;
                            averageWidth = (last.location.width / last.words.Length + now.location.width / now.words.Length) / 2;
                        }
                        else
                        {
                            //道
                            //车
                            //交
                            //公
                            distance = now.location.top + last.location.top - now.location.top;
                            averageWidth = (last.location.height / last.words.Length + now.location.height / now.words.Length) / 2;
                        }
                    }
                    else
                    {
                        if (isFromTopToDown)
                        {
                            //更欲
                            //上穷
                            //一千
                            //层里
                            //楼目
                            distance = now.location.top - last.location.top - last.location.height;
                            averageWidth = (last.location.height / last.words.Length + now.location.height / now.words.Length) / 2;
                        }
                        else
                        {
                            distance = last.location.left - now.location.left - now.location.width;
                            averageWidth = (last.location.width / last.words.Length + now.location.width / now.words.Length) / 2;
                        }
                    }

                    // 间距大于平均宽度的80%时，添加空格
                    var isAddSpace = distance > averageWidth * 0.8;
                    if (isAddSpace)
                    {
                        now.words = " " + now.words;
                    }
                }
            }

            line.words = string.Join(StrContactCell, line.lstCell.Select(p => p.words?.Replace("\n", " "))).TrimEnd();
            line.trans = string.Join(StrContactCell, line.lstCell.Select(p => p.trans?.Replace("\n", " "))).TrimEnd();
        }

        public static T JsonConvertClone<T>(object obj) where T : class
        {
            var str = JsonConvert.SerializeObject(obj);
            return JsonConvert.DeserializeObject<T>(str, new JsonSerializerSettings { NullValueHandling = NullValueHandling.Ignore, Converters = new List<JsonConverter>() });
        }

        private static bool CheckCross(Rectangle r1, Rectangle r2)
        {
            var c1 = new PointF(r1.Left + r1.Width / 2.0f, r1.Top + r1.Height / 2.0f);
            var c2 = new PointF(r2.Left + r2.Width / 2.0f, r2.Top + r2.Height / 2.0f);

            return (Math.Abs(c1.X - c2.X) <= r1.Width / 2.0 + r2.Width / 2.0 && Math.Abs(c2.Y - c1.Y) <= r1.Height / 2.0 + r2.Height / 2.0);
        }

        private const double NMinWidth = 5;

        private static TextCellInfo GetNextProcessFromLeftToRight(List<TextCellInfo> lstCells
            , bool isFromTopToDown, ref double? lastTop)
        {
            TextCellInfo cell = null;

            var minPage = lstCells.Any(p => !p.IsProcessed) ? lstCells.Where(p => !p.IsProcessed).Min(p => p.PageIndex) : 0;
            var lstNoProcessed = lstCells.Where(p => !p.IsProcessed && p.PageIndex == minPage);
            if (lstNoProcessed?.Count() > 0)
            {
                double? minTop = lastTop;
                List<TextCellInfo> lstTmp = null;
                if (!minTop.HasValue)
                {
                    if (isFromTopToDown)
                    {
                        minTop = lstNoProcessed.Min(p => p.location?.top);
                    }
                    else
                    {
                        minTop = lstNoProcessed.Max(p => p.location?.top);
                    }
                }
                else
                {
                    //Console.WriteLine("上次高度：" + lastTop);
                }
                lstTmp = GetFitResultByTop(isFromTopToDown, minTop, lstNoProcessed);
                lastTop = null;
                if (lstTmp?.Count > 0)
                {
                    cell = lstTmp.OrderBy(p => p.location?.left).FirstOrDefault();
                    if (lstTmp.Count > 1)
                    {
                        lastTop = minTop;
                    }
                }
                else
                {
                    cell = lstNoProcessed.FirstOrDefault();
                }
            }
            return cell;
        }

        private static TextCellInfo GetNextProcessFromRightToLeft(List<TextCellInfo> lstCells
            , bool isFromTopToDown, ref double? lastLeft)
        {
            TextCellInfo cell = null;

            //var lstNoProcessed = lstCells.Where(p => !p.IsProcessed);
            //var minPage = lstNoProcessed.Min(p => p.PageIndex);
            //lstNoProcessed = lstNoProcessed.Where(p => p.PageIndex == minPage);

            var minPage = lstCells.Any(p => !p.IsProcessed) ? lstCells.Where(p => !p.IsProcessed).Min(p => p.PageIndex) : 0;
            var lstNoProcessed = lstCells.Where(p => !p.IsProcessed && p.PageIndex == minPage);
            if (lstNoProcessed?.Count() > 0)
            {
                double? minLeft = lastLeft;
                if (!minLeft.HasValue)
                {
                    minLeft = lstNoProcessed.Max(p => p.location?.left);
                    //minWidth = lstNoProcessed.Where(p => p.rectangle?.left + p.rectangle?.width >= minLeft).Select(p => p.rectangle?.width).Average();
                }
                else
                {
                    if (!lstNoProcessed.Any(p => p.location?.left <= minLeft && p.location?.left + p.location?.width >= minLeft))
                    {
                        minLeft = lstNoProcessed.Select(p => p.location.left).Max();
                    }
                    //Console.WriteLine("上次高度：" + lastTop);
                }
                var lstTmp = GetFitResultByLeft(minLeft, lstNoProcessed);
                lastLeft = null;
                if (lstTmp?.Count > 0)
                {
                    if (isFromTopToDown)
                    {
                        cell = lstTmp.OrderBy(p => p.location?.top).ThenByDescending(p => p.location?.left).FirstOrDefault();
                    }
                    else
                    {
                        cell = lstTmp.OrderByDescending(p => p.location?.top).ThenByDescending(p => p.location?.left).FirstOrDefault();
                    }
                    if (lstTmp.Count > 1)
                    {
                        lastLeft = minLeft;
                    }
                }
                else
                {
                    cell = lstNoProcessed.FirstOrDefault();
                }
            }
            return cell;
        }

        private static List<TextCellInfo> GetFitResultByLeft(double? left, IEnumerable<TextCellInfo> lstNoProcessed)
        {
            double? minLeft;
            double? maxLeft = left;
            //minLeft = maxLeft - width * 1.5;
            minLeft = lstNoProcessed.Where(p => p.location?.left <= left && p.location?.left + p.location?.width >= left)
                .Select(p => p.location.left).Min();
            minLeft = Math.Max(minLeft.Value, NMinWidth);

            var lstTmp = lstNoProcessed
                 .Where(p => p.location?.left >= minLeft && p.location?.left <= maxLeft).ToList();
            return lstTmp;
        }

        private static List<TextCellInfo> GetFitResultByTop(bool isFromTopToDown, double? top, IEnumerable<TextCellInfo> lstNoProcessed)
        {
            double? minTop;
            double? maxTop;

            List<TextCellInfo> lstTmp;
            if (isFromTopToDown)
            {
                double? height = lstNoProcessed.Where(p => p.location?.top <= top).Select(p => p.location?.height).Max();
                if (height == null)
                {
                    top = lstNoProcessed.Where(p => p.IsProcessed == false).Select(p => p.location?.top).Min();
                    height = lstNoProcessed.Where(p => p.location?.top <= top).Select(p => p.location?.height).Max();
                }
                minTop = top;
                //maxTop = minTop + height * 1.25;
                maxTop = lstNoProcessed.Where(p => p.location?.top >= top && p.location?.top <= top + height * floatPercentUp)
                    .Select(p => p.location?.top + p.location?.height * floatPercentUp).Max();
                var tmpMaxHeight = lstNoProcessed.Where(p => p.location?.top <= maxTop).Select(p => p.location?.height).Max();
                if (tmpMaxHeight > height)
                {
                    maxTop = lstNoProcessed.Where(p => p.location?.top >= top && p.location?.top <= top + tmpMaxHeight * floatPercentUp)
                        .Select(p => p.location?.top + p.location?.height * floatPercentUp).Max();
                }
                //var tmpMaxTop = lstNoProcessed.Where(p => p.rectangle?.top >= minTop && p.rectangle?.top + p.rectangle?.height <= maxTop)
                //    .Select(p => p.rectangle.top + p.rectangle.height).Max();
            }
            else
            {
                maxTop = top;
                //minTop = maxTop - height * 1.25;
                //minTop = Math.Max(0, minTop.Value);
                minTop = lstNoProcessed.Where(p => p.location?.top <= top && p.location?.top + p.location?.height >= top)
                    .Select(p => p.location?.top).Min();
                minTop = lstNoProcessed.Where(p => p.location?.top <= minTop && p.location?.top + p.location?.height >= minTop)
                    .Select(p => p.location?.top).Min();
            }
            minTop = Math.Max(0, minTop ?? 0d);

            lstTmp = lstNoProcessed.Where(p => p.location?.top >= minTop && p.location?.top <= maxTop).ToList();
            return lstTmp;
        }

        public static string ProcessJsonResult(string jsonTmp, List<object> lstArray, bool isAll = false)
        {
            if (string.IsNullOrEmpty(jsonTmp) || lstArray == null || lstArray.Count == 0)
                return jsonTmp;

            foreach (var item in lstArray)
            {
                try
                {
                    if (Equals(item, "|^[]|"))
                    {
                        jsonTmp = string.Format("[{0}]", jsonTmp.Replace("[", "").Replace("]", "").TrimEnd(','));
                        continue;
                    }
                    jsonTmp = GetTokenByName(jsonTmp, item as string, isAll);
                    if (string.IsNullOrEmpty(jsonTmp))
                    {
                        jsonTmp = null;
                        break;
                    }
                    if (jsonTmp.EndsWith(","))
                    {
                        if (!jsonTmp.StartsWith("["))
                        {
                            jsonTmp = jsonTmp.TrimEnd(',').TrimStart(',');
                            if (!jsonTmp.StartsWith("{") || !jsonTmp.EndsWith("}"))
                            {
                                if (!jsonTmp.StartsWith("[") && !jsonTmp.EndsWith("]"))
                                {
                                    jsonTmp = string.Format("\"{0}\"", jsonTmp.TrimEnd('\"').TrimStart('\"'));
                                }
                            }
                            jsonTmp = string.Format("[{0}]", jsonTmp);
                        }
                        else if (jsonTmp.EndsWith("],"))
                        {
                            jsonTmp = jsonTmp.TrimEnd(',').TrimStart(',');
                            if (!jsonTmp.StartsWith("{") || !jsonTmp.EndsWith("}"))
                            {
                                if (!jsonTmp.StartsWith("[") && !jsonTmp.EndsWith("]"))
                                {
                                    jsonTmp = string.Format("\"{0}\"", jsonTmp.TrimEnd('\"').TrimStart('\"'));
                                }
                            }
                            jsonTmp = string.Format("[{0}]", jsonTmp);
                        }
                    }
                }
                catch (Exception oe)
                {
                    Console.WriteLine(oe.Message);
                }
            }

            return jsonTmp;
        }

        public static string GetTokenByArray(string json, string dataName, bool isAll = false)
        {
            var obj = JArray.Parse(json);
            string token = null;
            bool isMerge = dataName?.Equals("|Merge|") == true;
            foreach (var x in obj)
            {
                try
                {
                    if (x is JArray)
                    {
                        foreach (var y in x)
                        {
                            var tmpStr = isMerge ? y.ToString() : GetTokenByName(y.ToString(), dataName, isAll);
                            if (isAll)
                            {
                                token += tmpStr?.Trim().TrimStart('[').TrimEnd(']').Trim() + ",";
                            }
                            else
                            {
                                token = tmpStr;
                            }
                        }
                    }
                    else
                    {
                        var tmpStr = "";
                        if (x is JObject)
                        {
                            tmpStr = isMerge ? x.ToString() : GetTokenByName((x as JObject).ToString(), dataName, isAll);
                        }
                        else
                        {
                            tmpStr = x.ToString();
                        }
                        if (string.IsNullOrEmpty(tmpStr))
                        {
                            continue;
                        }
                        if (isAll)
                        {
                            token += tmpStr?.Trim().TrimStart('[').TrimEnd(']').Trim() + ",";
                        }
                        else
                        {
                            token = tmpStr;
                        }
                    }
                }
                catch (Exception oe)
                {
                    Console.WriteLine(oe.Message);
                }
                if (!isAll && token != null)
                {
                    break;
                }
            }
            return token?.Trim();
        }

        public static string GetTokenByName(string json, string dataName, bool isAll = false)
        {
            if (json.StartsWith("["))
            {
                return GetTokenByArray(json, dataName, isAll);
            }
            var obj = JObject.Parse(json);
            string token = null;
            bool isReplace = dataName.Contains("|R|");
            dataName = dataName.Replace("|R|", "");
            bool isMerge = dataName.Equals("|Merge|");
            bool isProcessOthers = obj.GetValue(dataName) == null;
            foreach (var x in obj)
            {
                if (isMerge)
                {
                    token += x.Value?.ToString().Trim() + ",";
                    continue;
                }
                if (x.Key.Equals(dataName))
                {
                    if (isReplace)
                    {
                        if (x.Value.GetType() == typeof(JArray))
                        {
                            var array = JArray.Parse(x.Value.ToString());
                            if (array.Count > 0)
                            {
                                obj[x.Key] = array[0];
                            }
                        }
                        break;
                    }
                    else
                    {
                        token = x.Value.ToString();
                    }
                }
                else if (isProcessOthers)
                {
                    if (x.Value.GetType() == typeof(JObject))
                    {
                        token = GetTokenByName(x.Value.ToString(), dataName, isAll);
                    }
                    else if (x.Value.GetType() == typeof(JArray))
                    {
                        try
                        {
                            token = GetTokenByArray(x.Value.ToString(), dataName, isAll);
                        }
                        catch (Exception oe)
                        {
                            Console.WriteLine(oe.Message);
                        }
                    }
                }
                if (token != null)
                {
                    break;
                }
            }
            if (isReplace && token == null)
            {
                token = obj.ToString();
            }
            return token?.Trim();
        }

        public static string GetPreResultByList(JToken token, List<object> lstSpilt)
        {
            string result = null;
            if (lstSpilt?.Count > 0)
            {
                //var body = JArray.Parse(JObject.Parse(html)["resultTables"][0]["resultCells"].ToString());
                for (int i = 0; i < lstSpilt.Count; i++)
                {
                    try
                    {
                        var item = lstSpilt[i].ToString();
                        if (lstSpilt[i] is string)
                        {
                            if (item.EndsWith("|S"))
                            {
                                item = item.Replace("|S", "");
                                token = JObject.Parse(token[item].ToString());
                            }
                            else if (item.Contains("|CONTACT|"))
                            {
                                var items = item.Split(new[] { "|CONTACT|" }, StringSplitOptions.RemoveEmptyEntries);
                                foreach (var row in items)
                                {
                                    result += "\"" + row + "\":\"" + token[row] + "\",";
                                }
                            }
                            else if (item.Contains("|N|"))
                            {
                                var items = item.Split(new[] { "|N|" }, StringSplitOptions.RemoveEmptyEntries);
                                if (items.Length > 1)
                                {
                                    token = JArray.Parse(token[items[0]].ToString());
                                    foreach (var row in token)
                                    {
                                        result += row[items[1]] + "\n";
                                    }
                                }
                            }
                            else
                            {
                                token = token[item];
                            }
                        }
                        else if (lstSpilt[i] is int)
                        {
                            if (token.Count() > 0)
                            {
                                token = token[BoxUtil.GetInt32FromObject(item)];
                            }
                            else
                            {
                                break;
                            }
                        }
                        if (token != null && i == lstSpilt.Count - 1 && string.IsNullOrEmpty(result))
                        {
                            result = token.ToString();
                        }
                    }
                    catch (Exception oe)
                    {
                        Console.WriteLine(oe.Message);
                    }
                }
            }
            return result?.Trim();
        }

        /// <summary>
        /// 计算表格分割
        /// </summary>
        /// <param name="pixellist"></param>
        /// <param name="thresholdtozero"></param>
        /// <returns></returns>
        private static List<int> GetZeroIndexs(int[] pixellist, int thresholdtozero)
        {
            List<int> zerolist = new List<int> { 0 };
            for (int i = 0; i < pixellist.Length; i++)
            {
                if ((i < pixellist.Length - 1)
                    && (Math.Abs(pixellist[i + 1] - pixellist[i])) > thresholdtozero)
                {
                    //突增点
                    zerolist.Add(i + 1);
                }
            }
            return zerolist;
        }

        /// <summary>
        ///结构化文本识别(临时解决方案，需要的请研究官方的结构化识别)
        /// </summary>
        /// <param name="image">图像</param>
        /// <param name="parameter">参数</param>
        /// <returns></returns>
        public static TableContentInfo DetectTableFromOcrResult(ResultEntity ocrResult)
        {
            var tableResult = new TableContentInfo();

            if (ocrResult == null || string.IsNullOrEmpty(ocrResult.verticalText))
                return tableResult;

            List<TextCellInfo> blocks = JsonConvert.DeserializeObject<List<TextCellInfo>>(ocrResult.verticalText);
            if (blocks == null || blocks.Count == 0) return tableResult;

            var listys = GetZeroIndexs(blocks.OrderBy(x => x.location.top).Select(x => (int)x.location.top).ToArray(), 10);
            var listxs = GetZeroIndexs(blocks.OrderBy(x => x.location.left).Select(x => (int)x.location.left).ToArray(), 10);

            int rowcount = listys.Count;
            int colcount = listxs.Count;
            tableResult.rows = new List<TableRow>();
            for (int i = 0; i < rowcount; i++)
            {
                var row = new TableRow
                {
                    index = i + 1,
                    cells = new List<TableCell>()
                };
                var y_min = blocks.OrderBy(x => x.location.top).OrderBy(x => x.location.top).ToList()[listys[i]].location.top;
                var y_max = 99999;
                if (i < rowcount - 1)
                {
                    y_max = (int)blocks.OrderBy(x => x.location.top).ToList()[listys[i + 1]].location.top;
                }

                for (int j = 0; j < colcount; j++)
                {
                    var x_min = blocks.OrderBy(x => x.location.left).ToList()[listxs[j]].location.left;
                    var x_max = 99999;

                    if (j < colcount - 1)
                    {
                        x_max = (int)blocks.OrderBy(x => x.location.left).ToList()[listxs[j + 1]].location.left;
                    }

                    var textBlocks = blocks.Where(x => x.location.left < x_max && x.location.left >= x_min && x.location.top < y_max && x.location.top >= y_min).OrderBy(u => u.location.left);
                    var texts = textBlocks.Select(x => x.words).ToArray();

                    var cell = new TableCell
                    {
                        index = j + 1,
                        content = string.Join<string>("", texts)
                    };
                    row.cells.Add(cell);
                }
                tableResult.rows.Add(row);
            }
            return tableResult;
        }

    }
}
