﻿using CommonLib.UserConfig;
using System;
using System.Collections.Generic;
using System.Linq;

namespace CommonLib
{
    public class UserType
    {
        public UserTypeEnum Type { get; set; }


        /// <summary>
        /// 指定时间段内（毫秒）
        /// </summary>
        [System.Xml.Serialization.XmlIgnore]
        [System.Web.Script.Serialization.ScriptIgnore]
        public int PerTimeSpan { get; set; }

        /// <summary>
        /// 指定时间段内执行次数
        /// </summary>
        [System.Xml.Serialization.XmlIgnore]
        [System.Web.Script.Serialization.ScriptIgnore]
        public int PerTimeSpanExecCount { get; set; }

        /// <summary>
        /// 是否显示其他处理结果
        /// </summary>
        public bool IsSetOtherResult { get; set; }

        /// <summary>
        /// 是否支持文本识别
        /// </summary>
        public bool IsSupportTxt { get; set; } = true;

        /// <summary>
        /// 是否支持翻译
        /// </summary>
        public bool IsSupportTranslate { get; set; }

        /// <summary>
        /// 是否支持图片文件识别
        /// </summary>
        public bool IsSupportImageFile { get; set; }

        /// <summary>
        /// 是否支持文档翻译
        /// </summary>
        public bool IsSupportDocFile { get; set; }

        /// <summary>
        /// 能否批量处理文件
        /// </summary>
        public bool IsSupportBatch { get; set; }

        /// <summary>
        /// 是否支持竖排识别
        /// </summary>
        public bool IsSupportVertical { get; set; }

        /// <summary>
        /// 是否支持数学公式识别
        /// </summary>
        public bool IsSupportMath { get; set; }

        /// <summary>
        /// 是否支持表格识别
        /// </summary>
        public bool IsSupportTable { get; set; }

        /// <summary>
        /// 能否多点登录
        /// </summary>
        public int MaxLoginCount { get; set; } = 1;

        /// <summary>
        /// 后台单次并行执行次数
        /// </summary>

        [System.Xml.Serialization.XmlIgnore]
        [System.Web.Script.Serialization.ScriptIgnore]
        public int ProcessPerTime { get; set; } = 1;

        /// <summary>
        /// 是否开放申请
        /// </summary>

        [System.Xml.Serialization.XmlIgnore]
        [System.Web.Script.Serialization.ScriptIgnore]
        public bool IsOpen { get; set; } = true;

        /// <summary>
        /// 单月价格
        /// </summary>
        public double PerPriceMonth { get; set; }

        /// <summary>
        /// 季度优惠
        /// </summary>
        public double QuarDiscount { get; set; } = 1;

        /// <summary>
        /// 年费优惠
        /// </summary>
        public double YearDiscount { get; set; } = 1;

        [System.Xml.Serialization.XmlIgnore]
        [System.Web.Script.Serialization.ScriptIgnore]
        public PriceType PriceType { get; set; }

        /// <summary>
        /// 单位价格
        /// </summary>

        [System.Xml.Serialization.XmlIgnore]
        [System.Web.Script.Serialization.ScriptIgnore]
        public double PerPrice { get; set; }

        /// <summary>
        /// 收费类型
        /// </summary>

        [System.Xml.Serialization.XmlIgnore]
        [System.Web.Script.Serialization.ScriptIgnore]
        public List<ChargeType> ChargeTypes { get; set; }

        public List<ChargeViewToUser> UserChargeType { get; set; }

        /// <summary>
        /// 最大可上传的识别文件
        /// </summary>
        public int MaxUploadSize { get; set; } = 300;

        [System.Xml.Serialization.XmlIgnore]
        [System.Web.Script.Serialization.ScriptIgnore]
        public string LimitDesc
        {
            get
            {
                string desc;
                switch (Type)
                {
                    case UserTypeEnum.体验版:
                        if (ConfigHelper.IsRegToProfessional)
                        {
                            desc = "\n现在注册会员，即送1个月[企业版]！\n支持文本，竖排，表格，公式，翻译等！\n速度更快，功能更强，识别精度更高！";
                        }
                        else
                        {
                            desc = "\n请考虑升级为[专业版]！\n支持文本，竖排，公式等！速度更快，功能更强，识别精度更高！";
                        }
                        break;
                    case UserTypeEnum.专业版:
                        desc = "\n如果需求量大，请考虑升级为[企业版]或[旗舰版]！\n支持表格，文档翻译，文档转换，文档导出等！\n速度更快，功能更强，识别精度更高！";
                        break;
                    case UserTypeEnum.企业版:
                        desc = "\n如果需求量大，请考虑升级为[旗舰版]！\n支持文档翻译，文档转换，文档导出等！\n速度更快，功能更强，识别精度更高！";
                        break;
                    case UserTypeEnum.旗舰版:
                        desc = "\n如果需求量大，请联系我们定制！";
                        break;
                    default:
                        desc = "\n请稍后重试！";
                        break;
                }
                if (!string.IsNullOrEmpty(ConfigHelper.TipMsg))
                {
                    desc += "\n\n" + ConfigHelper.TipMsg;
                }
                return string.Format("{0}用户限制识别频率为:{1}秒/次！{2}"
                    , Type
                    , Math.Floor(PerTimeSpan * 1d / 1000).ToString("F0")
                    , desc);
            }
        }
    }

    public class ChargeViewToUser
    {
        public string Name { get; set; }

        public string Desc { get; set; }

        public double Price { get; set; }

        public bool IsDefault { get; set; }

        public string Tag { get; set; }
    }

    public enum UserTypeEnum
    {
        内测版 = -1,
        体验版 = 0,
        专业版 = 1,
        企业版 = 2,
        旗舰版 = 3,
    }

    public class UserTypeHelper
    {
        static UserTypeHelper()
        {
            lstUserType.Add(new UserType()
            {
                Type = UserTypeEnum.体验版,
                PerTimeSpanExecCount = 1,
                PerTimeSpan = 30 * 1000,
                IsOpen = false
            });
            lstUserType.Add(new UserType()
            {
                Type = UserTypeEnum.内测版,
                PerTimeSpanExecCount = 1,
                PerTimeSpan = 2 * 1000,
                ProcessPerTime = 2,
                IsSupportTxt = true,
                IsSupportVertical = true,
                IsSupportMath = true,
                IsSupportTable = true,
                IsSetOtherResult = true,
                IsSupportBatch = true,
                IsSupportImageFile = true,
                IsSupportDocFile = true,
                IsSupportTranslate = true,
                IsOpen = false,
            });
            var zhuanYe = new UserType()
            {
                Type = UserTypeEnum.专业版,
                PriceType = PriceType.按年,
                PerPrice = 100,
                PerTimeSpanExecCount = 1,
                PerTimeSpan = 5 * 1000,
                ProcessPerTime = 2,
                IsSetOtherResult = true,
                IsSupportTxt = true,
                IsSupportImageFile = true,
                IsSupportVertical = true,
                IsSupportMath = true,
                IsSupportBatch = false,
                IsSupportTable = true,
                IsSupportDocFile = false,
                IsSupportTranslate = false,
                PerPriceMonth = 10,
                QuarDiscount = 0.9d,
                YearDiscount = 0.8d,
                MaxLoginCount = 2,
            };
            lstUserType.Add(zhuanYe);
            var qiYe = new UserType()
            {
                Type = UserTypeEnum.企业版,
                PriceType = PriceType.按年,
                PerPrice = 100,
                PerTimeSpanExecCount = 1,
                PerTimeSpan = 3 * 1000,
                ProcessPerTime = 3,
                IsSetOtherResult = true,
                IsSupportTxt = true,
                IsSupportImageFile = true,
                IsSupportVertical = true,
                IsSupportMath = true,
                IsSupportBatch = true,
                IsSupportTable = true,
                IsSupportDocFile = false,
                IsSupportTranslate = true,
                PerPriceMonth = 20,
                QuarDiscount = 0.85d,
                YearDiscount = 0.8d,
                MaxLoginCount = 5,
            };
            lstUserType.Add(qiYe);
            var qiJian = new UserType()
            {
                Type = UserTypeEnum.旗舰版,
                PerTimeSpanExecCount = 1,
                PerTimeSpan = 1 * 1000,
                ProcessPerTime = 3,
                IsSetOtherResult = true,
                IsSupportTxt = true,
                IsSupportImageFile = true,
                IsSupportVertical = true,
                IsSupportMath = true,
                IsSupportBatch = true,
                IsSupportTable = true,
                IsSupportDocFile = true,
                IsSupportTranslate = true,
                PerPriceMonth = 30,
                QuarDiscount = 0.85d,
                YearDiscount = 0.8d,
                MaxLoginCount = 10,
            };
            lstUserType.Add(qiJian);
            try
            {
                ChargeTypeConfigurationSectionHandler.ChargeTypes.UserTypeSettings.ForEach(p =>
                {
                    lstUserType.Find(q => Equals(q.Type, p.UserType)).PriceType = p.PriceType;
                    lstUserType.Find(q => Equals(q.Type, p.UserType)).PerPrice = p.PerPrice;
                    lstUserType.Find(q => Equals(q.Type, p.UserType)).ChargeTypes = p.ChargeTypes;
                });
            }
            catch { }
        }

        private static List<UserType> lstUserType = new List<UserType>();

        /// <summary>
        /// 获取可以注册的用户类别
        /// </summary>
        /// <returns></returns>
        public static List<UserType> GetCanRegUserTypes()
        {
            return lstUserType.Where(p => p.IsOpen).OrderBy(p => p.PerPriceMonth).ToList();
        }

        public static UserType GetUserTypeByAccount(string account, string token)
        {
            UserTypeEnum userType = UserTypeEnum.体验版;
            if (!string.IsNullOrEmpty(account) && !string.IsNullOrEmpty(token))
            {
                RdsCacheHelper.LstAccountCache.ValidateUser(account, token, ref userType);
            }
            var result = lstUserType.Find(p => Equals(p.Type, userType));
            return result;
        }

        public static UserType GetUserInfo(UserTypeEnum userType)
        {
            var result = lstUserType.FirstOrDefault(p => Equals(p.Type, userType));
            return result;
        }

        public static string GetLimitInfo(UserTypeEnum userType)
        {
            var result = lstUserType.Find(p => Equals(p.Type, userType));
            return result?.LimitDesc;
        }
    }
}
