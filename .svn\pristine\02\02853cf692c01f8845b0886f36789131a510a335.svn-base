﻿namespace CommonLib
{
    public static class RdsCacheHelper
    {
        public static IpLimitManager LimitHelper { get; set; }
        public static UserLoginCache LstAccountCache { get; set; }

        static RdsCacheHelper()
        {
            LstAccountCache = new UserLoginCache();
        }

        public static void InitLimiter()
        {
            LimitHelper = new IpLimitManager(ConfigHelper.NMaxExecPerSecond, ConfigHelper.NBlackMSencond, ConfigHelper.NMaxExecBlackSecond, ConfigHelper.IsValidateCodeAmount, false);
        }

        private static YuECache yuECache;

        public static YuECache YuECache
        {
            get
            {
                if (yuECache == null)
                {
                    yuECache = new YuECache();
                }
                return yuECache;
            }
            set { yuECache = value; }
        }

        private static UserCodeRecordCache codeRecordCache;

        public static UserCodeRecordCache CodeRecordCache
        {
            get
            {
                if (codeRecordCache == null)
                {
                    codeRecordCache = new UserCodeRecordCache();
                }
                return codeRecordCache;
            }
            set { codeRecordCache = value; }
        }

        //private static CollectImageQueue collectImageQueue;

        ///// <summary>
        ///// 图片收集
        ///// </summary>
        //public static CollectImageQueue CollectImageQueue
        //{
        //    get
        //    {
        //        if (collectImageQueue == null)
        //        {
        //            collectImageQueue = new CollectImageQueue();
        //        }
        //        return RdsCacheHelper.collectImageQueue;
        //    }
        //    set { RdsCacheHelper.collectImageQueue = value; }
        //}

        private static NoticeQueue noticeQueue;

        /// <summary>
        /// 消息池
        /// </summary>
        public static NoticeQueue NoticeQueue
        {
            get
            {
                if (noticeQueue == null)
                {
                    noticeQueue = new NoticeQueue();
                }
                return RdsCacheHelper.noticeQueue;
            }
            set { RdsCacheHelper.noticeQueue = value; }
        }

        private static DaMaQueue daMaQueue;

        /// <summary>
        /// 打码池
        /// </summary>
        public static DaMaQueue OcrProcessQueue
        {
            get
            {
                if (daMaQueue == null)
                {
                    daMaQueue = new DaMaQueue();
                }
                return RdsCacheHelper.daMaQueue;
            }
            set { RdsCacheHelper.daMaQueue = value; }
        }

        private static OcrResultCache ocrResult;

        /// <summary>
        /// OCR结果
        /// </summary>
        public static OcrResultCache OcrResult
        {
            get
            {
                if (ocrResult == null)
                {
                    ocrResult = new OcrResultCache();
                }
                return RdsCacheHelper.ocrResult;
            }
            set { RdsCacheHelper.ocrResult = value; }
        }

        private static ServerStateCache serverStateCache;

        /// <summary>
        /// Server状态
        /// </summary>
        public static ServerStateCache ServerStateCache
        {
            get
            {
                if (serverStateCache == null)
                {
                    serverStateCache = new ServerStateCache();
                }
                return RdsCacheHelper.serverStateCache;
            }
            set { RdsCacheHelper.serverStateCache = value; }
        }

        private static ServerLogCache serverLogCache;

        /// <summary>
        /// Server状态
        /// </summary>
        public static ServerLogCache ServerLogCache
        {
            get
            {
                if (serverLogCache == null)
                {
                    serverLogCache = new ServerLogCache();
                }
                return RdsCacheHelper.serverLogCache;
            }
            set { RdsCacheHelper.serverLogCache = value; }
        }

        private static ObjectMessageQueue fileStatusProcessQueue;

        /// <summary>
        /// 文件下载状态Queue
        /// </summary>
        public static ObjectMessageQueue FileStatusProcessQueue
        {
            get
            {
                if (fileStatusProcessQueue == null)
                {
                    fileStatusProcessQueue = new ObjectMessageQueue("FileStatusProcessQueue");
                }
                return RdsCacheHelper.fileStatusProcessQueue;
            }
            set { RdsCacheHelper.fileStatusProcessQueue = value; }
        }

        private static ObjectMessageQueue fileStatusResultQueue;

        /// <summary>
        /// 文件下载状态Queue
        /// </summary>
        public static ObjectMessageQueue FileStatusResultQueue
        {
            get
            {
                if (fileStatusResultQueue == null)
                {
                    fileStatusResultQueue = new ObjectMessageQueue("FileStatusResultQueue");
                }
                return RdsCacheHelper.fileStatusResultQueue;
            }
            set { RdsCacheHelper.fileStatusResultQueue = value; }
        }

        private static DaMaCache daMaHistory;

        /// <summary>
        /// 打码量统计
        /// </summary>
        public static DaMaCache DaMaHistory
        {
            get
            {
                if (daMaHistory == null)
                {
                    daMaHistory = new DaMaCache();
                }
                return RdsCacheHelper.daMaHistory;
            }
            set { RdsCacheHelper.daMaHistory = value; }
        }

        //private static CountCache countCache;

        ///// <summary>
        ///// 计数
        ///// </summary>
        //public static CountCache CountCache
        //{
        //    get
        //    {
        //        if (countCache == null)
        //        {
        //            countCache = new CountCache();
        //        }
        //        return RdsCacheHelper.countCache;
        //    }
        //    set { RdsCacheHelper.countCache = value; }
        //}

        //private static ListCache<string> listCache;

        ///// <summary>
        ///// 汉字特征库
        ///// </summary>
        //public static ListCache<string> ListCache
        //{
        //    get
        //    {
        //        if (listCache == null)
        //        {
        //            listCache = new ListCache<string>();
        //        }
        //        return RdsCacheHelper.listCache;
        //    }
        //    set { RdsCacheHelper.listCache = value; }
        //}

        //private static ImageCache imgCache;

        ///// <summary>
        ///// 图片特征库
        ///// </summary>
        //public static ImageCache ImgCache
        //{
        //    get
        //    {
        //        if (imgCache == null)
        //        {
        //            imgCache = new ImageCache();
        //        }
        //        return RdsCacheHelper.imgCache;
        //    }
        //    set { RdsCacheHelper.imgCache = value; }
        //}

        //public static StrCache StrCache { get; set; }

        //private static ProcessCache processQueue;

        ///// <summary>
        ///// 正在处理池
        ///// </summary>
        //public static ProcessCache ProcessQueue
        //{
        //    get
        //    {
        //        if (processQueue == null)
        //        {
        //            processQueue = new ProcessCache();
        //        }
        //        return RdsCacheHelper.processQueue;
        //    }
        //    set { RdsCacheHelper.processQueue = value; }
        //}

        //private static ListCache<OcrContent> ocrCache;

        ///// <summary>
        ///// 汉字特征库
        ///// </summary>
        //public static ListCache<OcrContent> OcrCache
        //{
        //    get
        //    {
        //        if (ocrCache == null)
        //        {
        //            ocrCache = new ListCache<OcrContent>();
        //        }
        //        return RdsCacheHelper.ocrCache;
        //    }
        //    set { RdsCacheHelper.ocrCache = value; }
        //}
    }
}