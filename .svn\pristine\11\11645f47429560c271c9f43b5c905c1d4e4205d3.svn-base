using System.Collections.Generic;
using System.Linq;
using Enterprise.Framework.Redis.Config;

namespace Enterprise.Framework.Redis
{
	public static class RedisClientConfigurationSetting
	{
		private static Dictionary<string, IList<string>> selectKeyDic;

		static RedisClientConfigurationSetting()
		{
			selectKeyDic = new Dictionary<string, IList<string>>();
		}

		public static IList<string> GetAllRedisNames()
		{
			return RedisConfigReader.DBServers.Keys.ToList();
		}

		public static IList<string> GetRedisNamesByPatten(string patten)
		{
			if (selectKeyDic.ContainsKey(patten))
			{
				return selectKeyDic[patten];
			}
			lock (selectKeyDic)
			{
				if (!selectKeyDic.ContainsKey(patten))
				{
					IList<string> list = (from p in GetAllRedisNames()
						where p.Contains(patten)
						select p).ToList();
					selectKeyDic.Add(patten, list);
					return list;
				}
				return selectKeyDic[patten];
			}
		}
	}
}
