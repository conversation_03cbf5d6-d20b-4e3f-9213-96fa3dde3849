﻿<%@ Page Title="JSON在线格式化工具 | 免费JSON美化与校验" Language="C#" MasterPageFile="~/tool/Tool.Master" AutoEventWireup="true" %>
<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="server">
    <link rel="stylesheet" href="static/css/index-b05f4643.css">
    <script type="text/javascript" src="static/js/vue.js"></script>
    <meta name="keywords" content="在线,JSON,JSON 校验,格式化,xml转json 工具,在线工具,json视图,可视化,正则表达式,测试,在线json格式化工具,json 格式化,json格式化工具,json字符串格式化,json 在线查看器,json在线,json 在线验证,json tools online,在线文字对比工具,json解析" />
    <meta name="description" content="JSON在线格式化工具,提供强大的JSON解析、验证、格式化、压缩、编辑器功能。支持JSON与XML相互转换、节点编辑、排序和自动解码等功能，帮助开发者高效处理JSON数据。"/>
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=5.0" />
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="server">
        <div class="panel panel-default" style="margin-bottom: 0px;">
            <div class="panel-heading">
                <h1 class="panel-title" style="font-size: 18px; margin: inherit;">
                    JSON在线格式化工具<span class="x-xdemo" ref="demoLink1" @click="setDemo" role="button" tabindex="0" aria-label="加载JSON示例">示例1：JSON片段</span>
                    <span id="layoutBar">
                        <button id="btnLeftRight" ref="btnLeftRight" class="selected" @click="changeLayout('left-right')">左右布局</button>
                        <button id="btnUpDown" ref="btnUpDown" @click="changeLayout('up-down')">上下布局</button>
                    </span></h3>
            </div>
        </div>
        <div class="x-toolbar x-inpage">
            <button id="btnFormat" class="btn btn-primary btn-xs ui-mr-10" @click="format">格式化</button>
            <button id="btnCompress" class="btn btn-success btn-xs" @click="compress">压缩</button>
            <span class="x-split">|</span>
            <input type="checkbox" v-model="jsonLintSwitch" id="jsonLint" @click="lintOn">
            <label for="jsonLint">JSONLint</label> <span class="x-split">|</span>
            <input type="checkbox" v-model="autoDecode" id="endecode" @click="autoDecodeFn">
            <label for="endecode">自动解码</label> <span class="x-split">|</span>
            <input type="checkbox" v-model="overrideJson" id="jsonOvrd" @click="setCache">
            <label for="jsonOvrd">节点编辑</label> <span class="x-split">|</span>  <span class="x-sort">
                <span class="x-stitle">排序：</span>
                <label for="sort_null">默认</label>
                <input type="radio" name="jsonsort" id="sort_null" value="0" checked="checked" @click="format">
                <label for="sort_asc">升序</label>
                <input type="radio" name="jsonsort" id="sort_asc" value="1" @click="format">
                <label for="sort_desc">降序</label>
                <input type="radio" name="jsonsort" id="sort_desc" value="-1" @click="format">
            </span><span class="x-split">|</span>  <span class="x-endecode"><button class="xjf-btn xjf-btn-left" @click="uniEncode">Uni编码</button><button class="xjf-btn xjf-btn-mid" @click="uniDecode">Uni解码</button><button class="xjf-btn xjf-btn-right" @click="urlDecode">URL解码</button> </span>
            <span id="optionBar"></span>
        </div>
        <div class="panel-body mod-json">
            <div class="row panel-txt">
                <textarea class="form-control mod-textarea" id="jsonSource" placeholder="在这里粘贴您需要进行格式化的JSON代码" ref="jsonBox"></textarea>
            </div>
            <div class="row rst-item" id="modJsonResult">
                <div id="formattingMsg"><span class="x-loading"></span>格式化中...</div>
                <div id="jfCallbackName_start" class="callback-name" v-html="jfCallbackName_start"></div>
                <div id="jfContent" v-html="placeHolder"></div><pre id="jfContent_pre"></pre>
                <div id="jfCallbackName_end" class="callback-name" v-html="jfCallbackName_end"></div>
            </div>
        </div>
    <script src="static/js/jquery-3.3.1.min.js"></script>
    <script src="static/js/codemirror-json.js"></script>
    <script src="static/js/javascript.js"></script>
    <script src="static/js/active-line.js"></script>
    <script src="static/js/matchbrackets.js"></script>
    <script src="static/js/placeholder.js"></script>
    <script src="static/js/json-lint.js"></script>
    <script src="static/js/content-script-cc14877d.js"></script>
    <script src="static/js/index-065782ae.js" type="module"></script>
</asp:Content>
