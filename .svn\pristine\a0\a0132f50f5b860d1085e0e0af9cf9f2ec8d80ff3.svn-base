﻿using System;
using System.IO;
using System.Security.Cryptography;
using System.Text;
using Microsoft.Win32;
using System.Management;
using System.Diagnostics;

namespace CommonLib
{
    public class ServerUuidUtil
    {
        public static ServerClientEntity GetServerInfo()
        {
            var server = new ServerClientEntity()
            {
                DomainName = Environment.UserDomainName,
                MachineName = Environment.MachineName,
                UserName = Environment.UserName,
                ProcessorCount = Environment.ProcessorCount,
                Is64BitOperatingSystem = Environment.Is64BitOperatingSystem,
                MachineId = GetMachineId(),
                MachineGuid = GetMachineGuid(),
                CreateTime = Directory.GetCreationTime(AppDomain.CurrentDomain.BaseDirectory),
                MemoryUsed = (Environment.WorkingSet / 1024 / 1024).ToString() + " MB",
                OS = Environment.OSVersion.ToString(),
            };

            server.Id = ToMd5(server.MachineId + server.MachineGuid);
            return server;
        }

        public static string GetMachineId()
        {
            var result = string.Empty;
            try
            {
                var mos = new ManagementObjectSearcher("SELECT UUID FROM Win32_ComputerSystemProduct");
                foreach (ManagementBaseObject mo in mos.Get())
                {
                    result = mo["UUID"] as string;
                }
            }
            catch
            {
            }

            return result;
        }

        private static string GetMachineGuid()
        {
            var result = string.Empty;
            try
            {
                var baseKey = RegistryKey.OpenBaseKey(RegistryHive.LocalMachine, Environment.Is64BitOperatingSystem ? RegistryView.Registry64 : RegistryView.Registry32);
                using (var subKey = baseKey.OpenSubKey(@"SOFTWARE\Microsoft\Cryptography", RegistryKeyPermissionCheck.ReadSubTree))
                {
                    if (subKey != null)
                    {
                        var value = subKey.GetValue("MachineGuid", "default");
                        if (value != null && value.ToString() != "default")
                        {
                            result = value.ToString();
                        }
                        subKey.Close();
                        subKey.Dispose();
                    }
                    baseKey.Close();
                    baseKey.Dispose();
                }
            }
            catch (Exception)
            {
            }

            return result;
        }

        static string ToMd5(string s, int len = 32)
        {
            using (var md5Hasher = new MD5CryptoServiceProvider())
            {
                var data = md5Hasher.ComputeHash(Encoding.UTF8.GetBytes(s));
                var sb = new StringBuilder();
                foreach (var t in data)
                {
                    sb.Append(t.ToString("x2"));
                }
                var result = sb.ToString();

                return len == 32 ? result : result.Substring(8, 16);
            }
        }
    }
    public class ServerClientEntity
    {
        public string Id { get; set; }

        public string DomainName { get; set; }

        public string UserName { get; set; }

        public string MachineId { get; set; }

        public int ProcessorCount { get; set; }
        public bool Is64BitOperatingSystem { get; set; }
        public string MachineName { get; set; }
        public DateTime CreateTime { get; set; }
        public string MemoryUsed { get; set; }
        public string MachineGuid { get; set; }
        public string OS { get; set; }
    }
}
