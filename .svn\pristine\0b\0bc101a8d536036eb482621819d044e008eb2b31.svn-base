﻿using ImageCodeDB.Context;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ImageCodeDB
{
    public class Program
    {
        static void Main()
        {
            using (CodeContext context = new CodeContext())
            {
                var tmp = context.ImageEntities.FirstOrDefault(p => p.StrHanZi.Equals("红豆"));
                if (tmp != null)
                {
                    Console.WriteLine(tmp.NAverageGray);
                }
            }
        }
    }
}
