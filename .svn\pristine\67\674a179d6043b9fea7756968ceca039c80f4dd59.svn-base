﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <PackageId>ServiceStack.Kestrel.Core</PackageId>
    <AssemblyName>ServiceStack.Kestrel</AssemblyName>
    <RootNamespace>ServiceStack.Kestrel</RootNamespace>
    <TargetFrameworks>netstandard2.0;net6.0</TargetFrameworks>
    <Title>ServiceStack.Kestrel .NET Standard 2.0</Title>
    <PackageDescription>
      Provides AppSelfHostBase implementation for .NET Core's Kestrel Self Host HTTP Server

      To get started see: https://servicestack.net/getting-started
    </PackageDescription>
    <PackageTags>ServiceStack;SelfHost;Host;Kestrel;HTTP;Server</PackageTags>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(TargetFramework)' == 'netstandard2.0' ">
    <DefineConstants>$(DefineConstants);NETCORE;NETSTANDARD2_0</DefineConstants>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(TargetFramework)' == 'net6.0' ">
    <DefineConstants>$(DefineConstants);NETCORE;NET6_0;NET6_0_OR_GREATER</DefineConstants>
  </PropertyGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\..\ServiceStack.Text\src\ServiceStack.Text\ServiceStack.Text.Core.csproj" />
    <ProjectReference Include="..\ServiceStack.Interfaces\ServiceStack.Interfaces.Core.csproj"/>
    <ProjectReference Include="..\ServiceStack.Common\ServiceStack.Common.Core.csproj" />
    <ProjectReference Include="..\ServiceStack.Client\ServiceStack.Client.Core.csproj" />
    <ProjectReference Include="..\ServiceStack\ServiceStack.Core.csproj" />
    <PackageReference Include="System.Memory" Version="4.5.4" />
  </ItemGroup>

  <ItemGroup Condition=" '$(TargetFramework)' == 'netstandard2.0' ">
    <PackageReference Include="Microsoft.AspNetCore.Server.Kestrel" Version="2.2.0" />
    <PackageReference Include="Microsoft.AspNetCore.Http" Version="2.2.2" />
    <PackageReference Include="Microsoft.AspNetCore.Http.Abstractions" Version="2.2.0" />
    <PackageReference Include="Microsoft.AspNetCore.Http.Extensions" Version="2.2.0" />
    <PackageReference Include="Microsoft.AspNetCore.Hosting.Abstractions" Version="2.2.0" />
    <PackageReference Include="Microsoft.Extensions.Logging.Abstractions" Version="2.2.0" />
    <PackageReference Include="Microsoft.Extensions.DependencyInjection.Abstractions" Version="2.2.0" />
    <PackageReference Include="Microsoft.Extensions.Primitives" Version="2.2.0" />
  </ItemGroup>

  <ItemGroup Condition=" '$(TargetFramework)' == 'net6.0' ">
    <FrameworkReference Include="Microsoft.AspNetCore.App" />
  </ItemGroup>

</Project>
