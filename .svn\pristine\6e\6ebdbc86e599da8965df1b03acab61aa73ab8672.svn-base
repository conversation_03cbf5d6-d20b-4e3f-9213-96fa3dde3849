﻿using System.Collections.Generic;
using Enterprise.Framework.Redis;
using System;

namespace CommonLib
{
    public class UserLoginCache : RedisCacheObject<UserLoginInfo>
    {
        protected override string CurrentObject_KeyPrefix
        {
            get { return "LoginCache:"; }
        }

        protected override string Object_DBName
        {
            get { return "OPS_Cache"; }
        }

        public const string TestUser = "test";

        public string GetTestUserName(string token)
        {
            return string.Format("{0}:{1}", TestUser, token);
        }

        public UserLoginInfo GetUserInfo(string account)
        {
            var loginInfo = Get(account);
            if (loginInfo != null)
            {
                if (loginInfo.IsExpired && !Equals(loginInfo.UserType, UserTypeEnum.体验版))
                {
                    loginInfo.UserType = UserTypeEnum.体验版;
                }
                loginInfo.LstToken.RemoveAll(p => !p.IsValidate);
            }
            else
            {
                if (account.StartsWith(TestUser))
                {
                    loginInfo = new UserLoginInfo
                    {
                        Account = account,
                        UserType = UserTypeEnum.体验版,
                        LstToken = new List<TokenEntity>()
                    };
                }
            }
            return loginInfo;
        }

        public void InsertOrUpdateUser(UserLoginInfo loginInfo)
        {
            Insert(loginInfo.Account, loginInfo, ServerTime.DateTime.AddDays(1));
        }

        public bool HeartBeat(string account, string token)
        {
            bool result = false;
            if (!string.IsNullOrEmpty(account) && !string.IsNullOrEmpty(token))
            {
                var loginInfo = GetUserInfo(account);
                if (loginInfo != null)
                {
                    if (account.StartsWith(TestUser) && !loginInfo.LstToken.Exists(p => Equals(p.Token, token)))
                    {
                        loginInfo.LstToken.Add(new TokenEntity { Token = token });
                    }

                    if (loginInfo.LstToken.Exists(p => Equals(p.Token, token)))
                    {
                        result = true;
                        loginInfo.LstToken.Find(p => p.Token.Equals(token)).DtExpired = ServerTime.DateTime.AddHours(1);
                        loginInfo.DtLastHeat = ServerTime.DateTime;
                    }

                    InsertOrUpdateUser(loginInfo);
                }
            }
            return result;
        }

        public bool ValidateUserAndToken(string account, string token, ref UserTypeEnum userType)
        {
            bool result = false;
            if (!string.IsNullOrEmpty(account) && !string.IsNullOrEmpty(token))
            {
                var loginInfo = GetUserInfo(account);
                if (loginInfo != null)
                {
                    userType = loginInfo.UserType;
                    result = loginInfo.LstToken.Exists(p => Equals(p.Token, token));
                }
            }
            return result;
        }

        #region 读写缓存消息队列

        /// <summary>
        /// 将消息压入队列
        /// </summary>
        /// <param name="cacheKey"></param>
        /// <param name="message"></param>
        public void EnqueueCacheMessage(string cacheKey, string message, DateTime? dtExpired)
        {
            ExecuteRedisAction(redisClient =>
            {
                redisClient.EnqueueItemOnList(cacheKey, message ?? "");
                if (dtExpired.HasValue)
                    redisClient.ExpireEntryAt(cacheKey, dtExpired.Value);
            });
        }

        /// <summary>
        /// 永远等待的取消息，直到队列中有了消息
        /// </summary>
        /// <param name="cacheKey"></param>
        /// <returns></returns>
        public string DequeueBlockingCacheMessage(string cacheKey, int endBlockSeconds = 0)
        {
            //string reallyKey = cacheKey.ToCacheQueueKey();
            return ExecuteRedisFunc(redisClient =>
            {
                return redisClient.BlockingDequeueItemFromList(cacheKey, new TimeSpan(0, 0, endBlockSeconds));
            });
        }
        #endregion
    }

    public class UserLoginInfo
    {
        public string Account { get; set; }
        public string NickName { get; set; }

        public UserTypeEnum UserType { get; set; }

        public string UserTypeName { get; set; }

        public List<TokenEntity> LstToken { get; set; }

        public string Token { get; set; }

        public DateTime DtLogin { get; set; }

        public DateTime DtLastHeat { get; set; }

        public string Remark { get; set; }

        public DateTime DtExpired { get; set; }

        public bool IsExpired { get { return DtExpired < ServerTime.DateTime; } }

        public DateTime DtReg { get; set; }
        public bool IsSetOtherResult { get; set; }
        public bool IsSupportBatch { get; set; }
        public bool IsSupportMath { get; set; }

        /// <summary>
        /// 是否支持图片文件识别
        /// </summary>
        public bool IsSupportImageFile { get; set; }

        /// <summary>
        /// 是否支持文档翻译
        /// </summary>
        public bool IsSupportDocFile { get; set; }
        public bool IsSupportTable { get; set; }
        public bool IsSupportTxt { get; set; }
        public bool IsSupportVertical { get; set; }
        public bool IsSupportTranslate { get; set; }

        /// <summary>
        /// 是否支持通道切换
        /// </summary>
        public bool IsSupportPassage { get; set; }

        /// <summary>
        /// 最大可上传的识别文件
        /// </summary>
        public int MaxUploadSize { get; set; } = 300;

        /// <summary>
        /// 指定时间段内执行次数
        /// </summary>
        public int PerTimeSpanExecCount { get; set; } = 1;

        /// <summary>
        /// 指定时间段内（毫秒）
        /// </summary>
        public int PerTimeSpan { get; set; } = 5000;

        /// <summary>
        /// 单账户每天最大执行次数
        /// </summary>
        public long LimitPerDayCount { get; set; } = 10000;
    }

    public class TokenEntity
    {
        public string Token { get; set; }

        public DateTime DtExpired { get; set; }

        public bool IsValidate { get { return DtExpired > ServerTime.DateTime; } }
    }
}