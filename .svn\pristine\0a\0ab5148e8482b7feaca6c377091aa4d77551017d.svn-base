﻿
using CommonLib;
using System.Collections.Generic;

namespace MathOcr
{
    /// <summary>
    /// https://ai.youdao.com/product-photosearch.s
    /// </summary>
    public class YouDaoTiKuRec : BaseMathRec
    {
        public YouDaoTiKuRec()
        {
            OcrGroup = OcrGroupType.有道;
            OcrType = MathOcrType.有道题库;
            MaxExecPerTime = 29;
            IsSupportUrlOcr = true;

            LstJsonPreProcessArray = new List<object>() { "data" };
            StrResultJsonSpilt = "originOcr";
            IsSupportUrlOcr = true;
        }

        protected override string GetHtml(OcrContent content)
        {
            var strPost = "imgBase=data%3Aimage%2Fpng%3Bbase64%2C" + System.Web.HttpUtility.UrlEncode(content.strBase64);
            var strTmp = WebClientSyncExt.GetHtml("https://aidemo.youdao.com/ocr_question", strPost, ExecTimeOutSeconds);

            return strTmp;
        }

        public override string GetHtmlByUrl(OcrContent content)
        {
            content.url = content.url.Replace("?1.png", "");
            var strPost = "imgUrl=" + System.Web.HttpUtility.UrlEncode(content.url);
            var strTmp = WebClientSyncExt.GetHtml("https://aidemo.youdao.com/ocr_question_for_url", strPost, ExecTimeOutSeconds);

            return strTmp;
        }

    }
}