﻿using CommonLib;
using System;
using System.Collections.Generic;
using System.Collections.Specialized;
using System.IO;

namespace TransOcr
{
    /// <summary>
    /// 搜狗深智API-Demo
    /// https://deepi.sogou.com/doccenter/textcognitivedoc
    /// </summary>
    public class SouGouBroswerRec : BaseOcrRec
    {
        public SouGouBroswerRec()
        {
            OcrGroup = OcrGroupType.搜狗;
            OcrType = TransOcrType.搜狗浏览器;
            MaxExecPerTime = 25;

            LstJsonPreProcessArray = new List<object>() { "result" };
            StrResultJsonSpilt = "content";
            StrResultTransJsonSpilt = "trans_content";

            IsSupportVertical = true;
            LstVerticalLocation = new List<object>() { "frame" };
            InitLanguage();
        }

        #region 支持的语言

        //zh-CHS	简体中文	Chinese
        //en 英语  English
        //ru  俄语 Russian
        //ja 日语  Japanese
        //ko  韩语 Korean
        //fr 法语  French
        //de  德语 German
        //es 西班牙语    Spanish
        //pt  葡萄牙语

        private void InitLanguage()
        {
            TransLanguageDic = new Dictionary<TransLanguageTypeEnum, string>();
            TransLanguageDic.Add(TransLanguageTypeEnum.中文, "zh-CHS");
            TransLanguageDic.Add(TransLanguageTypeEnum.英文, "en");
            TransLanguageDic.Add(TransLanguageTypeEnum.俄语, "ru");
            TransLanguageDic.Add(TransLanguageTypeEnum.日语, "ja");
            TransLanguageDic.Add(TransLanguageTypeEnum.韩语, "ko");
            TransLanguageDic.Add(TransLanguageTypeEnum.法语, "fr");
            TransLanguageDic.Add(TransLanguageTypeEnum.德语, "de");
            TransLanguageDic.Add(TransLanguageTypeEnum.西班牙语, "es");
            TransLanguageDic.Add(TransLanguageTypeEnum.葡萄牙语, "pt");
        }

        #endregion


        protected override string GetHtml(OcrContent content)
        {
            var byt = Convert.FromBase64String(content.strBase64);
            string from = "", to = "";
            InitLanguage(content, ref from, ref to);

            string strTmp = PostFileResult(byt, content.fileExt, from, to);
            return strTmp;
        }

        private string PostFileResult(byte[] content, string fileExt, string from, string to)
        {
            var result = "";
            try
            {
                var url = "http://snap.ie.sogou.com/translateimg";
                var file = new UploadFileInfo()
                {
                    Name = "file",
                    Filename = "1." + fileExt,
                    ContentType = ApplicationTypeHelper.GetApplicationType(fileExt),
                    Stream = new MemoryStream(content)
                };
                var values = new NameValueCollection()
                {
                { "from",from},
                { "to",to},
                };
                var headers = new NameValueCollection() {
                { "Sec-Fetch-Site","cross-site"},
                { "Sec-Fetch-Mode","cors"},
            };
                result = PostFile(url, new[] { file
    }, values, headers);
            }
            catch (Exception)
            {

            }
            return result;
        }

    }
}