﻿using CommonLib;
using System;
using System.Collections.Generic;
using System.Collections.Specialized;
using System.Security.Cryptography;
using System.Text;

namespace TableOcr
{
    /// <summary>
    /// 有道API-本地计算Sign版-小程序
    /// http://ai.youdao.com/product-ocr.s
    /// http://ai.youdao.com/DOCSIRMA/html/%E6%96%87%E5%AD%97%E8%AF%86%E5%88%ABOCR/API%E6%96%87%E6%A1%A3/%E8%A1%A8%E6%A0%BCOCR%E6%9C%8D%E5%8A%A1/%E8%A1%A8%E6%A0%BCOCR%E6%9C%8D%E5%8A%A1-API%E6%96%87%E6%A1%A3.html
    /// </summary>
    public class YouDaoAPIRec : BaseTableRec
    {
        public YouDaoAPIRec()
        {
            OcrGroup = OcrGroupType.有道;
            OcrType = TableOcrType.有道API;
            MaxExecPerTime = 24;

            LstJsonPreProcessArray = new List<object>() { "Result", "tables", 0, "cells" };
            LstJsonResultProcessArray = new List<object>() { "lines|N|text" };
            LstRowIndex = new List<object>() { "rowRange" };
            LstColumnIndex = new List<object>() { "colRange" };
            RowIndexIsArray = false;
            IsRowIndexAddOne = true;
        }

        static List<YouDaoAccount> lstAppAccount = new List<YouDaoAccount>() {
        new YouDaoAccount(){
         strAppId = "2423360539ba5632",
          strSecretId =  "QQ8gLkYxtchLt6Osj1eXrsSDTus8N2Ru"
        },
        new YouDaoAccount(){
         strAppId = "712b0ae8fd3d404d",
          strSecretId =  "TF7ORXNiC6J3V18WZ4JCVYe2chHPVnRZ"
        }
        };

        protected override string GetHtml(OcrContent content)
        {
            var account = lstAppAccount.GetRndItem();
            var dic = new NameValueCollection();
            string salt = DateTime.Now.Millisecond.ToString();
            dic.Add("q", System.Web.HttpUtility.UrlEncode(content.strBase64));
            dic.Add("type", "1");
            dic.Add("docType", "json");
            dic.Add("signType", "v3");
            dic.Add("angle", "1");
            TimeSpan ts = (DateTime.UtcNow - new DateTime(1970, 1, 1, 0, 0, 0, DateTimeKind.Utc));
            long millis = (long)ts.TotalMilliseconds;
            string curtime = Convert.ToString(millis / 1000);
            dic.Add("curtime", curtime);
            string signStr = account?.strAppId + Truncate(content.strBase64) + salt + curtime + account?.strSecretId;
            string sign = ComputeHash(signStr);
            dic.Add("appKey", account?.strAppId);
            dic.Add("salt", salt);
            dic.Add("sign", sign);
            StringBuilder builder = new StringBuilder();
            int i = 0;
            foreach (string item in dic.Keys)
            {
                if (i > 0)
                    builder.Append("&");
                builder.AppendFormat("{0}={1}", item, dic[item]);
                i++;
            }
            var result = WebClientSyncExt.GetHtml("http://openapi.youdao.com/ocr_table", builder.ToString(), ExecTimeOutSeconds);

            //var dicContent = GetContentFromJson(result);
            return result;
        }


        protected static string ComputeHash(string input)
        {
            using (HashAlgorithm algorithm = new SHA256CryptoServiceProvider())
            {
                byte[] inputBytes = Encoding.UTF8.GetBytes(input);
                byte[] hashedBytes = algorithm.ComputeHash(inputBytes);
                return BitConverter.ToString(hashedBytes).Replace("-", "");
            }
        }

        protected static string Truncate(string q)
        {
            if (q == null)
            {
                return null;
            }
            int len = q.Length;
            return len <= 20 ? q : (q.Substring(0, 10) + len + q.Substring(len - 10, 10));
        }

    }
    class YouDaoAccount
    {
        public string strAppId { get; set; }

        public string strSecretId { get; set; }
    }
}