﻿using System;
using System.Threading;
using System.Threading.Tasks;
using Enterprise.Framework.Redis;

namespace CommonLib
{
    public class ApiCountCache : RedisCounterObject
    {
        protected override string CurrentObject_KeyPrefix
        {
            get { return "ApiCountCache"; }
        }

        protected override string Object_DBName
        {
            get { return "OPS_Cache"; }
        }

        public void AddCount(string key, int totalCount = 1)
        {
            this.Increment(key, totalCount);
        }

        public int GetCount(string key)
        {
            return Get(key);
        }
    }
}