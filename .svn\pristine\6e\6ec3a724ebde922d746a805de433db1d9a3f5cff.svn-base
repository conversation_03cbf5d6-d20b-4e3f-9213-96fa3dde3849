// Code generated by Microsoft (R) AutoRest Code Generator 1.0.1.0
// Changes may cause incorrect behavior and will be lost if the code is
// regenerated.

namespace AutorestClient.Models
{
    using Newtonsoft.Json;
    using System.Collections;
    using System.Collections.Generic;
    using System.Linq;

    /// <summary>
    /// GetMovie
    /// </summary>
    /// <remarks>
    /// GetMovie
    /// </remarks>
    public partial class GetMovie
    {
        /// <summary>
        /// Initializes a new instance of the GetMovie class.
        /// </summary>
        public GetMovie()
        {
          CustomInit();
        }

        /// <summary>
        /// Initializes a new instance of the GetMovie class.
        /// </summary>
        /// <param name="id">Required ID of Movie.</param>
        /// <param name="includes">List of additional objects to include in the
        /// movie response.</param>
        public GetMovie(long id = default(long), IList<string> includes = default(IList<string>))
        {
            Id = id;
            Includes = includes;
            CustomInit();
        }

        /// <summary>
        /// An initialization method that performs custom operations like setting defaults
        /// </summary>
        partial void CustomInit();

        /// <summary>
        /// Gets or sets required ID of Movie.
        /// </summary>
        [JsonProperty(PropertyName = "Id")]
        public long Id { get; set; }

        /// <summary>
        /// Gets or sets list of additional objects to include in the movie
        /// response.
        /// </summary>
        [JsonProperty(PropertyName = "Includes")]
        public IList<string> Includes { get; set; }

    }
}
