﻿using CommonLib;
using System;
using System.Collections.Generic;
using System.Collections.Specialized;
using System.Diagnostics;
using System.IO;
using System.Net;
using System.Text;
using System.Web;

namespace DocOcr
{
    /// <summary>
    /// 云译
    /// https://cloudtranslation.com/online/
    /// X-Authorization
    /// </summary>
    public class YunYiRec : BaseDocOcrRec
    {
        public YunYiRec()
        {
            OcrType = DocOcrType.云译;
            MaxExecPerTime = 20;
            AllowUploadFileTypes = new List<string>() { "pdf", "doc", "docx", "ppt", "pptx", "xls", "xlsx", "csv" };//, "txt" 
            IsSupportTrans = true;
            InitLanguage();
        }

        #region 支持的语言

        private void InitLanguage()
        {
            TransLanguageDic = new Dictionary<TransLanguageTypeEnum, string>();
            TransLanguageDic.Add(TransLanguageTypeEnum.中文, "zh-cn");
            TransLanguageDic.Add(TransLanguageTypeEnum.繁体中文, "zh-tw");
            TransLanguageDic.Add(TransLanguageTypeEnum.英文, "en");
            TransLanguageDic.Add(TransLanguageTypeEnum.日语, "ja");
            TransLanguageDic.Add(TransLanguageTypeEnum.德语, "de");
            TransLanguageDic.Add(TransLanguageTypeEnum.法语, "fr");
            TransLanguageDic.Add(TransLanguageTypeEnum.西班牙语, "es");
            TransLanguageDic.Add(TransLanguageTypeEnum.葡萄牙语, "pt");
            TransLanguageDic.Add(TransLanguageTypeEnum.俄语, "ru");
        }

        #endregion


        protected override string GetHtml(OcrContent content)
        {
            string from = "", to = "";
            InitLanguage(content, ref from, ref to);

            var transType = string.Format("{0}_{1}", from, to);

            var byt = Convert.FromBase64String(content.strBase64);
            var result = PostFileResult(byt, content.fileExt, transType);

            return result;
        }

        private const string strDocIDFullSpilt = "\"tdoc\": \"";
        private const string strDocIDByWordSpilt = "\"bdoc\": \"";
        private string PostFileResult(byte[] content, string fileExt, string transType)
        {
            var result = "";
            try
            {
                var url = "https://cloudtranslation.com/nmtdoc";
                var file = new UploadFileInfo()
                {
                    Name = "doc",
                    Filename = "1." + fileExt,
                    ContentType = ApplicationTypeHelper.GetApplicationType(fileExt),
                    Stream = new MemoryStream(content)
                };
                var values = new NameValueCollection() {
                { "lang",transType},
            };
                var headers = new NameValueCollection() {
                { "Sec-Fetch-Site","same-origin"},
                { "Sec-Fetch-Mode","cors"},
            };
                result = PostFile(url, new[] { file
    }, values, headers);
            }
            catch (Exception)
            {

            }
            return result;
        }

        protected override ResultEntity GetProcessText(OcrContent content, string html)
        {
            //{"status":2
            //"is_preview_docx_ready":true,"is_preview_pdf_ready":true,"is_preview_uncomparison_docx_ready":true,"is_preview_uncomparison_pdf_ready":true
            //"is_full_docx_ready":true,"is_full_pdf_ready":true,"is_full_uncomparison_docx_ready":true,"is_full_uncomparison_pdf_ready":true
            //,"preview_status":2}
            //result = "1";
            var entity = new ResultEntity()
            {
                files = new List<DownLoadInfo>(),
                resultType = ResutypeEnum.网页
            };

            var strByFull = CommonHelper.SubString(html, strDocIDFullSpilt, "\"");
            if (!string.IsNullOrEmpty(strByFull))
            {
                var fullFile = new DownLoadInfo()
                {
                    fileType = OcrFileType.Doc,
                    desc = "云译-逐句翻译",
                    url = "https://cloudtranslation.com/" + strByFull,
                };
                entity.files.Add(fullFile);
                entity.viewUrl = OnLineViewHelper.GetViewUrl(fullFile.url);
                var viewFile = new DownLoadInfo()
                {
                    fileType = OcrFileType.Doc,
                    desc = "在线预览",
                    url = entity.viewUrl,
                };
                entity.files.Add(viewFile);
                var strByWord = CommonHelper.SubString(html, strDocIDByWordSpilt, "\"");
                if (!string.IsNullOrEmpty(strByWord))
                {
                    var byWordFile = new DownLoadInfo()
                    {
                        fileType = OcrFileType.Doc,
                        desc = "云译-单词翻译",
                        url = "https://cloudtranslation.com/" + strByWord,
                    };
                    entity.files.Add(byWordFile);
                }
            }
            return entity;
        }

    }
}