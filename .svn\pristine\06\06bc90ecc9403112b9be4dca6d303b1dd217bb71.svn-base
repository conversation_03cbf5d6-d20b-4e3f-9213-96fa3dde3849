version: "3.9"
services:
  redis:
    image: redis
    ports:
      - 6379:6379
  postgres:
    image: postgres
    environment:
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: test
      POSTGRES_DB: test
    ports:
      - 48303:5432
  sqlserver:
    image: mcr.microsoft.com/mssql/server:2017-latest
    environment:
      ACCEPT_EULA: Y
      SA_PASSWORD: Test!tesT
      MSSQL_PID: Developer
    ports:
      - 48501:1433
  mysql:
    image: mysql:8.0.28
    environment:
      MYSQL_ROOT_PASSWORD: test
      MYSQL_DATABASE: test
    ports:
      - 48205:3306
  firebird:
    image: jacobalberty/firebird:v4.0.0
    environment:
      ISC_PASSWORD: Test!tesT
      FIREBIRD_USER: test
      FIREBIRD_PASSWORD: Test!tesT
      FIREBIRD_DATABASE: test.gdb
      EnableLegacyClientAuth: true
    ports:
      - 48101:3050