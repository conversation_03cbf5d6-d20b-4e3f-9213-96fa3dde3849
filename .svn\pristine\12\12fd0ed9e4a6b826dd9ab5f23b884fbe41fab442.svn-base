
// var _hmt = _hmt || [];
// (function() {
//     var hm = document.createElement("script");
//     hm.src = "https://hm.baidu.com/hm.js?1226d0e0b52736ddc8b33acf9c34d14b";
//     var s = document.getElementsByTagName("script")[0];
//     s.parentNode.insertBefore(hm, s);
// })();


var _hmt = _hmt || [];
(function () {
    var hm = document.createElement("script");
    hm.src = "https://hm.baidu.com/hm.js?3c6180c0ce7e5b3226f8f40f065198b0";
    var s = document.getElementsByTagName("script")[0];
    s.parentNode.insertBefore(hm, s);
})();
//字节跳动蜘蛛
(function () {
    var el = document.createElement("script");
    el.src = "https://lf1-cdn-tos.bytegoofy.com/goofy/ttzz/push.js?1451648b04c24f4a8b8adeb5ac7ea2dbbf3df71f08c1516e9d3b0992df61490c65e0a2ada1d5e86b11e7de7c1a83287d04743a02fd1ee8dd8558a8cad50e91cb354f8c6f3f78e5fd97613c481f678e6d";
    el.id = "ttzz";
    var s = document.getElementsByTagName("script")[0];
    s.parentNode.insertBefore(el, s);
})(window);

//搜狗蜘蛛
(function (w, d, t, s, q, m, n) {
    if (w.sguic) return;
    q = w.sguic = function () {
        q.process ? q.process.apply(null, arguments) : q.queue.push(arguments);
    };
    q.queue = [];
    m = d.getElementsByTagName(t)[0];
    n = d.createElement(t);
    n.src = s;
    n.async = true;
    m.parentNode.insertBefore(n, m);
})(window, document, 'script', '//jstatic.sogoucdn.com/sdk/uic-pub.js');
