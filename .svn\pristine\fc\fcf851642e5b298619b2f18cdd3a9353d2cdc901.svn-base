﻿namespace NewTicket
{
    using System;
    using System.Net;
    /// <summary>
    /// 可xml配置的IPEndPoint。
    /// </summary>
    [Serializable]
    public sealed class AgileTcpClient : IEquatable<AgileTcpClient>
    {
        private int int_0;
        private IPEndPoint ipendPoint_0;
        private string string_0;

        public AgileTcpClient()
        {
            this.string_0 = "";
            this.int_0 = 0;
            this.ipendPoint_0 = null;
        }

        public AgileTcpClient(IPEndPoint ipe)
        {
            this.string_0 = "";
            this.int_0 = 0;
            this.ipendPoint_0 = null;
            this.LocalIPAddress = ipe.Address.ToString();
            this.Port = ipe.Port;
        }

        public AgileTcpClient(string addressAndPort)
        {
            this.string_0 = "";
            this.int_0 = 0;
            this.ipendPoint_0 = null;
            string[] strArray = addressAndPort.Split(new char[] { ':' });
            this.LocalIPAddress = strArray[0];
            this.Port = int.Parse(strArray[1]);
        }

        public AgileTcpClient(string ip, int thePort)
        {
            this.string_0 = "";
            this.int_0 = 0;
            this.ipendPoint_0 = null;
            this.LocalIPAddress = ip;
            this.Port = thePort;
        }

        public bool Equals(AgileTcpClient other)
        {
            if (object.ReferenceEquals(other, null))
            {
                return false;
            }
            return ((this.string_0 == other.string_0) && (this.int_0 == other.int_0));
        }

        public override bool Equals(object obj)
        {
            AgileTcpClient other = obj as AgileTcpClient;
            if (other == null)
            {
                return false;
            }
            return this.Equals(other);
        }

        public static bool operator ==(AgileTcpClient a, AgileTcpClient b)
        {
            if (object.ReferenceEquals(a, null) && object.ReferenceEquals(b, null))
            {
                return true;
            }
            if (object.ReferenceEquals(a, null) || object.ReferenceEquals(b, null))
            {
                return false;
            }
            return a.Equals(b);
        }

        public static bool operator !=(AgileTcpClient a, AgileTcpClient b)
        {
            return !(a == b);
        }

        public override string ToString()
        {
            return string.Format("{0}:{1}", this.string_0, this.int_0);
        }

        public IPEndPoint IPEndPoint
        {
            get {
                return this.ipendPoint_0 ??
                       (this.ipendPoint_0 = new IPEndPoint(System.Net.IPAddress.Parse(this.string_0), this.int_0));
            }
        }
        /// <summary>
        /// 通信采用的本地端口，其值可能在GetNetworkStream方法中被修改。
        /// </summary>
        public int Port
        {
            get
            {
                return this.int_0;
            }
            set
            {
                this.int_0 = value;
            }
        }
        /// <summary>
        /// 从哪个IP发出TCP连接。如果不设置，则选用第一块网卡的地址(此时，其值在GetNetworkStream方法中被修改)
        /// </summary>
        public string LocalIPAddress
        {
            get
            {
                return this.string_0;
            }
            set
            {
                this.string_0 = value;
                if (this.string_0 != null)
                {
                    this.string_0 = this.string_0.Trim();
                }
            }
        }
    }
}

