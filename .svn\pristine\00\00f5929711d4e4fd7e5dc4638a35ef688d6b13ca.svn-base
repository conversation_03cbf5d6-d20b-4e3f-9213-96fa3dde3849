﻿using Enterprise.Framework.Redis;
using ServiceStack.Text;
using System;

namespace CommonLib
{
    public class DaMaQueue : RedisMessageQueueObject<CusImageEntity>
    {
        protected override string MessageQueueType
        {
            get { return "DaMaQueue"; }
        }

        protected override string Object_DBName
        {
            get { return "OPS_Cache"; }
        }

        #region 读写缓存消息队列

        /// <summary>
        /// 永远等待的取消息，直到队列中有了消息
        /// </summary>
        /// <param name="cacheKey"></param>
        /// <returns></returns>
        public CusImageEntity DequeueBlockingCacheMessage(int endBlockSeconds = 0)
        {
            return ExecuteRedisReadonlyFunc(redisClient =>
            {
                CusImageEntity img = null;
                var str = redisClient.BlockingDequeueItemFromList(MessageQueueType, new TimeSpan(0, 0, endBlockSeconds));
                if (!string.IsNullOrEmpty(str))
                {
                    img = JsonSerializer.DeserializeFromString<CusImageEntity>(str);
                }
                return img;
            });
        }
        #endregion
    }

    public class CollectImageQueue : RedisMessageQueueObject<CusImageEntity>
    {
        protected override string MessageQueueType
        {
            get { return "CollectImageQueue"; }
        }

        protected override string Object_DBName
        {
            get { return "OPS_Cache"; }
        }

        //public CusImageEntity PopImage()
        //{
        //    var msg = Pop();

        //    if (string.IsNullOrWhiteSpace(msg))
        //        return null;

        //    return ParseMessage(msg);
        //}

        #region 读写缓存消息队列

        ///// <summary>
        ///// 将消息压入队列
        ///// </summary>
        ///// <param name="cacheKey"></param>
        ///// <param name="message"></param>
        //public void EnqueueCacheMessage(CusImageEntity message)
        //{
        //    Push(message);
        //}

        ///// <summary>
        ///// 永远等待的取消息，直到队列中有了消息
        ///// </summary>
        ///// <param name="cacheKey"></param>
        ///// <returns></returns>
        //public CusImageEntity DequeueBlockingCacheMessage(int endBlockSeconds = 0)
        //{
        //    return ExecuteRedisFunc(redisClient =>
        //    {
        //        CusImageEntity img = null;
        //        var str = redisClient.BlockingDequeueItemFromList(MessageQueueType, new TimeSpan(0, 0, endBlockSeconds));
        //        if (!string.IsNullOrEmpty(str))
        //        {
        //            img = JsonSerializer.DeserializeFromString<CusImageEntity>(str);
        //        }
        //        return img;
        //    });
        //}
        #endregion
    }

}