﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Net;
using System.Threading;
using System.Collections.Specialized;
using System.Net.Cache;

namespace AccountTool
{
    public delegate byte[] GetImage(CNNWebClient myClient, string Url, ref  string CookieStr, string ipAddress = "");
    public delegate string GetNoSyncHtml(CNNWebClient myClient, string Url, ref string CookieStr, string ipAddress = "", string strPost = "", string Referer = "", int timeOut = 2);
    public class WebClientExt
    {
        public static byte[] RequestSyncImage(string Url, ref  string CookieStr, string ipAddress = "")
        {
            //DateTime dtTmp = DateTime.Now;
            byte[] result = null;
            CNNWebClient myClient = new CNNWebClient();
            //if (myClient.Proxy != null)
            //{
            //    myClient.Proxy = null;
            //}
            GetImage handler = new GetImage(GetNoSyncImage);
            System.IAsyncResult async = handler.BeginInvoke(myClient, Url, ref CookieStr, ipAddress, null, null);
            async.AsyncWaitHandle.WaitOne(5000);  //采用异步等待的方式。直到取到合适的值
            if (async.IsCompleted)
            {
                result = handler.EndInvoke(ref CookieStr, async);
            }
            try
            {
                if (myClient != null)
                {
                    try
                    {
                        if (myClient.IsBusy)
                            myClient.CancelAsync();
                    }
                    catch { }
                    try
                    {
                        myClient.Dispose();
                    }
                    catch { }
                    try
                    {
                        myClient = null;
                    }
                    catch { }
                }
            }
            catch (Exception oe)
            {
                Console.WriteLine(oe.Message);
            }
            //Console.WriteLine("IMG：" + new TimeSpan(DateTime.Now.Ticks - dtTmp.Ticks).TotalMilliseconds);
            return result;
        }
        public static byte[] GetNoSyncImage(string Url, ref  string CookieStr, string ipAddress = "")
        {
            return GetNoSyncImage(new CNNWebClient(), Url, ref CookieStr, ipAddress);
        }
        private static byte[] GetNoSyncImage(CNNWebClient myClient, string Url, ref  string CookieStr, string ipAddress = "")
        {
            DateTime dtStart = DateTime.Now;
            byte[] result = null;
            myClient.Timeout = 5;
            if (!string.IsNullOrEmpty(ipAddress))
                myClient.StrIPAddress = ipAddress;
            try
            {
                myClient.Headers.Add("Accept: text/html, application/xhtml+xml, application/json, text/javascript, */*; q=0.01");
                myClient.Headers.Add("Accept-Encoding: gzip,identity");
                myClient.Headers.Add("Accept-Language: zh-CN");
                myClient.Headers.Add("User-Agent: Mozilla/4.0 (compatible; MSIE 8.0; Windows NT 5.1; Trident/4.0; .NET CLR 2.0.50727; .NET CLR 3.0.4506.2152; .NET4.0C; .NET4.0E; .NET CLR 3.5.30729; InfoPath.3)");
                myClient.Headers.Add("Content-Type: application/x-www-form-urlencoded");
                if (CookieStr != "")
                {
                    myClient.Headers.Add("Cookie: " + CookieStr);
                }
                myClient.Encoding = Encoding.UTF8;
                //if (myClient.Proxy != null)
                //{
                //    myClient.Proxy = null;
                //}
                result = myClient.DownloadData(Url);
                if (string.IsNullOrEmpty(CookieStr))
                {
                    CookieStr = myClient.ResponseHeaders["Set-Cookie"].Replace("Path=/otn", " ").Replace("path=/", "").Replace(",", "").Trim();
                }
            }
            catch (Exception oe)
            {
                if (oe != null && !string.IsNullOrEmpty(oe.Message))
                {
                }
            }
            finally
            {
                try
                {
                    if (myClient.IsBusy)
                        myClient.CancelAsync();
                }
                catch { }
                try
                {
                    myClient.Dispose();
                }
                catch { }
                try
                {
                    myClient = null;
                }
                catch { }
            }
            return result;
        }

        public static string GetHtml(string url, double timeOut)
        {
            return GetHtml(url, "", "", "", 1, timeOut > 0 ? (int)timeOut : 2);
        }

        public static string RequestSyncHtml(string Url, ref  string CookieStr, string ipAddress = "", string strPost = "")
        {
            //DateTime dtTmp = DateTime.Now;
            string result = null;
            CNNWebClient myClient = new CNNWebClient();
            myClient.Headers.Add("Content-Type: application/x-www-form-urlencoded");
            myClient.Headers.Add("User-Agent: Mozilla/5.0 (compatible; MSIE 10.0; Windows NT 6.2; WOW64; Trident/6.0)");
            //myClient.Headers.Add("Cache-Control: no-cache");
            //myClient.Headers.Add("Pragma: no-cache");
            myClient.Headers.Add("X-Requested-With: XMLHttpRequest");
            if (CookieStr != "")
            {
                myClient.Headers.Add("Cookie: " + CookieStr);
            }
            myClient.Encoding = Encoding.UTF8;
            GetNoSyncHtml handler = new GetNoSyncHtml(RequestNoSyncHtml);
            myClient.Proxy = null;
            //if (myClient.Proxy != null)
            //{
            //    myClient.Proxy = null;
            //}
            System.IAsyncResult async = handler.BeginInvoke(myClient, Url, ref CookieStr, ipAddress, strPost, "", 2, null, null);
            async.AsyncWaitHandle.WaitOne(2000);//采用异步等待的方式。直到取到合适的值
            if (async.IsCompleted)
            {
                result = handler.EndInvoke(ref CookieStr, async);
            }
            try
            {
                if (myClient != null)
                {
                    try
                    {
                        if (myClient.IsBusy)
                            myClient.CancelAsync();
                    }
                    catch { }
                    try
                    {
                        myClient.Dispose();
                    }
                    catch { }
                    try
                    {
                        myClient = null;
                    }
                    catch { }
                }
            }
            catch (Exception oe)
            {
                Console.WriteLine(oe.Message);
            }
            //Console.WriteLine("IMG：" + new TimeSpan(DateTime.Now.Ticks - dtTmp.Ticks).TotalMilliseconds);
            return result;
        }
        private static string RequestNoSyncHtml(CNNWebClient myClient, string Url, ref string CookieStr, string ipAddress = "", string strPost = "", string Referer = "", int timeOut = 2)
        {
            DateTime dtStart = DateTime.Now;
            string result = "";
            myClient.Timeout = timeOut;
            if (!string.IsNullOrEmpty(ipAddress))
                myClient.StrIPAddress = ipAddress;
            try
            {
                if (string.IsNullOrEmpty(strPost))
                {
                    //if (Url.IndexOf("query?") < 0)
                    {
                        result = myClient.DownloadString(new Uri(Url, true));
                    }
                    //else
                    //{
                    //    byte[] nn = myClient.UploadData(new Uri(Url, true), "GET", new byte[10]);
                    //    result = Encoding.UTF8.GetString(nn);
                    //}
                }
                else
                {
                    result = myClient.UploadString(new Uri(Url, true), strPost);
                }
                if (string.IsNullOrEmpty(CookieStr) && Url.IndexOf("12306") > 0 && !string.IsNullOrEmpty(myClient.ResponseHeaders["Set-Cookie"]))
                {
                    CookieStr = myClient.ResponseHeaders["Set-Cookie"].Replace("Path=/otn", " ").Replace("path=/", "").Replace(",", "").Trim();
                }
                if (Url.Contains("baidu") && !string.IsNullOrEmpty(myClient.ResponseHeaders["Date"]))//header.Url.Contains("12306") ||
                {
                    try
                    {
                        result = myClient.ResponseHeaders["Date"];
                    }
                    catch { }
                }
                if (Url.Contains("query") && !string.IsNullOrEmpty(myClient.ResponseHeaders["Age"]))//header.Url.Contains("12306") ||
                {
                    try
                    {
                        Console.WriteLine(string.Format("Cache:{0}", CookieStr));
                    }
                    catch { }
                }
            }
            catch (Exception oe)
            {
                if (oe != null && !string.IsNullOrEmpty(oe.Message))
                {
                }
            }
            finally
            {
                try
                {
                    if (myClient.IsBusy)
                        myClient.CancelAsync();
                }
                catch { }
                try
                {
                    myClient.Dispose();
                }
                catch { }
                try
                {
                    myClient = null;
                }
                catch { }
            }
            return result;
        }

        public static string GetHtml(string url, int retryCount = 1)
        {
            return GetHtml(url, "", "");
        }

        public static string GetHtml(string url, string cookie, string ipAddress, string post = "", int retryCount = 1, int timeOut = 2)
        {
            string strTmp = string.Empty;
            retryCount = retryCount <= 0 ? 1 : retryCount;
            for (int i = 0; i < retryCount; i++)
            {
                strTmp = GetHtml(url, ref cookie, ipAddress, post, "", timeOut);
                if (!string.IsNullOrEmpty(strTmp))
                    break;
            }
            return strTmp;
        }

        public static string GetHtml(string Url, ref string CookieStr, string ipAddress = "", string strPost = "", string Referer = "", int timeOut = 2)
        {
            DateTime dtStart = DateTime.Now;
            //return HttpHelper.GetHtml(Url, ref CookieStr, ipAddress, strPost, Referer, timeOut);
            string result = "";
            CNNWebClient myClient = new CNNWebClient();
            myClient.Timeout = timeOut;
            if (!string.IsNullOrEmpty(ipAddress))
                myClient.StrIPAddress = ipAddress;
            try
            {
                myClient.Headers.Add("Content-Type: application/x-www-form-urlencoded");
                myClient.Headers.Add("User-Agent: Mozilla/5.0 (compatible; MSIE 10.0; Windows NT 6.2; WOW64; Trident/6.0)");
                myClient.Headers.Add("Cache-Control: no-cache");
                myClient.Headers.Add("Pragma: no-cache");
                //if (Url.IndexOf("") >= 0)
                myClient.Headers.Add("X-Requested-With: XMLHttpRequest");
                if (CookieStr != "")
                {
                    myClient.Headers.Add("Cookie: " + CookieStr);
                }
                //if (myClient.Proxy != null)
                //{
                //    myClient.Proxy = null;
                //}
                myClient.Encoding = Encoding.UTF8;
                if (string.IsNullOrEmpty(strPost))
                    result = myClient.DownloadString(new Uri(Url, true));
                else
                    result = myClient.UploadString(new Uri(Url, true), strPost);
                if (!string.IsNullOrEmpty(myClient.ResponseHeaders["Set-Cookie"]))
                {
                    CookieStr += ";" + myClient.ResponseHeaders["Set-Cookie"].Replace("Path=/otn", " ").Replace("path=/", "").Replace(",", "").Trim();
                }
                if (Url.Contains("baidu") && !string.IsNullOrEmpty(myClient.ResponseHeaders["Date"]))//header.Url.Contains("12306") ||
                {
                    try
                    {
                        result = myClient.ResponseHeaders["Date"];
                    }
                    catch { }
                }
            }
            catch (Exception oe)
            {
                if (oe != null && !string.IsNullOrEmpty(oe.Message))
                {
                }
            }
            finally
            {
                try
                {
                    if (myClient.IsBusy)
                        myClient.CancelAsync();
                }
                catch { }
                try
                {
                    myClient.Dispose();
                }
                catch { }
                try
                {
                    myClient = null;
                }
                catch { }
            }
            return result;
        }

        private static string GetCookie(string CookieStr)
        {
            string result = "";
            string[] myArray = CookieStr.Split(',');
            if (myArray.Count() > 0)
            {
                //result = "Cookie: ";
                foreach (var str in myArray)
                {
                    string[] CookieArray = str.Split(';');
                    result += CookieArray[0].Trim();
                    result += "; ";
                }
                result = result.Substring(0, result.Length - 2);
            }
            return result;
        }

    }
    /// <summary>
    /// 过期时回调委托
    /// </summary>
    /// <param name="userdata"></param>
    public delegate void TimeoutCaller(object userdata);

    public class CNNWebClient : WebClient
    {
        private Calculagraph _timer;
        private int _timeOut = 3;
        private string strIPAddress = "";

        public string StrIPAddress
        {
            get { return strIPAddress; }
            set { strIPAddress = value; }
        }

        /// <summary>
        /// 过期时间
        /// </summary>
        public int Timeout
        {
            get
            {
                return _timeOut;
            }
            set
            {
                if (value <= 0)
                    _timeOut = 10;
                _timeOut = value;
            }
        }

        /// <summary>
        /// 重写GetWebRequest,添加WebRequest对象超时时间
        /// </summary>
        /// <param name="address"></param>
        /// <returns></returns>
        protected override WebRequest GetWebRequest(Uri address)
        {
            if (DateTime.Now.Second % 3 == 0)
            {
                //System.Threading.Thread.Sleep(1);
                System.GC.Collect();
            }
            if (!string.IsNullOrEmpty(StrIPAddress))
                address = new Uri(address.AbsoluteUri.Replace(address.Host, StrIPAddress), true);

            HttpWebRequest request = (HttpWebRequest)base.GetWebRequest(address);//address.AbsoluteUri.IndexOf("leftTicket") > 0 ? (HttpWebRequest)HttpWebRequest.Create(address.AbsoluteUri) :
            if (request.Proxy != null)
            {
                request.Proxy = null;
            }
            request.ProtocolVersion = HttpVersion.Version10;
            request.ServicePoint.ConnectionLimit = int.MaxValue;
            request.AllowWriteStreamBuffering = false;
            //request.ServicePoint.Expect100Continue = false;
            //request.ServicePoint.UseNagleAlgorithm = false;
            request.AutomaticDecompression = DecompressionMethods.Deflate | DecompressionMethods.GZip;
            request.AllowAutoRedirect = true;
            if (!string.IsNullOrEmpty(StrIPAddress))
            {
                request.Host = "kyfw.12306.cn";
                request.Referer = "https://kyfw.12306.cn/otn/leftTicket/init";
            }
            //if (!isImg)
            //{
            request.KeepAlive = false;
            request.IfModifiedSince = new DateTime(1970, 1, 1);
            request.UserAgent = "Mozilla/4.0 (compatible; MSIE 8.0; Windows NT 5.1; Trident/4.0; .NET CLR 2.0.50727; .NET CLR 3.0.4506.2152; .NET4.0C; .NET4.0E; .NET CLR 3.5.30729; InfoPath.3)";
            //request.Headers.Add("If-None-Match", DateTime.Now.Ticks.ToString());
            //request.CachePolicy = new HttpRequestCachePolicy(HttpRequestCacheLevel.NoCacheNoStore);

            //HttpHelper.SetHeaderValue(request.Headers, "If-Modified-Since", "Wed, 31 Dec 1969 16:00:00 GMT");
            //request.Headers.Add("If-None-Match", DateTime.Now.Ticks.ToString());
            //}
            request.Timeout = 1000 * Timeout;
            request.ReadWriteTimeout = 1000 * Timeout;
            //HttpHelper.SetHeaderValue(request.Headers, "Connection", "Close");
            return request;
        }

        /// <summary>
        /// 带过期计时的下载
        /// </summary>
        public void DownloadFileAsyncWithTimeout(Uri address, string fileName, object userToken)
        {
            if (_timer == null)
            {
                _timer = new Calculagraph(this);
                _timer.Timeout = Timeout;
                _timer.TimeOver += new TimeoutCaller(_timer_TimeOver);
                this.DownloadProgressChanged += new DownloadProgressChangedEventHandler(CNNWebClient_DownloadProgressChanged);
            }

            DownloadFileAsync(address, fileName, userToken);
            _timer.Start();
        }

        /// <summary>
        /// WebClient下载过程事件，接收到数据时引发
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        void CNNWebClient_DownloadProgressChanged(object sender, DownloadProgressChangedEventArgs e)
        {
            _timer.Reset();//重置计时器
        }

        /// <summary>
        /// 计时器过期
        /// </summary>
        /// <param name="userdata"></param>
        void _timer_TimeOver(object userdata)
        {
            this.CancelAsync();//取消下载
        }
    }

    public class Calculagraph
    {
        /// <summary>
        /// 时间到事件
        /// </summary>
        public event TimeoutCaller TimeOver;

        /// <summary>
        /// 开始时间
        /// </summary>
        private DateTime _startTime;
        private TimeSpan _timeout = new TimeSpan(0, 0, 10);
        private bool _hasStarted = false;
        object _userdata;

        /// <summary>
        /// 计时器构造方法
        /// </summary>
        /// <param name="userdata">计时结束时回调的用户数据</param>
        public Calculagraph(object userdata)
        {
            TimeOver += new TimeoutCaller(OnTimeOver);
            _userdata = userdata;
        }

        /// <summary>
        /// 超时退出
        /// </summary>
        /// <param name="userdata"></param>
        public virtual void OnTimeOver(object userdata)
        {
            Stop();
        }

        /// <summary>
        /// 过期时间(秒)
        /// </summary>
        public int Timeout
        {
            get
            {
                return _timeout.Seconds;
            }
            set
            {
                if (value <= 0)
                    return;
                _timeout = new TimeSpan(0, 0, value);
            }
        }

        /// <summary>
        /// 是否已经开始计时
        /// </summary>
        public bool HasStarted
        {
            get
            {
                return _hasStarted;
            }
        }

        /// <summary>
        /// 开始计时
        /// </summary>
        public void Start()
        {
            Reset();
            _hasStarted = true;
            Thread th = new Thread(WaitCall);
            th.IsBackground = true;
            th.Start();
        }

        /// <summary>
        /// 重置
        /// </summary>
        public void Reset()
        {
            _startTime = DateTime.Now;
        }

        /// <summary>
        /// 停止计时
        /// </summary>
        public void Stop()
        {
            _hasStarted = false;
        }

        /// <summary>
        /// 检查是否过期
        /// </summary>
        /// <returns></returns>
        private bool checkTimeout()
        {
            return (DateTime.Now - _startTime).Seconds >= Timeout;
        }

        private void WaitCall()
        {
            try
            {
                //循环检测是否过期
                while (_hasStarted && !checkTimeout())
                {
                    Thread.Sleep(1000);
                }
                if (TimeOver != null)
                    TimeOver(_userdata);
            }
            catch (Exception)
            {
                Stop();
            }
        }
    }
}
