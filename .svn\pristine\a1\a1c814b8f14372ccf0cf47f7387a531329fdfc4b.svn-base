{"DebugMode": true, "oauth.RedirectUrl": "https://localhost:5001/", "oauth.CallbackUrl": "https://localhost:5001/auth/{0}", "oauth.facebook.Permissions": ["email", "user_location"], "oauth.facebook.Permissions2": "email,user_location", "oauth.facebook.AppId": "531608123577340", "oauth.facebook.AppSecret": "********************************", "oauth.twitter.ConsumerKey": "*************************", "oauth.twitter.ConsumerSecret": "WNeOT6YalxXDR4iWZjc4jVjFaydoDcY8jgRrGc5FVLjsVlY2Y8", "oauth.github.Scopes": ["user"], "oauth.github.ClientId": "********************", "oauth.github.ClientSecret": "868d2b4c7b1632d1b774d6209b1c6ae522fe1954", "oauth.microsoftgraph.AppId": "8208d98e-400d-4ce9-89ba-d92610c67e13", "oauth.microsoftgraph.AppSecret": "hsrMP46|_kfkcYCWSW516?%", "oauth.microsoftgraph.SavePhoto": "true", "oauth.microsoftgraph.SavePhotoSize": "32x32", "oauth.apple.RedirectUrl": "https://localtest.me:5001/", "oauth.apple.CallbackUrl": "https://localtest.me:5001/auth/apple", "oauth.apple.TeamId": "DK4VJ3YB24", "oauth.apple.ClientId": "net.servicestack.myappid", "oauth.apple.KeyId": "L8N2DSVU8J", "oauth.apple.KeyPath": "AuthKey_L8N2DSVU8J.p8", "Logging": {"LogLevel": {"Default": "Warning"}}, "AllowedHosts": "*", "NavItems": [{"href": "/", "label": "Home", "exact": true}, {"href": "/about", "label": "About"}, {"href": "/services", "label": "Services"}, {"href": "/contact", "label": "Contact", "children": [{"href": "/contact/me", "label": "Me"}, {"href": "/contact/email", "label": "Email"}, {"label": "-"}, {"href": "/contact/phone", "label": "Phone"}]}, {"href": "/login", "label": "Sign In", "hide": "auth"}, {"href": "/profile", "label": "Profile", "show": "auth"}, {"href": "/admin", "label": "Admin", "show": "role:<PERSON><PERSON>"}], "NavItemsMap": {"aside": [{"href": "/aside", "label": "Aside"}], "footer": [{"href": "/terms", "label": "About"}, {"href": "/navitems", "label": "Services"}, {"href": "/contacts", "label": "Contacts", "children": [{"href": "/contact/me", "label": "Me"}, {"href": "/contact/email", "label": "Email"}, {"label": "-"}, {"href": "/contact/phone", "label": "Phone"}]}]}}