﻿using System;
using System.Drawing;
using CommonLib;
using HanZiOcr;
using ImageOCR;
using System.Diagnostics;

namespace DaMaLib
{
    public static class ZhuShouDaMaHelper
    {
        public static bool IsEnable { get; set; }

        //public static string GetCodeByBytes(byte[] byts, bool isLogin, UserTypeEnum siteFlag)
        //{
        //    var result = "";
        //    try
        //    {
        //        using (var bitmap = CommonCompress.BytesToImage(byts))
        //        {
        //            result = GetCodeByImg(bitmap, isLogin, siteFlag);
        //            bitmap.Dispose();
        //        }
        //    }
        //    catch (Exception oe)
        //    {
        //        ConfigHelper._Log.Error("助手打码GetCode", oe);
        //    }
        //    finally
        //    {
        //        byts = null;
        //    }
        //    return result;
        //}

        //private static string GetCodeByImg(Bitmap bitmap, bool isLogin, UserTypeEnum siteFlag)
        //{
        //    var result = "";

        //    try
        //    {
        //        Stopwatch stop = Stopwatch.StartNew();
        //        using (bitmap)
        //        {
        //            //var stop = System.Diagnostics.Stopwatch.StartNew();
        //            //System.Text.StringBuilder sb = new System.Text.StringBuilder();
        //            //sb.AppendLine(string.Format("开始识别汉字Local，耗时：{0}ms", stop.ElapsedMilliseconds.ToString("F0")));

        //            var strOld = "";
        //            var strHanZi = "";// HanZiHelper.GetHanZiByImg(bitmap, ref strOld, isLogin, siteFlag);
        //            //strHanZi = CustomImageHelper.RecHanZiByImg(bitmap, isLogin, from);
        //            //sb.AppendLine(string.Format("汉字[{1}]识别Local完毕，耗时：{0}ms", stop.ElapsedMilliseconds.ToString("F0"), strHanZi));
        //            if (!string.IsNullOrEmpty(strHanZi))
        //            {
        //                var info = string.Format("汉字：[{0}]，识别耗时：{1}ms", strHanZi, stop.ElapsedMilliseconds.ToString("F0"));
        //                stop.Restart();
        //                //sb.AppendLine(string.Format("开始识别图片Local[{1}]，耗时：{0}ms", stop.ElapsedMilliseconds.ToString("F0"), strHanZi));
        //                //result = DaMaHelper.GetCodeByPath(strHanZiPath, strHanZi, isLogin, siteFlag);
        //                //result = GetCodeNewByImg(bitmap, strHanZi, isLogin, from);
        //                result = CodeHelper.RecCodeByImg(bitmap, strHanZi, isLogin, siteFlag);
        //                info += Environment.NewLine + string.Format("图片：[{0}]，识别耗时：{1}ms", result, stop.ElapsedMilliseconds.ToString("F0"));
        //                Console.WriteLine(info);
        //                //sb.AppendLine(string.Format("图片Local[{1}-{2}]识别完毕，耗时：{0}ms", stop.ElapsedMilliseconds.ToString("F0"), strHanZi, result));
        //            }
        //            //ConfigHelper._Log.Info(sb.ToString());
        //            bitmap.Dispose();
        //        }
        //    }
        //    catch (Exception oe)
        //    {
        //        ConfigHelper._Log.Error("助手打码GetCode", oe);
        //    }
        //    return result;
        //}

        //public static string GetCodeByCodeStrBase64(string strCode, bool isLogin, UserTypeEnum siteFlag)
        //{
        //    var result = "";

        //    try
        //    {
        //        using (var bitmap = CodeHelper.GetCodeImg(strCode, isLogin))
        //        {
        //            result = GetCodeByImg(bitmap, isLogin, siteFlag);
        //            bitmap.Dispose();
        //        }
        //    }
        //    catch (Exception oe)
        //    {
        //        ConfigHelper._Log.Error("助手打码GetCode", oe);
        //    }
        //    finally
        //    {
        //        strCode = null;
        //    }
        //    return result;
        //}

        //public static string GetCodeByPath(string strPath, bool isLogin, UserTypeEnum siteFlag)
        //{
        //    var result = "";
        //    try
        //    {
        //        using (var bitmap = CommonCompress.GetImageByPath(strPath))
        //        {
        //            result = GetCodeByImg(bitmap, isLogin, siteFlag);
        //            bitmap.Dispose();
        //        }
        //    }
        //    catch (Exception oe)
        //    {
        //        ConfigHelper._Log.Error("GetCodeByPath", oe);
        //    }
        //    return result;
        //}

        #region Old

        //strHanZiPath = "";
        //if (string.IsNullOrEmpty(result))
        //{
        //    if (!string.IsNullOrEmpty(strCode))
        //    {
        //        if (!ConfigHelper.IsCanRecImg)
        //            strHanZiPath = CodeHelper.GetCodePath(strCode, isLogin, ref strHanZi);
        //        else
        //            bitTmp = CodeHelper.GetCodeImg(strCode, isLogin, ref strHanZi);
        //    }
        //}
        //else
        //{
        //    CustomImageHelper.AddImg(strCode, result, isLogin, strHanZi);
        //}
        //sb.AppendLine(string.Format("远程打码耗时：{0}ms", stop.ElapsedMilliseconds.ToString("F0")));

        //if (string.IsNullOrEmpty(result) && (bitTmp != null || !string.IsNullOrEmpty(strHanZiPath)))
        //{
        //    strCode = null;
        //    sb.AppendLine(string.Format("开始识别汉字Local，耗时：{0}ms", stop.ElapsedMilliseconds.ToString("F0")));
        //    if (!ConfigHelper.IsCanRecImg)
        //        strHanZi = CustomImageHelper.RecHanZiByPath(strHanZiPath, isLogin, siteFlag);
        //    else
        //        strHanZi = CustomImageHelper.RecHanZiByImg(bitTmp, isLogin, siteFlag);
        //    sb.AppendLine(string.Format("汉字[{1}]识别Local完毕，耗时：{0}ms", stop.ElapsedMilliseconds.ToString("F0"), strHanZi));

        //    if (!string.IsNullOrEmpty(strHanZi))
        //    {
        //        if (!ConfigHelper.IsCanRecImg)
        //        {
        //            sb.AppendLine(string.Format("开始识别图片[{1}]，耗时：{0}ms", stop.ElapsedMilliseconds.ToString("F0"), strHanZi));
        //            var newUrl = string.Format("http://{0}/code.ashx?op={1}", ConfigHelper.RecImgHost, "new_img");// context.Request.Url.ToString().Replace(context.Request.Url.Host, ConfigHelper.RecUrlHost);
        //            var strNewPost = string.Format("path={0}&tt={1}&hz={2}&flag={3}", strHanZiPath, isLogin ? "0" : "1", strHanZi, siteFlag);

        //            sb.AppendLine("Url:" + newUrl);
        //            sb.AppendLine("Post:" + strNewPost);
        //            result = WebClientExt.GetHtml(newUrl, "", "", strNewPost, 1, 3);
        //            sb.AppendLine(string.Format("图片[{1}-{2}]识别完毕，耗时：{0}ms", stop.ElapsedMilliseconds.ToString("F0"), strHanZi, result));
        //            if (result.Equals("no"))
        //            {
        //                result = "";
        //            }
        //        }
        //        else
        //        {
        //            sb.AppendLine(string.Format("开始识别图片Local[{1}]，耗时：{0}ms", stop.ElapsedMilliseconds.ToString("F0"), strHanZi));
        //            //result = DaMaHelper.GetCodeByPath(strHanZiPath, strHanZi, isLogin, siteFlag);
        //            result = DaMaHelper.GetCodeNewByImg(bitTmp, strHanZi, isLogin, siteFlag);
        //            sb.AppendLine(string.Format("图片Local[{1}-{2}]识别完毕，耗时：{0}ms", stop.ElapsedMilliseconds.ToString("F0"), strHanZi, result));
        //        }
        //    }
        //}

        #endregion
    }
}