﻿using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace CommonLib
{
    public class DisposeEmailHelper
    {
        static List<string> list = new List<string>();
        static string IndexUrl = "https://disposable.github.io/disposable-email-domains/domains.json";
        static string IndexUrl1 = "https://raw.githubusercontent.com/disposable/disposable-email-domains/master/domains.json";
        static string IndexUrl2 = "https://raw.githubusercontent.com/kslr/disposable-email-domains/master/list.json";
        static string IndexUrl3 = "https://cdn.jsdelivr.net/gh/disposable/disposable-email-domains@master/domains.json";
        static string IndexUrl4 = "https://github.com/ankaboot-source/email-open-data/raw/main/disposable-email-domains.json";
        static string IndexUrl5 = "https://raw.githubusercontent.com/ankaboot-source/email-open-data/main/disposable-email-domains.json";

        public static bool IsDisposeEmail(string email)
        {
            var hostname = email.Trim().ToLower();

            if (hostname.Contains('@'))
                hostname = hostname.Split("@".ToCharArray(), 2, StringSplitOptions.None)[1];

            return list.Any(o => hostname.EndsWith(o));
        }

        private static void InitData()
        {
            MemoryManager.ClearMemory();
            var content = WebClientSyncExt.GetHtml(IndexUrl3 + "?t=" + ServerTime.DateTime.Ticks, 60);
            if (string.IsNullOrEmpty(content) || !content.StartsWith("["))
            {
                content = WebClientSyncExt.GetHtml(IndexUrl1 + "?t=" + ServerTime.DateTime.Ticks, 60);
            }
            if (string.IsNullOrEmpty(content) || !content.StartsWith("["))
            {
                content = WebClientSyncExt.GetHtml(IndexUrl2 + "?t=" + ServerTime.DateTime.Ticks, 60);
            }
            if (string.IsNullOrEmpty(content) || !content.StartsWith("["))
            {
                content = WebClientSyncExt.GetHtml(IndexUrl + "?t=" + ServerTime.DateTime.Ticks, 60);
            }
            if (string.IsNullOrEmpty(content) || !content.StartsWith("["))
            {
                content = WebClientSyncExt.GetHtml(IndexUrl4 + "?t=" + ServerTime.DateTime.Ticks, 60);
            }
            if (string.IsNullOrEmpty(content) || !content.StartsWith("["))
            {
                content = WebClientSyncExt.GetHtml(IndexUrl5 + "?t=" + ServerTime.DateTime.Ticks, 60);
            }
            if (!string.IsNullOrEmpty(content) && content.StartsWith("["))
            {
                list = JsonConvert.DeserializeObject<List<string>>(content);
            }
        }

        public static void Init()
        {
            Task.Factory.StartNew(() =>
            {
                while (true)
                {
                    InitData();
                    if (list.Count <= 0)
                        System.Threading.Thread.Sleep(1 * 60 * 60);
                    else
                        System.Threading.Thread.Sleep(new Random().Next(30, 60) * 60 * 60);
                }
            });
        }
    }
}
