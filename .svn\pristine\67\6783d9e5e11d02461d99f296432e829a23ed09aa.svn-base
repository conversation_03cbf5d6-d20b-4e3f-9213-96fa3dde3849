﻿using CommonLib;
using System;
using System.Collections.Generic;
using System.Collections.Specialized;
using System.IO;
using System.Linq;
using System.Net;
using System.Reflection;
using System.Runtime.InteropServices;
using System.Security.AccessControl;
using System.Security.Cryptography;
using System.Text;
using System.Web.Script.Serialization;
using System.Web.UI.WebControls;

namespace DocOcr
{
    /// <summary>
    /// 汉王扫描王-小程序
    /// </summary>
    public class HanWangLiteTxtRec : BaseDocOcrRec
    {

        public HanWangLiteTxtRec()
        {
            OcrGroup = OcrGroupType.汉王;
            OcrType = DocOcrType.汉王Lite_TXT;
            ResultType = ResutypeEnum.文本;
            MaxExecPerTime = 25;
            //文件上限2M
            FileSizeLimit = 1024 * 1024 * 10;
            AllowUploadFileTypes.AddRange(new List<string>() { "pdf" });

            LstJsonPreProcessArray = new List<object>() { "data", "elems" };
            LstJsonNextProcessArray = new List<object>() { "lines", "chars" };
            IsSupportVertical = true;
            StrResultJsonSpilt = "code";
            LstVerticalLocation = new List<object>() { "coords" };
        }

        protected override string GetHtml(OcrContent content)
        {
            var result = string.Empty;
            var byt = Convert.FromBase64String(content.strBase64);
            var url = HanWangLiteHelper.Upload(byt, content.fileExt);
            if (!string.IsNullOrEmpty(url))
            {
                var head = HanWangLiteHelper.GetHeader();
                var body = new NameValueCollection() {
                    { "ossUrl",url}
                };
                result = PostFile("https://api.hanvonscanner.com/ocr-api/service/ocr/segment/ly", null, body, head);
            }
            return result;
        }

    }
}