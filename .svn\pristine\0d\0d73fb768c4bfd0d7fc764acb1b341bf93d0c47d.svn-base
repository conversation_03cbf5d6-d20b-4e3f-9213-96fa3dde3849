<?xml version="1.0"?>
<doc>
    <assembly>
        <name>ServiceStack.Text</name>
    </assembly>
    <members>
        <member name="T:ServiceStack.Text.AssemblyUtils">
            <summary>
            Utils to load types
            </summary>
        </member>
        <member name="M:ServiceStack.Text.AssemblyUtils.FindType(System.String)">
            <summary>
            Find the type from the name supplied
            </summary>
            <param name="typeName">[typeName] or [typeName, assemblyName]</param>
            <returns></returns>
        </member>
        <member name="M:ServiceStack.Text.AssemblyUtils.MainInterface``1">
            <summary>
            The top-most interface of the given type, if any.
            </summary>
        </member>
        <member name="M:ServiceStack.Text.AssemblyUtils.FindType(System.String,System.String)">
            <summary>
            Find type if it exists
            </summary>
            <param name="typeName"></param>
            <param name="assemblyName"></param>
            <returns>The type if it exists</returns>
        </member>
        <member name="M:ServiceStack.Text.Common.DateTimeSerializer.Prepare(System.DateTime,System.Boolean)">
            <summary>
            If AlwaysUseUtc is set to true then convert all DateTime to UTC. If PreserveUtc is set to true then UTC dates will not convert to local
            </summary>
            <param name="dateTime"></param>
            <returns></returns>
        </member>
        <member name="M:ServiceStack.Text.Common.DateTimeSerializer.RepairXsdTimeSeparator(System.String)">
            <summary>
            Repairs an out-of-spec XML date/time string which incorrectly uses a space instead of a 'T' to separate the date from the time.
            These string are occasionally generated by SQLite and can cause errors in OrmLite when reading these columns from the DB.
            </summary>
            <param name="dateTimeStr">The XML date/time string to repair</param>
            <returns>The repaired string. If no repairs were made, the original string is returned.</returns>
        </member>
        <member name="M:ServiceStack.Text.Common.DateTimeSerializer.ParseWcfJsonDateOffset(System.String)">
            <summary>
            WCF Json format: /Date(unixts+0000)/
            </summary>
            <param name="wcfJsonDate"></param>
            <returns></returns>
        </member>
        <member name="M:ServiceStack.Text.Common.DateTimeSerializer.ParseWcfJsonDate(System.String)">
            <summary>
            WCF Json format: /Date(unixts+0000)/
            </summary>
            <param name="wcfJsonDate"></param>
            <returns></returns>
        </member>
        <member name="M:ServiceStack.Text.Common.DeserializeTypeUtils.GetTypeStringConstructor(System.Type)">
            <summary>
            Get the type(string) constructor if exists
            </summary>
            <param name="type">The type.</param>
            <returns></returns>
        </member>
        <member name="M:ServiceStack.Text.Common.JsWriter.HasAnyEscapeChars(System.String)">
            <summary>
            micro optimizations: using flags instead of value.IndexOfAny(EscapeChars)
            </summary>
            <param name="value"></param>
            <returns></returns>
        </member>
        <member name="T:ServiceStack.Text.Controller.PathInfo">
            <summary>
            Class to hold  
            </summary>
        </member>
        <!-- Badly formed XML comment ignored for member "M:ServiceStack.Text.Controller.PathInfo.Parse(System.String)" -->
        <member name="T:ServiceStack.Text.DateTimeExtensions">
            <summary>
            A fast, standards-based, serialization-issue free DateTime serializer.
            </summary>
        </member>
        <member name="M:ServiceStack.Text.ITypeSerializer`1.CanCreateFromString(System.Type)">
            <summary>
            Determines whether this serializer can create the specified type from a string.
            </summary>
            <param name="type">The type.</param>
            <returns>
            	<c>true</c> if this instance [can create from string] the specified type; otherwise, <c>false</c>.
            </returns>
        </member>
        <member name="M:ServiceStack.Text.ITypeSerializer`1.DeserializeFromString(System.String)">
            <summary>
            Parses the specified value.
            </summary>
            <param name="value">The value.</param>
            <returns></returns>
        </member>
        <member name="M:ServiceStack.Text.ITypeSerializer`1.DeserializeFromReader(System.IO.TextReader)">
            <summary>
            Deserializes from reader.
            </summary>
            <param name="reader">The reader.</param>
            <returns></returns>
        </member>
        <member name="M:ServiceStack.Text.ITypeSerializer`1.SerializeToString(`0)">
            <summary>
            Serializes to string.
            </summary>
            <param name="value">The value.</param>
            <returns></returns>
        </member>
        <member name="M:ServiceStack.Text.ITypeSerializer`1.SerializeToWriter(`0,System.IO.TextWriter)">
            <summary>
            Serializes to writer.
            </summary>
            <param name="value">The value.</param>
            <param name="writer">The writer.</param>
        </member>
        <member name="M:ServiceStack.Text.JsConfig.Init">
            <summary>
            Mark JsConfig global config as initialized and assert it's no longer mutated
            </summary>
            <param name="config"></param>
        </member>
        <member name="M:ServiceStack.Text.JsConfig.Init(ServiceStack.Text.Config)">
            <summary>
            Initialize global config and assert that it's no longer mutated 
            </summary>
            <param name="config"></param>
        </member>
        <member name="P:ServiceStack.Text.JsConfig.TimeSpanHandler">
            <summary>
            Sets which format to use when serializing TimeSpans
            </summary>
        </member>
        <member name="P:ServiceStack.Text.JsConfig.TextCase">
            <summary>
            Text case to use for property names (Default = PascalCase)
            </summary>
        </member>
        <member name="P:ServiceStack.Text.JsConfig.EmitCamelCaseNames">
            <summary>
            Emitting camelCase for property names 
            </summary>
        </member>
        <member name="P:ServiceStack.Text.JsConfig.EmitLowercaseUnderscoreNames">
            <summary>
            Emitting lowercase_underscore_casing for property names 
            </summary>
        </member>
        <member name="P:ServiceStack.Text.JsConfig.PropertyConvention">
            <summary>
            Define how property names are mapped during deserialization
            </summary>
        </member>
        <member name="P:ServiceStack.Text.JsConfig.ThrowOnError">
            <summary>
            Gets or sets a value indicating if the framework should throw serialization exceptions
            or continue regardless of serialization errors. If <see langword="true"/>  the framework
            will throw; otherwise, it will parse as many fields as possible. The default is <see langword="false"/>.
            </summary>
        </member>
        <member name="P:ServiceStack.Text.JsConfig.AlwaysUseUtc">
            <summary>
            Gets or sets a value indicating if the framework should always convert <see cref="T:System.DateTime"/> to UTC format instead of local time. 
            </summary>
        </member>
        <member name="P:ServiceStack.Text.JsConfig.SkipDateTimeConversion">
            <summary>
            Gets or sets a value indicating if the framework should skip automatic <see cref="T:System.DateTime"/> conversions.
            Dates will be handled literally, any included timezone encoding will be lost and the date will be treaded as DateTimeKind.Local
            Utc formatted input will result in DateTimeKind.Utc output. Any input without TZ data will be set DateTimeKind.Unspecified
            This will take precedence over other flags like AlwaysUseUtc 
            JsConfig.DateHandler = DateHandler.ISO8601 should be used when set true for consistent de/serialization.
            </summary>
        </member>
        <member name="P:ServiceStack.Text.JsConfig.AssumeUtc">
            <summary>
            Gets or sets a value indicating if the framework should always assume <see cref="T:System.DateTime"/> is in UTC format if Kind is Unspecified. 
            </summary>
        </member>
        <member name="P:ServiceStack.Text.JsConfig.AppendUtcOffset">
            <summary>
            Gets or sets whether we should append the Utc offset when we serialize Utc dates. Defaults to no.
            Only supported for when the JsConfig.DateHandler == JsonDateHandler.TimestampOffset
            </summary>
        </member>
        <member name="P:ServiceStack.Text.JsConfig.EscapeUnicode">
            <summary>
            Gets or sets a value indicating if unicode symbols should be serialized as "\uXXXX".
            </summary>
        </member>
        <member name="P:ServiceStack.Text.JsConfig.EscapeHtmlChars">
            <summary>
            Gets or sets a value indicating if HTML entity chars [&gt; &lt; &amp; = '] should be escaped as "\uXXXX".
            </summary>
        </member>
        <member name="P:ServiceStack.Text.JsConfig.OnDeserializationError">
            <summary>
            Gets or sets a value indicating if the framework should call an error handler when
            an exception happens during the deserialization.
            </summary>
            <remarks>Parameters have following meaning in order: deserialized entity, property name, parsed value, property type, caught exception.</remarks>
        </member>
        <member name="P:ServiceStack.Text.JsConfig.PreferInterfaces">
            <summary>
            If set to true, Interface types will be preferred over concrete types when serializing.
            </summary>
        </member>
        <member name="P:ServiceStack.Text.JsConfig.IncludePublicFields">
            <summary>
            If set to true, Interface types will be preferred over concrete types when serializing.
            </summary>
        </member>
        <member name="P:ServiceStack.Text.JsConfig.MaxDepth">
            <summary>
            Sets the maximum depth to avoid circular dependencies
            </summary>
        </member>
        <member name="P:ServiceStack.Text.JsConfig.ModelFactory">
            <summary>
            Set this to enable your own type construction provider.
            This is helpful for integration with IoC containers where you need to call the container constructor.
            Return null if you don't know how to construct the type and the parameterless constructor will be used.
            </summary>
        </member>
        <member name="F:ServiceStack.Text.JsConfig`1.IncludeTypeInfo">
            <summary>
            Always emit type info for this type.  Takes precedence over ExcludeTypeInfo
            </summary>
        </member>
        <member name="F:ServiceStack.Text.JsConfig`1.ExcludeTypeInfo">
            <summary>
            Never emit type info for this type
            </summary>
        </member>
        <member name="P:ServiceStack.Text.JsConfig`1.TextCase">
            <summary>
            Text case to use for property names (Default = PascalCase)
            </summary>
        </member>
        <member name="P:ServiceStack.Text.JsConfig`1.EmitCamelCaseNames">
            <summary>
            Emitting camelCase for property names 
            </summary>
        </member>
        <member name="P:ServiceStack.Text.JsConfig`1.EmitLowercaseUnderscoreNames">
            <summary>
            Emitting lowercase_underscore_casing for property names 
            </summary>
        </member>
        <member name="F:ServiceStack.Text.JsConfig`1.serializeFn">
            <summary>
            Define custom serialization fn for BCL Structs
            </summary>
        </member>
        <member name="P:ServiceStack.Text.JsConfig`1.TreatValueAsRefType">
            <summary>
            Opt-in flag to set some Value Types to be treated as a Ref Type
            </summary>
        </member>
        <member name="P:ServiceStack.Text.JsConfig`1.HasSerializeFn">
            <summary>
            Whether there is a fn (raw or otherwise)
            </summary>
        </member>
        <member name="F:ServiceStack.Text.JsConfig`1.rawSerializeFn">
            <summary>
            Define custom raw serialization fn
            </summary>
        </member>
        <member name="F:ServiceStack.Text.JsConfig`1.onSerializingFn">
            <summary>
            Define custom serialization hook
            </summary>
        </member>
        <member name="F:ServiceStack.Text.JsConfig`1.onSerializedFn">
            <summary>
            Define custom after serialization hook
            </summary>
        </member>
        <member name="F:ServiceStack.Text.JsConfig`1.deSerializeFn">
            <summary>
            Define custom deserialization fn for BCL Structs
            </summary>
        </member>
        <member name="F:ServiceStack.Text.JsConfig`1.rawDeserializeFn">
            <summary>
            Define custom raw deserialization fn for objects
            </summary>
        </member>
        <member name="F:ServiceStack.Text.JsConfig`1.ExcludePropertyNames">
            <summary>
            Exclude specific properties of this type from being serialized
            </summary>
        </member>
        <member name="F:ServiceStack.Text.PropertyConvention.Strict">
            <summary>
            The property names on target types must match property names in the JSON source
            </summary>
        </member>
        <member name="F:ServiceStack.Text.PropertyConvention.Lenient">
            <summary>
            The property names on target types may not match the property names in the JSON source
            </summary>
        </member>
        <member name="F:ServiceStack.Text.TimeSpanHandler.DurationFormat">
            <summary>
            Uses the xsd format like PT15H10M20S
            </summary>
        </member>
        <member name="F:ServiceStack.Text.TimeSpanHandler.StandardFormat">
            <summary>
            Uses the standard .net ToString method of the TimeSpan class
            </summary>
        </member>
        <member name="F:ServiceStack.Text.TextCase.Default">
            <summary>
            If unspecified uses PascalCase
            </summary>
        </member>
        <member name="F:ServiceStack.Text.TextCase.PascalCase">
            <summary>
            PascalCase
            </summary>
        </member>
        <member name="F:ServiceStack.Text.TextCase.CamelCase">
            <summary>
            camelCase
            </summary>
        </member>
        <member name="F:ServiceStack.Text.TextCase.SnakeCase">
            <summary>
            snake_case
            </summary>
        </member>
        <member name="M:ServiceStack.Text.Config.UnsafeInit(ServiceStack.Text.Config)">
            <summary>
            Bypass Init checks. Only call on Startup.
            </summary>
            <param name="config"></param>
        </member>
        <member name="M:ServiceStack.Text.JsonExtensions.Get``1(System.Collections.Generic.Dictionary{System.String,System.String},System.String,``0)">
            <summary>
            Get JSON string value converted to T
            </summary>
        </member>
        <member name="M:ServiceStack.Text.JsonExtensions.Get(System.Collections.Generic.Dictionary{System.String,System.String},System.String)">
            <summary>
            Get JSON string value
            </summary>
        </member>
        <member name="P:ServiceStack.Text.JsonObject.Item(System.String)">
            <summary>
            Get JSON string value
            </summary>
        </member>
        <member name="M:ServiceStack.Text.JsonObject.GetUnescaped(System.String)">
            <summary>
            Get unescaped string value
            </summary>
        </member>
        <member name="M:ServiceStack.Text.JsonObject.Child(System.String)">
            <summary>
            Get unescaped string value
            </summary>
        </member>
        <member name="M:ServiceStack.Text.JsonObject.WriteValue(System.IO.TextWriter,System.Object)">
            <summary>
            Write JSON Array, Object, bool or number values as raw string
            </summary>
        </member>
        <member name="T:ServiceStack.Text.JsonSerializer">
            <summary>
            Creates an instance of a Type from a string value
            </summary>
        </member>
        <member name="M:ServiceStack.Text.JsonSerializer`1.DeserializeFromString(System.String)">
            <summary>
            Parses the specified value.
            </summary>
            <param name="value">The value.</param>
            <returns></returns>
        </member>
        <member name="M:ServiceStack.Text.Json.JsonTypeSerializer.WriteRawString(System.IO.TextWriter,System.String)">
            <summary>
            Shortcut escape when we're sure value doesn't contain any escaped chars
            </summary>
            <param name="writer"></param>
            <param name="value"></param>
        </member>
        <member name="M:ServiceStack.Text.Json.JsonTypeSerializer.ConvertFromUtf32(System.Int32)">
            <summary>
            Given a character as utf32, returns the equivalent string provided that the character
            is legal json.
            </summary>
            <param name="utf32"></param>
            <returns></returns>
        </member>
        <member name="F:ServiceStack.Text.Json.JsonUtils.EscapedBackslash">
            <summary>
            Micro-optimization keep pre-built char arrays saving a .ToCharArray() + function call (see .net implementation of .Write(string))
            </summary>
        </member>
        <member name="M:ServiceStack.Text.Json.JsonUtils.HasAnyEscapeChars(System.String,System.Boolean)">
            <summary>
            Searches the string for one or more non-printable characters.
            </summary>
            <param name="value">The string to search.</param>
            <param name="escapeHtmlChars"></param>
            <returns>True if there are any characters that require escaping. False if the value can be written verbatim.</returns>
            <remarks>
            Micro optimizations: since quote and backslash are the only printable characters requiring escaping, removed previous optimization
            (using flags instead of value.IndexOfAny(EscapeChars)) in favor of two equality operations saving both memory and CPU time.
            Also slightly reduced code size by re-arranging conditions.
            TODO: Possible Linq-only solution requires profiling: return value.Any(c => !c.IsPrintable() || c == QuoteChar || c == EscapeChar);
            </remarks>
        </member>
        <member name="T:ServiceStack.Text.Json.JsonWriter`1">
            <summary>
            Implement the serializer using a more static approach
            </summary>
            <typeparam name="T"></typeparam>
        </member>
        <member name="T:ServiceStack.Text.Jsv.JsvWriter`1">
            <summary>
            Implement the serializer using a more static approach
            </summary>
            <typeparam name="T"></typeparam>
        </member>
        <member name="T:ServiceStack.Text.Marc.Link`2">
            <summary>
            Pretty Thread-Safe cache class from:
            http://code.google.com/p/dapper-dot-net/source/browse/Dapper/SqlMapper.cs
            
            This is a micro-cache; suitable when the number of terms is controllable (a few hundred, for example),
            and strictly append-only; you cannot change existing values. All key matches are on **REFERENCE**
            equality. The type is fully thread-safe.
            </summary>
        </member>
        <member name="T:ServiceStack.Text.FastMember.ObjectAccessor">
            <summary>
            Represents an individual object, allowing access to members by-name
            </summary>
        </member>
        <member name="P:ServiceStack.Text.FastMember.ObjectAccessor.Item(System.String)">
            <summary>
            Get or Set the value of a named member for the underlying object
            </summary>
        </member>
        <member name="P:ServiceStack.Text.FastMember.ObjectAccessor.Target">
            <summary>
            The object represented by this instance
            </summary>
        </member>
        <member name="M:ServiceStack.Text.FastMember.ObjectAccessor.Equals(System.Object)">
            <summary>
            Use the target types definition of equality
            </summary>
        </member>
        <member name="M:ServiceStack.Text.FastMember.ObjectAccessor.GetHashCode">
            <summary>
            Obtain the hash of the target object
            </summary>
        </member>
        <member name="M:ServiceStack.Text.FastMember.ObjectAccessor.ToString">
            <summary>
            Use the target's definition of a string representation
            </summary>
        </member>
        <member name="M:ServiceStack.Text.FastMember.ObjectAccessor.Create(System.Object)">
            <summary>
            Wraps an individual object, allowing by-name access to that instance
            </summary>
        </member>
        <member name="T:ServiceStack.Text.FastMember.TypeAccessor">
            <summary>
            Provides by-name member-access to objects of a given type
            </summary>
        </member>
        <member name="P:ServiceStack.Text.FastMember.TypeAccessor.CreateNewSupported">
            <summary>
            Does this type support new instances via a parameterless constructor?
            </summary>
        </member>
        <member name="M:ServiceStack.Text.FastMember.TypeAccessor.CreateNew">
            <summary>
            Create a new instance of this type
            </summary>
        </member>
        <member name="M:ServiceStack.Text.FastMember.TypeAccessor.Create(System.Type)">
            <summary>
            Provides a type-specific accessor, allowing by-name access for all objects of that type
            </summary>
            <remarks>The accessor is cached internally; a pre-existing accessor may be returned</remarks>
        </member>
        <member name="P:ServiceStack.Text.FastMember.TypeAccessor.Item(System.Object,System.String)">
            <summary>
            Get or set the value of a named member on the target instance
            </summary>
        </member>
        <member name="T:ServiceStack.Text.Pools.BufferPool">
            <summary>
            Courtesy of @marcgravell
            https://github.com/mgravell/protobuf-net/blob/master/src/protobuf-net/BufferPool.cs
            </summary>
        </member>
        <member name="F:ServiceStack.Text.Pools.BufferPool.MaxByteArraySize">
            <remarks>
            https://docs.microsoft.com/en-us/dotnet/framework/configure-apps/file-schema/runtime/gcallowverylargeobjects-element
            </remarks>
        </member>
        <member name="F:ServiceStack.Text.Pools.CharPool.MaxcharArraySize">
            <remarks>
            https://docs.microsoft.com/en-us/dotnet/framework/configure-apps/file-schema/runtime/gcallowverylargeobjects-element
            </remarks>
        </member>
        <member name="T:ServiceStack.Text.Pools.ObjectPool`1">
            <summary>
            Generic implementation of object pooling pattern with predefined pool size limit. The main
            purpose is that limited number of frequently used objects can be kept in the pool for
            further recycling.
            
            Notes: 
            1) it is not the goal to keep all returned objects. Pool is not meant for storage. If there
               is no space in the pool, extra returned objects will be dropped.
            
            2) it is implied that if object was obtained from a pool, the caller will return it back in
               a relatively short time. Keeping checked out objects for long durations is ok, but 
               reduces usefulness of pooling. Just new up your own.
            
            Not returning objects to the pool in not detrimental to the pool's work, but is a bad practice. 
            Rationale: 
               If there is no intent for reusing the object, do not use pool - just use "new". 
            </summary>
        </member>
        <member name="T:ServiceStack.Text.Pools.ObjectPool`1.Factory">
            <remarks>
            Not using System.Func{T} because this file is linked into the (debugger) Formatter,
            which does not have that type (since it compiles against .NET 2.0).
            </remarks>
        </member>
        <member name="M:ServiceStack.Text.Pools.ObjectPool`1.Allocate">
            <summary>
            Produces an instance.
            </summary>
            <remarks>
            Search strategy is a simple linear probing which is chosen for it cache-friendliness.
            Note that Free will try to store recycled objects close to the start thus statistically 
            reducing how far we will typically search.
            </remarks>
        </member>
        <member name="M:ServiceStack.Text.Pools.ObjectPool`1.Free(`0)">
            <summary>
            Returns objects to the pool.
            </summary>
            <remarks>
            Search strategy is a simple linear probing which is chosen for it cache-friendliness.
            Note that Free will try to store recycled objects close to the start thus statistically 
            reducing how far we will typically search in Allocate.
            </remarks>
        </member>
        <member name="M:ServiceStack.Text.Pools.ObjectPool`1.ForgetTrackedObject(`0,`0)">
            <summary>
            Removes an object from leak tracking.  
            
            This is called when an object is returned to the pool.  It may also be explicitly 
            called if an object allocated from the pool is intentionally not being returned
            to the pool.  This can be of use with pooled arrays if the consumer wants to 
            return a larger array to the pool than was originally allocated.
            </summary>
        </member>
        <member name="T:ServiceStack.Text.Pools.PooledObject`1">
            <summary>
            this is RAII object to automatically release pooled object when its owning pool
            </summary>
        </member>
        <member name="T:ServiceStack.Text.Pools.SharedPools">
            <summary>
            Shared object pool for roslyn
            
            Use this shared pool if only concern is reducing object allocations.
            if perf of an object pool itself is also a concern, use ObjectPool directly.
            
            For example, if you want to create a million of small objects within a second, 
            use the ObjectPool directly. it should have much less overhead than using this.
            </summary>
        </member>
        <member name="M:ServiceStack.Text.Pools.SharedPools.BigDefault``1">
            <summary>
            pool that uses default constructor with 100 elements pooled
            </summary>
        </member>
        <member name="M:ServiceStack.Text.Pools.SharedPools.Default``1">
            <summary>
            pool that uses default constructor with 20 elements pooled
            </summary>
        </member>
        <member name="M:ServiceStack.Text.Pools.SharedPools.StringIgnoreCaseDictionary``1">
            <summary>
            pool that uses string as key with StringComparer.OrdinalIgnoreCase as key comparer
            </summary>
        </member>
        <member name="F:ServiceStack.Text.Pools.SharedPools.StringIgnoreCaseHashSet">
            <summary>
            pool that uses string as element with StringComparer.OrdinalIgnoreCase as element comparer
            </summary>
        </member>
        <member name="F:ServiceStack.Text.Pools.SharedPools.StringHashSet">
            <summary>
            pool that uses string as element with StringComparer.Ordinal as element comparer
            </summary>
        </member>
        <member name="F:ServiceStack.Text.Pools.SharedPools.ByteArray">
            <summary>
            Used to reduce the # of temporary byte[]s created to satisfy serialization and
            other I/O requests
            </summary>
        </member>
        <member name="F:ServiceStack.Text.Pools.SharedPools.ByteBufferSize">
            pooled memory : 4K * 512 = 4MB
        </member>
        <member name="T:ServiceStack.Text.RecyclableMemoryStreamManager">
             <summary>
             Manages pools of RecyclableMemoryStream objects.
             </summary>
             <remarks>
             There are two pools managed in here. The small pool contains same-sized buffers that are handed to streams
             as they write more data.
            
             For scenarios that need to call GetBuffer(), the large pool contains buffers of various sizes, all
             multiples/exponentials of LargeBufferMultiple (1 MB by default). They are split by size to avoid overly-wasteful buffer
             usage. There should be far fewer 8 MB buffers than 1 MB buffers, for example.
             </remarks>
        </member>
        <member name="T:ServiceStack.Text.RecyclableMemoryStreamManager.Events">
            <summary>
            ETW events for RecyclableMemoryStream
            </summary>
        </member>
        <member name="F:ServiceStack.Text.RecyclableMemoryStreamManager.Events.Writer">
            <summary>
            Static log object, through which all events are written.
            </summary>
        </member>
        <member name="T:ServiceStack.Text.RecyclableMemoryStreamManager.Events.MemoryStreamBufferType">
            <summary>
            Type of buffer
            </summary>
        </member>
        <member name="F:ServiceStack.Text.RecyclableMemoryStreamManager.Events.MemoryStreamBufferType.Small">
            <summary>
            Small block buffer
            </summary>
        </member>
        <member name="F:ServiceStack.Text.RecyclableMemoryStreamManager.Events.MemoryStreamBufferType.Large">
            <summary>
            Large pool buffer
            </summary>
        </member>
        <member name="T:ServiceStack.Text.RecyclableMemoryStreamManager.Events.MemoryStreamDiscardReason">
            <summary>
            The possible reasons for discarding a buffer
            </summary>
        </member>
        <member name="F:ServiceStack.Text.RecyclableMemoryStreamManager.Events.MemoryStreamDiscardReason.TooLarge">
            <summary>
            Buffer was too large to be re-pooled
            </summary>
        </member>
        <member name="F:ServiceStack.Text.RecyclableMemoryStreamManager.Events.MemoryStreamDiscardReason.EnoughFree">
            <summary>
            There are enough free bytes in the pool
            </summary>
        </member>
        <member name="M:ServiceStack.Text.RecyclableMemoryStreamManager.Events.MemoryStreamCreated(System.Guid,System.String,System.Int32)">
            <summary>
            Logged when a stream object is created.
            </summary>
            <param name="guid">A unique ID for this stream.</param>
            <param name="tag">A temporary ID for this stream, usually indicates current usage.</param>
            <param name="requestedSize">Requested size of the stream</param>
        </member>
        <member name="M:ServiceStack.Text.RecyclableMemoryStreamManager.Events.MemoryStreamDisposed(System.Guid,System.String)">
            <summary>
            Logged when the stream is disposed
            </summary>
            <param name="guid">A unique ID for this stream.</param>
            <param name="tag">A temporary ID for this stream, usually indicates current usage.</param>
        </member>
        <member name="M:ServiceStack.Text.RecyclableMemoryStreamManager.Events.MemoryStreamDoubleDispose(System.Guid,System.String,System.String,System.String,System.String)">
            <summary>
            Logged when the stream is disposed for the second time.
            </summary>
            <param name="guid">A unique ID for this stream.</param>
            <param name="tag">A temporary ID for this stream, usually indicates current usage.</param>
            <param name="allocationStack">Call stack of initial allocation.</param>
            <param name="disposeStack1">Call stack of the first dispose.</param>
            <param name="disposeStack2">Call stack of the second dispose.</param>
            <remarks>Note: Stacks will only be populated if RecyclableMemoryStreamManager.GenerateCallStacks is true.</remarks>
        </member>
        <member name="M:ServiceStack.Text.RecyclableMemoryStreamManager.Events.MemoryStreamFinalized(System.Guid,System.String,System.String)">
            <summary>
            Logged when a stream is finalized.
            </summary>
            <param name="guid">A unique ID for this stream.</param>
            <param name="tag">A temporary ID for this stream, usually indicates current usage.</param>
            <param name="allocationStack">Call stack of initial allocation.</param>
            <remarks>Note: Stacks will only be populated if RecyclableMemoryStreamManager.GenerateCallStacks is true.</remarks>
        </member>
        <member name="M:ServiceStack.Text.RecyclableMemoryStreamManager.Events.MemoryStreamToArray(System.Guid,System.String,System.String,System.Int32)">
            <summary>
            Logged when ToArray is called on a stream.
            </summary>
            <param name="guid">A unique ID for this stream.</param>
            <param name="tag">A temporary ID for this stream, usually indicates current usage.</param>
            <param name="stack">Call stack of the ToArray call.</param>
            <param name="size">Length of stream</param>
            <remarks>Note: Stacks will only be populated if RecyclableMemoryStreamManager.GenerateCallStacks is true.</remarks>
        </member>
        <member name="M:ServiceStack.Text.RecyclableMemoryStreamManager.Events.MemoryStreamManagerInitialized(System.Int32,System.Int32,System.Int32)">
            <summary>
            Logged when the RecyclableMemoryStreamManager is initialized.
            </summary>
            <param name="blockSize">Size of blocks, in bytes.</param>
            <param name="largeBufferMultiple">Size of the large buffer multiple, in bytes.</param>
            <param name="maximumBufferSize">Maximum buffer size, in bytes.</param>
        </member>
        <member name="M:ServiceStack.Text.RecyclableMemoryStreamManager.Events.MemoryStreamNewBlockCreated(System.Int64)">
            <summary>
            Logged when a new block is created.
            </summary>
            <param name="smallPoolInUseBytes">Number of bytes in the small pool currently in use.</param>
        </member>
        <member name="M:ServiceStack.Text.RecyclableMemoryStreamManager.Events.MemoryStreamNewLargeBufferCreated(System.Int32,System.Int64)">
            <summary>
            Logged when a new large buffer is created.
            </summary>
            <param name="requiredSize">Requested size</param>
            <param name="largePoolInUseBytes">Number of bytes in the large pool in use.</param>
        </member>
        <member name="M:ServiceStack.Text.RecyclableMemoryStreamManager.Events.MemoryStreamNonPooledLargeBufferCreated(System.Int32,System.String,System.String)">
            <summary>
            Logged when a buffer is created that is too large to pool.
            </summary>
            <param name="requiredSize">Size requested by the caller</param>
            <param name="tag">A temporary ID for this stream, usually indicates current usage.</param>
            <param name="allocationStack">Call stack of the requested stream.</param>
            <remarks>Note: Stacks will only be populated if RecyclableMemoryStreamManager.GenerateCallStacks is true.</remarks>
        </member>
        <member name="M:ServiceStack.Text.RecyclableMemoryStreamManager.Events.MemoryStreamDiscardBuffer(ServiceStack.Text.RecyclableMemoryStreamManager.Events.MemoryStreamBufferType,System.String,ServiceStack.Text.RecyclableMemoryStreamManager.Events.MemoryStreamDiscardReason)">
            <summary>
            Logged when a buffer is discarded (not put back in the pool, but given to GC to clean up).
            </summary>
            <param name="bufferType">Type of the buffer being discarded.</param>
            <param name="tag">A temporary ID for this stream, usually indicates current usage.</param>
            <param name="reason">Reason for the discard.</param>
        </member>
        <member name="M:ServiceStack.Text.RecyclableMemoryStreamManager.Events.MemoryStreamOverCapacity(System.Int32,System.Int64,System.String,System.String)">
            <summary>
            Logged when a stream grows beyond the maximum capacity.
            </summary>
            <param name="requestedCapacity">The requested capacity.</param>
            <param name="maxCapacity">Maximum capacity, as configured by RecyclableMemoryStreamManager.</param>
            <param name="tag">A temporary ID for this stream, usually indicates current usage.</param>
            <param name="allocationStack">Call stack for the capacity request.</param>
            <remarks>Note: Stacks will only be populated if RecyclableMemoryStreamManager.GenerateCallStacks is true.</remarks>
        </member>
        <member name="T:ServiceStack.Text.RecyclableMemoryStreamManager.EventHandler">
            <summary>
            Generic delegate for handling events without any arguments.
            </summary>
        </member>
        <member name="T:ServiceStack.Text.RecyclableMemoryStreamManager.LargeBufferDiscardedEventHandler">
            <summary>
            Delegate for handling large buffer discard reports.
            </summary>
            <param name="reason">Reason the buffer was discarded.</param>
        </member>
        <member name="T:ServiceStack.Text.RecyclableMemoryStreamManager.StreamLengthReportHandler">
            <summary>
            Delegate for handling reports of stream size when streams are allocated
            </summary>
            <param name="bytes">Bytes allocated.</param>
        </member>
        <member name="T:ServiceStack.Text.RecyclableMemoryStreamManager.UsageReportEventHandler">
            <summary>
            Delegate for handling periodic reporting of memory use statistics.
            </summary>
            <param name="smallPoolInUseBytes">Bytes currently in use in the small pool.</param>
            <param name="smallPoolFreeBytes">Bytes currently free in the small pool.</param>
            <param name="largePoolInUseBytes">Bytes currently in use in the large pool.</param>
            <param name="largePoolFreeBytes">Bytes currently free in the large pool.</param>
        </member>
        <member name="F:ServiceStack.Text.RecyclableMemoryStreamManager.DefaultBlockSize">
            <summary>
            Default block size, in bytes
            </summary>
        </member>
        <member name="F:ServiceStack.Text.RecyclableMemoryStreamManager.DefaultLargeBufferMultiple">
            <summary>
            Default large buffer multiple, in bytes
            </summary>
        </member>
        <member name="F:ServiceStack.Text.RecyclableMemoryStreamManager.DefaultMaximumBufferSize">
            <summary>
            Default maximum buffer size, in bytes
            </summary>
        </member>
        <member name="M:ServiceStack.Text.RecyclableMemoryStreamManager.#ctor">
            <summary>
            Initializes the memory manager with the default block/buffer specifications.
            </summary>
        </member>
        <member name="M:ServiceStack.Text.RecyclableMemoryStreamManager.#ctor(System.Int32,System.Int32,System.Int32)">
            <summary>
            Initializes the memory manager with the given block requiredSize.
            </summary>
            <param name="blockSize">Size of each block that is pooled. Must be > 0.</param>
            <param name="largeBufferMultiple">Each large buffer will be a multiple of this value.</param>
            <param name="maximumBufferSize">Buffers larger than this are not pooled</param>
            <exception cref="T:System.ArgumentOutOfRangeException">blockSize is not a positive number, or largeBufferMultiple is not a positive number, or maximumBufferSize is less than blockSize.</exception>
            <exception cref="T:System.ArgumentException">maximumBufferSize is not a multiple of largeBufferMultiple</exception>
        </member>
        <member name="M:ServiceStack.Text.RecyclableMemoryStreamManager.#ctor(System.Int32,System.Int32,System.Int32,System.Boolean)">
            <summary>
            Initializes the memory manager with the given block requiredSize.
            </summary>
            <param name="blockSize">Size of each block that is pooled. Must be > 0.</param>
            <param name="largeBufferMultiple">Each large buffer will be a multiple/exponential of this value.</param>
            <param name="maximumBufferSize">Buffers larger than this are not pooled</param>
            <param name="useExponentialLargeBuffer">Switch to exponential large buffer allocation strategy</param>
            <exception cref="T:System.ArgumentOutOfRangeException">blockSize is not a positive number, or largeBufferMultiple is not a positive number, or maximumBufferSize is less than blockSize.</exception>
            <exception cref="T:System.ArgumentException">maximumBufferSize is not a multiple/exponential of largeBufferMultiple</exception>
        </member>
        <member name="P:ServiceStack.Text.RecyclableMemoryStreamManager.BlockSize">
            <summary>
            The size of each block. It must be set at creation and cannot be changed.
            </summary>
        </member>
        <member name="P:ServiceStack.Text.RecyclableMemoryStreamManager.LargeBufferMultiple">
            <summary>
            All buffers are multiples/exponentials of this number. It must be set at creation and cannot be changed.
            </summary>
        </member>
        <member name="P:ServiceStack.Text.RecyclableMemoryStreamManager.UseMultipleLargeBuffer">
            <summary>
            Use multiple large buffer allocation strategy. It must be set at creation and cannot be changed.
            </summary>
        </member>
        <member name="P:ServiceStack.Text.RecyclableMemoryStreamManager.UseExponentialLargeBuffer">
            <summary>
            Use exponential large buffer allocation strategy. It must be set at creation and cannot be changed.
            </summary>
        </member>
        <member name="P:ServiceStack.Text.RecyclableMemoryStreamManager.MaximumBufferSize">
            <summary>
            Gets the maximum buffer size.
            </summary>
            <remarks>Any buffer that is returned to the pool that is larger than this will be
            discarded and garbage collected.</remarks>
        </member>
        <member name="P:ServiceStack.Text.RecyclableMemoryStreamManager.SmallPoolFreeSize">
            <summary>
            Number of bytes in small pool not currently in use
            </summary>
        </member>
        <member name="P:ServiceStack.Text.RecyclableMemoryStreamManager.SmallPoolInUseSize">
            <summary>
            Number of bytes currently in use by stream from the small pool
            </summary>
        </member>
        <member name="P:ServiceStack.Text.RecyclableMemoryStreamManager.LargePoolFreeSize">
            <summary>
            Number of bytes in large pool not currently in use
            </summary>
        </member>
        <member name="P:ServiceStack.Text.RecyclableMemoryStreamManager.LargePoolInUseSize">
            <summary>
            Number of bytes currently in use by streams from the large pool
            </summary>
        </member>
        <member name="P:ServiceStack.Text.RecyclableMemoryStreamManager.SmallBlocksFree">
            <summary>
            How many blocks are in the small pool
            </summary>
        </member>
        <member name="P:ServiceStack.Text.RecyclableMemoryStreamManager.LargeBuffersFree">
            <summary>
            How many buffers are in the large pool
            </summary>
        </member>
        <member name="P:ServiceStack.Text.RecyclableMemoryStreamManager.MaximumFreeSmallPoolBytes">
            <summary>
            How many bytes of small free blocks to allow before we start dropping
            those returned to us.
            </summary>
        </member>
        <member name="P:ServiceStack.Text.RecyclableMemoryStreamManager.MaximumFreeLargePoolBytes">
            <summary>
            How many bytes of large free buffers to allow before we start dropping
            those returned to us.
            </summary>
        </member>
        <member name="P:ServiceStack.Text.RecyclableMemoryStreamManager.MaximumStreamCapacity">
            <summary>
            Maximum stream capacity in bytes. Attempts to set a larger capacity will
            result in an exception.
            </summary>
            <remarks>A value of 0 indicates no limit.</remarks>
        </member>
        <member name="P:ServiceStack.Text.RecyclableMemoryStreamManager.GenerateCallStacks">
            <summary>
            Whether to save callstacks for stream allocations. This can help in debugging.
            It should NEVER be turned on generally in production.
            </summary>
        </member>
        <member name="P:ServiceStack.Text.RecyclableMemoryStreamManager.AggressiveBufferReturn">
            <summary>
            Whether dirty buffers can be immediately returned to the buffer pool. E.g. when GetBuffer() is called on
            a stream and creates a single large buffer, if this setting is enabled, the other blocks will be returned
            to the buffer pool immediately.
            Note when enabling this setting that the user is responsible for ensuring that any buffer previously
            retrieved from a stream which is subsequently modified is not used after modification (as it may no longer
            be valid).
            </summary>
        </member>
        <member name="P:ServiceStack.Text.RecyclableMemoryStreamManager.ThrowExceptionOnToArray">
            <summary>
            Causes an exception to be thrown if ToArray is ever called.
            </summary>
            <remarks>Calling ToArray defeats the purpose of a pooled buffer. Use this property to discover code that is calling ToArray. If this is 
            set and stream.ToArray() is called, a NotSupportedException will be thrown.</remarks>
        </member>
        <member name="M:ServiceStack.Text.RecyclableMemoryStreamManager.GetBlock">
            <summary>
            Removes and returns a single block from the pool.
            </summary>
            <returns>A byte[] array</returns>
        </member>
        <member name="M:ServiceStack.Text.RecyclableMemoryStreamManager.GetLargeBuffer(System.Int32,System.String)">
            <summary>
            Returns a buffer of arbitrary size from the large buffer pool. This buffer
            will be at least the requiredSize and always be a multiple/exponential of largeBufferMultiple.
            </summary>
            <param name="requiredSize">The minimum length of the buffer</param>
            <param name="tag">The tag of the stream returning this buffer, for logging if necessary.</param>
            <returns>A buffer of at least the required size.</returns>
        </member>
        <member name="M:ServiceStack.Text.RecyclableMemoryStreamManager.ReturnLargeBuffer(System.Byte[],System.String)">
            <summary>
            Returns the buffer to the large pool
            </summary>
            <param name="buffer">The buffer to return.</param>
            <param name="tag">The tag of the stream returning this buffer, for logging if necessary.</param>
            <exception cref="T:System.ArgumentNullException">buffer is null</exception>
            <exception cref="T:System.ArgumentException">buffer.Length is not a multiple/exponential of LargeBufferMultiple (it did not originate from this pool)</exception>
        </member>
        <member name="M:ServiceStack.Text.RecyclableMemoryStreamManager.ReturnBlocks(System.Collections.Generic.ICollection{System.Byte[]},System.String)">
            <summary>
            Returns the blocks to the pool
            </summary>
            <param name="blocks">Collection of blocks to return to the pool</param>
            <param name="tag">The tag of the stream returning these blocks, for logging if necessary.</param>
            <exception cref="T:System.ArgumentNullException">blocks is null</exception>
            <exception cref="T:System.ArgumentException">blocks contains buffers that are the wrong size (or null) for this memory manager</exception>
        </member>
        <member name="M:ServiceStack.Text.RecyclableMemoryStreamManager.GetStream">
            <summary>
            Retrieve a new MemoryStream object with no tag and a default initial capacity.
            </summary>
            <returns>A MemoryStream.</returns>
        </member>
        <member name="M:ServiceStack.Text.RecyclableMemoryStreamManager.GetStream(System.Guid)">
            <summary>
            Retrieve a new MemoryStream object with no tag and a default initial capacity.
            </summary>
            <param name="id">A unique identifier which can be used to trace usages of the stream.</param>
            <returns>A MemoryStream.</returns>
        </member>
        <member name="M:ServiceStack.Text.RecyclableMemoryStreamManager.GetStream(System.String)">
            <summary>
            Retrieve a new MemoryStream object with the given tag and a default initial capacity.
            </summary>
            <param name="tag">A tag which can be used to track the source of the stream.</param>
            <returns>A MemoryStream.</returns>
        </member>
        <member name="M:ServiceStack.Text.RecyclableMemoryStreamManager.GetStream(System.Guid,System.String)">
            <summary>
            Retrieve a new MemoryStream object with the given tag and a default initial capacity.
            </summary>
            <param name="id">A unique identifier which can be used to trace usages of the stream.</param>
            <param name="tag">A tag which can be used to track the source of the stream.</param>
            <returns>A MemoryStream.</returns>
        </member>
        <member name="M:ServiceStack.Text.RecyclableMemoryStreamManager.GetStream(System.String,System.Int32)">
            <summary>
            Retrieve a new MemoryStream object with the given tag and at least the given capacity.
            </summary>
            <param name="tag">A tag which can be used to track the source of the stream.</param>
            <param name="requiredSize">The minimum desired capacity for the stream.</param>
            <returns>A MemoryStream.</returns>
        </member>
        <member name="M:ServiceStack.Text.RecyclableMemoryStreamManager.GetStream(System.Guid,System.String,System.Int32)">
            <summary>
            Retrieve a new MemoryStream object with the given tag and at least the given capacity.
            </summary>
            <param name="id">A unique identifier which can be used to trace usages of the stream.</param>
            <param name="tag">A tag which can be used to track the source of the stream.</param>
            <param name="requiredSize">The minimum desired capacity for the stream.</param>
            <returns>A MemoryStream.</returns>
        </member>
        <member name="M:ServiceStack.Text.RecyclableMemoryStreamManager.GetStream(System.Guid,System.String,System.Int32,System.Boolean)">
            <summary>
            Retrieve a new MemoryStream object with the given tag and at least the given capacity, possibly using
            a single contiguous underlying buffer.
            </summary>
            <remarks>Retrieving a MemoryStream which provides a single contiguous buffer can be useful in situations
            where the initial size is known and it is desirable to avoid copying data between the smaller underlying
            buffers to a single large one. This is most helpful when you know that you will always call GetBuffer
            on the underlying stream.</remarks>
            <param name="id">A unique identifier which can be used to trace usages of the stream.</param>
            <param name="tag">A tag which can be used to track the source of the stream.</param>
            <param name="requiredSize">The minimum desired capacity for the stream.</param>
            <param name="asContiguousBuffer">Whether to attempt to use a single contiguous buffer.</param>
            <returns>A MemoryStream.</returns>
        </member>
        <member name="M:ServiceStack.Text.RecyclableMemoryStreamManager.GetStream(System.String,System.Int32,System.Boolean)">
            <summary>
            Retrieve a new MemoryStream object with the given tag and at least the given capacity, possibly using
            a single contiguous underlying buffer.
            </summary>
            <remarks>Retrieving a MemoryStream which provides a single contiguous buffer can be useful in situations
            where the initial size is known and it is desirable to avoid copying data between the smaller underlying
            buffers to a single large one. This is most helpful when you know that you will always call GetBuffer
            on the underlying stream.</remarks>
            <param name="tag">A tag which can be used to track the source of the stream.</param>
            <param name="requiredSize">The minimum desired capacity for the stream.</param>
            <param name="asContiguousBuffer">Whether to attempt to use a single contiguous buffer.</param>
            <returns>A MemoryStream.</returns>
        </member>
        <member name="M:ServiceStack.Text.RecyclableMemoryStreamManager.GetStream(System.Guid,System.String,System.Byte[],System.Int32,System.Int32)">
            <summary>
            Retrieve a new MemoryStream object with the given tag and with contents copied from the provided
            buffer. The provided buffer is not wrapped or used after construction.
            </summary>
            <remarks>The new stream's position is set to the beginning of the stream when returned.</remarks>
            <param name="id">A unique identifier which can be used to trace usages of the stream.</param>
            <param name="tag">A tag which can be used to track the source of the stream.</param>
            <param name="buffer">The byte buffer to copy data from.</param>
            <param name="offset">The offset from the start of the buffer to copy from.</param>
            <param name="count">The number of bytes to copy from the buffer.</param>
            <returns>A MemoryStream.</returns>
        </member>
        <member name="M:ServiceStack.Text.RecyclableMemoryStreamManager.GetStream(System.Byte[])">
            <summary>
            Retrieve a new MemoryStream object with the contents copied from the provided
            buffer. The provided buffer is not wrapped or used after construction.
            </summary>
            <remarks>The new stream's position is set to the beginning of the stream when returned.</remarks>
            <param name="buffer">The byte buffer to copy data from.</param>
            <returns>A MemoryStream.</returns>
        </member>
        <member name="M:ServiceStack.Text.RecyclableMemoryStreamManager.GetStream(System.String,System.Byte[],System.Int32,System.Int32)">
            <summary>
            Retrieve a new MemoryStream object with the given tag and with contents copied from the provided
            buffer. The provided buffer is not wrapped or used after construction.
            </summary>
            <remarks>The new stream's position is set to the beginning of the stream when returned.</remarks>
            <param name="tag">A tag which can be used to track the source of the stream.</param>
            <param name="buffer">The byte buffer to copy data from.</param>
            <param name="offset">The offset from the start of the buffer to copy from.</param>
            <param name="count">The number of bytes to copy from the buffer.</param>
            <returns>A MemoryStream.</returns>
        </member>
        <member name="E:ServiceStack.Text.RecyclableMemoryStreamManager.BlockCreated">
            <summary>
            Triggered when a new block is created.
            </summary>
        </member>
        <member name="E:ServiceStack.Text.RecyclableMemoryStreamManager.BlockDiscarded">
            <summary>
            Triggered when a new block is created.
            </summary>
        </member>
        <member name="E:ServiceStack.Text.RecyclableMemoryStreamManager.LargeBufferCreated">
            <summary>
            Triggered when a new large buffer is created.
            </summary>
        </member>
        <member name="E:ServiceStack.Text.RecyclableMemoryStreamManager.StreamCreated">
            <summary>
            Triggered when a new stream is created.
            </summary>
        </member>
        <member name="E:ServiceStack.Text.RecyclableMemoryStreamManager.StreamDisposed">
            <summary>
            Triggered when a stream is disposed.
            </summary>
        </member>
        <member name="E:ServiceStack.Text.RecyclableMemoryStreamManager.StreamFinalized">
            <summary>
            Triggered when a stream is finalized.
            </summary>
        </member>
        <member name="E:ServiceStack.Text.RecyclableMemoryStreamManager.StreamLength">
            <summary>
            Triggered when a stream is finalized.
            </summary>
        </member>
        <member name="E:ServiceStack.Text.RecyclableMemoryStreamManager.StreamConvertedToArray">
            <summary>
            Triggered when a user converts a stream to array.
            </summary>
        </member>
        <member name="E:ServiceStack.Text.RecyclableMemoryStreamManager.LargeBufferDiscarded">
            <summary>
            Triggered when a large buffer is discarded, along with the reason for the discard.
            </summary>
        </member>
        <member name="E:ServiceStack.Text.RecyclableMemoryStreamManager.UsageReport">
            <summary>
            Periodically triggered to report usage statistics.
            </summary>
        </member>
        <member name="T:ServiceStack.Text.RecyclableMemoryStream">
            <summary>
            MemoryStream implementation that deals with pooling and managing memory streams which use potentially large
            buffers.
            </summary>
            <remarks>
            This class works in tandem with the RecyclableMemoryStreamManager to supply MemoryStream
            objects to callers, while avoiding these specific problems:
            1. LOH allocations - since all large buffers are pooled, they will never incur a Gen2 GC
            2. Memory waste - A standard memory stream doubles its size when it runs out of room. This
            leads to continual memory growth as each stream approaches the maximum allowed size.
            3. Memory copying - Each time a MemoryStream grows, all the bytes are copied into new buffers.
            This implementation only copies the bytes when GetBuffer is called.
            4. Memory fragmentation - By using homogeneous buffer sizes, it ensures that blocks of memory
            can be easily reused.
            
            The stream is implemented on top of a series of uniformly-sized blocks. As the stream's length grows,
            additional blocks are retrieved from the memory manager. It is these blocks that are pooled, not the stream
            object itself.
            
            The biggest wrinkle in this implementation is when GetBuffer() is called. This requires a single 
            contiguous buffer. If only a single block is in use, then that block is returned. If multiple blocks 
            are in use, we retrieve a larger buffer from the memory manager. These large buffers are also pooled, 
            split by size--they are multiples/exponentials of a chunk size (1 MB by default).
            
            Once a large buffer is assigned to the stream the small blocks are NEVER again used for this stream. All operations take place on the 
            large buffer. The large buffer can be replaced by a larger buffer from the pool as needed. All blocks and large buffers 
            are maintained in the stream until the stream is disposed (unless AggressiveBufferReturn is enabled in the stream manager).
            
            </remarks>
        </member>
        <member name="F:ServiceStack.Text.RecyclableMemoryStream.blocks">
            <summary>
            All of these blocks must be the same size
            </summary>
        </member>
        <member name="F:ServiceStack.Text.RecyclableMemoryStream.dirtyBuffers">
            <summary>
            This list is used to store buffers once they're replaced by something larger.
            This is for the cases where you have users of this class that may hold onto the buffers longer
            than they should and you want to prevent race conditions which could corrupt the data.
            </summary>
        </member>
        <member name="F:ServiceStack.Text.RecyclableMemoryStream.largeBuffer">
            <summary>
            This is only set by GetBuffer() if the necessary buffer is larger than a single block size, or on
            construction if the caller immediately requests a single large buffer.
            </summary>
            <remarks>If this field is non-null, it contains the concatenation of the bytes found in the individual
            blocks. Once it is created, this (or a larger) largeBuffer will be used for the life of the stream.
            </remarks>
        </member>
        <member name="P:ServiceStack.Text.RecyclableMemoryStream.Id">
            <summary>
            Unique identifier for this stream across its entire lifetime
            </summary>
            <exception cref="T:System.ObjectDisposedException">Object has been disposed</exception>
        </member>
        <member name="P:ServiceStack.Text.RecyclableMemoryStream.Tag">
            <summary>
            A temporary identifier for the current usage of this stream.
            </summary>
            <exception cref="T:System.ObjectDisposedException">Object has been disposed</exception>
        </member>
        <member name="P:ServiceStack.Text.RecyclableMemoryStream.MemoryManager">
            <summary>
            Gets the memory manager being used by this stream.
            </summary>
            <exception cref="T:System.ObjectDisposedException">Object has been disposed</exception>
        </member>
        <member name="P:ServiceStack.Text.RecyclableMemoryStream.AllocationStack">
            <summary>
            Callstack of the constructor. It is only set if MemoryManager.GenerateCallStacks is true,
            which should only be in debugging situations.
            </summary>
        </member>
        <member name="P:ServiceStack.Text.RecyclableMemoryStream.DisposeStack">
            <summary>
            Callstack of the Dispose call. It is only set if MemoryManager.GenerateCallStacks is true,
            which should only be in debugging situations.
            </summary>
        </member>
        <member name="M:ServiceStack.Text.RecyclableMemoryStream.#ctor(ServiceStack.Text.RecyclableMemoryStreamManager)">
            <summary>
            Allocate a new RecyclableMemoryStream object.
            </summary>
            <param name="memoryManager">The memory manager</param>
        </member>
        <member name="M:ServiceStack.Text.RecyclableMemoryStream.#ctor(ServiceStack.Text.RecyclableMemoryStreamManager,System.Guid)">
            <summary>
            Allocate a new RecyclableMemoryStream object.
            </summary>
            <param name="memoryManager">The memory manager</param>
            <param name="id">A unique identifier which can be used to trace usages of the stream.</param>
        </member>
        <member name="M:ServiceStack.Text.RecyclableMemoryStream.#ctor(ServiceStack.Text.RecyclableMemoryStreamManager,System.String)">
            <summary>
            Allocate a new RecyclableMemoryStream object
            </summary>
            <param name="memoryManager">The memory manager</param>
            <param name="tag">A string identifying this stream for logging and debugging purposes</param>
        </member>
        <member name="M:ServiceStack.Text.RecyclableMemoryStream.#ctor(ServiceStack.Text.RecyclableMemoryStreamManager,System.Guid,System.String)">
            <summary>
            Allocate a new RecyclableMemoryStream object
            </summary>
            <param name="memoryManager">The memory manager</param>
            <param name="id">A unique identifier which can be used to trace usages of the stream.</param>
            <param name="tag">A string identifying this stream for logging and debugging purposes</param>
        </member>
        <member name="M:ServiceStack.Text.RecyclableMemoryStream.#ctor(ServiceStack.Text.RecyclableMemoryStreamManager,System.String,System.Int32)">
            <summary>
            Allocate a new RecyclableMemoryStream object
            </summary>
            <param name="memoryManager">The memory manager</param>
            <param name="tag">A string identifying this stream for logging and debugging purposes</param>
            <param name="requestedSize">The initial requested size to prevent future allocations</param>
        </member>
        <member name="M:ServiceStack.Text.RecyclableMemoryStream.#ctor(ServiceStack.Text.RecyclableMemoryStreamManager,System.Guid,System.String,System.Int32)">
            <summary>
            Allocate a new RecyclableMemoryStream object
            </summary>
            <param name="memoryManager">The memory manager</param>
            <param name="id">A unique identifier which can be used to trace usages of the stream.</param>
            <param name="tag">A string identifying this stream for logging and debugging purposes</param>
            <param name="requestedSize">The initial requested size to prevent future allocations</param>
        </member>
        <member name="M:ServiceStack.Text.RecyclableMemoryStream.#ctor(ServiceStack.Text.RecyclableMemoryStreamManager,System.Guid,System.String,System.Int32,System.Byte[])">
            <summary>
            Allocate a new RecyclableMemoryStream object
            </summary>
            <param name="memoryManager">The memory manager</param>
            <param name="id">A unique identifier which can be used to trace usages of the stream.</param>
            <param name="tag">A string identifying this stream for logging and debugging purposes</param>
            <param name="requestedSize">The initial requested size to prevent future allocations</param>
            <param name="initialLargeBuffer">An initial buffer to use. This buffer will be owned by the stream and returned to the memory manager upon Dispose.</param>
        </member>
        <member name="M:ServiceStack.Text.RecyclableMemoryStream.Finalize">
            <summary>
            The finalizer will be called when a stream is not disposed properly. 
            </summary>
            <remarks>Failing to dispose indicates a bug in the code using streams. Care should be taken to properly account for stream lifetime.</remarks>
        </member>
        <member name="M:ServiceStack.Text.RecyclableMemoryStream.Dispose(System.Boolean)">
            <summary>
            Returns the memory used by this stream back to the pool.
            </summary>
            <param name="disposing">Whether we're disposing (true), or being called by the finalizer (false)</param>
        </member>
        <member name="M:ServiceStack.Text.RecyclableMemoryStream.Close">
            <summary>
            Equivalent to Dispose
            </summary>
        </member>
        <member name="P:ServiceStack.Text.RecyclableMemoryStream.Capacity">
            <summary>
            Gets or sets the capacity
            </summary>
            <remarks>Capacity is always in multiples of the memory manager's block size, unless
            the large buffer is in use.  Capacity never decreases during a stream's lifetime. 
            Explicitly setting the capacity to a lower value than the current value will have no effect. 
            This is because the buffers are all pooled by chunks and there's little reason to 
            allow stream truncation.
            
            Writing past the current capacity will cause Capacity to automatically increase, until MaximumStreamCapacity is reached.
            </remarks>
            <exception cref="T:System.ObjectDisposedException">Object has been disposed</exception>
        </member>
        <member name="P:ServiceStack.Text.RecyclableMemoryStream.Length">
            <summary>
            Gets the number of bytes written to this stream.
            </summary>
            <exception cref="T:System.ObjectDisposedException">Object has been disposed</exception>
        </member>
        <member name="P:ServiceStack.Text.RecyclableMemoryStream.Position">
            <summary>
            Gets the current position in the stream
            </summary>
            <exception cref="T:System.ObjectDisposedException">Object has been disposed</exception>
        </member>
        <member name="P:ServiceStack.Text.RecyclableMemoryStream.CanRead">
            <summary>
            Whether the stream can currently read
            </summary>
        </member>
        <member name="P:ServiceStack.Text.RecyclableMemoryStream.CanSeek">
            <summary>
            Whether the stream can currently seek
            </summary>
        </member>
        <member name="P:ServiceStack.Text.RecyclableMemoryStream.CanTimeout">
            <summary>
            Always false
            </summary>
        </member>
        <member name="P:ServiceStack.Text.RecyclableMemoryStream.CanWrite">
            <summary>
            Whether the stream can currently write
            </summary>
        </member>
        <member name="M:ServiceStack.Text.RecyclableMemoryStream.GetBuffer">
            <summary>
            Returns a single buffer containing the contents of the stream.
            The buffer may be longer than the stream length.
            </summary>
            <returns>A byte[] buffer</returns>
            <remarks>IMPORTANT: Doing a Write() after calling GetBuffer() invalidates the buffer. The old buffer is held onto
            until Dispose is called, but the next time GetBuffer() is called, a new buffer from the pool will be required.</remarks>
            <exception cref="T:System.ObjectDisposedException">Object has been disposed</exception>
        </member>
        <member name="M:ServiceStack.Text.RecyclableMemoryStream.TryGetBuffer(System.ArraySegment{System.Byte}@)">
            <summary>
            Returns an ArraySegment that wraps a single buffer containing the contents of the stream.
            </summary>
            <param name="buffer">An ArraySegment containing a reference to the underlying bytes.</param>
            <returns>Always returns true.</returns>
            <remarks>GetBuffer has no failure modes (it always returns something, even if it's an empty buffer), therefore this method
            always returns a valid ArraySegment to the same buffer returned by GetBuffer.</remarks>
        </member>
        <member name="M:ServiceStack.Text.RecyclableMemoryStream.ToArray">
            <summary>
            Returns a new array with a copy of the buffer's contents. You should almost certainly be using GetBuffer combined with the Length to 
            access the bytes in this stream. Calling ToArray will destroy the benefits of pooled buffers, but it is included
            for the sake of completeness.
            </summary>
            <exception cref="T:System.ObjectDisposedException">Object has been disposed</exception>
            <exception cref="T:System.NotSupportedException">The current RecyclableStreamManager object disallows ToArray calls.</exception>
        </member>
        <member name="M:ServiceStack.Text.RecyclableMemoryStream.Read(System.Byte[],System.Int32,System.Int32)">
            <summary>
            Reads from the current position into the provided buffer
            </summary>
            <param name="buffer">Destination buffer</param>
            <param name="offset">Offset into buffer at which to start placing the read bytes.</param>
            <param name="count">Number of bytes to read.</param>
            <returns>The number of bytes read</returns>
            <exception cref="T:System.ArgumentNullException">buffer is null</exception>
            <exception cref="T:System.ArgumentOutOfRangeException">offset or count is less than 0</exception>
            <exception cref="T:System.ArgumentException">offset subtracted from the buffer length is less than count</exception>
            <exception cref="T:System.ObjectDisposedException">Object has been disposed</exception>
        </member>
        <member name="M:ServiceStack.Text.RecyclableMemoryStream.SafeRead(System.Byte[],System.Int32,System.Int32,System.Int32@)">
            <summary>
            Reads from the specified position into the provided buffer
            </summary>
            <param name="buffer">Destination buffer</param>
            <param name="offset">Offset into buffer at which to start placing the read bytes.</param>
            <param name="count">Number of bytes to read.</param>
            <param name="streamPosition">Position in the stream to start reading from</param>
            <returns>The number of bytes read</returns>
            <exception cref="T:System.ArgumentNullException">buffer is null</exception>
            <exception cref="T:System.ArgumentOutOfRangeException">offset or count is less than 0</exception>
            <exception cref="T:System.ArgumentException">offset subtracted from the buffer length is less than count</exception>
            <exception cref="T:System.ObjectDisposedException">Object has been disposed</exception>
        </member>
        <member name="M:ServiceStack.Text.RecyclableMemoryStream.Write(System.Byte[],System.Int32,System.Int32)">
            <summary>
            Writes the buffer to the stream
            </summary>
            <param name="buffer">Source buffer</param>
            <param name="offset">Start position</param>
            <param name="count">Number of bytes to write</param>
            <exception cref="T:System.ArgumentNullException">buffer is null</exception>
            <exception cref="T:System.ArgumentOutOfRangeException">offset or count is negative</exception>
            <exception cref="T:System.ArgumentException">buffer.Length - offset is not less than count</exception>
            <exception cref="T:System.ObjectDisposedException">Object has been disposed</exception>
        </member>
        <member name="M:ServiceStack.Text.RecyclableMemoryStream.ToString">
            <summary>
            Returns a useful string for debugging. This should not normally be called in actual production code.
            </summary>
        </member>
        <member name="M:ServiceStack.Text.RecyclableMemoryStream.WriteByte(System.Byte)">
            <summary>
            Writes a single byte to the current position in the stream.
            </summary>
            <param name="value">byte value to write</param>
            <exception cref="T:System.ObjectDisposedException">Object has been disposed</exception>
        </member>
        <member name="M:ServiceStack.Text.RecyclableMemoryStream.ReadByte">
            <summary>
            Reads a single byte from the current position in the stream.
            </summary>
            <returns>The byte at the current position, or -1 if the position is at the end of the stream.</returns>
            <exception cref="T:System.ObjectDisposedException">Object has been disposed</exception>
        </member>
        <member name="M:ServiceStack.Text.RecyclableMemoryStream.SafeReadByte(System.Int32@)">
            <summary>
            Reads a single byte from the specified position in the stream.
            </summary>
            <param name="streamPosition">The position in the stream to read from</param>
            <returns>The byte at the current position, or -1 if the position is at the end of the stream.</returns>
            <exception cref="T:System.ObjectDisposedException">Object has been disposed</exception>
        </member>
        <member name="M:ServiceStack.Text.RecyclableMemoryStream.SetLength(System.Int64)">
            <summary>
            Sets the length of the stream
            </summary>
            <exception cref="T:System.ArgumentOutOfRangeException">value is negative or larger than MaxStreamLength</exception>
            <exception cref="T:System.ObjectDisposedException">Object has been disposed</exception>
        </member>
        <member name="M:ServiceStack.Text.RecyclableMemoryStream.Seek(System.Int64,System.IO.SeekOrigin)">
            <summary>
            Sets the position to the offset from the seek location
            </summary>
            <param name="offset">How many bytes to move</param>
            <param name="loc">From where</param>
            <returns>The new position</returns>
            <exception cref="T:System.ObjectDisposedException">Object has been disposed</exception>
            <exception cref="T:System.ArgumentOutOfRangeException">offset is larger than MaxStreamLength</exception>
            <exception cref="T:System.ArgumentException">Invalid seek origin</exception>
            <exception cref="T:System.IO.IOException">Attempt to set negative position</exception>
        </member>
        <member name="M:ServiceStack.Text.RecyclableMemoryStream.WriteTo(System.IO.Stream)">
            <summary>
            Synchronously writes this stream's bytes to the argument stream.
            </summary>
            <param name="stream">Destination stream</param>
            <remarks>Important: This does a synchronous write, which may not be desired in some situations</remarks>
            <exception cref="T:System.ArgumentNullException">stream is null</exception>
        </member>
        <member name="M:ServiceStack.Text.RecyclableMemoryStream.WriteTo(System.IO.Stream,System.Int32,System.Int32)">
            <summary>
            Synchronously writes this stream's bytes, starting at offset, for count bytes, to the argument stream.
            </summary>
            <param name="stream">Destination stream</param>
            <param name="offset">Offset in source</param>
            <param name="count">Number of bytes to write</param>
            <exception cref="T:System.ArgumentNullException">stream is null</exception>
            <exception cref="T:System.ArgumentOutOfRangeException">Offset is less than 0, or offset + count is beyond  this stream's length.</exception>
        </member>
        <member name="M:ServiceStack.Text.RecyclableMemoryStream.ReleaseLargeBuffer">
            <summary>
            Release the large buffer (either stores it for eventual release or returns it immediately).
            </summary>
        </member>
        <member name="T:ServiceStack.Text.RuntimeSerializableAttribute">
            <summary>
            Allow Type to be deserialized into late-bound object Types using __type info
            </summary>
        </member>
        <member name="T:ServiceStack.Text.IRuntimeSerializable">
            <summary>
            Allow Type to be deserialized into late-bound object Types using __type info
            </summary>
        </member>
        <member name="T:ServiceStack.Text.StringBuilderCache">
            <summary>
            Reusable StringBuilder ThreadStatic Cache
            </summary>
        </member>
        <member name="T:ServiceStack.Text.StringBuilderCacheAlt">
            <summary>
            Alternative Reusable StringBuilder ThreadStatic Cache
            </summary>
        </member>
        <member name="T:ServiceStack.Text.StringSpanExtensions">
            <summary>
            Helpful extensions on ReadOnlySpan&lt;char&gt;
            Previous extensions on StringSegment available from: https://gist.github.com/mythz/9825689f0db7464d1d541cb62954614c
            </summary>
        </member>
        <member name="M:ServiceStack.Text.StringSpanExtensions.Value(System.ReadOnlySpan{System.Char})">
            <summary>
            Returns null if Length == 0, string.Empty if value[0] == NonWidthWhitespace, otherwise returns value.ToString()
            </summary>
        </member>
        <member name="T:ServiceStack.Text.StringWriterCache">
            <summary>
            Reusable StringWriter ThreadStatic Cache
            </summary>
        </member>
        <member name="T:ServiceStack.Text.StringWriterCacheAlt">
            <summary>
            Alternative Reusable StringWriter ThreadStatic Cache
            </summary>
        </member>
        <member name="T:ServiceStack.Text.Support.DoubleConverter">
            <summary>
            A class to allow the conversion of doubles to string representations of
            their exact decimal values. The implementation aims for readability over
            efficiency.
            
            Courtesy of @JonSkeet
            http://www.yoda.arachsys.com/csharp/DoubleConverter.cs
            </summary>
        </member>
        <!-- Badly formed XML comment ignored for member "M:ServiceStack.Text.Support.DoubleConverter.ToExactString(System.Double)" -->
        <!-- Badly formed XML comment ignored for member "T:ServiceStack.Text.Support.DoubleConverter.ArbitraryDecimal" -->
        <!-- Badly formed XML comment ignored for member "F:ServiceStack.Text.Support.DoubleConverter.ArbitraryDecimal.digits" -->
        <member name="F:ServiceStack.Text.Support.DoubleConverter.ArbitraryDecimal.decimalPoint">
            <summary> 
            How many digits are *after* the decimal point
            </summary>
        </member>
        <member name="M:ServiceStack.Text.Support.DoubleConverter.ArbitraryDecimal.#ctor(System.Int64)">
            <summary> 
            Constructs an arbitrary decimal expansion from the given long.
            The long must not be negative.
            </summary>
        </member>
        <member name="M:ServiceStack.Text.Support.DoubleConverter.ArbitraryDecimal.MultiplyBy(System.Int32)">
            <summary>
            Multiplies the current expansion by the given amount, which should
            only be 2 or 5.
            </summary>
        </member>
        <member name="M:ServiceStack.Text.Support.DoubleConverter.ArbitraryDecimal.Shift(System.Int32)">
            <summary>
            Shifts the decimal point; a negative value makes
            the decimal expansion bigger (as fewer digits come after the
            decimal place) and a positive value makes the decimal
            expansion smaller.
            </summary>
        </member>
        <member name="M:ServiceStack.Text.Support.DoubleConverter.ArbitraryDecimal.Normalize">
            <summary>
            Removes leading/trailing zeroes from the expansion.
            </summary>
        </member>
        <member name="M:ServiceStack.Text.Support.DoubleConverter.ArbitraryDecimal.ToString">
            <summary>
            Converts the value to a proper decimal string representation.
            </summary>
        </member>
        <member name="T:ServiceStack.Text.TypeSerializer">
            <summary>
            Creates an instance of a Type from a string value
            </summary>
        </member>
        <member name="M:ServiceStack.Text.TypeSerializer.CanCreateFromString(System.Type)">
            <summary>
            Determines whether the specified type is convertible from string.
            </summary>
            <param name="type">The type.</param>
            <returns>
            	<c>true</c> if the specified type is convertible from string; otherwise, <c>false</c>.
            </returns>
        </member>
        <member name="M:ServiceStack.Text.TypeSerializer.DeserializeFromString``1(System.String)">
            <summary>
            Parses the specified value.
            </summary>
            <param name="value">The value.</param>
            <returns></returns>
        </member>
        <member name="M:ServiceStack.Text.TypeSerializer.DeserializeFromString(System.String,System.Type)">
            <summary>
            Parses the specified type.
            </summary>
            <param name="type">The type.</param>
            <param name="value">The value.</param>
            <returns></returns>
        </member>
        <member name="M:ServiceStack.Text.TypeSerializer.ToStringDictionary(System.Object)">
            <summary>
            Useful extension method to get the Dictionary[string,string] representation of any POCO type.
            </summary>
            <returns></returns>
        </member>
        <member name="M:ServiceStack.Text.TypeSerializer.Dump``1(``0)">
            <summary>
            Recursively prints the contents of any POCO object in a human-friendly, readable format
            </summary>
            <returns></returns>
        </member>
        <member name="M:ServiceStack.Text.TypeSerializer.PrintDump``1(``0)">
            <summary>
            Print Dump to Console.WriteLine
            </summary>
        </member>
        <member name="M:ServiceStack.Text.TypeSerializer.Print(System.String,System.Object[])">
            <summary>
            Print string.Format to Console.WriteLine
            </summary>
        </member>
        <member name="M:ServiceStack.Text.TypeSerializer`1.DeserializeFromString(System.String)">
            <summary>
            Parses the specified value.
            </summary>
            <param name="value">The value.</param>
            <returns></returns>
        </member>
        <member name="T:ServiceStack.AutoMapping">
            <summary>
            Customize ServiceStack AutoMapping Behavior
            </summary>
        </member>
        <member name="M:ServiceStack.AutoMapping.RegisterConverter``2(System.Func{``0,``1})">
            <summary>
            Register Type to Type AutoMapping converter 
            </summary>
        </member>
        <member name="M:ServiceStack.AutoMapping.IgnoreMapping``2">
            <summary>
            Ignore Type to Type Mapping (including collections containing them) 
            </summary>
        </member>
        <member name="M:ServiceStack.AutoMapping.IgnoreMapping(System.Type,System.Type)">
            <summary>
            Ignore Type to Type Mapping (including collections containing them) 
            </summary>
        </member>
        <member name="M:ServiceStack.AutoMappingUtils.PopulateWith(System.Object)">
            <summary>
            Populate an object with Example data.
            </summary>
            <param name="obj"></param>
            <returns></returns>
        </member>
        <member name="M:ServiceStack.AutoMappingUtils.PopulateObjectInternal(System.Object,System.Collections.Generic.Dictionary{System.Type,System.Int32})">
            <summary>
            Populates the object with example data.
            </summary>
            <param name="obj"></param>
            <param name="recursionInfo">Tracks how deeply nested we are</param>
            <returns></returns>
        </member>
        <member name="T:ServiceStack.Defer">
            <summary>
            Useful class for C# 8 using declaration to defer action til outside of scope, e.g:
            using var defer = new Defer(() => response.Close());
            </summary>
        </member>
        <member name="T:ServiceStack.Extensions.ServiceStackExtensions">
            <summary>
            Move conflicting extension methods into 'ServiceStack.Extensions' namespace
            </summary>
        </member>
        <member name="M:ServiceStack.HttpUtils.SendStreamToUrl(System.String,System.String,System.IO.Stream,System.String,System.String,System.Action{System.Net.HttpWebRequest},System.Action{System.Net.HttpWebResponse})">
            <summary>
            Returns HttpWebResponse Stream which must be disposed
            </summary>
        </member>
        <member name="M:ServiceStack.HttpUtils.SendStreamToUrlAsync(System.String,System.String,System.IO.Stream,System.String,System.String,System.Action{System.Net.HttpWebRequest},System.Action{System.Net.HttpWebResponse},System.Threading.CancellationToken)">
            <summary>
            Returns HttpWebResponse Stream which must be disposed
            </summary>
        </member>
        <member name="T:ServiceStack.Licensing">
            <summary>
            Public Code API to register commercial license for ServiceStack.
            </summary>
        </member>
        <member name="T:ServiceStack.LicenseUtils">
            <summary>
            Internal Utilities to verify licensing
            </summary>
        </member>
        <member name="T:ServiceStack.ObjectDictionary">
            <summary>
            UX friendly alternative alias of Dictionary&lt;string, object&gt;
            </summary>
        </member>
        <member name="T:ServiceStack.StringDictionary">
            <summary>
            UX friendly alternative alias of Dictionary&lt;string, string&gt;
            </summary>
        </member>
        <member name="T:ServiceStack.KeyValuePairs">
            <summary>
            UX friendly alternative alias of List&lt;KeyValuePair&lt;string, object&gt;gt;
            </summary>
        </member>
        <member name="T:ServiceStack.KeyValueStrings">
            <summary>
            UX friendly alternative alias of List&lt;KeyValuePair&lt;string, string&gt;gt;
            </summary>
        </member>
        <member name="M:ServiceStack.PathUtils.MapProjectPath(System.String)">
            <summary>
            Maps the path of a file in the context of a VS project in a Console App
            </summary>
            <param name="relativePath">the relative path</param>
            <returns>the absolute path</returns>
            <remarks>Assumes static content is two directories above the /bin/ directory,
            eg. in a unit test scenario  the assembly would be in /bin/Debug/.</remarks>
        </member>
        <member name="M:ServiceStack.PathUtils.MapProjectPlatformPath(System.String)">
            <summary>
            Maps the path of a file in the context of a VS 2017+ multi-platform project in a Console App
            </summary>
            <param name="relativePath">the relative path</param>
            <returns>the absolute path</returns>
            <remarks>Assumes static content is two directories above the /bin/ directory,
            eg. in a unit test scenario  the assembly would be in /bin/Debug/net45</remarks>
        </member>
        <member name="M:ServiceStack.PathUtils.MapAbsolutePath(System.String)">
            <summary>
            Maps the path of a file in the bin\ folder of a self-hosted scenario
            </summary>
            <param name="relativePath">the relative path</param>
            <returns>the absolute path</returns>
            <remarks>Assumes static content is copied to /bin/ folder with the assemblies</remarks>
        </member>
        <member name="M:ServiceStack.PathUtils.MapHostAbsolutePath(System.String)">
            <summary>
            Maps the path of a file in an ASP.NET hosted scenario
            </summary>
            <param name="relativePath">the relative path</param>
            <returns>the absolute path</returns>
            <remarks>Assumes static content is in the parent folder of the /bin/ directory</remarks>
        </member>
        <member name="M:ServiceStack.PlatformExtensions.AddAttributes(System.Reflection.PropertyInfo,System.Attribute[])">
            <summary>
            Add a Property attribute at runtime. 
            <para>Not threadsafe, should only add attributes on Startup.</para>
            </summary>
        </member>
        <member name="M:ServiceStack.PlatformExtensions.ReplaceAttribute(System.Reflection.PropertyInfo,System.Attribute)">
            <summary>
            Add a Property attribute at runtime. 
            <para>Not threadsafe, should only add attributes on Startup.</para>
            </summary>
        </member>
        <member name="T:ServiceStack.QueryStringWriter`1">
            <summary>
            Implement the serializer using a more static approach
            </summary>
            <typeparam name="T"></typeparam>
        </member>
        <member name="M:ServiceStack.ReflectionExtensions.New``1(System.Type)">
            <summary>
            Creates a new instance of type. 
            First looks at JsConfig.ModelFactory before falling back to CreateInstance
            </summary>
        </member>
        <member name="M:ServiceStack.ReflectionExtensions.New(System.Type)">
            <summary>
            Creates a new instance of type. 
            First looks at JsConfig.ModelFactory before falling back to CreateInstance
            </summary>
        </member>
        <member name="M:ServiceStack.ReflectionExtensions.CreateInstance(System.Type)">
            <summary>
            Creates a new instance from the default constructor of type
            </summary>
        </member>
        <member name="F:ServiceStack.StreamExtensions.DefaultBufferSize">
            <summary>
            @jonskeet: Collection of utility methods which operate on streams.
            r285, February 26th 2009: http://www.yoda.arachsys.com/csharp/miscutil/
            </summary>
        </member>
        <member name="M:ServiceStack.StreamExtensions.ReadFully(System.IO.Stream)">
            <summary>
            Reads the given stream up to the end, returning the data as a byte array.
            </summary>
        </member>
        <member name="M:ServiceStack.StreamExtensions.ReadFully(System.IO.Stream,System.Int32)">
            <summary>
            Reads the given stream up to the end, returning the data as a byte
            array, using the given buffer size.
            </summary>
        </member>
        <member name="M:ServiceStack.StreamExtensions.ReadFully(System.IO.Stream,System.Byte[])">
            <summary>
            Reads the given stream up to the end, returning the data as a byte
            array, using the given buffer for transferring data. Note that the
            current contents of the buffer is ignored, so the buffer needn't
            be cleared beforehand.
            </summary>
        </member>
        <member name="M:ServiceStack.StreamExtensions.ReadFullyAsync(System.IO.Stream,System.Threading.CancellationToken)">
            <summary>
            Reads the given stream up to the end, returning the data as a byte array.
            </summary>
        </member>
        <member name="M:ServiceStack.StreamExtensions.ReadFullyAsync(System.IO.Stream,System.Int32,System.Threading.CancellationToken)">
            <summary>
            Reads the given stream up to the end, returning the data as a byte
            array, using the given buffer size.
            </summary>
        </member>
        <member name="M:ServiceStack.StreamExtensions.ReadFullyAsync(System.IO.Stream,System.Byte[],System.Threading.CancellationToken)">
            <summary>
            Reads the given stream up to the end, returning the data as a byte
            array, using the given buffer for transferring data. Note that the
            current contents of the buffer is ignored, so the buffer needn't
            be cleared beforehand.
            </summary>
        </member>
        <member name="M:ServiceStack.StreamExtensions.ReadFullyAsMemory(System.IO.Stream)">
            <summary>
            Reads the given stream up to the end, returning the MemoryStream Buffer as ReadOnlyMemory&lt;byte&gt;.
            </summary>
        </member>
        <member name="M:ServiceStack.StreamExtensions.ReadFullyAsMemory(System.IO.Stream,System.Int32)">
            <summary>
            Reads the given stream up to the end, returning the MemoryStream Buffer as ReadOnlyMemory&lt;byte&gt;.
            </summary>
        </member>
        <member name="M:ServiceStack.StreamExtensions.ReadFullyAsMemoryAsync(System.IO.Stream,System.Threading.CancellationToken)">
            <summary>
            Reads the given stream up to the end, returning the MemoryStream Buffer as ReadOnlyMemory&lt;byte&gt;.
            </summary>
        </member>
        <member name="M:ServiceStack.StreamExtensions.ReadFullyAsMemoryAsync(System.IO.Stream,System.Int32,System.Threading.CancellationToken)">
            <summary>
            Reads the given stream up to the end, returning the MemoryStream Buffer as ReadOnlyMemory&lt;byte&gt;.
            </summary>
        </member>
        <member name="M:ServiceStack.StreamExtensions.CopyTo(System.IO.Stream,System.IO.Stream)">
            <summary>
            Copies all the data from one stream into another.
            </summary>
        </member>
        <member name="M:ServiceStack.StreamExtensions.CopyTo(System.IO.Stream,System.IO.Stream,System.Int32)">
            <summary>
            Copies all the data from one stream into another, using a buffer
            of the given size.
            </summary>
        </member>
        <member name="M:ServiceStack.StreamExtensions.CopyTo(System.IO.Stream,System.IO.Stream,System.Byte[])">
            <summary>
            Copies all the data from one stream into another, using the given
            buffer for transferring data. Note that the current contents of
            the buffer is ignored, so the buffer needn't be cleared beforehand.
            </summary>
        </member>
        <member name="M:ServiceStack.StreamExtensions.CopyToAsync(System.IO.Stream,System.IO.Stream,System.Byte[],System.Threading.CancellationToken)">
            <summary>
            Copies all the data from one stream into another, using the given
            buffer for transferring data. Note that the current contents of
            the buffer is ignored, so the buffer needn't be cleared beforehand.
            </summary>
        </member>
        <member name="M:ServiceStack.StreamExtensions.ReadExactly(System.IO.Stream,System.Int32)">
            <summary>
            Reads exactly the given number of bytes from the specified stream.
            If the end of the stream is reached before the specified amount
            of data is read, an exception is thrown.
            </summary>
        </member>
        <member name="M:ServiceStack.StreamExtensions.ReadExactly(System.IO.Stream,System.Byte[])">
            <summary>
            Reads into a buffer, filling it completely.
            </summary>
        </member>
        <member name="M:ServiceStack.StreamExtensions.ReadExactly(System.IO.Stream,System.Byte[],System.Int32)">
            <summary>
            Reads exactly the given number of bytes from the specified stream,
            into the given buffer, starting at position 0 of the array.
            </summary>
        </member>
        <member name="M:ServiceStack.StreamExtensions.ReadExactly(System.IO.Stream,System.Byte[],System.Int32,System.Int32)">
            <summary>
            Reads exactly the given number of bytes from the specified stream,
            into the given buffer, starting at position 0 of the array.
            </summary>
        </member>
        <member name="M:ServiceStack.StreamExtensions.ReadExactlyFast(System.IO.Stream,System.Byte[],System.Int32,System.Int32)">
            <summary>
            Same as ReadExactly, but without the argument checks.
            </summary>
        </member>
        <member name="M:ServiceStack.StreamExtensions.InMemoryStream(System.Byte[])">
            <summary>
            Returns bytes in publiclyVisible MemoryStream
            </summary>
        </member>
        <member name="M:ServiceStack.StringExtensions.BaseConvert(System.String,System.Int32,System.Int32)">
            <summary>
            Converts from base: 0 - 62
            </summary>
            <param name="source">The source.</param>
            <param name="from">From.</param>
            <param name="to">To.</param>
            <returns></returns>
        </member>
        <member name="M:ServiceStack.StringExtensions.FastToUtf8Bytes(System.String)">
            <summary>
            Skip the encoding process for 'safe strings' 
            </summary>
            <param name="strVal"></param>
            <returns></returns>
        </member>
    </members>
</doc>
