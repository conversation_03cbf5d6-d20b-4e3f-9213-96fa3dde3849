﻿using System;
using System.Collections.Generic;
using System.Drawing;

namespace CommonLib
{
    [Serializable]
    public class TextContentInfo
    {
        public List<TextCellInfo> data { get; set; }
    }

    [Serializable]
    public class TextCellInfo
    {
        public string words { get; set; }

        public string trans { get; set; }

        public LocationInfo location { get; set; }

        [Newtonsoft.Json.JsonIgnore]
        [System.Xml.Serialization.XmlIgnore]
        [System.Web.Script.Serialization.ScriptIgnore]
        public bool IsProcessed { get; set; }

        public int PageIndex { get; set; }

        public override string ToString()
        {
            return string.Format("{0},Location:{1}", words, location.ToString());
        }
    }

    [Serializable]
    public class LineInfo
    {
        public bool IsFromLeftToRight { get; set; }

        public bool IsFromTopToDown { get; set; }

        public string words { get; set; }

        public string trans { get; set; }

        public Rectangle rectangle { get; set; }

        public int AverageWidth { get; set; }

        public List<TextCellInfo> lstCell { get; set; }

        public bool isBreak { get; set; }

        public string lang { get; set; }

        public string separator { get; set; }

        public List<string> breakReason { get; set; } = new List<string>();

        public void Init()
        {
            words = words?.TrimEnd('\n') ?? string.Empty;
            trans = trans?.TrimEnd('\n') ?? string.Empty;
            rectangle = OcrLineProcess.GetMergeRectangle(this);
        }
    }

    [Serializable]
    public class LocationInfo
    {
        public double left { get; set; }
        public double top { get; set; }
        public double width { get; set; }
        public double height { get; set; }

        //[System.Xml.Serialization.XmlIgnore]
        //[System.Web.Script.Serialization.ScriptIgnore]
        [Newtonsoft.Json.JsonIgnore]
        public string words { get; set; }

        [Newtonsoft.Json.JsonIgnore]
        public string trans { get; set; }

        public override string ToString()
        {
            return string.Format("top:{0},left:{1},width:{2},height:{3}", top, left, width, height);
        }

        public Rectangle ToRectangle()
        {
            Rectangle rectangle = Rectangle.Empty;
            if (width > 0 && height > 0)
            {
                return new Rectangle((int)left, (int)top, (int)width, (int)height);
            }
            return rectangle;
        }
    }

    [Serializable]
    public class VLocationInfo
    {
        public double left { get; set; }
        public double top { get; set; }
        public double width { get; set; }
        public double height { get; set; }

        public string words { get; set; }

        public string trans { get; set; }

        public override string ToString()
        {
            return string.Format("words:{4},top:{0},left:{1},width:{2},height:{3}", top, left, width, height, words);
        }
    }

    [Serializable]
    public class VLocationInfo2
    {
        public double x { get; set; }
        public double y { get; set; }

        public override string ToString()
        {
            return string.Format("x:{0},y:{1}", x, y);
        }
    }

    [Serializable]
    public class VLocationInfo3
    {
        public double left { get; set; }
        public double top { get; set; }
        public double width
        {
            get { return right - left; }
        }

        public double height
        {
            get { return bottom - top; }
        }

        public double right { get; set; }

        public double bottom { get; set; }
    }
}
