﻿namespace CommonLib
{
    public static class RdsCacheHelper
    {
        public static IpLimitManager LimitHelper { get; set; }
        public static UserLoginCache LstAccountCache { get; set; }

        public static void InitLimiter()
        {
            LstAccountCache = new UserLoginCache();
            LimitHelper = new IpLimitManager(ConfigHelper.NMaxExecPerSecond, ConfigHelper.NBlackMSencond, ConfigHelper.NMaxExecBlackSecond, ConfigHelper.IsValidateCodeAmount, false);
        }

        private static UserCodeRecordCache codeRecordCache;

        public static UserCodeRecordCache CodeRecordCache
        {
            get
            {
                if (codeRecordCache == null)
                {
                    codeRecordCache = new UserCodeRecordCache();
                }
                return codeRecordCache;
            }
            set { codeRecordCache = value; }
        }

        private static NoticeQueue noticeQueue;

        /// <summary>
        /// 消息池
        /// </summary>
        public static NoticeQueue NoticeQueue
        {
            get
            {
                if (noticeQueue == null)
                {
                    noticeQueue = new NoticeQueue();
                }
                return noticeQueue;
            }
            set { noticeQueue = value; }
        }

        private static ServerStateCache serverStateCache;

        /// <summary>
        /// Server状态
        /// </summary>
        public static ServerStateCache ServerStateCache
        {
            get
            {
                if (serverStateCache == null)
                {
                    serverStateCache = new ServerStateCache();
                }
                return RdsCacheHelper.serverStateCache;
            }
            set { RdsCacheHelper.serverStateCache = value; }
        }

        private static OcrProcessQueue ocrProcessQueue;

        /// <summary>
        /// 打码池
        /// </summary>
        public static OcrProcessQueue OcrProcessQueue
        {
            get
            {
                if (ocrProcessQueue == null)
                {
                    ocrProcessQueue = new OcrProcessQueue();
                }
                return ocrProcessQueue;
            }
            set { ocrProcessQueue = value; }
        }

        private static OcrResultCache ocrResult;

        /// <summary>
        /// OCR结果
        /// </summary>
        public static OcrResultCache OcrResult
        {
            get
            {
                if (ocrResult == null)
                {
                    ocrResult = new OcrResultCache();
                }
                return ocrResult;
            }
            set { ocrResult = value; }
        }

        private static FileProcessQueue fileStatusProcessQueue;

        /// <summary>
        /// 文件下载状态Queue
        /// </summary>
        public static FileProcessQueue FileStatusProcessQueue
        {
            get
            {
                if (fileStatusProcessQueue == null)
                {
                    fileStatusProcessQueue = new FileProcessQueue();
                }
                return fileStatusProcessQueue;
            }
            set { fileStatusProcessQueue = value; }
        }

        private static FileResultCache fileStatusResultQueue;

        /// <summary>
        /// 文件下载状态Queue
        /// </summary>
        public static FileResultCache FileStatusResult
        {
            get
            {
                if (fileStatusResultQueue == null)
                {
                    fileStatusResultQueue = new FileResultCache();
                }
                return fileStatusResultQueue;
            }
            set { fileStatusResultQueue = value; }
        }
    }
}