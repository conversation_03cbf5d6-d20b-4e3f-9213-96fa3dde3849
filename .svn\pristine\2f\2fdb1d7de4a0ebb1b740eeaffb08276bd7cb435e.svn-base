﻿<%@ Page Title="OCR文字识别助手功能体验中心" Language="C#" MasterPageFile="~/Site.Master" AutoEventWireup="true" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="server">
    <meta name="description" content="OCR文字识别助手在线工具，提供JSON美化，文字编码解码，取色器，文字排版统计，文本合并比对等在线工具。" />
    <meta name="keywords" content="JSON美化,文字编码解码,取色器,文字排版统计,文本合并比对,计算器,正则表达式匹配,在线时间戳转换" />
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="server">
    <iframe id="frm" style="padding-top: 50px; width: 100%" frameborder="0" border="0" scrolling="no" allowtransparency="yes" allow="clipboard-read;clipboard-write;fullscreen"></iframe>
    <script type="text/javascript">
        function getQueryParams(queryString) {
            const paramArray = {};
            const pairs = (queryString[0] === '?' ? queryString.substring(1) : queryString).split('&');
            for (let i = 0; i < pairs.length; i++) {
                const pair = pairs[i];
                const key = pair.split('=')[0];
                const value = pair.split('=')[1];
                paramArray[decodeURIComponent(key)] = decodeURIComponent(value || '');
            }
            return paramArray;
        }
        const params = getQueryParams(window.location.search);
        var type = params['type'];
        if (type == null || typeof type == 'undefined' || type.length <= 0) {
            type = "index";
        }
        var url = "/ocr/" + type + ".html";
        var strLang = params['lang'];
        if (strLang != null && typeof strLang != 'undefined' && strLang.length > 0) {
            url = url + "?lang=" + strLang;
        }
        document.getElementById('frm').src = url;
        var iFrames = document.getElementsByTagName('iframe');
        function iResize() {
            for (var i = 0, j = iFrames.length; i < j; i++) {
                var bHeight = iFrames[i].contentWindow && iFrames[i].contentWindow.document && iFrames[i].contentWindow.document.body ? iFrames[i].contentWindow.document.body.scrollHeight : 0;
                var dHeight = iFrames[i].contentWindow && iFrames[i].contentWindow.document && iFrames[i].contentWindow.document.documentElement ? iFrames[i].contentWindow.document.documentElement.scrollHeight : 0;
                var cHeight = iFrames[i].document && iFrames[i].document.documentElement ? iFrames[i].document.documentElement.scrollHeight : 0;
                var dHeight = window.innerHeight - 100;
                iFrames[i].style.height = Math.max(Math.max(Math.max(bHeight, dHeight), cHeight), dHeight) + 'px';
            }
        }
        window.setInterval("iResize()", 200);
    </script>
</asp:Content>
