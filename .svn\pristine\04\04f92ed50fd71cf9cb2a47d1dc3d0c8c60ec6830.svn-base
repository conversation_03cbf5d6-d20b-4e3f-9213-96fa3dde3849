﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using CommonLib; // 引入正则表达式命名空间

// 定义文本方向枚举，包含四种组合方向
public enum TextOrientation
{
    TopToBottomLeftToRight, // 从上到下，从左到右 (默认，适用于大多数场景)
    BottomToTopLeftToRight, // 从下到上，从左到右 (例如，某些亚洲语言古籍)
    TopToBottomRightToLeft, // 从上到下，从右到左 (例如，阿拉伯语、希伯来语)
    BottomToTopRightToLeft  // 从下到上，从右到左 (极少见，可能存在于特定排版)
}

public class TextReformatter
{
    // 常量定义区域，方便统一管理和调整，并添加详细注释说明
    private const double MIN_CELL_WIDTH = 5; // 最小单元格宽度 (单位: 像素)，建议范围: 3-10，过小可能导致误判，过大可能忽略细小文本
    private const double MIN_CELL_HEIGHT = 15; // 最小单元格高度 (单位: 像素)，建议范围: 10-20，过小可能忽略，过大可能合并文本行
    private const int HISTOGRAM_BINS = 100; // 直方图分箱数量，建议范围: 50-200，箱体越多，细节信息越多，但计算量也增加
    private const double CLUSTER_DISTANCE_THRESHOLD_FACTOR = 1.5; // 行聚类距离阈值系数，用于 GroupTextCells_Clustering_V3，建议范围: 1.2-2.0，值越大，容忍行间距越大，可能将不属于同一行的文本聚类到同一行
    private const double VERTICAL_OVERLAP_THRESHOLD_RATIO = 0.6; // 垂直重叠阈值比例，用于 IsSameLine_V3，建议范围: 0.4-0.8，值越大，行检测对垂直重叠度要求越高
    private const double HORIZONTAL_SPACING_THRESHOLD_FACTOR = 2.5; // 水平间距阈值系数，用于 IsSameLine_V3，建议范围: 2.0-3.0，值越大，容忍词间距越大，可能将不属于同一词的文本识别为同一词
    private const double VERTICAL_LINE_BREAK_THRESHOLD_FACTOR = 1.6; // 垂直换行阈值系数，用于 ShouldStartNewLine_Advanced_V3，建议范围: 1.4-2.0，值越大，垂直方向换行判断越宽松
    private const double HORIZONTAL_LINE_BREAK_THRESHOLD_FACTOR = 1.8; // 水平换行阈值系数，用于 ShouldStartNewLine_Advanced_V3，建议范围: 1.4-2.0，值越大，水平方向换行判断越宽松
    private const double PARAGRAPH_INDENT_THRESHOLD_FACTOR = 2; // 段落缩进阈值系数，用于 ShouldStartNewLine_Advanced_V3，建议范围: 3.0-4.0，值越大，段落缩进判断越敏感
    private const double SHORT_LINE_THRESHOLD_RATIO = 0.65; // 短行阈值比例，用于 ShouldStartNewLine_Advanced_V3，建议范围: 0.5-0.75，值越大，短行判断越宽松
    private const double SPACE_WIDTH_THRESHOLD_FACTOR = 0.7; // 空格宽度阈值系数，用于 DetermineSpacing_Advanced_V3，建议范围: 0.5-0.9，值越大，空格判断越宽松
    private const int SHORT_LINE_LENGTH_THRESHOLD = 8; // 短行长度阈值 (字符数)，用于 ShouldStartNewLine_Advanced_V3，建议范围: 5-10，值越大，短行判断越宽松

    // 后续可以改进的方向：
    // 1.  更智能的文本方向检测算法，例如基于文本行/文本块分析或机器学习的方法，提高复杂版面方向检测的准确性。
    // 2.  更精细的行聚类算法，例如考虑文本行的倾斜角度、字间距等因素，提升行分组的鲁棒性。
    // 3.  更完善的分段逻辑，例如结合标点符号、语义信息等，更准确地识别段落和章节。
    // 4.  更强大的标点符号和全半角字符纠正功能，例如基于语言模型或规则词典的方法，提高纠正准确率。
    // 5.  支持更多语言和字符集的处理。
    // 6.  性能优化，例如使用更高效的数据结构和算法，进一步提升处理速度。


    /// <summary>
    /// 重新格式化文本，将 TextCellInfo 列表转换为格式化的字符串
    /// </summary>
    /// <param name="textCells">TextCellInfo 列表，包含OCR识别的文本单元信息</param>
    /// <param name="orientation">文本方向 (可选)，指定文本的排版方向，如果为空则自动检测。建议在已知文档方向时传入，以提高准确性和效率</param>
    /// <returns>格式化后的文本字符串，包含换行和空格，并已进行标点符号和全半角字符纠正</returns>
    public static string ReformatText(List<TextCellInfo> textCells, TextOrientation? orientation = null)
    {
        if (textCells == null || !textCells.Any())
        {
            return string.Empty; // 空列表直接返回空字符串，避免空指针异常
        }

        // 兼容措施：设定文字最小宽高，防止后续计算出现异常，使用并行循环加速处理
        Parallel.ForEach(textCells, cell =>
        {
            if (cell.location.width < MIN_CELL_WIDTH)
            {
                cell.location.width = MIN_CELL_WIDTH; // 保证最小宽度
            }
            if (cell.location.height < MIN_CELL_HEIGHT)
            {
                cell.location.height = MIN_CELL_HEIGHT; // 保证最小高度
            }
        });

        TextOrientation detectedOrientation; // 声明检测到的文本方向变量

        if (orientation.HasValue)
        {
            detectedOrientation = orientation.Value; // 如果传入了方向参数，则使用传入的方向
        }
        else
        {
            // 自动检测排版方向 (使用直方图阴影法，快速但可能在复杂版面下不够精确)
            detectedOrientation = DetectTextOrientation_Histogram_V2(textCells); // 调用方向检测方法
            // 后续改进方向：可以尝试更复杂的方向检测算法，例如基于文本行/文本块分析或机器学习的方法
        }

        List<List<TextCellInfo>> groupedLines = GroupTextCells_Clustering_V3(textCells, detectedOrientation); // 基于聚类算法分组文本行，考虑文本方向
        // 后续改进方向：可以尝试更精细的聚类算法，例如 DBSCAN, OPTICS 等，或者考虑文本行的倾斜角度、字间距等因素

        string formattedText = FormatGroupedText_Advanced_V3(groupedLines, detectedOrientation); // 格式化分组后的文本，处理换行和空格，考虑文本方向
        // 后续改进方向：可以根据不同文本方向，调整格式化策略，例如 RTL 文本的空格处理

        return formattedText;
    }

    /// <summary>
    /// 使用直方图阴影法检测文本排版方向 (快速但可能不够精确，复杂版面下可能误判)
    /// </summary>
    /// <param name="textCells">TextCellInfo 列表</param>
    /// <returns>检测到的文本方向 TextOrientation 枚举值</returns>
    private static TextOrientation DetectTextOrientation_Histogram_V2(List<TextCellInfo> textCells)
    {
        if (textCells.Count < 2) return TextOrientation.TopToBottomLeftToRight; // 文本单元过少，无法有效检测方向，默认返回从上到下，从左到右

        double[] horizontalHistogram = new double[HISTOGRAM_BINS]; // 水平方向直方图，用于统计水平方向的文本投影分布
        double[] verticalHistogram = new double[HISTOGRAM_BINS]; // 垂直方向直方图，用于统计垂直方向的文本投影分布

        // 计算所有文本单元的最小和最大左边界和上边界坐标，用于确定直方图的坐标范围
        double minLeft = textCells.Min(c => c.location.left);
        double maxLeft = textCells.Max(c => c.location.left + c.location.width);
        double minTop = textCells.Min(c => c.location.top);
        double maxTop = textCells.Max(c => c.location.top + c.location.height);

        double horizontalBinWidth = (maxLeft - minLeft) / HISTOGRAM_BINS; // 水平方向每个箱体的宽度
        double verticalBinHeight = (maxTop - minTop) / HISTOGRAM_BINS; // 垂直方向每个箱体的高度

        foreach (var cell in textCells)
        {
            // 计算文本单元在水平直方图中的起始和结束箱体索引，并将文本单元的面积累加到对应的箱体中
            int startHorizontalBin = Math.Max(0, (int)((cell.location.left - minLeft) / horizontalBinWidth));
            int endHorizontalBin = Math.Min(HISTOGRAM_BINS, (int)((cell.location.left + cell.location.width - minLeft) / horizontalBinWidth));
            for (int i = startHorizontalBin; i < endHorizontalBin; i++)
            {
                horizontalHistogram[i] += cell.location.height * cell.location.width; // 累加文本单元面积到水平直方图，面积越大，表示该水平位置文本越多
            }

            // 计算文本单元在垂直直方图中的起始和结束箱体索引，并将文本单元的面积累加到对应的箱体中
            int startVerticalBin = Math.Max(0, (int)((cell.location.top - minTop) / verticalBinHeight));
            int endVerticalBin = Math.Min(HISTOGRAM_BINS, (int)((cell.location.top + cell.location.height - minTop) / verticalBinHeight));
            for (int i = startVerticalBin; i < endVerticalBin; i++)
            {
                verticalHistogram[i] += cell.location.width * cell.location.height; // 累加文本单元面积到垂直直方图，面积越大，表示该垂直位置文本越多
            }
        }

        double horizontalVariance = CalculateVariance(horizontalHistogram); // 计算水平直方图的方差，方差越大，表示文本在水平方向分布越分散
        double verticalVariance = CalculateVariance(verticalHistogram); // 计算垂直直方图的方差，方差越大，表示文本在垂直方向分布越分散


        // 基于方差比较和启发式规则判断文本方向 (启发式规则可能不适用于所有情况，后续可以改进)
        if (horizontalVariance > verticalVariance)
        {
            // 水平方向方差更大，倾向于水平排版 (例如，从左到右或从右到左)
            if (textCells.Average(c => c.location.left + c.location.width / 2) > (minLeft + maxLeft) / 2) // 启发式规则：如果文本单元平均 X 中心更偏左，可能为从右到左 (RTL)
            {
                return TextOrientation.TopToBottomRightToLeft; // 从上到下，从右到左 (TR-BL)，例如阿拉伯语、希伯来语
            }
            else
            {
                return TextOrientation.TopToBottomLeftToRight; // 从上到下，从左到右 (TL-BR)，例如英语、中文，水平排版的常见方向
            }
        }
        else if (verticalVariance > horizontalVariance)
        {
            // 垂直方向方差更大，倾向于垂直排版 (例如，从上到下或从下到上)
            if (textCells.Average(c => c.location.top + c.location.height / 2) < (minTop + maxTop) / 2) // 启发式规则：如果文本单元平均 Y 中心更偏上，可能为从下到上 (BT-T)
            {
                return TextOrientation.BottomToTopLeftToRight; // 从下到上，从左到右 (BL-TR)，例如一些亚洲语言古籍的垂直排版
            }
            else
            {
                return TextOrientation.TopToBottomLeftToRight; // 从上到下，从左到右 (TL-BR)，例如中文、日文的垂直排版，从上到下是更常见的方向
            }

        }
        else
        {
            // 水平和垂直方向方差相近，无法有效判断方向，默认返回从上到下，从左到右 (最常见的文本方向)
            return TextOrientation.TopToBottomLeftToRight;
        }
        // 后续改进方向：可以结合更多特征进行方向判断，例如文本行的平均长度、文本单元的宽高比等
    }

    /// <summary>
    /// 计算数组方差 (用于评估直方图的离散程度)
    /// </summary>
    /// <param name="values">数值数组，例如直方图数据</param>
    /// <returns>方差值，数值越大，数据越分散</returns>
    private static double CalculateVariance(double[] values)
    {
        if (values.Length <= 1) return 0; // 数组元素太少，方差无意义，返回0
        double avg = values.Average(); // 计算数组平均值
        return values.Sum(v => Math.Pow(v - avg, 2)) / (values.Length - 1); // 计算样本方差
    }


    /// <summary>
    /// 使用聚类算法将文本单元分组到行 (基于垂直位置和水平位置进行聚类)
    /// </summary>
    /// <param name="textCells">TextCellInfo 列表，包含OCR识别的文本单元信息</param>
    /// <param name="orientation">文本方向，用于指导排序和行间距判断</param>
    /// <returns>分组后的文本行列表 (List<List<TextCellInfo>>)，每个子列表代表一行文本单元</returns>



    /// <summary>
    /// 使用聚类算法将文本单元分组到行 (基于邻近度、重叠度和角度一致性进行聚类，更鲁棒，弱化方向依赖)
    /// </summary>
    /// <param name="textCells">TextCellInfo 列表，包含OCR识别的文本单元信息</param>
    /// <param name="orientation">文本方向，作为辅助参考，降低绝对依赖 (当前版本弱化了方向参数的绝对影响)</param>
    /// <returns>分组后的文本行列表 (List<List<TextCellInfo>>)，每个子列表代表一行文本单元</returns>
    private static List<List<TextCellInfo>> GroupTextCells_Clustering_V3(List<TextCellInfo> textCells, TextOrientation orientation)
    {
        List<List<TextCellInfo>> groupedLines = new List<List<TextCellInfo>>(); // 初始化分组行列表
        if (!textCells.Any()) return groupedLines; // 空文本单元列表直接返回空列表

        double avgHeight = CalculateAverageHeight(textCells); // 计算文本单元平均高度，用于动态设置阈值
        double avgWidth = CalculateAverageWidth(textCells); // 计算文本单元平均宽度，用于动态设置阈值
        double clusterDistanceThreshold = avgHeight * CLUSTER_DISTANCE_THRESHOLD_FACTOR; // 基于平均高度计算行聚类距离阈值 (调整系数以适应倾斜文本)


        List<TextCellInfo> sortedCells; // 声明排序后的文本单元列表
        // 排序主要为了方便后续的遍历和分组，排序规则对最终分组结果的影响降低
        switch (orientation)
        {
            case TextOrientation.BottomToTopLeftToRight:
            case TextOrientation.BottomToTopRightToLeft:
                sortedCells = textCells.OrderByDescending(cell => cell.location.top).ThenBy(cell => (orientation == TextOrientation.BottomToTopLeftToRight) ? cell.location.left : -cell.location.left).ToList();
                break;
            default:
                sortedCells = textCells.OrderBy(cell => cell.location.top).ThenBy(cell => (orientation == TextOrientation.TopToBottomLeftToRight) ? cell.location.left : -cell.location.left).ToList();
                break; // 仍然根据文本方向进行初步排序，但后续聚类不再强依赖排序结果
        }
        // 后续改进方向：可以尝试基于图的聚类算法，完全摆脱排序的依赖


        List<TextCellInfo> currentLine = new List<TextCellInfo>(); // 当前行列表
        if (sortedCells.Any())
        {
            currentLine.Add(sortedCells[0]); // 将排序后的第一个单元格添加到当前行
            groupedLines.Add(currentLine); // 将第一行添加到分组行列表
        }


        // 遍历排序后的文本单元，进行行聚类 (基于更鲁棒的 IsSameLine_V4 判断)
        for (int i = 1; i < sortedCells.Count; i++)
        {
            TextCellInfo currentCell = sortedCells[i];
            TextCellInfo lastCellInLine = currentLine.LastOrDefault();

            // 使用更鲁棒的 IsSameLine_V4 方法判断是否在同一行，该方法弱化了对水平/垂直方向的绝对依赖
            if (lastCellInLine != null && IsSameLine_V3(lastCellInLine, currentCell, clusterDistanceThreshold, orientation, avgHeight, avgWidth))
            {
                currentLine.Add(currentCell); // 如果在同一行，添加到当前行
            }
            else
            {
                if (groupedLines.Last().Count > 1)
                {
                    switch (orientation)
                    {
                        case TextOrientation.BottomToTopLeftToRight:
                        case TextOrientation.TopToBottomLeftToRight:
                            groupedLines[groupedLines.Count - 1] = groupedLines.Last().OrderBy(cell => cell.location.left).ToList();
                            break;
                        default:
                            groupedLines[groupedLines.Count - 1] = groupedLines.Last().OrderBy(cell => cell.location.top).ToList();
                            break; // 仍然根据文本方向进行初步排序，但后续聚类不再强依赖排序结果
                    }
                }
                currentLine = new List<TextCellInfo>
                {
                    currentCell // 将当前单元格添加到新行
                };
                groupedLines.Add(currentLine); // 将新行添加到分组行列表
            }
        }
        // 后续改进方向：可以尝试更高级的聚类算法，例如 DBSCAN, OPTICS 或图聚类算法

        return groupedLines; // 返回分组后的文本行列表
    }

    /// <summary>
    /// 判断两个文本单元是否在同一行 (基于垂直距离、垂直重叠度、和水平位置关系判断)
    /// </summary>
    /// <param name="cell1">文本单元1</param>
    /// <param name="cell2">文本单元2</param>
    /// <param name="clusterDistanceThreshold">行聚类距离阈值，最大容忍的垂直距离</param>
    /// <param name="orientation">文本方向，用于确定水平位置关系 (从左到右或从右到左)</param>
    /// <param name="avgHeight">平均高度，用于动态计算垂直重叠阈值</param>
    /// <param name="avgWidth">平均宽度，用于动态计算水平间距阈值</param>
    /// <returns>是否在同一行 (true: 是, false: 否)</returns>
    private static bool IsSameLine_V3(TextCellInfo cell1, TextCellInfo cell2, double clusterDistanceThreshold, TextOrientation orientation, double avgHeight, double avgWidth)
    {
        double verticalOverlapThreshold = Math.Min(cell1.location.height, cell2.location.height) * VERTICAL_OVERLAP_THRESHOLD_RATIO; // 垂直重叠阈值，动态阈值，取决于两个单元格中较小的高度
        double horizontalSpacingThreshold = avgWidth * HORIZONTAL_SPACING_THRESHOLD_FACTOR; // 水平间距阈值，动态阈值，取决于平均宽度


        if (Math.Abs(cell2.location.top - cell1.location.top) > clusterDistanceThreshold) return false; // 垂直距离超过阈值，直接判定为不在同一行，快速排除

        // 检查垂直方向重叠度 - 更鲁棒的行检测，避免因少量垂直偏移导致误判
        double overlapY = Math.Max(0, Math.Min(cell1.location.top + cell1.location.height, cell2.location.top + cell2.location.height) - Math.Max(cell1.location.top, cell2.location.top));
        if (overlapY < verticalOverlapThreshold) return false; // 垂直重叠度不足，判定为不在同一行，垂直方向重叠是同一行的必要条件

        // 根据文本方向判断水平位置关系，确定单元格 2 相对于单元格 1 的水平位置
        switch (orientation)
        {
            case TextOrientation.TopToBottomLeftToRight:
            case TextOrientation.BottomToTopLeftToRight:
                return (cell2.location.left > cell1.location.left && cell2.location.left < cell1.location.left + cell1.location.width + horizontalSpacingThreshold)
                    || (cell1.location.left > cell2.location.left && cell1.location.left < cell2.location.left + cell2.location.width + horizontalSpacingThreshold);
            // 从左到右：cell2 的左边界必须在 cell1 的右边界之后，且在水平间距阈值范围内，才判定为同一行

            case TextOrientation.TopToBottomRightToLeft:
            case TextOrientation.BottomToTopRightToLeft:
                return (cell2.location.left < cell1.location.left && cell1.location.left < cell2.location.left + cell2.location.width + horizontalSpacingThreshold)
                    || (cell1.location.left < cell2.location.left && cell2.location.left < cell1.location.left + cell1.location.width + horizontalSpacingThreshold);
            // 从右到左：cell2 的左边界必须在 cell1 的左边界之前，且在水平间距阈值范围内，才判定为同一行

            default: // 默认从左到右 (TopToBottomLeftToRight)
                return cell2.location.left > cell1.location.left && cell2.location.left < cell1.location.left + cell1.location.width + horizontalSpacingThreshold;
        }
        // 后续改进方向：可以考虑更复杂的空间关系模型，例如 Delaunay 三角剖分、Voronoi 图等，更精确地描述文本单元之间的相对位置
    }


    /// <summary>
    /// 格式化分组后的文本行，添加换行和空格 (根据文本方向和动态阈值进行格式化)
    /// </summary>
    /// <param name="groupedLines">分组后的文本行列表，由 GroupTextCells_Clustering_V3 方法生成</param>
    /// <param name="orientation">文本方向，用于指导空格和换行符的添加</param>
    /// <returns>格式化后的文本字符串，已添加换行符和空格，更易于阅读</returns>
    private static string FormatGroupedText_Advanced_V3(List<List<TextCellInfo>> groupedLines, TextOrientation orientation)
    {
        StringBuilder formattedText = new StringBuilder(); // 初始化格式化文本 StringBuilder，高效字符串拼接
        double avgLineHeight = CalculateAverageLineHeight(groupedLines); // 计算平均行高，用于动态设置垂直换行阈值
        double avgCharWidth = CalculateAverageCharWidthFromAllLines(groupedLines); // 计算所有行平均字符宽度，用于动态设置空格宽度阈值和段落缩进阈值

        TextCellInfo lastCell = null; // 初始化上一个单元格，用于计算空格和判断换行

        foreach (var line in groupedLines) // 遍历分组后的每一行文本单元列表
        {
            string currentLineText = ""; // 初始化当前行文本字符串，用于存储当前行的格式化文本

            // 遍历当前行中的每个文本单元，添加文本和空格
            for (int i = 0; i < line.Count; i++)
            {
                TextCellInfo cell = line[i]; // 获取当前单元格

                if (lastCell != null)
                {
                    // 判断是否需要开始新行 (分段逻辑)，如果需要则先添加换行符
                    if (ShouldStartNewLine_Advanced_V3(lastCell, cell, i, currentLineText, groupedLines, orientation, avgLineHeight, avgCharWidth))
                    {
                        formattedText.AppendLine(currentLineText); // 如果需要新行，先将上一行文本添加到格式化文本中，并添加换行符
                        currentLineText = ""; // 重置当前行文本字符串，开始新行
                    }
                    //非换行，处理空格
                    else if (!string.IsNullOrEmpty(cell.words))
                    {
                        // 确定当前单元格与上一个单元格之间的空格，并添加到当前行文本
                        if (i == 0)
                        {
                            //如果是第一个，拿到上一行的最后一个字符与当前单元格的第一个字符比较
                            if (formattedText.Length > 0 && ShouldAddSpace(formattedText[formattedText.Length - 1], cell.words[0]))
                            {
                                currentLineText += ' ';
                            }
                        }
                        else
                        {
                            //如果不是第一个，拿到上一单元格的最后一个字符与当前单元格的第一个字符比较
                            if (currentLineText.Length > 0 && ShouldAddSpace(currentLineText[currentLineText.Length - 1], cell.words[0]))
                            {
                                currentLineText += ' ';
                            }
                        }
                    }
                }
                currentLineText += cell.words; // 将当前单元格的文本内容添加到当前行文本字符串
                lastCell = cell; // 更新上一个单元格为当前单元格，为处理下一个单元格做准备
            }
            formattedText.Append(currentLineText); // 将最后一行文本添加到格式化文本中 (因为循环结束后可能还有最后一行未添加)
        }
        // 后续改进方向：可以根据不同文本方向和文档类型，调整格式化策略，例如添加段落缩进、行间距调整等

        return formattedText.ToString(); // 将 StringBuilder 转换为最终的格式化文本字符串并返回
    }

    /// <summary>
    /// 判断是否应该开始新行 (高级分段逻辑，考虑垂直间距、段落缩进和短行等因素)
    /// </summary>
    /// <param name="lastCell">上一单元格，用于比较位置关系</param>
    /// <param name="currentCell">当前单元格，用于判断是否需要换行</param>
    /// <param name="currentLineText">当前行文本，用于判断是否为空行或短行</param>
    /// <param name="groupedLines">分组后的文本行列表，用于计算平均行长和行缩进</param>
    /// <param name="orientation">文本方向，用于确定行缩进位置</param>
    /// <param name="avgLineHeight">平均行高，用于动态设置垂直换行阈值</param>
    /// <param name="avgCharWidth">平均字符宽度，用于动态设置段落缩进阈值</param>
    /// <returns>是否应该开始新行 (true: 是, false: 否)</returns>
    private static bool ShouldStartNewLine_Advanced_V3(TextCellInfo lastCell, TextCellInfo currentCell, int index, string currentLineText, List<List<TextCellInfo>> groupedLines, TextOrientation orientation, double avgLineHeight, double avgCharWidth)
    {
        double verticalLineBreakThreshold = avgLineHeight * VERTICAL_LINE_BREAK_THRESHOLD_FACTOR; // 垂直换行阈值，动态阈值，取决于平均行高
        double horuzontalLineBreakThreshold = avgLineHeight * HORIZONTAL_LINE_BREAK_THRESHOLD_FACTOR; // 垂直换行阈值，动态阈值，取决于平均行高
        double paragraphIndentThreshold = avgCharWidth * PARAGRAPH_INDENT_THRESHOLD_FACTOR; // 段落缩进阈值，动态阈值，取决于平均字符宽度
        double shortLineThresholdRatio = SHORT_LINE_THRESHOLD_RATIO; // 短行阈值比例，用于判断是否为短行

        // 垂直间距过大，判定为换行 (主要的分段依据)
        if (Math.Abs(currentCell.location.top - lastCell.location.top) > verticalLineBreakThreshold)
        {
            return true; // 垂直方向间距超过阈值，判定为需要换行，例如段落之间的空行
        }

        //if (Math.Abs(currentCell.location.left - lastCell.location.left) > horuzontalLineBreakThreshold)
        //{
        //    return true; // 垂直方向间距超过阈值，判定为需要换行，例如段落之间的空行
        //}

        if (index == 0)
        {
            switch (orientation)
            {
                case TextOrientation.TopToBottomLeftToRight:
                case TextOrientation.BottomToTopLeftToRight:
                    if (currentCell.location.left > paragraphIndentThreshold)//超出2个字符
                    {
                        return true;
                    }
                    break;
                case TextOrientation.TopToBottomRightToLeft:
                case TextOrientation.BottomToTopRightToLeft:
                    break;
            }
        }

        // 短行后接新行的判断 (例如，列表项，标题等)，针对短行后可能需要换行的情况进行处理
        if (currentLineText.Length > SHORT_LINE_LENGTH_THRESHOLD && currentLineText.Length < CalculateAverageLineLength(groupedLines) * shortLineThresholdRatio && currentCell.location.left < GetLineIndent_V3(groupedLines, orientation) - paragraphIndentThreshold / 2) // 调整了缩进判断条件
        {
            return true; // 如果当前行是“短行”，且下一行单元格的左边界明显小于当前行的起始位置，则判定为新段落或列表项的开始，需要换行
            // currentLineText.Length > SHORT_LINE_LENGTH_THRESHOLD 避免对过短的行 (例如，只有一个词的行) 进行误判
            // CalculateAverageLineLength(groupedLines) * shortLineThresholdRatio 计算短行长度的动态阈值，取决于平均行长
        }
        // 后续改进方向：可以结合标点符号、关键词、字体大小等更多特征进行分段判断，提高分段的准确性

        return false; // 默认情况下，不换行，将当前单元格添加到当前行
    }


    private static bool ShouldAddSpace(char prev, char next)
    {
        // 中文不需要额外空格
        if (IsChinese(prev) || IsChinese(next)) return false;

        // 英文数字间需要空格
        return char.IsLetterOrDigit(prev) && char.IsLetterOrDigit(next);
    }

    private static bool IsChinese(char c) => c >= 0x4E00 && c <= 0x9FFF;


    /// <summary>
    /// 计算分组行列表的平均行高 (用于动态设置垂直换行阈值)
    /// </summary>
    /// <param name="groupedLines">分组行列表</param>
    /// <returns>平均行高，如果列表为空则返回 0</returns>
    private static double CalculateAverageLineHeight(List<List<TextCellInfo>> groupedLines)
    {
        if (!groupedLines.Any()) return 0; // 空列表返回0，避免空列表计算平均值异常
        return groupedLines.Average(line => line.Average(cell => cell.location.height)); // 计算所有行中所有单元格高度的平均值
    }

    /// <summary>
    /// 计算文本单元列表的平均宽度 (用于动态设置水平间距阈值)
    /// </summary>
    /// <param name="textCells">文本单元列表</param>
    /// <returns>平均宽度，如果列表为空则返回 0</returns>
    private static double CalculateAverageWidth(List<TextCellInfo> textCells)
    {
        if (!textCells.Any()) return 0; // 空列表返回0，避免空列表计算平均值异常
        return textCells.Average(cell => cell.location.width / cell.words.Length); // 计算所有单元格宽度的平均值
    }

    /// <summary>
    /// 计算文本单元列表的平均高度
    /// </summary>
    /// <param name="textCells">文本单元列表</param>
    /// <returns>平均高度，如果列表为空则返回 0</returns>
    private static double CalculateAverageHeight(List<TextCellInfo> textCells)
    {
        if (!textCells.Any()) return 0;
        return textCells.Average(cell => cell.location.height);
    }

    /// <summary>
    /// 计算所有行中所有文本单元的平均字符宽度 (用于动态设置空格宽度阈值和段落缩进阈值)
    /// </summary>
    /// <param name="groupedLines">分组行列表</param>
    /// <returns>平均字符宽度，如果列表为空则返回 0</returns>
    private static double CalculateAverageCharWidthFromAllLines(List<List<TextCellInfo>> groupedLines)
    {
        if (!groupedLines.Any()) return 0; // 空列表返回0，避免空列表计算平均值异常
        List<TextCellInfo> allCells = groupedLines.SelectMany(line => line).ToList(); // 将分组行列表展平为包含所有单元格的单一列表
        if (!allCells.Any()) return 0; // 如果展平后列表为空，返回 0
        return allCells.Average(cell => cell.location.width / cell.words.Length); // 计算所有单元格宽度的平均值，即平均字符宽度
    }


    /// <summary>
    /// 计算分组行列表的平均行长度 (字符数量，用于动态设置短行阈值)
    /// </summary>
    /// <param name="groupedLines">分组行列表</param>
    /// <returns>平均行长度，如果列表为空则返回 0</returns>
    private static double CalculateAverageLineLength(List<List<TextCellInfo>> groupedLines)
    {
        if (!groupedLines.Any()) return 0; // 空列表返回0，避免空列表计算平均值异常
        return groupedLines.Average(line => line.Sum(cell => cell.words.Length)); // 计算每行字符数量的总和，并计算平均值，得到平均行长度
    }


    /// <summary>
    /// 获取指定文本方向下，行的缩进位置 (用于段落缩进判断)
    /// </summary>
    /// <param name="groupedLines">分组行列表</param>
    /// <param name="orientation">文本方向，决定缩进位置是左缩进还是右缩进</param>
    /// <returns>行缩进位置 (左缩进或右缩进坐标)，如果列表为空则返回 0</returns>
    private static double GetLineIndent_V3(List<List<TextCellInfo>> groupedLines, TextOrientation orientation)
    {
        if (!groupedLines.Any()) return 0; // 空列表返回0，避免空列表计算缩进异常
        switch (orientation)
        {
            default:
                return groupedLines.Min(line => line.Min(cell => cell.location.left)); // 左缩进 (Left-to-Right 方向，取每行最左侧单元格的左边界作为缩进位置)
            case TextOrientation.TopToBottomRightToLeft:
            case TextOrientation.BottomToTopRightToLeft:
                return groupedLines.Max(line => line.Max(cell => cell.location.left + cell.location.width)); // 右缩进 (Right-to-Left 方向，取每行最右侧单元格的右边界作为缩进位置)
        }
    }
}