﻿using System;
using System.Drawing;
using CommonLib;
using HanZiOcr;
using ImageOCR;

namespace BaiDuAPI
{
    public static class ZhuShouDaMaHelper
    {
        public static bool IsEnable { get; set; }

        public static string GetCodeByBytes(byte[] byts, bool isLogin, string siteFlag)
        {
            var result = "";
            try
            {
                using (var bitmap = CommonCompress.BytesToImage(byts))
                {
                    var strHanZi = "";
                    result = GetCodeByImg(bitmap, strHanZi, isLogin, siteFlag);
                    bitmap.Dispose();
                }
            }
            catch (Exception oe)
            {
                ConfigHelper._Log.Error("助手打码GetCode", oe);
            }
            finally
            {
                byts = null;
            }
            return result;
        }

        private static string GetCodeByImg(Bitmap bitmap, string strHanZi, bool isLogin, string from)
        {
            var result = "";

            try
            {
                using (bitmap)
                {
                    //StringBuilder sb = new StringBuilder();
                    //sb.AppendLine(string.Format("开始识别汉字Local，耗时：{0}ms", stop.ElapsedMilliseconds.ToString("F0")));

                    var strOld = "";
                    if (string.IsNullOrEmpty(strHanZi))
                    {
                        strHanZi = HanZiHelper.GetHanZiByImg(bitmap, ref strOld, isLogin, from);
                    }
                    //strHanZi = CustomImageHelper.RecHanZiByImg(bitmap, isLogin, from);
                    //sb.AppendLine(string.Format("汉字[{1}]识别Local完毕，耗时：{0}ms", stop.ElapsedMilliseconds.ToString("F0"), strHanZi));

                    if (!string.IsNullOrEmpty(strHanZi))
                    {
                        //sb.AppendLine(string.Format("开始识别图片Local[{1}]，耗时：{0}ms", stop.ElapsedMilliseconds.ToString("F0"), strHanZi));
                        //result = DaMaHelper.GetCodeByPath(strHanZiPath, strHanZi, isLogin, siteFlag);
                        //result = GetCodeNewByImg(bitmap, strHanZi, isLogin, from);
                        result = CodeHelper.RecCodeByImg(bitmap, strHanZi, isLogin, from);
                        //sb.AppendLine(string.Format("图片Local[{1}-{2}]识别完毕，耗时：{0}ms", stop.ElapsedMilliseconds.ToString("F0"), strHanZi, result));
                    }
                    bitmap.Dispose();
                }
            }
            catch (Exception oe)
            {
                ConfigHelper._Log.Error("助手打码GetCode", oe);
            }
            return result;
        }

        private static string GetCodeByCodeStrBase64(string strCode, string strHanZi, bool isLogin, string from)
        {
            var result = "";

            try
            {
                using (var bitmap = CodeHelper.GetCodeImg(strCode, isLogin, ref strHanZi))
                {
                    result = GetCodeByImg(bitmap, strHanZi, isLogin, from);
                    bitmap.Dispose();
                }
            }
            catch (Exception oe)
            {
                ConfigHelper._Log.Error("助手打码GetCode", oe);
            }
            return result;
        }

        public static string GetCodeByPath(string strPath, string strHanZi, bool isLogin, string from)
        {
            var result = "";
            try
            {
                using (var bitmap = CommonCompress.GetImageByPath(strPath))
                {
                    result = GetCodeByImg(bitmap, strHanZi, isLogin, from);
                    bitmap.Dispose();
                }
            }
            catch (Exception oe)
            {
                ConfigHelper._Log.Error("GetCodeByPath", oe);
            }
            return result;
        }

        #region Old

        //strHanZiPath = "";
        //if (string.IsNullOrEmpty(result))
        //{
        //    if (!string.IsNullOrEmpty(strCode))
        //    {
        //        if (!ConfigHelper.IsCanRecImg)
        //            strHanZiPath = CodeHelper.GetCodePath(strCode, isLogin, ref strHanZi);
        //        else
        //            bitTmp = CodeHelper.GetCodeImg(strCode, isLogin, ref strHanZi);
        //    }
        //}
        //else
        //{
        //    CustomImageHelper.AddImg(strCode, result, isLogin, strHanZi);
        //}
        //sb.AppendLine(string.Format("远程打码耗时：{0}ms", stop.ElapsedMilliseconds.ToString("F0")));

        //if (string.IsNullOrEmpty(result) && (bitTmp != null || !string.IsNullOrEmpty(strHanZiPath)))
        //{
        //    strCode = null;
        //    sb.AppendLine(string.Format("开始识别汉字Local，耗时：{0}ms", stop.ElapsedMilliseconds.ToString("F0")));
        //    if (!ConfigHelper.IsCanRecImg)
        //        strHanZi = CustomImageHelper.RecHanZiByPath(strHanZiPath, isLogin, siteFlag);
        //    else
        //        strHanZi = CustomImageHelper.RecHanZiByImg(bitTmp, isLogin, siteFlag);
        //    sb.AppendLine(string.Format("汉字[{1}]识别Local完毕，耗时：{0}ms", stop.ElapsedMilliseconds.ToString("F0"), strHanZi));

        //    if (!string.IsNullOrEmpty(strHanZi))
        //    {
        //        if (!ConfigHelper.IsCanRecImg)
        //        {
        //            sb.AppendLine(string.Format("开始识别图片[{1}]，耗时：{0}ms", stop.ElapsedMilliseconds.ToString("F0"), strHanZi));
        //            var newUrl = string.Format("http://{0}/code.ashx?op={1}", ConfigHelper.RecImgHost, "new_img");// context.Request.Url.ToString().Replace(context.Request.Url.Host, ConfigHelper.RecUrlHost);
        //            var strNewPost = string.Format("path={0}&tt={1}&hz={2}&flag={3}", strHanZiPath, isLogin ? "0" : "1", strHanZi, siteFlag);

        //            sb.AppendLine("Url:" + newUrl);
        //            sb.AppendLine("Post:" + strNewPost);
        //            result = WebClientExt.GetHtml(newUrl, "", "", strNewPost, 1, 3);
        //            sb.AppendLine(string.Format("图片[{1}-{2}]识别完毕，耗时：{0}ms", stop.ElapsedMilliseconds.ToString("F0"), strHanZi, result));
        //            if (result.Equals("no"))
        //            {
        //                result = "";
        //            }
        //        }
        //        else
        //        {
        //            sb.AppendLine(string.Format("开始识别图片Local[{1}]，耗时：{0}ms", stop.ElapsedMilliseconds.ToString("F0"), strHanZi));
        //            //result = DaMaHelper.GetCodeByPath(strHanZiPath, strHanZi, isLogin, siteFlag);
        //            result = DaMaHelper.GetCodeNewByImg(bitTmp, strHanZi, isLogin, siteFlag);
        //            sb.AppendLine(string.Format("图片Local[{1}-{2}]识别完毕，耗时：{0}ms", stop.ElapsedMilliseconds.ToString("F0"), strHanZi, result));
        //        }
        //    }
        //}

        #endregion
    }
}