﻿using CommonLib;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Collections.Specialized;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Security.Cryptography;
using System.Text;

namespace HanZiOcr
{
    /// <summary>
    /// https://www.volcengine.com/experience/%E6%99%BA%E8%83%BD%E8%A7%86%E8%A7%89%E4%BD%93%E9%AA%8C/%E9%80%9A%E7%94%A8%E6%96%87%E5%AD%97%E8%AF%86%E5%88%AB
    /// </summary>
    public class HuoShanRec : BaseOcrRec
    {
        public HuoShanRec()
        {
            OcrType = HanZiOcrType.火山引擎;
            MaxExecPerTime = 26;
            LstJsonPreProcessArray = new List<object>() { "Result", "data", "line_texts" };

            IsSupportVertical = true;
            IsJsonArrayStringWithLocation = true;
            LstJsonLocationProcessArray = new List<object>() { "Result", "data", "line_rects" };
            StrResultJsonSpilt = "words";
            IsDesrializeVerticalByLocation = true;
            DicDeserializeVerticalJson = new Dictionary<string, string> { { "left", "x" }, { "top", "y" } };
        }

        protected override string GetHtml(OcrContent content)
        {
            var file = new UploadFileInfo()
            {
                Name = "__upload",
                Filename = "1.jpg",
                ContentType = "image/jpg",
                Stream = new MemoryStream(Convert.FromBase64String(content.strBase64))
            };
            var strTmp = PostFile("https://www.volcengine.com/api/exp/2/model-i", new[] { file }, new NameValueCollection()
            {
                { "__upload_path","/ocr-process/v1/ocr_normal"},
                { "__upload_key","image"},
            });

            return strTmp;
        }
    }
}