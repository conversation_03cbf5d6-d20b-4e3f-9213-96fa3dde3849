﻿using System;
using System.Collections.Generic;
using System.Web;

namespace BugReprot
{
    public class CodeEntity
    {
        /*RegDate
        Type
        Window
        ExpiteDate
        LoginCount*/
        private string strAppCode = "";
        private string strMachine = "";
        private string strType = "";
        private DateTime dtReg = DateTime.MinValue;
        private DateTime dtExpire = DateTime.MinValue;
        private bool isEnable = false;
        private bool isValidate = false;
        private int nMaxWindow = 1;
        private int nMaxLogin = 5;

        public string StrAppCode
        {
            get { return strAppCode; }
            set { strAppCode = value; }
        }

        public string StrMachine
        {
            get { return strMachine; }
            set { strMachine = value; }
        }

        public string StrType
        {
            get { return strType; }
            set { strType = value; }
        }

        public DateTime DtReg
        {
            get { return dtReg; }
            set { dtReg = value; }
        }

        public DateTime DtExpire
        {
            get { return dtExpire; }
            set { dtExpire = value; }
        }

        public bool IsEnable
        {
            get
            {
                return dtExpire > dtReg;
            }
        }

        public bool IsValidate
        {
            get { return isValidate; }
            set { isValidate = value; }
        }

        public int NMaxWindow
        {
            get { return nMaxWindow; }
            set { nMaxWindow = value; }
        }

        public int NMaxLogin
        {
            get { return nMaxLogin; }
            set { nMaxLogin = value; }
        }
    }
}