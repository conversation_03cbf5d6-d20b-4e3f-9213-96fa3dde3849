﻿{"responseStatus":{"errorCode":"SqlException","message":"The INSERT statement conflicted with the CHECK constraint \"CK_SOME_CONSTRAINT\". The conflict occurred in database \"TEST_XXX\", table \"dbo.SOME_TABLE\".\r\nThe statement has been terminated.","stackTrace":"[MyDTORequest: 19/02/2014 07:29:11]:\n[REQUEST: {idIstituto:xx,codice:cod,descrizione:c,flaG_F_O:F,iD_MERCATO:0,iD_TIPOLOGIA_PRODOTTO:101,iD_DIVISA:AED,iD_TIPO_REGOLAMENTO:0,iD_MARGIN_STYLE:0,iD_TIPO_RAPPRESENTAZIONE_PREZZO:0,iD_UTENTE:185560,iD_MACROCATEGORIA_COMMERCIALE:0,divisorE_PREZZO:0}]\nSystem.Data.SqlClient.SqlException (0x80131904): The INSERT statement conflicted with the CHECK constraint \"CK_CONSTRAINT\". The conflict occurred in database \"TEST_XXX\", table \"dbo.SOME_TABLE\".\r\nThe statement has been terminated.\r\n   at MYNamespace.service.Any(MYDTORequest request) in cxxx.cs:line 163\r\n   at ServiceStack.Host.ServiceRunner`1.Execute(IRequest request, Object instance, TRequest requestDto)\r\nClientConnectionId:53921837-e741-4a96-bd7e-5763b648b1b6","errors":[]}}