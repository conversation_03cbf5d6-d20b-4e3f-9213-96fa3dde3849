﻿using CommonLib;
using log4net;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace Code.Process.Common
{
    public class CommonProcess
    {

        private static ILog _Log;

        static CommonProcess()
        {
            _Log = LogManager.GetLogger("ImageProcess");
        }

        public static void AddToProcess(CusImageEntity processEntity)
        {
            try
            {
                GetCode(processEntity);
            }
            catch (Exception oe)
            {
                _Log.Error("消息接收失败,错误原因如下:", oe);
            }
        }

        public static void AddToFileStatusProcess(CusFileStatusEntity cusImg)
        {
            if (cusImg == null || string.IsNullOrEmpty(cusImg.TaskId))
                return;
            try
            {
                var state = BaseRecHelper.GetInstance(cusImg.OcrType)?.QueryFileStatus(cusImg.TaskId);
                if (state != null)
                {
                    ProcessNew.ProcessFileResult(state);
                }
            }
            catch (ThreadAbortException)
            {
                Thread.ResetAbort();
            }
            catch (Exception oe)
            {
                _Log.Error("消息接收失败,错误原因如下:", oe);
            }
        }

        //private static CusImageEntity AddProcessQueue(CusImageEntity cusImg)
        //{
        //    //CacheHelper.ProcessQueue.AddOrUpdate(cusImg.StrIndex, tmpRes);
        //    return tmpRes;
        //}

        //static Random rndTmp = new Random();
        //static GetCodeDele ExecDaMa = GetCode;

        const string StrInvalidateOperate = "当前用户不支持此类型的操作！";

        private static void GetCode(CusImageEntity processEntity)
        {
            var startTicks = ServerTime.DateTime.Ticks;
            var limit = UserTypeHelper.GetUserInfo(processEntity.UserType);
            if (CheckIsForbidOperate(processEntity, limit))
            {
                SetEmptyResult(processEntity, StrInvalidateOperate, startTicks);
                return;
            }

            //可用的识别类型
            var processOcrType = processEntity.OcrType;
            int processPerTime = Math.Max(limit.ProcessPerTime, 1);
            //最多结果个数
            int nMaxResultCount = processPerTime;

            //查找可用的处理类型
            var listProcessEntity = GetSupportTypes(processEntity, nMaxResultCount);

            //异步处理图片加载
            if (string.IsNullOrEmpty(processEntity.StrImg) && listProcessEntity.Any(p => !p.IsSupportUrl && string.IsNullOrEmpty(p.StrImg)))
            {
                _ = Task.Run(async () =>
                {
                    try
                    {
                        processEntity.OcrTime.ClientDownloadStart = ServerTime.DateTime.Ticks;
                        var (byts, NewCookie) = await WebClientSyncExt.GetByteInternal(processEntity.ImgUrl, "", "", "", 30, null);
                        if (byts?.Length > 0)
                        {
                            processEntity.StrImg = Convert.ToBase64String(byts);
                        }
                        processEntity.OcrTime.ClientDownloadEnd = ServerTime.DateTime.Ticks;

                        foreach (var p in listProcessEntity)
                        {
                            p.OcrTime.ClientDownloadStart = processEntity.OcrTime.ClientDownloadStart;
                            p.StrImg = processEntity.StrImg;
                            p.OcrTime.ClientDownloadEnd = processEntity.OcrTime.ClientDownloadEnd;
                        }
                    }
                    catch { }
                });
            }

            //var getCode_GetSupportTypeTicks = ServerTime.DateTime.Ticks;

            if (listProcessEntity.Count > 0)
            {
                var totalResultCount = new RefCount() { TotalCount = 0, MaxCount = nMaxResultCount };
                try
                {
                    using (CancellationTokenSource cancellationToken = new CancellationTokenSource(new TimeSpan(processEntity.DtExpired - processEntity.OcrTime.ServerReceivedUserRequest)))
                    {
                        totalResultCount.AchieveMaxEvent += (sender, e) =>
                        {
                            cancellationToken.Cancel();
                        };

                        //var getCode_BeforeWhileTicks = ServerTime.DateTime.Ticks;

                        while (!cancellationToken.IsCancellationRequested && processEntity.IsValidate && listProcessEntity.Any(p => p.State == ProcessState.未处理))
                        {
                            //Take 多一个，避免出现没有支持Url的节点的场景
                            var lstProcess = listProcessEntity.Where(p => p.State == ProcessState.未处理).Take(nMaxResultCount + 1).ToList();
                            //移除等待图片下载的节点，其他先开始
                            lstProcess.RemoveAll(p => !p.IsSupportUrl && string.IsNullOrEmpty(p.StrImg));
                            if (lstProcess.Count <= 0)
                            {
                                Thread.Sleep(50);
                                continue;
                            }

                            //var getCode_GetRangeEndTicks = ServerTime.DateTime.Ticks;

                            foreach (var item in lstProcess)
                            {
                                item.State = ProcessState.正在处理;
                            }

                            //var getCode_BeforeParallelTicks = ServerTime.DateTime.Ticks;

                            //var sbProcessInfo = new StringBuilder();
                            //sbProcessInfo.AppendLine("=====GetCode调度信息=====");
                            //sbProcessInfo.AppendLine(string.Format("进入GetCode：{0}", new DateTime(startTicks).ToString("HH:mm:ss fff")));
                            //sbProcessInfo.AppendLine(string.Format("获取完SupportType：{0}，已耗时{1}ms", new DateTime(getCode_GetSupportTypeTicks).ToString("HH:mm:ss fff"), new TimeSpan(getCode_GetSupportTypeTicks - startTicks).TotalMilliseconds.ToString("F0")));
                            //sbProcessInfo.AppendLine(string.Format("BeforeWhile循环：{0}，已耗时{1}ms", new DateTime(getCode_BeforeWhileTicks).ToString("HH:mm:ss fff"), new TimeSpan(getCode_BeforeWhileTicks - getCode_GetSupportTypeTicks).TotalMilliseconds.ToString("F0")));
                            //sbProcessInfo.AppendLine(string.Format("分配lstProcess：{0}，已耗时{1}ms", new DateTime(getCode_GetRangeEndTicks).ToString("HH:mm:ss fff"), new TimeSpan(getCode_GetRangeEndTicks - getCode_BeforeWhileTicks).TotalMilliseconds.ToString("F0")));
                            //sbProcessInfo.AppendLine(string.Format("准备进入Parallel：{0}，已耗时{1}ms", new DateTime(getCode_BeforeParallelTicks).ToString("HH:mm:ss fff"), new TimeSpan(getCode_BeforeParallelTicks - getCode_GetRangeEndTicks).TotalMilliseconds.ToString("F0")));

                            ParallelProcessOcr(lstProcess, processPerTime, cancellationToken.Token, totalResultCount, startTicks);
                            //大于等于期望结果个数，跳出循环，否则继续循环
                            if (totalResultCount.TotalCount >= nMaxResultCount)
                            {
                                break;
                            }

                            processPerTime = Math.Max(1, Math.Min(processPerTime, nMaxResultCount - totalResultCount.TotalCount));
                        }
                    }
                }
                catch (Exception oe)
                {
                    Console.WriteLine(oe.Message);
                }
                if (totalResultCount.TotalCount == 0)
                {
                    var strError = "识别失败，请稍后重试！\nTrace:" + processEntity.StrIndex + "\nNode:" + ConfigHelper.OcrServer;
                    SetEmptyResult(processEntity, strError, startTicks);
                }
                //if (totalResultCount.TotalCount < nMaxResultCount)
                //{
                //}
            }
            else
            {
                var strErrorMsg = "未找到匹配的OCR功能，暂不支持当前操作！";
                if (processEntity.OcrGroup != OcrGroupType.不限)
                {
                    strErrorMsg = string.Format("【{0}】通道暂不支持当前操作，请更换其他识别通道后重试！", processEntity.OcrGroup.ToString());
                }
                SetEmptyResult(processEntity, strErrorMsg, startTicks);
            }
        }

        private static List<CusImageEntity> GetSupportTypes(CusImageEntity processEntity, int nMaxResultCount)
        {
            var lstResult = GetEnableRecType(processEntity);

            //文本转表格
            if (lstResult.Count <= nMaxResultCount)
            {
                if (Equals(processEntity.OcrType, OcrType.表格))
                {
                    processEntity.IsSupportVertical = true;
                    processEntity.OcrType = OcrType.文本;
                    processEntity.IsTextToTable = true;
                    var lstTmpType = GetEnableRecType(processEntity);
                    foreach (var item in lstTmpType)
                    {
                        if (!lstResult.Any(p => Equals(p.ProcessId, item.ProcessId)))
                            lstResult.Add(item);
                    }
                }
                else if (Equals(processEntity.OcrType, OcrType.翻译))
                {

                }
            }
            return lstResult;
        }

        private static List<CusImageEntity> GetEnableRecType(CusImageEntity processEntity)
        {
            var lstResult = new List<CusImageEntity>();
            var lstTmpType = BaseRecHelper.GetEnableRecType(true, processEntity.OcrType, processEntity.OcrGroup
        , processEntity.ProcessId, processEntity.IsSupportVertical, processEntity.FileExt, processEntity.FromLanguage, processEntity.ToLanguage);
            lstTmpType.ForEach(type =>
            {
                var objTmp = DeepCopyByReflect(processEntity);
                objTmp.ProcessId = type.OcrType;
                objTmp.IsSupportUrl = type.IsSupportUrl;
                lstResult.Add(objTmp);
            });
            return lstResult;
        }

        private static T DeepCopyByReflect<T>(T obj)
        {
            if (obj == null) return obj;

            return JsonConvert.DeserializeObject<T>(JsonConvert.SerializeObject(obj));
        }

        private static async Task SetEmptyResult(CusImageEntity processEntity, string errorMsg, long startTicks)
        {
            try
            {
                processEntity.State = ProcessState.失败;
                var content = new OcrContent()
                {
                    id = processEntity.StrIndex,
                    ocrType = processEntity.OcrType,
                    processName = "温馨提示",
                    result = new ResultEntity()
                    {
                        autoText = errorMsg,
                        spiltText = errorMsg,
                        verticalText = "{}"
                    }
                };

                // 设置时间信息
                content.OcrTime.ClientOcrEngineStart = startTicks;
                content.OcrTime.OcrServerEnd = ServerTime.DateTime.Ticks;

                ProcessNew.SendOcrResult(content);

                if (!processEntity.isLstOcrGroup)
                {
                    var strPost = string.Format("account={0}&token={1}&id={2}", System.Web.HttpUtility.UrlEncode(processEntity.Account), System.Web.HttpUtility.UrlEncode(processEntity.Token), processEntity.StrIndex);
                    var result = await WebClientSyncExt.GetHtmlAsync(ServerInfo.HostAccount?.FullUrl + "code.ashx?op=ocrresultcompensate" + "&version=" + ServerInfo.DtNowVersion.Ticks, strPost, 30);
                    _Log.Info($"返回OCR失败次数结果:{result}");
                }
            }
            catch (Exception e)
            {
                LogHelper.Log.Error("SetEmptyResult出错！", e);
            }
        }

        private static bool CheckIsForbidOperate(CusImageEntity processEntity, UserType limit)
        {
            bool isForbid = false;
            if ((Equals(processEntity.OcrType, OcrType.文本) && !limit.IsSupportTxt)
                            || (Equals(processEntity.OcrType, OcrType.竖排) && !limit.IsSupportVertical)
                            || (Equals(processEntity.OcrType, OcrType.表格) && !limit.IsSupportTable)
                            || (Equals(processEntity.OcrType, OcrType.公式) && !limit.IsSupportMath)
                            || (Equals(processEntity.OcrType, OcrType.翻译) && !limit.IsSupportTranslate)
                            || (CommonHelper.DocFileTypes.Contains(processEntity.FileExt) && !Equals(processEntity.FileExt, "txt") && !limit.IsSupportDocFile)
                            )
            {
                processEntity.State = ProcessState.失败;
                isForbid = true;
            }

            return isForbid;
        }

        private static void ParallelProcessOcr(IEnumerable<CusImageEntity> listProcessEntity, int perTimeProcess, CancellationToken token, RefCount refCount, long getCodeStartTicks)
        {
            //var preParallelTicks = ServerTime.DateTime.Ticks;
            // 创建任务列表
            var tasks = listProcessEntity.Select(processEntity => 
            {
                // 记录任务提交到线程池的时间
                processEntity.OcrTime.ThreadPoolQueuedTime = ServerTime.DateTime.Ticks;
                
                return Task.Run(() => 
                {
                    try
                    {
                        if (token.IsCancellationRequested) return;

                        // 记录线程池线程开始执行的时间
                        processEntity.OcrTime.ThreadPoolExecutionStartTime = ServerTime.DateTime.Ticks;

                        var content = ProcessPerOcr(processEntity);
                        if (content != null && content.result != null &&
                            (!string.IsNullOrEmpty(content.result.autoText) || content.result.files?.Count > 0))
                        {
                            ProcessNew.SendOcrResult(content);
                            refCount.Increment();
                        }
                    }
                    catch (Exception ex)
                    {
                        _Log.Error("并行处理OCR任务异常", ex);
                    }
                });
            }).ToArray();

            // 等待所有任务完成
            Task.WaitAll(tasks);
        }

        private static OcrContent ProcessPerOcr(CusImageEntity processEntity)
        {
            var ocr = BaseRecHelper.GetInstance(processEntity.OcrType, processEntity.ProcessId ?? 0);
            if (ocr.FileSizeLimit > 0 && processEntity.FileContentLength > 0 && ocr.FileSizeLimit < processEntity.FileContentLength)
            {
                //尺寸超限，不处理
                return null;
            }

            // 设置客户端准备处理资源的时间
            processEntity.OcrTime.OcrServerBegin = ServerTime.DateTime.Ticks;

            // 预先准备所有OCR参数，避免在GetResult内部进行配置，减少延迟
            ocr.IsFromLeftToRight = processEntity.IsFromLeftToRight;
            ocr.IsFromTopToDown = processEntity.IsFromTopToDown;
            ocr.IsAutoDirectionDetector = processEntity.IsAutoDirectionDetector;

            return ocr.GetResult(processEntity);
        }
    }
}
