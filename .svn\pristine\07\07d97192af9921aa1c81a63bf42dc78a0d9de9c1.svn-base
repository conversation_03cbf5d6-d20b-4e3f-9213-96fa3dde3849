﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="12.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{3DE4EEE8-B020-4863-BBDE-5118B1B7137F}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>DocOcr</RootNamespace>
    <AssemblyName>DocOcr</AssemblyName>
    <TargetFrameworkVersion>v4.7.2</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <TargetFrameworkProfile />
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <Prefer32Bit>false</Prefer32Bit>
    <PlatformTarget>AnyCPU</PlatformTarget>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="Aliyun.OSS, Version=2.13.0.0, Culture=neutral, PublicKeyToken=0ad4175f0dac0b9b, processorArchitecture=MSIL">
      <HintPath>..\packages\Aliyun.OSS.SDK.2.13.0\lib\net45\Aliyun.OSS.dll</HintPath>
    </Reference>
    <Reference Include="Newtonsoft.Json, Version=13.0.0.0, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed, processorArchitecture=MSIL">
      <HintPath>..\packages\Newtonsoft.Json.13.0.1\lib\net45\Newtonsoft.Json.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Web" />
    <Reference Include="System.Web.Extensions" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="DuHuiDocRec.cs" />
    <Compile Include="GroupDocsMDRec.cs" />
    <Compile Include="HanWangDocRec.cs" />
    <Compile Include="HanWangLiteDocRec.cs" />
    <Compile Include="GroupDocsRec.cs" />
    <Compile Include="HeHeMDRec.cs" />
    <Compile Include="HeHeDocRec.cs" />
    <Compile Include="QQBroswerRec.cs" />
    <Compile Include="WdkuRec.cs" />
    <Compile Include="DeeplRec.cs" />
    <Compile Include="WPSRec.cs" />
    <Compile Include="YouDaoAPIRec.cs" />
    <Compile Include="HuoShanPDFRec.cs" />
    <Compile Include="ConstHelper.cs" />
    <Compile Include="BaseDocOcrRec.cs" />
    <Compile Include="DocOcrType.cs" />
    <Compile Include="CaiYunRec.cs" />
    <Compile Include="DuGuangRec.cs" />
    <Compile Include="SouGouRec.cs" />
    <Compile Include="XunJiePDFRec.cs" />
    <Compile Include="XunJieTransRec.cs" />
    <Compile Include="XunJieV4Rec.cs" />
    <Compile Include="YouDaoTransFileRec.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\CommonLib\CommonLib.csproj">
      <Project>{fc03a7d4-8ef2-4dea-a15a-c099eb77b0eb}</Project>
      <Name>CommonLib</Name>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup>
    <None Include="app.config" />
    <None Include="packages.config" />
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>