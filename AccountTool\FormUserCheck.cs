﻿using System;
using System.Windows.Forms;

namespace AccountTool
{
    public partial class FormUserCheck : Form
    {
        public FormUserCheck()
        {
            InitializeComponent();
        }

        private string strCookie = "";
        string strToken = "";

        private void btnCheck_Click(object sender, EventArgs e)
        {
            string html = HttpHelper.GetHtml("https://memberprod.alipay.com/account/reg/completeRealnameCheck.json", ref strCookie, ""
                , string.Format("_input_charset=utf-8&ctoken={0}&userName={1}&certNo={2}", strToken, txtUserName.Text.Trim(), txtIDCard.Text.Trim()));
            MessageBox.Show(html);
        }

        private void FormUserCheck_Load(object sender, EventArgs e)
        {
        }

        private void webBrowser1_DocumentCompleted(object sender, WebBrowserDocumentCompletedEventArgs e)
        {
            try
            {
                try
                {
                    if (webURL.DocumentText.IndexOf("_form_token") > 0)
                    {
                        strToken = webURL.DocumentText.Substring(webURL.DocumentText.IndexOf("_form_token"));
                        strToken = strToken.Substring(strToken.IndexOf("value=\"") + "value=\"".Length);
                        strToken = strToken.Substring(0, strToken.IndexOf("\""));
                        lblToken.Text = string.Format("Token:{0}", strToken);
                        strCookie = webURL.Document.Cookie;
                    }
                }
                catch (Exception oe)
                {
                    Console.Read();
                }
            }
            catch
            {
            }
        }

        private void btnGetToken_Click(object sender, EventArgs e)
        {
            strToken = "";
            webURL.Navigate("https://memberprod.alipay.com/account/activeUserValid.htm?_pdType=txtActivity&sign=&merchant_user_id=&email=net10010%40vip.qq.com&partner=&ac=95a80d64b88f6f126a90abd54078a6e5");
        }
    }
}
