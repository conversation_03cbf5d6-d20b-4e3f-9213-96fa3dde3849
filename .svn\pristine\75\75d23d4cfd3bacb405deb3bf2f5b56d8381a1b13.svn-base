﻿namespace NewTicket
{
    public delegate void CbDelegate();
    public delegate void CbDelegate<T>(T obj);
    public delegate void CbDelegate<T1, T2>(T1 obj1, T2 obj2);
    public delegate void CbDelegate<T1, T2, T3>(T1 obj1, T2 obj2, T3 obj3);
    public delegate void CbDelegate<T1, T2, T3, T4>(T1 obj1, T2 obj2, T3 obj3, T4 obj4);
    public delegate void CbDelegate<T1, T2, T3, T4, T5>(T1 obj1, T2 obj2, T3 obj3, T4 obj4, T5 obj5);
    public delegate void CbDelegate<T1, T2, T3, T4, T5, T6>(T1 obj1, T2 obj2, T3 obj3, T4 obj4, T5 obj5, T6 obj6);
    public delegate void CbDelegate<T1, T2, T3, T4, T5, T6, T7>(T1 obj1, T2 obj2, T3 obj3, T4 obj4, T5 obj5, T6 obj6, T7 obj7);
}

