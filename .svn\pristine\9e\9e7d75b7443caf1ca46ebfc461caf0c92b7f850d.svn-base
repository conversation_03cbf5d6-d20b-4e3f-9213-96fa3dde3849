﻿using CommonLib;
using System;
using System.Collections.Generic;
using System.Collections.Specialized;
using System.IO;

namespace HanZiOcr
{
    /// <summary>
    /// 易道博识
    /// 调试页面：http://ai.exocr.com/v1_table_api.html
    /// </summary>
    public class YiDaoRec : BaseOcrRec
    {
        public YiDaoRec()
        {
            OcrType = HanZiOcrType.易道博识;

            MaxExecPerTime = 20;

            LstJsonPreProcessArray = new List<object>() { "result" };
            IsSupportVertical = true;
            DicDeserializeVerticalJson = new Dictionary<string, string> { { "location", "position" } };
        }

        protected override string GetHtml(OcrContent content)
        {
            var byt = Convert.FromBase64String(content.strBase64);
            var result = PostFileResult(byt);
            return result;
        }

        private string PostFileResult(byte[] content)
        {
            var result = "";
            try
            {
                var url = "http://ai.exocr.com/apqi/api/reco/all";
                var file = new UploadFileInfo()
                {
                    Name = "image_binary",
                    Filename = "1.jpg",
                    ContentType = "image/jpeg",
                    Stream = new MemoryStream(content)
                };
                var values = new NameValueCollection() {
                    { "reco_id","108"}
                };
                var header = new NameValueCollection() {
                    { "Refer","http://ai.exocr.com/ocr/"}
                };
                result = PostFile(url, new[] { file }, values, header);
            }
            catch (Exception)
            {

            }
            return result;
        }

    }
}