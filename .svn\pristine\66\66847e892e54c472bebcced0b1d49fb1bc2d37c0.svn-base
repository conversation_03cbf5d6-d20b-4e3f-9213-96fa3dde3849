﻿
using CommonLib;
using System.Collections.Generic;

namespace HanZiOcr
{
    /// <summary>
    /// https://web.baimiaoapp.com/
    /// </summary>
    public class BaiMiaoYouDaoRec : BaseOcrRec
    {
        public BaiMiaoYouDaoRec()
        {
            OcrGroup = OcrGroupType.有道;
            OcrType = HanZiOcrType.有道BM;
            MaxExecPerTime = 10;

            LstJsonPreProcessArray = new List<object>() { "data", "ydResp", "Result", "regions" };
            LstJsonNextProcessArray = new List<object>() { "lines" };
            StrResultJsonSpilt = "text";
            IsSupportVertical = true;
            LstVerticalLocation = new List<object>() { "boundingBox" };
        }

        protected override string GetHtml(OcrContent content)
        {
            var result = BaiMiaoProcessHelper.GetHtml(content, "https://web.baimiaoapp.com/api/ocr/image/youdao", ExecTimeOutSeconds);
            return result;
        }
    }
}