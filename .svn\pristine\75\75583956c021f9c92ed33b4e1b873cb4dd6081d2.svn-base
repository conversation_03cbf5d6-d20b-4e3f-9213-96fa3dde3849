.page-footer {
  color: #41464f;
  text-align: center;
  font-size: 14px;
}
.page-footer .line {
  width: 100%;
  border-bottom: 1px solid #e1e6ed;
  margin-top: 50px;
}
.page-footer .footer h6 {
  margin-bottom: 28px;
  color: #1a1c1f;
}
.page-footer .footer > a {
  margin-bottom: 20px;
  display: block;
  color: #41464f;
}
.page-footer .footer > a:hover {
  color: #006cff !important;
}
.page-footer .hu-an {
  margin-top: 90px;
}
.page-footer .hu-an a {
  color: #41464f;
}
.page-footer .contact-us {
  text-align: left;
}
.product-faq2 .body-content {
  box-shadow: none !important;
}
.product-faq2 {
  display: none;
}
#bto_phone {
  display: none;
}
#bto_phone {
  position: fixed;
  bottom: 0;
  width: 100%;
  height: 72px;
  line-height: 72px;
  z-index: 2;
}
.fiexd-phone {
  display: flex;
  align-items: center;
  background: #1764ff;
  color: #fff;
  justify-content: space-around;
}
.call-num {
  color: #1764ff;
  background: #fff;
  display: inline-block;
  width: 100px;
  height: 36px;
  line-height: 36px;
  text-align: center;
  border-radius: 2px;
}
.call-num:hover {
  color: #1764ff;
}
@media (max-width: 768px) {
  #bto_phone {
    display: block;
  }
  .product-faq2 {
    display: block;
  }
  .page-footer {
    display: none;
  }
}

@media screen and (max-width: 576px) {
  .dl-link {
    margin: 20px 10px;
  }
  .contact-info {
    margin-top: 40px;
  }
  .page-footer h6 {
    margin-bottom: 8px;
    font-size: 14px;
  }
}

@media screen and (max-width: 991px) {
  .page-footer .contact-us {
    display: flex;
    flex-direction: column;
    align-items: center;
  }
  .page-footer .wb-tips {
    font-size: 13px;
  }
  .page-footer .hu-an {
    font-size: 12px;
    margin-top: 12px !important;
    margin-bottom: 0 !important;
    text-align: center;
  }
  .page-footer .hu-an.icp {
    margin-bottom: 16px !important;
  }
}

@media screen and (min-width: 768px) and (max-width: 991px) {
  .page-footer .item-foot {
    font-size: 14px !important;
  }
}
