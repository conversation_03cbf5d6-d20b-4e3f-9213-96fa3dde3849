﻿<?xml version="1.0" encoding="utf-8"?>
<!--
https://go.microsoft.com/fwlink/?LinkID=208121.
-->
<Project>
  <PropertyGroup>
    <_PublishTargetUrl>C:\Users\<USER>\Desktop\Code.Process.Web</_PublishTargetUrl>
    <History>True|2023-08-30T09:26:50.3355757Z;</History>
    <LastFailureDetails />
  </PropertyGroup>
  <ItemGroup>
    <File Include="bin/Aliyun.OSS.dll">
      <publishTime>02/07/2021 02:23:08</publishTime>
    </File>
    <File Include="bin/Aliyun.OSS.pdb">
      <publishTime>02/07/2021 02:23:08</publishTime>
    </File>
    <File Include="bin/Code.Process.Common.dll">
      <publishTime>08/30/2023 17:00:26</publishTime>
    </File>
    <File Include="bin/Code.Process.Common.dll.config">
      <publishTime>01/09/2023 18:45:54</publishTime>
    </File>
    <File Include="bin/Code.Process.Common.pdb">
      <publishTime>08/30/2023 17:00:26</publishTime>
    </File>
    <File Include="bin/Code.Process.Web.dll">
      <publishTime>08/30/2023 17:26:48</publishTime>
    </File>
    <File Include="bin/Code.Process.Web.pdb">
      <publishTime>08/30/2023 17:26:48</publishTime>
    </File>
    <File Include="bin/CommonLib.dll">
      <publishTime>08/30/2023 15:48:43</publishTime>
    </File>
    <File Include="bin/CommonLib.dll.config">
      <publishTime>11/16/2022 16:44:21</publishTime>
    </File>
    <File Include="bin/CommonLib.pdb">
      <publishTime>08/30/2023 15:48:43</publishTime>
    </File>
    <File Include="bin/COSXML.dll">
      <publishTime>01/03/2023 16:38:18</publishTime>
    </File>
    <File Include="bin/de/PdfSharp.resources.dll">
      <publishTime>02/04/2019 13:21:52</publishTime>
    </File>
    <File Include="bin/DocOcr.dll">
      <publishTime>08/30/2023 17:00:23</publishTime>
    </File>
    <File Include="bin/DocOcr.dll.config">
      <publishTime>12/09/2022 18:12:08</publishTime>
    </File>
    <File Include="bin/DocOcr.pdb">
      <publishTime>08/30/2023 17:00:23</publishTime>
    </File>
    <File Include="bin/HanZiOcr.dll">
      <publishTime>08/30/2023 17:00:24</publishTime>
    </File>
    <File Include="bin/HanZiOcr.dll.config">
      <publishTime>01/09/2023 18:45:54</publishTime>
    </File>
    <File Include="bin/HanZiOcr.pdb">
      <publishTime>08/30/2023 17:00:24</publishTime>
    </File>
    <File Include="bin/Log4Net.config">
      <publishTime>08/11/2023 16:41:38</publishTime>
    </File>
    <File Include="bin/log4net.dll">
      <publishTime>06/10/2021 16:46:13</publishTime>
    </File>
    <File Include="bin/MathOcr.dll">
      <publishTime>08/30/2023 17:00:25</publishTime>
    </File>
    <File Include="bin/MathOcr.dll.config">
      <publishTime>11/16/2022 16:44:22</publishTime>
    </File>
    <File Include="bin/MathOcr.pdb">
      <publishTime>08/30/2023 17:00:25</publishTime>
    </File>
    <File Include="bin/Microsoft.CodeDom.Providers.DotNetCompilerPlatform.dll">
      <publishTime>09/05/2018 16:10:50</publishTime>
    </File>
    <File Include="bin/Newtonsoft.Json.dll">
      <publishTime>03/18/2021 04:03:36</publishTime>
    </File>
    <File Include="bin/Notice.Process.Common.dll">
      <publishTime>08/29/2023 15:36:19</publishTime>
    </File>
    <File Include="bin/Notice.Process.Common.pdb">
      <publishTime>08/29/2023 15:36:19</publishTime>
    </File>
    <File Include="bin/PdfSharp.dll">
      <publishTime>02/04/2019 13:21:52</publishTime>
    </File>
    <File Include="bin/roslyn/csc.exe">
      <publishTime>08/08/2018 05:38:48</publishTime>
    </File>
    <File Include="bin/roslyn/csc.exe.config">
      <publishTime>08/08/2018 12:18:12</publishTime>
    </File>
    <File Include="bin/roslyn/csc.rsp">
      <publishTime>08/08/2018 12:09:42</publishTime>
    </File>
    <File Include="bin/roslyn/csi.exe">
      <publishTime>08/08/2018 05:38:48</publishTime>
    </File>
    <File Include="bin/roslyn/csi.exe.config">
      <publishTime>08/08/2018 12:18:34</publishTime>
    </File>
    <File Include="bin/roslyn/csi.rsp">
      <publishTime>08/08/2018 12:09:56</publishTime>
    </File>
    <File Include="bin/roslyn/Microsoft.Build.Tasks.CodeAnalysis.dll">
      <publishTime>08/08/2018 05:38:48</publishTime>
    </File>
    <File Include="bin/roslyn/Microsoft.CodeAnalysis.CSharp.dll">
      <publishTime>08/08/2018 05:38:48</publishTime>
    </File>
    <File Include="bin/roslyn/Microsoft.CodeAnalysis.CSharp.Scripting.dll">
      <publishTime>08/08/2018 05:38:48</publishTime>
    </File>
    <File Include="bin/roslyn/Microsoft.CodeAnalysis.dll">
      <publishTime>08/08/2018 05:38:48</publishTime>
    </File>
    <File Include="bin/roslyn/Microsoft.CodeAnalysis.Scripting.dll">
      <publishTime>08/08/2018 05:38:48</publishTime>
    </File>
    <File Include="bin/roslyn/Microsoft.CodeAnalysis.VisualBasic.dll">
      <publishTime>08/08/2018 05:38:48</publishTime>
    </File>
    <File Include="bin/roslyn/Microsoft.CSharp.Core.targets">
      <publishTime>08/08/2018 12:09:42</publishTime>
    </File>
    <File Include="bin/roslyn/Microsoft.DiaSymReader.Native.amd64.dll">
      <publishTime>12/05/2017 09:36:44</publishTime>
    </File>
    <File Include="bin/roslyn/Microsoft.DiaSymReader.Native.x86.dll">
      <publishTime>12/05/2017 09:36:44</publishTime>
    </File>
    <File Include="bin/roslyn/Microsoft.Managed.Core.targets">
      <publishTime>08/08/2018 12:09:42</publishTime>
    </File>
    <File Include="bin/roslyn/Microsoft.VisualBasic.Core.targets">
      <publishTime>08/08/2018 12:09:42</publishTime>
    </File>
    <File Include="bin/roslyn/Microsoft.Win32.Primitives.dll">
      <publishTime>11/05/2016 18:55:32</publishTime>
    </File>
    <File Include="bin/roslyn/System.AppContext.dll">
      <publishTime>11/05/2016 18:55:34</publishTime>
    </File>
    <File Include="bin/roslyn/System.Collections.Immutable.dll">
      <publishTime>05/16/2018 03:29:34</publishTime>
    </File>
    <File Include="bin/roslyn/System.Console.dll">
      <publishTime>11/05/2016 18:55:48</publishTime>
    </File>
    <File Include="bin/roslyn/System.Diagnostics.DiagnosticSource.dll">
      <publishTime>11/05/2016 18:55:52</publishTime>
    </File>
    <File Include="bin/roslyn/System.Diagnostics.FileVersionInfo.dll">
      <publishTime>11/05/2016 18:55:56</publishTime>
    </File>
    <File Include="bin/roslyn/System.Diagnostics.StackTrace.dll">
      <publishTime>11/05/2016 18:55:52</publishTime>
    </File>
    <File Include="bin/roslyn/System.Globalization.Calendars.dll">
      <publishTime>11/05/2016 18:56:02</publishTime>
    </File>
    <File Include="bin/roslyn/System.IO.Compression.dll">
      <publishTime>11/05/2016 18:56:08</publishTime>
    </File>
    <File Include="bin/roslyn/System.IO.Compression.ZipFile.dll">
      <publishTime>11/05/2016 18:56:04</publishTime>
    </File>
    <File Include="bin/roslyn/System.IO.FileSystem.dll">
      <publishTime>11/05/2016 18:56:08</publishTime>
    </File>
    <File Include="bin/roslyn/System.IO.FileSystem.Primitives.dll">
      <publishTime>11/05/2016 18:56:08</publishTime>
    </File>
    <File Include="bin/roslyn/System.Net.Http.dll">
      <publishTime>11/05/2016 18:56:30</publishTime>
    </File>
    <File Include="bin/roslyn/System.Net.Sockets.dll">
      <publishTime>11/05/2016 18:56:34</publishTime>
    </File>
    <File Include="bin/roslyn/System.Reflection.Metadata.dll">
      <publishTime>05/16/2018 03:29:44</publishTime>
    </File>
    <File Include="bin/roslyn/System.Runtime.InteropServices.RuntimeInformation.dll">
      <publishTime>11/05/2016 18:57:00</publishTime>
    </File>
    <File Include="bin/roslyn/System.Security.Cryptography.Algorithms.dll">
      <publishTime>11/05/2016 18:57:14</publishTime>
    </File>
    <File Include="bin/roslyn/System.Security.Cryptography.Encoding.dll">
      <publishTime>11/05/2016 18:57:08</publishTime>
    </File>
    <File Include="bin/roslyn/System.Security.Cryptography.Primitives.dll">
      <publishTime>11/05/2016 18:57:18</publishTime>
    </File>
    <File Include="bin/roslyn/System.Security.Cryptography.X509Certificates.dll">
      <publishTime>11/05/2016 18:57:18</publishTime>
    </File>
    <File Include="bin/roslyn/System.Text.Encoding.CodePages.dll">
      <publishTime>11/05/2016 18:57:20</publishTime>
    </File>
    <File Include="bin/roslyn/System.Threading.Tasks.Extensions.dll">
      <publishTime>11/05/2016 18:57:24</publishTime>
    </File>
    <File Include="bin/roslyn/System.ValueTuple.dll">
      <publishTime>11/05/2016 18:57:30</publishTime>
    </File>
    <File Include="bin/roslyn/System.Xml.ReaderWriter.dll">
      <publishTime>11/05/2016 18:57:36</publishTime>
    </File>
    <File Include="bin/roslyn/System.Xml.XmlDocument.dll">
      <publishTime>11/05/2016 18:57:34</publishTime>
    </File>
    <File Include="bin/roslyn/System.Xml.XPath.dll">
      <publishTime>11/05/2016 18:57:40</publishTime>
    </File>
    <File Include="bin/roslyn/System.Xml.XPath.XDocument.dll">
      <publishTime>11/05/2016 18:57:34</publishTime>
    </File>
    <File Include="bin/roslyn/vbc.exe">
      <publishTime>08/08/2018 05:38:48</publishTime>
    </File>
    <File Include="bin/roslyn/vbc.exe.config">
      <publishTime>08/08/2018 12:18:18</publishTime>
    </File>
    <File Include="bin/roslyn/vbc.rsp">
      <publishTime>08/08/2018 12:09:46</publishTime>
    </File>
    <File Include="bin/roslyn/VBCSCompiler.exe">
      <publishTime>08/08/2018 05:38:48</publishTime>
    </File>
    <File Include="bin/roslyn/VBCSCompiler.exe.config">
      <publishTime>08/08/2018 12:18:16</publishTime>
    </File>
    <File Include="bin/ServiceStack.Text.dll">
      <publishTime>06/06/2023 19:11:00</publishTime>
    </File>
    <File Include="bin/System.Buffers.dll">
      <publishTime>02/19/2020 10:05:18</publishTime>
    </File>
    <File Include="bin/System.Diagnostics.DiagnosticSource.dll">
      <publishTime>05/09/2023 14:26:36</publishTime>
    </File>
    <File Include="bin/System.Memory.dll">
      <publishTime>05/08/2022 11:31:02</publishTime>
    </File>
    <File Include="bin/System.Numerics.Vectors.dll">
      <publishTime>05/15/2018 13:29:44</publishTime>
    </File>
    <File Include="bin/System.Runtime.CompilerServices.Unsafe.dll">
      <publishTime>05/09/2023 14:26:36</publishTime>
    </File>
    <File Include="bin/TableOcr.dll">
      <publishTime>08/30/2023 17:00:25</publishTime>
    </File>
    <File Include="bin/TableOcr.dll.config">
      <publishTime>11/16/2022 16:44:22</publishTime>
    </File>
    <File Include="bin/TableOcr.pdb">
      <publishTime>08/30/2023 17:00:25</publishTime>
    </File>
    <File Include="bin/TransOcr.dll">
      <publishTime>08/30/2023 17:00:25</publishTime>
    </File>
    <File Include="bin/TransOcr.dll.config">
      <publishTime>11/16/2022 16:44:21</publishTime>
    </File>
    <File Include="bin/TransOcr.pdb">
      <publishTime>08/30/2023 17:00:25</publishTime>
    </File>
    <File Include="Default.aspx">
      <publishTime>08/22/2023 22:27:58</publishTime>
    </File>
    <File Include="Global.asax">
      <publishTime>07/28/2023 17:33:09</publishTime>
    </File>
    <File Include="Log4Net.config">
      <publishTime>08/11/2023 16:41:38</publishTime>
    </File>
  </ItemGroup>
</Project>