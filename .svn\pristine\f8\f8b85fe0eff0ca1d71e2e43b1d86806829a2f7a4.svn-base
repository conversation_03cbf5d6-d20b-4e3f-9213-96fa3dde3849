﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Web;

namespace OcrLiteLib
{
    public class MyHttpServer : HttpServer
    {
        public MyHttpServer(int port)
            : base(port)
        {
            Init();
        }

        OcrLite OcrLite = null;

        System.Web.Script.Serialization.JavaScriptSerializer JavaScriptSerializer = new System.Web.Script.Serialization.JavaScriptSerializer();

        private void Init()
        {
            string appPath = AppDomain.CurrentDomain.BaseDirectory;
            string modelsDir = appPath + "models";
            OcrLite = new OcrLite(modelsDir, OcrUtils.MaxDegreeOfParallelism);
        }
        public static string SubString(string strSource, string strSpilt, string strEnd = "")
        {
            return strSource.SubStringHorspool(strSpilt, strEnd).Trim();
        }

        public override void handleGETRequest(HttpProcessor p)
        {
            try
            {
                p.writeSuccess();
                if (OcrLite == null)
                {
                    p.outputStream.Write("Initing…");
                }
                else
                {
                    var resultStr = "no";
                    if (p.http_url.StartsWith("/Code.do"))
                    {
                        try
                        {
                            var type = CommonLib.CommonHelper.SubString(p.http_url, "type=", "&");
                            var img = CommonLib.CommonHelper.SubString(p.http_url, "img=", "");
                            if (!string.IsNullOrEmpty(type) && !string.IsNullOrEmpty(img))
                            {
                                resultStr = processOcr(type, img);
                            }
                        }
                        catch (Exception oe)
                        {
                            Console.WriteLine(oe.Message + Environment.NewLine + oe.StackTrace + Environment.NewLine + oe.InnerException?.Message + Environment.NewLine + oe.InnerException?.StackTrace);
                        }
                    }
                    p.outputStream.Write(resultStr);
                }
            }
            catch
            {
            }
        }

        private string processOcr(string type, string img)
        {
            var resultStr = string.Empty;
            var byt = Convert.FromBase64String(HttpUtility.HtmlDecode(img));
            var lstCell = new List<TextCellInfo>();
            switch (type)
            {
                case "ocr":
                    {
                        var result = OcrLite.Detect(byt, 50, 0, (float)0.618, (float)0.3, (float)3.0, (float)1.8, true, true);
                        if (result != null && !string.IsNullOrEmpty(result.StrRes))
                        {
                            result.TextBlocks?.ForEach(text =>
                            {
                                var cell = new TextCellInfo()
                                {
                                    location = new LocationInfo()
                                    {
                                        left = Math.Min(text.BoxPoints[0].X, text.BoxPoints[3].X),
                                        top = Math.Min(text.BoxPoints[0].Y, text.BoxPoints[1].Y),
                                        width = Math.Min(text.BoxPoints[1].X - text.BoxPoints[0].X, text.BoxPoints[2].X - text.BoxPoints[3].X),
                                        height = Math.Min(text.BoxPoints[3].Y - text.BoxPoints[0].Y, text.BoxPoints[2].Y - text.BoxPoints[1].Y),
                                    },
                                    words = text.Text
                                };
                                lstCell.Add(cell);
                            });
                        }
                    }
                    break;
                case "line":
                    {
                        var result = OcrLite.DetectLine(byt, 50, 0, (float)0.618, (float)0.3, (float)3.0, (float)1.8, true, true);
                        result?.TextBoxs?.ForEach(text =>
                        {
                            var cell = new TextCellInfo()
                            {
                                location = new LocationInfo()
                                {
                                    left = Math.Min(text.Points[0].X, text.Points[3].X),
                                    top = Math.Min(text.Points[0].Y, text.Points[1].Y),
                                    width = Math.Min(text.Points[1].X - text.Points[0].X, text.Points[2].X - text.Points[3].X),
                                    height = Math.Min(text.Points[3].Y - text.Points[0].Y, text.Points[2].Y - text.Points[1].Y),
                                },
                            };
                            lstCell.Add(cell);
                        });
                    }
                    break;
            }
            resultStr = JavaScriptSerializer.Serialize(lstCell);
            return resultStr;
        }

        public override void handlePOSTRequest(HttpProcessor p, StreamReader inputData)
        {
            string arg = inputData.ReadToEnd();
            Console.WriteLine("POST request: {0}   {1}", p.http_url, arg);
        }
    }

    [Serializable]
    public class TextCellInfo
    {
        public string words { get; set; }

        public LocationInfo location { get; set; }

        public bool IsProcessed { get; set; }

        public int PageIndex { get; set; }

        public override string ToString()
        {
            return string.Format("{0},Location:{1}", words, location.ToString());
        }
    }

    [Serializable]
    public class LocationInfo
    {
        public double left { get; set; }
        public double top { get; set; }
        public double width { get; set; }
        public double height { get; set; }

        public string words { get; set; }

        public override string ToString()
        {
            return string.Format("top:{0},left:{1},width:{2},height:{3}", top, left, width, height);
        }
    }
}
