﻿using System;
using System.Linq;
using System.Web.UI;
using CommonLib;

namespace Code.Client.Web
{
    public partial class CodeList : Page
    {
        protected void Page_Load(object sender, EventArgs e)
        {
            if (BoxUtil.GetStringFromObject(Request.QueryString["pwd"]) != "123")
                return;
            LoadData();
        }

        //protected void btnOK_Click(object sender, EventArgs e)
        //{
        //    if (BoxUtil.GetStringFromObject(Request.QueryString["pwd"]) != "123")
        //        return;
        //    LoadData();
        //}

        //protected void btnClear_Click(object sender, EventArgs e)
        //{
        //    if (BoxUtil.GetStringFromObject(Request.QueryString["pwd"]) != "123")
        //        return;
        //    ClearData();
        //}

        private void LoadData()
        {
            //var dataSource = RdsCacheHelper.YuECache.GetAllCode();
            //dataSource = dataSource.OrderByDescending(p => p.DtAdd).ToList();
            //gvDataSource.DataSource = dataSource;
            //gvDataSource.DataBind();
        }

        //{

        //private void ClearData(string strDate = "")
        //    if (BoxUtil.GetStringFromObject(Request.QueryString["pwd"]) != "123")
        //        return;
        //    ConfigHelper.DaMaCache.RemoveAllData("打码计数");
        //}
    }
}