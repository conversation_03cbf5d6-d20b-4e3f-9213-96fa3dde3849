﻿using System;
using System.Collections.Generic;
using Enterprise.Framework.Redis;

namespace CommonLib
{
    public class ApiRequestCache : RedisCacheObject<List<ApiRequestLog>>
    {
        public ApiRequestCache()
        {
            OnCacheObjectNotFound += ApiRequestCache_OnCacheObjectNotFound;
        }

        private void ApiRequestCache_OnCacheObjectNotFound(object sender, RedisCacheEventArgs e)
        {
            e.CacheObject = null;
        }

        protected override string CurrentObject_KeyPrefix
        {
            get { return "ApiRequestCache"; }
        }

        protected override string Object_DBName
        {
            get { return "OPS_Cache"; }
        }

        public void Add(string strKey, ApiRequestLog request)
        {
            var lstMsg = Get(strKey) ?? new List<ApiRequestLog>();
            lstMsg.Add(request);
            Insert(strKey, lstMsg, ServerTime.DateTime.AddDays(30));
        }

        public void Add(string strKey, List<ApiRequestLog> lstMsg)
        {
            Insert(strKey, lstMsg, ServerTime.DateTime.AddDays(30));
        }

    }

    public class ApiRequestLog
    {
        public long BeginTime { get; set; }

        public long EndTime { get; set; }

        public double EsTime { get; set; }
    }
}