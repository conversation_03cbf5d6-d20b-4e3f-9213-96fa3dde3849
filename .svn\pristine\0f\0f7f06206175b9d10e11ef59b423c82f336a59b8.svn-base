﻿using CommonLib;
using System;
using System.Collections.Generic;
using System.Collections.Specialized;
using System.IO;

namespace HanZiOcr
{
    /// <summary>
    /// 灵云开放平台
    /// https://www.aicloud.com/dev/ability/index.html?key=ocr#ability-experience
    /// </summary>
    public class AICloudApiRec : BaseOcrRec
    {
        public AICloudApiRec()
        {
            OcrType = HanZiOcrType.AICloudAPI;
            MaxExecPerTime = 10;

            LstJsonPreProcessArray = new List<object>() { "ResponseInfo", "Result" };
            IsProcessJsonResultByArray = false;
        }

        protected override string GetHtml(OcrContent content)
        {
            var byt = Convert.FromBase64String(content.strBase64);
            var result = PostFileResult(byt);
            //"{\"code\":0,\"msg\":\"成功\",\"data\":\"[{\\\"confidence\\\":1,\\\"word\\\":[{\\\"content\\\":\\\"请点击下图中所有的靴子\\\"},{\\\"content\\\":\\\"火柴\\\"}]}]\",\"content\":null,\"audioPath\":null}"
            return result;
        }

        private string PostFileResult(byte[] content)
        {
            var result = "";
            try
            {
                var url = "http://ocr.aicloud.com/Home/Product/ocrText";
                var file = new UploadFileInfo()
                {
                    Name = "file",
                    Filename = "1.jpg",
                    ContentType = "image/jpg",
                    Stream = new MemoryStream(content)
                };
                var values = new NameValueCollection() {
                    { "text","10"},
                    { "0","t"},
                    { "1","e"},
                    { "2","x"},
                    { "3","t"},
                    { "4","="},
                    { "5","1"},
                    { "6","0"},
                };
                result = PostFile(url, new[] { file }, values);
            }
            catch (Exception)
            {

            }
            return BoxUtil.DeUnicode(result);
        }

    }
}