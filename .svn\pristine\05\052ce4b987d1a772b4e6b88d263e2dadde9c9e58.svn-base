﻿using System;
using System.Collections.Generic;
using System.Text;
using System.IO;
using System.Configuration;

namespace ToolCommon
{
    public static class Log
    {
        static bool IsWriteLog
        {
            get
            {
                try
                {
                    return bool.Parse(ConfigurationManager.AppSettings.Get("IsWriteLog"));
                }
                catch (Exception)
                {
                    return false;
                }
            }
        }

        private static readonly object obj = new object();
        /// <summary>
        /// 操作日志
        /// </summary>
        /// <param name="s">日志能容</param>
        public static void WriteLog(string content)
        {
            WriteLogs(content, "操作日志");
        }
        /// <summary>
        /// 错误日志
        /// </summary>
        /// <param name="s">日志内容</param>
        public static void WriteError(Exception oe, string expMSG = "")
        {
            string strTmp = "";
            try
            {
                if (IsWriteLog && oe != null)
                {
                    strTmp = string.Format("\n{3}\nMessage:{0}\nStackTrace:{1}\nTargetSite:{2}", oe.Message, oe.StackTrace, oe.TargetSite, expMSG);
                }
            }
            catch { }
            WriteLogs(strTmp, "错误日志");
        }

        private static void WriteLogs(string content, string type)
        {
            if (string.IsNullOrEmpty(content))
                return;
            try
            {
                lock (obj)
                {
                    string path = AppDomain.CurrentDomain.BaseDirectory;
                    if (!string.IsNullOrEmpty(path))
                    {
                        path = AppDomain.CurrentDomain.BaseDirectory + "Logs";
                        if (!Directory.Exists(path))
                        {
                            Directory.CreateDirectory(path);
                        }
                        path = path + "\\" + DateTime.Now.ToString("yyyyMM");
                        if (!Directory.Exists(path))
                        {
                            Directory.CreateDirectory(path);
                        }
                        path = path + "\\" + DateTime.Now.ToString("dd") + ".txt";
                        if (!File.Exists(path))
                        {
                            FileStream fs = File.Create(path);
                            fs.Close();
                        }
                        if (File.Exists(path))
                        {
                            StreamWriter sw = new StreamWriter(path, true, System.Text.Encoding.Default);
                            sw.WriteLine(DateTime.Now);
                            sw.WriteLine("日志类型：" + type);
                            sw.WriteLine("详情：" + content);
                            sw.WriteLine("----------------------------------------");
                            sw.Close();
                        }
                    }
                }
            }
            catch { }
        }
    }
}
