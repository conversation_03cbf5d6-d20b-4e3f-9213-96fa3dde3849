﻿using System;
using System.Collections.Specialized;
using System.IO;
using CommonLib;

namespace HanZiOcr
{
    public class EPRec
    {
        private static readonly string strSpilt = "\"description\": \"";

        public static string GetRecImg()
        {
            var result = "";
            var base64 = "";
            for (var j = 0; j < 10; j++)
            {
                for (var i = 11; i < 15; i++)
                {
                    var byt = File.ReadAllBytes(@"D:\助手\Image\0108\Old\" + i + ".jpg"); //10584306645.jpg");
                    if (byt.Length > 0)
                        base64 = Convert.ToBase64String(byt);

                    result += GetContext(base64, true);
                }
            }
            return result;
        }

        public static string GetContext(string strBase64, bool isOrder = false)
        {
            var result = EpHanZiHelper.GetCode(strBase64, isOrder);

            return result.Replace("\\n", "").Trim();
        }
    }
}