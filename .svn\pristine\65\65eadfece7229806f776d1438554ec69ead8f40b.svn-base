﻿using CommonLib;
using System;
using System.Collections.Generic;
using System.Collections.Specialized;
using System.IO;

namespace TransOcr
{
    /// <summary>
    /// 讯飞Lite-讯飞AI体验
    /// 拍照翻译
    /// </summary>
    public class XunFeiTransDevRec : BaseOcrRec
    {
        public XunFeiTransDevRec()
        {
            OcrGroup = OcrGroupType.讯飞;
            OcrType = TransOcrType.讯飞Dev;
            MaxExecPerTime = 23;

            LstJsonPreProcessArray = new List<object>() { "data" };
            LstVerticalLocation = new List<object>() { "position", "bounding_box" };
            IsSupportVertical = true;
            DicDeserializeVerticalJson = new Dictionary<string, string> { { "left", "x" }, { "top", "y" }, { "trans", "translate_result" } };
            StrResultJsonSpilt = "translate_result";
            InitLanguage();
        }

        #region 支持的语言

        private void InitLanguage()
        {
            TransLanguageDic = new Dictionary<TransLanguageTypeEnum, string>();
            TransLanguageDic.Add(TransLanguageTypeEnum.中文, "zh");
            TransLanguageDic.Add(TransLanguageTypeEnum.英文, "en");
            TransLanguageDic.Add(TransLanguageTypeEnum.日语, "ja");
            TransLanguageDic.Add(TransLanguageTypeEnum.韩语, "ko");
            TransLanguageDic.Add(TransLanguageTypeEnum.德语, "de");
            TransLanguageDic.Add(TransLanguageTypeEnum.法语, "fr");
            TransLanguageDic.Add(TransLanguageTypeEnum.俄语, "ru");
            TransLanguageDic.Add(TransLanguageTypeEnum.意大利语, "it");
            TransLanguageDic.Add(TransLanguageTypeEnum.葡萄牙语, "pt");
            TransLanguageDic.Add(TransLanguageTypeEnum.西班牙语, "es");
            TransLanguageDic.Add(TransLanguageTypeEnum.荷兰语, "nl");
        }

        #endregion

        protected override string GetHtml(OcrContent content)
        {
            var byt = Convert.FromBase64String(content.strBase64);

            string from = "", to = "";
            InitLanguage(content, ref from, ref to);

            var result = PostFileResult(byt, from, to);//.Replace("\\\"", "\"").Replace("\\\\", "\\").Replace("\\r", "\r").Replace("\\n", "\n")
            //"{\"code\":0,\"msg\":\"成功\",\"data\":\"[{\\\"confidence\\\":1,\\\"word\\\":[{\\\"content\\\":\\\"请点击下图中所有的靴子\\\"},{\\\"content\\\":\\\"火柴\\\"}]}]\",\"content\":null,\"audioPath\":null}"
            return result;
        }

        private string PostFileResult(byte[] content, string from, string to)
        {
            var result = "";
            try
            {
                var url = "https://aidev.xfyun.cn/xcx/aistack/translate/ocrtrans";
                var file = new UploadFileInfo()
                {
                    Name = "file",
                    Filename = "test.png",
                    ContentType = "image/png",
                    Stream = new MemoryStream(content)
                };
                var values = new NameValueCollection {
                    { "from", from} ,
                    { "to", to },
                };
                var header = new NameValueCollection
                {
                    //{ "account_id", "**********" },
                    //{ "ssoSessionId", "82d13c6f-9c57-4c30-84dc-6899e0f0d8cb" },
                    //{ "cookie", "ssoSessionId=82d13c6f-9c57-4c30-84dc-6899e0f0d8cb; account_id=**********" },
                    { "referer", "https://servicewechat.com/wxa7f07807b0f7f668/30/page-frame.html" }
                };
                result = PostFile(url, new[] { file }, values, header);
            }
            catch (Exception)
            {

            }
            return result;
        }

        /*
            "left": 597,
            "top": 162,
            "right": 248,
            "bottom": 248
         */
        protected override LocationInfo GetLocationByStr(string locationInfoStr)
        {
            var loc = ConstHelper.JavaScriptSerializer.Deserialize<LocationInfo>(locationInfoStr);
            return loc;
        }

    }
}