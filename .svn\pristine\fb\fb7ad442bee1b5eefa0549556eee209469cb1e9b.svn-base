﻿using CommonLib;
using System.Collections.Generic;

namespace HanZiOcr
{
    /// <summary>
    /// 萤石云开放平台-小程序
    /// </summary>
    public class YingShiRec : BaseOcrRec
    {
        public YingShiRec()
        {
            OcrType = HanZiOcrType.萤石;
            LstJsonPreProcessArray = new List<object>() { "data", "words" };
            IsJsonArrayStringWithLocation = true;
            IsSupportVertical = true;

            LstJsonLocationProcessArray = new List<object>() { "data", "locations" };
            IsSupportUrlOcr = true;
            IsDesrializeVerticalByLocation = true;
            DicDeserializeVerticalJson = new Dictionary<string, string> { { "left", "x" }, { "top", "y" } };
            MaxExecPerTime = 22;
        }

        private const string strErrorMsg = "\"code\":\"10002\"";

        protected override string GetHtml(OcrContent content)
        {
            var result = DoRequestByContent(content.strBase64, false);
            return result;
        }

        public override string GetHtmlByUrl(OcrContent content)
        {
            var result = DoRequestByContent(content.url, true);
            return result;
        }

        private string DoRequestByContent(string content, bool isUrl)
        {
            var result = "";
            InitToken();
            if (!string.IsNullOrEmpty(strToken))
            {
                string strPost = "accessToken=" + strToken + "&dataType=" + (isUrl ? "0" : "1") + "&operation=rect&image=" +
                                 System.Web.HttpUtility.UrlEncode(content);
                result = WebClientSyncExt.GetHtml("https://open.ys7.com/api/lapp/intelligence/ocr/generic", strPost,
                    ExecTimeOutSeconds);
                if (result.Contains(strErrorMsg))
                {
                    strToken = "";
                }
            }

            return result;
        }

        private const string strTokenSpilt = "\"accessToken\":\"";

        private string strToken = "";

        private void InitToken()
        {
            if (string.IsNullOrEmpty(strToken))
            {
                strToken = GetToken(lstKeys.GetRndItem());
            }
        }

        private List<string> lstKeys = new List<string>() {
            "appKey=471c96355ebe4f93a6f44d9861093747&appSecret=a67ddfaf28ddd90675e10b30c452b524",
        };

        private string GetToken(string key)
        {
            var result = "";
            var token = WebClientSyncExt.GetHtml("https://open.ys7.com/api/lapp/token/get", ""
                , key
                , "https://servicewechat.com/wxf2b3a0262975d8c2/19/page-frame.html", ExecTimeOutSeconds);

            if (!string.IsNullOrEmpty(token) && token.Contains(strTokenSpilt))
            {
                result = token.Substring(token.IndexOf(strTokenSpilt) + strTokenSpilt.Length);
                result = result.Substring(0, result.IndexOf("\""));
            }
            return result;
        }

        public override void Reset()
        {
            strToken = "";
            base.Reset();
        }
    }
}