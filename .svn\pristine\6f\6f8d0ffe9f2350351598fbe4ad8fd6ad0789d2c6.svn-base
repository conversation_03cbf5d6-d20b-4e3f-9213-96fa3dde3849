﻿namespace NewTicket
{
    public class IPDetail
    {
        private string strIP = string.Empty;
        private string strNowSpeed = "--";
        private int nTimeOut = 0;
        private bool isMobile = false;

        public int NTimeOut
        {
            get { return nTimeOut; }
            set
            {
                value = value <= 0 ? 1 : value;
                nTimeOut = value;
            }
        }

        public double DSpeed { get; set; }

        public string StrNowSpeed
        {
            get { return strNowSpeed; }
            set { strNowSpeed = value; }
        }

        public string StrIp
        {
            get { return strIP; }
            set { strIP = value; }
        }

        private string strIPXing = string.Empty;

        public string StrIpXing
        {
            get
            {
                if (string.IsNullOrEmpty(strIPXing) && strIP.HorspoolIndex(".") > 0)
                    strIPXing = strIP.Substring(0, strIP.LastIndexOf(".")) + ".*";
                return strIPXing;
            }
        }

        private string strLocation = string.Empty;

        public IPDetail()
        {
            DSpeed = 0d;
        }

        public string StrLocation
        {
            get { return strLocation; }
            set { strLocation = value; }
        }

        public override string ToString()
        {
            return string.Format("{0}${1}${2}${3}", StrIp, StrLocation, StrNowSpeed, NTimeOut);
        }
    }
}
