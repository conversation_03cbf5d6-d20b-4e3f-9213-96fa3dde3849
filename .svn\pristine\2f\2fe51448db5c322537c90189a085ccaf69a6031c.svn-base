﻿using System.Collections.Generic;
using Enterprise.Framework.Redis;
using System;

namespace CommonLib
{
    public class UserLoginCache : RedisCacheObject<UserLoginInfo>
    {
        protected override string CurrentObject_KeyPrefix
        {
            get { return "LoginCache:"; }
        }

        protected override string Object_DBName
        {
            get { return "OPS_Cache"; }
        }

        public UserLoginInfo GetUserInfo(string account)
        {
            var loginInfo = Get(account);
            if (loginInfo != null)
            {
                if (loginInfo.UserType != UserTypeEnum.体验版 && loginInfo.IsExpired)
                {
                    loginInfo.UserType = UserTypeEnum.体验版;
                    loginInfo.LstToken.RemoveAll(p => !p.IsValidate);
                    Insert(loginInfo.Account, loginInfo);
                }
            }
            return loginInfo;
        }

        public bool ValidateUser(string account, string token, ref UserTypeEnum userType)
        {
            bool result = false;
            if (!string.IsNullOrEmpty(account) && !string.IsNullOrEmpty(token))
            {
                var loginInfo = GetUserInfo(account);
                if (loginInfo != null && loginInfo.LstToken?.Count > 0)
                {
                    userType = loginInfo.UserType;
                    loginInfo.LstToken.RemoveAll(p => !p.IsValidate);
                    if (loginInfo.LstToken.Exists(p => p.Token.Equals(token)))
                    {
                        result = true;
                        loginInfo.LstToken.Find(p => p.Token.Equals(token)).DtExpired = ServerTime.DateTime.AddHours(1);
                        Insert(loginInfo.Account, loginInfo);
                    }
                }
            }
            return result;
        }

        #region 读写缓存消息队列

        /// <summary>
        /// 将消息压入队列
        /// </summary>
        /// <param name="cacheKey"></param>
        /// <param name="message"></param>
        public void EnqueueCacheMessage(string cacheKey, string message, DateTime? dtExpired)
        {
            ExecuteRedisAction(redisClient =>
            {
                redisClient.EnqueueItemOnList(cacheKey, message ?? "");
                if (dtExpired.HasValue)
                    redisClient.ExpireEntryAt(cacheKey, dtExpired.Value);
            });
        }

        /// <summary>
        /// 永远等待的取消息，直到队列中有了消息
        /// </summary>
        /// <param name="cacheKey"></param>
        /// <returns></returns>
        public string DequeueBlockingCacheMessage(string cacheKey, int endBlockSeconds = 0)
        {
            //string reallyKey = cacheKey.ToCacheQueueKey();
            return ExecuteRedisFunc(redisClient =>
            {
                return redisClient.BlockingDequeueItemFromList(cacheKey, new TimeSpan(0, 0, endBlockSeconds));
            });
        }
        #endregion
    }

    public class UserLoginInfo
    {
        public string Account { get; set; }
        public string NickName { get; set; }

        public UserTypeEnum UserType { get; set; }

        public List<TokenEntity> LstToken { get; set; }

        public string Token { get; set; }

        public DateTime DtLogin { get; set; }

        public DateTime DtLastHeat { get; set; }

        public string Remark { get; set; }

        public DateTime DtExpired { get; set; }

        public bool IsExpired { get { return DtExpired < ServerTime.DateTime; } }

        public DateTime DtReg { get; set; }
        public bool IsSetOtherResult { get; set; }
        public bool IsSupportBatch { get; set; }
        public bool IsSupportMath { get; set; }

        /// <summary>
        /// 是否支持图片文件识别
        /// </summary>
        public bool IsSupportImageFile { get; set; }

        /// <summary>
        /// 是否支持文档翻译
        /// </summary>
        public bool IsSupportDocFile { get; set; }
        public bool IsSupportTable { get; set; }
        public bool IsSupportTxt { get; set; }
        public bool IsSupportVertical { get; set; }
        public bool IsSupportTranslate { get; set; }

        /// <summary>
        /// 最大可上传的识别文件
        /// </summary>
        public int MaxUploadSize { get; set; } = 300;
    }

    public class TokenEntity
    {
        public string Token { get; set; }

        public DateTime DtExpired { get; set; }

        public bool IsValidate { get { return DtExpired > ServerTime.DateTime; } }
    }
}