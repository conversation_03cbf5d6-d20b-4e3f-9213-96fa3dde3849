﻿using CommonLib;
using System;
using System.Collections.Generic;
using System.Collections.Specialized;
using System.IO;

namespace DocOcr
{
    /// <summary>
    /// https://www.deepl.com/
    /// </summary>
    public class DeeplRec : BaseDocOcrRec
    {
        public DeeplRec()
        {
            OcrType = DocOcrType.DeepL;
            MaxExecPerTime = 20;
            AllowUploadFileTypes = new List<string>() { "docx", "pptx" };
            IsSupportTrans = true;
            InitLanguage();
        }

        #region 支持的语言

        private void InitLanguage()
        {
            TransLanguageDic = new Dictionary<TransLanguageTypeEnum, string>();
            //TransLanguageDic.Add(TransLanguageTypeEnum.自动, "auto");
            TransLanguageDic.Add(TransLanguageTypeEnum.中文, "zh");
            TransLanguageDic.Add(TransLanguageTypeEnum.英文, "en");
            TransLanguageDic.Add(TransLanguageTypeEnum.德语, "de");
            TransLanguageDic.Add(TransLanguageTypeEnum.法语, "fr");

            TransLanguageDic.Add(TransLanguageTypeEnum.西班牙语, "es");
            TransLanguageDic.Add(TransLanguageTypeEnum.葡萄牙语, "pt");
            TransLanguageDic.Add(TransLanguageTypeEnum.意大利语, "it");
            TransLanguageDic.Add(TransLanguageTypeEnum.荷兰语, "nl");
            TransLanguageDic.Add(TransLanguageTypeEnum.波兰语, "pl");
            TransLanguageDic.Add(TransLanguageTypeEnum.俄语, "ru");
            TransLanguageDic.Add(TransLanguageTypeEnum.日语, "ja");

        }

        #endregion


        protected override string GetHtml(OcrContent content)
        {
            var result = "";

            string from = "", to = "";
            InitLanguage(content, ref from, ref to);

            var byt = Convert.FromBase64String(content.strBase64);
            var key = "";
            var id = PostFileResult(byt, content.fileExt, from, to, ref key);
            if (!string.IsNullOrEmpty(id))
            {
                var strMergeId = string.Format("{0}_{1}", id, key);
                result = "{" +
                    string.Format("\"mergeId\":\"{0}\",\"id\":\"{1}\",\"key\":\"{2}\""
                    , strMergeId
                    , id
                    , key
                    ) + "}";
            }
            return result;
        }

        private string PostFileResult(byte[] content, string fileExt, string from, string to, ref string key)
        {
            var id = "";
            try
            {
                var url = string.Format("https://www.deepl.com/PHP/backend/docTrans.php?request_type=jsonrpc&il=zh&source_lang={0}&target_lang={1}", from, to);
                var file = new UploadFileInfo()
                {
                    Name = "data",
                    Filename = "1." + fileExt,
                    ContentType = ApplicationTypeHelper.GetApplicationType(fileExt),
                    Stream = new MemoryStream(content)
                };
                var headers = new NameValueCollection() {
                { "Sec-Fetch-Site","same-origin"},
                { "Sec-Fetch-Mode","cors"},
            };
                var html = PostFile(url, new[] { file
    }, null, headers);
                if (!string.IsNullOrEmpty(html))
                {
                    id = CommonHelper.SubString(html, "\"id\":\"", "\"");
                    key = CommonHelper.SubString(html, "\"key\":\"", "\"");
                }
            }
            catch (Exception)
            {

            }
            return id;
        }

        private string GetStatus(string taskId)
        {
            var result = "";
            try
            {
                string id = CommonHelper.SubString(taskId, "", "_");
                string key = CommonHelper.SubString(taskId, "_");
                var url = "https://www.deepl.com/PHP/backend/docTrans.php?request_type=jsonrpc&il=zh&t={0}";

                var strPost = "{\"jsonrpc\":\"2.0\",\"method\":\"getDocumentsStatus\",\"params\":{\"documents\":[{\"key\":\"" + key + "\",\"id\":\"" + id + "\"}]},\"id\":43410010}";

                var values = new NameValueCollection() {
                { "Sec-Fetch-Site","same-origin"},
                { "Sec-Fetch-Mode","cors"},
                { "Referer","https://fanyi.caiyunapp.com/"},
            };
                result = WebClientSyncExt.GetHtml(string.Format(url, ServerTime.DateTime.Ticks), strPost, ExecTimeOutSeconds, values);
            }
            catch (Exception)
            {

            }
            return result;
        }

        protected override ResultEntity GetProcessText(OcrContent content, string html)
        {
            //{"status":2
            //"is_preview_docx_ready":true,"is_preview_pdf_ready":true,"is_preview_uncomparison_docx_ready":true,"is_preview_uncomparison_pdf_ready":true
            //"is_full_docx_ready":true,"is_full_pdf_ready":true,"is_full_uncomparison_docx_ready":true,"is_full_uncomparison_pdf_ready":true
            //,"preview_status":2}
            //result = "1";
            var entity = new ResultEntity()
            {
                files = new List<DownLoadInfo>(),
                autoText = CommonHelper.SubString(html, "\"mergeId\":\"", "\""),
                resultType = ResutypeEnum.网页
            };

            if (!string.IsNullOrEmpty(entity.autoText))
            {
                var fileType = CommonHelper.GetFileType("doc");
                var url = string.Format("https://www.deepl.com/PHP/backend/docTrans.php?request_type=jsonrpc&il=zh&dl=1&id={0}&key={1}&name={0}.docx"
                    , CommonHelper.SubString(entity.autoText, "", "_")
                    , CommonHelper.SubString(entity.autoText, "_"));
                entity.viewUrl = OnLineViewHelper.GetViewUrl(url);
                var viewFile = new DownLoadInfo()
                {
                    fileType = fileType,
                    desc = "在线预览",
                    url = entity.viewUrl,
                };
                entity.files.Add(viewFile);
                var viewFileDown = new DownLoadInfo()
                {
                    fileType = fileType,
                    desc = "译文-" + fileType.ToString(),
                    url = url,
                };
                entity.files.Add(viewFileDown);
            }
            return entity;
        }

        public override ProcessStateEntity QueryFileStatuMethod(string taskId)
        {
            var html = GetStatus(taskId);
            var desc = CommonHelper.SubString(html, "\"error_details\":\"", "\"");
            var state = CommonHelper.SubString(html, "\"status\":\"", "\"");
            var processStatus = new ProcessStateEntity()
            {
                taskId = taskId
            };
            if (state.Contains("done"))
            {
                processStatus.state = OcrProcessState.处理成功;
                processStatus.desc = "处理完毕，可以下载了！";
            }
            else if (state.Contains("error"))
            {
                processStatus.state = OcrProcessState.处理失败;
                processStatus.desc = "处理失败，" + desc;
            }
            else if (state.Contains("translating"))
            {
                processStatus.state = OcrProcessState.处理中;
                processStatus.desc = "正在处理…";
            }
            else if (state.Contains("queued"))
            {
                processStatus.state = OcrProcessState.处理中;
                processStatus.desc = "正在排队…";
            }
            else
            {
                processStatus.state = OcrProcessState.未知状态;
                Console.WriteLine("当前状态：" + html);
            }
            return processStatus;
        }


    }
}