﻿<?xml version="1.0" encoding="utf-8"?>
<packages>
  <package id="AsyncIO" version="0.1.69" targetFramework="net45" />
  <package id="Microsoft.Bcl.AsyncInterfaces" version="5.0.0" targetFramework="net472" />
  <package id="Microsoft.CSharp" version="4.7.0" targetFramework="net472" />
  <package id="NaCl.Net" version="0.1.13" targetFramework="net472" />
  <package id="NetMQ" version="4.0.1.10" targetFramework="net472" />
  <package id="Newtonsoft.Json" version="13.0.1" targetFramework="net472" />
  <package id="PDFsharp" version="1.50.5147" targetFramework="net45" />
  <package id="ServiceStack.Interfaces" version="6.9.0" targetFramework="net472" />
  <package id="ServiceStack.Text" version="6.9.0" targetFramework="net472" />
  <package id="System.Buffers" version="4.5.1" targetFramework="net472" />
  <package id="System.Diagnostics.DiagnosticSource" version="6.0.0" targetFramework="net472" />
  <package id="System.Memory" version="4.5.5" targetFramework="net472" />
  <package id="System.Numerics.Vectors" version="4.5.0" targetFramework="net472" />
  <package id="System.Runtime.CompilerServices.Unsafe" version="6.0.0" targetFramework="net472" />
  <package id="System.Threading.Tasks.Extensions" version="4.5.4" targetFramework="net472" />
  <package id="System.ValueTuple" version="4.5.0" targetFramework="net472" />
</packages>