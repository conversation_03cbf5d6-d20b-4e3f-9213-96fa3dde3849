﻿using CommonLib;
using System;
using System.Collections.Generic;
using System.Collections.Specialized;
using System.IO;
using System.Security.Cryptography;
using System.Text;

namespace DocOcr
{
    /// <summary>
    /// 有道文档翻译
    /// https://ai.youdao.com/DOCSIRMA/html/%E6%96%87%E6%A1%A3%E7%BF%BB%E8%AF%91/API%E6%96%87%E6%A1%A3/%E6%96%87%E6%A1%A3%E7%BF%BB%E8%AF%91%E6%9C%8D%E5%8A%A1/%E6%96%87%E6%A1%A3%E7%BF%BB%E8%AF%91%E6%9C%8D%E5%8A%A1-API%E6%96%87%E6%A1%A3.html
    /// </summary>
    public class YouDaoDocAPIRec : BaseDocOcrRec
    {
        public YouDaoDocAPIRec()
        {
            OcrGroup = OcrGroupType.有道;
            OcrType = DocOcrType.有道文档API;
            MaxExecPerTime = 5;
            IsSupportTrans = true;
            AllowUploadFileTypes = new List<string>() { "jpg", "jpeg", "png", "gif", "bmp", "pdf", "doc", "docx", "ppt", "pptx" };
            //State = EnableState.禁用;
            InitLanguage();
        }

        #region 支持的语言

        //AUTO:自动
        //zh-CHS:中文
        //en:英语
        //ja:日语
        //ko:韩语
        //fr:法语
        //de:德语
        //ru:俄语
        //es:西班牙语
        //pt:葡萄牙语
        //it:意大利语
        //vi:越南语
        //id:印尼语
        //ar:阿拉伯语

        private void InitLanguage()
        {
            TransLanguageDic = new Dictionary<TransLanguageTypeEnum, string>();
            TransLanguageDic.Add(TransLanguageTypeEnum.中文, "zh-CHS");
            TransLanguageDic.Add(TransLanguageTypeEnum.英文, "en");
        }

        #endregion

        static List<YouDaoAccount> lstAppAccount = new List<YouDaoAccount>() {
        //new YouDaoAccount(){
        // strAppId = "deskdict",
        //  strSecretId =  "VPaHE3kX_vl4BhgYiu2n"
        //}
        new YouDaoAccount(){
         strAppId = "712b0ae8fd3d404d",
          strSecretId =  "TF7ORXNiC6J3V18WZ4JCVYe2chHPVnRZ"
        }
        };

        protected override string GetHtml(OcrContent content)
        {
            YouDaoAccount account = lstAppAccount.GetRndItem();

            string from = "", to = "";
            InitLanguage(content, ref from, ref to);

            var fileId = Upload(account, content.strBase64, content.fileExt, from.ToString(), to.ToString());
            if (!string.IsNullOrEmpty(fileId))
            {
                var query = "转换中";
                while (query.Equals("转换中"))
                {
                    System.Threading.Thread.Sleep(500);
                    query = Query(account, fileId);
                }
                var result = Download(account, fileId);
                return result;
            }
            return null;
        }

        private const string strFileIdSpilt = "\"flownumber\":\"";

        ///// <summary>
        ///// 
        ///// </summary>
        ///// <param name="account"></param>
        ///// <param name="strBase64"></param>
        ///// <param name="fileType">//pdf/docx</param>
        ///// <param name="from">zh-CHS/en</param>
        ///// <param name="to">zh-CHS/en</param>
        ///// <returns></returns>
        private string Upload(YouDaoAccount account, string strBase64, string fileType = "pdf", string from = "zh-CHS", string to = "en")
        {
            string appKey = account.strAppId;
            string appSecret = account.strSecretId;
            string url = "http://openapi.youdao.com/file_trans/upload";
            string salt = DateTime.Now.Millisecond.ToString();
            TimeSpan ts = DateTime.UtcNow - new DateTime(1970, 1, 1, 0, 0, 0, DateTimeKind.Utc);
            long millis = (long)ts.TotalMilliseconds;
            string curtime = Convert.ToString(millis / 1000);
            string signStr = appKey + Truncate(strBase64) + salt + curtime + appSecret; ;
            string sign = ComputeHash(signStr, new SHA256CryptoServiceProvider());
            var strPost = string.Format("q={0}&fileName={1}&fileType={2}&langFrom={3}&langTo={4}&appKey={5}&salt={6}&curtime={7}&sign={8}&docType={9}&signType={10}"
                                    , System.Web.HttpUtility.UrlEncode(strBase64)
                                    , "1." + fileType
                                    , fileType
                                    , from
                                    , to
                                    , appKey
                                    , salt
                                    , curtime
                                    , sign
                                    , "json"
                                    , "v3"
                                   );
            var result = WebClientSyncExt.GetHtml(url, strPost, ExecTimeOutSeconds);
            if (!string.IsNullOrEmpty(result) && result.Contains(strFileIdSpilt))
            {
                result = result.Substring(result.IndexOf(strFileIdSpilt) + strFileIdSpilt.Length);
                result = result.Substring(0, result.IndexOf("\""));
            }
            else
            {
                result = "";
            }
            return result;
        }

        private string Query(YouDaoAccount account, string fileId)
        {
            string appKey = account.strAppId;
            string appSecret = account.strSecretId;
            string url = "https://openapi.youdao.com/file_trans/query";
            string flownumber = fileId;
            string salt = DateTime.Now.Millisecond.ToString();
            TimeSpan ts = (DateTime.UtcNow - new DateTime(1970, 1, 1, 0, 0, 0, DateTimeKind.Utc));
            long millis = (long)ts.TotalMilliseconds;
            string curtime = Convert.ToString(millis / 1000);
            string signStr = appKey + Truncate(flownumber) + salt + curtime + appSecret; ;
            string sign = ComputeHash(signStr, new SHA256CryptoServiceProvider());

            var strPost = string.Format("flownumber={0}&appKey={1}&salt={2}&curtime={3}&sign={4}&docType={5}&signType={6}"
                                    , flownumber
                                    , appKey
                                    , salt
                                    , curtime
                                    , sign
                                    , "json"
                                    , "v3"
                                   );
            var result = WebClientSyncExt.GetHtml(url, strPost, ExecTimeOutSeconds);
            //"{\"statusString\":\"已完成\",\"errorCode\":\"0\",\"status\":4}"
            //"{\"statusString\":\"转换中\",\"errorCode\":\"0\",\"status\":2}"
            if (!string.IsNullOrEmpty(result))
            {
                result = CommonHelper.SubString(result, "\"statusString\":\"", "\"");
            }
            return result;
        }

        protected override ResultEntity GetProcessText(OcrContent content, string html)
        {
            //string.Format("\"fileId\":\"{0}\",\"fileType\":\"{1}\",\"url\":\"{2}\",\"param\":\"{3}\""
            //result = "1";
            var entity = new ResultEntity()
            {
                files = new List<DownLoadInfo>(),
                autoText = CommonHelper.SubString(html, "\"fileId\":\"", "\""),
                resultType = ResutypeEnum.网页
            };

            if (!string.IsNullOrEmpty(entity.autoText))
            {
                var fileType = CommonHelper.GetFileType(CommonHelper.SubString(html, "\"fileType\":\"", "\""));
                var file = new DownLoadInfo()
                {
                    fileType = fileType,
                    desc = "有道文档-" + fileType.ToString(),
                    url = CommonHelper.SubString(html, "\"url\":\"", "\""),
                    param = CommonHelper.SubString(html, "\"param\":\"", "\""),
                };
                entity.files.Add(file);
                //entity.downloadHtml = ConstHelper.GetDownLoadHtml(entity, OcrType.GetHashCode());
            }
            return entity;
        }

        private string Download(YouDaoAccount account, string fileId)
        {
            string appKey = account.strAppId;
            string appSecret = account.strSecretId;
            string url = "https://openapi.youdao.com/file_trans/download";
            string flownumber = fileId;
            string salt = DateTime.Now.Millisecond.ToString();
            TimeSpan ts = (DateTime.UtcNow - new DateTime(1970, 1, 1, 0, 0, 0, DateTimeKind.Utc));
            long millis = (long)ts.TotalMilliseconds;
            string curtime = Convert.ToString(millis / 1000);
            string signStr = appKey + Truncate(flownumber) + salt + curtime + appSecret;
            string sign = ComputeHash(signStr, new SHA256CryptoServiceProvider());
            var strPost = string.Format("flownumber={0}&downloadFileType={1}&appKey={2}&salt={3}&curtime={4}&sign={5}&docType={6}&signType={7}"
                                    , flownumber
                                    , "word"
                                    , appKey
                                    , salt
                                    , curtime
                                    , sign
                                    , "json"
                                    , "v3"
                                   );
            var result = "{" +
                 string.Format("\"fileId\":\"{0}\",\"fileType\":\"{1}\",\"url\":\"{2}\",\"param\":\"{3}\""
                 , fileId
                 , "doc"
                 , url
                 , strPost
                 ) + "}";
            //var result = WebClientSyncExt.GetHtml(url, strPost, ExecTimeOutSeconds);
            //File.WriteAllText("1234.doc", result);
            return result;
        }

        protected string ComputeHash(string input, HashAlgorithm algorithm)
        {
            Byte[] inputBytes = Encoding.UTF8.GetBytes(input);
            Byte[] hashedBytes = algorithm.ComputeHash(inputBytes);
            return BitConverter.ToString(hashedBytes).Replace("-", "");
        }

        protected string Truncate(string q)
        {
            if (q == null)
            {
                return null;
            }
            int len = q.Length;
            return len <= 20 ? q : (q.Substring(0, 10) + len + q.Substring(len - 10, 10));
        }

        protected string LoadAsBase64(string filename)
        {
            try
            {
                FileStream filestream = new FileStream(filename, FileMode.Open);
                byte[] arr = new byte[filestream.Length];
                filestream.Position = 0;
                filestream.Read(arr, 0, (int)filestream.Length);
                filestream.Close();
                return Convert.ToBase64String(arr);
            }
            catch (Exception ex)
            {
                return null;
            }
        }

    }


    class YouDaoAccount
    {
        public string strAppId { get; set; }

        public string strSecretId { get; set; }
    }
}