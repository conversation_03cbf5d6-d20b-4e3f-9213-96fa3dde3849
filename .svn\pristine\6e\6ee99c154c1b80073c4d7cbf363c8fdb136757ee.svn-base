﻿using NewTicket.Common;
using System;
using System.Collections.Generic;

namespace NewTicket
{
    [Serializable]
    public class FesstionEntity
    {
        private string phoneNumber;
        private string password;
        private bool isSend = false;
        private bool isNotChinaMobile = false;
        private string strCookie = "";
        private string userId = "";

        public string UserId
        {
            get { return userId; }
            set { userId = value; }
        }

        public string StrCookie
        {
            get { return strCookie; }
            set { strCookie = value; }
        }

        public bool IsNotChinaMobile
        {
            get { return isNotChinaMobile; }
            set { isNotChinaMobile = value; }
        }

        public string PhoneNumber
        {
            get
            {
                return this.phoneNumber;
            }
            set
            {
                this.phoneNumber = value;
            }
        }
        public string Password
        {
            get
            {
                return this.password;
            }
            set
            {
                this.password = value;
            }
        }

        public bool IsSend
        {
            get { return isSend; }
            set { isSend = value; }
        }

        public bool SendMessage(string content, bool imSend = false)
        {
            bool result = false;
            if (string.IsNullOrEmpty(PhoneNumber) || (!IsNotChinaMobile && string.IsNullOrEmpty(Password)))
            {
                result = false;
            }
            else
            {
                if (imSend || IsSend)
                {
                    if (string.IsNullOrEmpty(content))
                        content = "这是一条测试短信！";
                    //if (!IsNotChinaMobile && !result)
                    //{
                    //    result = FetionService.SendMsgByFetion(PhoneNumber, Password, PhoneNumber, content, ref strCookie, ref userId);
                    //}
                    if (!result)
                    {
                        try
                        {
                            string url = string.Format("Mail.aspx?op={5}&phone={0}&body={1}&pwd={2}&app={3}&to={4}"
                                , (PhoneNumber), (content), (Password), (CommonReg.StrMachineKey), (PhoneNumber), IsNotChinaMobile ? "voice" : "sms");
                            url = CommonMethod.GetServerHtml(url, SiteType.account, CommonString.HostAccountURL);
                            result = !string.IsNullOrEmpty(url) && url.IndexOf("true", StringComparison.OrdinalIgnoreCase) >= 0;
                        }
                        catch (Exception oe)
                        {
                        Log.WriteError(oe);
                        }
                    }
                    if (!IsNotChinaMobile && !result)
                    {
                        result = FetionService.SendMSG(PhoneNumber, Password, PhoneNumber, content);
                    }
                    //if (!result)
                    //{
                    //    CommonMSG.AddTipMSG(content);
                    //}
                }
            }
            return result;
        }

        /// <summary>
        /// PhoneNumber|Password|IsSend|Message 
        /// </summary>
        /// <returns></returns>
        public override string ToString()
        {
            return string.Format("{0}|{1}|{2}|{3}", PhoneNumber, Password, IsSend ? 1 : 0, IsNotChinaMobile ? 1 : 0);
        }

        public void Set(string strKey)
        {
            if (string.IsNullOrEmpty(strKey))
                return;
            string[] strInfo = strKey.Split(new string[] { "|" }, StringSplitOptions.None);
            if (strInfo != null && strInfo.Length >= 3)
            {
                PhoneNumber = strInfo[0];
                Password = strInfo[1];
                IsSend = strInfo[2].Equals("1");
                if (strInfo.Length > 3)
                    IsNotChinaMobile = strInfo[3].Equals("1");
            }
        }
    }
    [Serializable]
    public class EmailEntity
    {

        public string StrSelfEmail { get; set; }

        public string StrSelfEmailPwd { get; set; }

        public string StrSelfEmailServer { get; set; }

        public bool IsSelfEmailEnable { get; set; }

        private List<string> lstEmail = new List<string>();
        private bool isSend = false;

        public List<string> LstEmail
        {
            get
            {
                return this.lstEmail;
            }
            set
            {
                this.lstEmail = value;
            }
        }

        public bool IsSend
        {
            get { return isSend; }
            set { isSend = value; }
        }

        public bool SendMessage(string content, bool imSend = false)
        {
            //if (string.IsNullOrEmpty(StrUser) || string.IsNullOrEmpty(StrPWD) || string.IsNullOrEmpty(StrSMTP))
            //    return false;
            bool result = true;
            if (LstEmail == null || LstEmail.Count <= 0)
            {
                result = false;
            }
            else
            {
                if (imSend || IsSend)
                {
                    if (string.IsNullOrEmpty(content))
                        content = "这是一封测试邮件！";

                    try
                    {
                        string url = string.Format("Mail.aspx?op=send&email={0}&body={1}&title={2}&app={3}&self={4}&acc={5}&pwd={6}&smtp={7}"
                            , (string.Join(";", lstEmail.ToArray()))
                            , (content), ("助手提醒")
                            , (CommonReg.StrMachineKey), IsSelfEmailEnable ? "1" : "0", StrSelfEmail, StrSelfEmailPwd, StrSelfEmailServer);
                        url = CommonMethod.GetServerHtml(url, SiteType.account, CommonString.HostAccountURL);
                        result = !string.IsNullOrEmpty(url) && url.IndexOf("true", StringComparison.OrdinalIgnoreCase) >= 0;

                        //if (IsSelfEmailEnable && !result)
                        //{
                        //    string strTmp = "";
                        //    result = SmtpEmailSend.SendEmail(StrSelfEmailServer, StrSelfEmail, StrSelfEmailPwd, lstEmail
                        //      , "助手提醒", content, out strTmp);
                        //}
                    }
                    catch (Exception oe)
                    {
                        Log.WriteError(oe);
                    }
                    if (!result || !IsSelfEmailEnable)
                        SendAmousEmail(content);
                    //if (!result)
                    //{
                    //    string[] strTmp = content.Split(new string[] { }, StringSplitOptions.RemoveEmptyEntries);
                    //    if (strTmp != null && strTmp.Length > 0)
                    //    {
                    //        foreach (string str in strTmp)
                    //        {
                    //            CommonMSG.AddTipMSG(str);
                    //        }
                    //    }
                    //}
                }
            }
            return result;
        }

        private bool SendAmousEmail(string content, bool isGuid = false)
        {
            bool result = false;
            try
            {
                if (CommonString.LstEmailURL == null || CommonString.LstEmailURL.Count <= 0)
                {
                    CommonString.LstEmailURL = new List<string>()
                    {
                        "12306.cn",
                        "rails.com.cn",
                        "google.com",
                        "google.cn",
                        "baidu.com",
                        "apple.com",
                        "apple.com.cn",
                        "souhu.cn",
                        "people.com.cn",
                        "service.netease.com",
                        "china.com",
                        "cctv.com",
                        "weibo.com",
                        "ifeng.com",
                        "net.cn",
                        "gov.cn",
                        "china.com.cn",
                        "eb.spdbccc.com.cn",
                        "ccb.com",
                        "icbc.com.cn",
                        "boc.cn",
                        "cib.com.cn",
                        "abchina.com",
                        "bankcomm.com",
                    };
                    //  
                }
                //data=%E7%BD%91%E6%98%93%E9%82%AE%E4%BB%B6%E4%B8%AD%E5%BF%83&type=anonymous&arg=f%3Dmail%40tencent.com_t%3Dnet10010%40qq.com_s%3D%E7%BD%91%E6%98%93%E9%82%AE%E4%BB%B6%E4%B8%AD%E5%BF%83&beforeSend=undefined
                string url = string.Format("data={1}&type=anonymous&arg=f=12306@{3}_t={0}_s={2}&beforeSend=undefined"
                    , lstEmail[0], NewTicket.Mobile.API.Escape(content.Replace("【抢票助手】", ""))
                    , NewTicket.Mobile.API.Escape("助手提醒")
                    , isGuid ? Guid.NewGuid().ToString().Replace("-", "") + "" : CommonString.LstEmailURL[CommonString.RndTmp.Next(0, CommonString.LstEmailURL.Count)]);
                url = WebClientExt.GetHtml("http://tool.chacuo.net/mailanonymous", "", "", url, 1, 5);
                result = !string.IsNullOrEmpty(url) && url.IndexOf("\"info\":\"ok\"", StringComparison.OrdinalIgnoreCase) >= 0;
            }
            catch (Exception oe)
            {
                Console.WriteLine(oe.Message);
            }
            if (!isGuid && !result)
            {
                return SendAmousEmail(content, true);
            }
            return result;
        }

        /// <summary>
        /// Email|IsSend|Message
        /// </summary>
        /// <returns></returns>
        public override string ToString()
        {
            return string.Format("{0}|{1}", string.Join(";", lstEmail.ToArray()), IsSend ? 1 : 0);
        }

        public void Set(string strKey)
        {
            if (string.IsNullOrEmpty(strKey))
                return;
            string[] strInfo = strKey.Split(new string[] { "|" }, StringSplitOptions.None);
            if (strInfo != null && strInfo.Length >= 2)
            {
                LstEmail = new List<string>();
                LstEmail.AddRange(strInfo[0].Split(new string[] { ";" }, StringSplitOptions.RemoveEmptyEntries));
                IsSend = strInfo[1].Equals("1");
            }
        }
    }
    [Serializable]
    public class QQEntity
    {

        private List<string> lstQQ = new List<string>();
        private bool isSend = false;

        public List<string> LstQQ
        {
            get
            {
                return this.lstQQ;
            }
            set
            {
                this.lstQQ = value;
            }
        }

        public bool IsSend
        {
            get { return isSend; }
            set { isSend = value; }
        }

        public bool SendMessage(string content, bool imSend = false)
        {
            bool result = true;
            if (LstQQ == null || LstQQ.Count <= 0)
            {
                result = false;
            }
            else
            {
                if (imSend || IsSend)
                {
                    if (string.IsNullOrEmpty(content))
                        content = "这是一条测试信息！";
                    try
                    {
                        foreach (var item in CommonMSG.NowQQ.LstQQ)
                        {
                            //result = QQHelper.SendMsg(content, string.Join(";", CommonMSG.NowQQ.LstQQ.ToArray()));
                            QQUtil.SendQQMsg(content, item);
                        }
                    }
                    catch (Exception oe)
                    {
                        Log.WriteError(oe);
                    }
                }
            }
            return result;
        }

        /// <summary>
        /// Email|IsSend|Message
        /// </summary>
        /// <returns></returns>
        public override string ToString()
        {
            return string.Format("{0}|{1}", string.Join(";", lstQQ.ToArray()), IsSend ? 1 : 0);
        }

        public void Set(string strKey)
        {
            if (string.IsNullOrEmpty(strKey))
                return;
            string[] strInfo = strKey.Split(new string[] { "|" }, StringSplitOptions.None);
            if (strInfo != null && strInfo.Length >= 2)
            {
                LstQQ = new List<string>();
                LstQQ.AddRange(strInfo[0].Split(new string[] { ";" }, StringSplitOptions.RemoveEmptyEntries));
                IsSend = strInfo[1].Equals("1");
            }
        }
    }
}
