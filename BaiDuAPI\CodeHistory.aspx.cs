﻿using System;
using System.Linq;
using System.Web.UI;
using CommonLib;

namespace Code.Client.Web
{
    public partial class CodeHistory : Page
    {
        protected void Page_Load(object sender, EventArgs e)
        {
            if (BoxUtil.GetStringFromObject(Request.QueryString["pwd"]) != "123")
                return;
            LoadData();
        }

        //protected void btnOK_Click(object sender, EventArgs e)
        //{
        //    if (BoxUtil.GetStringFromObject(Request.QueryString["pwd"]) != "123")
        //        return;
        //    LoadData();
        //}

        //protected void btnClear_Click(object sender, EventArgs e)
        //{
        //    if (BoxUtil.GetStringFromObject(Request.QueryString["pwd"]) != "123")
        //        return;
        //    ClearData();
        //}

        private void LoadData()
        {
            var dataSource = RdsCacheHelper.DaMaUserCache.GetInfo();
            dataSource = dataSource.OrderByDescending(p => p.NTotalCount).ThenByDescending(p => p.NForbidTimes).ToList();
            gvDataSource.DataSource = dataSource;
            gvDataSource.DataBind();
            var lstTypes = dataSource.Select(p => p.SiteFlag).Distinct().ToList();

            lblCount.Text = string.Format("累计打码{0}次，共{1}条记录<br />", dataSource.Sum(p => p.NTotalCount), dataSource.Count);
            if (lstTypes != null && lstTypes.Count > 0)
            {
                foreach (var item in lstTypes)
                {
                    lblCount.Text += string.Format("【{0}】:{1}<br />", item,
                        dataSource.Where(p => p.SiteFlag != null && p.SiteFlag.Equals(item)).Sum(p => p.NTotalCount));
                }
            }
        }

        //{

        //private void ClearData(string strDate = "")
        //    if (BoxUtil.GetStringFromObject(Request.QueryString["pwd"]) != "123")
        //        return;
        //    ConfigHelper.DaMaCache.RemoveAllData("打码计数");
        //}
    }
}