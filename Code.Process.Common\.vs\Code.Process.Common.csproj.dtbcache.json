{"RootPath": "D:\\Code\\Code\\Code.Process.Common", "ProjectFileName": "Code.Process.Common.csproj", "Configuration": "Debug|AnyCPU", "FrameworkPath": "", "Sources": [{"SourceFile": "CommonProcess.cs"}, {"SourceFile": "Properties\\AssemblyInfo.cs"}, {"SourceFile": "ProcessNew.cs"}, {"SourceFile": "obj\\Debug\\.NETFramework,Version=v4.7.2.AssemblyAttributes.cs"}], "References": [{"Reference": "D:\\Code\\Code\\CommonLib\\bin\\Debug\\CommonLib.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": true, "ProjectPath": ""}, {"Reference": "D:\\Code\\Code\\DocOcr\\bin\\Debug\\DocOcr.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": true, "ProjectPath": ""}, {"Reference": "D:\\Code\\Code\\HanZiOcr\\bin\\Debug\\HanZiOcr.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": true, "ProjectPath": ""}, {"Reference": "D:\\Code\\Code\\DLL\\log4net.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\Code\\Code\\MathOcr\\bin\\Debug\\MathOcr.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": true, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\mscorlib.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\Code\\Code\\packages\\Newtonsoft.Json.13.0.1\\lib\\net45\\Newtonsoft.Json.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\System.Core.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\System.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\System.Web.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\Code\\Code\\TableZiOcr\\bin\\Debug\\TableOcr.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": true, "ProjectPath": ""}, {"Reference": "D:\\Code\\Code\\TransOcr\\bin\\Debug\\TransOcr.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": true, "ProjectPath": ""}], "Analyzers": [], "Outputs": [{"OutputItemFullPath": "D:\\Code\\Code\\Code.Process.Common\\bin\\Debug\\Code.Common.dll", "OutputItemRelativePath": "Code.Common.dll"}, {"OutputItemFullPath": "D:\\Code\\Code\\Code.Process.Common\\bin\\Debug\\Code.Common.pdb", "OutputItemRelativePath": "Code.Common.pdb"}], "CopyToOutputEntries": []}