﻿using System;
using System.Linq;
using System.Web.UI;
using CommonLib;

namespace Code.Manager.Web
{
    public partial class CodeHistory : Page
    {
        protected void Page_Load(object sender, EventArgs e)
        {
            if (BoxUtil.GetStringFromObject(Request.QueryString["pwd"]) != "123")
                return;
            LoadData();
        }

        private void LoadData()
        {
            var dataSource = RdsCacheHelper.DaMaHistory.GetInfo();
            dataSource = dataSource.OrderByDescending(p => p.NTotalCount).ThenByDescending(p => p.NForbidTimes).ToList();
            gvDataSource.DataSource = dataSource;
            gvDataSource.DataBind();
            var lstTypes = dataSource.Select(p => p.UserType).Distinct().ToList();

            lblCount.Text = string.Format("累计打码{0}次，共{1}条记录<br />", dataSource.Sum(p => p.NTotalCount), dataSource.Count);
            if (lstTypes != null && lstTypes.Count > 0)
            {
                foreach (var item in lstTypes)
                {
                    lblCount.Text += string.Format("【{0}】:{1}<br />", item,
                        dataSource.Where(p => p.UserType.Equals(item)).Sum(p => p.NTotalCount));
                }
            }
        }

    }
}