﻿using System;
using System.Collections.Generic;
using System.Drawing;
using System.Drawing.Imaging;
using System.IO;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using CommonLib;
using System.Diagnostics;

namespace Code.Manager.Web
{
    public static class CustomImgProcess
    {
        public static void AddImg(string strImg, string strResult, bool isLogin, string strHanZi, SiteFlag siteFlag)
        {
            try
            {
                if (!string.IsNullOrEmpty(strImg) && !string.IsNullOrEmpty(strResult))
                {
                    RdsCacheHelper.CollectImageQueue.Push(new CusImageEntity
                     {
                         IsLogin = isLogin,
                         StrImg = strImg,
                         StrCode = strResult,
                         StrIndex = RndCodeHelper.GetRndIndex(isLogin, strResult),
                         SiteFlag = siteFlag,
                         //IsResult = false,
                         StrHanZi = strHanZi
                     });
                }
            }
            catch (Exception oe)
            {
                ConfigHelper._Log.Error("【图片池】接收图片出错！", oe);
            }
        }

        private static bool IsCanProcessPic()
        {
            return true;
            //return !(ServerTime.DateTime.Minute <= 5 || ServerTime.DateTime.Minute >= 55 ||
            //               (ServerTime.DateTime.Minute >= 25 && ServerTime.DateTime.Minute <= 35));
        }

        public static void ProcessImg()
        {
            if (!ConfigHelper.IsCollectImg)
            {
                return;
            }
            ConfigHelper._Log.Info("开始收集图片…");
            long nowCount = 0;
            var perCount = 500;
            while (!ConfigHelper.IsExit)
            {
                Thread.Sleep(RandomHelper.GetRandomInt(5, 30) * 1000);
                try
                {
                    nowCount = RdsCacheHelper.CollectImageQueue.GetMessageCount();
                    if (nowCount > 0)
                    {
                        ////if (NowIndex % 50 == 0)
                        //_Log.InfoFormat("当前数据条数：{0}/{1}", NowIndex,LstImgPool.Count);
                        //lstTmp = new List<CusImageEntity>();

                        var tmps = new List<CusImageEntity>();
                        if (nowCount > perCount)
                        {
                            nowCount = perCount;
                        }
                        for (var i = 0; i < nowCount; i++)
                        {
                            var entity = RdsCacheHelper.CollectImageQueue.PopImage();
                            if (entity != null)
                            {
                                tmps.Add(entity);
                            }
                        }
                        if (tmps.Count > 0)
                        {
                            ConfigHelper._Log.InfoFormat("开始处理{0}张图片", tmps.Count);

                            Parallel.ForEach(tmps
                                , new ParallelOptions { MaxDegreeOfParallelism = tmps.Count > 50 ? 50 : tmps.Count }
                                , img =>
                                {
                                    //if (img.IsResult)
                                    //    SavePicByStr(img.IsLogin, img.StrImg, img.StrHanZi, img.StrIndex, true);
                                    //else
                                    GetHanZiByStr(img.StrImg, img.StrCode, img.IsLogin, img.StrIndex,
                                        img.SiteFlag);
                                });
                        }
                        tmps.ForEach(p =>
                        {
                            p.DisposeStrs();
                            p = null;
                        });
                        tmps = null;
                        //_Log.InfoFormat("当前数据条数：{0}/{1}", tmps.Length, LstImgPool.Count);
                        MemoryManager.ClearMemory();
                    }
                }
                catch (Exception oe)
                {
                    ConfigHelper._Log.Error("【轮询】处理图片出错！", oe);
                }
            }
            ConfigHelper._Log.Info("结束收集图片…");
        }

        private static string GetHanZiByStr(string strImg, string strResult, bool isLogin, string strIndex, SiteFlag siteFlag)
        {
            var strHanZi = "";
            try
            {
                var lstResult = RndCodeHelper.GetCodeIndexStr(strResult);
                if (string.IsNullOrEmpty(strIndex))
                    strIndex = RndCodeHelper.GetRndIndex(isLogin, strResult);

                var strHanZiPath = "";
                using (var img = CommonCompress.GetImageFromBase64(isLogin, strImg, strIndex, ref strHanZiPath))
                {
                    //if (ConfigHelper.IsProcessCollectImg)
                    //{
                    //    var strOld = "";
                    //    //strHanZi = HanZiHelper.GetHanZiByPath(strHanZiPath, ref strOld, isLogin, from);
                    //    strHanZi = HanZiHelper.GetHanZiByImg(img, ref strOld, isLogin, siteFlag, false);
                    //    ConfigHelper._Log.InfoFormat("汉字：{0}，结果：{1}", strHanZi, string.Join(",", lstResult));
                    //    //RegHanZi.TestRegNew(strOld, strHanZi, isLogin);
                    //    if (!string.IsNullOrEmpty(strHanZi) && (isLogin || ConfigHelper.IsRecOrder)
                    //        && lstResult != null && lstResult.Count > 0)
                    //    {
                    //        SaveImage(img, strIndex, lstResult, strHanZi, strOld, isLogin);
                    //    }
                    //    else
                    //    {
                    //        try
                    //        {
                    //            if (File.Exists(ConfigHelper.StrImagePath + strHanZiPath))
                    //                File.Delete(ConfigHelper.StrImagePath + strHanZiPath);
                    //        }
                    //        catch (Exception oe)
                    //        {
                    //            ConfigHelper._Log.Error("删除垃圾图片出错！Path：" + strHanZiPath, oe);
                    //        }
                    //    }
                    //}
                    if (img != null)
                        img.Dispose();
                }
                lstResult = null;
                strHanZiPath = null;
            }
            catch (Exception oe)
            {
                ConfigHelper._Log.Error("【处理】处理图片报错！", oe);
            }
            finally
            {
                strResult = null;
                strImg = null;
                strIndex = null;
            }
            return strHanZi;
        }

        //public static string GetHanZiByImg(Bitmap img, string strResult, bool isLogin, string strIndex, SiteFlag siteFlag, bool isFixed = true)
        //{
        //    var strHanZi = "";
        //    try
        //    {
        //        var lstResult = ImageHelper.GetCodeIndexStr(strResult);
        //        if (string.IsNullOrEmpty(strIndex))
        //            strIndex = RndCodeHelper.GetRndIndex(isLogin, strResult);

        //        //var strHanZiPath = "";
        //        //using (Bitmap img = GetImageFromBase64(isLogin, strImg, strIndex, ref strHanZiPath))
        //        {
        //            var strOld = "";
        //            strHanZi = HanZiHelper.GetHanZiByImg(img, ref strOld, isLogin, siteFlag, isFixed);
        //            //strHanZi = BaiDuCode.GetHanZiByPath(strHanZiPath, ref strOld, isLogin, from);
        //            //RegHanZi.TestRegNew(strOld, strHanZi, isLogin);
        //            if (!string.IsNullOrEmpty(strHanZi) && (isLogin || ConfigHelper.IsRecOrder)
        //                && lstResult != null && lstResult.Count > 0)
        //            {
        //                SaveImage(img, strIndex, lstResult, strHanZi, strOld, isLogin);
        //            }
        //            //else
        //            //{
        //            //    try
        //            //    {
        //            //        if (File.Exists(ConfigHelper.StrImagePath + strHanZiPath))
        //            //            File.Delete(ConfigHelper.StrImagePath + strHanZiPath);
        //            //    }
        //            //    catch (Exception oe)
        //            //    {
        //            //        ConfigHelper._Log.Error("删除垃圾图片出错！Path：" + strHanZiPath, oe);
        //            //    }
        //            //}
        //            if (img != null)
        //                img.Dispose();
        //        }
        //    }
        //    catch (Exception oe)
        //    {
        //        ConfigHelper._Log.Error("【处理】处理图片报错！", oe);
        //    }
        //    finally
        //    {
        //        //strImg = null;
        //        strIndex = null;
        //    }
        //    return strHanZi;
        //}


        //public static void SaveImage(Bitmap img, string strIndex, List<int> lstResult, string strHanZi, string strOld,
        //    bool isLogin = true)
        //{
        //    var imgType = ImageType.Eight; // ImageHelper.GetImgType(img);
        //    //if (imgType == ImageType.Eight)
        //    {
        //        try
        //        {
        //            var lstImg = new List<Bitmap>();
        //            var lstEntity = ImageHelper.SpiltImage(img, imgType, ref lstImg, lstResult);

        //            var lstRecName = new Dictionary<int, List<string>>();

        //            strHanZi = GetNameByImage(strHanZi, strOld, lstEntity, ref lstRecName, isLogin);

        //            lstEntity.ForEach(p => { p.StrId = null; p = null; });
        //            lstEntity = null;

        //            //if (lstRecName != null && lstRecName.Count > 0)
        //            //    _Log.InfoFormat("【识图结果】{4}{0}=>{1}{4}{2}=>{3}"
        //            //        , strOld, strHanZi, newStr, string.Join("|", lstRecName.ToArray()), Environment.NewLine);

        //            for (var i = 0; i < lstImg.Count; i++)
        //            {
        //                var name = strHanZi;
        //                if (lstRecName.Count == lstImg.Count)
        //                {
        //                    //if (!isLogin)
        //                    //{
        //                    if (lstRecName[i].Count <= 0)
        //                    {
        //                        var tmpName = GetRndName(lstRecName, strHanZi);
        //                        if (!string.IsNullOrEmpty(tmpName))
        //                            lstRecName[i].Add(tmpName);
        //                    }
        //                    if (lstRecName[i].Count > 0)
        //                    {
        //                        //}
        //                        if (lstRecName[i].Count == 1)
        //                        {
        //                            name = lstRecName[i][0];
        //                            //if (BaiDuCode.IsShowDaMa)
        //                            //    ConfigHelper._Log.InfoFormat("【识图】{0}=>{1}", strHanZi, name);
        //                        }
        //                        else
        //                        {
        //                            name = lstRecName[i][RandomHelper.GetRandomInt(0, lstRecName[i].Count)];
        //                            //if (BaiDuCode.IsShowDaMa)
        //                            //    ConfigHelper._Log.InfoFormat("【识图-Random】{0}=>{1}", strHanZi, name);
        //                        }
        //                    }
        //                }
        //                if (string.IsNullOrEmpty(name) || !IsCanSave(name, lstImg[i]))
        //                    continue;
        //                var path = ConfigHelper.StrImagePath + ServerTime.DateTime.ToString("MMdd") + "\\图库\\" + name + "\\";
        //                if (!Directory.Exists(path))
        //                {
        //                    Directory.CreateDirectory(path);
        //                }
        //                var pFile = "";
        //                //if (lstResult.Contains(i))
        //                //{
        //                //    pFile = path + strIndex + "_" + strOld + "_" + lstResult[i] + ".jpg";
        //                //}
        //                if (lstResult.Count > i)
        //                {
        //                    pFile = path + strIndex + "_" + strOld + "_" + lstResult[i] + ".jpg";
        //                }
        //                else
        //                {
        //                    pFile = path + strIndex + "_" + strOld + "_" + (i + 1) + ".jpg";
        //                }

        //                lstImg[i].Save(pFile, ImageFormat.Jpeg);
        //            }
        //            lstImg.ForEach(p =>
        //            {
        //                p.Dispose();
        //                p = null;
        //            });
        //            lstImg = null;
        //            lstRecName = null;
        //        }
        //        catch (Exception oe)
        //        {
        //            ConfigHelper._Log.Error("SaveImage出错！", oe);
        //        }
        //        finally
        //        {
        //            lstResult = null;
        //            strHanZi = null;
        //        }
        //    }
        //}

        ///// <summary>
        /////     以图找字
        ///// </summary>
        ///// <param name="strOld">百度原始结果</param>
        ///// <param name="strFirstRes">初步识别结果</param>
        ///// <param name="lstImg"></param>
        ///// <param name="lstRecName"></param>
        ///// <param name="isLogin"></param>
        ///// <returns></returns>
        //private static string GetNameByImage(string strFirstRes, string strOld, List<ImageEntity> lstImg
        //    , ref Dictionary<int, List<string>> lstRecName, bool isLogin = true)
        //{
        //    var result = "";
        //    try
        //    {
        //        if (lstImg != null && lstImg.Count > 0 && !string.IsNullOrEmpty(strFirstRes)
        //            && !CommonHanZi.lstHanZi.Contains(strOld) && !CommonHanZi.lstHanZiReplace.ContainsKey(strOld))
        //        {
        //            //var strLast = GetTmpRecHanZi(strOld);

        //            //Stopwatch stop = Stopwatch.StartNew();

        //            var lstHanZi = strFirstRes.Split(new[] { "、" }, StringSplitOptions.RemoveEmptyEntries);
        //            var lstNew = ImageHelper.GetNameByImage(lstImg, lstHanZi.ToList(), ref lstRecName, isLogin);

        //            //if (lstNew.Exists(p => lstHanZi.Contains(p)))
        //            //{
        //            //    lstNew = lstNew.FindAll(p => lstHanZi.Contains(p));
        //            //}
        //            if (lstNew != null && lstNew.Count > 0)
        //            {
        //                result = string.Join("、", lstNew.ToArray());

        //                if (!string.IsNullOrEmpty(result))
        //                {
        //                    if (!isLogin)
        //                    {
        //                        //if (BaiDuCode.IsShowDaMa)
        //                        //    ConfigHelper._Log.InfoFormat("【下单】{0}=>{1}=>{2}"
        //                        //        , strOld, strFirstRes, result);
        //                    }
        //                    else
        //                    {
        //                        if (lstNew.Count == 1 && !result.Contains(strOld) &&
        //                            !result.Contains(strFirstRes))
        //                        {
        //                            AddTmpRecHanZi(strOld, strFirstRes, result, true);
        //                            ConfigHelper._Log.InfoFormat("【自动修正】{0}=>{1}=>{2}"
        //                                , strOld, strFirstRes, result);
        //                        }
        //                    }
        //                }
        //            }
        //        }
        //    }
        //    catch (Exception oe)
        //    {
        //        ConfigHelper._Log.Error("【图-》字】转换出错！", oe);
        //    }
        //    if (string.IsNullOrEmpty(result))
        //        result = strFirstRes;
        //    return result;
        //}

        ///// <summary>
        /////     替换规则
        ///// </summary>
        ///// <param name="strOld">百度原始结果</param>
        ///// <param name="strFirst">初步识别结果</param>
        ///// <param name="result">以图匹配结果</param>
        ///// <param name="isAuto">是否自动修正</param>
        //public static void AddTmpRecHanZi(string strOld, string strFirst, string result, bool isAuto = false)
        //{
        //    try
        //    {
        //        var strExits = "";
        //        if (!string.IsNullOrEmpty(strOld) && !string.IsNullOrEmpty(result) && !strOld.Contains(result))
        //        {
        //            //strOld:猪青蛙
        //            //StrFis:青蛙、螃蟹、胶卷、猫
        //            //Result:猫
        //            if (result.Contains(strOld) ||
        //                CommonHanZi.lstHanZi.Exists(p => p.Contains(strOld) || (p.Length > 1 && strOld.Contains(p))))
        //            {
        //                return;
        //            }

        //            //if (strOld.Length > 1)
        //            //{
        //            //    string strStart = strOld[strOld.Length - 2].ToString();
        //            //    string strEnd = strOld[strOld.Length - 1].ToString();

        //            //    if (BaiDuCode.lstHanZi.Exists(p => p.StartsWith(strEnd)) && BaiDuCode.lstHanZi.Exists(p => p.EndsWith(strStart)))
        //            //        return;
        //            //}

        //            if (strOld.Length == 1)
        //            {
        //                if (isAuto || CommonHanZi.lstDanHanZi.Contains(strOld[0]))
        //                    return;
        //            }
        //            if (strOld.Length > 4 || Math.Abs(result.Length - strOld.Length) >= 2)
        //            {
        //                ConfigHelper._Log.InfoFormat("【忽略】：{0}=>{1}=>{2}", strOld, strFirst, result);
        //                return;
        //            }
        //            var isFixed = false;
        //            //if (!isAuto)
        //            //{
        //            if (CommonHanZi.lstHanZiReplace.ContainsKey(strOld))
        //            {
        //                strExits = CommonHanZi.lstHanZiReplace[strOld];
        //                if (!string.IsNullOrEmpty(strExits))
        //                {
        //                    if (!strExits.Contains(result) && !result.Contains(strExits))
        //                    {
        //                        CommonHanZi.lstHanZiReplace[strOld] = strExits + result;
        //                        isFixed = true;
        //                    }
        //                }
        //                else
        //                {
        //                    CommonHanZi.lstHanZiReplace[strOld] = result;
        //                    isFixed = true;
        //                }
        //            }
        //            else
        //            {
        //                CommonHanZi.lstHanZiReplace.Add(strOld, result);
        //                isFixed = true;
        //                var tmpReplace = CommonHanZi.GetListByNameLength(CommonHanZi.lstHanZiReplace);
        //                if (tmpReplace != null && tmpReplace.Count > 0)
        //                {
        //                    CommonHanZi.lstHanZiReplace = tmpReplace;
        //                }
        //            }
        //            if (isFixed)
        //                ConfigHelper._Log.InfoFormat("【{4}-Fix汉字】  内容：{0}=>{1} {2}=>{3}", strOld, strFirst, strExits,
        //                    strExits + result, isAuto ? "Auto" : "User");
        //            //}
        //            //else
        //            //{
        //            //    if (BaiDuCode.lstTmpHanZiReplace.ContainsKey(strName))
        //            //    {
        //            //        strExits = BaiDuCode.lstTmpHanZiReplace[strName];
        //            //        if (!string.IsNullOrEmpty(strExits)
        //            //            && !strExits.Contains(result) && !result.Contains(strExits))
        //            //        {
        //            //            _Log.InfoFormat("【Auto】  内容：{0}=>{1} {2}=>{3}", strName, strFirst, strExits, strExits + result);
        //            //            BaiDuCode.lstTmpHanZiReplace[strName] = strExits + result;
        //            //        }
        //            //    }
        //            //    else
        //            //    {
        //            //        BaiDuCode.lstTmpHanZiReplace.Add(strName, result);
        //            //    }
        //            //}
        //        }
        //    }
        //    catch (Exception oe)
        //    {
        //        ConfigHelper._Log.Error("【图-》字】添加结果出错！", oe);
        //    }
        //}

        private static string GetRndName(Dictionary<int, List<string>> dicOld, string strHanZi)
        {
            var result = "";
            var lstTmp = new List<string>();
            var lstHanZi = new List<string>();
            lstHanZi.AddRange(strHanZi.Split(new[] { "、" }, StringSplitOptions.RemoveEmptyEntries));
            foreach (var item in dicOld)
            {
                lstTmp.AddRange(item.Value);
            }
            lstTmp = lstTmp.Distinct().OrderBy(p => p).ToList();
            lstHanZi = lstHanZi.Distinct().OrderBy(p => p).ToList();
            if (lstHanZi.Exists(p => !lstTmp.Contains(p)))
            {
                result = lstHanZi.Find(p => !lstTmp.Contains(p));
            }
            if (string.IsNullOrEmpty(result))
            {
                if (lstHanZi.Count > 0)
                    result = lstHanZi[new Random().Next(0, lstHanZi.Count - 1)];
            }
            if (string.IsNullOrEmpty(result))
            {
                if (lstTmp.Count > 0)
                    result = lstTmp[new Random().Next(0, lstTmp.Count - 1)];
            }
            return result;
        }

        //private static bool IsCanSave(string strName, Bitmap img)
        //{
        //    var result = true;
        //    try
        //    {
        //        var entity = ImageHelper.GetHash(img);
        //        if (ImageHelper.LstCodes == null)
        //        {
        //            ImageHelper.LstCodes = new Dictionary<string, List<ImageEntity>>();
        //        }
        //        if (ImageHelper.LstCodes.ContainsKey(strName)
        //            && ImageHelper.LstCodes[strName].Exists(
        //                p => ImageHelper.IsSameByEntity(entity, p, ImageHelper.GetMaxColorDifByName(strName))))
        //        {
        //            result = false;
        //        }
        //        if (ImageHelper.LstTmpImg == null)
        //        {
        //            ImageHelper.LstTmpImg = new Dictionary<string, List<ImageEntity>>();
        //        }
        //        if (result && ImageHelper.LstTmpImg.ContainsKey(strName)
        //            && ImageHelper.LstTmpImg[strName].Exists(
        //                p => ImageHelper.IsSameByEntity(entity, p, ImageHelper.GetMaxColorDifByName(strName))))
        //        {
        //            result = false;
        //        }
        //        if (result)
        //        {
        //            if (!ImageHelper.LstTmpImg.ContainsKey(strName))
        //                ImageHelper.LstTmpImg.Add(strName, new List<ImageEntity> { entity });
        //            else
        //                ImageHelper.LstTmpImg[strName].Add(entity);
        //        }
        //    }
        //    catch (Exception oe)
        //    {
        //        ConfigHelper._Log.Error("检查图片能否保存出错！Local：" + oe.TargetSite + "\n堆栈：" + oe.StackTrace, oe);
        //    }
        //    return result;
        //}
    }
}