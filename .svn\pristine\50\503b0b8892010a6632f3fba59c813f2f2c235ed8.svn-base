﻿
using CommonLib;

namespace MathOcr
{
    /// <summary>
    /// https://cloud.baidu.com/doc/OCR/OCR-Android-SDK/18.5COCR-UI.E6.A8.A1.E5.9D.97.html#.E9.80.9A.E7.94.A8.E6.96.87.E5.AD.97.E8.AF.86.E5.88.AB
    /// 根据返回值查询题目和结果
    /// https://ai.xueersi.com/photoSeach/
    /// https://docai.xueersi.com/books/ai%E6%95%99%E8%82%B2%E5%BC%80%E6%94%BE%E5%B9%B3%E5%8F%B0/page/ocr%E6%8B%8D%E7%85%A7%E6%90%9C%E9%A2%98
    /// https://apiai.xueersi.com/v1/home/<USER>
    /// </summary>
    public class XueErSiRec : BaseMathRec
    {
        public XueErSiRec()
        {
            OcrGroup = OcrGroupType.学而思;
            OcrType = MathOcrType.学而思API;
            MaxExecPerTime = 10;

            LstJsonPreProcessArray = new System.Collections.Generic.List<object>() { "data", "recognition" };
            StrResultJsonSpilt = "formulaData";
        }


        protected override string GetHtml(OcrContent content)
        {
            content.strBase64 = System.Web.HttpUtility.UrlEncode(content.strBase64);
            return RequestHtmlContent(content.strBase64);
        }

        public override string GetHtmlByUrl(OcrContent content)
        {
            return RequestHtmlContent(null, content.url);
        }

        private string RequestHtmlContent(string strBase64, string url = null)
        {
            var strPost = "app_key=43cf4e00da73e71c1704e2cdfdba4a95cbfa694a&img=" + (string.IsNullOrEmpty(url) ? strBase64 : url) + "&img_type=" + (string.IsNullOrEmpty(url) ? "base64" : "url");

            var strTmp = WebClientSyncExt.GetHtml("https://openapiai.xueersi.com/v1/api/img/ocr/formula", "", strPost, "", ExecTimeOutSeconds);

            if (strTmp.StartsWith("<html") || strTmp.StartsWith("<!DOCTYPE"))
            {
                strTmp = "";
            }

            return strTmp;
        }
    }
}