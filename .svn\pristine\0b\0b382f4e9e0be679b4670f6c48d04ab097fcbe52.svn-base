﻿using CommonLib;
using System.Collections.Generic;
using System.Web;

namespace TableOcr
{
    /// <summary>
    /// https://cloud.baidu.com/doc/OCR/s/zjwvxzrw8/
    /// </summary>
    public class BaiDuAPIRec : BaseTableRec
    {
        public BaiDuAPIRec()
        {
            OcrGroup = OcrGroupType.百度;
            OcrType = TableOcrType.百度云;
            MaxExecPerTime = 30;

            LstJsonPreProcessArray = new List<object>() { "result", "result_data|S", "forms", 0, "header|&|body|&|footer" };
            LstJsonResultProcessArray = new List<object>() { "word" };
            LstRowIndex = new List<object>() { "row" };
            LstColumnIndex = new List<object>() { "column" };
            RowIndexIsArray = true;

            RegAccount();
            //IsSupportUrlOcr = true;
        }

        private AccountDto account;

        private void RegAccount()
        {
            var lstAccount = new List<AccountDto>()
            {
                new AccountDto
                {
                    clientId ="pe741pUxOAG1av01zk6hM5rK",
                    secretKey ="dRcL3OSPNstW0fxvChMlmpv8cabkWzqD",
                },
                new AccountDto
                {
                    clientId ="Cyb7moQatBXD42IFzE32fXI9",
                    secretKey ="nd60Zxt6qkDObEUvwf0r5CUtfltcLh27",
                },
                new AccountDto
                {
                    clientId ="5hM3gBr5a1M9YFBmwUZBwDTZ",
                    secretKey ="8CBMBalNp0CeuU2POttakt7HESKb0axs",
                },
                new AccountDto
                {
                    clientId ="iDeBwPb0lRxtRDSgnfA1HSlp",
                    secretKey ="xLUp2dmfdFoXhKR5aQY7CZVfpZyQ2xq2",
                },
                new AccountDto
                {
                    clientId ="GRhO7ZFGPuqVDGDOXcunDgmv",
                    secretKey ="Xlx27V21hGtuNOiozHyK6C6KVyzUFPAT",
                },
                new AccountDto
                {
                    clientId ="YsZKG1wha34PlDOPYaIrIIKO",
                    secretKey ="HPRZtdOHrdnnETVsZM2Nx7vbDkMfxrkD",
                },
            };
            AccountHelper.RegAccount(OcrType.GetHashCode(), lstAccount);
        }

        private const string strTokenSpilt = "\"access_token\":\"";

        private string strToken = "";

        private void InitToken()
        {
            if (account == null)
            {
                account = AccountHelper.GetAccount(OcrType.GetHashCode());
            }
            if (account != null && string.IsNullOrEmpty(strToken))
            {
                strToken = GetToken();
                if (string.IsNullOrEmpty(strToken) && account == null)
                {
                    InitToken();
                }
            }
        }
        private string GetToken()
        {
            var result = "";
            var token = WebClientSyncExt.GetHtml(string.Format("{0}?{1}"
                , "https://aip.baidubce.com/oauth/2.0/token"
                , "grant_type=client_credentials&client_id=" + account.clientId + "&client_secret=" + account.secretKey), ExecTimeOutSeconds);

            if (!string.IsNullOrEmpty(token))
            {
                if (token.Contains(strTokenSpilt))
                {
                    result = token.Substring(token.IndexOf(strTokenSpilt) + strTokenSpilt.Length);
                    result = result.Substring(0, result.IndexOf("\""));
                }
                else
                {
                    LogHelper.Log.Info(string.Format("账号暂停，明细：{0}", token));
                    AccountHelper.ForbidAccount(OcrType.GetHashCode(), account.clientId, account.secretKey);
                    account = null;
                }
            }
            return result;
        }

        public override void Reset()
        {
            strToken = "";
            base.Reset();
        }

        protected override string GetHtml(OcrContent content)
        {
            return RequestHtmlContent(content.strBase64);
        }

        public override string GetHtmlByUrl(OcrContent content)
        {
            return RequestHtmlContent(null, content.url);
        }

        private string RequestHtmlContent(string strBase64, string imgUrl = null)
        {
            InitToken();
            var url = "https://aip.baidubce.com/rest/2.0/solution/v1/form_ocr/request";
            var strPost = string.Format("image={0}&is_sync=true&request_type=json&table_border=none"
                , HttpUtility.UrlEncode(strBase64));
            var strTmp = WebClientSyncExt.GetHtml(url + "?access_token=" + strToken, strPost, ExecTimeOutSeconds);

            //Console.WriteLine("百度Result:" + strTmp);
            if (!string.IsNullOrEmpty(strTmp))
            {
                if (strTmp.Contains("\"error_code\":110") || strTmp.Contains("\"error_code\":17"))
                {
                    strToken = "";

                    LogHelper.Log.Info(string.Format("账号暂停，明细：{0}", strTmp));
                    AccountHelper.ForbidAccount(OcrType.GetHashCode(), account.clientId, account.secretKey);
                    account = null;
                    strTmp = "";
                }
            }
            return strTmp;
        }

        //private Dictionary<int, Dictionary<int, string>> GetContentFromJson(string str)
        //{
        //    var dicContent = new Dictionary<int, Dictionary<int, string>>();
        //    var bodys = JObject.Parse(JObject.Parse(str)["result"]["result_data"].ToString())["forms"][0];

        //    var header = JArray.Parse(bodys["header"].ToString());
        //    //var body = JArray.Parse(bodys["body"].ToString());
        //    //var footer = JArray.Parse(bodys["footer"].ToString());
        //    //var dicHeader = GetContentFromArray(header);
        //    //var dicBody = GetContentFromArray(body);
        //    //var dicFooter = GetContentFromArray(footer);
        //    //MergeContent(dicContent, dicHeader);
        //    //MergeContent(dicContent, dicBody);
        //    //MergeContent(dicContent, dicFooter);
        //    var bodyNew = OcrResultProcess.GetPreResultByList(str, LstJsonPreProcessArray);
        //    return dicContent;
        //}

        //private static void MergeContent(Dictionary<int, Dictionary<int, string>> dicContent, Dictionary<int, Dictionary<int, string>> dicHeader)
        //{
        //    var oldCount = dicContent.Count;
        //    if (dicHeader?.Count > 0)
        //    {
        //        foreach (var item in dicHeader)
        //        {
        //            dicContent.Add(item.Key + oldCount, item.Value);
        //        }
        //    }
        //}

        //private Dictionary<int, Dictionary<int, string>> GetContentFromArray(JArray body)
        //{
        //    var dicContent = new Dictionary<int, Dictionary<int, string>>();
        //    var maxColumn = 1;
        //    var maxRow = 1;
        //    for (var i = 0; i < body.Count; i++)
        //    {
        //        try
        //        {
        //            var jObject = JObject.Parse(body[i].ToString());

        //            var rowStr = jObject["row"].ToString().Trim();
        //            JArray rows = JArray.Parse(rowStr);
        //            rowStr = rows[0].ToString();
        //            foreach (var item in rows)
        //            {
        //                var rowIndex = Convert.ToInt32(item.ToString());
        //                maxRow = Math.Max(rowIndex, maxRow);
        //                if (!dicContent.ContainsKey(rowIndex))
        //                {
        //                    dicContent.Add(rowIndex, new Dictionary<int, string>());
        //                }
        //            }

        //            var columnStr = jObject["column"].ToString().Trim();
        //            JArray columns = JArray.Parse(columnStr);
        //            columnStr = columns[0].ToString();
        //            foreach (var item in columns)
        //            {
        //                var columnIndex = Convert.ToInt32(item.ToString());
        //                maxColumn = Math.Max(columnIndex, maxColumn);
        //            }

        //            var text = jObject["word"].ToString().Trim();
        //            dicContent[Convert.ToInt32(rowStr)][Convert.ToInt32(columnStr)] = text;
        //        }
        //        catch (Exception oe)
        //        {
        //            Console.WriteLine(oe.Message);
        //        }
        //    }
        //    OcrResultProcess.RemoveEmptyRow(dicContent);
        //    OcrResultProcess.AddEmptyColumn(dicContent, maxColumn);
        //    return dicContent;
        //}
    }
}