﻿using System;
using System.Text;

namespace CommonLib
{
    public class CusImageEntity : IDisposable
    {
        public string Account { get; set; }
        public string Token { get; set; }

        public ProcessState State { get; set; }

        //public string ResultFrom { get; set; }

        //public string Error { get; set; }

        public string StrImg { get; set; }

        public string ImgUrl { get; set; }

        /// <summary>
        /// 是否支持URL识别
        /// </summary>
        public bool IsSupportUrl { get; set; }

        //public string StrCode { get; set; }

        public string StrIndex { get; set; }

        //public bool IsResult { get; set; }

        //public string StrHanZi { get; set; }

        public UserTypeEnum UserType { get; set; }

        public OcrType OcrType { get; set; }

        public OcrGroupType OcrGroup { get; set; }

        public bool isLstOcrGroup { get; set; }

        public int? ProcessId { get; set; }

        /// <summary>
        /// 是否从左到右
        /// </summary>
        public bool IsFromLeftToRight { get; set; } = true;

        /// <summary>
        /// 是否从上到下
        /// </summary>
        public bool IsFromTopToDown { get; set; } = true;

        /// <summary>
        /// 服务端接收到用户请求
        /// </summary>
        public long DtAdd { get; set; }

        /// <summary>
        /// 用户端的请求时间
        /// </summary>
        public long DtUser { get; set; }

        /// <summary>
        /// OCR服务端接收到用户请求
        /// </summary>
        public long DtReceived { get; set; }

        /// <summary>
        /// 过期时间
        /// </summary>
        public long DtExpired { get; set; }

        /// <summary>
        /// 服务端分配完成时间
        /// </summary>
        public long DtOcrServerGet { get; set; }

        public bool IsValidate
        {
            get { return DtExpired >= ServerTime.DateTime.Ticks; }
        }

        private string fileExt;
        public string FileExt
        {
            get
            {
                if (string.IsNullOrEmpty(fileExt))
                {
                    fileExt = "png";
                }
                return fileExt;
            }
            set
            {
                fileExt = value;
            }
        }

        public int FileContentLength { get; set; }
        public TransLanguageTypeEnum FromLanguage { get; set; }
        public TransLanguageTypeEnum ToLanguage { get; set; }

        /// <summary>
        /// 是否为竖排识别
        /// </summary>
        public bool? IsSupportVertical { get; set; }

        /// <summary>
        /// 有限度的全角转半角（英文、数字、空格以及某些特殊字符等使用半角字符）
        /// </summary>
        public bool IsAutoFull2Half { get; set; } = true;

        /// <summary>
        /// 在中文与英文字母/用于数学、科学和工程的希腊字母/数字之间添加空格
        /// </summary>
        public bool IsAutoSpace { get; set; } = true;

        /// <summary>
        /// 根据语言类型，把文字中的标点符号转换为中/英文标点
        /// </summary>
        public bool IsAutoSymbol { get; set; } = true;

        /// <summary>
        /// 重复标点校正
        /// </summary>
        public bool IsAutoDuplicateSymbol { get; set; } = true;

        /// <summary>
        /// 是否文本转表格
        /// </summary>
        public bool IsTextToTable { get; set; }

        /// <summary>
        /// 是否文本转翻译
        /// </summary>
        public bool IsTextToTrans { get; set; }

        public long ClientTicks { get; set; }

        public int RetryTimes { get; set; }
        public long StartBase64Tick { get; set; }
        public long EndBase64Tick { get; set; }

        public void Dispose()
        {
            GC.SuppressFinalize(this);
        }

        public string ToStr()
        {
            return string.Format("【{4}】_{0}_总：{1}ms，消息：{2}ms，处理：{3}ms，ID:{5}"
                    , State.ToString()
                    , new TimeSpan(ServerTime.DateTime.Ticks - DtAdd).TotalMilliseconds.ToString("F0")
                    , new TimeSpan(DtReceived - DtAdd).TotalMilliseconds.ToString("F0")
                    //, (processEntity.DtStartProcess - processEntity.DtReceived).TotalMilliseconds.ToString("F0")
                    , new TimeSpan(ServerTime.DateTime.Ticks - DtReceived).TotalMilliseconds.ToString("F0")
                    , UserType
                    , StrIndex);
        }
    }

    public class OcrTimeEntity
    {
        /// <summary>
        /// 用户开始请求本地时间
        /// </summary>
        public long UserStartRequest { get; set; }

        /// <summary>
        /// Server收到请求时间
        /// </summary>
        public long ServerReceivedUserRequest { get; set; }

        /// <summary>
        /// Server分配成功时间
        /// </summary>
        public long ServerAlloted { get; set; }

        /// <summary>
        /// Server收到结果时间
        /// </summary>
        public long ServerReceivedOcrResult { get; set; }

        /// <summary>
        /// Server端返回用户结果时间
        /// </summary>
        public long ServerGiveUserResult { get; set; }

        /// <summary>
        /// OCR服务收到请求时间
        /// </summary>
        public long OcrServerAccepted { get; set; }

        /// <summary>
        /// OCR服务反馈开始处理时间
        /// </summary>
        public long OcrServerReported { get; set; }

        /// <summary>
        /// OCR服务开始处理时间
        /// </summary>
        public long OcrServerBegin { get; set; }

        /// <summary>
        /// OCR服务处理完成
        /// </summary>
        public long OcrServerEnd { get; set; }

        /// <summary>
        /// OCR服务反馈结果时间
        /// </summary>
        public long OcrServerReportedResult { get; set; }

        /// <summary>
        /// OCR服务开始处理并行前的时间
        /// </summary>
        public long OcrServerGetCode { get; set; }

        /// <summary>
        /// OCR服务单个处理线程开始处理的时间
        /// </summary>
        public long OcrServerProcessOne { get; set; }

        /// <summary>
        /// 获取Html完毕
        /// </summary>
        public long OcrServerGetHtmlCompleted { get; set; }

        /// <summary>
        /// 解析结果完毕
        /// </summary>
        public long OcrServerResolveCompleted { get; set; }

        /// <summary>
        /// 下载Base64图片
        /// </summary>
        public long? OcrServerGetBase64Completed { get; set; }

        public string Report()
        {
            StringBuilder sbTime = new StringBuilder();
            sbTime.AppendLine(string.Format("本次请求总耗时：{0}ms", new TimeSpan(ServerTime.DateTime.Ticks - UserStartRequest).TotalMilliseconds.ToString("F0")));
            sbTime.AppendLine(string.Format("客户端开始请求：{0}", new DateTime(UserStartRequest).ToString("HH:mm:ss fff")));
            sbTime.AppendLine(string.Format("服务端收到请求：{0}，已耗时:{1}ms", new DateTime(ServerReceivedUserRequest).ToString("HH:mm:ss fff"), new TimeSpan(Math.Abs(UserStartRequest - ServerReceivedUserRequest)).TotalMilliseconds.ToString("F0")));
            sbTime.AppendLine(string.Format("OCR抢单成功：{0}，已耗时:{1}ms", new DateTime(ServerAlloted).ToString("HH:mm:ss fff"), new TimeSpan(Math.Abs(ServerAlloted - ServerReceivedUserRequest)).TotalMilliseconds.ToString("F0")));
            sbTime.AppendLine(string.Format("OCR收到请求：{0}，已耗时:{1}ms", new DateTime(OcrServerAccepted).ToString("HH:mm:ss fff"), new TimeSpan(Math.Abs(OcrServerAccepted - ServerAlloted)).TotalMilliseconds.ToString("F0")));
            if (OcrServerReported > 0)
            {
                sbTime.AppendLine(string.Format("OCR反馈成功：{0}，已耗时:{1}ms", new DateTime(OcrServerReported).ToString("HH:mm:ss fff"), new TimeSpan(Math.Abs(OcrServerReported - OcrServerAccepted)).TotalMilliseconds.ToString("F0")));
            }
            if (OcrServerGetCode > 0)
            {
                sbTime.AppendLine(string.Format("OCR GetCode：{0}，已耗时:{1}ms", new DateTime(OcrServerGetCode).ToString("HH:mm:ss fff"), new TimeSpan(Math.Abs(OcrServerGetCode - OcrServerAccepted)).TotalMilliseconds.ToString("F0")));
            }
            if (OcrServerProcessOne > 0)
            {
                sbTime.AppendLine(string.Format("OCR 并行线程：{0}，已耗时:{1}ms", new DateTime(OcrServerProcessOne).ToString("HH:mm:ss fff"), new TimeSpan(Math.Abs(OcrServerGetCode - OcrServerProcessOne)).TotalMilliseconds.ToString("F0")));
            }
            sbTime.AppendLine(string.Format("OCR开始处理：{0}，已耗时:{1}ms", new DateTime(OcrServerBegin).ToString("HH:mm:ss fff"), new TimeSpan(Math.Abs(OcrServerBegin - OcrServerAccepted)).TotalMilliseconds.ToString("F0")));
            sbTime.AppendLine(string.Format("OCR处理完成：{0}，已耗时:{1}ms", new DateTime(OcrServerEnd).ToString("HH:mm:ss fff"), new TimeSpan(Math.Abs(OcrServerEnd - OcrServerBegin)).TotalMilliseconds.ToString("F0")));
            sbTime.AppendLine(string.Format("OCR反馈结果：{0}，已耗时:{1}ms", new DateTime(OcrServerReportedResult).ToString("HH:mm:ss fff"), new TimeSpan(Math.Abs(OcrServerReportedResult - OcrServerEnd)).TotalMilliseconds.ToString("F0")));
            sbTime.AppendLine(string.Format("服务端收到结果：{0}，已耗时:{1}ms", new DateTime(ServerReceivedOcrResult).ToString("HH:mm:ss fff"), new TimeSpan(Math.Abs(OcrServerReportedResult - ServerReceivedOcrResult)).TotalMilliseconds.ToString("F0")));
            sbTime.AppendLine(string.Format("用户请求结果：{0}，已耗时:{1}ms", new DateTime(ServerGiveUserResult).ToString("HH:mm:ss fff"), new TimeSpan(Math.Abs(ServerGiveUserResult - ServerReceivedOcrResult)).TotalMilliseconds.ToString("F0")));
            return sbTime.ToString();
        }
    }
}