﻿using System;
using System.Collections.Generic;

using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using ToolCommon;
using System.Data;
using System.Configuration;

namespace Account.Web
{
    public partial class QueryTicket : System.Web.UI.Page
    {
        protected void Page_Load(object sender, EventArgs e)
        {
            if (BoxUtil.GetStringFromObject(Request.QueryString["pwd"]) != CommonHelper.StrPWD)
                Response.End();
        }


        private void LoadData(int day, string strNo, string appCode, int isYuShou, string type)
        {
            DataTable dtNew = new DataTable();
            dtNew.Columns.Add("账号");
            dtNew.Columns.Add("机器名");
            dtNew.Columns.Add("注册日期");
            dtNew.Columns.Add("有效张数", typeof(int));
            dtNew.Columns.Add("合计", typeof(int));
            dtNew.Columns.Add("明细");
            int totalCount = 0;
            int validateCount = 0;
            int recordCount = 0;
            DataTable dtTmp = TicketHelper.GetAllTicket(day, strNo, appCode, isYuShou, type);
            if (dtTmp != null && dtTmp.Rows.Count > 0)
            {
                string machine = "";
                string machineName = "";
                string regDate = "";
                string strDesc = "";
                int validate = 0;
                int total = 0;
                for (int i = 0; i < dtTmp.Rows.Count; i++)
                {
                    if (string.IsNullOrEmpty(machine)
                        || !(dtTmp.Rows[i]["appCode"].ToString().Equals(machine)
                        && dtTmp.Rows[i]["machine"].ToString().Equals(machineName)
                        && dtTmp.Rows[i]["dtReg"].ToString().Equals(regDate)))
                    {
                        machine = dtTmp.Rows[i]["appCode"].ToString();
                        machineName = dtTmp.Rows[i]["machine"].ToString();
                        regDate = dtTmp.Rows[i]["dtReg"].ToString();
                        if (dtNew.Select(string.Format("账号='{0}' and 机器名='{1}' and 注册日期='{2}'", machine, machineName, regDate)).Length > 0)
                            continue;
                        DataRow[] rows = dtTmp.Select(string.Format("appCode='{0}' and machine='{1}' and dtReg='{2}'", machine, machineName, regDate));
                        strDesc = GetDetail(rows, out validate, out total);
                        DataRow row = dtNew.NewRow();
                        row["账号"] = machine;
                        row["机器名"] = machineName;
                        row["注册日期"] = regDate;
                        row["有效张数"] = validate;
                        row["明细"] = strDesc;
                        row["合计"] = total;
                        dtNew.Rows.Add(row);
                        totalCount += total;
                        validateCount += validate;
                        recordCount++;
                        rows = null;
                    }
                }
                dtNew.DefaultView.Sort = "[合计] desc";
            }
            lblCount.Text = string.Format("共{0}条，有效张数{1}张，合计{2}张", recordCount, validateCount, totalCount);
            gvDataSource.DataSource = dtNew;
            gvDataSource.DataBind();
        }

        protected void btnOK_Click(object sender, EventArgs e)
        {
            if (BoxUtil.GetStringFromObject(Request.QueryString["pwd"]) != CommonHelper.StrPWD)
                return;
            string type = "";
            int nDay = BoxUtil.GetInt32FromObject(txtCount.Text, 1);
            string strNo = txtNo.Text.Trim();
            string appCode = txtApp.Text.Trim();
            int isYuShou = 0;//预售
            LoadData(nDay, strNo, appCode, isYuShou, type);
        }

        private string GetDetail(DataRow[] dtRows, out int validate, out int total)
        {
            string result = "";
            validate = 0;
            total = 0;
            if (dtRows != null && dtRows.Length > 0)
            {
                Dictionary<string, int> dicCount = new Dictionary<string, int>();
                foreach (DataRow row in dtRows)
                {
                    if (!dicCount.ContainsKey(row["type"].ToString()))
                    {
                        dicCount.Add(row["type"].ToString(), BoxUtil.GetInt32FromObject(row["count"].ToString(), 1));
                    }
                    else
                        dicCount[row["type"].ToString()] += BoxUtil.GetInt32FromObject(row["count"].ToString(), 1);
                }
                if (dicCount.Count > 0)
                {
                    int noCount = 0;
                    int allCount = 0;
                    foreach (var item in dicCount)
                    {
                        allCount += item.Value;
                        if (item.Key.IndexOf("无座") >= 0)
                        {
                            noCount += item.Value;
                        }
                        result += string.Format("{0}:{1} ", item.Key, item.Value);
                    }
                    total = allCount;
                    validate = allCount - noCount;
                    //result += string.Format("   合计：{0}张,无座：{1}张,其他：{2}张", allCount, noCount, allCount - noCount);
                }
            }
            return result;
        }
    }
}