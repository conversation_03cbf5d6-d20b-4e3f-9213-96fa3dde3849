﻿using CommonLib;
using System;
using System.Collections.Generic;
using System.Collections.Specialized;
using System.IO;
using System.Net;
using System.Security.Cryptography;
using System.Text;
using System.Web;

namespace TransOcr
{
    /// <summary>
    /// 白描Lite
    /// </summary>
    public class BaiDuRec : BaseOcrRec
    {
        public BaiDuRec()
        {
            OcrGroup = OcrGroupType.百度;
            OcrType = TransOcrType.百度翻译;
            MaxExecPerTime = 22;
            LstJsonPreProcessArray = new List<object>() { "trans_result" };

            StrResultJsonSpilt = "src";
            StrResultTransJsonSpilt = "dst";

            AllowUploadFileTypes = new List<string>() { "txt" };
            RegAccount();
            InitLanguage();
        }

        #region 支持的语言

        private void InitLanguage()
        {
            TransLanguageDic = new Dictionary<TransLanguageTypeEnum, string>();
            TransLanguageDic.Add(TransLanguageTypeEnum.自动, "auto");
            TransLanguageDic.Add(TransLanguageTypeEnum.中文, "zh");
            TransLanguageDic.Add(TransLanguageTypeEnum.英文, "en");
            TransLanguageDic.Add(TransLanguageTypeEnum.日语, "jp");
            TransLanguageDic.Add(TransLanguageTypeEnum.朝鲜语, "kor");
            TransLanguageDic.Add(TransLanguageTypeEnum.西班牙语, "spa");
            TransLanguageDic.Add(TransLanguageTypeEnum.法语, "fra");
            TransLanguageDic.Add(TransLanguageTypeEnum.泰语, "th");
            TransLanguageDic.Add(TransLanguageTypeEnum.阿拉伯语, "ara");
            TransLanguageDic.Add(TransLanguageTypeEnum.俄语, "ru");

            TransLanguageDic.Add(TransLanguageTypeEnum.葡萄牙语, "pt");
            TransLanguageDic.Add(TransLanguageTypeEnum.德语, "de");
            TransLanguageDic.Add(TransLanguageTypeEnum.意大利语, "it");
            TransLanguageDic.Add(TransLanguageTypeEnum.希腊语, "el");
            TransLanguageDic.Add(TransLanguageTypeEnum.荷兰语, "nl");
            TransLanguageDic.Add(TransLanguageTypeEnum.波兰语, "pl");
            TransLanguageDic.Add(TransLanguageTypeEnum.保加利亚语, "bul");
            TransLanguageDic.Add(TransLanguageTypeEnum.爱沙尼亚语, "est");
            TransLanguageDic.Add(TransLanguageTypeEnum.丹麦语, "dan");
            TransLanguageDic.Add(TransLanguageTypeEnum.芬兰语, "fin");
            TransLanguageDic.Add(TransLanguageTypeEnum.捷克语, "cs");
            TransLanguageDic.Add(TransLanguageTypeEnum.罗马尼亚语, "rom");
            TransLanguageDic.Add(TransLanguageTypeEnum.斯洛文尼亚语, "slo");
            TransLanguageDic.Add(TransLanguageTypeEnum.瑞典语, "swe");
            TransLanguageDic.Add(TransLanguageTypeEnum.匈牙利语, "hu");
            TransLanguageDic.Add(TransLanguageTypeEnum.越南语, "vie");
        }

        #endregion

        private void RegAccount()
        {
            var lstAccount = new List<AccountDto>
            {
                new AccountDto
                {
                    clientId ="*****************",
                    secretKey ="f0fRzebysLiMtBj3CB_R",
                },
                new AccountDto
                {
                    clientId ="*****************",
                    secretKey ="yKYby8QiIp21wUE2vC8L",
                },
                new AccountDto
                {
                    clientId ="*****************",
                    secretKey ="4GEOkedoeuLlSiwypAoD",
                },
                new AccountDto
                {
                    clientId ="*****************",
                    secretKey ="QXkF2J3gy0kR8k_qFgCP",
                },
                new AccountDto
                {
                    clientId ="*****************",
                    secretKey ="7smZUeByLt4BJd5HqBMR",
                },
            };
            AccountHelper.RegAccount(OcrType.GetHashCode(), lstAccount);
        }

        protected override string GetHtml(OcrContent content)
        {
            string from = "", to = "";
            InitLanguage(content, ref from, ref to);
            var result = string.Empty;
            var account = AccountHelper.GetAccount(OcrType.GetHashCode());
            string salt = new Random().Next(100000).ToString();
            string sign = EncryptString(account.clientId + content.strBase64 + salt + account.secretKey);
            string url = "http://api.fanyi.baidu.com/api/trans/vip/translate?";
            url += "q=" + HttpUtility.UrlEncode(content.strBase64);
            url += "&from=" + from;
            url += "&to=" + to;
            url += "&appid=" + account.clientId;
            url += "&salt=" + salt;
            url += "&sign=" + sign;
            result = WebClientSyncExt.GetHtml(url, MaxExecPerTime);
            return result;
        }

        // 计算MD5值
        private string EncryptString(string str)
        {
            MD5 md5 = MD5.Create();
            // 将字符串转换成字节数组
            byte[] byteOld = Encoding.UTF8.GetBytes(str);
            // 调用加密方法
            byte[] byteNew = md5.ComputeHash(byteOld);
            // 将加密结果转换为字符串
            StringBuilder sb = new StringBuilder();
            foreach (byte b in byteNew)
            {
                // 将字节转换成16进制表示的字符串，
                sb.Append(b.ToString("x2"));
            }
            // 返回加密的字符串
            return sb.ToString();
        }

        private string UnixTime()
        {
            return Convert.ToInt64((DateTime.UtcNow - new DateTime(1970, 1, 1, 0, 0, 0, 0)).TotalSeconds * 1000).ToString();
        }

    }
}