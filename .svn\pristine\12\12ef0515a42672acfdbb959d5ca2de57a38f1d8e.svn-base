﻿using CommonLib;
using System;
using System.Collections.Generic;

namespace MathOcr
{
    /// <summary>
    /// https://ai.100tal.com/product/ocr-gs
    /// </summary>
    public class XueErSiDemoRec : BaseMathRec
    {
        public XueErSiDemoRec()
        {
            OcrGroup = OcrGroupType.学而思;
            OcrType = MathOcrType.学而思;
            MaxExecPerTime = 20;

            IsSupportUrlOcr = true;

            LstJsonPreProcessArray = new List<object>() { "data" };
            IsSupportVertical = false;

            StrResultJsonSpilt = "formula_result";
        }

        protected override string GetHtml(OcrContent content)
        {
            return processByContent(content);
        }

        private string processByContent(OcrContent content)
        {
            var strPost = "{\"method\":\"/aiimage/common-formula-reg\""
                            + ",\"image_base64\":" + (string.IsNullOrEmpty(content.strBase64) ? "null" : "[\"" + content.strBase64 + "\"]")
                            + ",\"reg_flag\":0"
                            + ",\"type\":0"
                            + ",\"image_url\":" + (string.IsNullOrEmpty(content.url) ? "null" : "[\"" + content.url + "\"]")
                            + "}";
            var result = WebClientSyncExt.GetHtml("https://ai.100tal.com/openapi/try/service", strPost, ExecTimeOutSeconds);
            //"{\"code\":0,\"msg\":\"成功\",\"data\":\"[{\\\"confidence\\\":1,\\\"word\\\":[{\\\"content\\\":\\\"请点击下图中所有的靴子\\\"},{\\\"content\\\":\\\"火柴\\\"}]}]\",\"content\":null,\"audioPath\":null}"
            return result;
        }

        public override string GetHtmlByUrl(OcrContent content)
        {
            return processByContent(content);
        }

        /*
            "rectangle": [
              22,
              225,
              121,
              239
            ]
         */
        protected override LocationInfo GetLocationByStr(string locationInfoStr)
        {
            locationInfoStr = locationInfoStr?
                      .Replace("[", "").Replace("]", "")
                      .Replace("{", "").Replace("}", "")
                      .Replace("\"", "").Replace(":", "")
                      .Replace("\r", "").Replace("\t", "").Replace("\n", "")
                      .Replace(" ", "").Trim();
            var spilt = locationInfoStr.Split(new string[] { "," }, StringSplitOptions.RemoveEmptyEntries);

            var location = new LocationInfo()
            {
                left = BoxUtil.GetInt32FromObject(spilt[0]),
                top = BoxUtil.GetInt32FromObject(spilt[1]),
                width = BoxUtil.GetInt32FromObject(spilt[2]) - BoxUtil.GetInt32FromObject(spilt[0]),
                height = BoxUtil.GetInt32FromObject(spilt[3]) - BoxUtil.GetInt32FromObject(spilt[1]),
            };

            return location;
        }

        private string UnixTime()
        {
            return Convert.ToInt64((DateTime.UtcNow - new DateTime(1970, 1, 1, 0, 0, 0, 0)).TotalSeconds * 1000).ToString();
        }

    }
}