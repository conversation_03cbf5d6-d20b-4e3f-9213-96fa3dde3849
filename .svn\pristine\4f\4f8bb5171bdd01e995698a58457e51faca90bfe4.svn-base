﻿<%@ Page Language="C#" AutoEventWireup="true" CodeBehind="TicketList.aspx.cs" Inherits="BugReprot.TicketList" %>

<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head runat="server">
    <title></title>
    <%--<base target="_blank">--%>
</head>
<body>
    <form id="form1" runat="server">
    <div>
        <asp:Label ID="Label1" runat="server" Text="日志列表："></asp:Label>
        <asp:Label ID="lblCount" runat="server" Text="0"></asp:Label>
        <asp:Label ID="lblTime" runat="server" Text=""></asp:Label>
    </div>
    <div>
        <asp:TextBox ID="txtNo" runat="server"></asp:TextBox>
        <asp:TextBox ID="txtApp" runat="server"></asp:TextBox>
        <asp:TextBox ID="txtTime" runat="server"></asp:TextBox>
        <asp:Button ID="btnOK" runat="server" Text="查询" OnClick="btnOK_Click" />
        <asp:Button ID="btnTong" runat="server" Text="同步" OnClick="btnTong_Click" />
        <asp:Button ID="btnTime" runat="server" Text="备注" OnClick="btnTime_Click" />
        <asp:Button ID="Button1" runat="server" Text="清理" OnClick="btnClear_Click" />
    </div>
    <asp:GridView ID="gvDataSource" runat="server" BackColor="White" BorderColor="#CCCCCC"
        BorderStyle="None" BorderWidth="1px" CellPadding="3" EnableModelValidation="True">
        <FooterStyle BackColor="White" ForeColor="#000066" />
        <HeaderStyle BackColor="#006699" Font-Bold="True" ForeColor="White" />
        <PagerStyle BackColor="White" ForeColor="#000066" HorizontalAlign="Left" />
        <RowStyle ForeColor="#000066" />
        <SelectedRowStyle BackColor="#669999" Font-Bold="True" ForeColor="White" />
    </asp:GridView>
    </form>
    <script type="text/javascript">
        window.onload = function () {
            //document.getElementById("gvDataSource").innerHTML = document.getElementById("gvDataSource").innerHTML
            //.replace(new RegExp("&lt;", "gm"), '<')
            //.replace(new RegExp("&gt;", "gm"), '>')
            ////.replace(new RegExp("http://r.qzone.qq.com/cgi-bin/user/cgi_personal_card?uin=【", "gm"), 'http://www.ip138.com/ips138.asp?ip=')
            ////.replace(new RegExp('uin=【', "gm"), 'ip=')
            //.replace(new RegExp('】" target="_blank">', "gm"), '" target="_blank">')
            //.replace(new RegExp('r.qzone.qq.com/cgi-bin/user/cgi_personal_card[\?]uin=', "gm"), 'www.ip138.com/ips138.asp?ip=');
            document.all.gvDataSource.rows[0].cells[8].style.width = "450px";
            document.all.gvDataSource.rows[0].cells[9].style.width = "100px";
        };
    </script>
</body>
</html>
