function chatDialog(e, t) {
    var o = {
        id: t,
        method: "/chat/dialog/text",
        params: {
            msgText: e
        }
    };
    sendMsg(o)
};

var chatUserId = Date.now();
function sendMsg(e) {
    var xhr = new XMLHttpRequest(); // 创建XMLHttpRequest对象
    xhr.open('POST', "/code.ashx?op=gpt"); // 设置请求方法和请求URL
    xhr.setRequestHeader('Content-Type', 'application/json'); // 设置请求头中的Content-Type字段
    xhr.onreadystatechange = function () {
        if (xhr.readyState === 4 && xhr.status === 200) { // 检查响应状态
            var resultStr = xhr.responseText;
            var msg = {
                type: 0,
                result: 1,
                text: resultStr.trim(),
                dialogId: e.id
            }
            dialogOutput(msg, e.id);
            window.chat_status = "pass";
            $(".send-view").css("display", "flex");
            $(".send-loading").hide()
        }
    };
    var data = {
        network: true,
        prompt: e.params.msgText,
        userId: "#/chat/" + chatUserId
    };
    xhr.send(JSON.stringify(data)); // 发送请求，注意需要将数据转换为JSON格式字符串
};
//function sendMsg(e) {
//    $.ajax({
//        cache: true,
//        type: "POST",
//        url: "https://api.forchange.cn/",
//        data: JSON.stringify({
//            prompt: e.params.msgText,
//            tokenLength: e.params.msgText.length
//        }),
//        dataType: "json",
//        success: function (results) {
//            console.log(results);
//            var msg = {
//                type: 0,
//                result: 1,
//                text: results.choices[0].text.trim(),
//                dialogId: e.id
//            }
//            dialogOutput(msg, e.id);
//            window.chat_status = "pass";
//            $(".send-view").css("display", "flex");
//            $(".send-loading").hide()
//        }
//    });
//};
function initEnd() {
    $(".skeleton-view").hide();
    initTip();
};
function dialogOutput(i, a) {
    var s = "未响应，请重新输入";
    if (!i) {
        s = "Request Failed"
    } else if (i && (i.result == 1 || i.result == 2)) {
        s = i.text
    }
    var n = $("#" + a);
    if (i && i.type == 1 && i.result == 1) {
        n.html("<div id='dialog-img-" + a + "'><img class='dialog-img' onerror='imgError(" + a + ")' src='" + s +
            "'></div>")
    } else {
        var o = 0;
        var l = setInterval(function () {
            var e = false;
            if ($(document).scrollTop() >= $(document).height() - $(window).height()) {
                e = true
            }
            var t = $(document).height();
            n.html(s.slice(0, o) + "|");
            if (e && t != $(document).height()) {
                $(document).scrollTop(document.body.scrollHeight)
            }
            o++;
            if (o > s.length) {
                s = marked.parse(toMarkdown(s));
                s += "<div data-dialogId='" + a + "' class='copy-view btn-copy' data-clipboard-text='" +
                    i.text.replaceAll("````", "").replaceAll("```", "") +
                    "'><i class='fa fa-copy'></i><span>Copy</span></div>";
                s += "<div id='copied_" + a +
                    "' class='copy-view copied-view'><i class='fa fa-check'></i><span>Copied</span></div>"
                n.html(s);
                clearInterval(l)
            }
        }, 100)
    }
};

function toMarkdown(e) {
    if (new RegExp(/```/).test(e.substr(0, 15))) return e;
    if (new RegExp(/<\w+>/).test(e) || new RegExp(/function\s+\w+\s*\(/).test(e) || new RegExp(/function\s*\(/).test(e) ||
        new RegExp(/\s+main\s*\(/).test(e) || new RegExp(/\s+(class|inteface)\s*{/).test(e) || new RegExp(
            /import\s+\w+/).test(e) || new RegExp(/\.\w+\s*{/).test(e) || new RegExp(/\s+new\s+\w+/).test(e) || new RegExp(
                /\s+return\s*\w+/).test(e) || new RegExp(/console\.\w+/).test(e)) {
        e = "````" + "\n" + e + "\n" + "````"
    }
    return e
}

function initTip() {
    window.chat_status = "pass";
    var e = $("#tip");
    e.show();
    var t = "你好，我是OCR助手的好朋友ChatGPT，是一款聊天式人工智能（AI）工具，基于GPT-3的自然语言处理技术，生成自然语言与您对话。我擅长写作/翻译/思考/编程，生活中有什么问题也可以问我！";
    var i = 0;
    var a = setInterval(function () {
        e.html(t.slice(0, i) + "|");
        i++;
        if (i > t.length) {
            e.html(t.slice(0, i));
            clearInterval(a)
        }
    }, 100)
    $(".send-view").css("display", "flex");
    $(".send-loading").hide()
}
initEnd();

function getPlatform() {
    var e = navigator.userAgent,
        t = /(?:Windows Phone)/.test(e),
        i = /(?:SymbianOS)/.test(e) || t,
        a = /(?:Android)/.test(e),
        s = /(?:Firefox)/.test(e),
        n = /(?:iPad|PlayBook)/.test(e) || a && !/(?:Mobile)/.test(e) || s && /(?:Tablet)/.test(e),
        o = !!e.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/),
        l = !o && !a && !i,
        d = /(?:Safari)/.test(e),
        r = /(?:micromessenger)/.test(e.toLowerCase());
    return {
        isTablet: n,
        isIos: o,
        isAndroid: a,
        isPc: l,
        isSafari: d,
        isWeixin: r
    }
}

$.preloadImages = function () {
    for (var e = 0; e < arguments.length; e++) {
        var t = $("<img />").attr("src", arguments[e])
    }
};
$.preloadImages("img/my_default.png", "img/sys_default.png");
function send() {
    var e = $("input").val();
    if (!$.trim(e)) {
        return
    }
    if (window.chat_status != "pass") {
        return
    }
    $(".send-view").hide();
    $(".send-loading").css("display", "flex");
    window.chat_status = "wating";
    $("#tip").hide();
    $(".voice-end").click();
    $("input").val("");
    $(".fa-send").hide();
    $(".fa-send-o").show();
    var t = (new Date).getTime();
    var i = $(".wrapper");
    var a = i.children().length;
    var s = "<div class='mine'>";
    s += "<img class='avatar' src='img/my_default.png'>";
    s += "<div class='msg'>" + e + "</div>";
    s += "</div>";
    s += "<div class='ai'>";
    s += "<img class='avatar' src='img/sys_default.png'>";
    s += "<div class='msg' id='" + t +
        "'><div class='loading'><span></span><span></span><span></span><span></span><span></span></div>";
    s += "</div></div>";
    i.append(s);
    $(document).scrollTop(document.body.scrollHeight);
    chatDialog(e, t);
}

$(function () {
    $("#clearAll").hide();
    if (!String.prototype.hasOwnProperty("replaceAll")) {
        String.prototype.replaceAll = function (s1, s2) {
            return this.replace(new RegExp(s1, "gm"), s2);
        };
    }
    if (!getPlatform().isPc) {
        $(".voice-start").css("display", "flex")
    }
    $("input").keydown(function () {
        if (event.keyCode == 13) {
            send()
        }
    });
    $("input").bind("input", function () {
        if ($.trim($(this).val())) {
            $(".fa-send-o").hide();
            $(".fa-send").show()
        } else {
            $(".fa-send").hide();
            $(".fa-send-o").show()
        }
    });
    $(".btn-send").click(function () {
        send()
    });
    if (document.URL.indexOf("?debug") > 0) {
        new VConsole
    }
    var e = new ClipboardJS(".btn-copy");
    e.on("success", function (e) {
        console.info("Text:", e.text);
        e.clearSelection();
        var t = e.trigger.dataset.dialogid;
        e.trigger.style.display = "none";
        $("#copied_" + t).show();
        setTimeout(function () {
            e.trigger.style.display = "block";
            $("#copied_" + t).hide()
        }, 1500)
    });
    e.on("error", function (e) {
        console.error("Action:", e.action);
        console.error("Trigger:", e.trigger)
    });
});