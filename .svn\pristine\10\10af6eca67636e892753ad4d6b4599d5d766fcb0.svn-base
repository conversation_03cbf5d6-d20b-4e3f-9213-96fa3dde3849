﻿using CommonLib;
using System;
using System.Collections.Generic;
using System.Collections.Specialized;
using System.Diagnostics;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Net;
using System.Security.Cryptography;
using System.Text;
using System.Threading.Tasks;
using System.Web;

namespace TransOcr
{
    public class PapaGoRec : BaseOcrRec
    {
        private string StrMacId = "f3a2dd66-775c-4948-a383-d2edd5fd64d3";
        public PapaGoRec()
        {
            OcrType = TransOcrType.灵格斯;
            MaxExecPerTime = 10;
            //StrMacId = Guid.NewGuid().ToString().ToLower();

            LstJsonPreProcessArray = new System.Collections.Generic.List<object>() { "translatedText" };
            IsProcessJsonResultByArray = false;

            //文件上限2M
            FileSizeLimit = 1024 * 1024 * 2;
            AllowUploadFileTypes = new List<string>() { "pdf", "doc", "docx", "xls", "xlsx", "ppt", "pptx", "txt" };
            InitLanguage();
        }

        #region 支持的语言

        //zh-CHS:简体中文
        //en:英文
        //zh-CHT:繁体中文
        //ja:日语
        //ko:朝鲜语
        //fr:法语
        //es:西班牙语
        //th:泰语
        //ar:阿拉伯语
        //ru:俄语
        //pt:葡萄牙语
        //de:德语
        //it:意大利语
        //el:希腊语
        //nl:荷兰语
        //pl:波兰语
        //bg:保加利亚语
        //et:爱沙尼亚语
        //da:丹麦语
        //fi:芬兰语
        //cs:捷克语
        //ro:罗马尼亚语
        //sl:斯洛文尼亚语
        //sv:瑞典语
        //hu:匈牙利语
        //vi:越南语

        private void InitLanguage()
        {
            //ko<->en, ko<->zh-CN, ko<->zh-TW, ko<->es, ko<->fr, ko<->vi, ko<->th, ko<->id, en<->ja, en<->fr
            TransLanguageDic = new Dictionary<TransLanguageTypeEnum, string>();
            TransLanguageDic.Add(TransLanguageTypeEnum.中文, "zh-CN");
            TransLanguageDic.Add(TransLanguageTypeEnum.英文, "en");
            TransLanguageDic.Add(TransLanguageTypeEnum.繁体中文, "zh-TW");
            TransLanguageDic.Add(TransLanguageTypeEnum.日语, "ja");
            TransLanguageDic.Add(TransLanguageTypeEnum.朝鲜语, "ko");
            TransLanguageDic.Add(TransLanguageTypeEnum.法语, "fr");
            TransLanguageDic.Add(TransLanguageTypeEnum.泰语, "th");
            TransLanguageDic.Add(TransLanguageTypeEnum.西班牙语, "es");
            TransLanguageDic.Add(TransLanguageTypeEnum.越南语, "vi");
        }

        #endregion

        private long GetRndTick()
        {
            return Convert.ToInt64((DateTime.UtcNow - new DateTime(1970, 1, 1, 0, 0, 0, 0)).TotalSeconds) * 1000;
        }

        private string GetSign(long timeSpan, string url)
        {
            //https://papago.naver.com/main.660acb667c4cee778e60.chunk.js
            // u = "PPG " + s + ":" + p.a.HmacMD5(s + "\n" + "https://papago.naver.com/apis/n2mt/translate" + "\n" + timeSpan(13位), "v1.5.1_4dfe1d83c2").toString(p.a.enc.Base64);


            var strTmp = StrMacId + "\n" + url + "\n" + timeSpan;

            var sign = HmacSha1Sign(strTmp, strToken);
            sign = "PPG " + StrMacId + ":" + sign;
            return sign;
        }

        private string strToken = "";

        private void InitToken()
        {
            if (string.IsNullOrEmpty(strToken))
            {
                strToken = InitSignCode();
            }
        }

        private string InitSignCode()
        {
            var result = "";
            var html = WebClientSyncExt.GetHtml("https://papago.naver.com", MaxExecPerTime);
            var jsUrl = CommonHelper.SubString(html, "src=\"/main.", "\"");
            if (!string.IsNullOrEmpty(jsUrl) && jsUrl.EndsWith("js"))
            {
                jsUrl = string.Format("https://papago.naver.com/main.{0}", jsUrl);
                html = WebClientSyncExt.GetHtml(jsUrl, MaxExecPerTime);
                html = html.SubStringHorspool("HmacMD5(", ").toString");
                html = html.SubStringHorspool(",\"v", "\"");
                if (!string.IsNullOrEmpty(html))
                {
                    result = "v" + html;
                }
            }

            if (string.IsNullOrEmpty(result))
            {
                result = "v1.5.2_0d13cb6cf4";
            }
            return result;
        }

        public override void Reset()
        {
            strToken = "";
            base.Reset();
        }

        protected override string GetHtml(OcrContent content)
        {
            InitToken();
            if (string.IsNullOrEmpty(strToken))
            {
                return null;
            }
            var timeSpan = GetRndTick();
            var url = "https://papago.naver.com/apis/nsmt/translate";
            //var url = "https://papago.naver.com/apis/n2mt/translate";
            var sign = GetSign(timeSpan, url);

            string from = "", to = "";
            InitLanguage(content, ref from, ref to);

            var strPost = string.Format("deviceId={0}&locale=zh-CN&dict=false&dictDisplay=30&honorific=false&instant=true&paging=false&source={1}&target={2}&text={3}"
                , StrMacId
                , from
                , to
                , HttpUtility.UrlEncode(content.strBase64));
            var result = WebClientSyncExt.GetHtml(url, "", strPost, "https://papago.naver.com/", ExecTimeOutSeconds, new NameValueCollection() {
                { "authorization", sign },
                { "timestamp",timeSpan.ToString()},
                { "x-apigw-partnerid","papago"},
                { "device-type","pc"},
                { "DNT","1"},
                //{ ":authority","papago.naver.com"},
                //{ ":method","POST"},
                //{ ":path","/apis/n2mt/translate"},
                //{ ":scheme","https"} 
            });
            return result;
        }

        private string HmacSha1Sign(string str, string key)
        {
            byte[] keyBytes = Encoding.UTF8.GetBytes(key);
            using (var hmac = new HMACMD5(keyBytes))
            {
                byte[] inputBytes = Encoding.UTF8.GetBytes(str);
                var byts = hmac.ComputeHash(inputBytes);
                return Convert.ToBase64String(byts);
            }
        }

        /// <summary>
        /// MD5加密（小写）
        /// </summary>
        /// <param name="s">字符串</param>
        /// <param name="len">长度</param>
        /// <returns></returns>
        string MD5(string s, int len = 32)
        {
            var md5Hasher = new MD5CryptoServiceProvider();
            byte[] data = md5Hasher.ComputeHash(Encoding.UTF8.GetBytes(s));
            StringBuilder sb = new StringBuilder();
            for (int i = 0; i < data.Length; i++)
            {
                sb.Append(data[i].ToString("x2"));
            }
            string result = sb.ToString();
            return len == 32 ? result : result.Substring(8, 16);
        }

    }
}
