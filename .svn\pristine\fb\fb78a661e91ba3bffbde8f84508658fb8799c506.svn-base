﻿<%@ Page Title="查看我的设备" Language="C#" MasterPageFile="~/UserMaster.Master" AutoEventWireup="true" CodeBehind="UserMac.aspx.cs" Inherits="Account.Web.UserMac" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="server">
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="server">
    <div class="user_body">
        <div class="body_content" style="padding-top: 0px;">
            <div class="mod-sheet">
                <div class="sheet_header">
                    <p class="header_title">我的设备</p>
                </div>
                <div class="sheet_body">
                    <table class="body_table" id="gvDataSource">
                        <thead>
                            <tr>
                                <th style="width: 60px;">序号</th>
                                <th>名称</th>
                                <th style="width: 220px;">IP</th>
                                <th style="width: 110px;">时间</th>
                                <%--<th style="width: 70px;">次数</th>--%>
                                <th style="width: 80px;">操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <%
                                var account = Account.Web.UserLoginInfoHelper.GetUserInfo(Request, Response).Account;
                                var dtTmp = Account.Web.CodeHelper.GetOnLineCode(account, "", DateTime.Now.AddDays(-90).ToString("yyyy-MM-dd"));
                                int index = 1;
                                var accountCache = CommonLib.RdsCacheHelper.CodeRecordCache.GetUserCodeInfo(account);
                                foreach (System.Data.DataRow item in dtTmp.Rows)
                                {
                                    var token = item["Token"] != null ? item["Token"].ToString() : "";
                            %>
                            <tr>
                                <td><%=index %></td>
                                <td><%=item["Mac"] %></td>
                                <td><%=item["Ip"]==null?"":item["Ip"].ToString() %></td>
                                <td><%=CommonLib.BoxUtil.GetDateTimeFromObject(item["活动时间"]).ToString("yyyy-MM-dd") %></td>
                                <%--<td><%=!string.IsNullOrEmpty(token)&&accountCache!=null&&accountCache.Tokens!=null? accountCache.Tokens.FirstOrDefault(p => Equals(p.Token, token))!=null?accountCache.Tokens.FirstOrDefault(p => Equals(p.Token, token)).TodayCount.ToString():"": "" %></td>--%>
                                <td>
                                    <%
                                        var state = item["state"].ToString();
                                        if (Equals(state, "1"))
                                        {
                                    %>
                                    <a style="color: red; cursor: pointer;" href="javascript:if(confirm('<%=Account.Web.CommonTranslate.GetTrans(Account.Web.UserConst.StrConfirmToDisableMac,Request)%>')){window.location.href='UserMac.aspx?op=disable&id=<%=item["uid"] %>'}">禁用</a>
                                    <%--<a style="color: blue; cursor: pointer;" href="javascript:if(confirm('确认要下线该设备？')){window.location.href='UserMac.aspx?op=offline&id=<%=item["uid"] %>'}">下线</a>--%>
                                    <%}
                                        else
                                        {  %>
                                    <a style="color: blue; cursor: pointer;" href="javascript:if(confirm('<%=Account.Web.CommonTranslate.GetTrans(Account.Web.UserConst.StrConfirmToEnableMac,Request)%>')){window.location.href='UserMac.aspx?op=enable&id=<%=item["uid"] %>'}">启用</a>
                                    <%} %>
                                </td>
                            </tr>
                            <%
                                    index++;
                                }
                            %>
                        </tbody>
                    </table>
                </div>


            </div>
        </div>
    </div>
    <script src="/static/js/autocdn.js" type="text/javascript"></script>
    <script src="//lf6-cdn-tos.bytecdntp.com/cdn/jquery/1.4.2/jquery.min.js" type="text/javascript" onerror="autoRetry(this)"></script>
    <script type="text/javascript">
        function editNickName() {
            $("#showNickName").hide();
            $("#editNickName").css("display", "flex");
        }

        var nickNameReg = /^[\u4e00-\u9fa5\w]{2,12}$/;

        function saveNickName() {
            var nickName = $("#nickname").val();
            if (!nickNameReg.test(nickName)) {
                alert("<%=Account.Web.CommonTranslate.GetTrans(Account.Web.UserConst.StrNickNameFormatError,Request)%>");
                return;
            }
            $("#lblNickName").text(nickName);
            $("#editNickName").hide();
            $("#showNickName").show();
            window.location = "UserIndex.aspx?op=updatenickname&nickname=" + nickName;
        }
        function editPwd() {
            if ($("#editPwd").is(':visible') == false) {
                $("#editPwd").show();
                return;
            }
            var password = $("#password").val();
            if (password == null || password.trim() == '') {
                alert("<%=Account.Web.CommonTranslate.GetTrans(Account.Web.UserConst.StrNewPwdEmpty,Request)%>");
                return
            }
            window.location = "UserIndex.aspx?op=updatepwd&pwd=" + password;
        }
        function fetchLocationInfo(ip) {
            return new Promise((resolve, reject) => {
                const url = "https://qifu-api.baidubce.com/ip/geo/v1/district?ip=" + encodeURIComponent(ip);
                const xhr = new XMLHttpRequest();
                xhr.open('GET', url, true);
                xhr.onload = function () {
                    if (xhr.status === 200) {
                        const response = JSON.parse(xhr.responseText);
                        resolve(response);
                    } else {
                        reject(xhr.statusText);
                    }
                };
                xhr.onerror = function () {
                    reject(xhr.statusText);
                };
                xhr.send();
            });
        }
        function LoadIp() {
            var dataGridView = document.getElementById("gvDataSource");
            var i = 1;
            function processNextRow() {
                if (i < dataGridView.rows.length) {
                    try {
                        const row = dataGridView.rows[i];
                        const ipCell = row.cells[2];
                        const ip = ipCell.innerText;
                        fetchLocationInfo(ip)
                            .then(response => {
                                const country = response.data.country;
                                const province = response.data.prov;
                                const newValue = `${ip}(${country},${province})`;
                                ipCell.innerText = newValue;
                                i++;
                                setTimeout(processNextRow, 500);
                            })
                            .catch(error => {
                                console.error('Failed to fetch location info:', error);
                                i++;
                                setTimeout(processNextRow, 1000);
                            });
                    }
                    catch { }
                }
            }
            processNextRow();
        }

        window.onload = function () {
            LoadIp();
        };
    </script>
</asp:Content>
