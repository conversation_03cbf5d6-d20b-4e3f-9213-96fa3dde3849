﻿using CommonLib;
using System;
using System.IO;

namespace HanZiOcr
{
    /// <summary>
    /// 东信北邮
    /// https://www.ebupt.tech/#/word-recognition
    /// </summary>
    public class EBuptLiteAppRec : BaseOcrRec
    {
        public EBuptLiteAppRec()
        {
            OcrType = HanZiOcrType.EBuptLite;
            MaxExecPerTime = 1;
            //State = EnableState.禁用;

            LstJsonPreProcessArray = new System.Collections.Generic.List<object>() { "content" };
            StrResultJsonSpilt = "data";
        }

        protected override string GetHtml(string strBase64)
        {
            var byt = Convert.FromBase64String(strBase64);
            var result = PostFileResult(byt);
            //"{\"code\":0,\"msg\":\"成功\",\"data\":\"[{\\\"confidence\\\":1,\\\"word\\\":[{\\\"content\\\":\\\"请点击下图中所有的靴子\\\"},{\\\"content\\\":\\\"火柴\\\"}]}]\",\"content\":null,\"audioPath\":null}"
            return result;
        }

        private string PostFileResult(byte[] content)
        {
            var result = "";
            try
            {
                var url = "https://www.ebupt.tech/word";
                var file = new UploadFileInfo()
                {
                    Name = "file",
                    Filename = "test.jpg",
                    ContentType = "image/jpg",
                    Stream = new MemoryStream(content)
                };
                result = UploadFileRequest.Post(url, new[] { file }, null);
            }
            catch (Exception)
            {

            }
            return result;
        }

    }
}