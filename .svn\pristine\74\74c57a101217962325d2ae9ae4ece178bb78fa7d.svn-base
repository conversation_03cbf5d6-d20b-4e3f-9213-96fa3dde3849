﻿using CommonLib;
using System;
using System.Collections.Generic;
using System.Collections.Specialized;
using System.IO;
using System.Security.Cryptography;
using System.Text;

namespace MathOcr
{
    /// <summary>
    /// 有道智云-小程序
    /// https://aidemo.youdao.com/formula_ocr.html?key=9238ec2f0ede4d3f8f73045f55510183
    /// </summary>
    public class YouDaoLiteRec : BaseMathRec
    {
        public YouDaoLiteRec()
        {
            OcrGroup = OcrGroupType.有道;
            OcrType = MathOcrType.有道Lite;
            MaxExecPerTime = 21;

            LstJsonPreProcessArray = new List<object>() { "data" };
            LstJsonNextProcessArray = new List<object>() { "originOcr" };
            IsProcessJsonResultByArray = false;
        }

        static List<YouDaoAccount> lstAppAccount = new List<YouDaoAccount>() {
        new YouDaoAccount(){
         strAppId = "2423360539ba5632",
          strSecretId =  "QQ8gLkYxtchLt6Osj1eXrsSDTus8N2Ru"
        },
        //new YouDaoAccount(){
        // strAppId = "712b0ae8fd3d404d",
        //  strSecretId =  "TF7ORXNiC6J3V18WZ4JCVYe2chHPVnRZ"
        //},
        new YouDaoAccount(){
        strAppId = "4987f62dcf974c87",
        strSecretId =  "e5Qc5zA6nwK8YOBcVyrBTV8s43IlMe41"
        }
        };

        protected override string GetHtml(OcrContent content)
        {
            var result = string.Empty;
            var account = lstAppAccount.GetRndItem();
            var dic = new NameValueCollection();
            dic.Add("type", "2");
            dic.Add("signType", "v4");
            TimeSpan ts = (DateTime.UtcNow - new DateTime(1970, 1, 1, 0, 0, 0, DateTimeKind.Utc));
            long millis = (long)ts.TotalMilliseconds;
            string curtime = Convert.ToString(millis / 1000);
            dic.Add("curtime", curtime);
            dic.Add("osType", "api");
            dic.Add("docType", "json");
            dic.Add("appKey", account?.strAppId);
            string salt = new Random().Next(1, 10).ToString();
            dic.Add("salt", salt);
            string signStr = account?.strAppId + salt + curtime + account?.strSecretId;
            string sign = ComputeHash(signStr, new SHA256CryptoServiceProvider());
            dic.Add("sign", sign);

            var url = "https://aidemo.youdao.com/ocrquestionmini";
            var byt = Convert.FromBase64String(content.strBase64);
            var file = new UploadFileInfo()
            {
                Name = "file",
                Filename = "1." + content.fileExt,
                ContentType = ApplicationTypeHelper.GetApplicationType(content.fileExt),
                Stream = new MemoryStream(byt)
            };
            var html = PostFile(url, new[] { file }, dic);
            var id = CommonHelper.SubString(html, "\"result\":\"", "\"");
            if (!string.IsNullOrEmpty(id))
            {
                result = WebClientSyncExt.GetHtml(string.Format("https://aidemo.youdao.com/wxconfig/{0}", id));
            }
            return result;
        }


        protected static string ComputeHash(string input, HashAlgorithm algorithm)
        {
            Byte[] inputBytes = Encoding.UTF8.GetBytes(input);
            Byte[] hashedBytes = algorithm.ComputeHash(inputBytes);
            return BitConverter.ToString(hashedBytes).Replace("-", "");
        }

        protected static string Truncate(string q)
        {
            if (q == null)
            {
                return null;
            }
            int len = q.Length;
            return len <= 20 ? q : (q.Substring(0, 10) + len + q.Substring(len - 10, 10));
        }

    }
}