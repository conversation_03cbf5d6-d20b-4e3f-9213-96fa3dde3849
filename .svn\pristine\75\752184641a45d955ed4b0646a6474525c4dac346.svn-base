﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Configuration;

namespace ToolCommon
{
    public static class ConfigUtil
    {
        public static string GetConnectionStrings(string key)
        {
            return BoxUtil.GetStringFromObject(ConfigurationManager.ConnectionStrings[key]);
        }

        public static string GetConnectionStrings(string key, string defaultValue)
        {
            return BoxUtil.GetStringFromObject(ConfigurationManager.ConnectionStrings[key], defaultValue);
        }

        public static string GetAppStrings(string key)
        {
            return BoxUtil.GetStringFromObject(ConfigurationManager.AppSettings[key]);
        }

        public static string GetAppStrings(string key, string defaultValue)
        {
            return BoxUtil.GetStringFromObject(ConfigurationManager.AppSettings[key], defaultValue);
        }
    }
}
