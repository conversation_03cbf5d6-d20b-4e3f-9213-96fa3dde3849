﻿using CommonLib;
using log4net;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net;
using System.Runtime.Remoting.Contexts;
using System.Security.Policy;
using System.Text;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

namespace Account.Web
{
    public class Global : HttpApplication
    {
        protected void Application_PreRequestHandlerExecute(object sender, EventArgs e)
        {
            MemoryManager.ClearMemory();
            if (HttpContext.Current.Handler is Page page)
            {
                page.PreRender += new EventHandler(Page_PreRender);
            }
        }

        List<string> lstBackgroundPage = new List<string>() { "alluser.aspx", "code.aspx", "iplist.aspx", "mail.aspx", "newuser.aspx", "pay", "view.aspx" };
        private void Page_PreRender(object sender, EventArgs e)
        {
            if (HttpContext.Current.Handler is Page page)
            {
                page.InitComplete += (s, ee) =>
                {
                    if (page == null || page.Header == null || page.Response == null || page.Response.ContentType != "text/html")
                        return;
                    page.SetExtTitle();
                };
            }
        }

        protected void Application_Start(object sender, EventArgs e)
        {
            lock (ConfigHelper.LockObj)
            {
                if (!ConfigHelper.IsOnInit)
                {
                    ConfigHelper.IsOnInit = true;
                    ConfigHelper.InitThread();
                    try
                    {
                        var file = new FileInfo(AppDomain.CurrentDomain.BaseDirectory + "Log4Net.config");
                        log4net.Config.XmlConfigurator.Configure(file);
                    }
                    catch { }
                    ConfigHelper.InitConfig();

                    MsgProcessHelper.Init();
                    RdsCacheHelper.InitLimiter();
                    DisposeEmailHelper.Init();
                    PayUtil.Init();
                }
            }
        }

        private bool ResponseEnd(string key, string value, bool isEndResponse, bool isAddToBlack, bool isShort = false, bool isStaticBlack = false)
        {
            Request.Log(string.Format("{1}:{0}被拦截！", value, key), true, isShort);
            if (isAddToBlack)
            {
                var strIp = HttpContext.Current.Items["nowIP"]?.ToString();
                if (!string.IsNullOrEmpty(strIp) && !IsSpider())
                {
                    var lstTmp = GetBlackIpCache(isStaticBlack);
                    if (!lstTmp.Contains(strIp))
                    {
                        lstTmp.Add(strIp);
                    }
                    if (isStaticBlack)
                    {
                        LogHelper.Log.Error(string.Format("加入永久黑名单！IP:{0}", strIp));
                        CodeProcessHelper.StaticBlackIpCache.Set(lstTmp);
                    }
                    else
                    {
                        LogHelper.Log.Error(string.Format("加入黑名单！IP:{0}", strIp));
                        CodeProcessHelper.BlackIpConfigCache.Set(lstTmp);
                    }
                }
            }
            if (isEndResponse)
            {
                Response.ContentType = "application/json; charset=utf-8";
                Response.End();
            }
            return isEndResponse;
        }

        private static List<string> lstBadAgent = new List<string>() { "curl", "python" };

        List<string> lstNoCachePage = new List<string>() { "ocr/", "tool/", "user", "server.aspx", "login.aspx" };

        protected void Application_BeginRequest(object sender, EventArgs e)
        {
            if (ConfigHelper.IsBlackIpEnable)
            {
                //IP黑名单
                var ip = Request.GetIPAddress();
                var uid = Request.GetValue("uid");
                if (IsBlack(ip, uid))
                {
                    if (ResponseEnd("IP/UID黑名单", ip + "|" + uid, true, false, true))
                        return;
                }
                if (!string.IsNullOrEmpty(ip))
                    try
                    {
                        HttpContext.Current.Items["nowIP"] = ip;
                    }
                    catch (Exception oe)
                    {
                        LogHelper.Log.Error(oe);
                    }
                ////Agent非法
                //var isBadAgent = !string.IsNullOrEmpty(Request.UserAgent) && lstBadAgent.Any(p => Request.UserAgent.ToLower().StartsWith(p));
                //if (isBadAgent)
                //{
                //    if (ResponseEnd("非法Agent", Request.UserAgent, true, true))
                //        return;
                //}
            }

            //检查路径是否存在，暂不拦截
            var cacheUrl = CommonTranslate.GetCahcePath(Request.Url);
            var strPhyPath = Server.MapPath(cacheUrl.LocalPath);
            var path = cacheUrl.LocalPath.TrimStart('/').Trim().ToLower();
            if (!string.IsNullOrEmpty(path))
            {
                if (path.ToLower().Contains("apppush"))
                {
                    int type = BoxUtil.GetInt32FromObject(Request.GetValue("type"));
                    string price = Request.GetValue("price");
                    string remark = Request.GetValue("remark");
                    string t = Request.GetValue("t");
                    string sign = Request.GetValue("sign");
                    var result = PayUtil.appPush(type, price, remark, t, sign);
                    Response.ContentType = "application/json";
                    Response.Write(JsonConvert.SerializeObject(result));
                    Response.End();
                }
                else if (path.ToLower().Contains("appheart"))
                {
                    string t = Request.GetValue("t");
                    string sign = Request.GetValue("sign");
                    var result = PayUtil.appHeart(t, sign);
                    Response.ContentType = "application/json";
                    Response.Write(JsonConvert.SerializeObject(result));
                    Response.End();
                }
                else
                {
                    //Check Bad Request
                    var lstBadUrl = CodeProcessHelper.StaticBlackUrlCache.Get() ?? new List<string>();
                    lstBadUrl.AddRange(lstBadUrl.Select(p => p.Replace("/", "\\")).ToList());
                    if (lstBadUrl.Any(p => path.Contains(p)))
                    {
                        if (ResponseEnd("URL黑名单", path.Replace(AppDomain.CurrentDomain.BaseDirectory, ""), true, true, false, true))
                            return;
                    }
                    else
                    {
                        var checkPathResult = File.Exists(strPhyPath) || Directory.Exists(strPhyPath);
                        if (!checkPathResult)
                        {
                            if (ResponseEnd("路径不存在", path, true, true))
                                return;
                        }

                        //解析缓存
                        if ((path.Contains(".aspx") || path.Contains(".html"))
                             && !lstBackgroundPage.Any(p => path.Contains(p)))
                        {
                            if (lstNoCachePage.Any(p => path.Contains(p)) && !path.Contains("ocr/index.html"))
                            {
                                Response.Clear();
                                Response.ContentType = "text/html; charset=utf-8";
                                Response.ContentEncoding = Encoding.UTF8;
                                Response.Write("<script type=\"text/javascript\">isCache = false;</script>");
                            }
                            else
                            {
                                var isCache = false;
                                var strAbsPath = cacheUrl.LocalPath.TrimStart('/');
                                string lang = CommonTranslate.GetCurrentLang(Request, true);
                                if (!Equals(lang, CommonTranslate.StrDefaultLang))
                                {
                                    string cachedContent = CommonTranslate.GetPageTrans(strAbsPath.Replace("/", "-"), strPhyPath, lang);
                                    if (!string.IsNullOrEmpty(cachedContent))
                                    {
                                        isCache = true;
                                        Response.Clear();
                                        Response.ContentType = "text/html; charset=utf-8";
                                        Response.ContentEncoding = Encoding.UTF8;
                                        Response.Write(cachedContent);
                                    }
                                }
                                if (isCache)
                                {
                                    try
                                    {
                                        HttpContext.Current.Items["cache"] = true;
                                    }
                                    catch (Exception oe)
                                    {
                                        LogHelper.Log.Error(oe);
                                    }
                                    Response.End();
                                }
                                else
                                {
                                    Response.Clear();
                                    Response.ContentType = "text/html; charset=utf-8";
                                    Response.ContentEncoding = Encoding.UTF8;
                                    Response.Write(CommonTranslate.GetLinkLang(strAbsPath));
                                    if (!Equals(lang, CommonTranslate.StrDefaultLang))
                                    {
                                        LogHelper.Log.Error(string.Format("NoCache:{0},lang:{1},url:{2},absPath:{3},agent:{4}", path, lang, Request.Url, strAbsPath, Request.UserAgent));
                                    }
                                }
                            }
                        }
                    }
                }
            }

            ////检查请求体
            //if (Request.Url.LocalPath.Contains(".ashx") == true || Request.RawUrl.ToLower().Contains("mail.aspx") || Request.RawUrl.ToLower().Contains("code.aspx"))
            //{
            //    return;
            //}
            //if (new Common.SqlChecker(Request).Check())
            //{
            //    if (ResponseEnd("非法字符串", Request.Url.LocalPath, true, true))
            //        return;
            //}
        }

        private static List<string> lstExpShowLanguagePage = new List<string>() {
            "default.aspx", "detail.aspx", "login.aspx", "ocr.aspx", "status.aspx", "tool.aspx", "user.aspx", "version.aspx"
            , "desc.aspx", "privacy.html","agreemeut.aspx" };
        protected void Application_EndRequest(object sender, EventArgs e)
        {
            var strPath = Request.Url.AbsolutePath.ToLower();
            if (ConfigHelper.IsLogRequest && strPath.Contains(".aspx"))
            {
                Request.Log("EndRequest", false);
            }
            if (HttpContext.Current.Response.ContentType == "text/html"
                && HttpContext.Current.Items["cache"] == null)
            {
                if (!lstBackgroundPage.Any(p => strPath.Contains(p)))
                {
                    if (!Request.Url.Host.Equals("localhost"))
                        HttpContext.Current.Response.Write(CommonRequest.TongJiCode);
                    if (!Equals("/", strPath) && !lstExpShowLanguagePage.Any(p => strPath.Contains(p)))
                        HttpContext.Current.Response.Write("<div id=\"translate\" class=\"ignore\" style=\"display: none\"></div>");
                    HttpContext.Current.Response.Write("<script src='/static/js/translate.js'></script>");
                }
            }

        }

        protected void Application_End(object sender, EventArgs e)
        {
            ConfigHelper.IsExit = true;
            LogManager.GetLogger("Application_End").Error("【POD挂了】 时间:" + ServerTime.LocalTime.ToString("yyyy-MM-dd HH:mm:ss"));
            Application_Error(sender, e);
        }

        private bool IsBlack(string ip, string uid)
        {
            var result = false;
            var lstTmpBlackIp = GetBlackIpCache(true);
            if (!string.IsNullOrEmpty(ip) && lstTmpBlackIp.Contains(ip))
            {
                result = true;
            }
            if (!result && !string.IsNullOrEmpty(uid) && lstTmpBlackIp.Contains(uid))
            {
                result = true;
            }
            if (!result)
            {
                lstTmpBlackIp = GetBlackIpCache();
                if (!string.IsNullOrEmpty(ip) && lstTmpBlackIp.Contains(ip))
                {
                    result = true;
                }
                if (!result && !string.IsNullOrEmpty(uid) && lstTmpBlackIp.Contains(uid))
                {
                    result = true;
                }
            }
            return result;
        }

        private List<string> GetBlackIpCache(bool isStatic = false)
        {
            return (isStatic ? CodeProcessHelper.StaticBlackIpCache.Get() : CodeProcessHelper.BlackIpConfigCache.Get()) ?? new List<string>();
        }

        protected void Application_Error(object sender, EventArgs e)
        {
            var oe = Server.GetLastError();
            if (oe != null)
            {
                LogManager.GetLogger("Application_Error").Error(" URL:" + Request.Url, oe);
                if (oe.Message.Contains("不存在") || oe is HttpRequestValidationException)
                {
                    ResponseEnd("IIS异常", Request.Url.LocalPath, false, true);
                }
                Server.ClearError();
            }
        }

        private bool IsSpider()
        {
            var result = false;
            try
            {
                //  - regex: '^.{0,100}(BUbiNG|zao|borg|oegp|silk|Xenu|zeal|^NING|crawl|htdig|lycos|slurp|teoma|voila|CiBra|Nutch|^Java/|^JNLP/|Daumoa|Daum|Genieo|ichiro|larbin|pompos|Scrapy|snappy|speedy|spider|vortex|^vortex|crawler|favicon|Riddler|scooter|scraper|scrubby|WhatWeb|WinHTTP||^voyager|archiver|Icarus6j|mogimogi|Netvibes|altavista|charlotte|findlinks|Retreiver|TLSProber|WordPress|wsr\-agent|Squrl Java|netresearch|searchsight|ICC\-Crawler|http%20client|Python-urllib|dataparksearch|converacrawler|Screaming Frog|fast\-webcrawler|semanticdiscovery|Innovazion Crawler|facebookexternalhit|BlogBridge|IlTrovatore-Setaccio|InternetArchive|GomezAgent|WebThumbnail|heritrix|NewsGator|PagePeeker|Reaper|ZooShot|holmes|NL-Crawler|Pingdom|StatusCake|WhatsApp|masscan|Qwantify|Yeti|OgScrper)'
                var agent = Request.UserAgent?.ToLower() ?? "";
                result = agent.Contains("spider") || agent.Contains("bot") || agent.Contains("preview") || agent.Contains("sogou") || agent.Contains("yahoo")
                    || agent.Contains("google") || agent.Contains("indexer") || agent.Contains("site");
            }
            catch { }
            return result;
        }
    }
}