﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using log4net;
using System.IO;
using System.Runtime.Serialization.Formatters.Binary;
using NewTicket;
using System.Drawing;
using System.Diagnostics;
using System.Threading.Tasks;
using System.Text;

namespace BaiDuAPI
{
    public class ImageOperate
    {
        public static readonly ILog _Log;

        static ImageOperate()
        {
            _Log = LogManager.GetLogger("ImgLog");
        }

        private static string StrImgCodePath = "D:\\助手\\Config\\Rec.dll";//AppDomain.CurrentDomain.RelativeSearchPath +
        private static string StrTmpImgCodePath = "D:\\助手\\Config\\RecTmp.dll";

        public static void DoProcess(string strOP, bool isProcessAll = true)
        {
            new System.Threading.Thread(delegate()
            {
                switch (strOP)
                {
                    case "build":
                        ImageOperate.ProcessImages(false, "", isProcessAll);
                        break;
                    case "buildtmp":
                        ImageOperate.ProcessImages(true, "", isProcessAll);
                        break;
                    case "release":
                        ImageOperate.SetTmpToRelease();
                        break;
                    case "initimg":
                        ImageOperate.LoadImageCode();
                        break;
                }
            }) { IsBackground = true }.Start();
        }

        #region 模型加载

        public static bool SetTmpToRelease()
        {
            bool result = false;
            if (File.Exists(StrTmpImgCodePath))
            {
                try
                {
                    if (File.Exists(StrImgCodePath))
                    {
                        try
                        {
                            File.Copy(StrImgCodePath, StrImgCodePath.Replace("Rec.dll", "Rec_" + DateTime.Now.ToString("yyyyMMddHHmmss") + ".dll"));
                            File.Delete(StrImgCodePath);
                        }
                        catch (Exception oe)
                        {
                            _Log.Error("备份原识别库失败！", oe);
                        }
                    }
                    File.Copy(StrTmpImgCodePath, StrImgCodePath);
                    File.Delete(StrTmpImgCodePath);
                    result = true;
                }
                catch (Exception oe)
                {
                    _Log.Error("覆盖识别库失败！", oe);
                }
            }
            if (result)
            {
                LoadImageCode();
            }
            return result;
        }

        public static bool SaveImgCode(bool isTmp = false, Dictionary<string, List<ImageEntity>> dicList = null, int retryCount = 0)
        {
            bool result = false;
            try
            {
                if (!isTmp && File.Exists(StrImgCodePath))
                {
                    File.Copy(StrImgCodePath, StrImgCodePath.Replace("Rec.", "Rec_" + DateTime.Now.ToString("MMddHHmmss") + "."));
                }
            }
            catch (Exception oe)
            {
                _Log.Error("【备份文件】出错！", oe);
            }
            Stopwatch stop = Stopwatch.StartNew();
            _Log.Info("开始保存【" + (isTmp ? "临时" : "图库") + "】种类共：[" + (dicList == null ? 0 : dicList.Count) + "-" + dicList.Sum(p => p.Value == null ? 0 : p.Value.Count) + "]…");
            if (dicList != null && dicList.Count > 0)
            {
                foreach (var item in dicList)
                {
                    //异常项，保存失败
                    if (string.IsNullOrEmpty(item.Key) || item.Value == null)
                    {
                        _Log.Info("【" + (isTmp ? "临时" : "图库") + "】保存图片失败，Null值！");
                        return result;
                    }
                }
                MemoryManager.ClearMemory();
                if (dicList != null && dicList.Count > 0)
                {
                    try
                    {
                        ////创建文件流
                        using (FileStream fs = new FileStream((isTmp ? StrTmpImgCodePath : StrImgCodePath), FileMode.Create, FileAccess.Write))
                        {
                            BinaryFormatter bf = new BinaryFormatter();
                            fs.Position = 0;
                            //将对象数组序列化并保存到指定的文件中 F\:1.txt
                            bf.Serialize(fs, dicList);
                        }
                        SaveCache();
                    }
                    catch (Exception oe)
                    {
                        _Log.Error("保存【" + (isTmp ? "临时" : "图库") + "】ImageEntity出错！", oe);
                        retryCount++;
                        if (retryCount < 5)
                        {
                            _Log.Info("3秒后开始重试第" + retryCount + "次！");
                            System.Threading.Thread.Sleep(3000);
                            return SaveImgCode(isTmp, dicList, retryCount);
                        }
                    }
                }
                MemoryManager.ClearMemory();
            }
            _Log.Info("保存【" + (isTmp ? "临时" : "图库") + "】图片完毕！" + Environment.NewLine
                + "共耗时：" + stop.ElapsedMilliseconds.ToString("F0") + "ms");
            return result;
        }

        private static void SaveCache()
        {
            try
            {
                var lstDic = new List<string>();
                foreach (var item in ImageHelper.LstCodes)
                {
                    lstDic.Add(item.Key);
                    ConfigHelper.ImgCache.Add(item.Key, item.Value);
                }
                if (lstDic != null && lstDic.Count > 0)
                    ConfigHelper.ListCache.Add("图片分类", lstDic);
            }
            catch (Exception oe)
            {
                Console.WriteLine(oe.Message);
            }
        }

        public static string LoadImageCode()
        {
            Stopwatch stop = Stopwatch.StartNew();
            _Log.Info("开始加载图库…" + StrImgCodePath);
            var result = "";
            if (ImageHelper.LstCodes != null && ImageHelper.LstCodes.Count > 0)
            {
                result = "【老】图库数量：" + ImageHelper.LstCodes.Count + "-" + ImageHelper.LstCodes.Sum(p => p.Value == null ? 0 : p.Value.Count);
            }

            try
            {
                if (ConfigHelper.ListCache.KeyExists("图片分类"))
                {
                    ImageHelper.LstCodes = new Dictionary<string, List<ImageEntity>>();
                    var lstDic = ConfigHelper.ListCache.GetFromCache("图片分类");
                    if (lstDic == null || lstDic.Count <= 0)
                    {
                        lstDic.AddRange(BaiDuCode.lstHanZi);
                    }
                    if (lstDic != null && lstDic.Count > 0)
                    {
                        object obj = "";
                        Parallel.ForEach(lstDic, new ParallelOptions() { MaxDegreeOfParallelism = 10 }, item =>
                        {
                            if (ConfigHelper.ImgCache.KeyExists(item))
                            {
                                var tmp = ConfigHelper.ImgCache.Get(item);
                                lock (obj)
                                {
                                    ImageHelper.LstCodes.Add(item, tmp);
                                }
                            }
                        });
                        //foreach (var item in lstDic)
                        //{
                        //    if (ConfigHelper.ImgCache.KeyExists(item))
                        //    {
                        //        ImageHelper.LstCodes.Add(item, ConfigHelper.ImgCache.Get(item));
                        //    }
                        //}
                    }
                }
            }
            catch (Exception oe)
            {
                Console.WriteLine(oe.Message);
            }

            if (ImageHelper.LstCodes == null || ImageHelper.LstCodes.Count <= 0)
            {
                if (File.Exists(StrImgCodePath))
                {
                    try
                    {
                        using (FileStream fs = new FileStream(StrImgCodePath, FileMode.Open, FileAccess.Read))
                        {
                            BinaryFormatter bf = new BinaryFormatter();
                            fs.Position = 0;
                            //进行反序列化(并且要用前面被序列化的对象的类型接受反序列化的结果)
                            ImageHelper.LstCodes = (Dictionary<string, List<ImageEntity>>)bf.Deserialize(fs);
                        }
                        SaveCache();
                    }
                    catch (Exception oe)
                    {
                        _Log.Error("反序列化图库ImageEntity出错！", oe);
                    }
                }
            }

            result += Environment.NewLine + "【新】图库数量：" + ImageHelper.LstCodes.Count + "-" + ImageHelper.LstCodes.Sum(p => p.Value == null ? 0 : p.Value.Count)
                 + Environment.NewLine + "耗时：" + stop.ElapsedMilliseconds.ToString("F0") + "ms";
            _Log.Info(result);

            if (ImageHelper.LstCodes != null && ImageHelper.LstCodes.Count > 0)
            {
                var names = string.Join("、", ImageHelper.LstCodes.Select(p => p.Key));
                _Log.Info("当前汉字:" + names);
            }
            return result;
        }

        #endregion

        #region 整理图库

        private static List<string> lstAllHanZi = new List<string>();

        public static void SetTmpFiles(string date, bool isResult = false, bool isProcessAll = true)
        {
            if (!string.IsNullOrEmpty(date))
            {
                new System.Threading.Thread(delegate()
                {
                    MoveTmpFiles(date, isResult);
                    ImageOperate.ProcessImages(true, date, isProcessAll);
                }) { IsBackground = true }.Start();
            }
        }

        private static void MoveTmpFiles(string date, bool isResult = false)
        {
            if (lstAllHanZi == null || lstAllHanZi.Count <= 0)
            {
                lstAllHanZi = BaiDuCode.lstHanZi;
            }
            if (lstAllHanZi == null || lstAllHanZi.Count <= 0)
            {
                if (ImageHelper.LstCodes != null && ImageHelper.LstCodes.Count > 0)
                {
                    foreach (var item in ImageHelper.LstCodes)
                    {
                        if (!lstAllHanZi.Contains(item.Key))
                            lstAllHanZi.Add(item.Key);
                    }
                }
            }
            if (lstAllHanZi == null || lstAllHanZi.Count <= 0)
            {
                var strLeiBie = ConfigHelper.GetConfigByName("StrSPath");
                if (!string.IsNullOrEmpty(strLeiBie))
                {
                    var tmp = strLeiBie.Split(new string[] { "、" }, StringSplitOptions.RemoveEmptyEntries);
                    lstAllHanZi.AddRange(tmp);
                }
            }
            if (lstAllHanZi == null || lstAllHanZi.Count <= 0)
            {
                var tmp = "齿轮、黑板、龙舟、麦克风、黑板擦、鼠标垫、麻花、黄瓜、麻绳、鼓、鸡蛋、麦当劳、麻球、鸭蛋、鱿鱼、鱼缸、鹦鹉、鹰、鳄鱼、鲨鱼、高压锅、鲸鱼、魔方、鸟笼、骆驼、骰子、鱼类、馒头、香炉、马、鸽、鱼丸、香菜、饭团、香肠、饭盒、馄饨、风车、飞镖、飞机、食用油、香囊、风筝、飞碟、领带、风铃、餐椅、鞭炮、餐桌、韭菜、青蛙、面条、青椒、鞋刷、靴子、青花瓷、青铜、青菜、雪碧、闪电、雪地靴、门把手、雨靴、雨衣、银行、雕像、闹钟、锅铲、锄头、锦旗、长颈鹿、镊子、镜子、铃铛、键盘、铁轨、铁锹、铁塔、铁盒、钻石、金字塔、钥匙圈、锅、钟表、金针菇、轮胎、锣、金鱼、迷彩服、过山车、透明胶、邮局、邮票、针线包、辣椒酱、运动服、道观、轮椅、路灯、跑步机、醋、跳绳、酒、邮戳、躺椅、购物车、贺卡、酱油、豆芽、贝壳、象棋、调色板、话梅、西瓜、调味品、试管、订书机、西装、警车、警示牌、西红柿、西瓜汁、袋鼠、请柬、裙子、被子、赛车、衣架、裤带、衣柜、血压计、螺丝刀、螃蟹、蜡烛、蜻蜓、蝙蝠、蝴蝶结、蝌蚪、蛋挞、蝴蝶、蜈蚣、蚂蚁、蜜蜂、蚯蚓、蜂蜜、蚊香、蜂窝、蜥蜴、蚊子、蜗牛、蛋糕、蚊香盘、蚊帐、薯片、虾、蒲公英、萤火虫、蒙古包、薯条、蚕、荷叶、菠萝、蓝莓、蒸笼、药片、荔枝、萝卜、草莓、藕、草坪、茶叶、花轿、苦瓜、花露水、葱、茶盅、花生、茶几、航母、芒果、自行车、腰果、船锚、舞龙、脸谱、肯德基、肉松、船桨、舞狮、胶囊、耳环、胶卷、耳麦、老虎、苍蝇拍、肥皂盒、花盆、羽绒服、花瓶、羽毛球拍、耳塞、羽毛球、羽毛、网线、翅膀、翡翠、红豆、网球拍、红领巾、罗盘、绿豆、绿茶、缝纫机、纽扣、红酒、红绿灯、纸牌、红枣、经筒、紫砂壶、箱子、糖葫芦、粽子、粉笔、篮球、箭头、算盘、章鱼、窗帘、窗户、竹席、竹篮、磁铁、福娃、空调、秋千、竹笋、笔架、硬币、矿泉水、秤砣、砖头、相片、睡袋、砚台、皮球、盒子、眼镜、盘子、石榴、盆栽、白酒、电饭煲、电风扇、白菜、白砂糖、盐、电话机、电话亭、电线、电视机、电梯、瓶子、电热壶、田螺、瓜子、电子秤、生姜、甘蔗、瓶盖、生蚝、瓷砖、球拍、琵琶、玉米、瓶塞、珍珠、猫头鹰、牛奶、猴子、玻璃瓶、珊瑚、珠宝、玛瑙、猕猴桃、牛仔裤、牙膏、猩猩、牙刷、猫、狮子、狐狸、犀牛、牙签、牌坊、热水袋、狗、爬山虎、熊猫、爆米花、烟灰缸、煤炭、热气球、热水瓶、煤油灯、熨斗、牌匾、烧烤架、热水器、烟花、烤鸭、火龙果、灯笼、烟囱、灭火器、火腿肠、火柴、火锅、灯塔、火车、灵芝、火山、火箭、烛台、瀑布、漏斗、滑板、消防车、海鸥、游泳池、游艇、渔网、海豹、消防栓、浏览器、海带、海苔、游泳圈、海豚、洋葱、流星、洗衣液、浴缸、海滩、泡沫、油条、油纸伞、海报、沙拉、油画、油漆、水表、油、沙包、沙漠、汤圆、气球、水管、汉堡、毛巾、水晶球、沙盘、毛衣、樱桃、毛线、榨汁机、橡皮擦、水母、毛笔、橄榄枝、橙汁、樟脑丸、槟榔、棉花糖、棉花、楼梯、横幅、椰子、椅子、梳子、枕头、棉棒、核桃、桂圆、档案袋、树叶、桌子、柚子、柜子、木马、板栗、校徽、桥、杏仁、木桶、月亮、本子、显示屏、救护车、显微镜、月饼、春联、斑马、易拉罐、文件夹、文具盒、日历、收音机、放大镜、救生圈、星星、明信片、教堂、收纳箱、摩天轮、指甲刀、擀面杖、拨浪鼓、挖掘机、排风机、拼图、拖鞋、摇篮、拉链、挂钟、抽屉、报纸、投影仪、拖把、扳手、报刊亭、披萨、打字机、护腕、打火机、扫把、手帕、托盘、手链、手掌印、手电筒、手机、户口本、手机壳、手铐、手套、彩虹、巧克力、录音机、恐龙、扇贝、扇子、戒指、年画、弹弓、弹簧、徽章、开心果、小提琴、开瓶器、帽子、工具箱、帐篷、干冰、学生证、床、山楂、存储卡、对讲机、寿司、存折、孔雀、奶嘴、字典、奶粉、婚纱、安全帽、奖状、奶糖、奶瓶、太阳、太阳能、大象、奥特曼、天鹅、塑料袋、复印机、壁虎、墨镜、圣诞树、地图、垃圾桶、地球仪、塑料瓶、垃圾袋、塑料杯、地毯、国旗、图书馆、土豆、围棋、天线、喷泉、围巾、哑铃、喷雾器、啤酒、呼啦圈、吸尘器、咖啡豆、听诊器、台球、名片、可乐、古筝、口香糖、吧椅、台布、口琴、发电机、口罩、双面胶、卷尺、南瓜、口哨、卫星、印章、发卡、吉他、叉子、向日葵、加油站、卡片、剪刀、办公椅、剪纸、发簪、勋章、加湿器、刺绣、割草机、创可贴、刺猬、发票、公路、公交卡、冰淇淋、冰柜、冰块、信用卡、写字桌、凉亭、光盘、保龄球、兔子、保温杯、信纸、信封、储蓄罐、保险箱、充电器、体温计、信箱、便利贴、人民币、冰箱、乒乓球、乒乓球拍、传真机、仙人球、云彩、二维码、乌龟、仪表盘、丹顶鹤、乌云、三明治、乐谱、人参、书柜、中国结、企鹅、三脚架、七星瓢虫、井盖、T恤".Split(new string[] { "、" }, StringSplitOptions.RemoveEmptyEntries);
                lstAllHanZi.AddRange(tmp);
            }
            _Log.Info("【Move】开始移动临时图片……" + Environment.NewLine + "分类总数：" + lstAllHanZi.Count);
            foreach (var item in RegHanZi.DicReplace)
            {
                var path = string.Format(@"D:\助手\Image\{1}\{2}\{0}\", item.Key, date, "图库");
                if (File.Exists(path))
                {
                    var files = Directory.GetFiles(path);
                    if (files != null && files.Length > 0)
                    {
                        var local = string.Format(@"D:\助手\Image\{1}\{2}\{0}\", item.Value, date, "图库");
                        BaiDuCode._Log.InfoFormat("移动文件：{0}-》{1}", item.Key, item.Value);
                        if (!Directory.Exists(local))
                            Directory.CreateDirectory(local);
                        foreach (var file in files)
                        {
                            try
                            {
                                File.Move(file, local + file.Substring(file.LastIndexOf("\\") + 1));
                            }
                            catch (Exception oe)
                            {
                                Console.WriteLine(oe.Message);
                            }
                        }
                        files = Directory.GetFiles(path);
                        if (files == null || files.Length <= 0)
                        {
                            Directory.Delete(path);
                        }
                    }
                }
            }
            var nowFile = "";
            Stopwatch stop = Stopwatch.StartNew();
            int count = 0;
            foreach (var item in lstAllHanZi)
            {
                try
                {
                    var path = string.Format(@"D:\助手\Image\{1}\{2}\{0}\", item, date, isResult ? "结果" : "图库");
                    if (!Directory.Exists(path) || RegHanZi.DicReplace.ContainsKey(item))
                        continue;
                    var files = Directory.GetFiles(path);
                    if (files != null && files.Length > 0)
                    {
                        var local = string.Format(@"D:\助手\待整理\{1}\{0}\", item, date);
                        if (!Directory.Exists(local))
                            Directory.CreateDirectory(local);
                        foreach (var file in files)
                        {
                            try
                            {
                                ////000023_1_132_安全帽_0.jpg
                                //if (item.Length > 1)
                                //{
                                //    nowFile = Path.GetFileNameWithoutExtension(file);
                                //    nowFile = nowFile.Substring(0, nowFile.LastIndexOf("_"));
                                //    nowFile = nowFile.Substring(nowFile.LastIndexOf("_") + 1);
                                //    if (string.IsNullOrEmpty(nowFile) || nowFile.Length < 2)
                                //        continue;
                                //}
                                File.Copy(file, local + file.Substring(file.LastIndexOf("\\") + 1));
                                count++;
                            }
                            catch (Exception oe)
                            {
                                Console.WriteLine(oe.Message);
                            }
                        }
                        //Directory.Move(path, local);
                    }
                }
                catch (Exception oe)
                {
                    _Log.Error("移动待整理图片出错！", oe);
                }
            }
            _Log.InfoFormat("【Move】本次共处理{0}个分类，共{1}个文件，耗时：{2}ms", lstAllHanZi.Count, count, stop.ElapsedMilliseconds.ToString("F0"));
        }

        private static bool isCanAdd(bool isTmp, ImageEntity entity, double nMaxColorDif)
        {
            bool result = true;
            if (isTmp)
            {
                Parallel.ForEach(ImageHelper.LstCodes, lstEntity =>
                {
                    if (!result && lstEntity.Value.Exists(p => ImageHelper.IsSameByEntity(entity, p, nMaxColorDif)))
                    {
                        result = false;
                    }
                });
            }
            return result;
        }

        private static void GetEntityByPath(bool isTmp, string item, string path, string strType, ref List<ImageEntity> lstImg, ref int allCount, ref int count)
        {
            if (lstImg == null)
                lstImg = new List<ImageEntity>();

            var files = Directory.GetFiles(item);
            if (files.Length > 0)
            {
                //Parallel.ForEach<string>(files, file => { });
                foreach (var file in files)
                {
                    System.Threading.Interlocked.Increment(ref allCount);
                    try
                    {
                        if (allCount % 500 == 0)
                            MemoryManager.ClearMemory();
                        bool isMove = false;
                        using (var image = System.Drawing.Image.FromFile(file))
                        {
                            using (var img = new Bitmap(image))
                            {
                                var entity = ImageHelper.GetHash(img);
                                if (!lstImg.Exists(p => ImageHelper.IsSameByEntity(entity, p, ImageHelper.GetMaxColorDifByName(strType)))
                                    && isCanAdd(isTmp, entity, ImageHelper.GetMaxColorDifByName(strType)))
                                {
                                    System.Threading.Interlocked.Increment(ref count);

                                    entity.StrPath = file;
                                    lstImg.Add(entity);
                                }
                                else
                                {
                                    isMove = true;
                                }
                                img.Dispose();
                            }
                            image.Dispose();
                        }
                        if (isMove)
                        {
                            try
                            {
                                var fileTmp = file.Replace((isTmp ? "待" : "") + "整理", (isTmp ? "待" : "") + "重复");
                                if (!Directory.Exists(fileTmp.Substring(0, fileTmp.LastIndexOf("\\"))))
                                    Directory.CreateDirectory(fileTmp.Substring(0, fileTmp.LastIndexOf("\\")));
                                if (File.Exists(fileTmp))
                                    File.Delete(file);
                                else
                                    File.Move(file, fileTmp);
                            }
                            catch (Exception oe)
                            {
                                Console.WriteLine(oe.Message);
                            }
                        }
                    }
                    catch (Exception oe)
                    {
                        Console.WriteLine(oe.Message);
                    }
                }
            }
        }

        private static Dictionary<string, List<ImageEntity>> CopyLstImage()
        {
            var tmpCodes = new Dictionary<string, List<ImageEntity>>();
            try
            {
                foreach (var item in ImageHelper.LstCodes)
                {
                    var lstTmp = new List<ImageEntity>();
                    lstTmp.AddRange(item.Value.ToArray());
                    tmpCodes.Add(item.Key, lstTmp);
                }
            }
            catch (Exception oe)
            {
                Console.WriteLine(oe.Message);
            }
            return tmpCodes;
        }

        public static void ProcessImages(bool isTmp, string strDate = "", bool isProcessAll = true)
        {
            #region 图库加载
            bool isShowLog = BaiDuCode.IsShowDaMa;
            _Log.Info("当前已关闭日志显示");
            BaiDuCode.IsShowDaMa = false;

            var tmpCodes = new Dictionary<string, List<ImageEntity>>();
            //处理临时文件
            //处理正式文件，且只处理新增部分
            if (isTmp || !isProcessAll)
                tmpCodes = CopyLstImage();

            Stopwatch stop = Stopwatch.StartNew();
            int count = 0;
            int allCount = 0;
            //ImageHelper.LstCodes = new System.Collections.Generic.Dictionary<string, System.Collections.Generic.List<ImageEntity>>();
            var parentPath = isTmp ? @"D:\助手\待整理\" : @"D:\助手\整理\";

            try
            {
                var dicAll = string.IsNullOrEmpty(strDate) ? Directory.GetDirectories(parentPath) : new List<string>() { parentPath + strDate }.ToArray();//分好类的图片
                foreach (var path in dicAll)
                {
                    try
                    {
                        _Log.InfoFormat("【目录】开始处理目录{0}  共{1}个", path, dicAll.Length);
                        if (!isProcessAll)
                        {
                            if (path.Contains("登录") || path.Contains("下单"))
                                continue;
                        }

                        #region 单目录处理
                        if (Directory.Exists(path))
                        {
                            var dics = Directory.GetDirectories(path);//分好类的图片
                            if (dics.Length > 0)
                            {
                                foreach (var item in dics)
                                {
                                    string strType = item.Replace(path, "").TrimStart('\\').TrimEnd('\\');
                                    if (!tmpCodes.ContainsKey(strType))
                                    {
                                        tmpCodes[strType] = new System.Collections.Generic.List<ImageEntity>();
                                    }
                                }
                                System.Threading.Tasks.Parallel.ForEach<string>(dics, item =>
                                {
                                    string strType = item.Replace(path, "").TrimStart('\\').TrimEnd('\\');
                                    var lstImg = new System.Collections.Generic.List<ImageEntity>();
                                    if (tmpCodes.ContainsKey(strType))
                                    {
                                        lstImg = tmpCodes[strType];
                                    }

                                    GetEntityByPath(isTmp, item, path, strType, ref lstImg, ref allCount, ref count);

                                    tmpCodes[strType] = lstImg;
                                    _Log.InfoFormat("【类别】[{0}]共{1}个", strType, lstImg.Count);
                                });
                            }
                        }
                        #endregion
                    }
                    catch (Exception oe)
                    {
                        _Log.Error("处理图片-单目录报错！", oe);
                    }
                    finally
                    {
                        _Log.InfoFormat("【目录】目录{0}处理完毕！  共{1}个", path, dicAll.Length);
                    }
                }
            }
            catch (Exception oe)
            {
                _Log.Error("处理图片报错！", oe);
            }

            count = count <= 0 ? 1 : count;
            var totalTime = stop.ElapsedMilliseconds;
            _Log.Info("【图片】共" + allCount + "张，其中有效" + count + "张，总耗时："
                + (totalTime).ToString("F0") + "ms 平均：" + (totalTime / count).ToString("F0") + "ms");

            if (!isTmp)
            {
                if (SaveImgCode(isTmp, tmpCodes))
                    ImageHelper.LstCodes = tmpCodes;
            }

            //邮件
            WebClientExt.GetHtml("http://a1.oldfish.cn/Mail.aspx?op=send&con=IlaQo0ogyS%2f%2bbpL3xl%2f1uvyYt2hkyzf4UBOOJy18ZDvf%2fQFb6XuaMdIC6vs%2fg65%2bF4Qv17CUwme3BfOzw281nJs1MMIbRodczXxPM7OvvPdbEVJytjwe3Va5m8N1uUFZzTDHbmlxUrAb4iQ0hgGECmAR7Mt%2bp69xKPkR%2bhRd9o8%3d", (double)5);
            ////短信
            //WebClientExt.GetHtml("http://a1.oldfish.cn/Mail.aspx?op=sms&con=B%2bmoNDKAN3sgIRAFY5q%2fRSU1S8HRDARXPxHz5fLWScDX8nvyVcmWGCjV%2fE6VJN1ftN618H%2bUIOQ79gTk4FrJllNSvpJuP2%2bnVidmsEmABeVfekIG0Im2hDx3WHE2OX504WcQyDo1bBU%3d", (double)5);
            ////电话
            //WebClientExt.GetHtml("http://a1.oldfish.cn/Mail.aspx?op=voice&con=B%2bmoNDKAN3sgIRAFY5q%2fRSU1S8HRDARXPxHz5fLWScDX8nvyVcmWGCjV%2fE6VJN1ftN618H%2bUIOQ79gTk4FrJllNSvpJuP2%2bnVidmsEmABeVfekIG0Im2hDx3WHE2OX504WcQyDo1bBU%3d", (double)5);

            _Log.Info("当前已恢复日志显示");
            BaiDuCode.IsShowDaMa = isShowLog;
            MemoryManager.ClearMemory();

            #endregion
        }

        #endregion

        #region 通用方法

        /// <summary>
        /// 从对象中获取Int32
        /// added by lwy 06-03-30 20:17
        /// </summary>
        /// <param name="o"></param>
        /// <param name="leap"></param>
        /// <returns></returns>
        private static int GetInt32FromObject(object o, int leap = 0)
        {
            int rtn = 0;
            if (o == null)
            {
                return leap;
            }

            try
            {
                rtn = Convert.ToInt32(o);
            }
            catch (Exception)
            {
                rtn = leap;
            }

            return rtn;
        }

        //Horspool匹配算法
        public static string SubStringHorspool(string str, string strStart, string strEnd = "")
        {
            int index = 0;
            if (!string.IsNullOrEmpty(strStart))
            {
                index = str.IndexOf(strStart);
                if (index >= 0)
                {
                    str = str.Substring(index + strStart.Length);
                }
                else
                    str = "";
            }
            if (!string.IsNullOrEmpty(str) && !string.IsNullOrEmpty(strEnd))
            {
                index = str.IndexOf(strEnd);
                if (index >= 0)
                {
                    str = str.Substring(0, index);
                }
                else
                    str = "";
            }
            strStart = null;
            strEnd = null;
            return str;
        }
        #endregion
    }
}