﻿using CommonLib;
using System;
using System.Collections.Generic;
using System.Collections.Specialized;
using System.IO;

namespace TableOcr
{
    /// <summary>
    /// https://www.etoplive.com/products/tybg.do
    /// </summary>
    public class YiTuRec : BaseTableRec
    {
        public YiTuRec()
        {
            OcrType = TableOcrType.译图;
            MaxExecPerTime = 20;
            //IsSupportUrlOcr = true;

            LstJsonPreProcessArray = new List<object>() { "result", "type=table", "data" };
            LstJsonNextProcessArray = new List<object>() { "|Merge|" };
            //LstJsonResultProcessArray = new List<object>() { "|Merge|" };
            LstRowIndex = new List<object>() { "row_no" };
            LstColumnIndex = new List<object>() { "col_no" };
            LstJsonResultProcessArray = new List<object>() { "data" };
            RowIndexIsArray = false;
            IsRowIndexAddOne = true;
            IsSupportUrlOcr = true;
        }

        protected override string GetHtml(OcrContent content)
        {
            var url = "https://www.etoplive.com/trial/recognizePage.srvc?fileapi";
            var values = new NameValueCollection() {
                { "appKey","tybg"},
                { "catalogId",""},
                { "token",""},
                { "_files","1." + content.fileExt},
            };
            string result;
            if (!string.IsNullOrEmpty(content.url))
            {
                values.Set("urlPath", content.url);
                result = PostFile(url, null, values);
            }
            else
            {
                var file = new UploadFileInfo()
                {
                    Name = "files",
                    Filename = "1." + content.fileExt,
                    ContentType = ApplicationTypeHelper.GetApplicationType(content.fileExt),
                    Stream = new MemoryStream(Convert.FromBase64String(content.strBase64))
                };
                result = PostFile(url, new[] { file }, values);
            }
            return result;
        }

        public override string GetHtmlByUrl(OcrContent content)
        {
            return GetHtml(content);
        }

    }
}