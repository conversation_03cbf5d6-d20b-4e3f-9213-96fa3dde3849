﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.IO;

namespace BaiDuAPI
{
    public class ConfigHelper
    {
        public static List<HanZiRecType> lstHanZiRecType = new List<HanZiRecType>() { HanZiRecType.BaiDuAPI };

        public static ImageCache ImgCache { get; set; }
        public static StrCache StrCache { get; set; }
        public static ListCache ListCache { get; set; }
        public static CountCache CountCache { get; set; }
        public static bool IsEnableCode { get; set; }
        public static bool IsGoogleDevEnable { get; set; }
        public static bool IsGoogleAPIEnable { get; set; }
        public static bool IsRecOrder { get; set; }
        public static bool IsCanRecImg { get; set; }
        public static string RecHanZiHost { get; set; }
        public static string RecImgHost { get; set; }
        public static string SiteFlag { get; set; }
        public static bool IsWait { get; set; }
        public static int NWaitSecond { get; set; }
        public static bool IsOnInit = false;
        public static object LockObj = "";

        public static string StrCodeBasePath = "D:\\助手\\Config\\";
        public static string StrImagePath = "D:\\助手\\Image\\";
        public static string StrFixImagePath = "D:\\助手\\待整理\\";
        public static string StrFixOKImagePath = "D:\\助手\\用户整理-OK\\";
        public static string StrFixErrorImagePath = "D:\\助手\\用户整理-Error\\";
        public static string StrFixImageGuiDangPath = "D:\\助手\\整理\\";
        public static string StrBlankImagePath = "D:\\助手\\Config\\blank.jpg";
        public static string StrLocalLoginCDSPath = "D:\\助手\\Config\\cds_login.cds";
        public static string StrLocalOrderCDSPath = "D:\\助手\\Config\\cds_order.cds";

        public static bool IsCanDaMa
        {
            get
            {
                if (DateTime.Now.Hour < 6)
                    return false;
                return true;
            }
        }

        public static bool GetBConfigByName(string strName, string defaultStr = "true")
        {
            bool result = GetConfigByName(strName).ToLower().Equals(defaultStr);
            return result;
        }

        public static int GetNConfigByName(string strName, int nDefault = 1000)
        {
            var result = 0;
            if (!int.TryParse(GetConfigByName(strName), out result))
            {
                result = nDefault;
            }
            return result;
        }

        public static string GetConfigByName(string strName)
        {
            string result = "";
            try
            {
                result = System.Configuration.ConfigurationManager.AppSettings[strName];
                if (string.IsNullOrEmpty(result))
                {
                    result = "";
                }
            }
            catch (Exception oe)
            {
                Console.WriteLine(oe.Message);
            }
            return result;
        }

        private static string LoadFileContext(string fileName)
        {
            string result = "";
            var file = StrCodeBasePath + fileName;
            if (File.Exists(file))
            {
                result = File.ReadAllText(file, System.Text.Encoding.UTF8);
            }
            else if (File.Exists(fileName))
            {
                result = File.ReadAllText(fileName, System.Text.Encoding.UTF8);
            }
            return result;
        }

        public static string LoadFileBase64Context(string fileName)
        {
            string result = "";
            var file = StrCodeBasePath + fileName;

            byte[] byt = null;
            try
            {
                if (File.Exists(file))
                {
                    byt = File.ReadAllBytes(file);
                }
                else if (File.Exists(fileName))
                {
                    byt = File.ReadAllBytes(file);
                }
                if (byt != null && byt.Length > 0)
                    result = Convert.ToBase64String(byt);
            }
            catch (Exception oe)
            {
                BaiDuCode._Log.Error("加载识别库文件失败！", oe);
            }
            byt = null;
            return result;
        }

        public static Dictionary<string, string> GetListByNameLength(Dictionary<string, string> dicTmp)
        {
            var tmpReplace = new Dictionary<string, string>();
            try
            {

                foreach (var item in dicTmp.OrderByDescending(p => p.Key.Length))
                {
                    tmpReplace.Add(item.Key, item.Value);
                }
            }
            catch (Exception oe)
            {
                tmpReplace = new Dictionary<string, string>();
                BaiDuCode._Log.Error("加载规则出错！", oe);
            }
            return tmpReplace;
        }

        private static void LoadWords()
        {
            var str = LoadFileContext("Words.txt");//ConfigurationManager.AppSettings["StrAllWord"];
            BaiDuCode.lstHanZi = new List<string>();
            BaiDuCode.lstHanZi.AddRange(str.Split(new string[] { "、" }, StringSplitOptions.RemoveEmptyEntries));
            BaiDuCode.lstHanZi = BaiDuCode.lstHanZi.OrderByDescending(p => p.Length).Distinct().ToList();

            List<char> lstFirst = new List<char>();
            BaiDuCode.lstReFirst = new List<char>();

            foreach (var item in BaiDuCode.lstHanZi)
            {
                if (!lstFirst.Contains(item[0]))
                {
                    lstFirst.Add(item[0]);
                }
                else
                {
                    if (!BaiDuCode.lstReFirst.Contains(item[0]))
                    {
                        BaiDuCode.lstReFirst.Add(item[0]);
                    }
                }
                foreach (var car in item)
                {
                    if (!BaiDuCode.lstDanHanZi.Contains(car))
                    {
                        BaiDuCode.lstDanHanZi.Add(car);
                    }
                }
            }
            lstFirst = null;
            str = null;
            BaiDuCode.lstDanHanZi.Add('T');
            BaiDuCode.lstDanHanZi = BaiDuCode.lstDanHanZi.Distinct().ToList();
        }

        private static void LoadUserWords()
        {
            var replace = LoadFileContext("ExpWords.txt").Replace("，", ",").Replace("。", ",");// ConfigurationManager.AppSettings["StrExWord"];
            var tmpReplace = new Dictionary<string, string>();
            foreach (var item in replace.Split(new string[] { "|", "\n", "\r" }, StringSplitOptions.RemoveEmptyEntries))
            {
                if (!tmpReplace.ContainsKey(item.Substring(0, item.IndexOf(",")).Trim()))
                    tmpReplace.Add(item.Substring(0, item.IndexOf(",")).Trim(), item.Substring(item.IndexOf(",") + 1).Trim());
            }

            try
            {
                replace = LoadFileContext("UserFix.txt").Replace("，", ",").Replace("。", ",");// ConfigurationManager.AppSettings["StrExWord"];
                foreach (var item in replace.Split(new string[] { "|", "\n", "\r" }, StringSplitOptions.RemoveEmptyEntries))
                {
                    if (!tmpReplace.ContainsKey(item.Substring(0, item.IndexOf(",")).Trim()))
                        tmpReplace.Add(item.Substring(0, item.IndexOf(",")).Trim(), item.Substring(item.IndexOf(",") + 1).Trim());
                }
                if (Directory.Exists(StrCodeBasePath + "User\\"))
                {
                    var dirUser = Directory.GetDirectories(StrCodeBasePath + "User\\");
                    if (dirUser.Length > 0)
                    {
                        var lstTmp = new List<string>();
                        lstTmp.AddRange(dirUser);
                        if (lstTmp.Exists(p => p.Contains(DateTime.Now.ToString("MMdd"))))
                        {
                            lstTmp.RemoveAll(p => !p.Contains(DateTime.Now.ToString("MMdd")));
                        }
                        else if (lstTmp.Exists(p => p.Contains(DateTime.Now.AddDays(-1).ToString("MMdd"))))
                        {
                            lstTmp.RemoveAll(p => !p.Contains(DateTime.Now.AddDays(-1).ToString("MMdd")));
                        }
                        else if (lstTmp.Exists(p => p.Contains(DateTime.Now.ToString("\\MM"))))
                        {
                            lstTmp.RemoveAll(p => !p.Contains(DateTime.Now.ToString("\\MM")));
                        }
                        else if (lstTmp.Exists(p => p.Contains(DateTime.Now.AddMonths(-1).ToString("\\MM"))))
                        {
                            lstTmp.RemoveAll(p => !p.Contains(DateTime.Now.AddMonths(-1).ToString("\\MM")));
                        }
                        foreach (var dir in lstTmp)
                        {
                            var files = Directory.GetFiles(dir);
                            if (files.Length > 0)
                            {
                                foreach (var file in files)
                                {
                                    replace = LoadFileContext(file).Replace("，", ",").Replace("。", ",");// ConfigurationManager.AppSettings["StrExWord"];
                                    var lstReplaceWords = replace.Split(new string[] { "|", "\n", "\r" }, StringSplitOptions.RemoveEmptyEntries);

                                    object obj = "";

                                    System.Threading.Tasks.Parallel.ForEach(lstReplaceWords, item =>
                                    {
                                        try
                                        {
                                            if (!string.IsNullOrEmpty(item))
                                            {
                                                var key = item.Substring(0, item.IndexOf(",")).Trim();
                                                bool isChongFu = BaiDuCode.lstHanZi.Exists(p => p.Contains(key) || key.Contains(p));
                                                //if (isChongFu)
                                                //{
                                                //    BaiDuCode._Log.InfoFormat("添加{0}时碰到包含字段：{1}", key, item);
                                                //}
                                                if (!isChongFu)
                                                {
                                                    isChongFu = key.Length == 2 && BaiDuCode.lstHanZi.Exists(p => p.StartsWith(key[1].ToString()))
                                                    && BaiDuCode.lstHanZi.Exists(p => p.EndsWith(key[0].ToString()));
                                                    //if (isChongFu)
                                                    //{
                                                    //    BaiDuCode._Log.InfoFormat("添加{0}时碰到开头和结尾重复字段：{1}", key, item);
                                                    //}
                                                }
                                                if (!isChongFu)
                                                {
                                                    if (!tmpReplace.ContainsKey(key))
                                                    {
                                                        lock (obj)
                                                        {
                                                            tmpReplace.Add(key, item.Substring(item.IndexOf(",") + 1).Trim());
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                        catch (Exception oe)
                                        {
                                            BaiDuCode._Log.Error("加载字典出错！当前项：" + item, oe);
                                        }
                                    });
                                }
                            }
                        }
                    }
                }
                tmpReplace = GetListByNameLength(tmpReplace);
                if (tmpReplace != null && tmpReplace.Count > 0)
                {
                    BaiDuCode.lstHanZiReplace = tmpReplace;
                }
            }
            catch (Exception oe)
            {
                BaiDuCode._Log.Error("加载字典出错！", oe);
            }
            finally
            {
                replace = null;
            }
        }

        private static DateTime dtLastLoad = DateTime.MinValue;

        public static void InitConfig()
        {
            BaiDuCode.IsAutoFix = ConfigHelper.GetBConfigByName("IsAutoFix");
            ConfigHelper.IsEnableCode = ConfigHelper.GetBConfigByName("IsEnableCode");
            ConfigHelper.IsRecOrder = ConfigHelper.GetBConfigByName("IsRecOrder");
            ConfigHelper.IsCanRecImg = ConfigHelper.GetBConfigByName("IsCanRecImg");
            //ConfigHelper.IsCanRecHanZi = ConfigHelper.GetBConfigByName("IsCanRecHanZi");
            ConfigHelper.IsWait = ConfigHelper.GetBConfigByName("IsWait");
            ConfigHelper.NWaitSecond = ConfigHelper.GetNConfigByName("NWaitSecond");
            ConfigHelper.RecHanZiHost = ConfigHelper.GetConfigByName("RecHanZiHost");
            ConfigHelper.RecImgHost = ConfigHelper.GetConfigByName("RecImgHost");
            ConfigHelper.IsGoogleDevEnable = ConfigHelper.GetBConfigByName("IsGoogleDevEnable");
            ConfigHelper.IsGoogleAPIEnable = ConfigHelper.GetBConfigByName("IsGoogleAPIEnable");
            ConfigHelper.SiteFlag = ConfigHelper.GetConfigByName("SiteFlag");

            if (ConfigHelper.IsGoogleAPIEnable)
            {
                ConfigHelper.lstHanZiRecType.Add(HanZiRecType.GoogleAPI);
            }
            if (ConfigHelper.IsGoogleDevEnable)
            {
                ConfigHelper.lstHanZiRecType.Add(HanZiRecType.GoogleDevelop);
            }
        }

        public static string GetFlagName(string strName, string site = "")
        {
            return string.Format((string.IsNullOrEmpty(site) ? ConfigHelper.SiteFlag : site) + strName);
        }

        public static void LoadWordInfo(bool isLoadWord = true)
        {
            if (isLoadWord)
            {
                LoadWords();
                BaiDuCode.lstHanZiReplace = new Dictionary<string, string>();
            }
            new System.Threading.Thread(delegate()
            {
                LoadUserWords();
                if (CustomImageHelper.ProcessOther == null)
                {
                    CustomImageHelper.ProcessOther = new System.Threading.Thread(delegate()
                    {
                        CustomImageHelper.ProcessPic();
                    }) { Priority = System.Threading.ThreadPriority.Highest, IsBackground = true };
                    CustomImageHelper.ProcessOther.Start();
                }
            }) { Priority = System.Threading.ThreadPriority.Highest }.Start();
        }

        public static void InitDaMaInfo()
        {
            //WuYiCode.IsWuYi = ConfigHelper.GetBConfigByName("IsWuYi");
            NewTicket.JiSu.JiSuDaMa.IsJiSu = ConfigHelper.GetBConfigByName("IsJiSu");
            try
            {
                NewTicket.JiSu.JiSuDaMa.LstChaoRenKey = ConfigHelper.GetConfigByName("JiSuDaMaKey").Split(new string[] { "|" }, StringSplitOptions.RemoveEmptyEntries).ToList();
                NewTicket.JiSu.JiSuDaMa.StrChaoRenKey = NewTicket.JiSu.JiSuDaMa.LstChaoRenKey.Count > 0 ? NewTicket.JiSu.JiSuDaMa.LstChaoRenKey[0] : "00040007RPTX7530DEEA0057ZSCT003c";
                NewTicket.JiSu.JiSuDaMa.GetCode(new byte[1000]);
            }
            catch (Exception oe)
            {
                Console.WriteLine(oe.Message);
            }
            try
            {
                NewTicket.NewLocalDaMa.IsLocalCodeEnable = ConfigHelper.GetBConfigByName("IsLocal");
                if (NewTicket.NewLocalDaMa.IsLocalCodeEnable)
                    NewTicket.NewLocalDaMa.Init();
            }
            catch (Exception oe)
            {
                Console.WriteLine(oe.Message);
            }
            //var name = "土豆档案能";
            //BaiDuCode.GetHanZiByPath(name, ref name);
            //BaiDuCode.FixName("土豆档案能", "土豆档案能");
            //RegHanZi.GetNewWords("土豆档案能");
            try
            {
                ImageOperate.LoadImageCode();
            }
            catch { }
        }


        //public static bool IsCanRecHanZi { get; set; }
    }
}