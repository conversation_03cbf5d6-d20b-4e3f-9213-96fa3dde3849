<!DOCTYPE html>
<html lang="en" style="">
<head>
<!--shared:Meta-->
<base href="">
<style>
/*vfs[]:/css/highlight.css*/
</style>
<script>
let DEBUG = false
DEBUG = true /*debug*/
</script>
<style>
/*file:css/app.css */
/*shared:css/ui.css */
/*file:custom.css */
</style>
<!--shared:custom-head-->
</head>
<body class="loading noauth">
<!--shared:custom-body-->

<div class="when-loading"></div>

<div v-scope class="app">

<div class="flex">
    <div id="sidebar" v-scope="Sidebar({ store, routes })"></div>
    <div class="md:pl-sidebar md:w-sidebar overflow-auto flex flex-col flex-1">
        <div class="sticky top-0 z-10 md:hidden pl-1 pt-1 sm:pl-3 sm:pt-3 bg-white">
            <button type="button" @click="transition('sidebar')"    
                    class="-ml-0.5 -mt-0.5 h-12 w-12 inline-flex items-center justify-center rounded-md text-gray-500 hover:text-gray-900 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-indigo-500">
                <span class="sr-only">Open sidebar</span>
                <!---: Heroicon name: outline/menu -->
                <svg class="h-6 w-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
                </svg>
            </button>
        </div>
        <main class="flex-1">
            <div class="pt-2.5 pb-6">
                <div class="absolute top-1 right-2 flex">
                    <div v-if="routes.uiHref()" class="flex items-center" v-scope="UiButton({ routes })"></div>
                    <div v-if="plugins.auth" v-scope="AuthNav({ auth:() => store.auth, logout:() => store.logout() })"></div>
                </div>
                <div v-if="store.op">
                    <div class="w-body md:w-body overflow-auto">
                        <div v-scope="AutoQuery({ store, routes, settings, Forms })"></div>
                        <div v-scope="ModalLookup(store.modalLookup, store, routes, settings)"></div>
                    </div>
                </div>
                <div class="fixed w-body md:w-body top-top-nav h-top-nav overflow-auto" v-else v-scope="Welcome({ store })"></div>
            </div>
        </main>
    </div>
</div>

</div>

<script src="../js/require.js?vfx=hash"></script>
<script src="../js/servicestack-client.js?vfx=hash"></script>
<script src="../types/js?exportTags=locode,auth&vfx=hash"></script>
<script>
Object.assign(window, exports)
/*shared/js:core,Files,Types,createForms */
import { combinePaths, humanify, $1,uniq } from "@servicestack/client"
import { Server } from "../lib/types"
import { setFavIcon, setBodyClass } from "../shared/js/core"

/*gateway:window.Server=MetadataApp({view:'locode',jsconfig:'eccn,inv:false'})?*/
function loadAuth(auth) {
    window.AUTH = !auth || auth.responseStatus ? null : auth
    setBodyClass({ auth })
}
</script>
<script src="../auth?callback=loadAuth&jsconfig=eccn"></script>
<script src="../js/petite-vue.js?vfx=hash"></script>
<script>

document.title = Server.app.serviceName
setFavIcon(Server.ui.brandIcon, combinePaths(Server.app.baseUrl, '/metadata/svg/servicestack.datauri'))

/*shared/js:createApp */
let App = createApp(PetiteVue)
/*plugins:* */

/*files:js/*.js */
</script>

<!--shared:Alert,AuthNav,Brand,CloseButton,Image,Input,Loading,SignIn,ErrorSummary,ModalDialog,PreviewObject,QueryPrefs,FilterColumn-->
<!--files:components/*.html-->
<!--file:custom.html-->

<script minify>
/**: crude workaround for PetiteVue's lack of dynamic components */
function hasNewComponent(dataModel) { return window[`New${dataModel}`] != null }
function hasEditComponent(dataModel) { return window[`Edit${dataModel}`] != null }
let DataModels = uniq(window.Server.api.operations.filter(op => op.dataModel).map(op => op.dataModel.name))

let newComponents = DataModels.filter(hasNewComponent)
let sb = []
sb.push(`<template id='new-component-template'><div v-if="false"></div>`)
newComponents.forEach(x => {
    sb.push(`<div v-else-if="store.opDataModel === '${x}'" v-scope="New${x}({ store, routes, settings, state, save, done })"></div>`)
})
sb.push(`</template>`)

let editComponents = DataModels.filter(hasEditComponent)
sb.push(`<template id='edit-component-template'><div v-if="false"></div>`)
editComponents.forEach(x => {
    sb.push(`<div v-else-if="store.opDataModel === '${x}'" v-scope="Edit${x}({ store, routes, settings, state, save, done })"></div>`)
})
sb.push(`</template>`)
document.write(sb.join(''))

App.components({
    NewComponent: '#new-component-template',    
    EditComponent: '#edit-component-template', 
})
</script>

<script>
App.build({
    app: Server.app,
    ui: Server.ui,
    serviceName: Server.app.serviceName,
    get plugins() { return Server.plugins },
    get api() { return Server.api },
    store,
    get request() { return this.store.op && this.store.op.request },
    humanify,
}).mount()

$1('.when-loading').appendChild($1('#loading-template').content.cloneNode(true))

let sidebarWidth = styleProperty('--sidebar-width')

App.start()
App.nextTick(() => {
    document.body.classList.remove('loading')
    let max = Math.max(...Array.from($$('#sidebar .nav-item')).map(el => el.clientWidth))
    if (max > (240 - 75)) {
       sidebarWidth = (max + 75) + 'px'
       setStyleProperty({ '--sidebar-width': sidebarWidth })
    }
})

/*file:custom.js */
</script>

<!--shared:custom-end-->
</body>
</html>
