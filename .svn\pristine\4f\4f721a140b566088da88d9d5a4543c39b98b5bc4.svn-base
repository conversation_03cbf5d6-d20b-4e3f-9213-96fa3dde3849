﻿namespace CommonLib
{
    public static class RdsCacheHelper
    {
        public static IpLimitManager LimitHelper { get; set; }
        public static UserLoginCache LstAccountCache { get; set; }

        public static void InitLimiter()
        {
            LstAccountCache = new UserLoginCache();
            LimitHelper = new IpLimitManager(ConfigHelper.NMaxExecPerSecond, ConfigHelper.NBlackMSencond, ConfigHelper.NMaxExecBlackSecond, ConfigHelper.IsValidateCodeAmount, false);
        }

        private static UserCodeRecordCache codeRecordCache;

        public static UserCodeRecordCache CodeRecordCache
        {
            get
            {
                if (codeRecordCache == null)
                {
                    codeRecordCache = new UserCodeRecordCache();
                }
                return codeRecordCache;
            }
            set { codeRecordCache = value; }
        }

        private static NoticeQueue noticeQueue;

        /// <summary>
        /// 消息池
        /// </summary>
        public static NoticeQueue NoticeQueue
        {
            get
            {
                if (noticeQueue == null)
                {
                    noticeQueue = new NoticeQueue();
                }
                return noticeQueue;
            }
            set { noticeQueue = value; }
        }

        private static DaMaQueue daMaQueue;

        /// <summary>
        /// 打码池
        /// </summary>
        public static DaMaQueue OcrProcessQueue
        {
            get
            {
                if (daMaQueue == null)
                {
                    daMaQueue = new DaMaQueue();
                }
                return daMaQueue;
            }
            set { daMaQueue = value; }
        }

        private static OcrResultCache ocrResult;

        /// <summary>
        /// OCR结果
        /// </summary>
        public static OcrResultCache OcrResult
        {
            get
            {
                if (ocrResult == null)
                {
                    ocrResult = new OcrResultCache();
                }
                return ocrResult;
            }
            set { ocrResult = value; }
        }

        private static ServerStateCache serverStateCache;

        /// <summary>
        /// Server状态
        /// </summary>
        public static ServerStateCache ServerStateCache
        {
            get
            {
                if (serverStateCache == null)
                {
                    serverStateCache = new ServerStateCache();
                }
                return RdsCacheHelper.serverStateCache;
            }
            set { RdsCacheHelper.serverStateCache = value; }
        }

        private static ServerLogCache serverLogCache;

        /// <summary>
        /// Server状态
        /// </summary>
        public static ServerLogCache ServerLogCache
        {
            get
            {
                if (serverLogCache == null)
                {
                    serverLogCache = new ServerLogCache();
                }
                return serverLogCache;
            }
            set { serverLogCache = value; }
        }

        private static ObjectMessageQueue fileStatusProcessQueue;

        /// <summary>
        /// 文件下载状态Queue
        /// </summary>
        public static ObjectMessageQueue FileStatusProcessQueue
        {
            get
            {
                if (fileStatusProcessQueue == null)
                {
                    fileStatusProcessQueue = new ObjectMessageQueue("FileStatusProcessQueue");
                }
                return fileStatusProcessQueue;
            }
            set { fileStatusProcessQueue = value; }
        }

        private static ObjectMessageQueue fileStatusResultQueue;

        /// <summary>
        /// 文件下载状态Queue
        /// </summary>
        public static ObjectMessageQueue FileStatusResultQueue
        {
            get
            {
                if (fileStatusResultQueue == null)
                {
                    fileStatusResultQueue = new ObjectMessageQueue("FileStatusResultQueue");
                }
                return fileStatusResultQueue;
            }
            set { fileStatusResultQueue = value; }
        }

        //private static DaMaCache daMaHistory;

        ///// <summary>
        ///// 打码量统计
        ///// </summary>
        //public static DaMaCache DaMaHistory
        //{
        //    get
        //    {
        //        if (daMaHistory == null)
        //        {
        //            daMaHistory = new DaMaCache();
        //        }
        //        return daMaHistory;
        //    }
        //    set { daMaHistory = value; }
        //}
    }
}