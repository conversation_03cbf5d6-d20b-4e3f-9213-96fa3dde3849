﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Runtime.InteropServices;
using System.Text;

namespace DaShiCodeServer
{
    public class DaShiRec
    {
        static DaShiRec()
        {
            if (IsEnable && !IsInit)
            {
                IsEnable = LoadCds();
            }
        }

        private static readonly string strSpilt = "|";

        private static bool IsInit = false;

        public static bool IsEnable = true;

        public static string GetRecImg()
        {
            var result = "";
            var base64 = "";
            for (var i = 11; i < 15; i++)
            {
                var byt = File.ReadAllBytes(@"D:\助手\Image\0108\Old\" + i + ".jpg"); //10584306645.jpg");
                result += GetCode(byt, true);
            }
            return result;
        }

        public static string GetCode(byte[] byt, bool isLogin = true)
        {
            var result = "";
            try
            {
                if (byt.Length > 0)
                {
                    var rest = QueryComputerT(byt, byt.Length, true);
                    if (rest >= 0)
                    {
                        var bytResult = new byte[128];
                        if (GetYzmResT(rest, bytResult))
                        {
                            result = Encoding.UTF8.GetString(bytResult);
                            if (result.Contains("|"))
                            {
                                result = result.Substring(result.IndexOf("|") + 1);
                            }
                        }
                    }
                }
            }
            catch (Exception oe)
            {
                Console.WriteLine(oe.Message);
            }
            return result.Trim();
        }

        [DllImport("analyyzm64_zs", CharSet = CharSet.Ansi)]
        public static extern bool LoadCds();

        /// <summary>
        /// 切图函数
        /// 函数原型：int QueryComputerT(const char *pBuff,int nSize,bool bLogin)
        /// </summary>
        [DllImport("analyyzm64_zs", CharSet = CharSet.Ansi)]
        public static extern int QueryComputerT(byte[] pBuff, int nSize, bool bLogin);

        /// <summary>
        /// 切图函数
        /// 函数原型：bool GetYzmResT(int nFlag, char *pResult)
        /// </summary>
        [DllImport("analyyzm64_zs", CharSet = CharSet.Ansi)]
        public static extern bool GetYzmResT(int nFlag, byte[] pResult);


    }
}
