$(document).ready(function () {
  var sms1 = {
    list:
      [
        {
          title: '1W条短信资源包',
          tips: '注册登陆、修改密码、触发通知等',
          rows: '10000',
          price: '0.045',
          point: '450',
          total_price: '450'
        },
        {
          title: '5W条短信资源包',
          tips: '注册登陆、修改密码、触发通知等',
          rows: '50000',
          price: '0.043',
          point: '2150',
          total_price: '2150'
        },
        {
          title: '10W条短信资源包',
          tips: '注册登陆、修改密码、触发通知等',
          rows: '100000',
          price: '0.040',
          point: '4000',
          total_price: '4000'
        },
        {
          title: '50W条短信资源包',
          tips: '注册登陆、修改密码、触发通知等',
          rows: '500000',
          price: '0.038',
          point: '19000',
          total_price: '19000'
        },
        {
          title: '100W条短信资源包',
          tips: '注册登陆、修改密码、触发通知等',
          rows: '1000000',
          price: '0.037',
          point: '37000',
          total_price: '37000'
        },
        {
          title: '300W条短信资源包',
          tips: '注册登陆、修改密码、触发通知等',
          rows: '3000000',
          price: '0.036',
          point: '108000',
          total_price: '108000'
        },
      ],
    init: function () {
      var _this = this;
      var lis = '';
      this.list.forEach(function (item, index) {
        var _html = `
              <div class="backage-items mb-3">
                  <div class="back-title">
                      <div class="pro-main-title fn20 fnbold">${item.title}</div>
                      <p class="pro-sub-title fn14 mt-2">${item.tips}</p>
                  </div>
                  <div class="mt-4">
                      <div class="d-flex align-items-center">
                          <div class="pro-sub-title fn14 mr-5">条数</div> 
                          <div class="pro-main-title fn15 fnbold">${item.rows}条</div>
                      </div>
                      <div class="d-flex align-items-center mt-3 mb-3">
                      <div class="pro-sub-title fn14 mr-5">单价</div> 
                      <div class="pro-main-title fn15 fnbold">${item.price}元/条</div>
                      </div>

                      <div class="d-flex align-items-center" style="opacity:0">
                          <div class="pro-sub-title fn14 mr-5">单价</div> 
                          <div class="pro-main-title fn15 fnbold">${item.price}元/条</div>
                      </div>
                  </div>

                  <div class="d-flex align-items-center mt-4">
                      <div class="permanently-valid fn12 mr-2">永久有效</div>
                      <div class="get-point pro-sub-title fn12">可获得${item.point}积分</div>
                  </div>
                  <div class="pro-price">${item.total_price} <span class="fn14 fnbold">元</span></div>
                  <a href="/console/sms#packages" class="pro-buy">立即购买</a>
              </div>
            `;
        lis += _html;
      });
      $('.promotion-section-1 .pro-sect1-right').empty().html(lis);
      let _html = `
              <div class="backage-items mb-3 add-item">
                  <div class="back-title">
                      <div class="pro-main-title fn20 fnbold">10W条短信首购包</div>
                      <p class="pro-sub-title fn14 mt-2">注册登陆、修改密码、触发通知等</p>
                  </div>
                  <div class="mt-4">
                      <div class="d-flex align-items-center">
                          <div class="pro-sub-title fn14 mr-5">条数&nbsp;&nbsp;&nbsp;</div>
                          <div class="pro-main-title fn15 fnbold">100000条</div>
                      </div>
                      <div class="d-flex align-items-center mt-3 mb-3">
                      <div class="pro-sub-title fn14 mr-5 ">原单价</div> 
                      <div class="pro-sub-title old-price fn15 fnbold">0.040元/条</div>
                      </div>
                      <div class="d-flex align-items-center">
                          <div class="pro-sub-title fn14 mr-5">现单价</div> 
                          <div class="pro-main-title fn15 fnbold">0.033元/条</div>
                      </div>
                  </div>

                  <div class="d-flex align-items-center mt-4">
                      <div class="permanently-valid fn12 mr-2">新用户专享</div>
                      <div class="get-point pro-sub-title fn12">可获得3300积分</div>
                  </div>
                  <div class="pro-price">3300 <span class="fn14 fnbold">元</span> <span class="single-price pro-sub-title fn14 old-price" style="font-family:none">4000元</span> </div>
                  <a href="/console/sms#packages" class="pro-buy">立即购买</a>
              </div>

              <div class="backage-items mb-3 add-item">
                <div class="back-title">
                    <div class="pro-main-title fn20 fnbold">100W条短信首购包</div>
                    <p class="pro-sub-title fn14 mt-2">注册登陆、修改密码、触发通知等</p>
                </div>
                <div class="mt-4">
                    <div class="d-flex align-items-center">
                        <div class="pro-sub-title fn14 mr-5">条数&nbsp;&nbsp;&nbsp;</div>
                        <div class="pro-main-title fn15 fnbold">1000000条</div>
                    </div>
                    <div class="d-flex align-items-center mt-3 mb-3">
                    <div class="pro-sub-title fn14 mr-5 ">原单价</div> 
                    <div class="pro-sub-title old-price fn15 fnbold">0.037元/条</div>
                    </div>
                    <div class="d-flex align-items-center">
                        <div class="pro-sub-title fn14 mr-5">现单价</div> 
                        <div class="pro-main-title fn15 fnbold">0.032元/条</div>
                    </div>
                </div>

                <div class="d-flex align-items-center mt-4">
                    <div class="permanently-valid fn12 mr-2">新用户专享</div>
                    <div class="get-point pro-sub-title fn12">可获得32000积分</div>
                </div>
                <div class="pro-price">32000 <span class="fn14 fnbold">元</span> <span class="single-price pro-sub-title fn14 old-price" style="font-family:none">37000元</span> </div>
                <a href="/console/sms#packages" class="pro-buy">立即购买</a>
            </div>
      
      `;
      $('.promotion-section-1 .pro-sect1-right').append(_html);

    }
  };

  var sms2 = {
    list:
      [
        {
          title: '1W条短信资源包',
          tips: '新品上线、活动推广、会员营销等',
          rows: '11000',
          old_price: '0.060',
          price: '0.0545',
          point: '600',
          total_price: '600',
          send: '（赠1000条）'
        },
        {
          title: '5W条短信资源包',
          tips: '新品上线、活动推广、会员营销等',
          rows: '55000',
          old_price: '0.0580',
          price: '0.0527',
          point: '2900',
          total_price: '2900',
          send: '（赠5000条）'
        },
        {
          title: '10W条短信资源包',
          tips: '新品上线、活动推广、会员营销等',
          rows: '110000',
          old_price: '0.050',
          price: '0.0455',
          point: '5000',
          total_price: '5000',
          send: '（赠1w条）'
        },
        {
          title: '50W条短信资源包',
          tips: '新品上线、活动推广、会员营销等',
          rows: '550000',
          old_price: '0.048',
          price: '0.0436',
          point: '24000',
          total_price: '24000',
          send: '（赠5w条）'
        },
        {
          title: '100W条短信资源包',
          tips: '新品上线、活动推广、会员营销等',
          rows: '1100000',
          old_price: '0.046',
          price: '0.0418',
          point: '46000',
          total_price: '46000',
          send: '（赠10w条）'
        },
        {
          title: '300W条短信资源包',
          tips: '新品上线、活动推广、会员营销等',
          rows: '3300000',
          old_price: '0.044',
          price: '0.040',
          point: '132000',
          total_price: '132000',
          send: '（赠30w条）'
        },

      ],
    init: function () {
      var _this = this;
      var lis = '';
      this.list.forEach(function (item, index) {
        var _html = `
                  <div class="backage-items mb-3 yixiao-item">
                    <div class="back-title">
                        <div class="pro-main-title fn20 fnbold">${item.title}</div>
                        <p class="pro-sub-title fn14 mt-2">${item.tips}</p>
                    </div>
                    <div class="mt-4">
                        <div class="d-flex align-items-center">
                            <div class="pro-sub-title fn14 mr-4" style="width:42px">条数</div>
                            <div class="pro-main-title fn15 fnbold">${item.rows}条 <span class="fn12" style="color:#ef441e">${item.send}</span> </div>
                        </div>
                        <div class="d-flex align-items-center mt-3 mb-3">
                        <div class="pro-sub-title fn14 mr-4">原单价</div> 
                        <div class="pro-sub-title old-price fn15 fnbold">${item.old_price}元/条</div>
                        </div>
                        <div class="d-flex align-items-center">
                            <div class="pro-sub-title fn14 mr-4">现单价</div> 
                            <div class="pro-main-title fn15 fnbold">${item.price}元/条</div>
                        </div>
                    </div>

                    <div class="d-flex align-items-center mt-4">
                        <div class="permanently-valid fn12 mr-2">永久有效</div>
                        <div class="get-point pro-sub-title fn12">可获得${item.point}积分</div>
                    </div>
                    <div class="pro-price">${item.total_price} <span class="fn14 fnbold">元</span></div>
                    <a href="/console/sms#packages" class="pro-buy">立即购买</a>
                </div>
            `;
        lis += _html;
      });
      $('.promotion-section-1_1 .pro-sect1-right').empty().html(lis);

      let _html = `
            <div class="backage-items mb-3 add-item2">
              <div class="back-title">
                  <div class="pro-main-title fn20 fnbold">10W条短信首购包</div>
                  <p class="pro-sub-title fn14 mt-2">新品上线、活动推广、会员营销</p>
              </div>
              <div class="mt-4">
                  <div class="d-flex align-items-center">
                      <div class="pro-sub-title fn14 mr-5">条数&nbsp;&nbsp;&nbsp;</div>
                      <div class="pro-main-title fn15 fnbold">100000条</div>
                  </div>
                  <div class="d-flex align-items-center mt-3 mb-3">
                  <div class="pro-sub-title fn14 mr-5">原单价</div> 
                  <div class="pro-sub-title old-price fn15 fnbold">0.050元/条</div>
                  </div>
                  <div class="d-flex align-items-center">
                      <div class="pro-sub-title fn14 mr-5">现单价</div> 
                      <div class="pro-main-title fn15 fnbold">0.035元/条</div>
                  </div>
              </div>

              <div class="d-flex align-items-center mt-4">
                  <div class="permanently-valid fn12 mr-2">新用户专享</div>
                  <div class="get-point pro-sub-title fn12">可获得3500积分</div>
              </div>
              <div class="pro-price">3500 <span class="fn14 fnbold">元</span> <span class="single-price pro-sub-title fn14 old-price" style="font-family:none">5000元</span></div>
              <a href="/console/sms#packages" class="pro-buy">立即购买</a>
          </div>

          <div class="backage-items mb-3 add-item2">
            <div class="back-title">
                <div class="pro-main-title fn20 fnbold">100W条短信首购包</div>
                <p class="pro-sub-title fn14 mt-2">新品上线、活动推广、会员营销</p>
            </div>
            <div class="mt-4">
                <div class="d-flex align-items-center">
                    <div class="pro-sub-title fn14 mr-5">条数&nbsp;&nbsp;&nbsp;</div>
                    <div class="pro-main-title fn15 fnbold">1000000条</div>
                </div>
                <div class="d-flex align-items-center mt-3 mb-3">
                <div class="pro-sub-title fn14 mr-5">原单价</div> 
                <div class="pro-sub-title old-price fn15 fnbold">0.046元/条</div>
                </div>
                <div class="d-flex align-items-center">
                    <div class="pro-sub-title fn14 mr-5">现单价</div> 
                    <div class="pro-main-title fn15 fnbold">0.034元/条</div>
                </div>
            </div>

            <div class="d-flex align-items-center mt-4">
                <div class="permanently-valid fn12 mr-2">新用户专享</div>
                <div class="get-point pro-sub-title fn12">可获得34000积分</div>
            </div>
            <div class="pro-price">34000 <span class="fn14 fnbold">元</span> <span class="single-price pro-sub-title fn14 old-price" style="font-family:none">37000元</span></div>
            <a href="/console/sms#packages" class="pro-buy">立即购买</a>
        </div>
      
      `;

      $('.promotion-section-1_1 .pro-sect1-right').append(_html);


    }
  };
  var email = {
    emaiList: [
      {
        title: '10万封邮件资源包',
        tips: '通知类邮件、营销类邮件',
        rows: '100000',
        price: '0.0076',
        total_price: '760'
      },
      {
        title: '50万封邮件资源包',
        tips: '通知类邮件、营销类邮件',
        rows: '500000',
        price: '0.0066',
        total_price: '3300'
      },
      {
        title: '200万封邮件资源包',
        tips: '通知类邮件、营销类邮件',
        rows: '2000000',
        price: '0.0058',
        total_price: '11600'
      },
      {
        title: '500万封邮件资源包',
        tips: '通知类邮件、营销类邮件',
        rows: '5000000',
        price: '0.0050',
        total_price: '25000'
      },
    ],
    init: function () {
      var _this = this;
      var lis = '';
      //邮件
      this.emaiList.forEach(function (item, index) {
        var _html = `
                  <div class="backage-items mb-3 mail-items">
                      <div class="back-title">
                          <div class="pro-main-title fn20 fnbold">${item.title}</div>
                          <p class="pro-sub-title fn14 mt-2">${item.tips}</p>
                      </div>
                      <div class="mt-4">
                          <div class="d-flex align-items-center">
                              <div class="pro-sub-title fn14 mr-5">条数</div> 
                              <div class="pro-main-title fn15 fnbold">${item.rows}条</div>
                          </div>
                        
                          <div class="d-flex align-items-center  mt-3 mb-3">
                              <div class="pro-sub-title fn14 mr-5">单价</div> 
                              <div class="pro-main-title fn15 fnbold">${item.price}元/封</div>
                          </div>

                          <div class="d-flex align-items-center" style="opacity:0">
                            <div class="pro-sub-title fn14 mr-5">计费</div> 
                            <div class="pro-main-title fn15 fnbold">发送成功计费</div>
                        </div>

                      </div>

                      <div class="d-flex align-items-center mt-4">
                          <div class="permanently-valid fn12 mr-2">永久有效</div>
                          <div class="get-point pro-sub-title fn12">可获得${item.total_price}积分</div>
                      </div>
                      <div class="pro-price">${item.total_price} <span class="fn14 fnbold">元</span></div>
                      <a href="/console/mail#packages" class="pro-buy">立即购买</a>
                  </div>
            `;
        lis += _html;
      });

      $('.promotion-section-3 .pro-email-backage').empty().append(lis);

    }
  };

  var gift = {
    list: [
      {
        integral: '50w',
        img_src: '/libraries_v4/view/promotion/img/S0008.png',
        title: '苹果 15 Pro MAX 512G',
        price: '11999',
      },
      {
        integral: '40w',
        img_src: '/libraries_v4/view/promotion/img/S0007.png',
        title: '苹果 15Pro 256G /华为 Mate 60 Pro+ 16G+512G',
        price: '11999',
      },
      {
        integral: '30w',
        img_src: '/libraries_v4/view/promotion/img/S0006.png',
        title: '华为 Mate 60 Pro 12G+512G',
        price: '11999',
      },
      {
        integral: '25w',
        img_src: '/libraries_v4/view/promotion/img/S0005.png',
        title: '华为 Mate60 12G+256G',
        price: '5499',
      },
      {
        integral: '20w',
        img_src: '/libraries_v4/view/promotion/img/S0004.png',
        title: '京东 4000 元购物卡',
        price: '4000',
      },
      {
        integral: '10w',
        img_src: '/libraries_v4/view/promotion/img/S0003.png',
        title: '京东 2000 元购物卡',
        price: '2000',
      },
      {
        integral: '5w',
        img_src: '/libraries_v4/view/promotion/img/S0002.png',
        title: '京东 1000 元购物卡',
        price: '1000',
      },
      {
        integral: '2w',
        img_src: '/libraries_v4/view/promotion/img/S0001.png',
        title: '京东 400 元购物卡',
        price: '400',
      },
      {
        integral: '1w',
        img_src: '/libraries_v4/view/promotion/img/S0000.png',
        title: '京东 200 元购物卡',
        price: '200',
      },
    ],

    init: function () {
      var _this = this;
      var lis = '';
      //邮件
      this.list.forEach(function (item, index) {
        var _html = `
                <div class="award-items">
                    <div class="award-tips text-right fn16 fnbold"> <span>${item.integral} 积分</span></div>
                    <div class="text-center mt-3">
                        <img class="phone-img" src="${item.img_src}" alt="">
                    </div>
                    <div class="mt-4 awardSec" style="padding:0 12px;">
                        <div class="phone-name fn16 fnbold pro-main-title">${item.title}</div>
                        <div class="pro-sub-title" style="font-size:13px">参考价  ${item.price}元</div>
                        <div class="exchange-gift fn14 mt-2">还差 90000 积分</div>
                    </div>
                </div>    
            `;
        lis += _html;
      });

      // $('.phone-bg').empty().append(lis);
    }
  };





  sms1.init();
  sms2.init();
  email.init();
  gift.init();
});



$(document).ready(function () {
  // 储值产品
  $('.promotion-section-2 .value-td2 ul li').on('click', function () {


    $(this).addClass('active').siblings().removeClass('active');
    let _text = $(this).find('span').html();
    $('.promotion-section-2 .value-td3').find('.same-num').html(_text);


  });


  $('.promotion-section-2 .value-td2_2 ul li').on('click', function () {
    $(this).addClass('active').siblings().removeClass('active');
    let _text = $(this).find('span').html();
    $('.promotion-section-2 .value-td3_3').find('.same-num').html(_text);
  });


  // ///////
  $('.promotion-section-4 .pro-btn').on('click', function () {
    $('.dro-men').show();
  });
  $(document).mouseup(function (e) {
    var pop = $('.promotion-section-4 .pro-btn');
    if (!pop.is(e.target) && pop.has(e.target).length === 0) {
      $('.dro-men').hide();
    }
  });

  var _type = 'base';
  $('.promotion-section-4 .dro-men div').on('click', function () {
    let _text = $(this).html();
    $('.pro-btn').html(_text);
    $('.dro-men').hide();
    $('.promotion-section-4 ul li:eq(0)').addClass('active').siblings().removeClass('active');

    _type = $(this).attr('data');
    switch (_type) {
      case 'base':
        $('.promotion-section-4 .value-td3').find('.same-num').html('5');
        break;
      case 'high':
        $('.promotion-section-4 .value-td3').find('.same-num').html('49');
        break;
      case 'bus':
        $('.promotion-section-4 .value-td3').find('.same-num').html('299');
        break;
      case 'flag':
        $('.promotion-section-4 .value-td3').find('.same-num').html('1299');
        break;
    }

  });
  var type_year = '';
  $('.promotion-section-4 .value-td2 ul li').on('click', function () {
    $(this).addClass('active').siblings().removeClass('active');

    let _text = '';
    type_year = $(this).attr('data-year');
    switch (_type) {
      case 'base':
        _text = $(this).attr('data');
        $('.promotion-section-4 .value-td3').find('.same-num').html(_text);
        break;
      case 'high':
        _text = $(this).attr('data-high');
        $('.promotion-section-4 .value-td3').find('.same-num').html(_text);
        break;
      case 'bus':
        _text = $(this).attr('data-bus');
        $('.promotion-section-4 .value-td3').find('.same-num').html(_text);
        break;
      case 'flag':
        _text = $(this).attr('data-flag');
        $('.promotion-section-4 .value-td3').find('.same-num').html(_text);
        break;
    }

    let _html = '';
    switch (type_year) {
      case 'one':
        _html = `购买时长 <span class="fnbold" style="color:#ef441e">（付 10 个月享 1 年）</span>`;
        $('.promotion-section-4 .buy-time').empty().append(_html);
        break;
      case 'two':
        _html = `购买时长 <span class="fnbold" style="color:#ef441e">（付 20 个月享 2 年）</span>`;
        $('.promotion-section-4 .buy-time').empty().append(_html);
        break;
      case 'three':
        _html = `购买时长 <span class="fnbold" style="color:#ef441e">（付 30 个月享 3 年）</span>`;
        $('.promotion-section-4 .buy-time').empty().append(_html);
        break;
      case 'five':
        _html = `购买时长 <span class="fnbold" style="color:#ef441e">（付 50 个月享 5 年）</span>`;
        $('.promotion-section-4 .buy-time').empty().append(_html);
        break;
      case undefined:
        _html = `购买时长`;
        $('.promotion-section-4 .buy-time').empty().append(_html);
        break;
    }
  });

  // 空号检测
  $('.promotion-section-4 .value-td2_2 ul li').on('click', function () {
    $(this).addClass('active').siblings().removeClass('active');

    let _text = $(this).attr('data-price1');
    let _text2 = $(this).attr('data-price2');
    $('.promotion-section-4 .kong-hao').find('.same-num').html(_text);
    $('.promotion-section-4 .kong-hao').find('.single-price').html(_text2 + '/次');
  });


  // 本机号码认证
  $('.promotion-section-4 .value-td2_3 ul li').on('click', function () {
    $(this).addClass('active').siblings().removeClass('active');

    let _text = $(this).attr('data-price1');
    let _text2 = $(this).attr('data-price2');
    $('.promotion-section-4 .ben-ji').find('.same-num').html(_text);
    $('.promotion-section-4 .ben-ji').find('.single-price').html(_text2 + '/次');
  });

  // 一键登录
  $('.promotion-section-4 .value-td2_4 ul li').on('click', function () {
    $(this).addClass('active').siblings().removeClass('active');

    let _text = $(this).attr('data-price1');
    let _text2 = $(this).attr('data-price2');
    $('.promotion-section-4 .one-pass').find('.same-num').html(_text);
    $('.promotion-section-4 .one-pass').find('.single-price').html(_text2 + '/次');
  });












});



