﻿using Newtonsoft.Json;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Web.UI.WebControls;

namespace CommonLib
{
    public static class CodeProcessHelper
    {
        public static string GetFileResultHtml(string json)
        {
            var content = JsonConvert.DeserializeObject<OcrContent>(json);
            var result = FileResultHtmlHelper.GetDownLoadHtml(content);
            return result;
        }

        public static string GetMathResultHtml(string json)
        {
            var content = JsonConvert.DeserializeObject<OcrContent>(json);
            var result = MathResultHtmlHelper.GetDownLoadHtml(content);
            return result;
        }

        #region ProcessStepCache

        static LocalWaitCache<List<string>> OcrProcesStepCache = new LocalWaitCache<List<string>>("OcrProcesInfoCache", new TimeSpan(0, 30, 0), false);

        public static void AddProcessInfo(string id, string content, string type = null)
        {
            var lstOld = GetProcessInfo(id);
            lstOld.Add(string.Format("{0} {1}{2}", ServerTime.DateTime.ToString("HH:mm:ss fff"), content, type ?? ""));
            OcrProcesStepCache.Set(id, lstOld);
        }

        public static List<string> GetProcessInfo(string id)
        {
            return OcrProcesStepCache.Get(id) ?? new List<string>();
        }
        #endregion

        #region OcrProcessServerCache

        static LocalWaitCache<string> OcrServerCache = new LocalWaitCache<string>("OcrServerCache", new TimeSpan(0, 1, 0));

        public static LocalWaitCache<string> ServerConfigCache = new LocalWaitCache<string>("ServerConfigCache", new TimeSpan(0, 1, 0), false, true);

        public static LocalWaitCache<List<string>> BlackIpConfigCache = new LocalWaitCache<List<string>>("BlackIpConfigCache", new TimeSpan(0, 5, 0), false, false);

        public static LocalWaitCache<List<string>> StaticBlackIpCache = new LocalWaitCache<List<string>>("StaticBlackIpCache", new TimeSpan(0, 5, 0), false, true);

        static int MAX_NO_RESULT_MILSECOND = 5000;

        public static void WaitOcrServerState(CusImageEntity img, string server, int milsec = 1500, bool isFirst = true)
        {
            //超过1.5秒未反馈开始处理
            var objRes = OcrServerCache.WaitLock(img.StrIndex, new TimeSpan(0, 0, 0, 0, milsec));
            if (string.IsNullOrEmpty(objRes))
            {
                var processStateText = isFirst ? "反馈" : "结果";
                var processContentText = img.RetryTimes > 3 ? "终止处理" : "重新分配";
                CodeProcessHelper.AddProcessInfo(img.StrIndex, "[" + server + "]节点，等待" + processStateText + "超时，将" + processContentText);
                LogHelper.Log.InfoFormat("【无{4}】ID:{0},Server:{1} 超过{2}ms，{3}！", img.StrIndex, server, milsec, processContentText, processStateText);
                if (img.RetryTimes < 3)
                {
                    img.RetryTimes++;
                    SendOcrRequestToRedis(img);
                }
            }
            else
            {
                if (isFirst)
                {
                    var leftTime = (int)(MAX_NO_RESULT_MILSECOND - new TimeSpan(ServerTime.DateTime.Ticks - img.DtAdd).TotalMilliseconds);
                    if (leftTime > 0)
                    {
                        //超过3.5秒未成功处理结果
                        img.RetryTimes = 0;
                        WaitOcrServerState(img, server, leftTime, false);
                    }
                    else
                    {
                        OcrServerCache.Set(img.StrIndex, server);
                        LogHelper.Log.InfoFormat("【超时】ID:{0},Server:{1} 超过{2}ms！", img.StrIndex, server, MAX_NO_RESULT_MILSECOND);
                    }
                }
            }
        }

        public static void ReportOcrServerState(string id, string server)
        {
            OcrServerCache.Set(id, server);
        }

        #endregion

        #region OcrCache

        static LocalWaitCache<CusImageEntity> WaitOcrCache = new LocalWaitCache<CusImageEntity>("WaitOcrCache", new TimeSpan(0, 1, 0));
        static LocalWaitCache<List<OcrContent>> OcrResultCache = new LocalWaitCache<List<OcrContent>>("OcrResultCache", new TimeSpan(0, 1, 0));

        public static void SendOcrRequestToRedis(CusImageEntity img)
        {
            WaitOcrCache.Set(img);
            ApiCountCacheHelper.AddCount("OcrCounts");
        }

        public static int GetTimeOut(string ext)
        {
            return !Equals(ext, "txt") && CommonHelper.DocFileTypes.Contains(ext) ? ConfigHelper.NGetCodeTimeOut * 3 : ConfigHelper.NGetCodeTimeOut;
        }

        public static string SendToProcessPool(string account, string token, string strImage, string url, UserTypeEnum siteFlag, OcrType ocrType, OcrGroupType groupType,
            TransLanguageTypeEnum from, TransLanguageTypeEnum to, int contentLength, int? processId
            , bool fromLeftToRight, bool fromTopToDown, string fileExt, int timeOut, long dtUser
            , bool isAutoFull2Half, bool isAutoSpace, bool isAutoSymbol, bool isAutoDuplicateSymbol
            , bool? isSupportVertical
            , long clientTicks)
        {
            var result = "";
            try
            {
                if (!string.IsNullOrEmpty(strImage) || !string.IsNullOrEmpty(url))
                {
                    var strKey = Guid.NewGuid().ToString().Replace("-", "");
                    OcrContent ocr;
                    using (var img = new CusImageEntity())
                    {
                        img.Account = account;
                        img.Token = token;
                        img.StrIndex = strKey;
                        img.DtUser = dtUser;
                        img.DtAdd = ServerTime.DateTime.Ticks;
                        img.DtExpired = ServerTime.DateTime.AddSeconds(timeOut).Ticks;
                        img.UserType = siteFlag;
                        img.StrImg = strImage;
                        img.ImgUrl = url;
                        img.OcrType = ocrType;
                        img.IsFromLeftToRight = fromLeftToRight;
                        img.IsFromTopToDown = fromTopToDown;
                        img.OcrGroup = groupType;
                        img.ProcessId = processId;
                        img.FileExt = fileExt;
                        img.FromLanguage = from;
                        img.ToLanguage = to;
                        img.FileContentLength = contentLength;
                        img.IsAutoFull2Half = isAutoFull2Half;
                        img.IsAutoSpace = isAutoSpace;
                        img.IsAutoDuplicateSymbol = isAutoDuplicateSymbol;
                        img.IsAutoSymbol = isAutoSymbol;
                        img.IsSupportVertical = isSupportVertical;
                        img.ClientTicks = clientTicks;
                        CodeProcessHelper.AddProcessInfo(strKey, "收到OCR请求，开始分配处理节点");
                        SendOcrRequestToRedis(img);
                        ocr = OcrResultCache.WaitLock(img.StrIndex, new TimeSpan(0, 0, timeOut))?.FirstOrDefault();
                    }
                    if (ocr == null || string.IsNullOrEmpty(ocr.processName))
                    {
                        ocr = new OcrContent()
                        {
                            id = strKey,
                            ocrType = ocrType,
                            url = url,
                            Server = OcrServerCache.Get(strKey)
                        };
                    }
                    result = JsonConvert.SerializeObject(ocr);
                    //LogHelper.Log.Info(result);
                }
            }
            catch (Exception oe)
            {
                LogHelper.Log.Error("获取结果出错！", oe);
            }
            return result;
        }

        public static CusImageEntity GetFromProcessPool(int seconds, string server)
        {
            ////old 
            //return RdsCacheHelper.OcrProcessQueue.DequeueBlockingCacheMessage(seconds);
            //new
            return WaitOcrCache.WaitLock(new TimeSpan(0, 0, seconds));
        }

        public static void Compensate(string account, string token)
        {
            RdsCacheHelper.CodeRecordCache.Compensate(account, token);
        }

        public static string SetOcrResult(OcrContent content)
        {
            var result = "";
            try
            {
                ////old
                //RdsCacheHelper.OcrResult.Push(content.id, content, 0, 1, 0);
                //new 
                var lstResult = OcrResultCache.Get(content.id) ?? new List<OcrContent>();
                lstResult.Add(content);
                OcrResultCache.Set(content.id, lstResult);
                CodeProcessHelper.AddProcessInfo(content.id, "[" + content.Server + "]节点，上报处理结果：" + content.processName);
                ReportOcrServerState(content.id, content.Server);
            }
            catch (Exception oe)
            {
                LogHelper.Log.Error("设置OCR结果出错！", oe);
            }
            return result;
        }

        public static string GetOtherOcrResultById(string strId)
        {
            var result = "";
            try
            {
                if (!string.IsNullOrEmpty(strId))
                {
                    ////old
                    //var lstOcr = RdsCacheHelper.OcrResult.PopAll(strId);
                    //new
                    var lstOcr = OcrResultCache.Get(strId);
                    if (lstOcr?.Count >= 0)
                    {
                        result = JsonConvert.SerializeObject(lstOcr);
                    }
                }
            }
            catch (Exception oe)
            {
                LogHelper.Log.Error("获取结果出错！", oe);
            }
            return result;
        }

        #endregion

        #region FileResultCache

        static LocalWaitCache<CusFileStatusEntity> WaitFileStateCache = new LocalWaitCache<CusFileStatusEntity>("WaitFileStateCache", new TimeSpan(0, 1, 0));
        static string WAIT_FILE_STATE_CACHE_KEY = "WaitFileStateKey";
        static LocalWaitCache<ProcessStateEntity> FileStateResultCache = new LocalWaitCache<ProcessStateEntity>("FileStateResultCache", new TimeSpan(0, 1, 0));

        public static ProcessStateEntity SendToFileStatusProcessPool(string taskId, int ocrType)
        {
            try
            {
                var img = new CusFileStatusEntity()
                {
                    OcrType = ocrType,
                    TaskId = taskId
                };
                WaitFileStateCache.Set(WAIT_FILE_STATE_CACHE_KEY, img);
                return FileStateResultCache.WaitLock(img.TaskId, new TimeSpan(0, 0, 10));
            }
            catch (Exception oe)
            {
                LogHelper.Log.Error("获取文件状态结果出错！", oe);
            }
            return null;
        }

        public static CusFileStatusEntity GetFileStatusFromProcessPool(int seconds, string server)
        {
            return WaitFileStateCache.WaitLock(WAIT_FILE_STATE_CACHE_KEY, new TimeSpan(0, 0, seconds));
        }

        public static string SetFileStatusResult(ProcessStateEntity content)
        {
            var result = "";
            try
            {
                FileStateResultCache.Set(content.taskId, content);
            }
            catch (Exception oe)
            {
                LogHelper.Log.Error("设置OCR结果出错！", oe);
            }
            return result;
        }

        #endregion

        #region ServerNodeCache

        static string SERVER_NODE_CACHE_KEY = "ServerNodeKey";
        static LocalWaitCache<List<ServerStateInfo>> ServerNodeCache = new LocalWaitCache<List<ServerStateInfo>>("ServerNodeCache", new TimeSpan(1, 0, 0, 0), false);

        public static void AddServerState(ServerStateInfo content)
        {
            try
            {
                //LogHelper.Log.Error("AddServerState:" + JsonConvert.SerializeObject(content));
                if (content != null && !string.IsNullOrEmpty(content.Id))
                {
                    var lstOld = GetServerStateInfos();
                    lstOld.RemoveAll(p => Equals(p.Id, content.Id) || p.DtUpdate < ServerTime.DateTime.AddDays(-1));
                    lstOld.Add(content);
                    ServerNodeCache.Set(SERVER_NODE_CACHE_KEY, lstOld);
                }
            }
            catch (Exception oe)
            {
                LogHelper.Log.Error("AddServerState出错！", oe);
            }
        }

        public static List<ServerStateInfo> GetServerStateInfos()
        {
            ////old
            //return RdsCacheHelper.ServerStateCache.GetAll();
            //new
            return ServerNodeCache.Get(SERVER_NODE_CACHE_KEY) ?? new List<ServerStateInfo>();
        }

        private static ConcurrentDictionary<DateTime, ServerErrorLog> DicServerErrors = new ConcurrentDictionary<DateTime, ServerErrorLog>();

        public static string GetServerStatus()
        {
            var result = string.Empty;
            Dictionary<string, ServerLog> dicServerLog = new Dictionary<string, ServerLog>();
            ServerLog accLog = new ServerLog { component = new ServerComponent() { code = "rhznvxg4v7yh", name = "账户服务" }, days = new List<ServerErrorLog>() };
            ServerLog ocrLog = new ServerLog { component = new ServerComponent() { code = "354mn7xfxz1h", name = "OCR识别" }, days = new List<ServerErrorLog>() };

            var startDate = ServerTime.LocalTime.AddDays(-89);
            for (int i = 0; i < 90; i++)
            {
                accLog.days.Add(new ServerErrorLog() { date = startDate.AddDays(i), outages = new ServerBreakTime(), related_events = new List<ServerComponent>() });
                ocrLog.days.Add(GetOcrLogByDate(startDate.AddDays(i)));
            }
            dicServerLog.Add(accLog.component.code, accLog);
            dicServerLog.Add(ocrLog.component.code, ocrLog);
            result = JsonConvert.SerializeObject(dicServerLog);
            return result;
        }

        private static ServerErrorLog GetOcrLogByDate(DateTime dtDate)
        {
            if (DicServerErrors.ContainsKey(dtDate.Date))
            {
                return DicServerErrors[dtDate.Date];
            }
            var errorLog = new ServerErrorLog { date = dtDate, related_events = new List<ServerComponent>() };
            errorLog.outages = new ServerBreakTime
            {
                p = errorLog.related_events?.Sum(q => q.esTime)
            };
            if (errorLog.related_events?.Count > 5)
            {
                errorLog.related_events = errorLog.related_events.Take(5).ToList();
            }
            if (dtDate.Date < ServerTime.DateTime.Date)
            {
                DicServerErrors[dtDate.Date] = errorLog;
            }
            return errorLog;
        }
        #endregion
    }

    public class ServerLog
    {
        public ServerComponent component { get; set; }

        public List<ServerErrorLog> days { get; set; }
    }

    public class ServerComponent
    {
        public DateTime time { get; set; }

        public string code { get; set; }

        public string name { get; set; }

        public double esTime { get; internal set; }
        public string from { get; set; }
    }

    public class ServerErrorLog
    {
        public DateTime date { get; set; }

        public List<ServerComponent> related_events { get; set; }

        public ServerBreakTime outages { get; set; }
    }

    public class ServerBreakTime
    {
        public double? p { get; set; }
    }
}
