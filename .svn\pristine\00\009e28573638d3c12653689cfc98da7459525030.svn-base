﻿//======================================================================
//        开源日期:           2013/09/01
//            作者:           何雨泉    
//            博客：          http://www.cnblogs.com/heyuquan/
//            版本:           0.0.0.1
//======================================================================

using System;
using System.Text;
using System.Net;
using System.Net.Mail;
using System.Security.Cryptography.X509Certificates;


namespace Notice.Process.Common
{
    // 1、一个SmtpClient一次只能发送一个MailMessage，不管是同步还是异步发送，所以批量发送也会因为这个条件而被阻塞。
    // 2、若要异步发送大批量邮件，方案：应当多个线程、每个线程去使用一个单独的SmtpClient去发送。（但要注意不合理分配资源会更加降低性能）
    // 3、何时使用 SmtpClient.SendAsync() 异步发送呢？是在发件内容、附件、加密等因素造成一条短信发送比较耗时的情况下使用。



    /// <summary>
    /// SmtpClient构造器
    /// 使用注意事项：
    /// 1、非线程安全类
    /// 2、构造的SmtpClient 实例由外部进行Dispose()。SmtpHelper类只简单提供构造，不做释放操作。
    /// 3、SmtpClient 没有提供 Finalize() 终结器，所以GC不会进行回收，只能由外部使用完后进行显示释放，否则会发生内存泄露问题
    /// </summary>
    public class SmtpHelper
    {
        /// <summary>
        /// 返回内部构造的SmtpClient实例
        /// </summary>
        public SmtpClient SmtpClient { get; private set; }

        #region  SmtpHelper 构造函数

        #region SMTP服务器 需要身份验证凭据

        /// <summary>
        /// 创建 SmtpHelper 实例
        /// </summary>
        /// <param name="host">设置 SMTP 主服务器</param>
        /// <param name="port">端口号</param>
        /// <param name="enableSsl">指定 SmtpClient 是否使用安全套接字层 (SSL) 加密连接。</param>
        /// <param name="userName">用户名</param>
        /// <param name="password">密码</param>
        public SmtpHelper(string userName, string password, bool enableSsl)
        {
            MailValidatorHelper.ValideStrNullOrEmpty(userName, "userName");
            MailValidatorHelper.ValideStrNullOrEmpty(password, "password");

            var mailType = MailTypeHelper.GetMailType(userName, enableSsl);
            SmtpClient = new SmtpClient(mailType.Smtp, mailType.Prot);
            SmtpClient.EnableSsl = mailType.IsSsl;
            SmtpClient.UseDefaultCredentials = false;
            SmtpClient.DeliveryMethod = SmtpDeliveryMethod.Network;
            SmtpClient.Credentials = new NetworkCredential(userName, password);
            SmtpClient.Timeout = 100000;
        }

        #endregion

        #endregion

        /// <summary>
        /// 设置SmtpClient.Send() 调用的超时时间。
        /// SmtpClient默认 Timeout =  （100秒=100*1000毫秒）。
        /// 应当根据“邮件大小、附件大小、加密耗时”等因素进行调整
        /// </summary>
        public SmtpHelper SetTimeout(int timeout)
        {
            if (timeout > 0)
            {
                SmtpClient.Timeout = timeout;
            }
            return this;
        }

        /// <summary>
        /// 设置 SmtpClient 如何处理待发的电子邮件。
        /// </summary>
        /// <param name="deliveryMethod">
        /// 0、Network（默认）：电子邮件通过网络发送到 SMTP 服务器。
        /// 1、SpecifiedPickupDirectory：将电子邮件复制到 SmtpClient.PickupDirectoryLocation 属性指定的目录，然后由外部应用程序传送。
        /// 2、PickupDirectoryFromIis：将电子邮件复制到拾取目录，然后通过本地 Internet 信息服务 (IIS) 传送。
        /// </param>
        public SmtpHelper SetDeliveryMethod(int deliveryMethod)
        {
            if (deliveryMethod < 0 || deliveryMethod > 2)
                deliveryMethod = 0;     //  Network（默认）

            SmtpClient.DeliveryMethod = (SmtpDeliveryMethod)deliveryMethod;

            return this;
        }

        /// <summary>
        /// 添加建立安全套接字层 (SSL) 连接的证书
        /// </summary>
        public SmtpHelper AddClientCertificate(X509Certificate certificate)
        {
            MailValidatorHelper.ValideArgumentNull<X509Certificate>(certificate, "certificate");

            SmtpClient.EnableSsl = true;
            SmtpClient.ClientCertificates.Add(certificate);

            return this;
        }

    }
}
