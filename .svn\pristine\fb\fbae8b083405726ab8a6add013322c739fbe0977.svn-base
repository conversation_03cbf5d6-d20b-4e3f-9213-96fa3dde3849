﻿using System;
using System.Linq;
using System.Collections.Generic;

namespace NewTicket
{
    public class AttentionItem// : IEquatable<AttentionItem>
    {
        private string key = "";
        public string Key
        {
            get
            {
                if (string.IsNullOrEmpty(key))
                {
                    key = string.Format("{0:yyyy-MM-dd}_{1}_{2}", this.Date, this.FromStation.Name, this.ToStation.Name);
                }
                return key;
            }
            set
            {
                key = value;
            }
        }

        /// <summary>
        /// 仅供查票类使用
        /// </summary>
        public bool IsMobile { get; set; }

        private string purposeCode;

        public string PurposeCode
        {
            get
            {
                if (string.IsNullOrEmpty(purposeCode))
                    purposeCode = CommonString.strAdultType;
                return purposeCode;
            }
            set { purposeCode = value; }
        }

        public string PurposeMobileCode
        {
            get
            {
                return PurposeCode.Equals(CommonString.strStudentType)
                    ? CommonString.strMobileStudentType : CommonString.strMobileAdultType;
            }
        }

        public List<string> LstTrainNo { get; set; }
        public string TrainID = string.Empty;
        public string FromStationName
        {
            get
            {
                return this.FromStation.Name;
            }
        }
        public string ToStationName
        {
            get
            {
                return this.ToStation.Name;
            }
        }

        public TrainStation FromStation
        {
            get;
            set;
        }
        public TrainStation ToStation
        {
            get;
            set;
        }
        public DateTime Date
        {
            get;
            set;
        }
        private string itemDate = string.Empty;

        public string ItemDate
        {
            get
            {
                if (string.IsNullOrEmpty(itemDate))
                    itemDate = Date.ToString("yyyy-MM-dd");
                return itemDate;
            }
            set { itemDate = value; }
        }

        public string ItemMobileDate
        {
            get
            {
                return Date.ToString("yyyyMMdd");
            }
        }

        private List<string> lstGroupStation = new List<string>();

        public List<string> LstGroupStation
        {
            get { return lstGroupStation; }
            set { lstGroupStation = value; }
        }

        private List<string> seatTypes = new List<string>();

        public List<string> LstAllSeatType
        {
            get { return seatTypes; }
            set
            {
                seatTypes = value ?? new List<string>();
            }
        }

        public void CopyTo(string strTmp)
        {
            //2015-02-23_BJP_SHH_G101_ADULT_硬卧
            if (string.IsNullOrEmpty(strTmp))
                return;
            var strOld = strTmp;
            try
            {
                if (CommonString.IsLoadStations)
                {
                    for (int i = 0; i < 5; i++)
                    {
                        if (CommonString.IsLoadStations)
                        {
                            System.Threading.Thread.Sleep(1000);
                        }
                        else
                        {
                            break;
                        }
                    }
                }
                Date = BoxUtil.GetDateTimeFromObject(CommonMethod.SubString(strTmp, "", "_"));
                strTmp = CommonMethod.SubString(strTmp, "_");
                FromStation = CommonString.Stations.FirstOrDefault(v => v.Code.Equals(CommonMethod.SubString(strTmp, "", "_")));
                if (FromStation == null)
                {
                    FromStation = CommonString.Stations.FirstOrDefault(v => v.Name.Equals(CommonMethod.SubString(strTmp, "", "_")));
                }
                strTmp = CommonMethod.SubString(strTmp, "_");
                ToStation = CommonString.Stations.FirstOrDefault(v => v.Code.Equals(CommonMethod.SubString(strTmp, "", "_")));
                if (ToStation == null)
                {
                    ToStation = CommonString.Stations.FirstOrDefault(v => v.Name.Equals(CommonMethod.SubString(strTmp, "", "_")));
                }
                strTmp = CommonMethod.SubString(strTmp, "_");
                if (LstTrainNo == null)
                {
                    LstTrainNo = new List<string>();
                }
                if (!LstTrainNo.Contains(CommonMethod.SubString(strTmp, "", "_")))
                    LstTrainNo.Add(CommonMethod.SubString(strTmp, "", "_"));
                strTmp = CommonMethod.SubString(strTmp, "_");
                PurposeCode = CommonMethod.SubString(strTmp, "", "_");
                //if(PurposeCode.Contains("承认"))
                strTmp = CommonMethod.SubString(strTmp, "_");
                if (LstAllSeatType == null)
                {
                    LstAllSeatType = new List<string>();
                }
                if (!LstAllSeatType.Contains(strTmp))
                    LstAllSeatType.Add(strTmp);
            }
            catch (Exception oe)
            {
                Log.WriteError("CopyTo出错" + strOld, oe);
            }
        }

        public List<string> GetAllKey()
        {
            if (LstGroupStation == null || lstGroupStation.Count <= 0)
            {
                LstGroupStation = new List<string>() { this.FromStation.Code + "|" + this.ToStation.Code };
            }
            if (LstAllSeatType == null)
            {
                LstAllSeatType = new List<string>();
            }
            List<string> lstTmp = new List<string>();
            try
            {
                foreach (var item in LstGroupStation)
                {
                    foreach (var trainNo in LstTrainNo)
                    {
                        foreach (var seat in LstAllSeatType)
                        {
                            if (CommonString.IsComCanUse)
                            {
                                lstTmp.Add(string.Format("[{0}_{1}_{2}_{3}_{4}]"
                                    , this.Date.ToString("yyyy-MM-dd"), item.Replace("|", "_")
                                    , trainNo, PurposeCode, seat));
                            }
                            if (CommonString.IsMobileCanUse)
                            {
                                lstTmp.Add(string.Format("[{0}_{1}_{2}_{3}_{4}]"
                                    , this.Date.ToString("yyyy-MM-dd"), item.Replace("|", "_")
                                    , trainNo, PurposeMobileCode, seat));
                            }
                        }
                    }
                }
            }
            catch (Exception oe)
            {
                Log.WriteError("GetAllKeys出错", oe);
            }
            return lstTmp;
        }

        public AttentionItem Copy()
        {
            AttentionItem item = new AttentionItem();
            item.Date = Date;
            item.FromStation = this.FromStation;
            item.ToStation = this.ToStation;
            item.IsMobile = this.IsMobile;
            item.ItemDate = this.ItemDate;
            item.Key = this.Key;
            item.LstAllSeatType = new List<string>();
            item.LstAllSeatType.AddRange(this.LstAllSeatType.ToArray());
            item.LstGroupStation = new List<string>();
            item.LstGroupStation.AddRange(this.LstGroupStation.ToArray());
            item.LstTrainNo = new List<string>();
            item.LstTrainNo.AddRange(this.LstTrainNo.ToArray());
            item.PurposeCode = this.PurposeCode;
            item.TrainID = this.TrainID;
            return item;
        }
    }
}
