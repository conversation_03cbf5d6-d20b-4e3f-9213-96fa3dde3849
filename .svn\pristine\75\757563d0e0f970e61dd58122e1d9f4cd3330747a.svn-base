﻿using CommonLib;
using System;
using System.Collections.Generic;
using System.Collections.Specialized;
using System.IO;

namespace HanZiOcr
{
    /// <summary>
    /// https://openai.maxvisioncloud.com/ai/ocr?squareActive=1&pid=163&id=36
    /// </summary>
    public class ShengShiRec : BaseOcrRec
    {
        public ShengShiRec()
        {
            OcrType = HanZiOcrType.盛视AI;
            MaxExecPerTime = 20;

            LstJsonPreProcessArray = new List<object>() { "data|F|key=text" };
            IsJsonArrayStringWithLocation = true;
            LstJsonLocationProcessArray = new List<object>() { "data|F|key=box" };
            IsSupportVertical = true;
            StrJsonArrayWordSpilt = "value";
            StrResultJsonSpilt = "words";
            IsSupportUrlOcr = true;
            LstVerticalLocation = new List<object>() { "value" };
        }

        protected override string GetHtml(OcrContent content)
        {
            var result = string.Empty;
            try
            {
                var byts = Convert.FromBase64String(content.strBase64);
                var file = new UploadFileInfo()
                {
                    Name = "image",
                    Filename = Guid.NewGuid().ToString() + "." + content.fileExt,
                    ContentType = ApplicationTypeHelper.GetApplicationType(content.fileExt),
                    Stream = new MemoryStream(byts)
                };
                result = PostFile("https://openai.maxvisioncloud.com/open/scan/algorithm/ocr?side=0&type=generalOcr&cached=1", new[] { file }, null);
            }
            catch (Exception)
            {

            }
            return result;
        }

        public override string GetHtmlByUrl(OcrContent content)
        {
            var url = "https://openai.maxvisioncloud.com/open/scan/algorithm/ocr?side=0&type=generalOcr&url=" + content.url;
            var strTmp = WebClientSyncExt.GetHtml(url, "", "\n", "https://openai.maxvisioncloud.com/ai/ocr?squareActive=1&pid=163&id=36", ExecTimeOutSeconds);
            return strTmp;
        }

    }
}