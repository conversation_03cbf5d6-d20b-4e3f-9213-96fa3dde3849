﻿using CommonLib;
using System;
using System.Collections.Generic;
using System.Collections.Specialized;
using System.IO;
using System.Security.Cryptography;
using System.Text;

namespace TableOcr
{
    /// <summary>
    /// 有道智云-小程序
    /// https://aidemo.youdao.com/ocr.html?key=76393d7dbef4406e9826c9e23e362158
    /// </summary>
    public class YouDaoLiteRec : BaseTableRec
    {
        public YouDaoLiteRec()
        {
            OcrGroup = OcrGroupType.有道;
            OcrType = TableOcrType.有道Lite;
            MaxExecPerTime = 21;

            LstJsonPreProcessArray = new List<object>() { "Result", "tables", 0, "cells" };
            LstJsonResultProcessArray = new List<object>() { "lines|N|text" };
            LstRowIndex = new List<object>() { "rowRange" };
            LstColumnIndex = new List<object>() { "colRange" };
            RowIndexIsArray = false;
            IsRowIndexAddOne = true;
        }

        static List<YouDaoAccount> lstAppAccount = new List<YouDaoAccount>() {
        new YouDaoAccount(){
         strAppId = "2423360539ba5632",
          strSecretId =  "QQ8gLkYxtchLt6Osj1eXrsSDTus8N2Ru"
        },
        new YouDaoAccount(){
         strAppId = "712b0ae8fd3d404d",
          strSecretId =  "TF7ORXNiC6J3V18WZ4JCVYe2chHPVnRZ"
        },
        new YouDaoAccount(){
        strAppId = "4987f62dcf974c87",
        strSecretId =  "e5Qc5zA6nwK8YOBcVyrBTV8s43IlMe41"
        }
        };

        protected override string GetHtml(OcrContent content)
        {
            var result = string.Empty;
            var account = lstAppAccount.GetRndItem();
            var dic = new NameValueCollection();
            string salt = DateTime.Now.Millisecond.ToString();
            dic.Add("docType", "json");
            dic.Add("osType", "api");
            dic.Add("signType", "v3");
            dic.Add("angle", "1");
            dic.Add("source", "table");
            dic.Add("type", "2");
            TimeSpan ts = (DateTime.UtcNow - new DateTime(1970, 1, 1, 0, 0, 0, DateTimeKind.Utc));
            long millis = (long)ts.TotalMilliseconds;
            string curtime = Convert.ToString(millis / 1000);
            dic.Add("curtime", curtime);
            string signStr = account?.strAppId + Truncate(content.strBase64) + salt + curtime + account?.strSecretId;
            string sign = ComputeHash(signStr);
            dic.Add("appKey", account?.strAppId);
            dic.Add("salt", salt);
            dic.Add("sign", sign);

            var url = "https://aidemo.youdao.com/ocrmini";
            var byt = Convert.FromBase64String(content.strBase64);
            var file = new UploadFileInfo()
            {
                Name = "file",
                Filename = "1." + content.fileExt,
                ContentType = ApplicationTypeHelper.GetApplicationType(content.fileExt),
                Stream = new MemoryStream(byt)
            };
            var html = PostFile(url, new[] { file }, dic);
            var id = CommonHelper.SubString(html, "\"result\":\"", "\"");
            if (!string.IsNullOrEmpty(id))
            {
                result = WebClientSyncExt.GetHtml(string.Format("https://aidemo.youdao.com/wxocrconfig/{0}/table", id));
            }
            return result;
        }

        protected static string Truncate(string q)
        {
            if (q == null)
            {
                return null;
            }
            int len = q.Length;
            return len <= 20 ? q : (q.Substring(0, 10) + len + q.Substring(len - 10, 10));
        }


        protected static string ComputeHash(string input)
        {
            using (HashAlgorithm algorithm = new SHA256CryptoServiceProvider())
            {
                byte[] inputBytes = Encoding.UTF8.GetBytes(input);
                byte[] hashedBytes = algorithm.ComputeHash(inputBytes);
                return BitConverter.ToString(hashedBytes).Replace("-", "");
            }
        }

    }
}