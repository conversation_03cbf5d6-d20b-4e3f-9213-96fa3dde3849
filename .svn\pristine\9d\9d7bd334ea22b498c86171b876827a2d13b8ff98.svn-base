﻿using System;
using System.Collections.Generic;

namespace NewTicket
{
    public class LocalDaMa
    {
        //#齿轮#黑板#龙舟#麦克风#黑板擦#鼠标垫#麻花#黄瓜#麻绳#鼓#鸡蛋#麦当劳#麻球#鸭蛋#鱿鱼#鱼缸#鹦鹉#鹰#鳄鱼#鲨鱼#高压锅#鲸鱼#魔方#鸟笼#骆驼#骰子#鱼类#馒头#香炉#马#鸽#鱼丸#香菜#饭团#香肠#饭盒#馄饨#风车#飞镖#飞机#食用油#香囊#风筝#飞碟#领带#风铃#餐椅#鞭炮#餐桌#韭菜#青蛙#面条#青椒#鞋刷#靴子#青花瓷#青铜#青菜#雪碧#闪电#雪地靴#门把手#雨靴#雨衣#银行#雕像#闹钟#锅铲#锄头#锦旗#长颈鹿#镊子#镜子#铃铛#键盘#铁轨#铁锹#铁塔#铁盒#钻石#金字塔#钥匙圈#锅#钟表#金针菇#轮胎#锣#金鱼#迷彩服#过山车#透明胶#邮局#邮票#针线包#辣椒酱#运动服#道观#轮椅#路灯#跑步机#醋#跳绳#酒#邮戳#躺椅#购物车#贺卡#酱油#豆芽#贝壳#象棋#调色板#话梅#西瓜#调味品#试管#订书机#西装#警车#警示牌#西红柿#西瓜汁#袋鼠#请柬#裙子#被子#赛车#衣架#裤带#衣柜#血压计#螺丝刀#螃蟹#蜡烛#蜻蜓#蝙蝠#蝴蝶结#蝌蚪#蛋挞#蝴蝶#蜈蚣#蚂蚁#蜜蜂#蚯蚓#蜂蜜#蚊香#蜂窝#蜥蜴#蚊子#蜗牛#蛋糕#蚊香盘#蚊帐#薯片#虾#蒲公英#萤火虫#蒙古包#薯条#蚕#荷叶#菠萝#蓝莓#蒸笼#药片#荔枝#萝卜#草莓#藕#草坪#茶叶#花轿#苦瓜#花露水#葱#茶盅#花生#茶几#航母#芒果#自行车#腰果#船锚#舞龙#脸谱#肯德基#肉松#船桨#舞狮#胶囊#耳环#胶卷#耳麦#老虎#苍蝇拍#肥皂盒#花盆#羽绒服#花瓶#羽毛球拍#耳塞#羽毛球#羽毛#网线#翅膀#翡翠#红豆#网球拍#红领巾#罗盘#绿豆#绿茶#缝纫机#纽扣#红酒#红绿灯#纸牌#红枣#经筒#紫砂壶#箱子#糖葫芦#粽子#粉笔#篮球#箭头#算盘#章鱼#窗帘#窗户#竹席#竹篮#磁铁#福娃#空调#秋千#竹笋#笔架#硬币#矿泉水#秤砣#砖头#相片#睡袋#砚台#皮球#盒子#眼镜#盘子#石榴#盆栽#白酒#电饭煲#电风扇#白菜#白砂糖#盐#电话机#电话亭#电线#电视机#电梯#瓶子#电热壶#田螺#瓜子#电子秤#生姜#甘蔗#瓶盖#生蚝#瓷砖#球拍#琵琶#玉米#瓶塞#珍珠#猫头鹰#牛奶#猴子#玻璃瓶#珊瑚#珠宝#玛瑙#猕猴桃#牛仔裤#牙膏#猩猩#牙刷#猫#狮子#狐狸#犀牛#牙签#牌坊#热水袋#狗#爬山虎#熊猫#爆米花#烟灰缸#煤炭#热气球#热水瓶#煤油灯#熨斗#牌匾#烧烤架#热水器#烟花#烤鸭#火龙果#灯笼#烟囱#灭火器#火腿肠#火柴#火锅#灯塔#火车#灵芝#火山#火箭#烛台#瀑布#漏斗#滑板#消防车#海鸥#游泳池#游艇#渔网#海豹#消防栓#浏览器#海带#海苔#游泳圈#海豚#洋葱#流星#洗衣液#浴缸#海滩#泡沫#油条#油纸伞#海报#沙拉#油画#油漆#水表#油#沙包#沙漠#汤圆#气球#水管#汉堡#毛巾#水晶球#沙盘#毛衣#樱桃#毛线#榨汁机#橡皮擦#水母#毛笔#橄榄枝#橙汁#樟脑丸#槟榔#棉花糖#棉花#楼梯#横幅#椰子#椅子#梳子#枕头#棉棒#核桃#桂圆#档案袋#树叶#桌子#柚子#柜子#木马#板栗#校徽#桥#杏仁#木桶#月亮#本子#显示屏#救护车#显微镜#月饼#春联#斑马#易拉罐#文件夹#文具盒#日历#收音机#放大镜#救生圈#星星#明信片#教堂#收纳箱#摩天轮#收银机#指甲刀#擀面杖#拨浪鼓#挖掘机#排风机#拼图#拖鞋#摇篮#拉链#挂钟#抽屉#报纸#投影仪#拖把#扳手#报刊亭#披萨#打字机#护腕#打火机#扫把#手帕#托盘#手链#手掌印#手电筒#手机#户口本#手机壳#手铐#手套#彩虹#巧克力#录音机#恐龙#扇贝#扇子#戒指#年画#弹弓#弹簧#徽章#开心果#小提琴#开瓶器#帽子#工具箱#帐篷#干冰#学生证#床#山楂#存储卡#对讲机#寿司#存折#孔雀#奶嘴#字典#奶粉#婚纱#安全帽#奖状#奶糖#奶瓶#太阳#太阳能#大象#奥特曼#天鹅#塑料袋#复印机#壁虎#墨镜#圣诞树#地图#垃圾桶#地球仪#塑料瓶#垃圾袋#塑料杯#地毯#国旗#图书馆#土豆#围棋#天线#喷泉#围巾#哑铃#喷雾器#啤酒#呼啦圈#吸尘器#咖啡豆#听诊器#台球#名片#可乐#古筝#口香糖#吧椅#台布#口琴#发电机#口罩#双面胶#卷尺#南瓜#口哨#卫星#印章#发卡#吉他#叉子#向日葵#加油站#卡片#剪刀#办公椅#剪纸#发簪#勋章#加湿器#刺绣#割草机#创可贴#刺猬#发票#公路#公交卡#冰淇淋#冰柜#冰块#信用卡#写字桌#凉亭#光盘#保龄球#兔子#保温杯#信纸#信封#储蓄罐#保险箱#充电器#体温计#信箱#便利贴#人民币#冰箱#乒乓球#乒乓球拍#传真机#仙人球#云彩#二维码#乌龟#仪表盘#丹顶鹤#乌云#三明治#乐谱#人参#书柜#中国结#企鹅#三脚架#七星瓢虫#井盖#T恤#
        
        public static Dictionary<string, List<string>> LstTuKu = new Dictionary<string, List<string>>();

        public static string GetCode(byte[] buffer)
        {
            ImageType type = ImageType.Eight;
            string strName = "";
            return GetCode(buffer, ref strName, ref type);
        }

        public static string GetCode(byte[] buffer, ref string strName, ref ImageType type)
        {
            string result = "";
            if (CommonString.IsLocalCodeEnable)
            {
                try
                {
                    var lstCodes = GetCodeIndex(buffer, ref strName, ref type);
                    result = ImageComparator.GetCodePointByIndex(lstCodes, type);
                }
                catch (Exception oe)
                {
                    Console.WriteLine(oe.Message);
                }
            }
            return result.TrimEnd(',').TrimStart(',');
        }

        public static string GetIndexStr(byte[] buffer, ref string strName, ref ImageType type)
        {
            string result = "";
            if (CommonString.IsLocalCodeEnable)
            {
                try
                {
                    var lstCodes = GetCodeIndex(buffer, ref strName, ref type);
                    result = string.Join(",", lstCodes.ToArray());
                }
                catch (Exception oe)
                {
                    Console.WriteLine(oe.Message);
                }
            }
            return result.TrimEnd(',').TrimStart(',');
        }

        public static List<int> GetCodeIndex(byte[] buffer, ref string strName, ref ImageType type)
        {
            var result = new List<int>();
            try
            {
                strName = "";
                var lstHanZi = AutoCodeNew.GetHanZi(buffer, ref type);
                if (type == ImageType.Eight)
                {
                    if (lstHanZi != null && lstHanZi.Count > 0)
                    {
                        var lstImageEntity = ImageComparator.SpiltImageEntity(ImageComparator.GetImageByByte(buffer), type);
                        foreach (var key in lstHanZi)
                        {
                            bool isFind = false;
                            foreach (var item in key.Value)
                            {
                                string strTmp = item;
                                var codes = ImageComparator.GetCodes(buffer, ref strTmp, out type, false, lstImageEntity);
                                if (codes != null && codes.Count > 0)
                                {
                                    foreach (var p in codes)
                                    {
                                        if (!result.Contains(p))
                                        {
                                            result.Add(p);
                                        }
                                    }
                                    strName += item + ",";
                                    isFind = true;
                                    break;
                                }
                            }
                            if (!isFind)
                                strName += string.Join(",", key.Value.ToArray()) + ",";
                        }
                        lstImageEntity = null;
                    }
                }
            }
            catch (Exception oe)
            {
                Console.WriteLine(oe.Message);
            }
            strName = strName.TrimEnd(',');
            return result;
        }
    }
}
