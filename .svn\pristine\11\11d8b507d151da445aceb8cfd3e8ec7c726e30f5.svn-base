﻿using System;
using CommonLib;
using System.Collections.Generic;
using System.Collections.Specialized;
using System.IO;
using Newtonsoft.Json;

namespace MathOcr
{
    /// <summary>
    /// http://developer.hanvon.com/formula/toFormula.do
    /// </summary>
    public class HanWangRec : BaseMathRec
    {
        public HanWangRec()
        {
            OcrGroup = OcrGroupType.汉王;
            OcrType = MathOcrType.汉王;

            MaxExecPerTime = 20;

            LstJsonPreProcessArray = new List<object>() { "blocks" };
            IsSupportVertical = true;
            StrResultJsonSpilt = "formulaResult";
            LstVerticalLocation = new List<object>() { "rect" };
        }

        protected override string GetHtml(OcrContent content)
        {
            var result = "";
            try
            {
                var url = "http://developer.hanvon.com/formula/receive.do";
                var file = new UploadFileInfo()
                {
                    Name = "accessFile",
                    Filename = "1." + content.fileExt,
                    ContentType = ApplicationTypeHelper.GetApplicationType(content.fileExt),
                    Stream = new MemoryStream(Convert.FromBase64String(content.strBase64))
                };
                result = PostFile(url, new[] { file }, null);
            }
            catch (Exception)
            {

            }
            return result;
        }

        protected override LocationInfo GetLocationByStr(string locationInfoStr)
        {
            var loc = JsonConvert.DeserializeObject<VLocationInfo3>(locationInfoStr, new JsonSerializerSettings());
            return new LocationInfo
            {
                left = loc.left,
                top = loc.top,
                width = loc.width,
                height = loc.height
            };
        }

    }
}