﻿using CommonLib;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Collections.Specialized;
using System.Xml;

namespace HanZiOcr
{
    /// <summary>
    /// https://cloud.tencent.com/document/product/866/33526
    /// https://cloud.tencent.com/act/pro/ciExhibition?from=15022&tab=imageProcess&sub=contentRecognize&thirdSub=univerTextRecognize
    /// </summary>
    public class TencentWanXiangAPIRec : BaseOcrRec
    {
        public TencentWanXiangAPIRec()
        {
            OcrGroup = OcrGroupType.腾讯;
            OcrType = HanZiOcrType.腾讯万象;
            MaxExecPerTime = 20;

            LstJsonPreProcessArray = new List<object>() { "Response", "TextDetections" };
            StrResultJsonSpilt = "DetectedText";
            LstVerticalLocation = new List<object>() { "Polygon" };
            IsSupportVertical = true;
        }

        protected override string GetHtml(OcrContent content)
        {
            var result = "";
            var html = WebClientSyncExt.GetHtml("https://ci-exhibition.cloud.tencent.com/samples/createUploadKey?ext=png&ciProcess=OCR", ExecTimeOutSeconds);
            var token = JsonConvert.DeserializeObject<WanXiangRoot>(html);
            if (!string.IsNullOrEmpty(token?.data?.key))
            {
                try
                {
                    var url = "https://ci-h5-demo-1258125638.cos.ap-chengdu.myqcloud.com/" + token.data.key;
                    html = WebClientSyncExt.GetHtml(url, Convert.ToBase64String(Convert.FromBase64String(content.strBase64)), ExecTimeOutSeconds
                       , new NameValueCollection()
                       {
                        { "Content-Type", "image/png" },
                            { "Authorization", token.data.uploadAuthorization},
                       });
                    html = WebClientSyncExt.GetHtml(url + "?" + token.data.ciProcessAuthorization + "&ci-process=OCR", "", ExecTimeOutSeconds);
                    var doc = new XmlDocument();
                    doc.LoadXml(html);
                    result = JsonConvert.SerializeXmlNode(doc);
                }
                catch { }
            }
            return result;
        }

        public class WanXiangData
        {
            public string key { get; set; }
            public string uploadAuthorization { get; set; }
            public string downloadAuthorization { get; set; }
            public string ciProcessAuthorization { get; set; }
        }

        public class WanXiangRoot
        {
            public int code { get; set; }
            public string message { get; set; }
            public WanXiangData data { get; set; }
        }


    }
}