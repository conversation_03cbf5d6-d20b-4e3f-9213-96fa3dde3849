﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.IO;

namespace BaiDuAPI
{
    public class CommonHelper
    {
        public static string CreateIdByInt()
        {
            byte[] buffer = Guid.NewGuid().ToByteArray();
            string id = BitConverter.ToInt64(buffer, 0).ToString();

            return id;
        }

        public static string GetLastLog()
        {
            string result = "";

            try
            {
                var file = AppDomain.CurrentDomain.BaseDirectory + "\\Logs\\" + DateTime.Now.ToString("yyyyMM") + "\\" + DateTime.Now.ToString("dd") + ".log";
                if (File.Exists(file))
                {
                    var tmps = new List<string>();
                    tmps.AddRange(File.ReadAllLines(file));
                    if (tmps != null && tmps.Count > 0)
                    {
                        tmps.Reverse();
                        int len = 1000;
                        len = len > tmps.Count ? tmps.Count : len;
                        for (int i = 0; i < len; i++)
                        {
                            result += tmps[i] + Environment.NewLine;
                        }
                    }
                    else
                    {
                        result = "当前日志为空！";
                    }
                }
                else
                {
                    result = "日志文件不存在！";
                }
            }
            catch (Exception oe)
            {
                BaiDuCode._Log.Error("加载日志失败！", oe);
            }
            return result;
        }

    }
}