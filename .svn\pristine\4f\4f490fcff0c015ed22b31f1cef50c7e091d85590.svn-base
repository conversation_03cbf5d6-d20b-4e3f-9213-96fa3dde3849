﻿using CommonLib;
using System;
using System.Collections.Generic;
using System.Collections.Specialized;
using System.Linq;

namespace HanZiOcr
{
    /// <summary>
    /// https://brandfolder.com/workbench/extract-text-from-image
    /// https://cloud.google.com/vision/docs/request?hl=zh-cn
    /// </summary>
    public class GoogleDocApiRec : BaseOcrRec
    {
        public GoogleDocApiRec()
        {
            OcrType = HanZiOcrType.GoogleDoc;

            MaxExecPerTime = 25;

            IsSupportVertical = true;
            IsSupportUrlOcr = true;

            LstJsonPreProcessArray = new List<object>() { "responses" };
            LstJsonNextProcessArray = new List<object>() { "fullTextAnnotation", "pages", "blocks", "paragraphs", "words", "symbols" };
            StrResultJsonSpilt = "text";
            LstVerticalLocation = new List<object>() { "boundingBox" };
            StrSpaceKey = "type";
            StrSpaceValue = "SPACE";
            StrLineKey = "type";
            StrLineValue = "LINE_BREAK";
        }

        protected override string GetHtml(OcrContent content)
        {
            return RequestHtmlContent(content.strBase64);
        }

        public override string GetHtmlByUrl(OcrContent content)
        {
            return RequestHtmlContent(null, content.url);
        }

        //view-source:https://brandfolder.com/workbench/extract-text-from-image
        //const BROWSER_GOOGLE_VISION_API_KEY ='AIzaSyAV-SXt0qiF5aHdn-Zgcl4Gr61_gxx28qs';

        private List<string> lstApiKeys = new List<string>() { "AIzaSyBL0QYPScnrjHML8i9Kq5VLXVuqoiM_34o", "AIzaSyCs7a3C69UMSf8Sy2rPWNJzIF9uqX90jvg", "AIzaSyA7ZlydINXmk61P2lz3sDi0ACSIwJEloUY", "AIzaSyDDotUJaMWtaKJrvbVC_s-sE-DTbnR065A", "AIzaSyDJldBAoY9XqVcRrRbww0tHyX8LrBZ9AXg", "AIzaSyB8k-Lc5ry1h2p5cMQU122r7EKN9_FloO8", "AIzaSyCZs_vdFd2lI7650JuXabYNJUh4ljzTFgk", "AIzaSyDXrtDTnhaSQNS69QumAZ_TNjRAoPlivl4", "AIzaSyAfODrfyamagW_DLjezjgVJRZ8XsXSkUCQ", "AIzaSyAV-SXt0qiF5aHdn-Zgcl4Gr61_gxx28qs", "AIzaSyCbJhOwLHEkuKM6d7yxNkfs17snC3PCFbw" };

        private string RequestHtmlContent(string strBase64, string imgUrl = null)
        {
            var apiKey = lstApiKeys.GetRndItem();
            if (string.IsNullOrEmpty(apiKey))
            {
                State = EnableState.禁用;
                return "";
            }
            var strPost = "{\"requests\":[{\"image\":{\"" + (string.IsNullOrEmpty(imgUrl) ?
                "content\":\"" + strBase64 + "\""
                : "source\":{\"imageUri\":\"" + imgUrl + "\"}") + "},\"features\":[{\"type\":\"DOCUMENT_TEXT_DETECTION\"}]}]}";
            var url = "https://vision.googleapis.com/v1/images:annotate?key=" + apiKey;
            var result = WebClientSyncExt.GetHtml(url, "", strPost, "https://brandfolder.com", ExecTimeOutSeconds);
            if (result.Contains("\"API key not valid")
            || result.Contains("\"API Key not found")
            || result.Contains("\"API key expired")
            || result.Contains("\"This API method requires")
            || result.Contains("\"Cloud Vision API has not been used")
            || result.Contains("\"Requests from this Android client application")
            || result.Contains("\"Requests from referer"))
            {
                lstApiKeys.Remove(apiKey);
                result = null;
            }
            return result;
        }

        private string GetKeys(string strBase64, string imgUrl = null)
        {
            //https://github.com/search?q=%2Fimages%3Aannotate%5C%3Fkey%3D%5Ba-zA-Z0-9_-%5D%7B20%2C%7D%2F&type=code
            var strTmp = "";
            var lstKeys = new List<string>();
            var strLine = strTmp.Split(new string[] { "\n" }, StringSplitOptions.RemoveEmptyEntries);
            foreach (var item in strLine)
            {
                if (!item.Contains("annotate?key="))
                    continue;
                var key = item.Substring(item.IndexOf("annotate?key=") + "annotate?key=".Length);
                if (key.Contains(" "))
                    key = key.Substring(0, key.IndexOf(" "));
                if (key.Contains("'"))
                    key = key.Substring(0, key.IndexOf("'"));
                if (key.Contains("\""))
                    key = key.Substring(0, key.IndexOf("\""));
                if (key.Contains("/"))
                    key = key.Substring(0, key.IndexOf("/"));
                lstKeys.Add(key.Trim());
            }
            lstKeys = lstKeys.Distinct().ToList();
            foreach (var apiKey in lstKeys)
            {
                var strPost = "{\"requests\":[{\"image\":{\"" + (string.IsNullOrEmpty(imgUrl) ?
                    "content\":\"" + strBase64 + "\""
                    : "source\":{\"imageUri\":\"" + imgUrl + "\"}") + "},\"features\":[{\"type\":\"DOCUMENT_TEXT_DETECTION\"}]}]}";
                var url = "https://vision.googleapis.com/v1/images:annotate?key=" + apiKey;
                var result = WebClientSyncExt.GetHtml(url, "", strPost, "https://brandfolder.com", ExecTimeOutSeconds);
                if (result.Contains("\"API key not valid")
                || result.Contains("\"API Key not found")
                || result.Contains("\"API key expired")
                || result.Contains("\"This API method requires")
                || result.Contains("\"Cloud Vision API has not been used")
                || result.Contains("\"Requests from this Android client application")
                || result.Contains("\"Requests from referer"))
                {
                }
                else
                {
                    Console.WriteLine(apiKey);
                }

            }
            return "";
        }

    }
}