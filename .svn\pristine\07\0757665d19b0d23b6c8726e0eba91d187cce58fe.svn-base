﻿using System;
using System.Windows.Forms;
using System.Net;
using System.Security.Cryptography.X509Certificates;
using System.Net.Security;
using System.Threading;

namespace IPScan
{
    static class Program
    {
        /// <summary>
        /// 应用程序的主入口点。
        /// </summary>
        [STAThread]
        static void Main()
        {
            Application.EnableVisualStyles();
            Application.SetCompatibleTextRenderingDefault(false);

            try
            {
                //CommonMethod.CheckFrameWork();
                ServicePointManager.SecurityProtocol = SecurityProtocolType.Ssl3
                                                       | SecurityProtocolType.Tls
                                                       | (SecurityProtocolType)0x300 //Tls11
                                                       | (SecurityProtocolType)0xC00; //Tls12
                ServicePointManager.ServerCertificateValidationCallback = ((object sender, X509Certificate certificate, X509Chain chain, SslPolicyErrors sslPolicyErrors) => true);
                //ServicePointManager.Expect100Continue = false;
                ServicePointManager.DefaultConnectionLimit = int.MaxValue;
                //if (ServicePointManager.DefaultConnectionLimit < 200)
                //{
                //    ServicePointManager.DefaultConnectionLimit = 200;
                //}
                //ServicePointManager.MaxServicePoints = int.MaxValue;
                //ServicePointManager.MaxServicePointIdleTime = int.MaxValue;
                ServicePointManager.UseNagleAlgorithm = false;
                //ServicePointManager.CheckCertificateRevocationList = false;
                ServicePointManager.Expect100Continue = false;
                //ServicePointManager.SetTcpKeepAlive(true, 1000, 200);
                GlobalProxySelection.Select = GlobalProxySelection.GetEmptyWebProxy();
                WebRequest.DefaultWebProxy = null;

                var workerThreads = 0;
                var completionPortThreads = 0;
                ThreadPool.GetAvailableThreads(out workerThreads, out completionPortThreads);
                Console.WriteLine("默认配置：工作线程：{0}，完成端口：{1}", workerThreads, completionPortThreads);
                var result = ThreadPool.SetMinThreads(workerThreads - 1, completionPortThreads - 1);
            }
            catch
            {
                // ignored
            }

            WebRequest.DefaultWebProxy = null;
            Application.Run(new frmMain());
        }
    }
}
