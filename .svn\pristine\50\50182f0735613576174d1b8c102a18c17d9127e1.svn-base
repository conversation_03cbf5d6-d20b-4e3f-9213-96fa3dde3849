namespace ServiceStack.<PERSON>.<PERSON>
{
    public class JsonOneWayHandler : <PERSON>ric<PERSON>andler
    {
        public JsonOneWayHandler()
            : base(MimeTypes.Json, RequestAttributes.OneWay | RequestAttributes.<PERSON><PERSON>, Feature.Json)
        {
        }
    }

    public class JsonReplyHandler : GenericHandler
    {
        public JsonReplyHandler()
            : base(MimeTypes.J<PERSON>, RequestAttributes.Reply | RequestAttributes.<PERSON><PERSON>, Feature.Json)
        {
        }
    }
}