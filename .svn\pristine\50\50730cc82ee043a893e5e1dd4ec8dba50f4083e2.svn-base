using System.Collections.Generic;
using System.Linq;

namespace Enterprise.Framework.Redis.Config
{
	internal class RedisDBConfig
	{
		private List<RedisConnection> _servers;

		private List<RedisSentinelConnection> _sentinelServer;

		private RedisSentinelsConfig _globalSentinelsConfig;

		public string DBName { get; private set; }

		public int DBNumber { get; private set; }

		public string MasterNameInSentinel { get; private set; }

		public RedisDBConfigOptions Options { get; private set; }

		public bool IsUsedSentinel => !string.IsNullOrEmpty(MasterNameInSentinel);

		public string[] WriteServers
		{
			get
			{
				IEnumerable<string> source = from o in _servers
					where !o.IsReadOnly
					select o.ToString();
				if (source.Count() == 0)
				{
					throw new RedisException("请至少配置一台可写的Redis服务器");
				}
				return source.ToArray();
			}
		}

		public string[] ReadServers
		{
			get
			{
				IEnumerable<string> source = _servers.Select((RedisConnection o) => o.ToString());
				if (source.Count() == 0)
				{
					throw new RedisException("请至少配置一台Redis服务器");
				}
				return source.ToArray();
			}
		}

		public RedisDBConfig(string dbName, RedisDBConfigOptions options)
			: this(dbName, 0, null, options)
		{
		}

		public RedisDBConfig(string dbName, int dbNumber, RedisDBConfigOptions options)
			: this(dbName, dbNumber, null, options)
		{
		}

		public RedisDBConfig(string dbName, string masterName, RedisDBConfigOptions options)
			: this(dbName, 0, masterName, options)
		{
		}

		public RedisDBConfig(string dbName, int dbNumber, string masterName, RedisDBConfigOptions options)
		{
			DBName = dbName;
			DBNumber = dbNumber;
			MasterNameInSentinel = masterName;
			Options = options;
			_servers = new List<RedisConnection>();
			_sentinelServer = new List<RedisSentinelConnection>();
		}

		public void AddServer(RedisConnection conn)
		{
			_servers.Add(conn);
		}

		public void AddSentinel(RedisSentinelConnection conn)
		{
			_sentinelServer.Add(conn);
		}

		public void SetGlobalSentinel(RedisSentinelsConfig sentinelConfig)
		{
			_globalSentinelsConfig = sentinelConfig;
		}

		public string[] GetSentinelHosts()
		{
			if (_sentinelServer.Count > 0)
			{
				string[] array = new string[_sentinelServer.Count];
				for (int i = 0; i < _sentinelServer.Count; i++)
				{
					if (_sentinelServer[i].Port.HasValue)
					{
						array[i] = $"{_sentinelServer[i].Host}:{_sentinelServer[i].Port.Value}";
					}
					else
					{
						array[i] = _sentinelServer[i].Host;
					}
				}
				return array;
			}
			if (_globalSentinelsConfig != null)
			{
				return _globalSentinelsConfig.GetSentinelHosts();
			}
			throw new RedisException("要么给该RedisDB配置Sentinel，要么配置全局的Sentinel");
		}
	}
}
