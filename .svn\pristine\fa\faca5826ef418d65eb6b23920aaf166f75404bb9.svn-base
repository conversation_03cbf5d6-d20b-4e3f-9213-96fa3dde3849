﻿namespace monocaptcha
{
    public abstract class AbstractAnswerResult : IAnswerResult
    {
        protected byte[] pictureData;
        public void setPictureData(byte[] pictureData)
        {
            this.pictureData = pictureData;
        }

        public byte[] getPictureData()
        {
            return this.pictureData;
        }

        public abstract void handle(ResponseEnum status, string answer, int yue);
    }
}