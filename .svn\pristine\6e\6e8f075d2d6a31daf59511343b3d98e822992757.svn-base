
$('body').ready(function () {
  var timerId = null
  function activeCommonTwoItem (ele) {
    var period = 5000
    $('.commonTwo-type-item').removeClass('commonTwo-type-item--on')
    $(ele).addClass('commonTwo-type-item--on')
    var index = $('.commonTwo-type-item').index(ele)
    $('.commonTwo-show-image').hide()
    $('.commonTwo-show-image:eq(' + index + ')' ).show()
    $('.commonTwo-type-item').not(ele).find('.progress').css({ width: 0 }).stop()
    $(ele).find('.progress').animate({ width: '100%' }, period)
    
    timerId = setTimeout(() => {
      var nextEle = null
      if (index === $('.commonTwo-type-item').length - 1) {
        nextEle = $('.commonTwo-type-item:eq(0)')
      } else {
        nextEle = $('.commonTwo-type-item:eq(' + (index + 1) + ')')
      }
      activeCommonTwoItem(nextEle)
    }, period)
  }
  $('.commonTwo-type-item').click(function (e, i) {
    clearTimeout(timerId)
    activeCommonTwoItem(this)
  })
  activeCommonTwoItem($('.commonTwo-type-item:eq(0)'))

  jQuery('.index_banner').slide({
    titCell: '.hd ul',
    mainCell: '.bd ul',
    effect: 'fold',
    autoPlay: true,
    autoPage: true,
    trigger: 'click',
    delayTime: 3000,
    interTime: 7000
  })

  // 导航栏
  var map = {
    card: 'ai',
    invoice: 'ai',
    system: 'ai',
    contract: 'ai',
    manage: 'ai',
    train: 'ai',
    database: 'bigdata',
    api: 'bigdata',
    qxb: 'bigdata',
    bankcard: 'solution',
    securities: 'solution',
    insurance: 'solution',
    government: 'solution',
    supply_chains: 'solution',
    mobile_internet: 'solution',
    saas: 'solution',
    'personal-camcard': 'star',
    'personal-camscanner': 'star',
    'camcardbusiness': 'star',
    'qixinbao': 'star',
    about: 'hehe',
    contactus: 'hehe',
    recruit: 'hehe'
  }
  var file = ''
  var res = ''
  if (!window.location.pathname.includes('shtml')) {
    file =  window.location.pathname.split('/')
    res = file[file.length - 1]
  } else {
    file = window.location.pathname.split('/')
    res = file[file.length - 1].split('.')[0]
  }
  $('.nav-item').removeClass('nav-item--on')
  if (window.location.pathname === '/solution/index.shtml') {
    $('#solutionNav').addClass('nav-item--on')
  } else if (res) {
    $('#' + map[res] + 'Nav').addClass('nav-item--on')
  } else {
    $('#homeNav').addClass('nav-item--on')
  }


  // 返回顶部
  $('.return-top').click(function () {
    document.body.scrollTop = 0;
    $('html').animate({ scrollTop: 0 }, 300)
  })
  // 锚点
  $(".anchor").smartFloat();
  $('.anchor-item a').click(function () {
    var id = $(this).attr('data')
    var agent = navigator.userAgent.toLowerCase()
    var offsetTop = $('#' + id).attr('offsetTop')
    if (agent.indexOf("windows") >= 0) { // 目前仅有windows会出现自动放大的情况
      offsetTop = offsetTop * 1 / window.devicePixelRatio
    }
    $('html').animate({ scrollTop: offsetTop - 200 }, 300)
  })

  // 兼容windows自推荐150%放大
  var agent = navigator.userAgent.toLowerCase()
  if (agent.indexOf("windows") >= 0) { // 目前仅有windows会出现自动放大的情况
    correctDevicePixeRatio()
    window.onresize = correctDevicePixeRatio
  }
  function correctDevicePixeRatio () {
    $('body').css({ 'zoom': 1 / window.devicePixelRatio })
  }
})