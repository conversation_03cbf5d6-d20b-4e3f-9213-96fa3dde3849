﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="12.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{213EF4BA-786A-432F-B147-5702B18DE3CC}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>Check.ServiceModel</RootNamespace>
    <AssemblyName>Check.ServiceModel</AssemblyName>
    <TargetFrameworkVersion>v4.7.2</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <TargetFrameworkProfile />
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="System" />
    <Reference Include="System.ComponentModel.DataAnnotations" />
    <Reference Include="System.Core" />
    <Reference Include="System.Net" />
    <Reference Include="System.Runtime.Serialization" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Annotations.cs" />
    <Compile Include="AutoQueryTypes.cs" />
    <Compile Include="CodeGenTestTypes.cs" />
    <Compile Include="CustomMethodRoutes.cs" />
    <Compile Include="DynamicallyRegisteredService.cs" />
    <Compile Include="Echoes.cs" />
    <Compile Include="Errors.cs" />
    <Compile Include="MetadataTest.cs" />
    <Compile Include="NativeTypeIssues.cs" />
    <Compile Include="QueryPosts.cs" />
    <Compile Include="ReturnData.cs" />
    <Compile Include="Rockstar.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="SwaggerTest.cs" />
    <Compile Include="TechStacks.cs" />
    <Compile Include="ThrowHttpError.cs" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\..\src\ServiceStack.Interfaces\ServiceStack.Interfaces.csproj">
      <Project>{55942102-033a-4da8-a6af-1db7b2f34a2d}</Project>
      <Name>ServiceStack.Interfaces</Name>
    </ProjectReference>
    <ProjectReference Include="..\..\src\ServiceStack\ServiceStack.csproj">
      <Project>{680A1709-25EB-4D52-A87F-EE03FFD94BAA}</Project>
      <Name>ServiceStack</Name>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>