﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace NewTicket.Domain
{
    public class TongChengTicket
    {
        /// <summary>
        /// Trains
        /// </summary>
        public List<TongChengTrains> trains { get; set; }
    }

    public class TongChengTrains
    {
        /// <summary>
        /// K1108
        /// </summary>
        public string trainNum { get; set; }
        /// <summary>
        /// 21:20
        /// </summary>
        public string fromTime { get; set; }
        /// <summary>
        /// 13:05
        /// </summary>
        public string toTime { get; set; }
        /// <summary>
        /// 南阳
        /// </summary>
        public string BeginPlace { get; set; }
        /// <summary>
        /// 上海
        /// </summary>
        public string EndPlace { get; set; }
        /// <summary>
        /// 南阳
        /// </summary>
        public string fromCity { get; set; }
        /// <summary>
        /// nanyang
        /// </summary>
        public string fromCityPy { get; set; }
        /// <summary>
        /// 上海
        /// </summary>
        public string toCity { get; set; }
        /// <summary>
        /// 15小时45分钟
        /// </summary>
        public string usedTime { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string note { get; set; }
        /// <summary>
        /// 正常车次，不受控
        /// </summary>
        public string TrainFlagMsg { get; set; }
    }

}
