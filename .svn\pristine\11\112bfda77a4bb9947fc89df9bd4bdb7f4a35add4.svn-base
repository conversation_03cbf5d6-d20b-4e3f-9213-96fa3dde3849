﻿using CommonLib;
using Newtonsoft.Json.Linq;
using System.Collections.Generic;

namespace TableOcr
{
    public abstract class BaseTableRec : BaseRec
    {
        public override ResutypeEnum ResultType { get; set; } = ResutypeEnum.表格;

        private TableOcrType _OcrType;

        public new TableOcrType OcrType
        {
            get { return _OcrType; }
            set
            {
                _OcrType = value;
                base.OcrType = value.GetHashCode();
            }
        }

        public override string GetOcrTypeName()
        {
            return OcrType.ToString();
        }

        public override int GetOcrType()
        {
            return OcrType.GetHashCode();
        }

        #region JSON字符串处理

        protected bool RowIndexIsArray { get; set; }

        protected bool IsRowIndexAddOne { get; set; }

        /// <summary>
        /// Cell是否为数组方式
        /// </summary>
        protected bool IsCellArray { get; set; }

        protected List<object> LstRowIndex { get; set; }

        protected List<object> LstColumnIndex { get; set; }

        public List<object> LstFileUrlProcessArray { get; set; }

        /// <summary>
        /// 是否为竖排转表格
        /// </summary>
        protected bool IsTextToTable { get; set; }

        /// <summary>
        /// 是否为Base64转表格
        /// </summary>
        protected bool IsBase64ToTable { get; set; }

        #endregion

        protected override ResultEntity GetProcessText(OcrContent content, string html)
        {
            //string result = GetSpiltTextByJson(html);
            var result = base.GetProcessText(content, html);
            if (IsTextToTable)
            {
                result.ToTable();
            }
            else
            {
                if (!string.IsNullOrEmpty(result?.resultHtml))
                {
                    var array = JArray.Parse(result.resultHtml);
                    var tableInfo = OcrResultProcess.GetContentFromArray(array, RowIndexIsArray, IsRowIndexAddOne,IsCellArray, LstRowIndex, LstColumnIndex, LstJsonResultProcessArray);
                    if (tableInfo?.rows?.Count > 0)
                    {
                        var resultStr = ConstHelper.JavaScriptSerializer.Serialize(tableInfo);
                        result.autoText = result.spiltText = resultStr;
                    }
                }
                else
                {
                    if (LstFileUrlProcessArray?.Count > 0)
                    {
                        result = OcrHtmlProcess.GetSpiltTextByJsonNew(html, ResultType, false
                           , LstFileUrlProcessArray, LstJsonNextProcessArray, LstJsonLastProcessArray, LstJsonLocationProcessArray
                           , StrResultJsonSpilt, StrResultTransJsonSpilt,
                           StrSpaceKey, StrSpaceValue, StrLineKey, StrLineValue, StrContactCell,
                           IsMultiPage, IsAutoPageIndex, IsSupportVertical,
                           IsJsonArrayString, IsJsonArrayStringWithLocation, StrJsonArrayWordSpilt
                           , IsFromLeftToRight, IsFromTopToDown
                           , LstVerticalLocation, DicDeserializeVerticalJson, IsDesrializeVerticalByLocation, this.GetLocationByStr
                           , content.IsAutoFull2Half, content.IsAutoSpace, content.IsAutoSymbol, content.IsAutoDuplicateSymbol, IsPercentSize, content.Size);
                        if (!string.IsNullOrEmpty(result.autoText) && result.autoText.StartsWith("http"))
                        {
                            result.files = new List<DownLoadInfo>();
                            result.resultType = ResutypeEnum.网页;
                            result.viewUrl = result.autoText;
                            var viewFileDown = new DownLoadInfo()
                            {
                                fileType = OcrFileType.Xls,
                                desc = OcrFileType.Xls.ToString(),
                                url = result.autoText
                            };
                            result.files.Add(viewFileDown);
                            result.autoText = content.id;
                            return result;
                        }
                    }
                }
            }
            return result;
        }
    }
}
