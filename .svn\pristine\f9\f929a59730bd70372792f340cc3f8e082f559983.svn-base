// Code generated by Microsoft (R) AutoRest Code Generator 1.0.1.0
// Changes may cause incorrect behavior and will be lost if the code is
// regenerated.

namespace AutorestClient.Models
{
    using Newtonsoft.Json;
    using System.Linq;

    /// <summary>
    /// HelloString
    /// </summary>
    /// <remarks>
    /// HelloString
    /// </remarks>
    public partial class HelloString
    {
        /// <summary>
        /// Initializes a new instance of the HelloString class.
        /// </summary>
        public HelloString()
        {
          CustomInit();
        }

        /// <summary>
        /// Initializes a new instance of the HelloString class.
        /// </summary>
        public HelloString(string name = default(string))
        {
            Name = name;
            CustomInit();
        }

        /// <summary>
        /// An initialization method that performs custom operations like setting defaults
        /// </summary>
        partial void CustomInit();

        /// <summary>
        /// </summary>
        [JsonProperty(PropertyName = "Name")]
        public string Name { get; set; }

    }
}
