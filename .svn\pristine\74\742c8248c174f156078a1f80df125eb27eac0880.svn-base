echo off
chcp 65001

set "msbuildPath=C:\Program Files\Microsoft Visual Studio\2022\Enterprise\MSBuild\Current\Bin\MSBuild.exe"
set "projectPath=D:\Code\Code\Code.Process.Web\Code.Process.Web.csproj"
set "publishProfilesFolder=D:\Code\Code\Code.Process.Web\Publish\"

for %%F in ("%publishProfilesFolder%*.pubxml") do (
	"%msbuildPath%" "%projectPath%" /p:DeployOnBuild=true /p:Configuration=Release /p:PublishProfile="%publishProfilesFolder%%%~nF.pubxml" /p:SkipInvalidConfigurations=true
	for /f "tokens=3 delims=><" %%i in ('findstr /i /c:"https" "%publishProfilesFolder%%%~nF.pubxml"') do (
		start "" "%%i"
	)
	cls
)