:root {
    --sidebar-width: 20rem;
    --sm-nav-height: 0px;
    --top-nav-height: 65px;
    --sub-nav-height: 45px;
    --scroll-width: 8px;
    --body-width: calc(100vw - var(--sidebar-width));
    --pb-scroll: 0;
}
@media (max-width: 768px) {
    :root {
        --pb-scroll: 8rem; /* fix scrolling to bottom */
        --sm-nav-height: 45px;
    }
    .md\:pb-scroll { padding-bottom: var(--pb-scroll) !important }
}
.top-sm-nav { top: calc(var(--sm-nav-height)) }
.top-top-nav { top: calc(var(--sm-nav-height) + var(--top-nav-height)) }
.top-sub-nav { top: calc(var(--sm-nav-height) + var(--top-nav-height) + var(--sub-nav-height)) }
.w-sidebar { width: var(--sidebar-width); }
.max-w-sidebar { max-width: var(--sidebar-width); }
.h-top-nav { height: calc(100vh - var(--sm-nav-height) - var(--top-nav-height)) }
.h-sub-nav { height: calc(100vh - var(--sm-nav-height) - var(--top-nav-height) - var(--sub-nav-height)) }
.pt-top-nav { padding-top: calc(var(--sm-nav-height) + var(--top-nav-height)) }
.w-body { width: 100vw }
@media (min-width: 768px) {
    .md\:w-sidebar { width: var(--sidebar-width) }
    .md\:pl-sidebar { padding-left: var(--sidebar-width) }
    .md\:w-sidebar { width: calc(100vw - var(--sidebar-width)) !important }
    .md\:w-body { width: calc(100vw - var(--sidebar-width)) }
}
.icon-right .icon {
    right: 1em;
}
.notes a {
    color: rgb(30 64 175); /*text-blue-800*/
}
