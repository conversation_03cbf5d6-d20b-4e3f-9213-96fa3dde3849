﻿using CommonLib;
using System;
using System.Collections.Generic;
using System.Collections.Specialized;
using System.IO;

namespace HanZiOcr
{
    /// <summary>
    /// 易享未来小程序-识图翻译
    /// </summary>
    public class YiXiangWeiLaiRec : BaseOcrRec
    {
        public YiXiangWeiLaiRec()
        {
            OcrType = HanZiOcrType.易享未来;
            MaxExecPerTime = 22;
            //IsSupportUrlOcr = true;

            LstJsonPreProcessArray = new List<object>() { };

            IsSupportVertical = true;
            IsProcessJsonResultByArray = true;
        }


        protected override string GetHtml(OcrContent content)
        {
            var byt = Convert.FromBase64String(content.strBase64);

            var result = PostFileResult(byt);
            //"{\"code\":0,\"msg\":\"成功\",\"data\":\"[{\\\"confidence\\\":1,\\\"word\\\":[{\\\"content\\\":\\\"请点击下图中所有的靴子\\\"},{\\\"content\\\":\\\"火柴\\\"}]}]\",\"content\":null,\"audioPath\":null}"
            return result;
        }

        private string PostFileResult(byte[] content)
        {
            var result = "";
            try
            {
                var url = "https://ctszj.yxweilai.cn/tp5/public/index.php/ctsz/sxt";
                /*
candidateLangs  zh|en
guid"	oqdgX0eKRj3vbSC6GQBQxs_rlz7Y
myfile"	wxfile://temp/qq.png
platform"	WeChat_APP
scene"	doc
source"	auto
target"	auto
type"	png
user"	wechatLittle
                 */
                var posts = new NameValueCollection()
                {
                    {"user","test" },
                };
                var file = new UploadFileInfo()
                {
                    Name = "file",
                    Filename = "qq.png",
                    ContentType = "image/png",
                    Stream = new MemoryStream(content)
                };
                var headers = new NameValueCollection()
                {
                    {"User-Agent","Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/53.0.2785.143 Safari/537.36 MicroMessenger/7.0.9.501 NetType/WIFI MiniProgramEnv/Windows WindowsWechat" },
                    {"Referer","https://servicewechat.com/wx8705e7e6d1d1d59c/28/page-frame.html" },
                };
                result = PostFile(url, new[] { file }, posts, headers);
            }
            catch (Exception)
            {

            }
            return result;
        }

    }
}