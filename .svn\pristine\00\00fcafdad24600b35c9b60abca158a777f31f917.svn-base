﻿using CommonLib;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Net;
using System.Security.Policy;
using System.Text;
using System.Web;

namespace Account.Web
{
    public static class CommonTranslate
    {
        public const string UserTranslageKey = "TransCache";
        public const string WebTranslageKey = "WebTrans";

        public static string GetTrans(this string source, HttpRequest Request)
        {
            var lang = Request.GetValue("lang");
            return GetTrans(source, lang);
        }

        public static string GetTrans(this string source, string lang = null, string key = WebTranslageKey)
        {
            if (!string.IsNullOrEmpty(source) && !string.IsNullOrEmpty(lang))
            {
                lang = GetCurrentLang(lang);
                if (!string.IsNullOrEmpty(lang) && !Equals(lang, StrDefaultLang))
                {
                    try
                    {
                        var dicTrans = GetCache(key);
                        if (dicTrans.ContainsKey(source) && dicTrans[source].ContainsKey(lang))
                        {
                            source = dicTrans[source][lang];
                        }
                    }
                    catch { }
                }
            }
            return source;
        }

        private static Dictionary<string, Dictionary<string, string>> GetCache(string key)
        {
            return JsonConvert.DeserializeObject<Dictionary<string, Dictionary<string, string>>>(CodeProcessHelper.ServerConfigCache.Get(key) ?? "") ?? new Dictionary<string, Dictionary<string, string>>();
        }

        public static void SetTranslate(string strLanguage, string strSource, string strTrans, string strKey)
        {
            var dicAll = GetCache(strKey);
            if (!dicAll.ContainsKey(strSource))
                dicAll[strSource] = new Dictionary<string, string>();
            dicAll[strSource][strLanguage] = strTrans;
            CodeProcessHelper.ServerConfigCache.Set(strKey, JsonConvert.SerializeObject(dicAll));
        }

        private static Dictionary<string, Dictionary<string, string>> GetPageCache(string key)
        {
            return JsonConvert.DeserializeObject<Dictionary<string, Dictionary<string, string>>>(CodeProcessHelper.PageTransCache.Get(key) ?? "") ?? new Dictionary<string, Dictionary<string, string>>();
        }

        public static string GetPageTrans(this string strUrl, string strUrlFile, string lang = null)
        {
            var cacheContent = string.Empty;
            if (!string.IsNullOrEmpty(strUrl) && !string.IsNullOrEmpty(strUrlFile) && !string.IsNullOrEmpty(lang) && File.Exists(strUrlFile))
            {
                try
                {
                    strUrl = strUrl.ToUpper().Trim();
                    var key = string.Format("{0}_{1}", strUrl, File.GetLastWriteTime(strUrlFile).ToString("yyyy-MM-dd HHmmss"));
                    var dicTrans = GetPageCache(key);
                    if (dicTrans.ContainsKey(strUrl) && dicTrans[strUrl].ContainsKey(lang))
                    {
                        cacheContent = dicTrans[strUrl][lang];
                    }
                }
                catch { }
            }
            return cacheContent;
        }

        public static void SetPageTranslate(string strLanguage, string strUrl, string strUrlFile, string strTrans)
        {
            if (string.IsNullOrEmpty(strUrlFile) || !File.Exists(strUrlFile))
                return;
            strUrl = strUrl.ToUpper().Trim();
            var strKey = string.Format("{0}_{1}", strUrl, File.GetLastWriteTime(strUrlFile).ToString("yyyy-MM-dd HHmmss"));
            var dicAll = GetPageCache(strKey);
            if (!dicAll.ContainsKey(strUrl))
                dicAll[strUrl] = new Dictionary<string, string>();
            dicAll[strUrl][strLanguage] = strTrans;
            CodeProcessHelper.PageTransCache.Set(strKey, JsonConvert.SerializeObject(dicAll));
        }

        public static string StrHost = "https://ocr.oldfish.cn/";

        public static string StrDefaultLang = "zh-CN";

        public static Dictionary<string, List<string>> DicJsLanguage = new Dictionary<string, List<string>>() {
            { "zh-CN",new List<string>(){"simplifiedchinese","chinese_simplified","zh-hans","zh-sg","zh"}},
            { "zh-HK",new List<string>(){"traditionalchinese","chinese_traditional","zh-tw","zh-hant","zh-mo"}},
            { "en-US",new List<string>(){"english","en"}},
            { "ja-JP",new List<string>(){"japanese","ja"}},
            { "ko-KR",new List<string>(){"korean","ko"}},
            { "fr-FR",new List<string>(){"french","fr"}},
            { "de-DE",new List<string>(){"german", "deutsch","de"}},
            { "ru-RU",new List<string>(){"russian","ru"}},
            { "uk-UA",new List<string>(){"ukrainian","uk"}},
            { "ar-SA",new List<string>(){"arabic","ar"}},
            { "es-ES",new List<string>(){"spanish","es"}},
            { "th-TH",new List<string>(){"thai","th"}},
            { "vi-VN",new List<string>(){"vietnamese","vi"}},
            { "pt-PT",new List<string>(){"portuguese","pt"}},
            { "he-IL",new List<string>(){"hebrew","he"}},
            { "hu-HU",new List<string>(){"hungarian","hu"}},
            { "id-ID",new List<string>(){"indonesian","id"}},
            { "it-IT",new List<string>(){"italian","it"}},
            { "fa-IR",new List<string>(){"persian","fa"}},
            { "pl-PL",new List<string>(){"polish","pl"}},
            { "ro-RO",new List<string>(){"romanian","ro"}},
            { "tr-TR",new List<string>(){"turkish","tr"}},
            { "nl-NL",new List<string>(){"dutch","nl"}},
            { "hi-IN",new List<string>(){"hindi","hi"}},
            { "ta-IN",new List<string>(){"tamil","ta"}},
            { "sr-Latn-RS",new List<string>(){ "serbian"}},
            { "sv-SE",new List<string>(){"swedish","sv"}},
            { "ms-MY",new List<string>(){"malay","ms"}},
            { "cs",new List<string>(){"czech","cs"}},
            { "km-KH",new List<string>(){"km-kh", "khmer"}},
            { "ka",new List<string>(){"georgian","ka"} }
        };

        public const string DefaultPage = "default.aspx";
        public const string DefaultIndex = "index.html";
        public static Uri GetCahcePath(Uri url)
        {
            var localPath = url.LocalPath.TrimStart('/').Trim();
            if (string.IsNullOrEmpty(localPath))
            {
                localPath = DefaultPage;
            }
            else if (localPath.EndsWith("/"))
            {
                localPath += DefaultIndex;
            }
            if (!url.LocalPath.Contains(localPath))
            {
                url = new Uri(url, localPath);
            }
            return url;
        }

        public static string GetCurrentLang(HttpRequest request, bool decodeBroswer = false)
        {
            var lang = HttpContext.Current.Items["lang"]?.ToString();
            if (string.IsNullOrEmpty(lang))
            {
                lang = GetCurrentLang(request.GetValue("lang"));
                if (string.IsNullOrEmpty(lang) && decodeBroswer)
                {
                    var arrLang = request.Headers["Accept-Language"]?.Split(new string[] { ",", ";" }, System.StringSplitOptions.RemoveEmptyEntries);
                    if (arrLang != null && arrLang.Length > 0)
                    {
                        foreach (var strAccLang in arrLang)
                        {
                            if (strAccLang.StartsWith("q="))
                                continue;
                            lang = CommonTranslate.DicJsLanguage
                                .FirstOrDefault(p => Equals(p.Key.ToLower(), strAccLang.ToLower()) || p.Value.Contains(strAccLang.ToLower())).Key;
                            if (!string.IsNullOrEmpty(lang))
                                break;
                        }
                    }
                    LogHelper.Log.Error("Lang:" + lang + "Accept-Language:" + request.Headers["Accept-Language"]);
                }
                if (!string.IsNullOrEmpty(lang))
                {
                    try
                    {
                        HttpContext.Current.Items["lang"] = lang;
                    }
                    catch { }
                }
            }
            return lang;
        }

        public static string GetCurrentLang(string lang)
        {
            return DicJsLanguage.FirstOrDefault(p => Equals(lang.ToLower(), p.Key.ToLower()) || p.Value.Contains(lang.ToLower())).Key ?? (string.IsNullOrEmpty(lang) ? StrDefaultLang : lang);
        }

        public static string GetLinkLang(string strUrl)
        {
            StringBuilder sbLink = new StringBuilder(string.Format("<link rel=\"alternate\" href=\"{0}{1}\" hreflang=\"x-default\" />", StrHost, strUrl));

            foreach (var item in DicJsLanguage)
            {
                sbLink.Append(string.Format("<link rel=\"alternate\" href=\"{0}{1}?lang={2}\" hreflang=\"{2}\" />", StrHost, strUrl, item.Key));
            }

            return sbLink.ToString();
        }
    }
}