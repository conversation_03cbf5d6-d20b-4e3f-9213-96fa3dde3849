﻿<?xml version="1.0" encoding="utf-8" ?>
<configuration>
  <configSections>
    <section name="Enterprise.Framework.Redis" type="Enterprise.Framework.Redis.Config.RedisConfigReader,Enterprise.Framework.Redis" />
    <section name="UserChargeTypeSetting" type="CommonLib.UserConfig.ChargeTypeConfigurationSectionHandler, CommonLib" />
  </configSections>
  <appSettings>
    <add key="FileHostUrl" value="http://**************:7070/"/>
    <add key="NMaxCodeProcessThread" value="1" />
    <add key="NMaxExecPerSecond" value="20" />
  </appSettings>
  <Enterprise.Framework.Redis>
    <RedisDB Name="OPS_Cache" WritePoolSize="1000" ReadPoolSize="1000">
      <!--**************-->
      <RedisServer Host="ocr.oldfish.cn" Port="3600" IsReadOnly="False" />
    </RedisDB>
  </Enterprise.Framework.Redis>
  <UserChargeTypeSetting>
    <UserTypeSettings>
      <UserTypeSetting UserType="专业版" PriceType="按年" PerPrice="100" Enable="true">
        <ChargeTypes>
          <ChargeType Name="季版" IncreaseStep="1" Enable="true" Discount="0.30" Desc="" LimitActivity="true" LimitDiscount="0.16" LimitDesc="新春特惠" DtEnd="2021-11-01" />
          <ChargeType Name="一年版" IncreaseStep="1" Enable="true" Discount="1.20" Desc="" LimitActivity="true" LimitDiscount="0.36" LimitDesc="新春特惠" DtEnd="2021-03-01" />
          <ChargeType Name="三年版" IncreaseStep="1" Enable="true" Discount="3.60" Desc="" LimitActivity="true" LimitDiscount="0.66" LimitDesc="新春特惠" DtEnd="2021-03-01" />
          <ChargeType Name="终身版" IncreaseStep="1" Enable="true" Discount="5.66" Desc="" LimitActivity="true" LimitDiscount="0.01" LimitDesc="一元抢-限50名" DtStart="2021-01-28" DtEnd="2021-03-01" />
        </ChargeTypes>
      </UserTypeSetting>
      <UserTypeSetting UserType="企业版" PriceType="按年" PerPrice="100" Enable="true">
        <ChargeTypes>
          <ChargeType Name="季版" IncreaseStep="1" Enable="true" Discount="0.60" Desc="" LimitActivity="true" LimitDiscount="0.26" LimitDesc="新春特惠" DtEnd="2021-03-01" />
          <ChargeType Name="一年版" IncreaseStep="1" Enable="true" Discount="1.60" Desc="" LimitActivity="true" LimitDiscount="0.56" LimitDesc="新春特惠" DtEnd="2021-03-01" />
          <ChargeType Name="三年版" IncreaseStep="1" Enable="true" Discount="2.60" Desc="" LimitActivity="true" LimitDiscount="0.86" LimitDesc="新春特惠" DtEnd="2021-03-01" />
          <ChargeType Name="终身版" IncreaseStep="1" Enable="true" Discount="5.60" Desc="" LimitActivity="true" LimitDiscount="0.01" LimitDesc="一元抢-限20名" DtStart="2021-01-28" DtEnd="2021-03-01" />
        </ChargeTypes>
      </UserTypeSetting>
      <UserTypeSetting UserType="旗舰版" PriceType="按年" PerPrice="100" Enable="true">
        <ChargeTypes>
          <ChargeType Name="季版" IncreaseStep="1" Enable="true" Discount="0.90" Desc="" LimitActivity="true" LimitDiscount="0.36" LimitDesc="新春特惠" DtEnd="2021-03-01" />
          <ChargeType Name="一年版" IncreaseStep="1" Enable="true" Discount="3.60" Desc="" LimitActivity="true" LimitDiscount="0.86" LimitDesc="新春特惠" DtEnd="2021-03-01" />
          <ChargeType Name="三年版" IncreaseStep="1" Enable="true" Discount="6.60" Desc="" LimitActivity="true" LimitDiscount="1.26" LimitDesc="新春特惠" DtEnd="2021-03-01" />
        </ChargeTypes>
      </UserTypeSetting>
    </UserTypeSettings>
  </UserChargeTypeSetting>

  <runtime>
    <gcServer enabled="true" />
    <gcAllowVeryLargeObjects enabled="true" />
    <assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
      <dependentAssembly>
        <assemblyIdentity name="System.Runtime" publicKeyToken="b03f5f7f11d50a3a" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-2.6.8.0" newVersion="2.6.8.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Threading.Tasks" publicKeyToken="b03f5f7f11d50a3a" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-2.6.8.0" newVersion="2.6.8.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Newtonsoft.Json" culture="neutral" publicKeyToken="30ad4fe6b2a6aeed" />
        <bindingRedirect oldVersion="0.0.0.0-8.0.0.0" newVersion="8.0.0.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="log4net" publicKeyToken="669e0ddf0bb1aa2a" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-1.2.15.0" newVersion="1.2.15.0" />
      </dependentAssembly>
    </assemblyBinding>
  </runtime>
</configuration>