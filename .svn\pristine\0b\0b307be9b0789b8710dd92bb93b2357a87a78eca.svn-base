﻿using CommonLib;
using log4net;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace Code.Process.Common
{
    public class ServicesManager : BaseProcess<ServicesManager>
    {
        #region 构造函数

        static ServicesManager()
        {
            _Log = LogManager.GetLogger("ServicesManager");
        }

        #endregion

        public static bool IsExit = false;
        public static bool IsCanExit = false;

        /// <summary>
        /// 启动进程
        /// </summary>
        public static void StartProcess(bool isSync = true)
        {
            _Log.Info("消息接收主线程开启成功");
            _Log.InfoFormat("消息接收子线程最大可开启{0}个", MaxThreadCount);
            if (isSync)
            {
                Task.Factory.StartNew(() =>
                {
                    BeginReceiveMessage();
                });
            }
            else
            {
                BeginReceiveMessage();
            }
            //ServicesManager obj = new ServicesManager();
            //if (isSync)
            //{
            //    thread = new Thread(obj.BeginReceiveMessage);
            //    thread.IsBackground = true; //主程序关闭的时候 强制关闭
            //    thread.Start();
            //    _Log.Info("消息接收主线程开启成功");
            //    _Log.InfoFormat("消息接收子线程最大可开启{0}个", MaxThreadCount);
            //}
            //else
            //{
            //    _Log.Info("消息接收主线程开启成功");
            //    _Log.InfoFormat("消息接收子线程最大可开启{0}个", MaxThreadCount);
            //    obj.BeginReceiveMessage();
            //}
        }

        /// <summary>
        /// 停止进程
        /// </summary>
        public static void StopProgress()
        {
            try
            {
                _Log.Info("消息接收主线程停止");
            }
            catch (Exception ex)
            {
                _Log.Error(ex.Message);
            }
        }

        /// <summary>
        /// 接收消息
        /// </summary>
        public static void BeginReceiveMessage()
        {
            while (!IsExit)
            {
                try
                {
                    if (CurrentThreadCount >= MaxThreadCount)
                    {
                        //_Log.Info("当前最大线程数已满，休眠一秒钟等待线程处理完结");
                        Thread.Sleep(1);
                        continue;
                    }
                    //int count = (int)RdsCacheHelper.ImageQueue.GetMessageCount();
                    //if (count <= 0)
                    //{
                    //    Thread.Sleep(1);
                    //    continue;
                    //}
                    ////_Log.InfoFormat("当前列表中有{0}个消息待处理", count);
                    ////long count = 1;
                    //Stopwatch stop = Stopwatch.StartNew();
                    //count = Math.Abs(Math.Min(count, MaxThreadCount - CurrentThreadCount));

                    #region 多线程并行处理

                    ////DateTime dtGetMsg = DateTime.Now;

                    //var lstImg = RdsCacheHelper.ImageQueue.PopListMsg(count);
                    ////lstImg.ForEach(p => { p.DtAdd = dtGetMsg; });

                    //if (lstImg != null && lstImg.Count > 0)
                    //{
                    //    Task.Factory.StartNew(() =>
                    //    {
                    //        Parallel.ForEach(lstImg, img =>
                    //        {
                    //            if (CurrentThreadCount < MaxThreadCount)
                    //            {
                    //                try
                    //                {
                    //                    if (img == null)
                    //                        return;

                    //                    img.DtReceived = DateTime.Now;
                    //                    AddThreadCount();

                    //                    //ThreadPool.QueueUserWorkItem(p =>
                    //                    //{
                    //                    CommonProcess.AddToProcess(img);
                    //                    //});
                    //                    //Task.Factory.StartNew(() =>
                    //                    //{
                    //                    //    CommonProcess.AddToProcess(img);
                    //                    //});
                    //                }
                    //                catch (Exception ex)
                    //                {
                    //                    _Log.Error("接收消息消息并分配线程时出错,错误原因如下:", ex);
                    //                }
                    //            }
                    //        });
                    //    });
                    //}
                    ////lstImg.ForEach(p => { p.DisposeStrs(); p = null; });

                    #endregion

                    #region 单线程轮流处理

                    ////有数据有空闲的线程
                    //int i = 0;
                    //while (i < count && CurrentThreadCount < MaxThreadCount)
                    //{
                    //    try
                    //    {
                    //        var img = RdsCacheHelper.ImageQueue.PopImage();

                    //        if (img == null)
                    //            break;

                    //        img.DtReceived = DateTime.Now;
                    //        Task.Factory.StartNew(() =>
                    //        {
                    //            CommonProcess.AddToProcess(img);
                    //        });

                    //        AddThreadCount();
                    //        i++;
                    //    }
                    //    catch (Exception ex)
                    //    {
                    //        string msg = string.Format("接收消息消息并分配线程时出错,错误原因如下:{0}", ex.Message);
                    //        _Log.Error(msg, ex);
                    //    }
                    //}

                    #endregion

                    #region 单线程Block

                    try
                    {
                        var img = RdsCacheHelper.ImageQueue.DequeueBlockingCacheMessage();

                        if (img == null)
                            return;

                        if (!img.IsValidate)
                        {
                            continue;
                        }
                        //img.StrImg = img.StrImg.Replace("%2B", "+");
                        //img.DtReceived = DateTime.Now;
                        ////AddThreadCount();
                        //ServicesManager.RemoveThreadCount();
                        ////ThreadPool.QueueUserWorkItem(obj =>
                        ////{
                        ////CommonProcess.AddToProcess(img);
                        ////});
                        ////if (DateTime.Now.Second % 5 == 0)
                        ////    GC.Collect();
                        ////Task.Factory.StartNew(() =>
                        ////{
                        ////    CommonProcess.AddToProcess(img);
                        ////});
                        CommonProcess.AddToProcess(img);
                    }
                    catch (Exception ex)
                    {
                        _Log.Error("接收消息消息并分配线程时出错,错误原因如下:", ex);
                    }

                    #endregion

                    //_Log.InfoFormat("接收{0}条消息，总耗时：{1}ms，平均耗时：{2}ms", count, stop.ElapsedMilliseconds.ToString("F0"), (stop.ElapsedMilliseconds / count).ToString("F0"));
                    //stop.Stop();
                }
                catch (Exception ex)
                {
                    _Log.Error("轮询消息接收消息时出错,错误原因如下:", ex);
                }
            }
            IsCanExit = true;
        }

    }
}
