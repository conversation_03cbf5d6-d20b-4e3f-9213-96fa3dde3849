﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace NewTicket.Domain
{
    public class TaoBaoTicket
    {
        /// <summary>
        /// Module
        /// </summary>
        public Module module { get; set; }
    }
    public class TaoBaoTrains
    {
        /// <summary>
        /// 南阳
        /// </summary>
        public string arriveStation { get; set; }
        /// <summary>
        /// 05:25
        /// </summary>
        public string arriveTime { get; set; }
        /// <summary>
        /// 14:21
        /// </summary>
        public string costTime { get; set; }
        /// <summary>
        /// 上海
        /// </summary>
        public string departStation { get; set; }
        /// <summary>
        /// 15:04
        /// </summary>
        public string departTime { get; set; }
        /// <summary>
        /// FromStart
        /// </summary>
        public bool fromStart { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string reason { get; set; }
        /// <summary>
        /// ToEnd
        /// </summary>
        public bool toEnd { get; set; }
        /// <summary>
        /// K1106/K1107
        /// </summary>
        public string trainNo { get; set; }
    }

    public class Module
    {
        /// <summary>
        /// Trains
        /// </summary>
        public List<TaoBaoTrains> trains { get; set; }
    }


}
