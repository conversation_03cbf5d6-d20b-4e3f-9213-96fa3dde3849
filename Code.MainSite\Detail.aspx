﻿<%@ Page Title="" Language="C#" MasterPageFile="~/Default.Master" AutoEventWireup="true" CodeBehind="Detail.aspx.cs" Inherits="Code.MainSite.Detail" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="server">
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="server">
    <div class="camscanner_banner index_card">
        <div class="bd">
            <ul>
                <li style="background-image: url('https://scm-file-new-1301864755.cos.ap-beijing.myqcloud.com/f0481b1f-3226-415a-bcc4-9b28e2fb0f9f/static/v1/image/camscanner_banner.png');">
                    <div class="warp">
                        <a href="http://**************:6060/update/Setup.exe">安装版</a>
                        &nbsp;
                        <a href="http://**************:6060/update/OCR助手.exe">绿色版</a>
                    </div>
                </li>
                <li style="background-image: url('https://scm-file-new-1301864755.cos.ap-beijing.myqcloud.com/f0481b1f-3226-415a-bcc4-9b28e2fb0f9f/static/v1/image/camscanner_banner.png');">
                    <div class="warp">
                        <a href="https://smartoldfish.lanzoui.com/imOWlsc0n8b">安装版(密码:ocr)</a>
                        &nbsp;
                        <a href="https://smartoldfish.lanzoui.com/i0Ydvsc0nbe">绿色版(密码:ocr)</a>
                    </div>
                </li>
            </ul>
        </div>
        <div class="hd">
            <ul></ul>
        </div>
    </div>
    <script>jQuery(".camscanner_banner").slide({ titCell: ".hd ul", mainCell: ".bd ul", effect: "fold", autoPlay: true, autoPage: true, trigger: "click", delayTime: 1000, interTime: 5000 });</script>
    <!-- the banner end -->

    <!-- 应用场景 -->
    <div class="camscanner_menu" id="tip">
        <div class="warp">
            <a id="gotop" class="logo">
            </a>
            <ul class="fl">
                <li><a class="a_anli">应用场景</a></li>
                <li><a class="a_gongneng">功能介绍</a></li>
                <li><a class="a_fangan">用户评价</a></li>
                <li><a class="a_ccc">媒体报道</a></li>
            </ul>
            <ul class="fr">
                <li class="style"><a href="http://**************:6060/update/Setup.exe">立即下载</a></li>
            </ul>
        </div>
    </div>

    <!-- 个人产品—名片全能王——第一块图文 -->

    <div class="main warp a_anli_content">
        <div class="sales_x">
            <div class="biaoti">
                <p class="tit mtit">电脑扫描仪，随时记录，轻松分享</p>
                <p class="info minfo">“电脑上最好的100个软件之一”</p>
            </div>
            <dl class="d1">
                <dd class="pic">
                    <img src="https://scm-file-new-1301864755.cos.ap-beijing.myqcloud.com/f0481b1f-3226-415a-bcc4-9b28e2fb0f9f/static/v1/picture/cam.001.png" alt="pic"></dd>
                <dt>轻松处理各种场景</dt>
                <span class="info">办公一族非常需要，能非常方便的处理转换图文</span>
            </dl>
            <dl class="d1">
                <dd class="pic">
                    <img src="https://scm-file-new-1301864755.cos.ap-beijing.myqcloud.com/f0481b1f-3226-415a-bcc4-9b28e2fb0f9f/static/v1/picture/cam.002.png" alt="pic"></dd>
                <dt>自动图像识别预处理</dt>
                <span class="info">图片扫描生成自动锐化提亮，倾斜矫正</span>
            </dl>
            <dl class="d1 kkel">
                <dd class="pic">
                    <img src="https://scm-file-new-1301864755.cos.ap-beijing.myqcloud.com/f0481b1f-3226-415a-bcc4-9b28e2fb0f9f/static/v1/picture/cam.003.png" alt="pic"></dd>
                <dt>打字到手酸的同学们的神器</dt>
                <span class="info">OCR识别，图片瞬间变文本，告别打字打到手酸</span>
            </dl>
        </div>
    </div>
    <!-- 产品优势 end -->
    <div class="clear"></div>
    <div class="advantage a_gongneng_content">
        <div class="tu_a">
            <div class="warp">
                <div class="ccb_tr">
                    <div class="ccb_rt_a">AI-智能OCR识别</div>
                    <div class="ccb_rt_b">
                        <span>截图/拍照/文件，一键操作，深度加速<br>
                            支持识别100+语言，助力全球业务展开<br>
                            账号特权，云端存储，不限设备，不限次数。<br>
                            出差/异地，随时登录，随时使用！<br>
                        </span>
                    </div>
                </div>
            </div>
        </div>
        <div class="tu_e">
            <div class="warp">
                <div class="ccb_tr linx">
                    <div class="ccb_rt_a">
                        轻松处理各种文档
                    </div>
                    <div class="ccb_rt_b">
                        <span>支持Office全家桶(Word,PPT,Excel等)<br>
                            支持PDF文档扫描及转换<br>
                            支持全文扫描，支持全文翻译<br>
                            各种办公文档智能解析，处理结果支持一键下载
                        </span>
                    </div>
                </div>
            </div>
        </div>
        <div class="tu_b">
            <div class="warp">
                <div class="ccb_lr">
                    <div class="ccb_rt_a">云上OCR，轻享服务</div>
                    <div class="ccb_rt_b">
                        <span>依托于助手高性能服务器，用户只需要一个账号<br>
                            即可轻松享用各种大厂提供的服务<br>
                            众星捧月，只为让您提升工作生活效率！<br>
                        </span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="advantage a_fangan_content">
        <div class="ying camying">
            <!-- 用户评价 -->
            <div class="user">
                <p class="titx">用户评价</p>
                <div class="user_pj">
                    <div class="user_pj_a">
                        <div class="bd bd_x">
                            <ul>
                                <li><span>工作原因要经常扫描东西发邮件给客户，用的机会多。OCR助手自动识别，剪裁方便，关键能有多种格式互相转换，简直逆天哦哦哦，真心推荐！！</span></li>
                                <li><span>审计人员尤其适合使用，图片处理之后效果灰常好，很清晰，值得推荐。就查账翻凭证的时候啊，就你懂的啊，就有了OCR助手超级方便呢～</span></li>
                                <li><span>门诊每天的手写病历和处方可以用OCR助手，很容易的将图片转成数字化，并存储，在没有实现数字化系统的小门诊可以很容易的实现病例和处方的讨论！有不足但是很棒的软件！</span></li>
                                <li><span>不得不说，对于学生党太好用了，书上的重点都可以OCR识别下来再排版打印，写论文到图书馆拍资料都是用的它，很喜欢。推荐给好多同学用~</span></li>
                            </ul>
                        </div>

                        <div class="hd bd_xe">
                            <ul>
                                <li>
                                    <span class="hd_ax">
                                        <img src="https://scm-file-new-1301864755.cos.ap-beijing.myqcloud.com/f0481b1f-3226-415a-bcc4-9b28e2fb0f9f/static/v1/picture/cam.005.png" alt="办公族"></span>
                                    <span class="hd_bx">
                                        <span class="hd_bx_a">John</span>
                                        <span class="hd_bx_b">办公族</span>
                                    </span>
                                </li>
                                <li>
                                    <span class="hd_ax">
                                        <img src="https://scm-file-new-1301864755.cos.ap-beijing.myqcloud.com/f0481b1f-3226-415a-bcc4-9b28e2fb0f9f/static/v1/picture/cam.006.png" alt="审计"></span>
                                    <span class="hd_bx">
                                        <span class="hd_bx_a">Abby</span>
                                        <span class="hd_bx_b">审计</span>
                                    </span>
                                </li>
                                <li>
                                    <span class="hd_ax">
                                        <img src="https://scm-file-new-1301864755.cos.ap-beijing.myqcloud.com/f0481b1f-3226-415a-bcc4-9b28e2fb0f9f/static/v1/picture/cam.007.png" alt="医生"></span>
                                    <span class="hd_bx">
                                        <span class="hd_bx_a">Han</span>
                                        <span class="hd_bx_b">医生</span>
                                    </span>
                                </li>
                                <li>
                                    <span class="hd_ax">
                                        <img src="https://scm-file-new-1301864755.cos.ap-beijing.myqcloud.com/f0481b1f-3226-415a-bcc4-9b28e2fb0f9f/static/v1/picture/cam.008.png" alt="学生"></span>
                                    <span class="hd_bx">
                                        <span class="hd_bx_a">Mia</span>
                                        <span class="hd_bx_b">学生</span>
                                    </span>
                                </li>
                            </ul>
                        </div>
                        <script type="text/javascript">jQuery(".user_pj_a").slide({ mainCell: ".bd ul", effect: "left", autoPlay: true });</script>

                    </div>
                </div>
            </div>
            <!-- 用户评价 end -->
        </div>
    </div>

    <div class="media warp camsmedia a_ccc_content" style="margin-top: 80px;">
        <p class="tit">媒体报道</p>
        <div class="news_bd">
            <div class="slideBoxx">
                <div class="bd">
                    <ul>
                        <li>
                            <span class="news_bd_a">
                                <img src="https://scm-file-new-1301864755.cos.ap-beijing.myqcloud.com/f0481b1f-3226-415a-bcc4-9b28e2fb0f9f/static/v1/picture/c103.png"></span>
                            <span class="news_bd_b"><a href="javascript:;">电脑上最好的50个软件之一</a></span>
                        </li>
                        <li>
                            <span class="news_bd_a">
                                <img src="https://scm-file-new-1301864755.cos.ap-beijing.myqcloud.com/f0481b1f-3226-415a-bcc4-9b28e2fb0f9f/static/v1/picture/c102.png"></span>
                            <span class="news_bd_b"><a href="javascript:;">OCR助手，超级扫描仪</a></span>
                        </li>
                        <li>
                            <span class="news_bd_a">
                                <img src="https://scm-file-new-1301864755.cos.ap-beijing.myqcloud.com/f0481b1f-3226-415a-bcc4-9b28e2fb0f9f/static/v1/picture/c100.png"></span>
                            <span class="news_bd_b"><a href="javascript:;">上班族必备10款应用，OCR助手榜上有名</a></span>
                        </li>
                    </ul>
                </div>
                <div class="h36"></div>
                <div class="hd">
                    <ul>
                        <li></li>
                        <li></li>
                        <li></li>
                    </ul>
                </div>
                <!-- 下面是前/后按钮代码，如果不需要删除即可 -->
            </div>
            <script type="text/javascript">
                jQuery(".slideBoxx").slide({
                    mainCell: ".bd ul",
                    autoPlay: true
                });
            </script>

        </div>
    </div>
</asp:Content>
