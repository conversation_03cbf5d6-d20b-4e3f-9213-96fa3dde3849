﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFrameworks>net45;netstandard2.0</TargetFrameworks>
    <AssemblyName>Northwind.Common</AssemblyName>
    <PackageId>Northwind.Common</PackageId>
    <GenerateAssemblyTitleAttribute>false</GenerateAssemblyTitleAttribute>
    <GenerateAssemblyDescriptionAttribute>false</GenerateAssemblyDescriptionAttribute>
    <GenerateAssemblyConfigurationAttribute>false</GenerateAssemblyConfigurationAttribute>
    <GenerateAssemblyCompanyAttribute>false</GenerateAssemblyCompanyAttribute>
    <GenerateAssemblyProductAttribute>false</GenerateAssemblyProductAttribute>
    <GenerateAssemblyCopyrightAttribute>false</GenerateAssemblyCopyrightAttribute>
    <GenerateAssemblyVersionAttribute>false</GenerateAssemblyVersionAttribute>
    <GenerateAssemblyFileVersionAttribute>false</GenerateAssemblyFileVersionAttribute>
    <GenerateAssemblyInformationalVersionAttribute>false</GenerateAssemblyInformationalVersionAttribute>
  </PropertyGroup>

  <PropertyGroup Condition=" '$(Configuration)' != 'Debug' ">
    <GenerateDocumentationFile>true</GenerateDocumentationFile>
    <Optimize>true</Optimize>
  </PropertyGroup>

  <PropertyGroup Condition=" '$(Configuration)' == 'Debug' ">
    <DebugType>portable</DebugType>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="protobuf-net" Version="2.3.17" />
    <PackageReference Include="ServiceStack.Interfaces" Version="$(Version)" />
  </ItemGroup>

  <PropertyGroup Condition=" '$(TargetFramework)' == 'net45' ">
    <DefineConstants>$(DefineConstants);NET45</DefineConstants>
  </PropertyGroup>
  <ItemGroup Condition=" '$(TargetFramework)' == 'net45' ">
    <Reference Include="System.Configuration" />
    <Reference Include="System.Threading" />
    <Reference Include="System.Threading.Tasks" />
    <Reference Include="Microsoft.CSharp" />
  </ItemGroup>

  <PropertyGroup Condition=" '$(TargetFramework)' == 'netstandard2.0' ">
    <DefineConstants>$(DefineConstants);NETSTANDARD2_0</DefineConstants>
  </PropertyGroup>
  <ItemGroup Condition=" '$(TargetFramework)' == 'netstandard2.0' ">
    <PackageReference Include="Microsoft.Extensions.Primitives" Version="2.1.1" />
    <PackageReference Include="System.Runtime" Version="4.3.1" />
    <PackageReference Include="System.Reflection.Emit" Version="4.3.0" />
    <PackageReference Include="System.Reflection.Emit.LightWeight" Version="4.3.0" />
    <PackageReference Include="Microsoft.CSharp" Version="4.5.0" />
  </ItemGroup>

</Project>