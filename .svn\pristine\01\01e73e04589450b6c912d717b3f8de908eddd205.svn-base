﻿
using CommonLib;
using System;
using System.Collections.Generic;
using System.Collections.Specialized;
using System.Diagnostics;
using System.IO;
using System.Net;
using System.Text;

namespace HanZiOcr
{
    /// <summary>
    /// https://www.etoplive.com/products/tysb.do
    /// </summary>
    public class YiTuRec : BaseOcrRec
    {
        public YiTuRec()
        {
            OcrType = HanZiOcrType.译图;

            MaxExecPerTime = 20;
            IsVerticalOcr = false;

            LstJsonPreProcessArray = new List<object>() { "json", "PageInfo", 0, "Result", 0, "UniversalData", "PageData", 0, "RowData" };
            StrResultJsonSpilt = "word";
        }

        protected override string GetHtml(OcrContent content)
        {
            var url = "https://www.etoplive.com/trial/recognizePage.srvc?fileapi";
            var file = new UploadFileInfo()
            {
                Name = "files",
                Filename = "1." + content.fileExt,
                ContentType = ApplicationTypeHelper.GetApplicationType(content.fileExt),
                Stream = new MemoryStream(Convert.FromBase64String(content.strBase64))
            };
            var values = new NameValueCollection {
                { "appKey","tysb"},//"sxtsb" 手写体识别
                { "catalogId",""},
                { "token",""},
                { "_files","1." + content.fileExt},
            };
            var headers = new NameValueCollection
            {
                {"cookie","sid="+Guid.NewGuid().ToString().ToLower()}
            };
            var result = PostFile(url, new[] { file }, values, headers);
            return result;
        }

    }
}