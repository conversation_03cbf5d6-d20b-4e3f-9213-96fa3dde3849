﻿using System;
using System.Text;
using System.Security.Cryptography;
using System.IO;

namespace NewTicket.Mobile
{
    public class KeysSet
    {
        private static byte[] Keys;
        public static string encrypt(string ptoencrypt, string skey)
        {
            byte[] bytes = Encoding.Default.GetBytes(ptoencrypt);
            string text = Convert.ToBase64String(bytes);
            return text.Replace("==", "");
        }
        public static string decrypt(string ptodecrypt, string skey)
        {
            byte[] bytes = Convert.FromBase64String(ptodecrypt);
            return Encoding.Default.GetString(bytes);
        }
        public static string smethod_0(string encryptString, string encryptKey)
        {
            string result;
            try
            {
                byte[] bytes = Encoding.UTF8.GetBytes(encryptKey.Substring(0, 8));
                byte[] keys = KeysSet.Keys;
                byte[] bytes2 = Encoding.UTF8.GetBytes(encryptString);
                DESCryptoServiceProvider dESCryptoServiceProvider = new DESCryptoServiceProvider();
                MemoryStream memoryStream = new MemoryStream();
                CryptoStream cryptoStream = new CryptoStream(memoryStream, dESCryptoServiceProvider.CreateEncryptor(bytes, keys), CryptoStreamMode.Write);
                cryptoStream.Write(bytes2, 0, bytes2.Length);
                cryptoStream.FlushFinalBlock();
                result = Convert.ToBase64String(memoryStream.ToArray());
            }
            catch
            {
                result = encryptString;
            }
            return result;
        }
        public static string smethod_1(string decryptString, string decryptKey)
        {
            string result;
            try
            {
                byte[] bytes = Encoding.UTF8.GetBytes(decryptKey);
                byte[] keys = KeysSet.Keys;
                byte[] array = Convert.FromBase64String(decryptString);
                DESCryptoServiceProvider dESCryptoServiceProvider = new DESCryptoServiceProvider();
                MemoryStream memoryStream = new MemoryStream();
                CryptoStream cryptoStream = new CryptoStream(memoryStream, dESCryptoServiceProvider.CreateDecryptor(bytes, keys), CryptoStreamMode.Write);
                cryptoStream.Write(array, 0, array.Length);
                cryptoStream.FlushFinalBlock();
                result = Encoding.UTF8.GetString(memoryStream.ToArray());
            }
            catch
            {
                result = decryptString;
            }
            return result;
        }
        public KeysSet()
        {

        }
        static KeysSet()
        {
            // Note: this type is marked as 'beforefieldinit'.
            KeysSet.Keys = new byte[]
			{
				20,
				54,
				88,
				135,
				154,
				203,
				237,
				255
			};
        }
    }
}
