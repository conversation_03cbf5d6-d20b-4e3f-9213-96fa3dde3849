<?xml version="1.0" encoding="UTF-8"?>
<svg width="180px" height="180px" viewBox="0 0 180 180" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <!-- Generator: Sketch 54 (76480) - https://sketchapp.com -->
    <title>icon_flexible_configuration </title>
    <desc>Created with Sketch.</desc>
    <defs>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="98.3504012%" id="linearGradient-1">
            <stop stop-color="#004FFF" offset="0%"></stop>
            <stop stop-color="#FFFFFF" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="98.3504012%" id="linearGradient-2">
            <stop stop-color="#FFFFFF" offset="0%"></stop>
            <stop stop-color="#193FAD" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="65.3172029%" y1="28.8166994%" x2="25.4274321%" y2="53.5174256%" id="linearGradient-3">
            <stop stop-color="#71C2FF" offset="0%"></stop>
            <stop stop-color="#2F60EE" offset="100%"></stop>
        </linearGradient>
        <path d="M50.794604,85.7219721 L97.8600231,112.078426 C98.5687057,112.475286 99.4338411,112.47036 100.137959,112.065456 L141.889374,88.0562492 C142.994225,87.4209035 143.374834,86.0101958 142.739488,84.9053454 C142.531871,84.5443054 142.230419,84.2461808 141.867096,84.0425849 L94.8325917,57.6857422 C94.1240417,57.2886903 93.2589307,57.2933568 92.5547054,57.6980294 L50.7723751,81.7076249 C49.667326,82.342625 49.2862759,83.7532135 49.921276,84.8582626 C50.1290134,85.2197745 50.4308137,85.5182509 50.794604,85.7219721 Z" id="path-4"></path>
        <filter x="-4.8%" y="-24.5%" width="119.1%" height="132.7%" filterUnits="objectBoundingBox" id="filter-5">
            <feMorphology radius="1" operator="erode" in="SourceAlpha" result="shadowSpreadOuter1"></feMorphology>
            <feOffset dx="6" dy="-6" in="shadowSpreadOuter1" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="2.5" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feColorMatrix values="0 0 0 0 0.44409173   0 0 0 0 0.629283647   0 0 0 0 0.872848732  0 0 0 0.4 0" type="matrix" in="shadowBlurOuter1"></feColorMatrix>
        </filter>
        <path d="M29.2528178,98.8270062 L76.3182369,125.18346 C77.0269195,125.58032 77.8920548,125.575394 78.5961724,125.17049 L120.347588,101.161283 C121.452439,100.525938 121.833047,99.1152299 121.197702,98.0103795 C120.990085,97.6493396 120.688633,97.3512149 120.32531,97.147619 L73.2908055,70.7907763 C72.5822555,70.3937244 71.7171445,70.3983909 71.0129191,70.8030636 L29.2305888,94.8126591 C28.1255397,95.4476591 27.7444897,96.8582477 28.3794897,97.9632968 C28.5872272,98.3248087 28.8890275,98.623285 29.2528178,98.8270062 Z" id="path-6"></path>
        <filter x="-4.8%" y="-24.5%" width="119.1%" height="132.7%" filterUnits="objectBoundingBox" id="filter-7">
            <feMorphology radius="1" operator="erode" in="SourceAlpha" result="shadowSpreadOuter1"></feMorphology>
            <feOffset dx="6" dy="-6" in="shadowSpreadOuter1" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="2.5" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feColorMatrix values="0 0 0 0 0.44409173   0 0 0 0 0.629283647   0 0 0 0 0.872848732  0 0 0 0.4 0" type="matrix" in="shadowBlurOuter1"></feColorMatrix>
        </filter>
        <path d="M88.4205588,111.393307 C88.3617207,111.697673 88.2583084,111.982373 88.1156707,112.248744 C87.973033,112.517013 87.8000848,112.732193 87.5968261,112.896032 C87.3935674,113.061794 87.1653471,113.132398 86.924646,113.118181 C86.678596,113.102824 86.4646395,113.042921 86.2809935,112.940875 C86.0973475,112.83883 85.8958717,112.68866 85.6712174,112.482137 C85.4679587,112.310048 85.2112108,112.111652 84.9063228,111.891656 C84.5978688,111.671365 84.2894148,111.491086 83.972046,111.342436 C83.6564601,111.196552 83.3408742,111.085592 83.0252883,111.00961 C82.7114854,110.932978 82.4369079,110.953373 82.2158195,111.07004 C81.7255024,111.298219 81.4366611,111.710461 81.3564274,112.308479 C81.2761937,112.906669 81.3671252,113.567665 81.6327879,114.294664 C81.8360466,114.808542 81.8057361,115.190716 81.5418564,115.446902 C81.4206144,115.541596 81.2227046,115.597957 80.9606078,115.624619 C80.6949451,115.651235 80.4025379,115.647632 80.088735,115.616724 C79.7749321,115.587718 79.4415165,115.514808 79.0920541,115.406919 C78.7443748,115.298341 78.4394867,115.172705 78.1720411,115.027028 C77.9883951,114.926997 77.8190128,114.764304 77.6531965,114.533064 C77.4909461,114.301929 77.3804019,114.090114 77.316215,113.894732 L77.2841215,113.877216 C77.0594671,113.12697 76.6779113,112.380261 76.1376711,111.6288 C75.5974309,110.876523 74.971608,110.304575 74.2548537,109.910627 C73.5612779,109.52942 72.9336721,109.413456 72.3738192,109.564352 C71.8121833,109.715116 71.4199297,110.034118 71.1952753,110.523643 C71.093646,110.713873 70.9403104,110.808328 70.7352688,110.808784 C70.535576,110.81223 70.3109217,110.745106 70.0648717,110.611084 C69.7795963,110.455695 69.4586615,110.230896 69.1020673,109.93124 C68.7454731,109.635035 68.3870959,109.310913 68.0305017,108.96256 C67.6739075,108.614028 67.3511897,108.258736 67.0659143,107.896829 C66.780639,107.536524 66.5666824,107.213256 66.4240447,106.930682 C66.3224154,106.729694 66.2653603,106.529752 66.2546625,106.330069 C66.2439647,106.128584 66.3206324,105.89789 66.4846658,105.638131 C66.7093201,105.288175 66.7949027,104.797363 66.7449795,104.161946 C66.6950563,103.528224 66.4347426,102.82444 65.9640382,102.050077 C65.6787628,101.582777 65.3328665,101.191122 64.9245661,100.870647 C64.5162657,100.550049 64.129361,100.298856 63.7620689,100.116189 C63.3323729,99.8970402 62.895545,99.7371092 62.4462363,99.6335608 C62.1609609,99.5563124 61.8756856,99.376114 61.5886272,99.0936402 C61.3639729,98.8873588 61.1553652,98.5795588 60.9610214,98.1709607 C60.7666776,97.7621737 60.5955123,97.3317132 60.4403939,96.884664 C60.2870583,96.4348065 60.1711652,95.9984708 60.0891486,95.5664721 C60.0089149,95.1371839 59.9875192,94.8097248 60.0285275,94.5825693 C60.0891486,94.159826 60.2924073,93.9842223 60.6400866,94.0549877 C61.2516457,94.1497132 61.8008008,94.0506633 62.2911178,93.7565415 C62.7814349,93.4651318 63.0257019,92.9683654 63.0257019,92.26477 C63.0257019,91.5431338 62.7814349,90.7723616 62.2911178,89.9544035 C61.8008008,89.1337822 61.2498627,88.4259243 60.6383036,87.8256779 C60.4600065,87.6655012 60.306671,87.42719 60.185429,87.1184119 C60.062404,86.808516 60,86.5279267 60,86.2813656 C60,86.0112362 60.0410083,85.7320969 60.121242,85.4466115 C60.2014757,85.161212 60.310237,84.9014382 60.4421768,84.6660488 C60.5758997,84.4318241 60.7310181,84.2372878 60.9021834,84.0757284 C61.0769145,83.9200011 61.2659095,83.8360208 61.4673852,83.8281339 C61.6314185,83.8202846 61.7901029,83.8708456 61.9434385,83.9797682 C62.096774,84.0886809 62.2643733,84.2275413 62.4480193,84.3973637 C63.0595783,85.0013069 63.697882,85.447255 64.3611472,85.7401026 C65.0226295,86.0321137 65.6110099,86.0788877 66.1209397,85.8831779 C66.3652067,85.7776101 66.5435038,85.5782157 66.655831,85.2849453 C66.7663752,84.9871796 66.84126,84.6776773 66.8697875,84.3447395 C66.901881,84.0139215 66.901881,83.6966062 66.8697875,83.3909508 C66.839477,83.0845188 66.8127324,82.8644233 66.7931198,82.7309804 C66.7521114,82.5831663 66.716452,82.4169599 66.6861415,82.2359479 C66.654048,82.0520774 66.6700947,81.9162504 66.7307158,81.8299867 C66.8519578,81.6145025 67.0712632,81.4928006 67.3886321,81.4649633 C67.704218,81.4362965 68.0447654,81.4565937 68.4138404,81.5315036 C68.7811325,81.6037454 69.1395097,81.7101861 69.4854061,81.8397173 C69.8313024,81.9729019 70.107663,82.1006972 70.3109217,82.2209331 C70.5748014,82.3770289 70.785192,82.5869894 70.9385275,82.8522026 C71.091863,83.1173279 71.1970583,83.3592769 71.2576793,83.5784143 C71.4823337,84.2608253 71.8496257,84.9591869 72.3595554,85.6786126 C72.8694851,86.3955584 73.4721294,86.958676 74.1657051,87.3656711 C74.8806765,87.7852213 75.5118483,87.9576335 76.0627863,87.8855423 C76.6137244,87.8141737 77.0006291,87.542868 77.2252834,87.0689454 C77.3055171,86.954176 77.435674,86.8636598 77.6068393,86.795695 C77.7797874,86.725359 77.9563016,86.7452509 78.1435135,86.855994 C78.4270059,87.0236906 78.7390259,87.2469402 79.0760074,87.5288396 C79.4129889,87.807134 79.7446216,88.1155258 80.0691223,88.4494007 C80.3989719,88.7845273 80.700294,89.1429631 80.9730886,89.524673 C81.2494491,89.9082838 81.4669716,90.2762782 81.6310049,90.6336041 C81.7326343,90.8543676 81.7629448,91.0471255 81.7219365,91.2119381 C81.6809281,91.3785262 81.6417028,91.4902006 81.6006944,91.5466051 C81.3350317,91.9735428 81.2547981,92.5357651 81.3546444,93.2365937 C81.4562738,93.9364969 81.7540299,94.6733412 82.240781,95.4372983 C82.7310981,96.2025413 83.3337423,96.8157666 84.0469307,97.2816754 C84.7619021,97.7450117 85.4251674,98.0165741 86.0367264,98.0916648 C86.1989768,98.0868801 86.3968866,98.1601375 86.6322388,98.3176275 C86.867591,98.475101 87.0637178,98.6791321 87.2277511,98.9303415 C87.5326392,99.3844176 87.8018678,99.9630015 88.03722,100.665271 C88.2725722,101.367089 88.4294736,102.026716 88.5114903,102.648156 C88.5524986,103.008967 88.4972265,103.240093 88.345674,103.341642 C88.1905555,103.439511 88.033654,103.474145 87.8678377,103.439676 C87.236666,103.296259 86.6982087,103.371189 86.2489,103.659226 C85.7995913,103.94809 85.5767199,104.444937 85.5767199,105.141901 C85.5767199,105.819747 85.7657148,106.519398 86.1401388,107.238356 C86.5181286,107.956951 87.0120116,108.599758 87.6217877,109.161719 C87.7662084,109.320997 87.8874504,109.470649 87.9890798,109.605766 C88.219083,109.913461 88.3724185,110.228073 88.4526522,110.549925 C88.4936606,110.811636 88.4847457,111.092008 88.4205588,111.393307 Z M76.4437826,106.539451 C77.2887565,106.657831 78.0230352,106.576856 78.6466185,106.296745 C79.2702019,106.014208 79.7609509,105.560639 80.1188656,104.928399 C80.4767803,104.297126 80.6557377,103.506243 80.6557377,102.555024 C80.6557377,101.601958 80.4767803,100.603851 80.1188656,99.5599804 C79.7609509,98.5132945 79.2702019,97.4933155 78.6466185,96.5035289 C78.0230352,95.5106335 77.2906014,94.5842347 76.4437826,93.7249956 C75.6024986,92.8661786 74.702177,92.1524122 73.7538875,91.5928259 C72.805598,91.0332397 71.9108112,90.6887908 71.0769069,90.5608143 C70.2466923,90.4378532 69.5161035,90.5098233 68.89621,90.7815714 C68.2744716,91.0516049 67.7818776,91.5115424 67.4239629,92.1549856 C67.0642033,92.7983203 66.8852459,93.6099081 66.8852459,94.5822846 C66.8852459,95.554661 67.0660482,96.5726468 67.4239629,97.6333885 C67.7818776,98.6912773 68.2744716,99.7234958 68.89621,100.728523 C69.5179484,101.730391 70.2485372,102.650187 71.0805967,103.489578 C71.9145011,104.327175 72.805598,105.016701 73.7538875,105.555137 C74.702177,106.093573 75.6024986,106.422287 76.4437826,106.539451 Z" id="path-8"></path>
        <filter x="-26.3%" y="-21.9%" width="152.6%" height="143.9%" filterUnits="objectBoundingBox" id="filter-9">
            <feOffset dx="0" dy="0" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="2.5" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feColorMatrix values="0 0 0 0 1   0 0 0 0 1   0 0 0 0 1  0 0 0 0.5 0" type="matrix" in="shadowBlurOuter1"></feColorMatrix>
        </filter>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="98.3504012%" id="linearGradient-10">
            <stop stop-color="#E7E9F0" offset="0%"></stop>
            <stop stop-color="#B3C3EF" offset="99.675359%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-11">
            <stop stop-color="#FFFFFF" stop-opacity="0" offset="0%"></stop>
            <stop stop-color="#FFFFFF" stop-opacity="0.6" offset="100%"></stop>
        </linearGradient>
    </defs>
    <g id="设计规范" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="图标" transform="translate(-1171.000000, -518.000000)">
            <g id="icon_flexible_configuration-" transform="translate(1171.000000, 518.000000)">
                <rect id="矩形复制-3" fill="#FFFFFF" opacity="0.01" x="0" y="0" width="180" height="180"></rect>
                <g id="编组-7" transform="translate(0.000000, 3.000000)">
                    <polygon id="多边形" stroke="#004FFF" stroke-width="0.576923077" opacity="0.3" points="90.4918033 17.4043833 149.464529 52.21315 149.464529 121.830683 90.4918033 156.63945 31.5190772 121.830683 31.5190772 52.21315"></polygon>
                    <polygon id="路径-2复制" fill-opacity="0.2" fill="url(#linearGradient-1)" points="31.4754098 121.830683 90.4481359 156.63945 149.420862 121.830683 90.4918033 87.0219166"></polygon>
                    <polygon id="路径-2复制-2" fill-opacity="0.1" fill="url(#linearGradient-2)" transform="translate(60.940122, 69.617639) rotate(-240.000000) translate(-60.940122, -69.617639) " points="0.979026367 70.1884857 60.6106656 103.855981 120.901218 69.0472147 61.3139779 35.3792965"></polygon>
                    <g id="路径-2复制-7" opacity="0.3" transform="translate(96.330562, 84.881923) scale(-1, 1) rotate(-240.000000) translate(-96.330562, -84.881923) ">
                        <use fill="black" fill-opacity="1" filter="url(#filter-5)" xlink:href="#path-4"></use>
                        <use fill="url(#linearGradient-3)" fill-rule="evenodd" xlink:href="#path-4"></use>
                    </g>
                    <g id="路径-2复制-5" transform="translate(74.788775, 97.986957) scale(-1, 1) rotate(-240.000000) translate(-74.788775, -97.986957) ">
                        <use fill="black" fill-opacity="1" filter="url(#filter-7)" xlink:href="#path-6"></use>
                        <use fill="url(#linearGradient-3)" fill-rule="evenodd" xlink:href="#path-6"></use>
                    </g>
                    <g id="形状" fill-rule="nonzero">
                        <use fill="black" fill-opacity="1" filter="url(#filter-9)" xlink:href="#path-8"></use>
                        <use fill="#FFFFFF" xlink:href="#path-8"></use>
                    </g>
                    <polygon id="路径-2复制-3" fill-opacity="0.2" fill="url(#linearGradient-10)" transform="translate(119.956150, 69.617639) scale(-1, 1) rotate(-240.000000) translate(-119.956150, -69.617639) " points="59.9950539 70.1884857 119.626693 103.855981 179.917246 69.0472147 120.330005 35.3792965"></polygon>
                    <path d="M31.4754098,52.21315 L90.4481359,87.0219166 L149.420862,52.21315" id="路径-2" stroke="#004FFF" stroke-width="0.576923077" opacity="0.3" stroke-linejoin="round" stroke-dasharray="3.461538461538461,3.461538461538461"></path>
                    <path d="M31.4754098,52.21315 L90.4481359,87.0219166 L149.420862,52.21315 L90.4918033,17.4043833 L31.4754098,52.21315 Z" id="路径-2复制-4" fill="url(#linearGradient-11)"></path>
                </g>
            </g>
        </g>
    </g>
</svg>