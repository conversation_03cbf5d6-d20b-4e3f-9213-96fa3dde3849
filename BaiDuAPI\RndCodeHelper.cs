﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace BaiDuAPI
{
    public class RndCodeHelper
    {
        #region 随机坐标相关

        private static List<string> lstTmpLoc = new List<string>();
        private static Random rndTmp = new Random();

        public static string GetRndCode()
        {
            string result = "";
            try
            {
                if (lstTmpLoc == null || lstTmpLoc.Count <= 0)
                {
                    GetValidaCodeRndByTmp();
                }
                result = lstTmpLoc[rndTmp.Next(0, lstTmpLoc.Count)];
            }
            catch (Exception oe)
            {
                result = "38,118";
                //Console.WriteLine(oe.Message);
            }
            return result;
        }

        private static void GetValidaCodeRndByTmp()
        {
            //lstTmpLoc = new List<string>() { "187,44", "254,43", "38,118" };
            for (int j = 1; j < 9; j += 2)
            {
                for (int i = 1; i < 5; i += 2)
                {
                    lstTmpLoc.Add(string.Format("{0},{1}", j * 40, i * 40));
                    lstTmpLoc.AddRange(GetNextLoc(j, i));
                }
            }
        }

        private static List<string> GetNextLoc(int x = 0, int y = 0, ImageType type = ImageType.Eight)
        {
            List<string> lstTmp = new List<string>();
            //int width, height;
            var codes = new List<int>();
            for (int j = 1; j < 9; j += 2)
            {
                for (int i = 1; i < 5; i += 2)
                {
                    if (y == i && x == j)
                        continue;
                    lstTmp.Add(string.Format("{2},{3},{0},{1}", j * 40, i * 40, x * 40, y * 40));
                }
            }
            return lstTmp;
        }

        #endregion

        #region 随机数相关

        /// <summary>   
        /// 生成小于输入值绝对值的随机数   
        /// </summary>   
        /// <param name="NumSides"></param>   
        /// <returns></returns>   
        public static int Next(int numSeeds)
        {
            //numSeeds = Math.Abs(numSeeds);
            if (numSeeds <= 1)
            {
                return 0;
            }
            int length = 4;
            if (numSeeds <= byte.MaxValue)
            {
                length = 1;
            }
            else if (numSeeds <= short.MaxValue)
            {
                length = 2;
            }
            return Next(numSeeds, length);
        }

        private static int Next(int numSeeds, int length)
        {
            // Create a byte array to hold the random value.   
            byte[] buffer = new byte[length];
            // Create a new instance of the RNGCryptoServiceProvider.   
            System.Security.Cryptography.RNGCryptoServiceProvider Gen = new System.Security.Cryptography.RNGCryptoServiceProvider();
            // Fill the array with a random value.   
            Gen.GetBytes(buffer);
            // Convert the byte to an uint value to make the modulus operation easier.   
            uint randomResult = 0x0;//这里用uint作为生成的随机数   
            for (int i = 0; i < length; i++)
            {
                randomResult |= ((uint)buffer[i] << ((length - 1 - i) * 8));
            }
            // Return the random number mod the number   
            // of sides.  The possible values are zero-based   
            return (int)(randomResult % numSeeds);
        }
        #endregion
    }
}