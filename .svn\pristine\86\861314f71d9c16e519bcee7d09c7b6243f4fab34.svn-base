﻿using CommonLib;
using log4net;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Threading;
using System.Threading.Tasks;

namespace Code.Process.Common
{
    public class CommonProcess
    {

        private static ILog _Log;

        static CommonProcess()
        {
            _Log = LogManager.GetLogger("ImageProcess");
        }

        public static void AddToProcess(CusImageEntity cusImg)
        {
            using (cusImg)
            {
                if (cusImg.IsValidate)
                {
                    cusImg.DtReceived = ServerTime.DateTime.Ticks;
                    //var processEntity = AddProcessQueue(cusImg);
                    Process(cusImg);
                }
                cusImg.Dispose();
            }
        }

        public static void AddToFileStatusProcess(CusFileStatusEntity cusImg)
        {
            if (cusImg == null || string.IsNullOrEmpty(cusImg.TaskId))
                return;
            ProcessFileStatus(cusImg);
        }

        private static void ProcessFileStatus(CusFileStatusEntity processEntity)
        {
            try
            {
                GetProcessFileStatus(processEntity);
            }
            catch (ThreadAbortException)
            {
                //processEntity.Error = ex.Message;
                //processEntity.State = ProcessState.失败;
                Thread.ResetAbort();
            }
            catch (Exception oe)
            {
                _Log.Error("消息接收失败,错误原因如下:", oe);
                //processEntity.Error = oe.Message;
                //processEntity.State = ProcessState.失败;
            }
        }

        private static void GetProcessFileStatus(CusFileStatusEntity processEntity)
        {
            var ocr = BaseRecHelper.GetInstance(processEntity.OcrType);
            var state = ocr?.QueryFileStatus(processEntity.TaskId);
            if (state?.Result != null)
            {
                ProcessNew.ProcessFileResult(state.Result);
            }
        }

        //private static CusImageEntity AddProcessQueue(CusImageEntity cusImg)
        //{
        //    //CacheHelper.ProcessQueue.AddOrUpdate(cusImg.StrIndex, tmpRes);
        //    return tmpRes;
        //}

        //static Random rndTmp = new Random();
        //static GetCodeDele ExecDaMa = GetCode;

        private static void Process(CusImageEntity processEntity)
        {
            //System.Threading.Thread.Sleep(5000);
            //return;
            try
            {
                //processEntity.Error = string.Format("接收线程ID：{0}\n", System.Threading.Thread.CurrentThread.ManagedThreadId);

                ////_Log.Info("开始处理…");
                //ExecDaMa.BeginInvoke(processEntity, CallBack, processEntity);

                processEntity.State = ProcessState.正在处理;
                GetCode(processEntity);

                //processEntity.Error += string.Format("最终线程ID：{0}\n", System.Threading.Thread.CurrentThread.ManagedThreadId);
                _Log.Info(processEntity.ToStr());
            }
            catch (ThreadAbortException)
            {
                //processEntity.Error = ex.Message;
                //processEntity.State = ProcessState.失败;
                Thread.ResetAbort();
            }
            catch (Exception oe)
            {
                _Log.Error("消息接收失败,错误原因如下:", oe);
                //processEntity.Error = oe.Message;
                //processEntity.State = ProcessState.失败;
            }
        }

        const string StrInvalidateOperate = "当前用户不支持此类型的操作！";

        private static void GetCode(CusImageEntity processEntity)
        {
            var limit = UserTypeHelper.GetUserInfo(processEntity.UserType);
            if (CheckIsForbidOperate(processEntity, limit))
            {
                SetEmptyResult(processEntity, StrInvalidateOperate);
                return;
            }

            if (!string.IsNullOrEmpty(processEntity.ImgUrl)
                && processEntity.ImgUrl.StartsWith(ConfigHelper.FileHostUrl)
                && string.IsNullOrEmpty(processEntity.StrImg))
            {
                var strImg = GetBytesByUrl(processEntity.ImgUrl);
                if (!string.IsNullOrEmpty(strImg))
                {
                    processEntity.StrImg = strImg;
                    processEntity.ImgUrl = null;
                }
            }

            //可用的识别类型
            var processOcrType = processEntity.OcrType;
            int processPerTime = Math.Max(limit.ProcessPerTime, 1);
            //最多结果个数
            int nMaxResultCount = processPerTime;

            //查找可用的处理类型
            List<CusImageEntity> listProcessEntity = GetSupportTypes(processEntity, nMaxResultCount);

            if (listProcessEntity.Count > 0)
            {
                var totalResultCount = new RefCount() { TotalCount = 0, MaxCount = nMaxResultCount };
                try
                {
                    using (CancellationTokenSource cancellationToken = new CancellationTokenSource(new TimeSpan(processEntity.DtExpired - processEntity.DtAdd)))
                    {
                        totalResultCount.AchieveMaxEvent += (sender, e) =>
                        {
                            cancellationToken.Cancel();
                        };
                        int nStart = 0;
                        while (!cancellationToken.IsCancellationRequested && processEntity.IsValidate && nStart < listProcessEntity.Count)
                        {
                            var lstProcess = listProcessEntity.GetRange(nStart, nStart + nMaxResultCount < listProcessEntity.Count ? nMaxResultCount : listProcessEntity.Count - nStart);
                            nStart += lstProcess.Count;

                            //如果任意一个不支持URL，且没有下载，则全量赋值
                            if (lstProcess.Any(p => !p.IsSupportUrl) && string.IsNullOrEmpty(processEntity.StrImg) && !string.IsNullOrEmpty(processEntity.ImgUrl))
                            {
                                processEntity.StrImg = GetBytesByUrl(processEntity.ImgUrl);
                                lstProcess.ForEach(p => p.StrImg = processEntity.StrImg);
                            }
                            ParallelProcessOcr(lstProcess, processPerTime, cancellationToken.Token, totalResultCount);
                            //大于等于期望结果个数，跳出循环，否则继续循环
                            if (totalResultCount.TotalCount >= nMaxResultCount)
                            {
                                break;
                            }

                            processPerTime = Math.Max(1, Math.Min(processPerTime, nMaxResultCount - totalResultCount.TotalCount));
                        }
                    }
                }
                catch (Exception oe)
                {
                    Console.WriteLine(oe.Message);
                }
                if (totalResultCount.TotalCount == 0)
                {
                    var strError = "识别失败，请稍后重试！\nTrace:" + processEntity.StrIndex + "\nNode:" + ConfigHelper.OcrServer;
                    SetEmptyResult(processEntity, strError);
                }
                //if (totalResultCount.TotalCount < nMaxResultCount)
                //{
                //}
            }
            else
            {
                var strErrorMsg = "未找到匹配的OCR功能，暂不支持当前操作！";
                if (processEntity.OcrGroup != OcrGroupType.不限)
                {
                    strErrorMsg = string.Format("【{0}】通道暂不支持当前操作，请更换其他识别通道后重试！", processEntity.OcrGroup.ToString());
                }
                SetEmptyResult(processEntity, strErrorMsg);
            }
        }

        private static List<CusImageEntity> GetSupportTypes(CusImageEntity processEntity, int nMaxResultCount)
        {
            var lstResult = GetEnableRecType(processEntity);

            //文本转表格
            if (lstResult.Count <= nMaxResultCount)
            {
                if (Equals(processEntity.OcrType, OcrType.表格))
                {
                    processEntity.IsSupportVertical = true;
                    processEntity.OcrType = OcrType.文本;
                    processEntity.IsTextToTable = true;
                    var lstTmpType = GetEnableRecType(processEntity);
                    lstTmpType.ForEach(t =>
                    {
                        if (!lstResult.Any(p => Equals(p.ProcessId, t.ProcessId)))
                            lstResult.Add(t);
                    });
                }
                else if (Equals(processEntity.OcrType, OcrType.翻译))
                {

                }
            }
            return lstResult;
        }

        private static List<CusImageEntity> GetEnableRecType(CusImageEntity processEntity)
        {
            var lstResult = new List<CusImageEntity>();
            var lstTmpType = BaseRecHelper.GetEnableRecType(true, processEntity.OcrType, processEntity.OcrGroup
        , processEntity.ProcessId, processEntity.IsSupportVertical, processEntity.FileExt, processEntity.FromLanguage, processEntity.ToLanguage);
            lstTmpType.ForEach(type =>
            {
                var objTmp = DeepCopyByReflect(processEntity);
                objTmp.ProcessId = type.OcrType;
                objTmp.IsSupportUrl = type.IsSupportUrl;
                lstResult.Add(objTmp);
            });
            return lstResult;
        }

        private static T DeepCopyByReflect<T>(T obj)
        {
            //如果是字符串或值类型则直接返回
            if (obj == null || (obj is string) || (obj.GetType().IsValueType)) return obj;

            object retval = Activator.CreateInstance(obj.GetType());
            FieldInfo[] fields = obj.GetType().GetFields(BindingFlags.Public | BindingFlags.NonPublic | BindingFlags.Instance | BindingFlags.Static);
            foreach (FieldInfo field in fields)
            {
                try { field.SetValue(retval, DeepCopyByReflect(field.GetValue(obj))); }
                catch { }
            }
            return (T)retval;
        }

        private static void SetEmptyResult(CusImageEntity processEntity, string errorMsg)
        {
            try
            {
                processEntity.State = ProcessState.失败;
                var content = new OcrContent()
                {
                    id = processEntity.StrIndex,
                    ocrType = processEntity.OcrType,
                    processName = "温馨提示",
                    result = new ResultEntity()
                    {
                        autoText = errorMsg,
                        spiltText = errorMsg,
                        verticalText = "{}"
                    }
                };
                ProcessNew.SendOcrResult(content);

                var strPost = string.Format("account={0}&token={1}", System.Web.HttpUtility.UrlEncode(processEntity.Account), System.Web.HttpUtility.UrlEncode(processEntity.Token));
                var result = WebClientSyncExt.GetHtml(ServerInfo.HostAccount?.FullUrl + "code.ashx?op=ocrresultcompensate", strPost, 30);
                _Log.Info($"返回OCR失败次数结果:{result}");
            }
            catch (Exception e)
            {
                LogHelper.Log.Error("SetEmptyResult出错！", e);
            }
        }

        private static string GetBytesByUrl(string url)
        {
            try
            {
                using (var webClient = new CNNWebClient())
                {
                    return Convert.ToBase64String(webClient.DownloadData(url));
                }
            }
            catch { }
            return null;
        }

        private static bool CheckIsForbidOperate(CusImageEntity processEntity, UserType limit)
        {
            bool isForbid = false;
            if ((Equals(processEntity.OcrType, OcrType.文本) && !limit.IsSupportTxt)
                            || (Equals(processEntity.OcrType, OcrType.竖排) && !limit.IsSupportVertical)
                            || (Equals(processEntity.OcrType, OcrType.表格) && !limit.IsSupportTable)
                            || (Equals(processEntity.OcrType, OcrType.公式) && !limit.IsSupportMath)
                            || (Equals(processEntity.OcrType, OcrType.翻译) && !limit.IsSupportTranslate)
                            || (CommonHelper.DocFileTypes.Contains(processEntity.FileExt) && !Equals(processEntity.FileExt, "txt") && !limit.IsSupportDocFile)
                            )
            {
                processEntity.State = ProcessState.失败;
                isForbid = true;
            }

            return isForbid;
        }

        private static void ParallelProcessOcr(List<CusImageEntity> listProcessEntity, int perTimeProcess, CancellationToken token, RefCount refCount)
        {
            //OcrContent result = null;
            Parallel.ForEach(listProcessEntity, new ParallelOptions() { MaxDegreeOfParallelism = perTimeProcess, CancellationToken = token }
            , processEntity =>
            {
                try
                {
                    if (!token.IsCancellationRequested)
                    {
                        var content = ProcessPerOcr(processEntity);
                        if (content != null && (!string.IsNullOrEmpty(content.result?.autoText) || content.result?.files?.Count > 0))
                        {
                            refCount.Increment();
                            ProcessNew.SendOcrResult(content);
                        }
                    }
                }
                catch { }
            });
        }

        private static OcrContent ProcessPerOcr(CusImageEntity processEntity)
        {
            LogHelper.Log.Info(string.Format("{0}开始处理", processEntity.OcrType.ToString() + processEntity.ProcessId));

            var ocr = BaseRecHelper.GetInstance(processEntity.OcrType, processEntity.ProcessId ?? 0);
            if (ocr.FileSizeLimit > 0 && processEntity.FileContentLength > 0 && ocr.FileSizeLimit < processEntity.FileContentLength)
            {
                //尺寸超限，不处理
                return null;
            }

            ocr.IsFromLeftToRight = processEntity.IsFromLeftToRight;
            ocr.IsFromTopToDown = processEntity.IsFromTopToDown;

            var content = new OcrContent
            {
                id = processEntity.StrIndex,
                url = processEntity.ImgUrl,
                strBase64 = processEntity.StrImg,
                requestTicks = processEntity.DtUser <= 0 ? processEntity.DtAdd : processEntity.DtUser,
                receivedTicks = processEntity.DtAdd,
                ocrType = processEntity.OcrType,
                from = processEntity.FromLanguage,
                to = processEntity.ToLanguage,
                fileExt = processEntity.FileExt,
                IsAutoDuplicateSymbol = processEntity.IsAutoDuplicateSymbol,
                IsAutoFull2Half = processEntity.IsAutoFull2Half,
                IsAutoSpace = processEntity.IsAutoSpace,
                IsAutoSymbol = processEntity.IsAutoSymbol,
                ClientTicks = processEntity.ClientTicks
            };
            content = ocr.GetResult(content).Result;

            switch (content.state)
            {
                case OcrProcessState.类型不支持:
                case OcrProcessState.处理失败:
                case OcrProcessState.并发限制:
                    processEntity.State = ProcessState.失败;
                    break;
                case OcrProcessState.处理成功:
                    processEntity.State = ProcessState.已完成;
                    break;
                case OcrProcessState.处理超时:
                    processEntity.State = ProcessState.已超时;
                    break;
                default:
                    processEntity.State = ProcessState.正在处理;
                    break;
            }

            if (processEntity.State == ProcessState.已完成)
            {
                if (processEntity.IsTextToTable)
                {
                    if (content.result.ToTable())
                        content.result.resultType = ResutypeEnum.表格;
                }
                else if (processEntity.IsTextToTrans)
                {
                }
            }

            LogHelper.Log.Info(string.Format("【{0}】:{1},OcrWaitTime:{2}ms,OcrProcessTime:{3}ms,UserWaitTime:{4}ms,ID:{5}"
                , content.processName
                , content.state
                , new TimeSpan(content.startTicks - content.receivedTicks).TotalMilliseconds.ToString("F0")
                , new TimeSpan(content.endTicks - content.startTicks).TotalMilliseconds.ToString("F0")
                , new TimeSpan(content.endTicks - content.requestTicks).TotalMilliseconds.ToString("F0")
                , content.id
                ));
            return content;
        }
    }
}
