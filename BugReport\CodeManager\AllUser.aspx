﻿<%@ Page Language="C#" AutoEventWireup="true" CodeBehind="AllUser.aspx.cs" Inherits="BugReprot.AllUser" %>

<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head runat="server">
    <title></title>
</head>
<body>
    <form id="form1" runat="server">
    <div>
        <asp:Label ID="Label1" runat="server" Text="账户列表："></asp:Label>
        <asp:Label ID="lblCount" runat="server" Text="0"></asp:Label>
        <asp:Button ID="btnOK" runat="server" Text="查询" OnClick="btnOK_Click" />
        <asp:Button ID="Button1" runat="server" Text="优化" OnClick="btnVacuum_Click" />&nbsp;&nbsp;服务器时间：<%=BugReprot.CommonHelper.dtServer.ToString() %>
    </div>
    <asp:GridView ID="gvDataSource" runat="server" BackColor="White" BorderColor="#CCCCCC"
        BorderStyle="None" BorderWidth="1px" CellPadding="3" EnableModelValidation="True">
        <FooterStyle BackColor="White" ForeColor="#000066" />
        <HeaderStyle BackColor="#006699" Font-Bold="True" ForeColor="White" />
        <PagerStyle BackColor="White" ForeColor="#000066" HorizontalAlign="Left" />
        <RowStyle ForeColor="#000066" />
        <SelectedRowStyle BackColor="#669999" Font-Bold="True" ForeColor="White" />
    </asp:GridView>
    </form>
</body>
</html>
