﻿using System;
using System.Collections.Generic;
using System.Drawing;
using System.Drawing.Imaging;
using System.Threading.Tasks;
using log4net;
using System.Linq;

namespace BaiDuAPI
{
    public class ImageHelper
    {

        #region 坐标转换
        public static int getIndexByPositon(int p1, int p2)
        {
            var result = ((int)Math.Floor(p1 * 1.0 / 75) * 2) + ((int)Math.Floor(p2 * 1.0 / 75));
            //var result = ((int)Math.Floor(p1 * 1.0 / 70) * 2) + ((int)Math.Floor(p2 * 1.0 / 70));
            //for (int x = 0; x < 4; x++)
            //{
            //    for (int y = 0; y < 2; y++)
            //    {
            //        var width = 6 * (x + 1) + x * 66 - 1;
            //        var height = 41 + y * 66 + 6 * y;
            //        if (p1 >= width && p1 <= width + 67 && p2 >= height && p2 <= height + 67)
            //        {
            //            return x * 2 + y;
            //        }
            //    }
            //}
            return result;
        }

        public static int get51Index(int index)
        {
            //1234
            //5678

            //0246
            //1357
            int result = 8;
            switch (index)
            {
                case 1:
                    result = 0;
                    break;
                case 2:
                    result = 2;
                    break;
                case 3:
                    result = 4;
                    break;
                case 4:
                    result = 6;
                    break;
                case 5:
                    result = 1;
                    break;
                case 6:
                    result = 3;
                    break;
                case 7:
                    result = 5;
                    break;
                case 8:
                    result = 7;
                    break;
                default:
                    break;
            }
            return result;
        }

        public static string GetCodePointByIndex(List<int> codes, ImageType type = ImageType.Eight)
        {
            string strCode = "";
            if (codes != null && codes.Count > 0)
            {
                int x, y, width, height;
                switch (type)
                {
                    case ImageType.Eight:
                        foreach (var item in codes)
                        {
                            x = item / 2;
                            y = item % 2;
                            width = x * 70 + 70 / 2;
                            height = y * 70 + 70 / 2;
                            //0:35,35
                            //1:35,105
                            //2:105,35
                            //3:105,105
                            //4:175,35
                            //5:175,105
                            //6:245,35
                            //7:245,105

                            strCode += string.Format(",{0},{1}", width, height);
                        }
                        break;
                    case ImageType.Eighteen:
                        foreach (var item in codes)
                        {
                            x = item / 3;
                            y = item % 3;
                            width = 4 * (x + 1) + x * 44 + 44 / 2;
                            height = 40 + y * 44 + 4 * y + 44 / 2;

                            strCode += string.Format(",{0},{1}", width, height);
                        }
                        break;
                    case ImageType.ThirtySix:
                        break;
                    default:
                        break;
                }
            }
            codes = null;
            return strCode.TrimStart(',').TrimEnd(',');
        }

        #endregion
        public static List<ImageEntity> GetImageEntity(List<Bitmap> lstTmp)
        {
            List<ImageEntity> lstResult = new List<ImageEntity>();
            for (int i = 0; i < lstTmp.Count; i++)
            {
                lstResult.Add(null);
            }

            System.Threading.Tasks.Parallel.For(0, lstTmp.Count, i =>
            {
                try
                {
                    lstResult[i] = GetHash(lstTmp[i]);
                }
                catch { }
            });
            return lstResult;
        }

        public static List<Bitmap> SpiltImage(Bitmap source, ImageType type, List<int> lstRes = null)
        {
            List<Bitmap> lstTmp = new List<Bitmap>();
            if (source != null && source.Width > 250)
            {
                int width, height;
                RectangleF part;
                switch (type)
                {
                    case ImageType.Eight:
                        for (int x = 0; x < 4; x++)
                        {
                            for (int y = 0; y < 2; y++)
                            {
                                if (lstRes == null || lstRes.Contains(x * 2 + y))
                                {
                                    width = 6 * (x + 1) + x * 66 - 1;
                                    height = 41 + y * 66 + 6 * y;
                                    Bitmap s = source.Clone(new RectangleF(width, height, 67, 67), PixelFormat.DontCare);
                                    lstTmp.Add(s);
                                }
                            }
                        }
                        break;
                    case ImageType.Eighteen:
                        for (int x = 0; x < 6; x++)
                        {
                            for (int y = 0; y < 3; y++)
                            {
                                if (lstRes == null || lstRes.Contains(x * 3 + y))
                                {
                                    width = 4 * (x + 1) + x * 44;
                                    height = 40 + y * 44 + 4 * y;
                                    part = new RectangleF(width, height, 44, 44);//像素矩阵
                                    Bitmap s = source.Clone(part, PixelFormat.DontCare);
                                    lstTmp.Add(s);
                                }
                            }
                        }
                        break;
                    case ImageType.ThirtySix:
                        break;
                    default:
                        break;
                }
            }
            else
            {
                lstTmp.Add(source);
            }
            return lstTmp;
        }

        public static List<ImageEntity> SpiltImageEntity(Bitmap source, ImageType type, List<int> lstRes = null)
        {
            List<ImageEntity> lstResult = new List<ImageEntity>();
            if (source != null && source.Width > 250)
            {
                List<Bitmap> lstTmp = SpiltImage(source, type, lstRes);

                lstResult = GetImageEntity(lstTmp);

                lstTmp.ForEach(p => p.Dispose());
                lstTmp = null;
                //Console.WriteLine("图片分拆耗时：" + new TimeSpan(DateTime.Now.Ticks - dtStart.Ticks).TotalMilliseconds.ToString("F0") + "ms");
            }
            return lstResult;
        }

        public static ImageType GetImgType(Bitmap source)
        {
            var result = ImageType.Eight;
            try
            {
                int count = 0;
                Color color;

                PointBitmap bit = new PointBitmap(source);
                try
                {
                    bit.LockBits();
                    for (int x = 1; x < 4; x++)
                    {
                        for (int y = 0; y < 2; y++)
                        {
                            int width = 6 * (x + 1) + x * 66 - 1 - 3;
                            int height = 41 + y * 66 + 6 * y;
                            for (int i = 1; i < 6; i++)
                            {
                                color = bit.GetPixel(width, height + (i + 1) * 10);
                                if ((color.R + color.G + color.B) / 3 < 240)
                                {
                                    count++;
                                }
                            }
                        }
                    }
                }
                catch (Exception oe)
                {
                    Console.WriteLine(oe.Message);
                }
                finally
                {
                    bit.UnlockBits();
                    bit = null;
                }
                if (count < 8)
                //if ((count * 1.0 / 36) * 100 <= 6)
                {
                    result = ImageType.Eight;
                }
                else
                {
                    result = ImageType.Eighteen;
                    //count = 0;
                    //for (int x = 1; x < 6; x++)
                    //{
                    //    for (int y = 0; y < 3; y++)
                    //    {
                    //        int width = 4 * (x + 1) + x * 44 - 2;
                    //        int height = 41 + y * 44 + 4 * y;
                    //        for (int i = 0; i < 4; i++)
                    //        {
                    //            color = bit.GetPixel(width, height + (i + 1) * 10);
                    //            if ((color.R + color.G + color.B) / 3 < 240)
                    //            {
                    //                count++;
                    //            }
                    //        }
                    //    }
                    //}
                    //if ((count * 1.0 / 60) * 100 <= 5)
                    //{
                    //    result = ImageType.Eighteen;
                    //}
                }
            }
            catch (Exception oe)
            {
                Console.WriteLine(oe.Message);
            }
            return result;
        }

        #region 图片识别
        public static Dictionary<string, List<ImageEntity>> LstCodes = new Dictionary<string, List<ImageEntity>>();
        public static Dictionary<string, List<ImageEntity>> LstTmpImg = new Dictionary<string, List<ImageEntity>>();

        public static string GetCode(Bitmap bit, string strName, bool isLogin, string from)
        {
            ImageType type = ImageType.Eight;
            var codes = GetCodes(bit, strName, out  type, isLogin, from);
            return GetCodePointByIndex(codes, type);
        }

        public static string GetCodeIndex(Bitmap bit, string strName, bool isLogin, string from)
        {
            string strCode = "";
            ImageType type = ImageType.Eight;
            var codes = GetCodes(bit, strName, out  type, isLogin, from);
            if (codes != null && codes.Count > 0)
            {
                strCode = string.Join(",", codes.ToArray());
            }
            codes = null;
            return strCode;
        }

        private static List<int> GetCodes(Bitmap bit, string strName, out ImageType type, bool isLogin, string from)
        {
            try
            {
                ConfigHelper.CountCache.Increment(ConfigHelper.GetFlagName(isLogin ? "登陆图片识别" : "下单图片识别", from), 1);
            }
            catch (Exception oe)
            {
                BaiDuCode._Log.Error("记录图片识别次数失败！", oe);
            }
            var codes = new List<int>();
            using (bit)
            {
                type = GetImgType(bit);
                try
                {
                    if (type == ImageType.Eight)
                    {
                        if (string.IsNullOrEmpty(strName))
                        {
                            try
                            {
                                //strName = BaiDuCode.GetCodeByPath();
                                //todo getName
                            }
                            catch (Exception oe)
                            {
                                Console.WriteLine(oe.Message);
                            }
                        }
                        //LogManager.GetLogger("ImageHelper").InfoFormat("开始识别{0}，当前库：{1}", strName, LstCodes.Count);
                        if (!string.IsNullOrEmpty(strName))
                        {
                            var lstImg = SpiltImageEntity(bit, type);

                            codes = GetCodesByImages(lstImg, strName, isLogin);
                            lstImg = null;
                        }
                    }
                }
                catch (Exception oe)
                {
                    Console.WriteLine(oe.Message);
                }
                finally
                {
                    if (bit != null)
                        bit.Dispose();
                }
            }
            return codes;
        }

        public static List<string> GetNameByImage(List<ImageEntity> lstImg, string[] lstNames, ref Dictionary<int, List<string>> lstRecName, bool isLogin = true)
        {
            List<string> lstResult = new List<string>();
            try
            {
                for (int i = 0; i < lstImg.Count; i++)
                {
                    List<string> lstTmp = new List<string>();
                    foreach (var strName in lstNames)
                    {
                        if (LstCodes.ContainsKey(strName))
                        {
                            bool res = IsSame(lstImg[i], LstCodes[strName], 10);// GetMaxColorDifByName(name));
                            if (res)
                            {
                                if (!lstTmp.Contains(strName))
                                    lstTmp.Add(strName);
                            }
                        }
                    }
                    if (lstTmp == null || lstTmp.Count <= 0)
                    {
                        Parallel.ForEach(LstCodes, p =>
                        {
                            try
                            {
                                var res = IsSame(lstImg[i], p.Value, 10);// );
                                if (res)
                                {
                                    if (!lstTmp.Contains(p.Key))
                                        lstTmp.Add(p.Key);
                                }
                            }
                            catch { }
                        });
                    }

                    lstTmp.RemoveAll(p => string.IsNullOrEmpty(p));
                    lstTmp = lstTmp.Distinct().OrderBy(p => p).ToList();

                    if (lstTmp.Count > 1 && lstTmp.Exists(p => lstNames.Contains(p)))
                    {
                        lstTmp = lstTmp.FindAll(p => lstNames.Contains(p)).ToList();
                    }

                    lstRecName[i] = lstTmp;

                    lstResult.AddRange(lstTmp);
                }
            }
            catch (Exception oe)
            {
                BaiDuCode._Log.Error(oe);
            }
            if (!isLogin && lstNames != null && lstNames.Length > 1 && lstImg.Count > 1)
            {
                if (lstRecName.Sum(p => (p.Value != null && p.Value.Count > 0) ? 1 : 0) > 0)
                {
                    List<string> lstExitsName = new List<string>();
                    foreach (var item in lstRecName)
                    {
                        if (item.Value != null && item.Value.Count > 0)
                        {
                            lstExitsName.AddRange(item.Value);
                        }
                    }
                    var lstExpName = lstNames.ToList().Select(p => !lstExitsName.Contains(p) ? p : "").ToList();
                    lstExpName.RemoveAll(p => string.IsNullOrEmpty(p));
                    lstExpName = lstExpName.Distinct().OrderBy(p => p).ToList();

                    if (lstExpName.Count > 0)
                    {
                        for (int i = 0; i < lstRecName.Count; i++)
                        {
                            if (lstRecName[i] == null || lstRecName[i].Count <= 0)
                            {
                                lstRecName[i] = lstExpName;
                            }
                        }
                        lstResult.AddRange(lstExpName);
                    }
                }
            }
            lstResult = lstResult.Distinct().OrderBy(p => p).ToList();
            return lstResult;
        }

        private static List<int> GetCodesByImages(List<ImageEntity> lstImg, string strName, bool isLogin = true)
        {
            //DateTime dtStart = DateTime.Now;
            var codes = new List<int>();
            var nowStrs = new List<string>();
            if (lstImg != null && lstImg.Count > 0)
            {
                var names = strName.Split(new string[] { "、" }, StringSplitOptions.RemoveEmptyEntries);
                if (names != null && names.Length > 0)
                {
                    Parallel.ForEach(names, name =>
                    {
                        if (LstCodes.ContainsKey(name))
                        {
                            Parallel.For(0, lstImg.Count, i =>
                            {
                                try
                                {
                                    bool res = IsSame(lstImg[i], LstCodes[name], 10);// GetMaxColorDifByName(name));
                                    if (res && !codes.Contains(i))
                                    {
                                        codes.Add(i);
                                        if (!nowStrs.Contains(name))
                                            nowStrs.Add(name);
                                    }
                                }
                                catch { }
                            });
                        }
                    });
                }
                //if (codes.Count <= 0)
                //{
                //    var tmp = GetNameByImages(lstImg);
                //    if (tmp.Count > 0)
                //    {
                //        var allNames = tmp.Select(p => p.Value).Distinct().ToList();
                //        if (names != null && names.Length > 0)
                //        {
                //            foreach (var item in tmp)
                //            {
                //                if (names.Contains(item.Value))
                //                {
                //                    codes.Add(lstImg.IndexOf(item.Key));
                //                    if (!nowStrs.Contains(item.Value))
                //                        nowStrs.Add(item.Value);
                //                }
                //            }
                //        }
                //        var countDic = tmp.Select(p=>new );

                //        if (tmp.Count != lstImg.Count)
                //        {
                //            foreach (var item in lstImg)
                //            {
                //                if (!tmp.ContainsKey(item))
                //                {
                //                    codes.Add(lstImg.IndexOf(item));
                //                }
                //            }
                //        }
                //    }
                //}
                names = null;
            }
            //Console.WriteLine("图片识别耗时：" + new TimeSpan(DateTime.Now.Ticks - dtStart.Ticks).TotalMilliseconds.ToString("F0") + "ms");
            return codes;
        }

        private static Dictionary<ImageEntity, string> GetNameByImages(List<ImageEntity> lstImg)
        {
            var lstDic = new Dictionary<ImageEntity, string>();
            Parallel.ForEach(lstImg, img =>
            {
                if (!lstDic.ContainsKey(img))
                {
                    Parallel.ForEach(LstCodes, p =>
                    {
                        try
                        {
                            if (!lstDic.ContainsKey(img))
                            {
                                var res = IsSame(img, p.Value, 10);
                                if (res)
                                {
                                    if (!lstDic.ContainsKey(img))
                                        lstDic.Add(img, p.Key);
                                }
                            }
                        }
                        catch { }
                    });
                }
            });
            return lstDic;
        }

        // Compare hash
        public static bool IsSame(ImageEntity a, List<ImageEntity> lst, double nmaxColorDif)
        {
            bool result = false;
            foreach (var b in lst)
            {
                result = IsSameByEntity(a, b, nmaxColorDif);
                if (result)
                    break;
            }
            return result;
        }

        public static double NGrayDifference = 2;
        public static double NColorDifference = 5;
        public static double NMaxDifPercent = 10;
        private static Dictionary<string, double> LstColorDif = new Dictionary<string, double>();

        public static double GetMaxColorDifByName(string strName)
        {
            if (string.IsNullOrEmpty(strName) || !LstColorDif.ContainsKey(strName))
            {
                return NColorDifference;
            }
            else
            {
                return LstColorDif[strName];
            }
        }

        public static ImageEntity GetHash(Bitmap SourceImg, bool isHanZi = false)
        {
            var reslut = new ImageEntity();
            using (var bit = ReduceSize(SourceImg))
            {
                Byte[] colorRValues = null;
                Byte[] colorGValues = null;
                Byte[] colorBValues = null;
                Byte[] grayValues = ReduceColor(bit, isHanZi, ref colorRValues, ref colorGValues, ref colorBValues);
                if (grayValues != null)
                    reslut.NAverageGray = CalcAverage(grayValues);
                if (colorRValues != null)
                    reslut.NAverageColorR = CalcAverage(colorRValues);
                if (colorGValues != null)
                    reslut.NAverageColorG = CalcAverage(colorGValues);
                if (colorBValues != null)
                    reslut.NAverageColorB = CalcAverage(colorBValues);
                reslut.StrId = ComputeBits(grayValues, Convert.ToByte(reslut.NAverageGray));
                grayValues = null;
                colorRValues = null;
                colorRValues = null;
                colorGValues = null;
                colorBValues = null;
                bit.Dispose();
            }
            return reslut;
        }

        // Step 1 : Reduce size to 8*8
        private static Bitmap ReduceSize(Bitmap SourceImg, int width = 16, int height = 16)
        {
            return new Bitmap(SourceImg.GetThumbnailImage(width, height, () => { return false; }, IntPtr.Zero));
        }
        // Step 2 : Reduce Color
        private static Byte[] ReduceColor(Bitmap image, bool isHanZi, ref byte[] colorRValues, ref byte[] colorGValues, ref byte[] colorBValues)
        {
            Byte[] grayValues = null;
            PointBitmap bit = new PointBitmap(image);
            try
            {
                bit.LockBits();
                grayValues = new Byte[bit.Width * bit.Height];
                if (!isHanZi)
                {
                    colorRValues = new Byte[(bit.Width - 5) * (bit.Height - 5)];
                    colorGValues = new Byte[(bit.Width - 5) * (bit.Height - 5)];
                    colorBValues = new Byte[(bit.Width - 5) * (bit.Height - 5)];
                }
                Color color; byte grayValue;
                for (int x = 0; x < bit.Width; x++)
                    for (int y = 0; y < bit.Height; y++)
                    {
                        color = bit.GetPixel(x, y);
                        if (!isHanZi && x >= 5 && y >= 5)
                        {
                            colorRValues[(x - 5) * (bit.Width - 5) + (y - 5)] = (byte)(color.R);
                            colorGValues[(x - 5) * (bit.Width - 5) + (y - 5)] = (byte)(color.G);
                            colorBValues[(x - 5) * (bit.Width - 5) + (y - 5)] = (byte)(color.B);
                        }
                        grayValue = (byte)((color.R * 30 + color.G * 59 + color.B * 11) / 100);

                        grayValues[x * bit.Width + y] = grayValue;
                    }
            }
            catch (Exception oe)
            {
                Console.WriteLine(oe.Message);
            }
            finally
            {
                bit.UnlockBits();
                bit = null;
            }
            return grayValues;
        }
        // Step 3 : Average the colors
        private static double CalcAverage(byte[] values)
        {
            int sum = 0;
            for (int i = 0; i < values.Length; i++)
                sum += (int)values[i];
            return sum * 1.0 / values.Length;
        }
        // Step 4 : Compute the bits
        private static String ComputeBits(byte[] values, byte averageValue)
        {
            char[] result = new char[values.Length];
            for (int i = 0; i < values.Length; i++)
            {
                if (values[i] < averageValue)
                    result[i] = '0';
                else
                    result[i] = '1';
            }
            return new String(result);
        }

        // Compare hash
        public static int CalcSimilarDegree(string a, string b)
        {
            if (a.Length != b.Length)
                throw new ArgumentException();
            int count = 0;
            for (int i = 0; i < a.Length; i++)
            {
                if (a[i] != b[i])
                    count++;
            }
            return count;
        }

        // Compare hash
        public static bool IsSameByEntity(ImageEntity a, ImageEntity b, double maxColorDif)
        {
            bool result = false;
            if (a.NAverageGray <= b.NAverageGray + NGrayDifference && a.NAverageGray >= b.NAverageGray - NGrayDifference)
            {
                if ((a.NAverageColorR <= b.NAverageColorR + maxColorDif && a.NAverageColorR >= b.NAverageColorR - maxColorDif)
                    && (a.NAverageColorG <= b.NAverageColorG + maxColorDif && a.NAverageColorG >= b.NAverageColorG - maxColorDif)
                    && (a.NAverageColorB <= b.NAverageColorB + maxColorDif + 3 && a.NAverageColorB >= b.NAverageColorB - maxColorDif - 3))
                {
                    int count = CalcSimilarDegree(a.StrId, b.StrId);
                    if (Math.Floor((count * 1.0 / a.StrId.Length) * 100) <= NMaxDifPercent)
                    {
                        result = true;
                    }
                }
            }
            return result;
        }

        #endregion

    }

}