﻿namespace NewTicket
{
    partial class FormNowUser
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle1 = new System.Windows.Forms.DataGridViewCellStyle();
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle2 = new System.Windows.Forms.DataGridViewCellStyle();
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle5 = new System.Windows.Forms.DataGridViewCellStyle();
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle3 = new System.Windows.Forms.DataGridViewCellStyle();
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle4 = new System.Windows.Forms.DataGridViewCellStyle();
            this.gvUsers = new System.Windows.Forms.DataGridView();
            this.dataGridViewTextBoxColumn1 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.dataGridViewTextBoxColumn2 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.实名制 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.cmsContext = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.打开12306网站ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.取消未完成订单ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.查看未完成订单ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.查看已完成订单ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripSeparator2 = new System.Windows.Forms.ToolStripSeparator();
            this.tsmCopy = new System.Windows.Forms.ToolStripMenuItem();
            this.检查状态ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.复制登录信息ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripSeparator1 = new System.Windows.Forms.ToolStripSeparator();
            this.验证账户状态ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.cmsDelte = new System.Windows.Forms.ToolStripMenuItem();
            this.重新登录RToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.全部重新登陆ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.btnOK = new System.Windows.Forms.Button();
            this.btnCancel = new System.Windows.Forms.Button();
            this.lblStatus = new System.Windows.Forms.Label();
            this.btnCancelAll = new System.Windows.Forms.Button();
            this.bgCancelAll = new System.ComponentModel.BackgroundWorker();
            this.lblCount = new System.Windows.Forms.Label();
            this.save = new System.Windows.Forms.SaveFileDialog();
            this.btnAll = new System.Windows.Forms.Button();
            ((System.ComponentModel.ISupportInitialize)(this.gvUsers)).BeginInit();
            this.cmsContext.SuspendLayout();
            this.SuspendLayout();
            // 
            // gvUsers
            // 
            this.gvUsers.AllowUserToAddRows = false;
            this.gvUsers.AllowUserToDeleteRows = false;
            this.gvUsers.AllowUserToResizeRows = false;
            dataGridViewCellStyle1.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleLeft;
            this.gvUsers.AlternatingRowsDefaultCellStyle = dataGridViewCellStyle1;
            this.gvUsers.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.gvUsers.BackgroundColor = System.Drawing.Color.FromArgb(((int)(((byte)(224)))), ((int)(((byte)(224)))), ((int)(((byte)(224)))));
            this.gvUsers.BorderStyle = System.Windows.Forms.BorderStyle.None;
            this.gvUsers.CausesValidation = false;
            dataGridViewCellStyle2.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleCenter;
            dataGridViewCellStyle2.BackColor = System.Drawing.SystemColors.Control;
            dataGridViewCellStyle2.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            dataGridViewCellStyle2.ForeColor = System.Drawing.SystemColors.WindowText;
            dataGridViewCellStyle2.SelectionBackColor = System.Drawing.SystemColors.Highlight;
            dataGridViewCellStyle2.SelectionForeColor = System.Drawing.SystemColors.HighlightText;
            dataGridViewCellStyle2.WrapMode = System.Windows.Forms.DataGridViewTriState.True;
            this.gvUsers.ColumnHeadersDefaultCellStyle = dataGridViewCellStyle2;
            this.gvUsers.Columns.AddRange(new System.Windows.Forms.DataGridViewColumn[] {
            this.dataGridViewTextBoxColumn1,
            this.dataGridViewTextBoxColumn2,
            this.实名制});
            this.gvUsers.ContextMenuStrip = this.cmsContext;
            this.gvUsers.Location = new System.Drawing.Point(8, 28);
            this.gvUsers.Name = "gvUsers";
            this.gvUsers.ReadOnly = true;
            this.gvUsers.RowHeadersVisible = false;
            dataGridViewCellStyle5.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleLeft;
            this.gvUsers.RowsDefaultCellStyle = dataGridViewCellStyle5;
            this.gvUsers.RowTemplate.Height = 25;
            this.gvUsers.SelectionMode = System.Windows.Forms.DataGridViewSelectionMode.FullRowSelect;
            this.gvUsers.Size = new System.Drawing.Size(397, 301);
            this.gvUsers.TabIndex = 29;
            this.gvUsers.TabStop = false;
            this.gvUsers.CellMouseDown += new System.Windows.Forms.DataGridViewCellMouseEventHandler(this.gvUsers_CellMouseDown);
            this.gvUsers.DataBindingComplete += new System.Windows.Forms.DataGridViewBindingCompleteEventHandler(this.gvUsers_DataBindingComplete);
            this.gvUsers.MouseDoubleClick += new System.Windows.Forms.MouseEventHandler(this.gvUsers_MouseDoubleClick);
            // 
            // dataGridViewTextBoxColumn1
            // 
            this.dataGridViewTextBoxColumn1.DataPropertyName = "StrNowUserName";
            dataGridViewCellStyle3.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleCenter;
            this.dataGridViewTextBoxColumn1.DefaultCellStyle = dataGridViewCellStyle3;
            this.dataGridViewTextBoxColumn1.HeaderText = "用户名";
            this.dataGridViewTextBoxColumn1.Name = "dataGridViewTextBoxColumn1";
            this.dataGridViewTextBoxColumn1.ReadOnly = true;
            this.dataGridViewTextBoxColumn1.Width = 119;
            // 
            // dataGridViewTextBoxColumn2
            // 
            this.dataGridViewTextBoxColumn2.DataPropertyName = "StrNowUserStatus";
            dataGridViewCellStyle4.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleCenter;
            dataGridViewCellStyle4.Format = "MM月dd日";
            dataGridViewCellStyle4.NullValue = null;
            this.dataGridViewTextBoxColumn2.DefaultCellStyle = dataGridViewCellStyle4;
            this.dataGridViewTextBoxColumn2.HeaderText = "状态";
            this.dataGridViewTextBoxColumn2.Name = "dataGridViewTextBoxColumn2";
            this.dataGridViewTextBoxColumn2.ReadOnly = true;
            this.dataGridViewTextBoxColumn2.Width = 119;
            // 
            // 实名制
            // 
            this.实名制.DataPropertyName = "NowAccountStatus";
            this.实名制.HeaderText = "实名制";
            this.实名制.Name = "实名制";
            this.实名制.ReadOnly = true;
            this.实名制.Width = 119;
            // 
            // cmsContext
            // 
            this.cmsContext.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.打开12306网站ToolStripMenuItem,
            this.取消未完成订单ToolStripMenuItem,
            this.查看未完成订单ToolStripMenuItem,
            this.查看已完成订单ToolStripMenuItem,
            this.toolStripSeparator2,
            this.tsmCopy,
            this.检查状态ToolStripMenuItem,
            this.复制登录信息ToolStripMenuItem,
            this.toolStripSeparator1,
            this.验证账户状态ToolStripMenuItem,
            this.cmsDelte,
            this.重新登录RToolStripMenuItem,
            this.全部重新登陆ToolStripMenuItem});
            this.cmsContext.Name = "cmsContext";
            this.cmsContext.Size = new System.Drawing.Size(195, 302);
            // 
            // 打开12306网站ToolStripMenuItem
            // 
            this.打开12306网站ToolStripMenuItem.Name = "打开12306网站ToolStripMenuItem";
            this.打开12306网站ToolStripMenuItem.Size = new System.Drawing.Size(194, 24);
            this.打开12306网站ToolStripMenuItem.Text = "打开12306网站";
            this.打开12306网站ToolStripMenuItem.Click += new System.EventHandler(this.打开12306网站ToolStripMenuItem_Click);
            // 
            // 取消未完成订单ToolStripMenuItem
            // 
            this.取消未完成订单ToolStripMenuItem.Name = "取消未完成订单ToolStripMenuItem";
            this.取消未完成订单ToolStripMenuItem.Size = new System.Drawing.Size(194, 24);
            this.取消未完成订单ToolStripMenuItem.Text = "取消未完成订单";
            this.取消未完成订单ToolStripMenuItem.Click += new System.EventHandler(this.取消未完成订单ToolStripMenuItem_Click);
            // 
            // 查看未完成订单ToolStripMenuItem
            // 
            this.查看未完成订单ToolStripMenuItem.Name = "查看未完成订单ToolStripMenuItem";
            this.查看未完成订单ToolStripMenuItem.Size = new System.Drawing.Size(194, 24);
            this.查看未完成订单ToolStripMenuItem.Text = "查看未完成订单";
            this.查看未完成订单ToolStripMenuItem.Click += new System.EventHandler(this.查看未完成订单ToolStripMenuItem_Click);
            // 
            // 查看已完成订单ToolStripMenuItem
            // 
            this.查看已完成订单ToolStripMenuItem.Name = "查看已完成订单ToolStripMenuItem";
            this.查看已完成订单ToolStripMenuItem.Size = new System.Drawing.Size(194, 24);
            this.查看已完成订单ToolStripMenuItem.Text = "查看已完成订单";
            this.查看已完成订单ToolStripMenuItem.Click += new System.EventHandler(this.查看已完成订单ToolStripMenuItem_Click);
            // 
            // toolStripSeparator2
            // 
            this.toolStripSeparator2.Name = "toolStripSeparator2";
            this.toolStripSeparator2.Size = new System.Drawing.Size(191, 6);
            // 
            // tsmCopy
            // 
            this.tsmCopy.Font = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.tsmCopy.ForeColor = System.Drawing.Color.Red;
            this.tsmCopy.Name = "tsmCopy";
            this.tsmCopy.Size = new System.Drawing.Size(194, 24);
            this.tsmCopy.Text = "复制成功账号";
            this.tsmCopy.Click += new System.EventHandler(this.tsmCopy_Click);
            // 
            // 检查状态ToolStripMenuItem
            // 
            this.检查状态ToolStripMenuItem.Font = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.检查状态ToolStripMenuItem.ForeColor = System.Drawing.Color.Red;
            this.检查状态ToolStripMenuItem.Name = "检查状态ToolStripMenuItem";
            this.检查状态ToolStripMenuItem.Size = new System.Drawing.Size(194, 24);
            this.检查状态ToolStripMenuItem.Text = "复制成功账号(带席别)";
            this.检查状态ToolStripMenuItem.Click += new System.EventHandler(this.检查状态ToolStripMenuItem_Click);
            // 
            // 复制登录信息ToolStripMenuItem
            // 
            this.复制登录信息ToolStripMenuItem.Font = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.复制登录信息ToolStripMenuItem.Name = "复制登录信息ToolStripMenuItem";
            this.复制登录信息ToolStripMenuItem.Size = new System.Drawing.Size(194, 24);
            this.复制登录信息ToolStripMenuItem.Text = "复制账号信息";
            this.复制登录信息ToolStripMenuItem.Click += new System.EventHandler(this.复制登录信息ToolStripMenuItem_Click);
            // 
            // toolStripSeparator1
            // 
            this.toolStripSeparator1.Name = "toolStripSeparator1";
            this.toolStripSeparator1.Size = new System.Drawing.Size(191, 6);
            // 
            // 验证账户状态ToolStripMenuItem
            // 
            this.验证账户状态ToolStripMenuItem.Font = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.验证账户状态ToolStripMenuItem.Name = "验证账户状态ToolStripMenuItem";
            this.验证账户状态ToolStripMenuItem.Size = new System.Drawing.Size(194, 24);
            this.验证账户状态ToolStripMenuItem.Text = "刷票故障诊断(&C)";
            this.验证账户状态ToolStripMenuItem.ToolTipText = "如果在刷票过程中账号自动停止，\r\n可以点击此选项开始检查故障.\r\n检查到问题后,自动重新开始刷票.";
            this.验证账户状态ToolStripMenuItem.Click += new System.EventHandler(this.验证账户状态ToolStripMenuItem_Click);
            // 
            // cmsDelte
            // 
            this.cmsDelte.Name = "cmsDelte";
            this.cmsDelte.Size = new System.Drawing.Size(194, 24);
            this.cmsDelte.Text = "删除选定账号";
            this.cmsDelte.Click += new System.EventHandler(this.cmsDelte_Click);
            // 
            // 重新登录RToolStripMenuItem
            // 
            this.重新登录RToolStripMenuItem.Name = "重新登录RToolStripMenuItem";
            this.重新登录RToolStripMenuItem.Size = new System.Drawing.Size(194, 24);
            this.重新登录RToolStripMenuItem.Text = "重新登录(&R)";
            this.重新登录RToolStripMenuItem.Click += new System.EventHandler(this.重新登录RToolStripMenuItem_Click);
            // 
            // 全部重新登陆ToolStripMenuItem
            // 
            this.全部重新登陆ToolStripMenuItem.Font = new System.Drawing.Font("微软雅黑", 10.5F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.全部重新登陆ToolStripMenuItem.Name = "全部重新登陆ToolStripMenuItem";
            this.全部重新登陆ToolStripMenuItem.Size = new System.Drawing.Size(194, 24);
            this.全部重新登陆ToolStripMenuItem.Text = "全部重新登陆";
            this.全部重新登陆ToolStripMenuItem.Click += new System.EventHandler(this.全部重新登陆ToolStripMenuItem_Click);
            // 
            // btnOK
            // 
            this.btnOK.Anchor = System.Windows.Forms.AnchorStyles.Bottom;
            this.btnOK.Location = new System.Drawing.Point(109, 337);
            this.btnOK.Name = "btnOK";
            this.btnOK.Size = new System.Drawing.Size(88, 30);
            this.btnOK.TabIndex = 31;
            this.btnOK.Text = "确定";
            this.btnOK.UseVisualStyleBackColor = true;
            this.btnOK.Click += new System.EventHandler(this.btnOK_Click);
            // 
            // btnCancel
            // 
            this.btnCancel.Anchor = System.Windows.Forms.AnchorStyles.Bottom;
            this.btnCancel.DialogResult = System.Windows.Forms.DialogResult.Cancel;
            this.btnCancel.Location = new System.Drawing.Point(203, 337);
            this.btnCancel.Name = "btnCancel";
            this.btnCancel.Size = new System.Drawing.Size(88, 30);
            this.btnCancel.TabIndex = 32;
            this.btnCancel.Text = "关闭";
            this.btnCancel.UseVisualStyleBackColor = true;
            this.btnCancel.Click += new System.EventHandler(this.btnCancel_Click);
            // 
            // lblStatus
            // 
            this.lblStatus.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Bold);
            this.lblStatus.Location = new System.Drawing.Point(12, 5);
            this.lblStatus.Name = "lblStatus";
            this.lblStatus.Size = new System.Drawing.Size(271, 20);
            this.lblStatus.TabIndex = 125;
            this.lblStatus.Text = "提示:双击查看未支付，右键查看更多操作!";
            this.lblStatus.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
            // 
            // btnCancelAll
            // 
            this.btnCancelAll.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.btnCancelAll.Location = new System.Drawing.Point(315, 1);
            this.btnCancelAll.Name = "btnCancelAll";
            this.btnCancelAll.Size = new System.Drawing.Size(89, 26);
            this.btnCancelAll.TabIndex = 129;
            this.btnCancelAll.Text = "取消全部订单";
            this.btnCancelAll.UseVisualStyleBackColor = true;
            this.btnCancelAll.Click += new System.EventHandler(this.button1_Click);
            // 
            // bgCancelAll
            // 
            this.bgCancelAll.WorkerSupportsCancellation = true;
            this.bgCancelAll.DoWork += new System.ComponentModel.DoWorkEventHandler(this.bgCancelAll_DoWork);
            this.bgCancelAll.RunWorkerCompleted += new System.ComponentModel.RunWorkerCompletedEventHandler(this.bgCancelAll_RunWorkerCompleted);
            // 
            // lblCount
            // 
            this.lblCount.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.lblCount.Location = new System.Drawing.Point(321, 340);
            this.lblCount.Name = "lblCount";
            this.lblCount.Size = new System.Drawing.Size(83, 14);
            this.lblCount.TabIndex = 130;
            this.lblCount.Text = "共0个";
            this.lblCount.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // save
            // 
            this.save.Filter = "TXT文件|*.txt";
            this.save.Title = "选择保存位置";
            // 
            // btnAll
            // 
            this.btnAll.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Left)));
            this.btnAll.Location = new System.Drawing.Point(14, 340);
            this.btnAll.Name = "btnAll";
            this.btnAll.Size = new System.Drawing.Size(42, 24);
            this.btnAll.TabIndex = 126;
            this.btnAll.Text = "全选";
            this.btnAll.UseVisualStyleBackColor = true;
            this.btnAll.Visible = false;
            this.btnAll.Click += new System.EventHandler(this.btnAll_Click);
            // 
            // FormNowUser
            // 
            this.AcceptButton = this.btnOK;
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.CancelButton = this.btnCancel;
            this.ClientSize = new System.Drawing.Size(413, 375);
            this.Controls.Add(this.lblCount);
            this.Controls.Add(this.btnCancelAll);
            this.Controls.Add(this.btnAll);
            this.Controls.Add(this.lblStatus);
            this.Controls.Add(this.btnCancel);
            this.Controls.Add(this.btnOK);
            this.Controls.Add(this.gvUsers);
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.Name = "FormNowUser";
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen;
            this.Text = "当前用户列表";
            this.Load += new System.EventHandler(this.FormNowUser_Load);
            ((System.ComponentModel.ISupportInitialize)(this.gvUsers)).EndInit();
            this.cmsContext.ResumeLayout(false);
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.DataGridView gvUsers;
        private System.Windows.Forms.Button btnOK;
        private System.Windows.Forms.Button btnCancel;
        private System.Windows.Forms.Label lblStatus;
        private System.Windows.Forms.Button btnCancelAll;
        private System.Windows.Forms.ContextMenuStrip cmsContext;
        private System.Windows.Forms.ToolStripMenuItem 查看未完成订单ToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem 取消未完成订单ToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem 查看已完成订单ToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem 打开12306网站ToolStripMenuItem;
        private System.Windows.Forms.ToolStripSeparator toolStripSeparator2;
        private System.Windows.Forms.ToolStripSeparator toolStripSeparator1;
        private System.ComponentModel.BackgroundWorker bgCancelAll;
        private System.Windows.Forms.ToolStripMenuItem 复制登录信息ToolStripMenuItem;
        private System.Windows.Forms.Label lblCount;
        private System.Windows.Forms.ToolStripMenuItem cmsDelte;
        private System.Windows.Forms.ToolStripMenuItem 重新登录RToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem 验证账户状态ToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem 检查状态ToolStripMenuItem;
        private System.Windows.Forms.SaveFileDialog save;
        private System.Windows.Forms.DataGridViewTextBoxColumn dataGridViewTextBoxColumn1;
        private System.Windows.Forms.DataGridViewTextBoxColumn dataGridViewTextBoxColumn2;
        private System.Windows.Forms.DataGridViewTextBoxColumn 实名制;
        private System.Windows.Forms.ToolStripMenuItem tsmCopy;
        private System.Windows.Forms.Button btnAll;
        private System.Windows.Forms.ToolStripMenuItem 全部重新登陆ToolStripMenuItem;
    }
}