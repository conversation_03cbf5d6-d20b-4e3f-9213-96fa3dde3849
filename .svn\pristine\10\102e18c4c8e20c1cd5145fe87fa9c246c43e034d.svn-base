﻿using CommonLib;
using System;
using System.IO;

namespace HanZiOcr
{
    /// <summary>
    /// 搜狗图片
    /// </summary>
    public class WeiXinLiteAppRec : BaseOcrRec
    {
        public WeiXinLiteAppRec()
        {
            OcrGroup = OcrGroupType.腾讯;
            OcrType = HanZiOcrType.WeChatLite;
            MaxExecPerTime = 22;

            LstJsonPreProcessArray = new System.Collections.Generic.List<object>() { "ocrcomm_res", "items" };
            StrResultJsonSpilt = "text";
            IsSupportVertical = true;
            LstVerticalLocation = new System.Collections.Generic.List<object>() { "pos" };
        }

        protected override string GetHtml(OcrContent content)
        {
            var byt = Convert.FromBase64String(content.strBase64);
            var result = PostFileResult(byt).Replace("\\\"", "\"").Replace("\\r", "\r").Replace("\\n", "\n");
            //"{\"code\":0,\"msg\":\"成功\",\"data\":\"[{\\\"confidence\\\":1,\\\"word\\\":[{\\\"content\\\":\\\"请点击下图中所有的靴子\\\"},{\\\"content\\\":\\\"火柴\\\"}]}]\",\"content\":null,\"audioPath\":null}"
            return result;
        }

        private string PostFileResult(byte[] content)
        {
            var result = "";
            try
            {
                var url = "https://mp.weixin.qq.com/wxamusic/apidebug_imagequery?action=ocr_comm";
                var file = new UploadFileInfo()
                {
                    Name = "file",
                    Filename = "test.png",
                    ContentType = "image/png",
                    Stream = new MemoryStream(content)
                };
                result = PostFile(url, new[] { file }, null);
            }
            catch (Exception)
            {

            }
            return result;
        }

    }
}