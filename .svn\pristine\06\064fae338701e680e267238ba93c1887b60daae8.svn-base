﻿using System;
namespace NewTicket
{
    public sealed class EmptyAgileLogger : IAgileLogger
    {
        public void Log(Exception exception_0, string string_0, ErrorLevel enum1_0)
        {
        }

        public void Log(string string_0, string string_1, string string_2, ErrorLevel enum1_0)
        {
        }

        public void LogSimple(Exception exception_0, string string_0, ErrorLevel enum1_0)
        {
        }

        public void LogWithTime(string string_0)
        {
        }

        public bool Enabled
        {
            set
            {
            }
        }
    }

}