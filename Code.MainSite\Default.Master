﻿<%@ Master Language="C#" AutoEventWireup="true" CodeBehind="Default.master.cs" Inherits="Account.Web.Default" %>

<!DOCTYPE html>

<html>
<head runat="server">
    <meta http-equiv="X-UA-Compatible" content="IE=Edge,chrome=1"/>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <script type="text/javascript" src="https://scm-file-new-**********.cos.ap-beijing.myqcloud.com/f0481b1f-3226-415a-bcc4-9b28e2fb0f9f/static/v1/js/log.min.js"></script>
    <script type="text/javascript" src="https://scm-file-new-**********.cos.ap-beijing.myqcloud.com/f0481b1f-3226-415a-bcc4-9b28e2fb0f9f/static/v1/js/common_sdk.min.js"></script>
    <script type="text/javascript" src="https://scm-file-new-**********.cos.ap-beijing.myqcloud.com/f0481b1f-3226-415a-bcc4-9b28e2fb0f9f/static/v1/js/log.min.js"></script>
    <link rel="stylesheet" type="text/css" href="https://scm-file-new-**********.cos.ap-beijing.myqcloud.com/f0481b1f-3226-415a-bcc4-9b28e2fb0f9f/static/v1/css/common_new.css?1=1">
    <link rel="stylesheet" type="text/css" href="https://scm-file-new-**********.cos.ap-beijing.myqcloud.com/f0481b1f-3226-415a-bcc4-9b28e2fb0f9f/static/v1/css/index_new.css">
    <title>OCR文字识别助手_致力提升您的工作效率！</title>
    <meta name="keywords" content="文字识别，图片识别，识别图片文字，图片转文字，图片文字识别，文字识别软件，在线文字识别，在线OCR，OCR识别，OCR，名片识别，证件识别，文档识别，OCR助手，OCR文字识别助手">
    <link rel="stylesheet" href="https://scm-file-new-**********.cos.ap-beijing.myqcloud.com/f0481b1f-3226-415a-bcc4-9b28e2fb0f9f/static/v1/css/style.1545014209769.css" type="text/css" media='all'>
    <link rel="stylesheet" href="https://scm-file-new-**********.cos.ap-beijing.myqcloud.com/f0481b1f-3226-415a-bcc4-9b28e2fb0f9f/static/v1/css/styles.css" type="text/css" media='all'>
    <script type="text/javascript" src="https://scm-file-new-**********.cos.ap-beijing.myqcloud.com/f0481b1f-3226-415a-bcc4-9b28e2fb0f9f/static/v1/js/jquery1.42.min.js"></script>
    <script type="text/javascript" src="https://scm-file-new-**********.cos.ap-beijing.myqcloud.com/f0481b1f-3226-415a-bcc4-9b28e2fb0f9f/static/v1/js/body.1509421810036.js"></script>
    <script type="text/javascript" src="https://scm-file-new-**********.cos.ap-beijing.myqcloud.com/f0481b1f-3226-415a-bcc4-9b28e2fb0f9f/static/v1/js/jquery.SuperSlide.2.1.1.js"></script>
    <asp:ContentPlaceHolder ID="head" runat="server">
    </asp:ContentPlaceHolder>
</head>
<body>

    <div id="header">
        <div class="header-containner page-containner">
            <a href="Default.aspx" class="logo"></a>
            <ul class="nav">
                <li class="nav-item" id="homeNav"><a href="Default.aspx">首页</a></li>
                <li class="nav-item" id="aiNav">
                    <a href="Detail.aspx">AI智能文字识别</a>
                    <div class="sub-menu sub-menu--280">
                        <a class="sub-menu-item sub-menu-item--w25" href="javascript:;">
                            <div class="sub-menu-item-title">通用文字识别</div>
                            <div class="sub-menu-item-desc">多场景、多语种、高精度的整图文字检测和识别服务，可识别各类印刷和文档</div>
                        </a>
                        <a class="sub-menu-item sub-menu-item--w25" href="javascript:;">
                            <div class="sub-menu-item-title">手写文字识别</div>
                            <div class="sub-menu-item-desc">支持对手写中文、手写数字进行检测和识别，识别准确率可达90%以上</div>
                        </a>
                        <a class="sub-menu-item sub-menu-item--w25" href="javascript:;">
                            <div class="sub-menu-item-title">卡证文字识别</div>
                            <div class="sub-menu-item-desc">分类、识别、结构化输出22种票据，含增值税发票、行程单、出租车发票、火车票等</div>
                        </a>
                        <a class="sub-menu-item sub-menu-item--w25" href="javascript:;">
                            <div class="sub-menu-item-title">数学公式识别</div>
                            <div class="sub-menu-item-desc">识别教育场景所涉及的作业及试卷中公式、手写文字、题目等内容，可用于智能阅卷、搜题</div>
                        </a>
                        <a class="sub-menu-item sub-menu-item--w25" href="javascript:;">
                            <div class="sub-menu-item-title">表格文档识别</div>
                            <div class="sub-menu-item-desc">对图片中的表格文字内容进行提取和识别，支持识别完整框线/无框线表格，合并单元格等</div>
                        </a>
                        <a class="sub-menu-item sub-menu-item--w25" href="javascript:;">
                            <div class="sub-menu-item-title">划词/取词翻译</div>
                            <div class="sub-menu-item-desc">支持将文本内容翻译成用户选择的目标语言</div>
                        </a>
                        <a class="sub-menu-item sub-menu-item--w25" href="javascript:;">
                            <div class="sub-menu-item-title">文档/图片翻译</div>
                            <div class="sub-menu-item-desc">支持对图片/文档中的文本内容OCR识别并翻译成目标语言，可实现Word/PPT/Excel/PDF等的全文翻译</div>
                        </a>
                        <div class="sub-menu-item sub-menu-item--w25" style="background: #fff; cursor: unset"></div>
                        <div class="sub-menu-item sub-menu-item--w25" style="background: #fff; cursor: unset"></div>
                    </div>
                </li>
                <li class="nav-item" id="solutionNav">
                    <a href="Status.aspx">服务状态</a>
                </li>
                <li class="nav-item" id="heheNav">
                    <a href="javascript:;">技术分享</a>
                    <div class="sub-menu sub-menu--140">
                        <a class="sub-menu-item sub-menu-item--w25" href="javascript:;">
                            <div class="sub-menu-item-title">OCR识别有技术含量吗</div>
                            <div class="sub-menu-item-desc">
                                <p>做OCR软件，就是一个接口调用的事？</p>
                            </div>
                        </a>
                    </div>
                </li>
            </ul>
            <div class="language">
                <a style="color: #fff" class="language-current">中文简体 <span class="icon-down"></span></a>
                <div class="sub-language" style="width: 100px;">
                    <div class="sub-language-item"><a href="javascript:;">中文简体</a></div>
                    <div class="sub-language-item"><a href="javascript:;">English</a></div>
                </div>
            </div>
        </div>
    </div>

    <!-- 用户咨询 -->
    <div id="sidebar">
        <div class="consult">
            <div class="consult-icon-chat"></div>
            <div class="consult-icon-text">联系我们</div>
            <div class="consult-content-containner">
                <div class="consult-content">
                    <div class="consult-item">
                        <div class="consult-item-title">客服邮箱</div>
                        <div class="consult-item-value consult-item-value--flex">
                            <div class="consult-icon-email"></div>
                            <div class="consult-email"><a href="mailto:<EMAIL>"><EMAIL></a></div>
                        </div>
                    </div>
                    <div class="consult-item">
                        <div class="consult-item-title">联系我们</div>
                        <div class="consult-item-value">
                            <div class="consult-link"><a target="_blank" href="http://wpa.qq.com/msgrd?v=3&uin=365833440&site=qq&menu=yes">QQ客服(365833440)</a></div>
                            <div class="consult-link"><a target="_blank" href="https://qm.qq.com/cgi-bin/qm/qr?k=bohNy9GMdgD5bsYrB-fcIjy-s6Ot3zOg&jump_from=webapi&qr=1">QQ群(100029010)</a></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 返回顶部 -->
        <div class="return-top">
            <div class="icon-return"></div>
        </div>
    </div>
    <!-- banner -->
    <form id="form1" runat="server">
        <div>
            <asp:ContentPlaceHolder ID="ContentPlaceHolder1" runat="server">
            </asp:ContentPlaceHolder>
        </div>
    </form>

    <div id="footer">
        <div class="page-containner" style="text-align: center;">
            <div class="footer-nav">
                <div class="footer-left">
                    <div class="footer-column">
                        <div class="footer-column-title">OCR助手-AI智能识别，一站式服务平台
                        </div>
                    </div>
                </div>
            </div>

        </div>
        <div class="footer-ohter" style="text-align: center;">
            <div>
                <span class="footer-info">Copyright @ <%=DateTime.Now.Year %> <b>OCR助手</b> 保留所有权利</span>
                <a class="keep-record" href="https://beian.miit.gov.cn/" target="_blank">鄂ICP备2021012692号</a> <span>法律声明</span>
            </div>
        </div>
    </div>
    <script type="text/javascript" src="https://scm-file-new-**********.cos.ap-beijing.myqcloud.com/f0481b1f-3226-415a-bcc4-9b28e2fb0f9f/static/v1/js/wp-embed.min.js"></script>
    <script type="text/javascript" src="https://scm-file-new-**********.cos.ap-beijing.myqcloud.com/f0481b1f-3226-415a-bcc4-9b28e2fb0f9f/static/v1/js/common_new.js"></script>
</body>
</html>
