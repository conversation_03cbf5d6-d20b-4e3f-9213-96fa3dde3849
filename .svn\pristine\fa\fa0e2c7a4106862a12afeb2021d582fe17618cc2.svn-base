﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="12.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{FC03A7D4-8EF2-4DEA-A15A-C099EB77B0EB}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>CommonLib</RootNamespace>
    <AssemblyName>CommonLib</AssemblyName>
    <TargetFrameworkVersion>v4.7.2</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <TargetFrameworkProfile />
    <SolutionDir Condition="$(SolutionDir) == '' Or $(SolutionDir) == '*Undefined*'">..\</SolutionDir>
    <RestorePackages>true</RestorePackages>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="Aliyun.OSS, Version=2.13.0.0, Culture=neutral, PublicKeyToken=0ad4175f0dac0b9b, processorArchitecture=MSIL">
      <HintPath>..\packages\Aliyun.OSS.SDK.2.13.0\lib\net45\Aliyun.OSS.dll</HintPath>
    </Reference>
    <Reference Include="log4net, Version=1.2.10.0, Culture=neutral, PublicKeyToken=692fbea5521e1304, processorArchitecture=AMD64">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\DLL\log4net.dll</HintPath>
    </Reference>
    <Reference Include="Newtonsoft.Json, Version=13.0.0.0, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed, processorArchitecture=MSIL">
      <HintPath>..\packages\Newtonsoft.Json.13.0.1\lib\net45\Newtonsoft.Json.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Configuration" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Net" />
    <Reference Include="System.ValueTuple, Version=4.0.3.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.ValueTuple.4.5.0\lib\net47\System.ValueTuple.dll</HintPath>
    </Reference>
    <Reference Include="System.Web" />
    <Reference Include="System.Web.Extensions" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="AccountHelper.cs" />
    <Compile Include="ApiCountCacheHelper.cs" />
    <Compile Include="ApplicationTypeHelper.cs" />
    <Compile Include="BadApiException.cs" />
    <Compile Include="BaiMiaoProcessHelper.cs" />
    <Compile Include="BaseRec.cs" />
    <Compile Include="BaseRecHelper.cs" />
    <Compile Include="BoxUtil.cs" />
    <Compile Include="CodeProcessHelper.cs" />
    <Compile Include="CommonCompress.cs" />
    <Compile Include="CommonEncryptHelper.cs" />
    <Compile Include="CommonHelper.cs" />
    <Compile Include="CommonStyle.cs" />
    <Compile Include="ConfigHelper.cs" />
    <Compile Include="CusFileStatusEntity.cs" />
    <Compile Include="CusImageEntity.cs" />
    <Compile Include="HanWangHelper.cs" />
    <Compile Include="DisposeEmailHelper.cs" />
    <Compile Include="LocalMemoryCache.cs" />
    <Compile Include="LocalWaitLock.cs" />
    <Compile Include="LocalWaitCache.cs" />
    <Compile Include="NoticeQueueEntity.cs" />
    <Compile Include="RequestFlagConst.cs" />
    <Compile Include="MathResultHtmlHelper.cs" />
    <Compile Include="FileResultHtmlHelper.cs" />
    <Compile Include="HtmlHelper.cs" />
    <Compile Include="Events\OnCompletedEventArgs.cs" />
    <Compile Include="Events\OnErrorEventArgs.cs" />
    <Compile Include="Events\OnStartedEventArgs.cs" />
    <Compile Include="ListExtensions.cs" />
    <Compile Include="OcrContent.cs" />
    <Compile Include="JsonResultProcess.cs" />
    <Compile Include="OcrHtmlProcess.cs" />
    <Compile Include="OcrLineProcess.cs" />
    <Compile Include="RefCount.cs" />
    <Compile Include="ServerInfo.cs" />
    <Compile Include="ServerUuidUtil.cs" />
    <Compile Include="TaskEx.cs" />
    <Compile Include="TencentCloudSignHelper.cs" />
    <Compile Include="TextContentInfo.cs" />
    <Compile Include="TransLanguageTypeEnum.cs" />
    <Compile Include="UserConfig\TipMsgConfigurationSectionHandler.cs" />
    <Compile Include="UserConfig\ObjectItemConfigurationSectionHandler.cs" />
    <Compile Include="UserConfig\ChargeTypeConfigurationSectionHandler.cs" />
    <Compile Include="UserCodeRecordCache.cs" />
    <Compile Include="UserLoginCache.cs" />
    <Compile Include="PropsContractResolver.cs" />
    <Compile Include="DES.cs" />
    <Compile Include="DnsHelper.cs" />
    <Compile Include="ImageEntity.cs" />
    <Compile Include="ImageType.cs" />
    <Compile Include="IPCacheInfo.cs" />
    <Compile Include="IPLimitManager.cs" />
    <Compile Include="LogHelper.cs" />
    <Compile Include="MemoryManager.cs" />
    <Compile Include="SNtpClient.cs" />
    <Compile Include="ProcessState.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="RDSCacheHelper.cs" />
    <Compile Include="ServerTime.cs" />
    <Compile Include="StringExtension.cs" />
    <Compile Include="UploadFile.cs" />
    <Compile Include="UserType.cs" />
    <Compile Include="WebClientSyncExt.cs" />
    <Compile Include="YouDaoAccount.cs" />
  </ItemGroup>
  <ItemGroup>
    <None Include="app.config" />
    <None Include="packages.config" />
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <Import Project="$(SolutionDir)\.nuget\NuGet.targets" Condition="Exists('$(SolutionDir)\.nuget\NuGet.targets')" />
  <Target Name="EnsureNuGetPackageBuildImports" BeforeTargets="PrepareForBuild">
    <PropertyGroup>
      <ErrorText>This project references NuGet package(s) that are missing on this computer. Enable NuGet Package Restore to download them.  For more information, see http://go.microsoft.com/fwlink/?LinkID=322105. The missing file is {0}.</ErrorText>
    </PropertyGroup>
    <Error Condition="!Exists('$(SolutionDir)\.nuget\NuGet.targets')" Text="$([System.String]::Format('$(ErrorText)', '$(SolutionDir)\.nuget\NuGet.targets'))" />
  </Target>
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>