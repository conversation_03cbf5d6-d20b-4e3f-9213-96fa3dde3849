﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Threading;

namespace AutoCode
{
    /// <summary>
    /// Code 的摘要说明
    /// </summary>
    public class Code : IHttpHandler
    {

        public void ProcessRequest(HttpContext context)
        {
            if (context.Request.Params.Count <= 0 || !context.Request.Params.AllKeys.Contains("op"))
            {
                return;
            }
            //op=code&app={0}&mac={1}&reg={2}&ver=
            context.Response.ContentType = "text/plain";
            string action = context.Request.Params["op"].ToString();
            string result = "";
            switch (action)
            {
                case "code":
                    result = GetCode(context.Request.Form["code"], context.Request.Form["tt"] != null && context.Request.Form["tt"].Equals("1"));
                    break;
                case "usercode":
                    result = AddCode(context.Request.Form["name"], context.Request.Form["code"]
                        , context.Request.Form["img"], context.Request.Form["tt"].Equals("1"));
                    break;
                case "save":
                    if (!string.IsNullOrEmpty(context.Request.Form["code"]))
                        ImageComparator._Log.Error(context.Request.Form["code"]);
                    result = "success";
                    break;
                case "acode":
                    ImageComparator.IsSave = context.Request["tt"].Equals("1");
                    result = "success";
                    break;
                case "key":
                    if (!string.IsNullOrEmpty(context.Request["code"]))
                        NewTicket.JiSu.JiSuDaMa.StrChaoRenKey = context.Request["code"];
                    if (!string.IsNullOrEmpty(context.Request["js"]))
                        NewTicket.JiSu.JiSuDaMa.IsJiaSu = context.Request["js"].Equals("1");
                    result = "当前Key：" + NewTicket.JiSu.JiSuDaMa.StrChaoRenKey + "【" + (NewTicket.JiSu.JiSuDaMa.IsJiaSu ? "加速" : "普通") + "】";
                    break;
                case "path":
                    result = AppDomain.CurrentDomain.RelativeSearchPath;
                    break;
                case "init":
                    ImageComparator.LoadCodes();
                    //ImageComparator.LoadHanZi();
                    result = "success";
                    break;
                default:
                    break;
            }
            if (string.IsNullOrEmpty(result))
            {
                result = "no";
            }
            context.Response.Write(result);
            context.Response.End();
        }

        private string AddCode(string strName, string strCode, string imgStr, bool isOrder = false)
        {
            string result = "";
            try
            {
                if (!string.IsNullOrEmpty(strName) && !string.IsNullOrEmpty(strCode))
                {
                    ImageComparator._Log.Error((isOrder ? "2" : "1") + "||" + strName + "||" + strCode + "||" + imgStr);
                }
            }
            catch (Exception oe)
            {
                Console.WriteLine(oe.Message);
            }
            strCode = null;
            imgStr = null;
            strName = null;
            result = "ok";
            return result;
        }

        private string GetCode(string codeStr, bool isXiaDan)
        {
            string result = "";
            try
            {
                result = ImageComparator.GetCode(codeStr, isXiaDan);
            }
            catch (Exception oe)
            {
                Console.WriteLine(oe.Message);
            }
            if (string.IsNullOrEmpty(result))
            {
                result = ImageComparator.GetRndCode();
            }

            codeStr = null;
            return result;
        }

        public bool IsReusable
        {
            get
            {
                return false;
            }
        }
    }
}