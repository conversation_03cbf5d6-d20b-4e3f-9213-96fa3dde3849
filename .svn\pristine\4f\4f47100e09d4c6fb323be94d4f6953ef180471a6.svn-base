﻿<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <TargetFrameworks>net472;net6.0</TargetFrameworks>
    <DebugType>portable</DebugType>
    <AssemblyName>ServiceStack.Text.Tests</AssemblyName>
    <OutputType>Library</OutputType>
    <PackageId>ServiceStack.Text.Tests</PackageId>
    <GenerateAssemblyTitleAttribute>false</GenerateAssemblyTitleAttribute>
    <GenerateAssemblyDescriptionAttribute>false</GenerateAssemblyDescriptionAttribute>
    <GenerateAssemblyConfigurationAttribute>false</GenerateAssemblyConfigurationAttribute>
    <GenerateAssemblyCompanyAttribute>false</GenerateAssemblyCompanyAttribute>
    <GenerateAssemblyProductAttribute>false</GenerateAssemblyProductAttribute>
    <GenerateAssemblyCopyrightAttribute>false</GenerateAssemblyCopyrightAttribute>
    <GenerateAssemblyVersionAttribute>false</GenerateAssemblyVersionAttribute>
    <GenerateAssemblyFileVersionAttribute>false</GenerateAssemblyFileVersionAttribute>
    <LangVersion>latest</LangVersion>
    <NoWarn>1591</NoWarn>
  </PropertyGroup>
  <PropertyGroup>
    <DebugType Condition="'$(TargetFramework)' != '' AND '$(TargetFramework)' != 'net6.0'">Full</DebugType>
  </PropertyGroup>
  <ItemGroup>
    <ProjectReference Include="..\..\..\ServiceStack\src\ServiceStack.Client\ServiceStack.Client.csproj" />
    <ProjectReference Include="..\..\..\ServiceStack\src\ServiceStack.Common\ServiceStack.Common.csproj" />
    <ProjectReference Include="..\..\..\ServiceStack\src\ServiceStack\ServiceStack.csproj" />
    <ProjectReference Include="..\..\src\ServiceStack.Text\ServiceStack.Text.csproj" />
    <ProjectReference Include="..\Northwind.Common\Northwind.Common.csproj" />
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="NUnit3TestAdapter" Version="4.1.0" />
    <PackageReference Include="Microsoft.NET.Test.Sdk" Version="17.0.0" />
    <PackageReference Include="NUnit" Version="3.13.2" />
    <PackageReference Include="protobuf-net" Version="3.0.101" />
    <PackageReference Include="System.ComponentModel.TypeConverter" Version="4.3.0" />
    <PackageReference Include="System.Drawing.Primitives" Version="4.3.0" />
    <PackageReference Include="System.Reflection.Extensions" Version="4.3.0" />
    <PackageReference Include="System.Runtime" Version="4.3.1" />
    <PackageReference Include="System.Runtime.Serialization.Json" Version="4.3.0" />
    <PackageReference Include="System.ValueTuple" Version="4.5.0" />
    <PackageReference Include="System.Memory" Version="4.5.4" />
  </ItemGroup>
  <PropertyGroup Condition=" '$(TargetFramework)' == 'net472' ">
    <DefineConstants>$(DefineConstants);NETFX</DefineConstants>
  </PropertyGroup>
  <ItemGroup Condition=" '$(TargetFramework)' == 'net472' ">
    <Reference Include="System.Configuration" />
    <Reference Include="System.Threading" />
    <Reference Include="System.Threading.Tasks" />
    <Reference Include="System.Runtime.Serialization" />
    <Reference Include="System.ComponentModel.DataAnnotations" />
    <Reference Include="System.Web" />
    <Reference Include="System.Web.Extensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Net.Http" />
  </ItemGroup>
  <PropertyGroup Condition=" '$(TargetFramework)' == 'net6.0' ">
    <DefineConstants>$(DefineConstants);NETCORE;NETSTANDARD2_0</DefineConstants>
  </PropertyGroup>
  <ItemGroup Condition=" '$(TargetFramework)' == 'net6.0' ">
    <PackageReference Include="System.Collections.Specialized" Version="4.*" />
    <PackageReference Include="System.Collections.NonGeneric" Version="4.*" />
    <PackageReference Include="System.Diagnostics.TraceSource" Version="4.*" />
  </ItemGroup>
  <ItemGroup>
    <Service Include="{82a7f48d-3b50-4b1e-b82e-3ada8210c358}" />
  </ItemGroup>
</Project>