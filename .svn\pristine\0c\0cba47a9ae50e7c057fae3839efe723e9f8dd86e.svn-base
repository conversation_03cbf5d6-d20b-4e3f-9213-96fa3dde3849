﻿
using CommonLib;
using System;

namespace TableOcr
{
    /// <summary>
    /// 百度AI 演示Demo
    /// https://ai.baidu.com/tech/ocr/webimage
    /// </summary>
    public class BaiDuAIRec : BaseTableRec
    {
        public BaiDuAIRec()
        {
            OrcType = TableOcrType.百度AI;
            MaxExecPerTime = 10;

            IsJsonResult = true;
            LstJsonPreProcessArray = new System.Collections.Generic.List<string>() { "words_result" };
            StrResultJsonSpilt = "words";
            IsSupportUrlOcr = true;
        }

        protected override string GetHtml(string strBase64)
        {
            strBase64 = "data%3Aimage%2Fjpeg%3Bbase64%2C" + System.Web.HttpUtility.UrlEncode(strBase64);
            return RequestHtmlContent(strBase64);
        }

        public override string GetHtmlByUrl(string url)
        {
            return RequestHtmlContent(null, url);
        }

        private static string RequestHtmlContent(string strBase64, string imgUrl = null)
        {
            var op = "https%3A%2F%2Faip.baidubce.com%2Frest%2F2.0%2Focr%2Fv1%2Fform";
            var strPost = "type=" + op + "&image=" + strBase64 + "&image_url=" + System.Web.HttpUtility.UrlEncode(imgUrl) + "&detect_direction=true";

            var cookie = "BDAIUID=" + Guid.NewGuid().ToString().ToUpper().Replace("-", "")
                + ";BDAUIDD=" + Guid.NewGuid().ToString().ToUpper().Replace("-", "")
                + ";BAIDUID=" + Guid.NewGuid().ToString().ToUpper().Replace("-", "") + ":FG=1;max-age=31536000;version=1;";

            WebClientSyncExt.GetHtml("https://ai.baidu.com/tech/ocr_others/table", ref cookie);

            var strTmp = WebClientSyncExt.GetHtml("http://ai.baidu.com/aidemo", ref cookie, "", strPost, "http://ai.baidu.com/tech/ocr", 10);

            return strTmp;
        }
    }
}