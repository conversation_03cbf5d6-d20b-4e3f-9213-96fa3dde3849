﻿using CommonLib;
using System;
using System.Collections.Generic;
using System.Collections.Specialized;
using System.Diagnostics;
using System.IO;
using System.Net;
using System.Text;
using System.Web;

namespace DocOcr
{
    public class XunJieTransRec : BaseDocOcrRec
    {
        public XunJieTransRec()
        {
            OcrGroup = OcrGroupType.迅捷;
            OcrType = DocOcrType.迅捷翻译;
            ResultType = ResutypeEnum.网页;
            MaxExecPerTime = 10;
            IsDownLoadFile = true;
            //文件上限2M
            FileSizeLimit = 1024 * 1024 * 2;
            AllowUploadFileTypes = new List<string>() { "pdf", "doc", "docx", "xls", "xlsx", "ppt", "pptx" };//, "txt"
            InitLanguage();
            IsSupportTrans = true;

        }

        #region 支持的语言

        //zh-CHS:简体中文
        //en:英文
        //zh-CHT:繁体中文
        //ja:日语
        //ko:朝鲜语
        //fr:法语
        //es:西班牙语
        //th:泰语
        //ar:阿拉伯语
        //ru:俄语
        //pt:葡萄牙语
        //de:德语
        //it:意大利语
        //el:希腊语
        //nl:荷兰语
        //pl:波兰语
        //bg:保加利亚语
        //et:爱沙尼亚语
        //da:丹麦语
        //fi:芬兰语
        //cs:捷克语
        //ro:罗马尼亚语
        //sl:斯洛文尼亚语
        //sv:瑞典语
        //hu:匈牙利语
        //vi:越南语

        private void InitLanguage()
        {
            TransLanguageDic = new Dictionary<TransLanguageTypeEnum, string>();
            TransLanguageDic.Add(TransLanguageTypeEnum.自动, "auto");
            TransLanguageDic.Add(TransLanguageTypeEnum.中文, "zh-CHS");
            TransLanguageDic.Add(TransLanguageTypeEnum.英文, "en");
            TransLanguageDic.Add(TransLanguageTypeEnum.繁体中文, "zh-CHT");
            TransLanguageDic.Add(TransLanguageTypeEnum.日语, "ja");
            TransLanguageDic.Add(TransLanguageTypeEnum.朝鲜语, "ko");
            TransLanguageDic.Add(TransLanguageTypeEnum.法语, "fr");
            TransLanguageDic.Add(TransLanguageTypeEnum.泰语, "th");
            TransLanguageDic.Add(TransLanguageTypeEnum.阿拉伯语, "ar");
            TransLanguageDic.Add(TransLanguageTypeEnum.俄语, "ru");
            TransLanguageDic.Add(TransLanguageTypeEnum.葡萄牙语, "pt");
            TransLanguageDic.Add(TransLanguageTypeEnum.德语, "de");
            TransLanguageDic.Add(TransLanguageTypeEnum.意大利语, "it");
            TransLanguageDic.Add(TransLanguageTypeEnum.希腊语, "el");
            TransLanguageDic.Add(TransLanguageTypeEnum.荷兰语, "nl");
            TransLanguageDic.Add(TransLanguageTypeEnum.波兰语, "pl");
            TransLanguageDic.Add(TransLanguageTypeEnum.保加利亚语, "bg");
            TransLanguageDic.Add(TransLanguageTypeEnum.爱沙尼亚语, "et");
            TransLanguageDic.Add(TransLanguageTypeEnum.丹麦语, "da");
            TransLanguageDic.Add(TransLanguageTypeEnum.芬兰语, "fi");
            TransLanguageDic.Add(TransLanguageTypeEnum.捷克语, "cs");
            TransLanguageDic.Add(TransLanguageTypeEnum.罗马尼亚语, "ro");
            TransLanguageDic.Add(TransLanguageTypeEnum.斯洛文尼亚语, "sl");
            TransLanguageDic.Add(TransLanguageTypeEnum.瑞典语, "sv");
            TransLanguageDic.Add(TransLanguageTypeEnum.匈牙利语, "hu");
            TransLanguageDic.Add(TransLanguageTypeEnum.越南语, "vi");
        }

        #endregion

        private const string strUploadUrl = "https://app.xunjiepdf.com/api/Upload?tasktype=fanyi&phonenumber=&loginkey=&machineid={0}&token={1}&limitsize=20480&pdfname=1.{6}&queuekey={2}&uploadtime=&filecount=1&fileindex=1&pagerange=all&picturequality=&outputfileextension={3}&picturerotate=0%2Cundefined&filesequence=0%2Cundefined&filepwd=&iconsize=&picturetoonepdf=&isshare=0&softname=pdfonlineconverter&softversion=V5.0&validpagescount=2000&limituse=1&filespwdlist=&fileCountwater=0&languagefrom={4}&languageto={5}&cadverchose=&pictureforecolor=&picturebackcolor=&parainfo=maxlimit%3Achar2000page0&id=WU_FILE_0&name=1.{6}&type={7}&lastModifiedDate=&size=";

        private string GetNewId(ref string macId, ref string token)
        {
            var result = "";
            macId = Guid.NewGuid().ToString().Replace("-", "").ToLower();
            var html = WebClientSyncExt.GetHtml("https://app.xunjiepdf.com/api/producetoken", "machineid=" + macId, ExecTimeOutSeconds);
            //{"code":10000,"guid":"403c2a192aec44d1b159edfd3e2db1ba","token":"03dfc627fcf95d659041447f3de052c9"}
            if (html.Contains("\"guid\":\""))
            {
                html = html.Substring(html.IndexOf("\"guid\":\"") + "\"guid\":\"".Length);
                result = html.Substring(0, html.IndexOf("\""));
                html = html.Substring(html.IndexOf("\"token\":\"") + "\"token\":\"".Length);
                token = html.Substring(0, html.IndexOf("\""));
            }
            return result;
        }

        protected override string GetHtml(OcrContent content)
        {
            var result = "";
            var macId = "";
            var token = "";

            string from = "", to = "";
            InitLanguage(content, ref from, ref to);

            //var from = "zh-CHS";
            //var to = "en";
            var fileExt = content.fileExt;
            var guid = GetNewId(ref macId, ref token);
            var byt = Convert.FromBase64String(content.strBase64);
            if (content.fileExt.Equals("txt"))
            {
                ResultFileType = OcrFileType.Txt;
            }
            var tranFileType = ResultFileType == OcrFileType.PDF ? "pdf" : (ResultFileType == OcrFileType.Txt ? "txt" : "docx");
            var upLoadUrl = string.Format(strUploadUrl, macId, token, guid, tranFileType, from, to, fileExt, HttpUtility.UrlEncode(ApplicationTypeHelper.GetApplicationType(fileExt)));

            if (UploadPic(upLoadUrl, byt))
            {
                result = "{" +
                    string.Format("\"fileId\":\"{0}\",\"fileType\":\"{1}\",\"url\":\"{2}\""
                    , guid
                    , tranFileType
                    , string.Format("https://app.xunjiepdf.com/download/fileid/{0}", guid)) + "}";
            }
            return result;
        }

        private string PostFileResult(string taskId)
        {
            var result = "";
            try
            {
                var url = "http://app.xunjiepdf.com/api/progress";
                var values = new NameValueCollection {
                    { "tasktag", taskId },
                    { "limituse", "-1" } };
                var html = PostFile(url, null, values);

                if (!string.IsNullOrEmpty(html))
                {
                    result = CommonHelper.SubString(html, "\"message\":\"", "\"");
                    if (string.IsNullOrEmpty(result) && html.Contains(",\"downurl\":\"http"))
                    {
                        result = "成功";
                    }
                }
            }
            catch (Exception)
            {

            }
            return result;
        }

        private bool UploadPic(string url, byte[] b)
        {
            var result = false;
            var strTmpHtml = WebClientSyncExt.GetHtml(url, "", Convert.ToBase64String(b), "", 5, new NameValueCollection()
            {
                { "Content-Type","application/octet-stream"}
            });
            if (strTmpHtml.Contains("完成"))
            {
                result = true;
            }
            return result;
        }

        protected override ResultEntity GetProcessText(OcrContent content, string html)
        {
            //{"status":2
            //"is_preview_docx_ready":true,"is_preview_pdf_ready":true,"is_preview_uncomparison_docx_ready":true,"is_preview_uncomparison_pdf_ready":true
            //"is_full_docx_ready":true,"is_full_pdf_ready":true,"is_full_uncomparison_docx_ready":true,"is_full_uncomparison_pdf_ready":true
            //,"preview_status":2}
            //result = "1";
            var entity = GetFileResult(CommonHelper.SubString(html, "\"fileId\":\"", "\""));

            if (!string.IsNullOrEmpty(entity.autoText))
            {
                var fileType = CommonHelper.GetFileType(CommonHelper.SubString(html, "\"fileType\":\"", "\""));
                var file = new DownLoadInfo()
                {
                    fileType = fileType,
                    desc = "迅捷PDF-" + fileType.ToString(),
                    url = CommonHelper.SubString(html, "\"url\":\"", "\""),
                };
                if (!Equals(fileType, OcrFileType.Txt))
                {
                    entity.viewUrl = file.url;
                }
                entity.files.Add(file);
                //entity.downloadHtml = ConstHelper.GetDownLoadHtml(entity, OcrType.GetHashCode(), true);
            }
            return entity;
        }

        public override ProcessStateEntity QueryFileStatuMethod(string taskId)
        {
            var html = PostFileResult(taskId);
            var processStatus = new ProcessStateEntity()
            {
                state = OcrProcessState.未知状态,
                taskId = taskId
            };
            if (html.Contains("成功"))
            {
                processStatus.state = OcrProcessState.处理成功;
                processStatus.desc = "处理完毕，可以下载了！";
            }
            else if (html.Contains("正在处理"))
            {
                processStatus.state = OcrProcessState.处理中;
                processStatus.desc = string.Format("处理中，请稍后…", processStatus.privewPercent, processStatus.percent);
            }
            else if (html.Contains("未处理"))
            {
                processStatus.state = OcrProcessState.待处理;
                processStatus.desc = "排队中，请稍后…";
            }
            else if (html.Contains("失败"))
            {
                processStatus.state = OcrProcessState.处理失败;
                processStatus.desc = "处理失败，详细：" + html;
            }
            else if (!string.IsNullOrEmpty(html))
            {
                processStatus.desc = "处理结果：" + html;
            }
            else
            {
                Console.WriteLine("彩云翻译查询状态异常：" + html);
            }
            return processStatus;
        }
    }
}