﻿
using CommonLib;
using System.Collections.Generic;
using System.Collections.Specialized;

namespace MathOcr
{
    /// <summary>
    /// </summary>
    public class MathPixRec : BaseMathRec
    {
        public MathPixRec()
        {
            OcrType = MathOcrType.MathPix;
            OcrGroup = OcrGroupType.MathPix;

            MaxExecPerTime = 30;

            LstJsonPreProcessArray = new List<object>() { "latex" };
            IsProcessJsonResultByArray = false;
            IsSupportUrlOcr = true;
        }

        private List<string> lstAuthAccount = new List<string>() {
            "{\"email\":\"<EMAIL>\",\"password\":\"aa123456\"}",
            "{\"email\":\"<EMAIL>\",\"password\":\"123456\"}",
            "{\"email\":\"<EMAIL>\",\"password\":\"123456\"}",

            "{\"email\":\"<EMAIL>\",\"password\":\"123456\"}",
            "{\"email\":\"<EMAIL>\",\"password\":\"123456\"}",
            "{\"email\":\"<EMAIL>\",\"password\":\"123456\"}",

            "{\"email\":\"<EMAIL>\",\"password\":\"123456\"}",
            "{\"email\":\"<EMAIL>\",\"password\":\"123456\"}",
            "{\"email\":\"<EMAIL>\",\"password\":\"123456\"}",

            "{\"email\":\"<EMAIL>\",\"password\":\"123456\"}",
            "{\"email\":\"<EMAIL>\",\"password\":\"123456\"}",
            "{\"email\":\"<EMAIL>\",\"password\":\"123456\"}",

            "{\"email\":\"<EMAIL>\",\"password\":\"123456\"}",
            "{\"email\":\"<EMAIL>\",\"password\":\"123456\"}",
            "{\"email\":\"<EMAIL>\",\"password\":\"123456\"}",
        };

        //https://api.mathpix.com/v1/user/login
        /*

{"email": "<EMAIL>","password": "123456"}
{"message":"user_logged_in","token":"ZaGz-5ESPNwqdRLaI67efJmUnwOp3cwoZLc58tjk8FjOVnO4FdaeMFx0rIWel3GmLSeUm_ZCPjurhlqC0uaQig"}
 */
        string strSignSpilt = "\"token\":\"";
        string strBadAccount = "\":\"invalid_email\"";

        private string strToken = "";

        public void InitToken()
        {
            if (string.IsNullOrEmpty(strToken))
            {
                strToken = GetSign();
            }
        }

        public override void Reset()
        {
            strToken = "";
            base.Reset();
        }

        private string GetSign()
        {
            var strPost = lstAuthAccount.GetRndItem();
            var html = WebClientSyncExt.GetHtml("https://api.mathpix.com/v1/user/login", strPost, ExecTimeOutSeconds);
            var result = "";
            if (html.Contains(strBadAccount))
            {
                LogHelper.Log.Info("Bad MathPix Account：" + strPost);
                lstAuthAccount.Remove(strPost);
            }
            else
            {
                if (html.Contains(strSignSpilt))
                {
                    result = html.Substring(html.IndexOf(strSignSpilt) + strSignSpilt.Length);
                    result = result.Substring(0, result.IndexOf("\"")).Trim();
                    if (!string.IsNullOrEmpty(result))
                    {
                        result = "Bearer " + result;
                    }
                }
            }
            return result;
        }

        protected override string GetHtml(OcrContent content)
        {
            return GetResult(content.strBase64, null);
        }

        public override string GetHtmlByUrl(OcrContent content)
        {
            return GetResult(null, content.url);
        }

        private string GetResult(string strBase64, string imgUrl)
        {
            InitToken();
            if (!string.IsNullOrEmpty(strToken))
            {
                //1、
                string strPost = "{\"src\":\"" + (string.IsNullOrEmpty(imgUrl) ? "data:image/jpeg;base64," + strBase64 : imgUrl) + "\"}";
                NameValueCollection collection = new NameValueCollection() { { "Authorization", strToken } };
                var result = WebClientSyncExt.GetHtml("https://api.mathpix.com/v1/snips", strPost, ExecTimeOutSeconds, collection);
                if (string.IsNullOrEmpty(result))
                {
                    strToken = "";
                }
                return result;
            }
            return string.Empty;
        }

    }
}