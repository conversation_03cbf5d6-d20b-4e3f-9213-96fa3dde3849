﻿using System;
using System.Collections.Generic;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using CommonLib;

namespace Code.Client.Web
{
    public class BaiDuCode
    {
        public enum FixImageType
        {
            待整理 = 0,
            用户修正_OK = 1,
            用户修正_Error = 2,
            归档 = 3
        }

        //public static readonly ILog _Log;

        public static bool IsShowDaMa = false;

        private static readonly List<Point> lstPoint = new List<Point>
        {
            new Point(5, 41),
            new Point(5, 114),
            new Point(77, 41),
            new Point(77, 114),
            new Point(149, 41),
            new Point(149, 114),
            new Point(221, 41),
            new Point(221, 114)
        };

        private static Bitmap BlankImage;
        private static readonly Dictionary<string, LearnProcess> DicImgLearn = new Dictionary<string, LearnProcess>();

        public static RectangleF rtgHanZi = new RectangleF(120, 0, 170, 25); //像素矩阵

        //static BaiDuCode()
        //{
        //    _Log = LogManager.GetLogger("ImgLog");
        //}


        public static void DelDoubleHanZi(string dir, string old, string strDate)
        {
            if (string.IsNullOrEmpty(strDate))
            {
                strDate = ServerTime.DateTime.ToString("MMdd");
            }
            var path = ConfigHelper.StrImagePath + strDate + "\\图库\\" + dir;
            path = path.Replace("\\\\", "\\");
            if (Directory.Exists(path) && !(CommonHanZi.lstHanZi.Contains(dir) && old.Equals(dir)))
            {
                try
                {
                    var files = Directory.GetFiles(path);
                    foreach (var ff in files)
                    {
                        if (ff.Contains("_" + old + "_"))
                        {
                            File.Delete(ff);
                        }
                    }
                    files = Directory.GetFiles(path);
                    if (files.Length <= 0)
                        Directory.Delete(path);
                }
                catch (Exception oe)
                {
                    ConfigHelper._Log.Error("移动图片失败！", oe);
                }
            }
        }

        public static void DelHanZi(string old)
        {
            try
            {
                if (CommonHanZi.lstHanZiReplace.ContainsKey(old))
                {
                    CommonHanZi.lstHanZiReplace.Remove(old);
                }
            }
            catch (Exception oe)
            {
                ConfigHelper._Log.Error("保存汉字信息失败！", oe);
            }
        }

        public static void FixHanZi(string dir, string old, string newStr, string strDate)
        {
            if (!newStr.Contains(old))
            {
                if (string.IsNullOrEmpty(strDate))
                {
                    strDate = ServerTime.DateTime.ToString("MMdd");
                }
                try
                {
                    if (!CommonHanZi.lstHanZiReplace.ContainsKey(old) ||
                        (CommonHanZi.lstHanZiReplace.ContainsKey(old) &&
                         !CommonHanZi.lstHanZiReplace[old].Contains(newStr)))
                    {
                        CustomImgProcess.AddTmpRecHanZi(old, dir, newStr);
                        //lstHanZiReplace.Add(old, newStr);

                        //File.AppendAllText(AppDomain.CurrentDomain.BaseDirectory + "\\Config\\UserFix.txt", string.Format("{0},{1}|", old, newStr), Encoding.UTF8);
                    }
                }
                catch (Exception oe)
                {
                    ConfigHelper._Log.Error("保存汉字信息失败！", oe);
                }
                var path = ConfigHelper.StrImagePath + strDate + "\\图库\\" + dir;
                path = path.Replace("\\\\", "\\");
                if (Directory.Exists(path) && !dir.Equals(newStr))
                {
                    try
                    {
                        var files = Directory.GetFiles(path);
                        var newDir = ConfigHelper.StrImagePath + strDate + "\\图库\\" + newStr;
                        if (!Directory.Exists(newDir))
                            Directory.CreateDirectory(newDir);
                        foreach (var ff in files)
                        {
                            if (ff.Contains("_" + old + "_"))
                            {
                                //ConfigHelper._Log.InfoFormat("{0}->{1}", ff, ff.Replace("\\" + dir + "\\", "\\" + newStr + "\\"));
                                File.Move(ff, ff.Replace("\\" + dir + "\\", "\\" + newStr + "\\"));
                            }
                        }
                        files = Directory.GetFiles(path);
                        if (files.Length <= 0)
                            Directory.Delete(path);
                    }
                    catch (Exception oe)
                    {
                        ConfigHelper._Log.Error("移动图片失败！", oe);
                    }
                }
            }
        }

        public static string LoadErrorHanZi(string strDate, bool isLogin = true)
        {
            var strTmp = new StringBuilder();
            var count = 0;
            try
            {
                if (string.IsNullOrEmpty(strDate))
                {
                    strDate = ServerTime.DateTime.ToString("MMdd");
                }
                //Rec\New\1222\图库
                var path = ConfigHelper.StrImagePath + strDate + "\\图库\\";
                path = path.Replace("\\\\", "\\");
                var paths = Directory.GetDirectories(path);
                Parallel.ForEach(paths, new ParallelOptions { MaxDegreeOfParallelism = 30 }, item =>
                //foreach (var item in paths)
                {
                    var child = item.TrimEnd('\\').Replace(path, "");
                    //if (lstHanZi.Contains(child)
                    //if (!isOne || (isOne && child.Length > 1))// || (!isOne && child.Length <= 1))
                    //{
                    //    continue;
                    //}
                    var lstRes = new List<string>();
                    var files = Directory.GetFiles(item);
                    if (files.Length > 0)
                    {
                        try
                        {
                            if (CommonHanZi.lstHanZiReplace.ContainsKey(child))
                            {
                                var newDir = ConfigHelper.StrImagePath + strDate + "\\图库\\" +
                                             CommonHanZi.lstHanZiReplace[child];
                                if (!Directory.Exists(newDir))
                                    Directory.CreateDirectory(newDir);
                                foreach (var ff in files)
                                {
                                    File.Move(ff,
                                        ff.Replace("\\" + child + "\\", "\\" + CommonHanZi.lstHanZiReplace[child] + "\\"));
                                }
                                Directory.Delete(item);
                                return;
                            }
                            //if (lstTmpHanZiReplace.ContainsKey(child))
                            //{
                            //    var newDir = ConfigHelper.StrImagePath + DateTime.Now.ToString("MMdd") + "\\图库\\" + lstTmpHanZiReplace[child];
                            //    if (!Directory.Exists(newDir))
                            //        Directory.CreateDirectory(newDir);
                            //    foreach (var ff in files)
                            //    {
                            //        File.Move(ff, ff.Replace("\\" + child + "\\", "\\" + lstTmpHanZiReplace[child] + "\\"));
                            //    }
                            //    Directory.Delete(item);
                            //    return;
                            //}
                        }
                        catch (Exception oe)
                        {
                            Console.WriteLine(oe.Message);
                        }
                        //child:菠萝、猫
                        foreach (var file in files)
                        {
                            var newName = file.Substring(file.LastIndexOf("\\")).TrimStart('\\');
                            newName = newName.Substring(0, newName.IndexOf("."));
                            newName = newName.Substring(0, newName.LastIndexOf("_"));
                            var id = newName.Substring(0, newName.IndexOf("_"));
                            if ((isLogin && id.StartsWith("0")) || (!isLogin && id.StartsWith("1")))
                            {
                                newName = newName.Substring(newName.LastIndexOf("_") + 1);
                                if (child.Length == 1 && CommonHanZi.lstHanZi.Contains(child) && newName.Contains(child))
                                    continue;
                                //newName:澎猫
                                if (!isLogin ||
                                    (newName.Length < 5 && newName.Length > 1 &&
                                     CommonHanZi.lstHanZi.FindAll(p => p.StartsWith(newName)).Count <= 1))
                                {
                                    if (!CommonHanZi.lstHanZiReplace.ContainsKey(newName) &&
                                        (!isLogin ||
                                         !CommonHanZi.lstHanZi.Exists(
                                             p => p.Length > 1 && (p.Contains(newName) || newName.Contains(p)))))
                                    {
                                        //if(newName.Length-)
                                        //000015469_205_112_88_112_拿日_5.jpg
                                        var errorStr = newName + ",|";
                                        if (!lstRes.Exists(p => p.StartsWith(errorStr)))
                                        {
                                            count++;
                                            lstRes.Add(errorStr + "http://img.oldfish.cn/" + strDate + "/old/" + id +
                                                       ".jpg");
                                        }
                                    }
                                }
                            }
                        }
                    }
                    if (lstRes.Count > 0)
                    {
                        strTmp.AppendLine(string.Format("[{0}]共{1}个{2}{3}{2}", child, files.Length, Environment.NewLine,
                            string.Join(Environment.NewLine, lstRes.ToArray())));
                    }
                });
            }
            catch (Exception oe)
            {
                Console.WriteLine(oe.Message);
            }
            strTmp.Insert(0, string.Format("共【{0}】个{1}", count, Environment.NewLine));
            return strTmp.ToString();
        }

        public static string LoadErrorImg(string strDate)
        {
            var result = "";
            try
            {
                if (string.IsNullOrEmpty(strDate))
                {
                    strDate = ServerTime.DateTime.ToString("MMdd");
                }
                if (strDate.Length > 4)
                {
                    strDate = strDate.Substring(0, 4);
                }
                if (!DicImgLearn.ContainsKey(strDate))
                {
                    DicImgLearn.Add(strDate, new LearnProcess { ImageType = FixImageType.待整理 });
                }
                //Rec\New\1222\图库

                var parentPath = ConfigHelper.StrFixImagePath + strDate + "\\";
                parentPath = parentPath.Replace("\\\\", "\\");

                var paths = Directory.GetDirectories(parentPath);
                foreach (var path in paths)
                {
                    if (!Directory.Exists(path))
                        continue;
                    var strName = path.TrimEnd('\\').Replace(parentPath, "");
                    var lstRes = new List<string>();
                    var files = Directory.GetFiles(path);
                    if (files != null && files.Length > 0)
                    {
                        RandomHelper.GetRandomArray(files);
                        var fileName = "";
                        try
                        {
                            var bit = new Bitmap(293, 190);
                            if (BlankImage == null)
                            {
                                var img = CommonCompress.FastFromFile(ConfigHelper.StrBlankImagePath);
                                if (img != null)
                                {
                                    BlankImage = new Bitmap(img);
                                }
                            }
                            if (BlankImage != null)
                            {
                                bit = (Bitmap)BlankImage.Clone();
                            }
                            using (var graph = Graphics.FromImage(bit))
                            {
                                //graph.DrawImage(bit, 293, 190);
                                graph.DrawString(strName, new Font("黑体", 16, FontStyle.Bold), new SolidBrush(Color.Blue),
                                    125, 5);

                                for (var i = 0; i < (files.Length > 8 ? 8 : files.Length); i++)
                                {
                                    var newName = files[i].Substring(files[i].LastIndexOf("\\")).TrimStart('\\');
                                    newName = newName.Substring(0, newName.IndexOf("."));
                                    fileName += newName + "|_|";
                                    var img = Image.FromFile(files[i]);
                                    if (img != null)
                                    {
                                        //拼图
                                        graph.DrawImage(img, lstPoint[i]);
                                        img.Dispose();
                                        img = null;
                                    }
                                }
                                graph.Dispose();
                            }
                            //try
                            //{
                            //    fileName += "|_|TT" + DicImgLearn[strDate].ImageType.GetHashCode();
                            //}
                            //catch (Exception oe)
                            //{
                            //    Console.WriteLine(oe.Message);
                            //}
                            //Date_Loc_文件名_图片
                            var strType = DicImgLearn[strDate].ImageType == FixImageType.归档 &&
                                          DicImgLearn[strDate].Count > 1
                                ? 4
                                : DicImgLearn[strDate].ImageType.GetHashCode();
                            result = string.Format("{0}~{1}~{2}~{3}"
                                , strDate + strType, strName, fileName,
                                Convert.ToBase64String(CommonCompress.ImageToByte(bit)).Replace("+", "%2B"));
                        }
                        catch (Exception oe)
                        {
                            Console.WriteLine(oe.Message);
                        }
                        if (!string.IsNullOrEmpty(result))
                            break;
                    }
                    else
                    {
                        Directory.Delete(path);
                    }
                }
            }
            catch (Exception oe)
            {
                Console.WriteLine(oe.Message);
            }
            try
            {
                if (!string.IsNullOrEmpty(result))
                {
                    if (!DicImgLearn.ContainsKey(strDate))
                    {
                        DicImgLearn.Add(strDate, new LearnProcess { IsLearn = true, ImageType = FixImageType.待整理 });
                    }
                    else
                    {
                        if (DicImgLearn.ContainsKey(strDate) && !DicImgLearn[strDate].IsLearn)
                        {
                            DicImgLearn[strDate].IsLearn = true;
                        }
                    }
                }
                else
                {
                    if (DicImgLearn.ContainsKey(strDate) && DicImgLearn[strDate].IsLearn)
                    {
                        if (!DicImgLearn[strDate].IsMoving)
                        {
                            DicImgLearn[strDate].IsMoving = true;
                            DicImgLearn[strDate].IsLearn = false;
                            DicImgLearn[strDate].Count++;
                            try
                            {
                                switch (DicImgLearn[strDate].ImageType)
                                {
                                    case FixImageType.待整理:
                                        if (MoveTmpImage(strDate, FixImageType.用户修正_Error, FixImageType.待整理))
                                        {
                                            DicImgLearn[strDate].ImageType = FixImageType.用户修正_Error;
                                        }
                                        break;
                                    case FixImageType.用户修正_Error:
                                        if (MoveTmpImage(strDate, FixImageType.用户修正_OK, FixImageType.待整理))
                                        {
                                            DicImgLearn[strDate].ImageType = FixImageType.用户修正_OK;
                                            DicImgLearn[strDate].Count = 1;
                                        }
                                        break;
                                    case FixImageType.用户修正_OK:
                                        if (DicImgLearn[strDate].Count < 2)
                                        {
                                            if (MoveTmpImage(strDate, FixImageType.用户修正_OK, FixImageType.待整理))
                                            {
                                                DicImgLearn[strDate].Count++;
                                            }
                                        }
                                        else
                                        {
                                            if (MoveTmpImage(strDate, FixImageType.用户修正_OK, FixImageType.归档))
                                            {
                                                DicImgLearn[strDate].ImageType = FixImageType.归档;
                                                DicImgLearn.Remove(strDate);
                                            }
                                        }
                                        break;
                                }
                            }
                            catch (Exception oe)
                            {
                                Console.WriteLine(oe.Message);
                            }
                            DicImgLearn[strDate].IsMoving = false;
                        }
                    }
                }
            }
            catch (Exception oe)
            {
                ConfigHelper._Log.Error("【User图片】处理出错！", oe);
            }
            return result;
        }

        private static bool MoveTmpImage(string strDate, FixImageType fromImgType = FixImageType.待整理,
            FixImageType toImgType = FixImageType.待整理)
        {
            var result = false;
            ConfigHelper._Log.InfoFormat("开始【{0}】=》【{1}】", fromImgType, toImgType);
            try
            {
                if (fromImgType.Equals(toImgType))
                {
                    ConfigHelper._Log.InfoFormat("类别一致，无需移动！{0}=>{1}", fromImgType, toImgType);
                    return result;
                }
                if (string.IsNullOrEmpty(strDate))
                {
                    strDate = ServerTime.DateTime.ToString("MMdd");
                }
                //Rec\New\1222\图库
                var fromPath = GetImagePath(strDate, fromImgType);
                var toPath = GetImagePath(strDate, toImgType);
                if (!Directory.Exists(fromPath))
                {
                    ConfigHelper._Log.InfoFormat("文件夹不存在！{0}=>{1}", fromImgType, toImgType);
                    return result;
                }
                var paths = Directory.GetDirectories(fromPath);
                foreach (var path in paths)
                {
                    var files = Directory.GetFiles(path);
                    if (files.Length > 0)
                    {
                        var pp = toPath + path.Replace(fromPath, "") + "\\";
                        if (!Directory.Exists(pp))
                            Directory.CreateDirectory(pp);
                        //ConfigHelper._Log.InfoFormat("【{2}】Path:{0}=》{1}", fromPath, pp, fromImgType.ToString());
                        foreach (var ff in files)
                        {
                            if (File.Exists(ff))
                            {
                                if (!Directory.Exists(Path.GetDirectoryName(ff.Replace(fromPath, toPath))))
                                {
                                    Directory.CreateDirectory(Path.GetDirectoryName(ff.Replace(fromPath, toPath)));
                                }
                                //ConfigHelper._Log.InfoFormat("【{2}】{0}=》{1}", ff, ff.Replace(fromPath, toPath), fromImgType.ToString());
                                File.Move(ff, ff.Replace(fromPath, toPath));
                            }
                        }
                        if (!result)
                        {
                            result = true;
                        }
                    }
                }
                if (result && toImgType == FixImageType.归档)
                {
                    fromPath = GetImagePath(strDate, FixImageType.用户修正_Error);
                    DeleteFiles(fromPath);
                    fromPath = GetImagePath(strDate, FixImageType.用户修正_OK);
                    DeleteFiles(fromPath);
                    fromPath = GetImagePath(strDate, FixImageType.待整理);
                    DeleteFiles(fromPath);
                }
            }
            catch (Exception oe)
            {
                ConfigHelper._Log.Error("移动图片失败！", oe);
            }
            finally
            {
                ConfigHelper._Log.InfoFormat("结束【{0}】=》【{1}】", fromImgType, toImgType);
            }
            return result;
        }

        private static void DeleteFiles(string str)
        {
            try
            {
                var fatherFolder = new DirectoryInfo(str);
                fatherFolder.Delete(true);
            }
            catch (Exception oe)
            {
            }
        }

        private static string GetImagePath(string strDate, FixImageType imgType = FixImageType.待整理)
        {
            var tmpPath = "";
            switch (imgType)
            {
                case FixImageType.待整理:
                    tmpPath = ConfigHelper.StrFixImagePath;
                    break;
                case FixImageType.用户修正_OK:
                    tmpPath = ConfigHelper.StrFixOKImagePath;
                    break;
                case FixImageType.用户修正_Error:
                    tmpPath = ConfigHelper.StrFixErrorImagePath;
                    break;
                case FixImageType.归档:
                    tmpPath = ConfigHelper.StrFixImageGuiDangPath;
                    break;
            }
            tmpPath += strDate + "\\";

            return tmpPath.Replace("\\\\", "\\");
        }

        public static void FixImage(string dir, string strDate, string ids, string result)
        {
            if (!string.IsNullOrEmpty(dir) && !string.IsNullOrEmpty(ids) && !string.IsNullOrEmpty(result))
            {
                if (string.IsNullOrEmpty(strDate))
                {
                    strDate = ServerTime.DateTime.ToString("MMdd");
                }
                if (strDate.Length > 4)
                {
                    strDate = strDate.Substring(0, 4);
                }
                var lstResult = ImageHelper.GetCodeIndexStr(result);
                if (result.Equals("-") || (lstResult != null && lstResult.Count > 0))
                {
                    var path = ConfigHelper.StrFixImagePath + strDate + "\\" + dir + "\\";
                    path = path.Replace("\\\\", "\\");
                    if (Directory.Exists(path))
                    {
                        try
                        {
                            var files = Directory.GetFiles(path);
                            var oldIds = ids.Split(new[] { "|_|" }, StringSplitOptions.RemoveEmptyEntries);
                            oldIds = oldIds.Select(p => p + ".jpg").ToArray();
                            var errorLst = new List<string>();
                            var extLst = new List<string>();
                            if (!result.Equals("-"))
                            {
                                foreach (var item in lstResult)
                                {
                                    if (!extLst.Contains(oldIds[item]))
                                    {
                                        extLst.Add(oldIds[item]);
                                    }
                                }
                            }
                            errorLst = oldIds.Where(p => !extLst.Contains(p)).ToList();
                            extLst = files.Where(p => extLst.Contains(p.Replace(path, ""))).ToList();
                            errorLst = files.Where(p => errorLst.Contains(p.Replace(path, ""))).ToList();
                            if (extLst.Count > 0)
                            {
                                var newDir = ConfigHelper.StrFixImagePath.Replace("待整理", "用户整理-OK") + strDate + "\\" +
                                             dir + "\\";
                                if (!Directory.Exists(newDir))
                                    Directory.CreateDirectory(newDir);
                                foreach (var ff in extLst)
                                {
                                    if (File.Exists(ff))
                                    {
                                        File.Move(ff, ff.Replace("待整理", "用户整理-OK"));
                                    }
                                }
                                files = Directory.GetFiles(path);
                                if (files.Length <= 0)
                                    Directory.Delete(path);
                            }
                            if (errorLst.Count > 0)
                            {
                                var newDir = ConfigHelper.StrFixImagePath.Replace("待整理", "用户整理-Error") + strDate + "\\" +
                                             dir + "\\";
                                if (!Directory.Exists(newDir))
                                    Directory.CreateDirectory(newDir);
                                foreach (var ff in errorLst)
                                {
                                    if (File.Exists(ff))
                                    {
                                        File.Move(ff, ff.Replace("待整理", "用户整理-Error"));
                                    }
                                }
                                files = Directory.GetFiles(path);
                                if (files.Length <= 0)
                                    Directory.Delete(path);
                            }
                        }
                        catch (Exception oe)
                        {
                            ConfigHelper._Log.Error("移动图片失败！", oe);
                        }
                    }
                }
            }
        }

        //public static string GetHanZiByStr(string strImage)
        //{
        //    var strOld = "";
        //    var fileName = GetImageFromBase64(strImage);
        //    return GetCodeByPath(fileName, ref strOld);
        //}

        private static int GetCount(string old, string newStr)
        {
            var count = 0;
            if (old.Length == newStr.Length)
            {
                for (var i = 0; i < newStr.Length; i++)
                {
                    if (old[i].Equals(newStr[i]))
                    {
                        count++;
                    }
                }
            }
            else
            {
                foreach (var item in old)
                {
                    if (newStr.Contains(item))
                    {
                        count++;
                    }
                }
            }
            return count;
        }

        public class LearnProcess
        {
            public bool IsLearn { get; set; }
            public bool IsMoving { get; set; }
            public int Count { get; set; }
            public FixImageType ImageType { get; set; }
        }
    }
}