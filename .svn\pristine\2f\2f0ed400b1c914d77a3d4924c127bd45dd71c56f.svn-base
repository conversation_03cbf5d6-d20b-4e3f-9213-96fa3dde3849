﻿using CommonLib;
using System;
using System.Collections.Generic;
using System.Collections.Specialized;
using System.IO;
using System.Linq;
using System.Security.Cryptography;
using System.Text;

namespace TransOcr
{
    /// <summary>
    /// https://www.iciba.com/translate?type=pic
    /// </summary>
    public class ICiBaImageRec : BaseOcrRec
    {
        public ICiBaImageRec()
        {
            OcrGroup = OcrGroupType.金山;
            OcrType = TransOcrType.金山词霸;
            MaxExecPerTime = 28;

            LstJsonPreProcessArray = new List<object>() { "data" };

            IsSupportVertical = true;
            StrResultJsonSpilt = "original_text";
            StrResultTransJsonSpilt = "text";
            LstVerticalLocation = new List<object> { "x1|CONTACT|y1|CONTACT|x3|CONTACT|y3|CONTACT|left_top" };
            InitLanguage();
        }

        #region 支持的语言

        //zh-CHS	简体中文	Chinese
        //en 英语  English
        //ru  俄语 Russian
        //ja 日语  Japanese
        //ko  韩语 Korean
        //fr 法语  French
        //de  德语 German
        //es 西班牙语    Spanish
        //pt  葡萄牙语

        private void InitLanguage()
        {
            TransLanguageDic = new Dictionary<TransLanguageTypeEnum, string>
            {
                { TransLanguageTypeEnum.自动, "auto" },
                { TransLanguageTypeEnum.中文, "zh" },
                { TransLanguageTypeEnum.英文, "en" },
                { TransLanguageTypeEnum.日语, "jp" },
                { TransLanguageTypeEnum.韩语, "ko" },
                { TransLanguageTypeEnum.西班牙语, "es" },
                { TransLanguageTypeEnum.法语, "fr" },
                { TransLanguageTypeEnum.俄语, "ru" },
                { TransLanguageTypeEnum.德语, "de" }
            };
        }

        #endregion

        protected override string GetHtml(OcrContent content)
        {
            string from = "", to = "";
            InitLanguage(content, ref from, ref to);

            var url = "https://dict.iciba.com/dictionary/img/trans";
            var strPost = "{" + string.Format("\"fromLan\":\"{0}\",\"image\":\"{2}\",\"toLan\":\"{1}\"", from, to, content.strBase64) + "}";

            //https://www.iciba.com/_next/static/chunks/9223.a57dc61ef23d81e7.js
            var r = "3EA453880EFB400CB46C";
            var a = "translate-cloud-image";
            var c = "application/json";
            var s = ToMd5(strPost);
            var o = DateTime.UtcNow.ToString("r");
            var u = string.Join("\n", "POST", s, c, o, "/img-trans/json");
            var g = HmacSha1Sign(u, "b3e896E094bF4b55973e49A6B64E456293228B16");
            var d = string.Join(":", a, r, g);
            var headers = new NameValueCollection
            {
                { "Content-Type",c},
                { "Content-Md5",s},
                { "Request-Date",o},
                { "Authorization",d},
            };
            var result = WebClientSyncExt.GetHtml(url, "", strPost, "", ExecTimeOutSeconds, headers);
            return result;
        }

        private string HmacSha1Sign(string str, string key)
        {
            using (HMACSHA1 hmac = new HMACSHA1(Encoding.UTF8.GetBytes(key)))
            {
                return Convert.ToBase64String(hmac.ComputeHash(Encoding.UTF8.GetBytes(str)));
            }
        }

        private string ToMd5(string strContent)
        {
            string result;
            if (strContent == null)
            {
                result = null;
            }
            else
            {
                MD5 md = MD5.Create();
                byte[] array = md.ComputeHash(Encoding.UTF8.GetBytes(strContent));
                StringBuilder stringBuilder = new StringBuilder();
                for (int i = 0; i < array.Length; i++)
                {
                    stringBuilder.Append(array[i].ToString("x2"));
                }
                result = stringBuilder.ToString();
            }
            return result;
        }
    }
}