﻿namespace HanZiOcr
{
    public enum HanZiOcrType
    {
        腾讯优图Open = 1001,
        //搜狗手机V2 = 1003,
        搜狗图片 = 1004,
        迅捷PDF = 1005,
        百度云 = 1006,
        百度AI = 1007,
        //百度云Open = 1008,
        百度AILite = 1030,
        有道AI = 1009,
        腾讯优图 = 1010,
        搜狗深智 = 1011,
        讯飞API = 1013,
        腾讯优图Lite = 1015,
        讯飞Lite = 1016,
        有道API = 1018,
        传程Lite = 1019,
        //搜狗AI = 1020,
        //腾讯黑镜Lite = 1021,
        //CaterLite = 1022,

        //迅捷PDFLite = 1024,
        //XbyHamLite = 1025,
        WeChatLite = 1026,
        影音艺Lite = 1027,
        //RongCCLite = 1028,
        TaiKangLite = 1031,
        //WitLite = 1032,
        去哪网 = 1033,
        //EBuptLite = 1034,
        QQAI = 1037,
        FaceAI = 1038,
        AICloud = 1039,
        JDAI = 1041,
        OnLineOCR = 1042,
        VIVO = 1043,

        //ComWeiXin = 1045,
        JD = 1046,
        有道文件翻译 = 1049,
        搜狗手机 = 1050,
        微软 = 1051,
        Flitto = 1052,
        欧路词典 = 1058,
        NewOCR = 1059,
        NewOCRApi = 1060,
        有道文档API = 1061,
        PuTaoYu = 1062,
        QQ翻译 = 1063,
        薪火 = 1064,
        AICloudAPI = 1065,
        UUKit = 1066,
        学而思 = 1067,
        学而思API = 1068,
        海马扫描 = 1070,
        飞桨 = 1071,
        合合 = 1072,
        易道博识 = 1073,
        VivoAI = 1075,
        云从 = 1079,
        阿里读光 = 1081,
        讯飞小智 = 1082,
        学而思手写 = 1084,
        OCR助手 = 1085,
        白描_有道 = 1086,
        白描_百度 = 1087,
        译图 = 1088,
        汉王 = 1089,
        搜狗浏览器 = 1090,
        合合中英 = 1091,
        Yandex = 1092,
        白描_讯飞 = 1093,
        汉王云 = 1094,
        萤石 = 1095,
        创迹AI = 1096,
        新东方 = 1097,
        TextIn = 1099,
    }
}
