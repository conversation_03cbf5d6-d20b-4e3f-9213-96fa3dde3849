﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Diagnostics;

namespace GoogleAPI
{
    /// <summary>
    /// Code1 的摘要说明
    /// </summary>
    public class Code : IHttpHandler
    {

        public void ProcessRequest(HttpContext context)
        {
            if (context.Request.Params.Count <= 0 || !context.Request.Params.AllKeys.Contains("op"))
            {
                return;
            }
            context.Response.ContentType = "text/plain";
            string action = context.Request.Params["op"];
            string result = "";
            switch (action)
            {
                case "siteinfo":

                    #region SiteInfo

                    result += Environment.NewLine + "服务器名称：" + context.Server.MachineName;
                    //服务器名称  
                    result += Environment.NewLine + "服务器IP地址：" + context.Request.ServerVariables["LOCAL_ADDR"];
                    //服务器IP地址  
                    result += Environment.NewLine + "HTTP访问端口：" + context.Request.ServerVariables["SERVER_PORT"];
                    //HTTP访问端口"
                    result += Environment.NewLine + ".NET解释引擎版本：" + ".NET CLR" + Environment.Version.Major + "." + Environment.Version.Minor + "." + Environment.Version.Build + "." + Environment.Version.Revision;
                    //.NET解释引擎版本  
                    result += Environment.NewLine + "服务器操作系统版本：" + Environment.OSVersion.ToString();
                    //服务器操作系统版本  
                    result += Environment.NewLine + "服务器IIS版本：" + context.Request.ServerVariables["SERVER_SOFTWARE"];
                    //服务器IIS版本  
                    result += Environment.NewLine + "服务器域名：" + context.Request.ServerVariables["SERVER_NAME"];
                    //服务器域名  
                    result += Environment.NewLine + "虚拟目录的绝对路径：" + context.Request.ServerVariables["APPL_RHYSICAL_PATH"];
                    //虚拟目录的绝对路径  
                    result += Environment.NewLine + "执行文件的绝对路径：" + context.Request.ServerVariables["PATH_TRANSLATED"];
                    ////执行文件的绝对路径  
                    //result += Environment.NewLine + "虚拟目录Session总数：" + context.Session.Contents.Count.ToString();
                    ////虚拟目录Session总数  
                    //result += Environment.NewLine + "虚拟目录Application总数：" + context.Application.Contents.Count.ToString();
                    //虚拟目录Application总数  
                    result += Environment.NewLine + "域名主机：" + context.Request.ServerVariables["HTTP_HOST"];
                    //域名主机  
                    result += Environment.NewLine + "服务器区域语言：" + context.Request.ServerVariables["HTTP_ACCEPT_LANGUAGE"];
                    //服务器区域语言  
                    result += Environment.NewLine + "用户信息：" + context.Request.ServerVariables["HTTP_USER_AGENT"];
                    result += Environment.NewLine + "CPU个数：" + Environment.GetEnvironmentVariable("NUMBER_OF_PROCESSORS");
                    //CPU个数  
                    result += Environment.NewLine + "CPU类型：" + Environment.GetEnvironmentVariable("PROCESSOR_IDENTIFIER");
                    //CPU类型  
                    result += Environment.NewLine + "请求来源地址：" + context.Request.Headers["X-Real-IP"];
                    #endregion

                    break;
                case "code":
                    Stopwatch stop = Stopwatch.StartNew();
                    //result = string.Format("开始请求[{0}]…{1}", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"), Environment.NewLine);
                    string strCode = context.Request.Form["code"];
                    bool isApi = !string.IsNullOrEmpty(context.Request.Form["api"]) && context.Request.Form["api"].Equals("1");
                    if (!string.IsNullOrEmpty(strCode))
                    {
                        if (isApi)
                        {
                            result = GoogleRec.GetContext(strCode);
                        }
                        else
                        {
                            result = GoogleRecTest.GetContext(strCode);
                        }
                    }
                    //result += string.Format("{1}结束请求[{0}]…", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"), Environment.NewLine);
                    break;
                case "time":
                    var strTime = context.Request.QueryString["time"];
                    int nTime = CommonHelper.NMaxTimeOut;
                    if (int.TryParse(strTime, out nTime))
                    {
                        CommonHelper.NMaxTimeOut = nTime;
                    }
                    break;
                default:
                    break;
            }
            if (string.IsNullOrEmpty(result))
            {
                result = "no";
            }
            context.Response.Write(result);
            context.Response.End();
        }

        public bool IsReusable
        {
            get
            {
                return false;
            }
        }
    }
}