﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.InteropServices;
using System.Text;

namespace NewTicket.HttpItem.CurlItem
{
    public sealed class Curl
	{
		private static CURLcode sm_curlCode;

		public static string Version
		{
			get
			{
				Curl.EnsureCurl();
				return Marshal.PtrToStringAnsi(External.curl_version());
			}
		}

		public static CURLcode GlobalInit(int flags)
		{
			Curl.sm_curlCode = External.curl_global_init(flags);
			if (Curl.sm_curlCode == CURLcode.CURLE_OK)
			{
				External.curl_shim_initialize();
			}
			return Curl.sm_curlCode;
		}

		public static void GlobalCleanup()
		{
			if (Curl.sm_curlCode == CURLcode.CURLE_OK)
			{
				External.curl_shim_cleanup();
				External.curl_global_cleanup();
				Curl.sm_curlCode = CURLcode.CURLE_FAILED_INIT;
			}
		}

		public static string Escape(string url, int length)
		{
			Curl.EnsureCurl();
			IntPtr intPtr = External.curl_escape(url, length);
			string result = Marshal.PtrToStringAnsi(intPtr);
			External.curl_free(intPtr);
			return result;
		}

		public static string Unescape(string url, int length)
		{
			Curl.EnsureCurl();
			IntPtr intPtr = External.curl_unescape(url, length);
			string result = Marshal.PtrToStringAnsi(intPtr);
			External.curl_free(intPtr);
			return result;
		}

		public static VersionInfoData GetVersionInfo(CURLversion ver)
		{
			Curl.EnsureCurl();
			return new VersionInfoData(ver);
		}

		internal static void EnsureCurl()
		{
			if (Curl.sm_curlCode != CURLcode.CURLE_OK)
			{
				throw new InvalidOperationException("cURL not initialized");
			}
		}

		static Curl()
		{
			Curl.sm_curlCode = CURLcode.CURLE_FAILED_INIT;
		}

		private Curl()
		{
		}

		private bool Equals(object obj)
		{
			return false;
		}

		private int GetHashCode()
		{
			return 0;
		}

		private Type GetType()
		{
			return null;
		}

		private string ToString()
		{
			return null;
		}
	}
}
