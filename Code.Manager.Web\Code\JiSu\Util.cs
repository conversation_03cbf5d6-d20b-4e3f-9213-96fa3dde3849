﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.IO;
using System.Net;

namespace monocaptcha
{
    class Util
    {
        private static readonly string configurl = "http://conf.monocaptcha.com/endpoints?version=0.0.1";
        private static readonly IPEndPoint defaultEndpoint = new IPEndPoint(IPAddress.Parse("**************"), 8085);
        private static readonly int reqRemoteAddressTimeOut = 1000 * 5;//5s 
        public static IPEndPoint getRemoteAddress()
        {
            ////return new IPEndPoint(IPAddress.Parse("**************"),8081);
            //List<IPEndPoint> remoteAddress = new List<IPEndPoint>();
            //try
            //{
            //    WebRequest wReq = WebRequest.Create(configurl);
            //    wReq.Timeout = reqRemoteAddressTimeOut;
            //    WebResponse wResp = wReq.GetResponse();
            //    Stream respStream = wResp.GetResponseStream();
            //    using (StreamReader reader = new StreamReader(respStream, Encoding.UTF8))
            //    {
            //        string ipaddress = reader.ReadToEnd();
            //        if (ipaddress == null)
            //        {
            //            return defaultEndpoint;
            //        }
                    
            //        string[] ips = ipaddress.Split(new char[] { '|' }, System.StringSplitOptions.RemoveEmptyEntries);
            //        foreach (var item in ips)
            //        {
            //            string[] eps = item.Split(new char[] { ':' }, System.StringSplitOptions.RemoveEmptyEntries);
            //            if (eps.Length < 2)
            //            {
            //                continue;
            //            }
            //            try
            //            {
            //                IPEndPoint ep = new IPEndPoint(IPAddress.Parse(eps[0]), int.Parse(eps[1]));
            //                remoteAddress.Add(ep);
            //            }
            //            catch (FormatException fe)
            //            {
            //                Console.WriteLine(fe);
            //            }
            //            catch (Exception e)
            //            {
            //                Console.WriteLine(e);
            //            }
            //        }
            //    }
            //}
            //catch (System.Exception ex)
            //{
            //    Console.WriteLine(ex.Message);
            //}

            //if (remoteAddress.Count > 0)
            //{
            //    Random ran = new Random();
            //    return remoteAddress[ran.Next(remoteAddress.Count)];
            //}
            //else
            {
                return defaultEndpoint;
            }

        }
        public static byte[] wrapPictureBuffer(byte[] pictureData)
        {
            if (pictureData == null)
            {
                return new byte[0];
            }
            byte[] ret = new byte[pictureData.Length + 4];

            using (MemoryStream ms = new MemoryStream(ret))
            {
                StringReader sr = new StringReader("");
                StringWriter sw = new StringWriter();
                
                ms.Write(BitConverter.GetBytes(pictureData.Length), 0, 4);
                ms.Write(pictureData, 0, pictureData.Length);
            }
            return ret;
        }
    }
}
