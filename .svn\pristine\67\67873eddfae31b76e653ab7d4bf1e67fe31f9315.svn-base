﻿using System;
using System.IO;
using ToolCommon;

namespace DhcpService
{
    public class CommonHelper
    {
        private static string strDBFile = "Code.txt";

        public static string SubString(string strSource, string strSpilt, string strEnd = "")
        {
            return SubStringHorspool(strSource, strSpilt, strEnd).Trim();
        }

        //Horspool匹配算法
        public static string SubStringHorspool(string str, string strStart, string strEnd = "")
        {
            int index = 0;
            if (!string.IsNullOrEmpty(strStart))
            {
                index = str.IndexOf(strStart);
                str = index >= 0 ? str.Substring(index + strStart.Length) : "";
            }
            if (!string.IsNullOrEmpty(str) && !string.IsNullOrEmpty(strEnd))
            {
                index = str.IndexOf(strEnd);
                if (index >= 0)
                {
                    str = str.Substring(0, index);
                }
                //else
                //    str = "";
            }
            strStart = null;
            strEnd = null;
            return str;
        }

        private static string strConnectString = string.Empty;

        private static DataPool _DBHelper = null;

        public static DataPool DBHelper
        {
            get
            {
                if (string.IsNullOrEmpty(strConnectString))
                {
                    if (File.Exists(AppDomain.CurrentDomain.BaseDirectory + "\\DB\\" + strDBFile))
                        strConnectString = AppDomain.CurrentDomain.BaseDirectory + "\\DB\\" + strDBFile;
                    else
                        strConnectString = Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData) + "\\DB\\" + strDBFile;
                }
                if (_DBHelper == null)
                {
                    _DBHelper = new SQLiteHelper("Data Source=" + strConnectString);
                    //CheckDataBase();
                }
                return CommonHelper._DBHelper;
            }
            set { CommonHelper._DBHelper = value; }
        }

        public static bool CheckDataBase()
        {
            bool isOK = true;
            DBHelper.ToString();
            if (!File.Exists(strConnectString))
            {
                if (!Directory.Exists(strConnectString.Substring(0, strConnectString.LastIndexOf("\\"))))
                    Directory.CreateDirectory(strConnectString.Substring(0, strConnectString.LastIndexOf("\\")));
            }
            CreateTableByName();
            return isOK;
        }

        public static void CreateTableByName()
        {
            //if (!DBHelper.IsTableExist("reg"))
            //{
            //    string sql = "CREATE TABLE reg(appCode TEXT,machine TEXT,NType TEXT,dtReg TEXT,dtExpired TEXT,MaxWindow TEXT DEFAULT 1,MaxLogin TEXT DEFAULT 5,IsForbid INTEGER DEFAULT 0);";
            //    DBHelper.ExecuteCommand(sql);
            //}
            //if (!DBHelper.IsTableExist("ticket"))
            //{
            //    string sql = @"CREATE TABLE ticket(appCode TEXT,machine TEXT,dtReg TEXT,ver TEXT,user TEXT,TrainNo TEXT,dtDate TEXT,dtTicket TEXT,count INT DEFAULT 0);";
            //    DBHelper.ExecuteCommand(sql);
            //}
            //if (!DBHelper.IsTableExist("ticketquery"))
            {
                try
                {
                    //(appCode,machine,dtReg,user,TrainNo,dtTicket,dtDate,ver,strFrom,strTo)
                    string sql = @"CREATE TABLE ticketquery(appCode TEXT,machine TEXT,dtReg TEXT,ver TEXT,user TEXT,TrainNo TEXT,dtDate TEXT,dtTicket TEXT,strFrom TEXT,strTo TEXT);";
                    DBHelper.ExecuteCommand(sql);
                }
                catch (Exception oe)
                {
                    //Console.WriteLine(oe.Message);
                }
            }
            //if (!DBHelper.IsTableExist("FoodCategory"))
            //{
            //    string sql = @"CREATE TABLE ticketquery (PID  int NOT NULL,ID  int,Name  varchar(50) NOT NULL,Picture  varchar(100),DetailInfo  varchar(500),Remark  varchar(1000));";
            //    DBHelper.ExecuteCommand(sql);
            //}
            //if (!DBHelper.IsTableExist("OtherInfo"))
            //{
            //    string sql = @"CREATE TABLE OtherInfo (LID  int,KeyName  varchar(100),Value  varchar(100),ShowIndex  int,Type  int);CREATE INDEX OtherTT_Index ON OtherInfo (LID ASC, Type ASC);CREATE INDEX OtherType_Index ON OtherInfo (Type ASC, KeyName ASC);";
            //    DBHelper.ExecuteCommand(sql);
            //}
        }

        public static string GetPublicPicPath()
        {
            string strPath = Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData) + "\\Pic\\";
            if (!Directory.Exists(strPath))
                Directory.CreateDirectory(strPath);
            return strPath;
        }
    }
}
