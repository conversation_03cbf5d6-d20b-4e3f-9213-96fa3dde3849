﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProductVersion>8.0.30703</ProductVersion>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectGuid>{C4D55449-D7ED-4F05-91ED-D353D913078D}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>ToolCommon</RootNamespace>
    <AssemblyName>ToolCommon</AssemblyName>
    <TargetFrameworkVersion>v2.0</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="DataSetSurrogate">
      <HintPath>..\..\GoodFood\DLL\DataSetSurrogate.dll</HintPath>
    </Reference>
    <Reference Include="ICSharpCode.SharpZipLib">
      <HintPath>..\..\GoodFood\DLL\ICSharpCode.SharpZipLib.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.configuration" />
    <Reference Include="System.Data" />
    <Reference Include="System.Data.SQLite">
      <HintPath>..\..\ANDA\DLL\System.Data.SQLite.dll</HintPath>
    </Reference>
    <Reference Include="System.Web" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="CacheHelper.cs" />
    <Compile Include="CacheItem.cs" />
    <Compile Include="ConfigUtil.cs" />
    <Compile Include="CookieHelper.cs" />
    <Compile Include="DataCommon.cs" />
    <Compile Include="FetionService.cs" />
    <Compile Include="HttpClient.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Log\AppLog.cs" />
    <Compile Include="Log\AppLogWriter.cs" />
    <Compile Include="BoxUtil.cs" />
    <Compile Include="Comordecom.cs" />
    <Compile Include="CompressionHelper.cs" />
    <Compile Include="DataFormatter.cs" />
    <Compile Include="DataPool.cs" />
    <Compile Include="DownLoadFiles.cs" />
    <Compile Include="EntityMapper.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="SafeIOCPThreadPool.cs" />
    <Compile Include="SearchEntity.cs" />
    <Compile Include="SQLDBHelper.cs" />
    <Compile Include="SQLiteHelper.cs" />
    <Compile Include="WebClientExt.cs">
      <SubType>Component</SubType>
    </Compile>
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>