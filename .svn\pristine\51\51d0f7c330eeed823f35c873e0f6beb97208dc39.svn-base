﻿using CommonLib.Events;
using Newtonsoft.Json;
using ServiceStack.Web;
using System;
using System.Collections.Generic;
using System.Collections.Specialized;
using System.Linq;
using System.Net;
using System.Security.Policy;
using System.Threading;
using System.Threading.Tasks;

namespace CommonLib
{
    public abstract class BaseRec
    {
        public event EventHandler<OnErrorEventArgs> OnError;
        public event EventHandler<OnStartedEventArgs> OnStart;
        public event EventHandler<OnCompletedEventArgs> OnCompleted;
        public delegate void BeginInvokeDelegate(OnReportEventArgs eventArgs);

        public OcrGroupType OcrGroup { get; set; }

        public int OcrType { get; set; }

        /// <summary>
        /// 处理单元描述
        /// </summary>
        protected KeyValuePair<string, string> EngineType;

        public string EngineDesc
        {
            get
            {
                return string.IsNullOrEmpty(EngineType.Key) ? "默认" : string.Format("{0}", EngineType.Key, EngineType.Value);
            }
        }

        public EnableState State
        {
            get { return state; }
            set
            {
                if (!Equals(state, value))
                {
                    BaseRecHelper.ClearCache();
                }
                state = value;
            }
        }

        #region 竖排识别

        /// <summary>
        /// 是否支持竖排识别
        /// </summary>
        public bool IsSupportVertical { get; set; }

        /// <summary>
        /// 是否支持翻译
        /// </summary>
        public bool IsSupportTrans { get; set; }

        /// <summary>
        /// 文字横排方向
        /// </summary>
        public bool IsFromLeftToRight { get; set; } = true;

        /// <summary>
        /// 文字竖排方向
        /// </summary>
        public bool IsFromTopToDown { get; set; } = true;

        /// <summary>
        /// 是否多页结果返回，需要合并每一页结果
        /// </summary>
        public bool IsMultiPage { get; set; }

        /// <summary>
        /// 是否自动获取页码
        /// </summary>
        public bool IsAutoPageIndex { get; set; }

        #endregion

        #region 竖排文字识别
        protected bool IsDesrializeVerticalByLocation { get; set; }

        protected List<object> LstVerticalLocation { get; set; }

        protected Dictionary<string, string> DicDeserializeVerticalJson { get; set; }

        #endregion

        #region 文件下载

        /// <summary>
        /// 指定返回文件类型
        /// </summary>
        public OcrFileType ResultFileType { get; set; }

        #endregion

        #region 支持处理的文件类型及语言，大小相关属性

        public List<string> AllowUploadFileTypes = new List<string>() { "bmp", "jpg", "png", "jpeg", "gif" };

        public Dictionary<TransLanguageTypeEnum, string> TransLanguageDic = new Dictionary<TransLanguageTypeEnum, string>();

        public int FileSizeLimit { get; set; }

        #endregion

        #region JSON字符串处理

        /// <summary>
        /// 结果是否为字符串数组
        /// </summary>
        protected bool IsJsonArrayString = false;

        /// <summary>
        /// 字符与Location是单独的，需要单独处理
        /// </summary>
        protected bool IsJsonArrayStringWithLocation = false;

        /// <summary>
        /// 配合JsonArrayStringWithLocation，取货位信息
        /// </summary>
        protected List<object> LstJsonLocationProcessArray;

        /// <summary>
        /// 结果是否为JSON字符串
        /// </summary>
        protected bool IsJsonResult = true;

        /// <summary>
        /// 预处理JSON
        /// </summary>
        protected List<object> LstJsonPreProcessArray;

        /// <summary>
        /// 预处理之后的JSON处理（非结果处理）
        /// </summary>
        protected List<object> LstJsonNextProcessArray;

        /// <summary>
        /// 带分页的后续JSON处理（非结果处理）
        /// </summary>
        protected List<object> LstJsonLastProcessArray;

        /// <summary>
        /// 是否以数组方式处理JSON结果
        /// </summary>
        protected bool IsProcessJsonResultByArray = true;

        /// <summary>
        /// JSON结果处理
        /// </summary>
        protected List<object> LstJsonResultProcessArray;

        /// <summary>
        /// 最终结果分割字符
        /// </summary>
        protected string StrResultJsonSpilt;

        /// <summary>
        /// 最终结果分割字符
        /// </summary>
        protected string StrResultTransJsonSpilt;

        /// <summary>
        /// 是否支持Url图片识别
        /// </summary>
        public bool IsSupportUrlOcr { get; set; }

        #endregion

        #region 并发限制

        /// <summary>
        /// 触发被屏蔽字符串
        /// </summary>
        protected Dictionary<string, List<string>> LstForbidArray;

        /// <summary>
        /// 触发被降级字符串
        /// </summary>
        protected Dictionary<string, List<string>> LstReduceWeightArray;

        public int EmptyTimes = 0;

        /// <summary>
        /// 请求超时时间
        /// </summary>
        protected int ExecTimeOutSeconds = 20;

        public long MaxExecPerTime = int.MaxValue;

        public long MaxExecPerTimeBack;

        public long NowExecTimes = 0;

        public long TotalExecTimes = 0;

        public long ForbidTimes = 0;
        private EnableState state;

        public bool IsEnable()
        {
            return State == EnableState.启用 && (MaxExecPerTime == 0 || NowExecTimes < MaxExecPerTime);
        }

        #endregion

        #region 结果处理

        public virtual ResutypeEnum ResultType { get; set; }

        public ResultEntity GetFileResult(string autoText)
        {
            return new ResultEntity()
            {
                files = new List<DownLoadInfo>(),
                autoText = autoText,
                resultType = ResultType
            };
        }
        #endregion

        protected abstract string GetHtml(OcrContent content);

        //public virtual string GetHtml(string strBase64)
        //{
        //    return string.Empty;
        //}

        public abstract string GetOcrTypeName();

        public abstract int GetOcrType();

        public virtual string GetHtmlByUrl(OcrContent content)
        {
            return string.Empty;
        }

        public virtual void InitLanguage(OcrContent content, ref string from, ref string to)
        {
            var defaultFrom = content.from.Equals(TransLanguageTypeEnum.自动)
                ? (content.to.Equals(TransLanguageTypeEnum.中文) ? TransLanguageTypeEnum.英文 : TransLanguageTypeEnum.中文)
                : TransLanguageTypeEnum.中文;
            from = GetLanguage(content.from, true, defaultFrom);
            if (!string.IsNullOrEmpty(from))
            {
                var defaultTo = from.Equals(TransLanguageDic[TransLanguageTypeEnum.中文]) ? TransLanguageTypeEnum.英文 : TransLanguageTypeEnum.中文;
                if (Equals(content.to, TransLanguageTypeEnum.自动))
                {
                    content.to = defaultTo;
                }
                to = GetLanguage(content.to, false, defaultTo);
            }
        }

        private string GetLanguage(TransLanguageTypeEnum from, bool isAutoFirst, TransLanguageTypeEnum defaultType)
        {
            var result = string.Empty;
            if (TransLanguageDic.ContainsKey(from))
            {
                result = TransLanguageDic[from];
            }
            if (string.IsNullOrEmpty(result))
            {
                if (isAutoFirst && TransLanguageDic.ContainsKey(TransLanguageTypeEnum.自动))
                {
                    result = TransLanguageDic[TransLanguageTypeEnum.自动];
                }
            }
            if (string.IsNullOrEmpty(result))
            {
                if (TransLanguageDic.ContainsKey(defaultType))
                {
                    result = TransLanguageDic[defaultType];
                }
                else if (TransLanguageDic.Count > 0)
                {
                    //随机取一个
                    result = TransLanguageDic.First().Value;
                }
            }
            return result;
        }

        protected virtual LocationInfo GetLocationByStr(string locationInfo)
        {
            LocationInfo location;
            if (locationInfo.Contains("\"y\"") == true && !locationInfo.Contains("\"left_top\""))
            {
                var lstLocations = JsonConvert.DeserializeObject<List<VLocationInfo2>>(locationInfo, new JsonSerializerSettings());
                location = GetLocationInfoByStr(lstLocations);
            }
            else
            {
                bool isLeftBottom = locationInfo?.Contains("left_bottom") == true && locationInfo?.Contains("left_top") == false;
                bool isLeftTop = locationInfo?.Contains("left_top") == true && locationInfo?.Contains("left_bottom") == false;
                locationInfo = locationInfo?
                       .Replace("left_bottom", "")
                       .Replace("right_top", "")
                       .Replace("y", "").Replace("x", "")
                       .Replace("Y", "").Replace("X", "")
                       .Replace("[", "").Replace("]", "")
                       .Replace("{", "").Replace("}", "")
                       .Replace("\"", "").Replace(":", "")
                       .Replace("\r", "").Replace("\t", "").Replace("\n", "")
                       .Replace(" ", "").Trim();
                location = GetLocationInfoByStr(locationInfo, isLeftBottom, isLeftTop);
            }
            return location;
        }

        protected LocationInfo GetLocationInfoByStr(List<VLocationInfo2> lstLocations)
        {
            LocationInfo location = null;
            if (lstLocations?.Count == 4)
            {
                var minLeft = lstLocations.Select(p => p.x).Min();
                var minTop = lstLocations.Select(p => p.y).Min();
                var maxLeft = lstLocations.Select(p => p.x).Max();
                var maxTop = lstLocations.Select(p => p.y).Max();
                location = new LocationInfo()
                {
                    left = (int)minLeft,
                    top = (int)minTop,
                    width = (int)(maxLeft - minLeft),
                    height = (int)(maxTop - minTop),
                };
            }
            return location;
        }

        protected LocationInfo GetLocationInfoByStr(string strTmp, bool isLeftBottom, bool isLeftTop)
        {
            LocationInfo location = null;
            if (!string.IsNullOrEmpty(strTmp))
            {
                var spilt = strTmp.Split(new string[] { "," }, StringSplitOptions.RemoveEmptyEntries);
                if (spilt.Length == 8)
                {
                    //左上角坐标(0,1)，右上角坐标(2,3)，右下角坐标(4,5)，左下角坐标(6,7)
                    location = new LocationInfo()
                    {
                        left = Math.Min(BoxUtil.GetInt32FromObject(spilt[0]), BoxUtil.GetInt32FromObject(spilt[6])),
                        top = Math.Min(BoxUtil.GetInt32FromObject(spilt[1]), BoxUtil.GetInt32FromObject(spilt[3])),
                        width = Math.Max(BoxUtil.GetInt32FromObject(spilt[2]) - BoxUtil.GetInt32FromObject(spilt[0]), BoxUtil.GetInt32FromObject(spilt[4]) - BoxUtil.GetInt32FromObject(spilt[6])),
                        height = Math.Max(BoxUtil.GetInt32FromObject(spilt[7]) - BoxUtil.GetInt32FromObject(spilt[1]), BoxUtil.GetInt32FromObject(spilt[5]) - BoxUtil.GetInt32FromObject(spilt[3])),
                    };
                    if (location.width < 0 && location.height < 0)
                    {
                        //右下角坐标(0,1)，左下角坐标(2,3)，左上角坐标(4,5)，右上角坐标(6,7)
                        location = new LocationInfo()
                        {
                            left = Math.Min(BoxUtil.GetInt32FromObject(spilt[4]), BoxUtil.GetInt32FromObject(spilt[2])),
                            top = Math.Min(BoxUtil.GetInt32FromObject(spilt[5]), BoxUtil.GetInt32FromObject(spilt[7])),
                            width = Math.Max(BoxUtil.GetInt32FromObject(spilt[6]) - BoxUtil.GetInt32FromObject(spilt[4]), BoxUtil.GetInt32FromObject(spilt[0]) - BoxUtil.GetInt32FromObject(spilt[2])),
                            height = Math.Max(BoxUtil.GetInt32FromObject(spilt[3]) - BoxUtil.GetInt32FromObject(spilt[5]), BoxUtil.GetInt32FromObject(spilt[1]) - BoxUtil.GetInt32FromObject(spilt[7])),
                        };
                    }
                    else
                    {

                    }
                }
                else if (spilt.Length == 4)
                {
                    if (isLeftTop)
                    {
                        location = new LocationInfo()
                        {
                            left = BoxUtil.GetInt32FromObject(spilt[0]),
                            top = BoxUtil.GetInt32FromObject(spilt[1]),
                            width = BoxUtil.GetInt32FromObject(spilt[2]) - BoxUtil.GetInt32FromObject(spilt[0]),
                            height = BoxUtil.GetInt32FromObject(spilt[3]) - BoxUtil.GetInt32FromObject(spilt[1]),
                        };
                    }
                    else if (isLeftBottom)
                    {
                        location = new LocationInfo()
                        {
                            left = BoxUtil.GetInt32FromObject(spilt[0]),
                            top = BoxUtil.GetInt32FromObject(spilt[3]),
                            width = BoxUtil.GetInt32FromObject(spilt[2]) - BoxUtil.GetInt32FromObject(spilt[0]),
                            height = BoxUtil.GetInt32FromObject(spilt[1]) - BoxUtil.GetInt32FromObject(spilt[3]),
                        };
                    }
                    else
                    {
                        location = new LocationInfo()
                        {
                            left = BoxUtil.GetInt32FromObject(spilt[0]),
                            top = BoxUtil.GetInt32FromObject(spilt[1]),
                            width = BoxUtil.GetInt32FromObject(spilt[2]),
                            height = BoxUtil.GetInt32FromObject(spilt[3]),
                        };
                    }
                }
            }

            return location;
        }

        protected virtual ResultEntity GetProcessText(OcrContent content, string html)
        {
            var result = OcrHtmlProcess.GetSpiltTextByJsonNew(html, ResultType, IsProcessJsonResultByArray
                , LstJsonPreProcessArray, LstJsonNextProcessArray, LstJsonLastProcessArray, LstJsonLocationProcessArray
                , StrResultJsonSpilt, StrResultTransJsonSpilt, IsMultiPage, IsAutoPageIndex, IsSupportVertical, IsJsonArrayString, IsJsonArrayStringWithLocation
                , IsFromLeftToRight, IsFromTopToDown
                , LstVerticalLocation, DicDeserializeVerticalJson, IsDesrializeVerticalByLocation, this.GetLocationByStr
                , content.IsAutoFull2Half, content.IsAutoSpace, content.IsAutoSymbol, content.IsAutoDuplicateSymbol);
            return result;
        }

        public async Task<ProcessStateEntity> QueryFileStatus(string taskId)
        {
            return await Task.Run(() =>
            {
                return QueryFileStatuMethod(taskId);
            });
        }

        public virtual ProcessStateEntity QueryFileStatuMethod(string taskId)
        {
            return new ProcessStateEntity() { state = OcrProcessState.处理成功, desc = "处理完毕，可以下载了！", taskId = taskId };
        }

        public virtual void Reset()
        {
            //if (State != EnableState.启用)
            //{
            //    State = EnableState.启用;
            //}
            if (MaxExecPerTimeBack != 0 && MaxExecPerTimeBack != MaxExecPerTime)
            {
                MaxExecPerTime = MaxExecPerTimeBack;
                State = EnableState.启用;
            }
        }

        public async Task<OcrContent> GetResult(OcrContent content)
        {
            return await Task.Run(() =>
            {
                content.startTicks = ServerTime.DateTime.Ticks;
                content.threadId = Thread.CurrentThread.ManagedThreadId;
                content.state = OcrProcessState.处理中;
                content.processId = GetOcrType();
                content.processName = GetOcrTypeName();
                OnStart?.Invoke(this, new OnStartedEventArgs(content));
                long processedTick = ServerTime.DateTime.Ticks;
                if (MaxExecPerTime == 0 || NowExecTimes < MaxExecPerTime)
                {
                    Interlocked.Increment(ref NowExecTimes);
                    Interlocked.Increment(ref TotalExecTimes);
                    var html = string.Empty;
                    try
                    {
                        if (!AllowUploadFileTypes.Contains(content.fileExt))
                        {
                            //类型不支持，不处理
                            throw new Exception("文件类型不支持！");
                        }

                        processedTick = ServerTime.DateTime.Ticks;
                        //如果不支持URL，且没有下载，先下载再处理
                        if (!IsSupportUrlOcr && string.IsNullOrEmpty(content.strBase64) && !string.IsNullOrEmpty(content.url))
                        {
                            try
                            {
                                using (var webClient = new CNNWebClient())
                                {
                                    content.strBase64 = Convert.ToBase64String(webClient.DownloadData(content.url));
                                }
                            }
                            catch { }
                        }
                        if (IsSupportUrlOcr && !string.IsNullOrEmpty(content.url))
                        {
                            html = GetHtmlByUrl(content);
                        }
                        else if (!string.IsNullOrEmpty(content.strBase64))
                        {
                            html = GetHtml(content);
                        }

                        if (string.IsNullOrEmpty(html))
                        {
                            ReduceWeight(true, processedTick);
                            content.state = OcrProcessState.处理失败;
                        }
                        else
                        {
                            if (!ProcessForbidResultHtml(content.ocrType, html, processedTick))
                            {
                                processedTick = ServerTime.DateTime.Ticks;
                                EmptyTimes = 0;
                                content.result = GetProcessText(content, html);
                                if (content.result != null
                                    && string.IsNullOrEmpty(content.result.spiltText)
                                    && ResultType != ResutypeEnum.网页)
                                {
                                    LogHelper.Log.Info(string.Format("【{0}】解析结果为空，HTML：{1}"
                                        , OcrDesc
                                        , html?.Length > 200 ? html.Substring(0, 200) : html));
                                }

                                content.state = OcrProcessState.处理成功;
                            }
                            else
                            {
                                content.state = OcrProcessState.处理失败;
                            }
                        }
                    }
                    catch (Exception oe)
                    {
                        SetErrorLog(oe.Message, processedTick);
                        LogHelper.Log.Error("Html:" + html, oe);
                        content.state = OcrProcessState.处理失败;
                        OnError?.Invoke(this, new OnErrorEventArgs(content, oe));
                    }
                    finally
                    {
                        Interlocked.Decrement(ref NowExecTimes);
                    }
                }
                else
                {
                    Interlocked.Increment(ref ForbidTimes);
                    content.state = OcrProcessState.并发限制;
                    OnError?.Invoke(this, new OnErrorEventArgs(content, new Exception("最大并发限制" + MaxExecPerTime)));
                    //Console.WriteLine(string.Format("【{4}】限制次数：{1}，当前次数：{0}/{2}，拒绝次数：{3}", NowExecTimes, MaxExecPerTime, TotalExecTimes, ForbidTimes, OrcType));
                }
                content.endTicks = ServerTime.DateTime.Ticks;
                if (content.state != OcrProcessState.处理失败 && content.state != OcrProcessState.并发限制)
                {
                    OnCompleted?.Invoke(this, new OnCompletedEventArgs(content));
                }

                Task.Run(() =>
                {
                    WebClientSyncExt.GetHtml(ConfigHelper.OcrWebSite + "code.ashx?op=reportocrlog&server=" + ConfigHelper.OcrServer
                        + "&BeginTime=" + content.startTicks
                        + "&EndTime=" + ServerTime.DateTime.Ticks
                    + "&EsTime=" + new TimeSpan(ServerTime.DateTime.Ticks - content.startTicks).TotalMilliseconds
                        , "", 30);
                });
                return content;
            });
        }

        private void SetErrorLog(string message, long fromTick)
        {
            LogHelper.Log.Error(string.Format("【{0}】:{1}", OcrDesc, message));
        }

        private List<string> lstForbidHttpCode = new List<string>() { HttpStatusCode.MethodNotAllowed.ToString(), HttpStatusCode.Unauthorized.ToString(), HttpStatusCode.NotFound.ToString() };
        private bool ProcessForbidResultHtml(OcrType ocrType, string html, long processedTick)
        {
            var result = lstForbidHttpCode.Contains(html) || CheckIfMatch(LstForbidArray, html);
            if (result)
            {
                Forbid(ocrType, processedTick);
            }
            else
            {
                result = CheckIfMatch(LstReduceWeightArray, html);
                if (result)
                {
                    ReduceWeight(false, processedTick);
                }
            }
            return result;
        }

        private bool CheckIfMatch(Dictionary<string, List<string>> dicCheck, string html)
        {
            var result = false;
            if (dicCheck != null)
            {
                foreach (var item in dicCheck)
                {
                    var comPareStr = item.Key;
                    if (item.Value?.Count > 0)
                    {
                        comPareStr = html.SubStringHorspool(item.Value[0], item.Value.Count > 1 ? item.Value[1] : null);
                    }
                    if (!string.IsNullOrEmpty(comPareStr) && html.Contains(comPareStr))
                    {
                        result = true;
                        break;
                    }
                }
            }
            return result;
        }

        public void Forbid(OcrType ocrType, long processedTick)
        {
            SetErrorLog("接口被屏蔽", processedTick);
            State = EnableState.禁用;
            LogHelper.Log.Info(string.Format("【{0}】:接口被屏蔽！", OcrDesc));
            BaseRecHelper.DisableByType(ocrType, OcrType.GetHashCode());
        }

        public void ReduceWeight(bool isEmpty = true, long processedTick = 0)
        {
            EmptyTimes++;
            if (EmptyTimes == 5)
            {
                EmptyTimes = 0;
                MaxExecPerTime -= 5;
                if (MaxExecPerTime < 1)
                {
                    State = EnableState.禁用;
                }

                SetErrorLog("接口被降权", processedTick);
                LogHelper.Log.Info(string.Format("【{0}】:接口被降权", OcrDesc));
                BaseRecHelper.ClearCache();
            }
            else
            {
                if (isEmpty)
                {
                    LogHelper.Log.Info(string.Format("【{0}】:返回内容为空，当前连续失败总次数：{1}！"
                        , OcrDesc
                        , EmptyTimes));
                }
            }
        }

        public string OcrDesc
        {
            get
            {
                return string.Format("{0}-{1}", GetOcrTypeName(), EngineDesc);
            }
        }

        public string PostFile(string url, IEnumerable<UploadFileInfo> files, NameValueCollection contents, NameValueCollection headers = null)
        {
            var result = string.Empty;
            try
            {
                result = UploadFileRequest.Post(url, files, contents, headers);
            }
            catch (BadApiException exception)
            {
                LogHelper.Log.Error(OcrDesc, exception);
                switch (exception.Code)
                {
                    //case HttpStatusCode.Forbidden: //403
                    case HttpStatusCode.MethodNotAllowed: //405
                    case HttpStatusCode.Unauthorized://401
                    case HttpStatusCode.NotFound: //404
                        result = exception.Code.ToString();
                        break;
                }
            }
            catch { }
            return result;
        }
    }

    public enum EnableState
    {
        启用 = 0,
        禁用 = 1
    }

    public class ProcessStateEntity
    {

        public string taskId { get; set; }

        public int percent { get; set; }

        public int privewPercent { get; set; }

        public OcrProcessState state { get; set; }

        public string desc { get; set; }
    }
}
