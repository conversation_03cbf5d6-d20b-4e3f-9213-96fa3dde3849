﻿using System;
using System.Collections.Generic;
using System.Collections.Specialized;
using System.Security.Cryptography;
using System.Text;

namespace CommonLib
{
    public class TencentCloudSignHelper
    {
        public class TencentAccount
        {
            public string strSecretId { get; set; }

            public string strSecretKey { get; set; }
        }

        static List<TencentAccount> lstAppAccount = new List<TencentAccount>() {
            new TencentAccount(){
                strSecretId = "AKIDMSJzRckGjvFI4ZkWna6LL4nN9g9DPgy6",
                strSecretKey =  "3oIWZS6Wn3lFSnY2FJ9iz0nH75uUoujH"
            }
        };

        public static string DoRequest(string service, string endpoint, string region, string action, string version, string strPost, TencentAccount account = null)
        {
            account = account ?? lstAppAccount.GetRndItem();

            DateTime date = DateTime.UtcNow;

            var headers = BuildHeaders(account.strSecretId, account.strSecretKey, service
                , endpoint, region, action, version, date, strPost);

            var result = WebClientSyncExt.GetHtml("https://" + endpoint, strPost, 20, headers);
            return result;
        }
        public static string SHA256Hex(string s)
        {
            using (SHA256 algo = SHA256.Create())
            {
                byte[] hashbytes = algo.ComputeHash(Encoding.UTF8.GetBytes(s));
                StringBuilder builder = new StringBuilder();
                for (int i = 0; i < hashbytes.Length; ++i)
                {
                    builder.Append(hashbytes[i].ToString("x2"));
                }
                return builder.ToString();
            }
        }
        public static byte[] HmacSHA256(byte[] key, byte[] msg)
        {
            using (HMACSHA256 mac = new HMACSHA256(key))
            {
                return mac.ComputeHash(msg);
            }
        }
        public static NameValueCollection BuildHeaders(string secretid,
        string secretkey, string service, string endpoint, string region,
        string action, string version, DateTime date, string requestPayload)
        {
            string datestr = date.ToString("yyyy-MM-dd");
            //Console.WriteLine(datestr);

            DateTime startTime = new DateTime(1970, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc);
            long requestTimestamp = (long)Math.Round((date - startTime).TotalMilliseconds, MidpointRounding.AwayFromZero) / 1000;
            //Console.WriteLine(requestTimestamp);
            // ************* 步骤 1：拼接规范请求串 *************
            string algorithm = "TC3-HMAC-SHA256";
            string httpRequestMethod = "POST";
            string canonicalUri = "/";
            string canonicalQueryString = "";
            string contentType = "application/json";
            string canonicalHeaders = "content-type:" + contentType + "; charset=utf-8\n" + "host:" + endpoint + "\n";
            string signedHeaders = "content-type;host";
            string hashedRequestPayload = SHA256Hex(requestPayload);
            string canonicalRequest = httpRequestMethod + "\n"
                + canonicalUri + "\n"
                + canonicalQueryString + "\n"
                + canonicalHeaders + "\n"
                + signedHeaders + "\n"
                + hashedRequestPayload;
            //Console.WriteLine(canonicalRequest);
            //Console.WriteLine("----------------------------------");

            // ************* 步骤 2：拼接待签名字符串 *************
            string credentialScope = datestr + "/" + service + "/" + "tc3_request";
            string hashedCanonicalRequest = SHA256Hex(canonicalRequest);
            string stringToSign = algorithm + "\n" + requestTimestamp.ToString() + "\n" + credentialScope + "\n" + hashedCanonicalRequest;
            //Console.WriteLine(stringToSign);
            //Console.WriteLine("----------------------------------");

            // ************* 步骤 3：计算签名 *************
            byte[] tc3SecretKey = Encoding.UTF8.GetBytes("TC3" + secretkey);
            byte[] secretDate = HmacSHA256(tc3SecretKey, Encoding.UTF8.GetBytes(datestr));
            byte[] secretService = HmacSHA256(secretDate, Encoding.UTF8.GetBytes(service));
            byte[] secretSigning = HmacSHA256(secretService, Encoding.UTF8.GetBytes("tc3_request"));
            byte[] signatureBytes = HmacSHA256(secretSigning, Encoding.UTF8.GetBytes(stringToSign));
            string signature = BitConverter.ToString(signatureBytes).Replace("-", "").ToLower();
            //Console.WriteLine(signature);
            //Console.WriteLine("----------------------------------");

            // ************* 步骤 4：拼接 Authorization *************
            string authorization = algorithm + " "
                + "Credential=" + secretid + "/" + credentialScope + ", "
                + "SignedHeaders=" + signedHeaders + ", "
                + "Signature=" + signature;
            //Console.WriteLine(authorization);
            //Console.WriteLine("----------------------------------");

            var headers = new NameValueCollection();
            headers.Add("Authorization", authorization);
            //headers.Add("Host", endpoint);
            //headers.Add("Content-Type", contentType + "; charset=utf-8");
            headers.Add("X-TC-Timestamp", requestTimestamp.ToString());
            headers.Add("X-TC-Version", version);
            headers.Add("X-TC-Action", action);
            headers.Add("X-TC-Region", region);
            return headers;
        }
    }
}
