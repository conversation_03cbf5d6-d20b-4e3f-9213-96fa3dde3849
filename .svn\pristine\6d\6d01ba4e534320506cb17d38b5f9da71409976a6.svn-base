﻿<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <PackageId>ServiceStack.Redis</PackageId>
    <AssemblyName>ServiceStack.Redis</AssemblyName>
    <TargetFrameworks>net472;netstandard2.0;netstandard2.1;net6.0</TargetFrameworks>
    <Title>C# Redis client for the Redis NoSQL DB</Title>
    <PackageDescription>
      C# Redis Client for the worlds fastest distributed NoSQL datastore.
      Byte[], String and POCO Typed clients.
      Thread-Safe Basic and Pooled client managers included.
    </PackageDescription>
    <PackageTags>Redis;NoSQL;Client;Distributed;Cache;PubSub;Messaging;Transactions</PackageTags>
    <SignAssembly>False</SignAssembly>
  </PropertyGroup>
  <!-- TODO: talk about TFMs; Microsoft.Bcl.AsyncInterfaces starts at net461 -->
  <PropertyGroup Condition="'$(TargetFramework)' == 'net472'">
    <DefineConstants>$(DefineConstants);NET472</DefineConstants>
  </PropertyGroup>
  <PropertyGroup Condition="'$(TargetFramework)' == 'netstandard2.0'">
    <DefineConstants>$(DefineConstants);NETCORE</DefineConstants>
  </PropertyGroup>
  <PropertyGroup Condition="'$(TargetFramework)' == 'netstandard2.1'">
    <DefineConstants>$(DefineConstants);ASYNC_MEMORY;NETCORE</DefineConstants>
  </PropertyGroup>
  <PropertyGroup Condition="'$(TargetFramework)' == 'net6.0'">
    <DefineConstants>$(DefineConstants);ASYNC_MEMORY;NETCORE;NET6_0</DefineConstants>
  </PropertyGroup>
  <ItemGroup>
    <PackageReference Include="Microsoft.Bcl.AsyncInterfaces" Version="6.0.0" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\..\..\ServiceStack.Text-master\src\ServiceStack.Text\ServiceStack.Text.csproj" />
    <ProjectReference Include="..\..\..\ServiceStack\src\ServiceStack.Interfaces\ServiceStack.Interfaces.csproj" />
    <ProjectReference Include="..\..\..\ServiceStack\src\ServiceStack.Common\ServiceStack.Common.csproj" />
  </ItemGroup>
  <ItemGroup Condition=" '$(TargetFramework)' == 'net472' ">
    <Reference Include="System.Net" />
  </ItemGroup>
  <ItemGroup Condition=" '$(TargetFramework)' == 'netstandard2.0' ">
    <PackageReference Include="System.Net.Security" Version="4.3.2" />
    <PackageReference Include="System.Collections.Specialized" Version="4.3.0" />
    <PackageReference Include="System.Collections.NonGeneric" Version="4.3.0" />
    <PackageReference Include="System.Net.NameResolution" Version="4.3.0" />
    <PackageReference Include="System.Data.Common" Version="4.3.0" />
    <PackageReference Include="System.Threading.Thread" Version="4.3.0" />
    <!-- note: not needed from netstandard2.1 or netcoreapp3.0 onwards -->
    <PackageReference Include="Microsoft.Bcl.AsyncInterfaces" Version="5.0.0" />
  </ItemGroup>
  <ItemGroup Condition=" '$(TargetFramework)' == 'net6.0' ">
  </ItemGroup>
  <ItemGroup>
    <PackageReference Update="Microsoft.SourceLink.GitHub" Version="1.1.1" />
  </ItemGroup>
  
</Project>