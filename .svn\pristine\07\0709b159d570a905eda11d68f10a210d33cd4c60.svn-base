﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Diagnostics;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Windows.Forms;
using System.Threading;
using System.Threading.Tasks;
using NewTicket.MyControl;

namespace NewTicket
{
    public sealed partial class FormTask : Form
    {

        public bool IsAdd { get; set; }

        public List<UserEntity> LstUser { get; set; }

        public FormTask()
        {
            InitializeComponent();
            CheckForIllegalCrossThreadCalls = false;
            Control.CheckForIllegalCrossThreadCalls = false;
            this.DoubleBuffered = true;
        }

        private void FormTask_Load(object sender, EventArgs e)
        {
            if (IsAdd)
            {
                this.Text = "新增任务";
                QueryTick.Value = Settings.Default.NQueryTick;
            }
            else
            {
                this.Text = "修改任务";
            }
            InitControls();
            LoadUser();
            LoadTask();
            QueryTick.ValueChanged += QueryTick_ValueChanged;
        }

        private BindingList<TicketEntity> _BindingLeftTicketStatus;
        private BindingList<UserEntity> _BindingUser;

        private void LoadUser()
        {
            _BindingLeftTicketStatus = new BindingList<TicketEntity>(_LeftTicketStatus);
            dgQuery.AutoGenerateColumns = false;
            dgQuery.DataError += dvLog_DataError;
            dgQuery.Columns["TrainNo"].DefaultCellStyle.Font = new Font("微软雅黑", 12, FontStyle.Bold);
            dgQuery.Columns["TrainNo"].DefaultCellStyle.ForeColor = Color.Blue;
            dgQuery.DataSource = _BindingLeftTicketStatus;

            _BindingUser = new BindingList<UserEntity>(LstUser);
            dgNoTaskUser.AutoGenerateColumns = false;
            dgNoTaskUser.DataError += new DataGridViewDataErrorEventHandler(dvLog_DataError);
            dgNoTaskUser.DataSource = _BindingUser;

            if (LstUser != null && LstUser.Count > 0)
            {
                if (!IsAdd)
                {
                    foreach (DataGridViewRow item in dgNoTaskUser.Rows)
                    {
                        item.Cells[0].Value = true;
                    }
                }
                dgNoTaskUser.Rows[0].Selected = true;
                dgNoTaskUser.Rows[0].Cells[0].Value = true;
                dgNoTaskUser_MouseClick(null, null);
            }
        }

        private void InitControls()
        {
            try
            {
                foreach (string str in CommonString.SeatTypeNames)
                {
                    ToolStripMenuItem item = new ToolStripMenuItem();
                    item.Text = str;
                    item.Click += new EventHandler(ticketType_Click);
                    cmsTicketTypes.DropDownItems.Add(item);
                }
                foreach (string str in CommonString.UserTypeNames)
                {
                    ToolStripMenuItem item = new ToolStripMenuItem();
                    item.Text = "全部" + str;
                    item.Click += new EventHandler(userType_Click);
                    cmsTypes.DropDownItems.Add(item);
                }
                cmsTypes.DropDownItems.Add(new ToolStripSeparator());
                foreach (string str in CommonString.UserTypeNames)
                {
                    ToolStripMenuItem item = new ToolStripMenuItem();
                    item.Text = str;
                    item.Click += new EventHandler(userType_Click);
                    cmsTypes.DropDownItems.Add(item);
                    ddlUType.Items.Add(str);
                }
                foreach (var str in CommonString.IDCardTypes)
                {
                    ToolStripMenuItem item = new ToolStripMenuItem();
                    item.Text = "全部" + str.Key;
                    item.Click += new EventHandler(userCardType_Click);
                    cmsCardTypes.DropDownItems.Add(item);
                }
                cmsCardTypes.DropDownItems.Add(new ToolStripSeparator());
                foreach (var str in CommonString.IDCardTypes)
                {
                    ToolStripMenuItem item = new ToolStripMenuItem();
                    item.Text = str.Key;
                    item.Click += new EventHandler(userCardType_Click);
                    cmsCardTypes.DropDownItems.Add(item);
                }
                foreach (var item in CommonString.SeatTypeNames)
                {
                    cmbSeat.TreeDropDown.Nodes.Add(item);
                    ddlSeat.TreeDropDown.Nodes.Add(item);
                }
            }
            catch (Exception oe)
            {
                Log.WriteError("InitControls出错", oe);
            }
            cmbSeat.StrSpilt = "|";
            ddlSeat.StrSpilt = "|";


            try
            {
                dtGoDate.Tag = CommonMethod.MaxDate.ToString("yyyy-MM-dd");
                dtAttention.TreeDropDown.Nodes.Clear();
                dtGoDate.MinDate = DateTime.Today;
                dtGoDate.MaxDate = CommonMethod.MaxDate.AddDays(3);
                //dtAttention.TreeDropDown.TreeViewNodeSorter = null;
                for (DateTime i = dtGoDate.MinDate; i <= dtGoDate.MaxDate; )
                {
                    dtAttention.TreeDropDown.Nodes.Add(i.ToString("yyyy-MM-dd"));
                    i = i.AddDays(1);
                }
            }
            catch (Exception oe)
            {
                Log.WriteError("InitControls出错", oe);
            }
            lnkYuShou_LinkClicked(null, null);

            //姓名,类别,席别,证件,证件号
            ColumnHeader columnHeader0 = new ColumnHeader();
            columnHeader0.Text = "姓名";
            columnHeader0.Width = 70;
            ColumnHeader columnHeader1 = new ColumnHeader();
            columnHeader1.Text = "类别";
            columnHeader1.Width = 50;
            ColumnHeader columnHeader2 = new ColumnHeader();
            columnHeader2.Text = "席别";
            columnHeader2.Width = 45;
            ColumnHeader columnHeader3 = new ColumnHeader();
            columnHeader3.Text = "证件";
            columnHeader3.Width = 45;
            ColumnHeader columnHeader4 = new ColumnHeader();
            columnHeader4.Text = "证件号";
            columnHeader4.Width = 125;
            ColumnHeader columnHeader5 = new ColumnHeader();
            columnHeader5.Text = "状态";
            columnHeader5.Width = 50;
            lvPassenger.Columns.AddRange(new ColumnHeader[] { columnHeader0, columnHeader1, columnHeader2, columnHeader3, columnHeader4, columnHeader5 });

        }

        private void LoadTask()
        {
            if (IsAdd)
                return;
            var user = LstUser[0];
            if (user.LstNowAttetion == null || user.LstNowAttetion.Count <= 0)
            {
                return;
            }
            if (user.LstNowAttetion[0].PurposeCode.Equals(CommonString.strStudentType))
            {
                rdoStudent.Checked = true;
            }
            List<string> lstTmp = new List<string>();
            string strTmp = "," + user.TaskFrom + ",";
            isAuto = true;
            cbxFrom.Text = user.LstNowAttetion[0].FromStationName;
            if (!string.IsNullOrEmpty(cbxFrom.Text))
            {
                strTmp = strTmp.Replace("," + cbxFrom.Text + ",", "");
                lstTmp.AddRange(strTmp.Split(new string[] { "," }, StringSplitOptions.RemoveEmptyEntries));
                BindListViewGroup(lvStartN, lstTmp);
            }
            strTmp = "," + user.TaskTo + ",";
            isAuto = true;
            cbxTo.Text = user.LstNowAttetion[0].ToStationName;
            isAuto = false;
            if (!string.IsNullOrEmpty(cbxTo.Text))
            {
                strTmp = strTmp.Replace("," + cbxTo.Text + ",", "");
                lstTmp = new List<string>();
                lstTmp.AddRange(strTmp.Split(new string[] { "," }, StringSplitOptions.RemoveEmptyEntries));
                BindListViewGroup(lvEndN, lstTmp);
            }
            try
            {
                dtGoDate.Value = user.LstNowAttetion[0].Date;
            }
            catch (Exception oe)
            {
                Log.WriteError("LoadTask出错", oe);
            }
            txtTrainNo.Text = user.TaskTrainNo;
            dtAttention.Value = user.TaskDate;
            QueryTick.Value = user.NQueryTick;
            nSubSleepTine.Value = user.NSubTick;
            if (user.IsTickQuery)
            {
                dtRefreshTick.Checked = true;
                if (user.dtTickQuery.Date == CommonString.serverTime.Date.AddDays(1))
                    chkNextDay.Checked = true;
                dtRefreshTick.Value = BoxUtil.GetDateTimeFromObject(user.dtTickQuery.ToString("HH:mm:ss"));
                nQueryMSecond.Value = user.dtTickQuery.Millisecond;
            }
            if (user.IsTickSub)
            {
                dtSubTick.Checked = true;
                dtSubTick.Value = BoxUtil.GetDateTimeFromObject(user.dtTickSub.ToString("HH:mm:ss"));
                nSubMSecond.Value = user.dtTickSub.Millisecond;
            }
            if (user.IsJianLou)
            {
                rdoJianLou.Checked = true;
            }
            nSubStopTime.Value = user.NSubStopTime;
            chkNoSeat.Checked = user.IsNoControlSeat;
            chkAutoRemovePassenger.Checked = user.IsAutoRemovePassenger;
            rdoWuZuo.Checked = user.IsWuZuo;
            chkAutoCancel.Checked = user.IsAutoCancel;
            txtRemark.Text = user.Remark;

            if (user.IsNMode)
            {
                chkNMore.Checked = true;
                ddlSeat.Value = user.FirstSeat;
                ddlUType.Text = user.FirstUType;
            }
            btnQuery_Click(null, null);
        }

        private void dtGoDate_ValueChanged(object sender, EventArgs e)
        {
            if (string.IsNullOrEmpty(dtAttention.Value) || dtAttention.CheckedItems.Count == 1)
                dtAttention.Value = dtGoDate.Value.ToString("yyyy-MM-dd");
        }

        #region 订票相关

        private void DoCheckTicket()
        {
            if (LstUser != null && LstUser.Count > 0)
            {
                for (int i = 0; i < LstUser.Count; i++)
                {
                    UserEntity user = LstUser[i];
                    if (user.isAccountError)
                    {
                        continue;
                    }
                    user.Init(false);
                    user.LstTmpPassage.RemoveAll(p => !p.IsChecked);
                    if (user.LstTmpPassage != null && user.LstTmpPassage.Count > 1 && user.IsNMode)
                    {
                        user.LstTmpPassage[0].SetSeatTypes(user.FirstSeat);
                        user.LstTmpPassage[0].SetUserType(user.FirstUType);
                    }
                    if (user.IsLogined)
                        user.StrNowUserStatus = "登录成功";
                    if (user.LstTmpPassage != null && user.LstTmpPassage.Count > 0)
                    {
                        user.StrFirstIdCard = user.LstTmpPassage[0].IDCard;
                    }
                    user.LstTmpPassage = user.LstTmpPassage;
                }
            }
        }

        private List<AttentionItem> GetNowAttention()
        {
            List<AttentionItem> lstAttention = null;
            List<string> lstStartStation = null;
            List<string> lstEndStation = null;
            try
            {
                lstStartStation = GetlstStationByListView(lvStartN);
                lstEndStation = GetlstStationByListView(lvEndN);
                if (!lstStartStation.Contains(cbxFrom.Text.Trim()))
                {
                    lstStartStation.Insert(0, cbxFrom.Text.Trim());
                }
                if (!lstEndStation.Contains(cbxTo.Text.Trim()))
                {
                    lstEndStation.Insert(0, cbxTo.Text.Trim());
                }
                List<string> lstTmoStation = new List<string>();
                bool isTurn = false;
                if (lstStartStation.Count < lstEndStation.Count)
                {
                    isTurn = true;
                    lstTmoStation = lstEndStation;
                    lstEndStation = lstStartStation;
                    lstStartStation = lstTmoStation;
                }
                lstTmoStation = null;
                List<string> lstTmpTrainNo = new List<string>();
                if (string.IsNullOrEmpty(txtTrainNo.Text.Trim()))
                {
                    if (dgQuery.DataSource != null && dgQuery.Rows.Count > 0)
                    {
                        var trainNo = "";
                        foreach (DataGridViewRow row in dgQuery.Rows)
                        {
                            trainNo = row.Cells["TrainNo"].Value.ToString();
                            if (!string.IsNullOrEmpty(trainNo) && !lstTmpTrainNo.Contains(trainNo))
                            {
                                lstTmpTrainNo.Add(trainNo);
                            }
                        }
                    }
                    if (lstTmpTrainNo == null || lstTmpTrainNo.Count <= 0)
                    {
                        MessageBox.Show(this, "(不限车次)请先查询出所有需要预定的车次后重试！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Error);
                        return lstAttention;
                    }
                    if (MessageBox.Show(this, "(不限车次)将添加以下车次:\n" + string.Join("、", lstTmpTrainNo) + "\n请确定是否正确！", "提示", MessageBoxButtons.YesNo, MessageBoxIcon.Question) == System.Windows.Forms.DialogResult.No)
                        return lstAttention;
                }
                else
                {
                    txtTrainNo.Text = txtTrainNo.Text.Replace(",,", ",").TrimStart(',').TrimEnd(',').Trim().ToUpper();
                    lstTmpTrainNo.AddRange(txtTrainNo.Text.Trim().Split(new string[] { "," }, StringSplitOptions.RemoveEmptyEntries));
                    if (lstTmpTrainNo.Count <= 0)
                    {
                        MessageBox.Show(this, "请设置好抢票的车次后重试（双击查询列表中车次添加）！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Error);
                        return lstAttention;
                    }
                }
                while (lstTmpTrainNo.Exists(p => p.Contains("/")))
                {
                    var trainNo = lstTmpTrainNo.Find(p => p.Contains("/"));
                    if (!string.IsNullOrEmpty(trainNo))
                    {
                        lstTmpTrainNo.Remove(trainNo);
                        var trains = trainNo.Split(new string[] { "/" }, StringSplitOptions.RemoveEmptyEntries);
                        foreach (var item in trains)
                        {
                            if (!lstTmpTrainNo.Contains(item))
                            {
                                lstTmpTrainNo.Add(item);
                            }
                        }
                    }
                }
                lstAttention = new List<AttentionItem>();
                foreach (string strStart in lstStartStation)
                {
                    foreach (string strEnd in lstEndStation)
                    {
                        var fromSation = CommonString.Stations.FirstOrDefault(v => v.Name == strStart || v.ShortCut == strStart);
                        if (fromSation == null)
                        {
                            MessageBox.Show(this, "起始站点有误(" + strStart + ")，请确认后重试！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Error);
                            return null;
                        }
                        var toStation = CommonString.Stations.FirstOrDefault(v => v.Name == strEnd || v.ShortCut == strEnd);
                        if (toStation == null)
                        {
                            MessageBox.Show(this, "起始站点有误(" + strStart + ")，请确认后重试！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Error);
                            return null;
                        }
                        foreach (string str in dtAttention.CheckedItems)
                        {
                            AttentionItem item = new AttentionItem();
                            //item.IsMobile = CommonString.IsMobile;
                            item.FromStation = isTurn ? toStation : fromSation;
                            item.ToStation = isTurn ? fromSation : toStation;
                            item.Date = DateTime.Parse(str);
                            item.LstTrainNo = lstTmpTrainNo;
                            item.LstGroupStation = NewTicketHelper.GetStationCodes(item.FromStation.Code, item.ToStation.Code);
                            item.PurposeCode = rdoAdult.Checked ? CommonString.strAdultType : CommonString.strStudentType;
                            item.LstAllSeatType = new List<string>();
                            //(CommonString.IsMobile ? CommonString.strMobileAdultType : CommonString.strAdultType)
                            //: (CommonString.IsMobile ? CommonString.strMobileStudentType : CommonString.strStudentType);
                            if (!lstAttention.Exists(oo =>
                                oo.FromStation.Code.Equals(item.FromStation.Code)
                                && oo.ToStation.Code.Equals(item.ToStation.Code)
                                && oo.LstTrainNo.Equals(item.LstTrainNo)
                                && oo.Date.Equals(item.Date)))
                                lstAttention.Add(item);
                        }
                    }
                }
            }
            catch (Exception oe)
            {
                Log.WriteError("GetNowAttention出错", oe);
            }
            finally
            {
                lstStartStation = null;
                lstEndStation = null;
            }
            return lstAttention;
        }

        private DateTime GetTickQuery(bool isGetTask = false)
        {
            DateTime dtTmp = DateTime.MinValue;
            bool isTickQuery = (dtRefreshTick.Checked || chkNextDay.Checked);
            if (!isGetTask && chkNextDay.Checked && (CommonString.serverTime.Hour >= CommonMethod.NWebStartHour && CommonString.serverTime.Hour <= CommonMethod.NWebEndHour))
            {

                ThreadPool.QueueUserWorkItem((object obj) =>
                {
                    System.Threading.Thread.Sleep(50);
                    IntPtr hwnd = WindowsAPI.FindWindow(null, "定时抢票设置");
                    if (hwnd != null && hwnd.ToInt32() > 0)
                    {
                        IntPtr hbtn1 = WindowsAPI.FindWindowEx(hwnd, IntPtr.Zero, "BUTTON", null);
                        IntPtr hbtn2 = WindowsAPI.FindWindowEx(hwnd, hbtn1, "BUTTON", null);
                        WindowsAPI.SendMessage(hbtn1, WindowsAPI.WM_SETTEXT, IntPtr.Zero, "继续操作");
                        WindowsAPI.SendMessage(hbtn2, WindowsAPI.WM_SETTEXT, IntPtr.Zero, "抢今天票");
                    }
                });
                if (MessageBox.Show(this, string.Format("你选择了【抢明天票】,请确定是否正确？\n错误请点'抢【今天】票'。"), "定时抢票设置", MessageBoxButtons.YesNo, MessageBoxIcon.Question) == System.Windows.Forms.DialogResult.No)
                    chkNextDay.Checked = false;
            }
            if (chkNextDay.Checked)
            {
                if (dtRefreshTick.Checked)
                {
                    if (dtRefreshTick.Value.Hour <= CommonMethod.NWebStartHour - 1)
                    {
                        ThreadPool.QueueUserWorkItem((object obj) =>
                        {
                            System.Threading.Thread.Sleep(50);
                            IntPtr hwnd = WindowsAPI.FindWindow(null, "定时抢票设置");
                            if (hwnd != null && hwnd.ToInt32() > 0)
                            {
                                IntPtr hbtn1 = WindowsAPI.FindWindowEx(hwnd, IntPtr.Zero, "BUTTON", null);
                                IntPtr hbtn2 = WindowsAPI.FindWindowEx(hwnd, hbtn1, "BUTTON", null);
                                IntPtr hbtn3 = WindowsAPI.FindWindowEx(hwnd, hbtn2, "BUTTON", null);
                                WindowsAPI.SendMessage(hbtn1, WindowsAPI.WM_SETTEXT, IntPtr.Zero, "继续操作");
                                WindowsAPI.SendMessage(hbtn2, WindowsAPI.WM_SETTEXT, IntPtr.Zero, CommonMethod.NWebStartHour + "点开始");
                                WindowsAPI.SendMessage(hbtn3, WindowsAPI.WM_SETTEXT, IntPtr.Zero, "现在开始");
                            }
                        });
                        DialogResult res = MessageBox.Show(this, string.Format("确定开始抢票时间为：【{0}】？\n官网【" + CommonMethod.NWebStartHour + "点】开张，如果提前过多会影响正常订票！\n请确定是否继续？", dtRefreshTick.Value.ToString("HH:mm:ss")), "定时抢票设置", MessageBoxButtons.YesNoCancel, MessageBoxIcon.Question);
                        if (res == System.Windows.Forms.DialogResult.Cancel)
                            return dtTmp;
                        else if (res == System.Windows.Forms.DialogResult.No)
                        {
                            dtRefreshTick.Value = DateTime.Parse(DateTime.Now.ToString("yyyy-MM-dd " + CommonMethod.NWebStartHour.ToString("00") + ":00:00"));
                        }
                    }
                    dtTmp = DateTime.Parse(CommonString.serverTime.AddDays(CommonString.serverTime.Hour >= CommonMethod.NWebStartHour ? 1 : 0).ToString("yyyy-MM-dd") + dtRefreshTick.Value.ToString(" HH:mm:ss"));
                }
                else
                    dtTmp = DateTime.Parse(CommonString.serverTime.AddDays(CommonString.serverTime.Hour >= CommonMethod.NWebStartHour ? 1 : 0).ToString("yyyy-MM-dd " + CommonMethod.NWebStartHour.ToString("00") + ":00:00"));
            }
            else
            {
                if (dtRefreshTick.Checked)
                {
                    dtTmp = DateTime.Parse(string.Format("{0} {1}"
                        , CommonString.serverTime.ToString("yyyy-MM-dd"),
                        dtRefreshTick.Value.ToString("HH:mm:ss")));
                }
            }
            if (dtRefreshTick.Checked)
            {
                dtTmp = dtTmp.AddMilliseconds((double)nQueryMSecond.Value);
            }
            if (!isGetTask && isTickQuery && dtTmp <= CommonString.serverTime)
            {
                dtTmp = DateTime.MinValue;
                isTickQuery = false;
            }
            return dtTmp;
        }

        #endregion

        #region 同城相关

        private void lnkToday_LinkClicked(object sender, LinkLabelLinkClickedEventArgs e)
        {
            LinkLabel lnk = sender as LinkLabel;
            if (lnk == null || lnk.Tag == null)
                return;
            dtGoDate.Value = CommonString.serverTime.Date.AddDays(int.Parse(lnk.Tag.ToString()));
        }

        private void lnkYuShou_LinkClicked(object sender, LinkLabelLinkClickedEventArgs e)
        {
            try
            {
                dtGoDate.Value = CommonString.serverTime.Date.AddDays(CommonMethod.GetYuShouDate(CommonString.serverTime) - 1);
            }
            catch { }
        }

        private void btnPlus_Click(object sender, EventArgs e)
        {
            try
            {
                dtGoDate.Value = dtGoDate.Value.AddDays(1);
            }
            catch { }
        }

        private void btnJian_Click(object sender, EventArgs e)
        {
            try
            {
                dtGoDate.Value = dtGoDate.Value.AddDays(-1);
            }
            catch { }
        }

        private bool isAuto = false;
        private void btnChange_Click(object sender, EventArgs e)
        {
            string strTmp = cbxFrom.Text.Trim();
            isAuto = true;
            cbxFrom.Text = cbxTo.Text;
            isAuto = true;
            cbxTo.Text = strTmp;
            if (chkTC.Checked)
            {
                List<string> lstStart = GetlstStationByListView(lvStartN);
                List<string> lstEnd = GetlstStationByListView(lvEndN);
                BindListViewGroup(lvEndN, lstStart);
                BindListViewGroup(lvStartN, lstEnd);
            }
        }

        private void txtFrom_TxtChanged(object sender, TxtEvenargs e)
        {
            DropTextBox drp = sender as DropTextBox;
            if (drp == null)// || e.txt.Equals(cbxFrom.Text.Trim())
                return;
            if (isAuto)
            {
                isAuto = false;
            }
            else
            {
                string txt = e.txt.Trim(); //参数e中包含了textBox当前的文本内容  
                List<string> lstTmp = new List<string>();
                if (e.txt.Length >= drp.Minletters) //如果文本内容小于Minletters不查询数据库  
                {
                    try
                    {
                        var items = CommonString.Stations.Where(v =>
                            v.ShortCut.StartsWith(txt, StringComparison.OrdinalIgnoreCase)
                            || v.Name.StartsWith(txt, StringComparison.OrdinalIgnoreCase)
                            || v.Pinyin.StartsWith(txt, StringComparison.OrdinalIgnoreCase)
                            || v.Sipinyin.StartsWith(txt, StringComparison.OrdinalIgnoreCase)).ToList();
                        if (items != null && items.Count > 0)
                        {
                            foreach (TrainStation station in items)
                            {
                                lstTmp.Add(station.Name);
                            }
                        }
                    }
                    catch (Exception oe)
                    {
                        //MessageBox.Show("Test");
                    }
                }
                drp.DropItems = lstTmp.ToArray();//赋值给DropItems  
            }
            try
            {
                if (drp.Name != "cbxFrom")
                    return;
                string strTmp = CommonMethod.GetPreSellingTimeStr(cbxFrom.Text.Trim());
                lblTime.Text = string.Format("{0}起售", string.IsNullOrEmpty(strTmp) ? "--" : strTmp.ToString());
                decimal nMSecond = nQueryMSecond.Value;
                dtRefreshTick.Value = CommonMethod.GetPreSellingTimeDateByTime(strTmp, ref nMSecond, 10);
                nQueryMSecond.Value = nMSecond;
                if (dtRefreshTick.Checked)
                {
                    dtRefreshTick.Checked = false;
                }
            }
            catch (Exception oe)
            {
                Log.WriteError("txtChanged出错", oe);
                throw;
            }
        }

        private void chkTC_CheckedChanged(object sender, EventArgs e)
        {
            SetDefaultTongCheng();
        }

        private void SetDefaultTongCheng()
        {
            try
            {
                if (chkTC.Checked)
                {
                    if (!string.IsNullOrEmpty(cbxFrom.Text) || !string.IsNullOrEmpty(cbxTo.Text))
                    {
                        List<string> lstTC = new List<string>();
                        if (!string.IsNullOrEmpty(cbxFrom.Text))
                        {
                            lstTC = CommonMethod.GetGroupByValue(cbxFrom.Text);
                            //lstTm.AddRange(CommonString.LastFromTC.Split(new string[] { "|" }, StringSplitOptions.RemoveEmptyEntries));
                            BindListViewGroup(lvStartN, lstTC, cbxFrom.Text.Trim());
                        }
                        if (!string.IsNullOrEmpty(cbxTo.Text))
                        {
                            lstTC = CommonMethod.GetGroupByValue(cbxTo.Text);
                            //lstTm.AddRange(CommonString.LastToTC.Split(new string[] { "|" }, StringSplitOptions.RemoveEmptyEntries));
                            BindListViewGroup(lvEndN, lstTC, cbxTo.Text.Trim());
                        }
                    }
                }
                else
                {
                    BindListViewGroup(lvStartN, null);
                    BindListViewGroup(lvEndN, null);
                }
            }
            catch { }
        }

        private void btnStart_Click(object sender, EventArgs e)
        {
            Button btn = (sender as Button);
            if (btn != null)
            {
                frmTC frm = new frmTC();
                frm.LstTC = new List<string>();
                frm.StrExcept = cbxFrom.Text.Trim();
                ListBox lvStation = new ListBox();
                if (btn.Tag != null)
                {
                    lvStation = lvStartN;
                }
                else
                {
                    lvStation = lvEndN;
                }
                frm.LstTC = GetlstStationByListView(lvStation);
                if (frm.ShowDialog(this) == System.Windows.Forms.DialogResult.OK)
                {
                    BindListViewGroup(lvStation, frm.LstTC);
                }
            }
        }

        private List<string> GetlstStationByListView(ListBox lvStation)
        {
            List<string> LstTC = new List<string>();
            if (chkTC.Checked && lvStation.Items.Count > 0)
            {
                foreach (object item in lvStation.Items)
                {
                    if (!LstTC.Contains(item.ToString()))
                        LstTC.Add(item.ToString());
                }
            }
            return LstTC;
        }

        private void BindListViewGroup(ListBox lvStation, List<string> lstStation, string strExp = "")
        {
            try
            {
                lvStation.DataSource = string.IsNullOrEmpty(strExp) ? lstStation : lstStation.FindAll(p => !p.Equals(strExp));
                //lvStation.Items.Clear();
                //foreach (string item in lstStation)
                //{
                //    lvStation.Items.Add(item);
                //}
            }
            catch (Exception ex)
            {
                Console.WriteLine(ex.Message);
            }
        }
        #endregion

        #region 乘客管理

        private bool PreparePassenger()
        {
            bool result = true;
            foreach (DataGridViewRow row in dgNoTaskUser.Rows)
            {
                if (row.Cells[0].EditedFormattedValue.ToString().ToLower() == "true")
                {
                    var user = LstUser.Find(p => row.Cells[1].Value != null && p.StrNowUserName.Equals(row.Cells[1].Value.ToString()));
                    if (user == null)
                    {
                        Console.WriteLine("");
                        continue;
                    }
                    var lstTmp = user.LstTmpPassage.FindAll(p => p.IsChecked);
                    foreach (var item in lstTmp)
                    {
                        //姓名,类别,席别,证件,证件号
                        // icon,cmbUser, txtName, cmbType, cmbCardType, txtCardNo, cmbXiBie
                        if (string.IsNullOrEmpty(item.IDCard) || string.IsNullOrEmpty(item.Name))
                        {
                            MessageBox.Show(this, string.Format("账户【{0}】中乘客信息设置不完整，请修改后重试！", user.StrNowUserName), "提示", MessageBoxButtons.OK, MessageBoxIcon.Error);
                            result = false;
                            break;
                        }
                        if (item.Card.Key.StartsWith("二代") && !CommonMethod.CheckCardNO(item.IDCard))
                        {
                            MessageBox.Show(this, string.Format("账户【{1}】中身份证号【{0}】错误，请修正后重试！", item.IDCard, user.StrNowUserName), "提示", MessageBoxButtons.OK, MessageBoxIcon.Error);
                            result = false;
                            break;
                        }
                        if (item.TicType.Key.Contains("儿童") && !lstTmp.Exists(pp => pp.IDCard.Equals(item.IDCard)))
                        {
                            MessageBox.Show(this, string.Format("账户【{1}】中儿童票-{0}缺少对应的成人信息，请修正后重试！", item.IDCard, user.StrNowUserName), "提示", MessageBoxButtons.OK, MessageBoxIcon.Error);
                            result = false;
                            break;
                        }
                    }
                    //购买残疾军人（伤残警察）优待票需使用中华人民共和国居民身份证！
                    if (result && lstTmp != null && lstTmp.Count > 0)
                    {
                        if (lstTmp.Exists(p => p.Card.Key == "护照" && p.TicType.Key.StartsWith("残军")))
                        {
                            MessageBox.Show(this, string.Format("护照不能购买残军票，请修正后重试！"), "提示", MessageBoxButtons.OK, MessageBoxIcon.Error);
                            result = false;
                        }
                    }
                }
            }
            return result;
        }

        private void DetermineCall(MethodInvoker method)
        {
            if (InvokeRequired)
            {
                //BeginInvoke(method);
                Invoke(method);
            }
            else
            {
                method();
            }
        }

        private void BindPassengers(string strNowUser, bool isRefresh = false)
        {
            ThreadPool.QueueUserWorkItem((object obj) =>
            {
                List<Passenger> lstPassenger = new List<Passenger>();
                try
                {
                    lvPassenger.Items.Clear();
                    lvPassenger.Groups.Clear();
                }
                catch
                {
                    // ignored
                }

                var user = LstUser.Find(p => p.StrNowUserName == strNowUser);
                if (user == null)
                {
                    return;
                }
                isOnLoadPassenger = true;
                user.GetAllPassenger(isRefresh);

                DetermineCall(() =>
                {
                    try
                    {
                        lvPassenger.Items.Clear();
                        lvPassenger.Groups.Clear();
                    }
                    catch
                    {
                        // ignored
                    }

                    ListViewGroup lvUser = new ListViewGroup();  //创建分组  
                    lvUser.Header = user.StrNowUserName + "(共" + user.LstTmpPassage.Count + "个)";  //设置组的标题。  
                    lvUser.HeaderAlignment = HorizontalAlignment.Left;   //设置组标题文本的对齐方式。（默认为Left） 
                    lvPassenger.Groups.Add(lvUser);    //把分组添加到listview中
                    lstPassenger = user.LstTmpPassage.OrderByDescending(p => p.IsChecked).ToList();
                    lstPassenger.ForEach(delegate(Passenger pInfo)
                    {
                        ListViewItem item = new ListViewItem(new string[]
                { 
                    pInfo.Name
                    , pInfo.TicType.Key
                    , pInfo.GetAllTypeName()
                    , string.IsNullOrEmpty(pInfo.Card.Key)?"": pInfo.Card.Key.Replace("身份证","").Replace("通行证","")
                    , pInfo.IDCard
                    ,string.IsNullOrEmpty(pInfo.Status)?"--":pInfo.Status
                }, 0, lvUser);
                        pInfo.User = user.StrNowUserName;
                        item.Tag = pInfo.User;
                        item.Checked = pInfo.IsChecked;
                        lvPassenger.Items.Add(item);
                    });
                });
                isOnLoadPassenger = false;
            });
        }

        private void btnImport_Click(object sender, EventArgs e)
        {
            var user = GetNowSelectedUser();
            if (user == null)
                return;
            if (open.ShowDialog(this) == System.Windows.Forms.DialogResult.OK)
            {
                string msg = string.Empty;
                try
                {
                    List<Passenger> lstTmp = NewTicketHelper.LoadPassFormFile(open.FileName, false, ref msg);
                    if (lstTmp != null && lstTmp.Count > 0)
                    {
                        if (user.LstTmpPassage == null || user.LstTmpPassage.Count <= 0)
                        {
                            user.LstTmpPassage = lstTmp;
                        }
                        else
                        {
                            if (MessageBox.Show(this, "是否覆盖该账号目前已经设置过的乘客？", "提示", MessageBoxButtons.YesNo, MessageBoxIcon.Information) == System.Windows.Forms.DialogResult.Yes)
                                user.LstTmpPassage = new List<Passenger>();
                            foreach (var pass in lstTmp)
                            {
                                pass.IsChecked = true;
                                pass.User = user.StrNowUserName;
                                if (!user.LstTmpPassage.Exists(item => item.IDCard == pass.IDCard
                                        && item.TicType.Key.Equals(pass.TicType.Key)))
                                {
                                    user.LstTmpPassage.Add(pass);
                                }
                            }
                            user.LstTmpPassage = user.LstTmpPassage;
                        }
                    }
                }
                catch { }
                if (!string.IsNullOrEmpty(msg))
                    MessageBox.Show(this, "导入过程中发生错误，\n" + msg + "\n请检查文件内容是否符合\n【姓名,证件类型,证件号,手机号,票种,席别】\n确认无误后重试！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Error);
                else
                    ThreadPool.QueueUserWorkItem((object obj) =>
                    {
                        user.SavePassengerToWeb();
                        BindPassengers(user.StrNowUserName, true);
                    });
            }
        }

        private void btnClear_Click(object sender, EventArgs e)
        {
            var user = GetNowSelectedUser();
            if (user == null)
                return;
            if (user.LstTmpPassage != null && user.LstTmpPassage.Count > 0)
            {
                user.LstTmpPassage.ForEach(p => { p.IsChecked = false; });
            }
            BindPassengers(user.StrNowUserName);
        }

        private void btnPaste_Click(object sender, EventArgs e)
        {
            var user = GetNowSelectedUser();
            if (user == null)
                return;
            try
            {
                string strText = Clipboard.GetText();
                if (string.IsNullOrEmpty(strText))
                {
                    MessageBox.Show(this, "请先将乘客信息复制到粘贴板后重试！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }
                strText = strText.Replace("，", ",");
                string strSpilt = strText.Contains("，") ? "，" : (strText.Contains(",") ? "," : (strText.Contains(" ") ? " " : ""));
                if (string.IsNullOrEmpty(strSpilt))
                {
                    MessageBox.Show(this, "乘客信息中姓名与身份证信息需以逗号隔开,请检查格式后重试！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }
                StringBuilder sb = new StringBuilder();
                List<Passenger> lstTmp = new List<Passenger>();
                string[] strTmp = strText.Replace("\r", "").Replace(" ", "").Replace("　", "").Split(new string[] { "\n" }, StringSplitOptions.RemoveEmptyEntries);
                foreach (string str in strTmp)
                {
                    string[] strPP = str.Trim().Split(new string[] { strSpilt }, StringSplitOptions.RemoveEmptyEntries);
                    if (strPP == null || strPP.Length < 2)
                    {
                        sb.AppendFormat("“{0}”行分隔符不正确！\n", str.Trim());
                        continue;
                    }
                    Passenger pss = new Passenger();
                    pss.Name = strPP[0].Trim();
                    pss.IDCard = strPP[1].Trim();
                    //pss.SetCardType("二代身份证");
                    pss.SetUserType("成人票");
                    pss.IsChecked = true;
                    pss.User = user.StrNowUserName;
                    if (strPP.Length > 2)
                    {
                        pss.SetSeatTypes(strPP[2].Trim());
                    }
                    else
                        pss.SetSeatType("硬卧");
                    if (!lstTmp.Contains(pss) && !user.LstTmpPassage.Exists(passs => passs.IDCard.Equals(pss.IDCard))
                        && !user.LstTmpPassage.Exists(passs => passs.IDCard.Equals(pss.IDCard)))
                    {
                        lstTmp.Add(pss);
                    }
                }
                if (lstTmp.Count > 0)
                {
                    if (MessageBox.Show(this, "是否覆盖该账号目前已经设置过的乘客？", "提示", MessageBoxButtons.YesNo, MessageBoxIcon.Information) == System.Windows.Forms.DialogResult.Yes)
                        user.LstTmpPassage = new List<Passenger>();
                    user.LstTmpPassage.AddRange(lstTmp);
                    user.LstTmpPassage = user.LstTmpPassage;
                    MessageBox.Show(this, string.Format("成功导入{0}条数据！", lstTmp.Count), "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);

                    ThreadPool.QueueUserWorkItem((object obj) =>
                    {
                        user.SavePassengerToWeb();
                        BindPassengers(user.StrNowUserName, true);
                    });
                }
            }
            catch (Exception oe)
            {
                Log.WriteError("btnPaste_Click出错", oe);
            }
        }

        private void 删除ToolStripMenuItem_Click(object sender, EventArgs e)
        {
            var user = GetNowSelectedUser();
            if (user == null)
                return;
            if (lvPassenger.SelectedItems != null && lvPassenger.SelectedItems.Count > 0)
            {
                if (user.IsLogined || user.IsMobileLogined)
                {
                    string strMSG = "";
                    StringBuilder sbDelErrorMSG = new StringBuilder();
                    var result = false;
                    foreach (ListViewItem item in lvPassenger.SelectedItems)
                    {
                        var pass = user.LstTmpPassage.FirstOrDefault(p => p.IDCard.Equals(item.SubItems[4].Text) && p.Name.Equals(item.SubItems[0].Text));
                        if (pass != null)
                        {
                            result = user.DelePassenger(pass, ref strMSG);
                            user.LstTmpPassage.Remove(pass);
                        }
                        if (!result && !string.IsNullOrEmpty(strMSG))
                            sbDelErrorMSG.AppendLine(strMSG);
                    }
                    //user.LstTmpPassage = user.LstTmpPassage;

                    if (string.IsNullOrEmpty(sbDelErrorMSG.ToString()))
                    {
                        sbDelErrorMSG.Append("删除成功！");
                    }
                    if (!string.IsNullOrEmpty(sbDelErrorMSG.ToString()))
                    {
                        MessageBox.Show(this, sbDelErrorMSG.ToString(), "删除乘客", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                    BindPassengers(user.StrNowUserName, true);
                }
                else
                {
                    MessageBox.Show(this, "当前账号未登录，请登录后重试！", "温馨提示", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
        }

        private void 删除所有ToolStripMenuItem_Click(object sender, EventArgs e)
        {
            var user = GetNowSelectedUser();
            if (user == null)
                return;
            if (user.IsLogined)
            {
                var sbDelErrorMSG = "";
                try
                {
                    tsmDeleAll.Enabled = false;

                    sbDelErrorMSG = user.DeleAllPassenger();
                    if (string.IsNullOrEmpty(sbDelErrorMSG))
                    {
                        sbDelErrorMSG = "删除成功！";
                    }
                }
                catch (Exception oe)
                {
                    sbDelErrorMSG = "清空乘客出错，详细信息：\n" + oe.Message;
                }
                finally
                {
                    tsmDeleAll.Enabled = true;
                }
                if (!string.IsNullOrEmpty(sbDelErrorMSG))
                {
                    MessageBox.Show(this, sbDelErrorMSG, "删除所有乘客", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                BindPassengers(user.StrNowUserName, true);
            }
            else
            {
                MessageBox.Show(this, "当前账号未登录，请登录后重试！", "温馨提示", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void 修改ToolStripMenuItem_Click(object sender, EventArgs e)
        {
            var user = GetNowSelectedUser();
            if (user == null)
                return;
            if (lvPassenger.SelectedItems != null && lvPassenger.SelectedItems.Count > 0)
            {
                FormPassenger pass = new FormPassenger();
                pass.NowPassenger = new Passenger();
                pass.NowPassenger.Name = lvPassenger.SelectedItems[0].SubItems[0].Text;
                pass.NowPassenger.IDCard = lvPassenger.SelectedItems[0].SubItems[4].Text;
                pass.NowPassenger.SetUserType(lvPassenger.SelectedItems[0].SubItems[1].Text);
                pass.NowPassenger.SetCardType(lvPassenger.SelectedItems[0].SubItems[3].Text);
                string strOldIDCard = pass.NowPassenger.IDCard;
                //pass.NowPassenger.SetSeatTypes(pass.NowPassenger.Seat.Key);
                if (pass.ShowDialog(this) == System.Windows.Forms.DialogResult.Yes)
                {
                    pass.NowPassenger.IsChecked = true;
                    try
                    {
                        ////姓名,类别,席别,证件,证件号
                        //lvPassenger.SelectedItems[0].SubItems[0].Text = pass.NowPassenger.Name;
                        //lvPassenger.SelectedItems[0].SubItems[1].Text = pass.NowPassenger.TicType.Key;
                        //lvPassenger.SelectedItems[0].SubItems[2].Text = pass.NowPassenger.GetAllTypeName();
                        //lvPassenger.SelectedItems[0].SubItems[3].Text = pass.NowPassenger.Card.Key;
                        //lvPassenger.SelectedItems[0].SubItems[4].Text = pass.NowPassenger.IDCard;
                        user.LstTmpPassage.RemoveAll(pp => pp.IDCard.Equals(strOldIDCard));
                        user.LstTmpPassage.Add(pass.NowPassenger);
                        user.LstTmpPassage = user.LstTmpPassage;
                    }
                    catch { }
                    //lvPassenger_ItemChecked(sender, null);
                    BindPassengers(dgNoTaskUser.SelectedRows[0].Cells[1].Value.ToString());
                }
            }
        }

        private void 添加ToolStripMenuItem_Click(object sender, EventArgs e)
        {
            var user = GetNowSelectedUser();
            if (user == null)
                return;
            FormPassenger pass = new FormPassenger();
            if (pass.ShowDialog(this) == System.Windows.Forms.DialogResult.Yes)
            {
                pass.NowPassenger.IsChecked = true;
                if (user.LstTmpPassage.Exists(pp => pp.IDCard.Equals(pass.NowPassenger.IDCard)))
                {
                    user.LstTmpPassage.Find(pp => pp.IDCard.Equals(pass.NowPassenger.IDCard)).IDCard = pass.NowPassenger.IDCard;
                    user.LstTmpPassage.Find(pp => pp.IDCard.Equals(pass.NowPassenger.IDCard)).Card = pass.NowPassenger.Card;
                    user.LstTmpPassage.Find(pp => pp.IDCard.Equals(pass.NowPassenger.IDCard)).Mobile = pass.NowPassenger.Mobile;
                    user.LstTmpPassage.Find(pp => pp.IDCard.Equals(pass.NowPassenger.IDCard)).Name = pass.NowPassenger.Name;
                    user.LstTmpPassage.Find(pp => pp.IDCard.Equals(pass.NowPassenger.IDCard)).IsChecked = pass.NowPassenger.IsChecked;
                }
                else
                {
                    user.LstTmpPassage.Add(pass.NowPassenger);
                }
                user.LstTmpPassage = user.LstTmpPassage;

                ThreadPool.QueueUserWorkItem((object obj) =>
                {
                    user.SavePassengerToWeb();
                    BindPassengers(user.StrNowUserName, true);
                });
            }
        }

        private void btnPassenger_Click(object sender, EventArgs e)
        {
            try
            {
                if (!PreparePassenger())
                {
                    return;
                }
                if (!CommonMethod.isCanOperate(true, false))
                {
                    MessageBox.Show(this, "官网已打烊，明天再来吧！", "保存乘客到官网");
                    return;
                }
                btnPassenger.Enabled = false;
                bgSaveWeb.RunWorkerAsync();
            }
            catch { }
        }

        private void bgSaveWeb_DoWork(object sender, DoWorkEventArgs e)
        {
            try
            {
                Parallel.ForEach<UserEntity>(LstUser, user =>
                {
                    user.SavePassengerToWeb();
                });
            }
            catch (AggregateException oe)
            {
                foreach (Exception item in oe.InnerExceptions)
                {
                    Log.WriteError("bgSaveWeb_DoWork异常", item);
                }
            }
            catch { }
        }

        private void bgSaveWeb_RunWorkerCompleted(object sender, RunWorkerCompletedEventArgs e)
        {
            MessageBox.Show("保存乘客信息完毕！");
            btnPassenger.Enabled = true;
        }

        private void btnTest_Click(object sender, EventArgs e)
        {
            if (!PreparePassenger())
            {
                return;
            }
            new System.Threading.Thread(delegate()
            {
                try
                {
                    btnTest.Enabled = false;
                    btnTest.Text = "...";
                    string strMSG = "";
                    foreach (var user in LstUser)
                    {
                        if (!user.IsLogined && !user.IsMobileLogined)
                        {
                            continue;
                        }
                        if (user.LstTmpPassage == null || user.LstTmpPassage.Count <= 0)
                        {
                            continue;
                        }
                        user.LstNetPassage = user.GetNetPassenger() ?? new List<Passenger>();
                        if (user.LstNetPassage != null)
                        {
                            foreach (var pss in user.LstTmpPassage.FindAll(p => p.IsChecked))
                            {
                                if (!user.LstNetPassage.Exists(p => p.IDCard.Equals(pss.IDCard)))
                                {
                                    List<Passenger> lstTT = null;
                                    lstTT = user.GetNetPassenger(true);
                                    if (lstTT != null && lstTT.Count > 0)
                                        user.LstNetPassage = lstTT;
                                }
                                if (!user.LstNetPassage.Exists(p => p.IDCard.Equals(pss.IDCard)))
                                {
                                    if (MessageBox.Show(this, string.Format("乘客{0}-{1}不是账户{2}的常用联系人，是否立即添加？", pss.Name, pss.IDCard, user.StrNowUserName), "提示", MessageBoxButtons.YesNo, MessageBoxIcon.Question) != System.Windows.Forms.DialogResult.Yes)
                                        return;
                                    else
                                    {
                                        bool tmpBool = false;
                                        if (user.IsLogined)
                                            tmpBool = NewTicketHelper.AddPassengers(pss, ref strMSG, user.GetComCookie(), user.StrNowComIp);
                                        else if (user.IsMobileLogined)
                                            tmpBool = MHelper.AddPassengers(user.MobileUser, pss, ref strMSG);
                                        if (!tmpBool)
                                        {
                                            MessageBox.Show(this, string.Format("添加乘客{0}-{1}到账户{2}失败！\n错误信息如下：\n{3}", pss.Name, pss.IDCard, user.StrNowUserName, strMSG), "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                                            return;
                                        }
                                    }
                                }
                                else
                                {
                                    pss.Status = user.LstNetPassage.Find(p => p.IDCard.Equals(pss.IDCard)).Status;
                                    //strMSG = pss.Status;
                                    if (!new List<string> { "已通过", "请报验", "预通过" }.Contains(pss.Status))
                                    {
                                        MessageBox.Show(this, string.Format("乘客{0}-{1}的状态为：{2}，不能正常办理网上购票、改签、退票等业务。", pss.Name, pss.IDCard, pss.Status), "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                                        return;
                                    }
                                }
                            }
                        }
                    }
                    MessageBox.Show(this, string.Format("校验全部通过，未分配乘客请先点击[保存到官网]！"), "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                catch (Exception oe)
                {
                }
                finally
                {
                    btnTest.Enabled = true;
                    btnTest.Text = "校验";
                }
            }) //{ IsBackground = true, Priority = ThreadPriority.Highest }
            .Start();
        }

        private void cmsTicketTypes_DropDownOpening(object sender, EventArgs e)
        {
            List<string> lstTmp = new List<string>();
            if (lvPassenger.SelectedItems != null && lvPassenger.SelectedItems.Count == 1)
            {
                lstTmp.AddRange(lvPassenger.SelectedItems[0].SubItems[2].Text.Split(new string[] { "|" }, StringSplitOptions.RemoveEmptyEntries));
            }
            foreach (ToolStripMenuItem item in cmsTicketTypes.DropDownItems)
            {
                if (lstTmp.Contains(item.Text))
                {
                    item.Checked = true;
                }
                else
                {
                    item.Checked = false;
                }
            }
        }

        private void cmbSeat_SelectedValueChanged(object sender, EventArgs e)
        {
            var uses = GetAllSelectedUsers();
            if (uses == null || uses.Count <= 0)
                return;
            string strNowSelectUser = "";
            if (dgNoTaskUser.SelectedRows.Count >= 0)
                strNowSelectUser = dgNoTaskUser.SelectedRows[0].Cells[1].Value.ToString();
            uses.ForEach(user =>
            {
                user.LstTmpPassage.ForEach(p =>
                {
                    p.SetSeatTypes(cmbSeat.Value);
                });
                if (strNowSelectUser.Equals(user.StrNowUserName))
                    BindPassengers(user.StrNowUserName);
            });
        }

        private bool isOnLoadPassenger = false;

        private void lvPassenger_ItemChecked(object sender, ItemCheckedEventArgs e)
        {
            if (!isOnLoadPassenger && dgNoTaskUser.SelectedRows != null && dgNoTaskUser.SelectedRows.Count > 0)
            {
                var strTmp = dgNoTaskUser.SelectedRows[0].Cells[1].Value.ToString();
                var user = LstUser.Find(p => p.StrNowUserName.Equals(strTmp));
                if (user != null)
                {
                    foreach (var item in lvPassenger.Items)
                    {
                        var tmp = ((ListViewItem)item);
                        user.LstTmpPassage.Find(p =>
                           (!string.IsNullOrEmpty(p.IDCard) && p.IDCard.Equals(tmp.SubItems[4].Text))
                           || (!string.IsNullOrEmpty(p.Name) && p.Name.Equals(tmp.SubItems[0].Text))
                            ).IsChecked = tmp.Checked;
                    }
                    user.LstTmpPassage = user.LstTmpPassage;
                    if (user.LstTmpPassage != null && user.LstTmpPassage.FindAll(p => p.IsChecked).Count > 5)
                    {
                        //user.LstTmpPassage.RemoveAll(p => p.IDCard.Equals(e.Item.SubItems[4].Text));
                        //while (user.LstTmpPassage.Count > 5)
                        //{
                        //    user.LstTmpPassage.RemoveAt(user.LstTmpPassage.Count - 1);
                        //}
                        MessageBox.Show(this, "最多可选择5个乘客，请先去掉不需要的乘客！", "任务提示");
                        //BindPassengers(user.StrNowUserName);
                    }
                }
            }
        }
        void ticketType_Click(object sender, EventArgs e)
        {
            ToolStripMenuItem seleItem = (ToolStripMenuItem)sender;
            if (seleItem != null)
            {
                if (dgNoTaskUser.SelectedRows != null && dgNoTaskUser.SelectedRows.Count > 0)
                {
                    var strUser = dgNoTaskUser.SelectedRows[0].Cells[1].Value.ToString();
                    var user = LstUser.Find(p => p.StrNowUserName.Equals(strUser));
                    if (user != null)
                    {
                        seleItem.Checked = !seleItem.Checked;
                        if (lvPassenger.SelectedItems != null && lvPassenger.SelectedItems.Count > 0)
                        {
                            string strTmp = "";
                            foreach (ToolStripMenuItem item in cmsTicketTypes.DropDownItems)
                            {
                                if (item.Checked)
                                {
                                    strTmp += string.Format("{0}|", item.Text);
                                }
                            }
                            strTmp = strTmp.TrimEnd('|');
                            foreach (ListViewItem lv in lvPassenger.SelectedItems)
                            {
                                lv.SubItems[2].Text = strTmp;
                                //姓名,类别,席别,证件,证件号
                                user.LstTmpPassage.Find(p => p.IDCard.Equals(lv.SubItems[4].Text)).SetSeatTypes(strTmp);
                            }
                            //BindPassengers(user.StrNowUserName);
                        }
                    }
                }
            }
            //if (table.SelectedItems != null && table.SelectedItems.Length > 0)
            //    table.SelectedItems[0].Cells[4].Text = item.Text;
        }

        void userType_Click(object sender, EventArgs e)
        {
            if (dgNoTaskUser.SelectedRows != null && dgNoTaskUser.SelectedRows.Count > 0)
            {
                var strUser = dgNoTaskUser.SelectedRows[0].Cells[1].Value.ToString();
                var user = LstUser.Find(p => p.StrNowUserName.Equals(strUser));
                if (user != null)
                {
                    ToolStripMenuItem item = (ToolStripMenuItem)sender;
                    if (item.Text.StartsWith("全部"))
                    {
                        if (lvPassenger.Items != null && lvPassenger.Items.Count > 0)
                        {
                            foreach (ListViewItem lv in lvPassenger.Items)
                            {
                                //姓名,类别,席别,证件,证件号
                                lv.SubItems[1].Text = item.Text.Replace("全部", "");
                                user.LstTmpPassage.Find(p => p.IDCard.Equals(lv.SubItems[4].Text)).SetUserType(item.Text.Replace("全部", ""));
                            }
                        }
                    }
                    else
                    {
                        if (lvPassenger.SelectedItems != null && lvPassenger.SelectedItems.Count > 0)
                        {
                            foreach (ListViewItem lv in lvPassenger.SelectedItems)
                            {
                                //姓名,类别,席别,证件,证件号
                                lv.SubItems[1].Text = item.Text;
                                user.LstTmpPassage.Find(p => p.IDCard.Equals(lv.SubItems[4].Text)).SetUserType(item.Text);
                            }
                        }
                    }
                    //BindPassengers(user.StrNowUserName);
                }
            }
        }

        void userCardType_Click(object sender, EventArgs e)
        {
            if (dgNoTaskUser.SelectedRows != null && dgNoTaskUser.SelectedRows.Count > 0)
            {
                var strUser = dgNoTaskUser.SelectedRows[0].Cells[1].Value.ToString();
                var user = LstUser.Find(p => p.StrNowUserName.Equals(strUser));
                if (user != null)
                {
                    ToolStripMenuItem item = (ToolStripMenuItem)sender;
                    if (item.Text.StartsWith("全部"))
                    {
                        if (lvPassenger.Items != null && lvPassenger.Items.Count > 0)
                        {
                            foreach (ListViewItem lv in lvPassenger.Items)
                            {
                                //姓名,类别,席别,证件,证件号
                                lv.SubItems[3].Text = item.Text.Replace("全部", "");
                                user.LstTmpPassage.Find(p => p.IDCard.Equals(lv.SubItems[4].Text)).SetCardType(item.Text.Replace("全部", ""));
                            }
                        }
                    }
                    else
                    {
                        if (lvPassenger.SelectedItems != null && lvPassenger.SelectedItems.Count > 0)
                        {
                            foreach (ListViewItem lv in lvPassenger.SelectedItems)
                            {
                                //姓名,类别,席别,证件,证件号
                                lv.SubItems[3].Text = item.Text;
                                user.LstTmpPassage.Find(p => p.IDCard.Equals(lv.SubItems[4].Text)).SetCardType(item.Text);
                            }
                        }
                    }
                }
            }
        }

        #endregion

        #region 查票

        private List<TicketEntity> _LeftTicketStatus = new List<TicketEntity>();

        private void btnQuery_Click(object sender, EventArgs e)
        {
            if (!btnQuery.Enabled)
                return;
            try
            {
                bgQuery.RunWorkerAsync();
            }
            catch (Exception oe)
            {
                Log.WriteError("btnQuery_Click出错", oe);
            }
        }

        private void bgQuery_DoWork(object sender, DoWorkEventArgs e)
        {
            try
            {
                QueryLeftTicketStatus();
            }
            catch { }
        }

        private void chkAllTCode_CheckedChanged(object sender, EventArgs e)
        {
            var lstTmp = new List<TicketEntity>();
            List<string> lstNo = new List<string>();
            if (_LeftTicketStatus != null && _LeftTicketStatus.Count > 0)
            {
                lstTmp.AddRange(_LeftTicketStatus.ToArray());
                try
                {
                    if (!string.IsNullOrEmpty(txtTrainNo.Text))
                    {
                        lstNo.AddRange(txtTrainNo.Text.ToUpper().Replace(" ", "").Replace("　", "").Trim().Split(new string[] { "," }, StringSplitOptions.RemoveEmptyEntries));
                        lstNo.RemoveAll(p => !lstTmp.Exists(q => p.Equals(q.TrainNo)));
                        txtTrainNo.Text = string.Join(",", lstNo.ToArray());
                        if (lstNo != null && lstNo.Count > 0)
                            lstTmp.RemoveAll(ll => !lstNo.Exists(nn => nn.Equals(ll.TrainNo)));
                    }
                }
                catch (Exception oe)
                {
                    Log.WriteError("chkAllTCode出错", oe);
                }
                List<string> lstType = GetNoCheckedType();
                if (lstType != null && lstType.Count > 0)
                {
                    foreach (string str in lstType)
                    {
                        switch (str)
                        {
                            case "D":
                                lstTmp.RemoveAll(p => p.TrainNo.StartsWith("C") || p.TrainNo.StartsWith("D") || p.TrainNo.StartsWith("G"));
                                break;
                            case "K":
                                lstTmp.RemoveAll(p => p.TrainNo.StartsWith("K"));
                                break;
                            case "Z":
                                lstTmp.RemoveAll(p => p.TrainNo.StartsWith("Z"));
                                break;
                            case "T":
                                lstTmp.RemoveAll(p => p.TrainNo.StartsWith("T"));
                                break;
                            case "QT":
                                lstTmp.RemoveAll(p => !p.TrainNo.StartsWith("C") && !p.TrainNo.StartsWith("D") && !p.TrainNo.StartsWith("G")
                                    && !p.TrainNo.StartsWith("K") && !p.TrainNo.StartsWith("Z") && !p.TrainNo.StartsWith("T"));
                                break;
                            default:
                                break;
                        }
                    }
                }
            }
            _BindingLeftTicketStatus = new BindingList<TicketEntity>(lstTmp);
            dgQuery.DataSource = _BindingLeftTicketStatus;
            _BindingLeftTicketStatus.ResetBindings();
            if (lstNo != null && lstNo.Count > 0 && lstTmp != null && lstTmp.Count > 0)
            {
                foreach (DataGridViewRow item in dgQuery.Rows)
                {
                    if (lstNo.Contains(item.Cells[1].Value.ToString().ToUpper()))
                    {
                        item.Cells[0].Value = true;
                    }
                }
            }
        }

        private void txtTrainNo_TextChanged(object sender, EventArgs e)
        {
            if (string.IsNullOrEmpty(txtTrainNo.Text.Trim()))
            {
                chkAllTCode_CheckedChanged(sender, e);
            }
        }

        private string strCookie = "";

        private void QueryLeftTicketStatus()
        {
            if (string.IsNullOrEmpty(cbxFrom.Text))
            {
                cbxFrom.Focus();
                return;
            }
            if (string.IsNullOrEmpty(cbxTo.Text))
            {
                cbxTo.Focus();
                return;
            }
            var fromSation = CommonString.Stations.FirstOrDefault(v => v.Name == cbxFrom.Text || v.ShortCut == cbxFrom.Text);
            if (fromSation == null)
            {
                cbxFrom.Text = "";
                cbxFrom.Focus();
                return;
            }
            var toStation = CommonString.Stations.FirstOrDefault(v => v.Name == cbxTo.Text || v.ShortCut == cbxTo.Text);
            if (toStation == null)
            {
                cbxTo.Text = "";
                cbxTo.Focus();
                return;
            }
            if (cbxFrom.Text != fromSation.Name)
            {
                cbxFrom.Text = fromSation.Name;
            }
            if (cbxTo.Text != toStation.Name)
            {
                cbxTo.Text = toStation.Name;
            }
            AttentionItem nowAttention = new AttentionItem();
            nowAttention.FromStation = fromSation;
            nowAttention.ToStation = toStation;
            nowAttention.Date = dtGoDate.Value;
            nowAttention.PurposeCode = rdoAdult.Checked ? CommonString.strAdultType : CommonString.strStudentType;
            //if (NowQueryTrain != null && !string.IsNullOrEmpty(NowQueryTrain.TrainID))
            //{
            //    nowAttention.TrainID = NowQueryTrain.TrainID;
            //    nowAttention.TrainNo = NowQueryTrain.TrainNo;
            //}
            string strMSG = string.Empty;
            btnQuery.Text = "查询中…";
            btnQuery.Enabled = false;
            try
            {
                _LeftTicketStatus.Clear();
                bool isCache = false;
                List<TicketEntity> lstTmp = new List<TicketEntity>();
                bool isNoValidate = false;
                string strIP = CommonString.StrLocalComIP;

                strCookie = CommonMethod.GetCompleteCookie(false, strCookie, nowAttention);

                lstTmp = NewTicketHelper.QueryTmpTicket(strIP, nowAttention, ref strCookie, out strMSG, out isCache, ref isNoValidate);

                _LeftTicketStatus.AddRange(lstTmp);
                if (!string.IsNullOrEmpty(strMSG))
                {
                    if (strMSG.Contains("空") || strMSG.Contains("失败") || strMSG.Contains("网络"))
                    {
                        //if (strIP.Equals(CommonString.StrLocalComIP) && strMSG.Contains("网络"))
                        //    CommonString.StrLocalComIP = IPHelper.AutoChangeIP(false, CommonString.StrLocalComIP);
                        if (!CommonString.isDebug)
                            strMSG = "";
                        else
                            strMSG += " IP:" + strIP;
                    }
                }
                lstTmp = null;
                strMSG = null;
            }
            catch (Exception ex)
            {
                Log.WriteError("QueryLeftTicketStatus出错", ex);
            }
            fromSation = null;
            toStation = null;
            nowAttention = null;
        }

        private void bgQuery_RunWorkerCompleted(object sender, RunWorkerCompletedEventArgs e)
        {
            try
            {
                chkAllTCode_CheckedChanged(sender, null);
                if (dgQuery != null && dgQuery.Rows.Count <= 0)
                {
                    MessageBox.Show(this, "竟然没查到票，可能是官网正在抽风！\n如果知道要定票的车次，直接在车次中输入即可！\n注:手动输的车次一定要确保正确，不用区分大小写！", "查票提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                //if (_BindingLeftTicketStatus == null)
                //    _BindingLeftTicketStatus = new BindingList<TicketEntity>(_LeftTicketStatus);
                //_BindingLeftTicketStatus.ResetBindings();
            }
            catch { }
            btnQuery.Enabled = true;
            btnQuery.Text = "查询(&S)";
        }

        void dvLog_DataError(object sender, DataGridViewDataErrorEventArgs e)
        {
            //if (CommonString.isDebug)
            //{
            //    MessageBox.Show(e.Context.ToString());
            //}
        }

        private void dgQuery_CellContentClick(object sender, DataGridViewCellEventArgs e)
        {
            if (e.ColumnIndex == 0 && e.RowIndex != -1)
            {
                if (dgQuery.Rows[e.RowIndex].Cells[0].EditedFormattedValue.ToString().ToLower() == "false")
                {
                    dgQuery.Rows[e.RowIndex].Cells[0].Value = true;
                }
                else
                {
                    dgQuery.Rows[e.RowIndex].Cells[0].Value = false;
                }
                GetSelectedTrainNos();
            }
        }

        private void chkTaskSele_CheckedChanged(object sender, EventArgs e)
        {
            foreach (DataGridViewRow item in dgQuery.Rows)
            {
                item.Cells[0].Value = chkTaskSele.Checked;
            }
            GetSelectedTrainNos();
        }

        private void GetSelectedTrainNos()
        {
            List<string> lstNo = new List<string>();
            foreach (DataGridViewRow item in dgQuery.Rows)
            {
                if (item.Cells[0].EditedFormattedValue.ToString().ToLower() == "true")
                {
                    lstNo.Add(item.Cells[1].Value.ToString().ToUpper());
                }
            }
            txtTrainNo.Text = string.Join(",", lstNo.ToArray());
        }

        private void dgQuery_MouseDoubleClick(object sender, MouseEventArgs e)
        {
            if (dgQuery.SelectedRows.Count <= 0)
                return;
            try
            {
                dgQuery.SelectedRows[0].Cells[0].Value = true;
            }
            catch (Exception oe)
            {
                Log.WriteError("dgQuery_MouseDoubleClick出错", oe);
            }
            GetSelectedTrainNos();
        }

        #endregion

        private void dgNoTaskUser_MouseClick(object sender, MouseEventArgs e)
        {
            var user = GetNowSelectedUser();
            if (user == null)
                return;
            try
            {
                cmbSeat.Value = "";
                BindPassengers(user.StrNowUserName);
            }
            catch (Exception oe)
            {
                Log.WriteError("dgNoTaskUser_MouseClick出错", oe);
            }

        }

        private void dgNoTaskUser_CellContentClick(object sender, DataGridViewCellEventArgs e)
        {
            if (e.ColumnIndex == 0 && e.RowIndex >= 0)
            {
                if (dgNoTaskUser.Rows[e.RowIndex].Cells[0].EditedFormattedValue.ToString().ToLower() == "false")
                {
                    dgNoTaskUser.Rows[e.RowIndex].Cells[0].Value = true;
                }
                else
                {
                    dgNoTaskUser.Rows[e.RowIndex].Cells[0].Value = false;
                }
            }
        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            if (dgNoTaskUser.Rows.Count > 0)
            {
                List<string> lstRemoveTmp = new List<string>();
                foreach (DataGridViewRow row in dgNoTaskUser.Rows)
                {
                    if (row.Cells[0].EditedFormattedValue.ToString().ToLower() == "false")
                    {
                        if (row.Cells[1].Value == null)
                        {
                            Console.WriteLine();
                        }
                        else
                        {
                            lstRemoveTmp.Add(row.Cells[1].Value.ToString());
                        }
                    }
                }
                if (lstRemoveTmp.Count < LstUser.Count)
                {
                    foreach (var user in LstUser.FindAll(p => !lstRemoveTmp.Contains(p.StrNowUserName)))
                    {
                        if (user.LstTmpPassage.FindAll(p => p.IsChecked).Count <= 0)
                        {
                            MessageBox.Show(this, "账号【" + user.StrNowUserName + "】没有设置乘客！", "任务提示");
                            return;
                        }
                        if (user.LstTmpPassage.FindAll(p => p.IsChecked).Count > 5)
                        {
                            MessageBox.Show(this, "账号【" + user.StrNowUserName + "】设置的乘客数量大于5！", "任务提示");
                            return;
                        }
                        if (rdoStudent.Checked && user.LstTmpPassage.Exists(p => p.IsChecked && p.TicType.Key.Contains("成人")))
                        {
                            MessageBox.Show(this, "账号【" + user.StrNowUserName + "】，成人类型证件不能订学生票！", "任务提示");
                            return;
                        }

                        var badPass = user.LstTmpPassage.FirstOrDefault(p => p.IsChecked && p.LstSeatType.Count <= 0);
                        if (badPass != null)
                        {
                            MessageBox.Show(this, "账号【" + user.StrNowUserName + "】中的乘客" + badPass.Name + "没有设置任何席别，不能订票！", "任务提示");
                            return;
                        }
                    }
                    LstUser.RemoveAll(p => lstRemoveTmp.Contains(p.StrNowUserName));
                }
                else
                {
                    MessageBox.Show(this, "至少勾选一个账号！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return;
                }
            }
            if (LstUser == null || LstUser.Count <= 0)
            {
                MessageBox.Show(this, "至少勾选一个账号！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return;
            }
            if (dtAttention.CheckedItems == null || dtAttention.CheckedItems.Count <= 0)
            {
                MessageBox.Show(this, "请设置订票日期！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return;
            }
            if (CommonReg.NowUserIsExpired)
            {
                return;
            }
            if (CommonString.IsComCanUse)
            {
                if ((IPHelper.lstComEnableIP == null || IPHelper.lstComEnableIP.Count <= 0) && MessageBox.Show(this, "服务器列表还在加载……\n你确定要继续刷票，可能造成本地IP被封！\n点‘是’继续刷票操作\n点‘否’取消操作，可以稍等几分钟后重试！", "", MessageBoxButtons.YesNo, MessageBoxIcon.Warning) == System.Windows.Forms.DialogResult.No)
                {
                    return;
                }
            }
            else if (CommonString.IsMobileCanUse)
            {
                if ((IPHelper.lstMobileEnableIP == null || IPHelper.lstMobileEnableIP.Count <= 0) && MessageBox.Show(this, "服务器列表还在加载……\n你确定要继续刷票，可能造成本地IP被封！\n点‘是’继续刷票操作\n点‘否’取消操作，可以稍等几分钟后重试！", "", MessageBoxButtons.YesNo, MessageBoxIcon.Warning) == System.Windows.Forms.DialogResult.No)
                {
                    return;
                }
            }
            if (!PreparePassenger())
            {
                return;
            }

            bool isNMore = chkNMore.Checked;
            string FirstSeat = ddlSeat.Value;
            string FirstUType = ddlUType.SelectedIndex > -1 ? ddlUType.Text : "";
            if (isNMore)
            {
                if (string.IsNullOrEmpty(FirstSeat))
                {
                    MessageBox.Show(this, "使用[1+N]模式，请先设置第一个乘客的席别！", "[1+N]设置提醒", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return;
                }
                if (string.IsNullOrEmpty(FirstUType))
                {
                    MessageBox.Show(this, "使用[1+N]模式，请先设置第一个乘客的票类型！", "[1+N]设置提醒", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return;
                }
            }
            if (chkAutoCancel.Checked && !rdoWuZuo.Checked)
            {
                rdoWuZuo.Checked = true;
            }
            DateTime dtTickQuery = GetTickQuery();
            bool IsTickQuery = dtTickQuery != DateTime.MinValue;
            bool IsJianLou = rdoJianLou.Checked;
            bool IsTickSub = dtSubTick.Checked;
            DateTime dtTickSub = DateTime.MinValue;
            if (IsTickSub)
            {
                dtTickSub = DateTime.Parse(string.Format("{0} {1}"
                        , CommonString.serverTime.ToString("yyyy-MM-dd"),
                        dtSubTick.Value.ToString("HH:mm:ss"))).AddMilliseconds((double)nSubMSecond.Value);
                if (IsTickQuery)
                    IsTickSub = dtTickSub > dtTickQuery;
                else
                    IsTickSub = dtTickSub > CommonString.serverTime;
            }

            List<AttentionItem> lstAttention = GetNowAttention();
            if (lstAttention == null || lstAttention.Count <= 0)
            {
                return;
            }
            string remark = txtRemark.Text.Replace("(", "[").Replace(")", "]").Replace("【", "[").Replace("】", "]").Trim();
            foreach (var item in LstUser)
            {
                item.IsNMode = isNMore;
                item.FirstSeat = FirstSeat;
                item.FirstUType = FirstUType;
                item.IsAutoCancel = chkAutoCancel.Checked;
                item.IsJianLou = IsJianLou;
                item.IsNoControlSeat = chkNoSeat.Checked;
                item.NQueryTick = (int)QueryTick.Value;
                item.NSubTick = (int)nSubSleepTine.Value;
                item.NSubStopTime = (int)nSubStopTime.Value;
                item.IsWuZuo = rdoWuZuo.Checked;
                item.IsAutoRemovePassenger = chkAutoRemovePassenger.Checked;

                item.dtTickQuery = dtTickQuery;
                item.IsTickQuery = IsTickQuery;

                item.IsTickSub = IsTickSub;
                item.dtTickSub = dtTickSub;

                item.Remark = remark;
            }

            DoCheckTicket();

            try
            {
                Parallel.ForEach<UserEntity>(LstUser, user =>
                {
                    try
                    {
                        user.Status = TaskStatus.已停止;
                        user.IsHasTask = true;
                        var lstTmpAttion = new List<AttentionItem>();
                        lstAttention.ForEach(p =>
                        {
                            lstTmpAttion.Add(p.Copy());
                        });
                        user.LstNowAttetion = CommonMethod.GetLstByIndex(lstTmpAttion, LstUser.IndexOf(user));
                        var lstAllSeat = new List<string>();
                        user.LstTmpPassage.ForEach(delegate(Passenger pss)
                        {
                            if (pss.IsChecked)
                            {
                                pss.LstSeatType.ForEach(delegate(KeyValueEntity obj)
                                {
                                    if (!lstAllSeat.Contains(obj.Key))
                                    {
                                        lstAllSeat.Add(obj.Key);
                                    }
                                });
                            }
                        });
                        user.LstNowAttetion.ForEach(p =>
                        {
                            p.LstAllSeatType = lstAllSeat;
                        });
                        user.LstNowAttetion = user.LstNowAttetion;
                    }
                    catch { }
                });
            }
            catch (AggregateException oe)
            {
                foreach (Exception item in oe.InnerExceptions)
                {
                    Log.WriteError("btnOK_Click异常", item);
                }
            }
            catch (Exception oe)
            {
                Log.WriteError("btnOK_Click异常", oe);
            }

            if (LstUser.Exists(user => user.LstNowAttetion != null
                && user.LstNowAttetion.Exists(item => item.LstTrainNo != null && item.LstTrainNo.Count > 0
                    && item.LstTrainNo.Exists(p => p.StartsWith("C") || p.StartsWith("D") || p.StartsWith("G"))
                    && item.LstTrainNo.Count(p => p.StartsWith("C") || p.StartsWith("D") || p.StartsWith("G")) == item.LstTrainNo.Count
                    && item.LstAllSeatType.Exists(p => p.Contains("硬")))))
            {
                var res = MessageBox.Show(this, "助手检测到你设置了高铁动车车次，但是席别勾选了硬卧或硬座！\n请确认是否设置错误，点[是]返回修改，点[否]继续操作！", "席别错误警告", MessageBoxButtons.YesNo, MessageBoxIcon.Error);
                if (res == System.Windows.Forms.DialogResult.Yes)
                {
                    return;
                }
            }
            if (LstUser.Exists(user => user.LstNowAttetion != null
                && user.LstNowAttetion.Exists(item => item.LstTrainNo != null && item.LstTrainNo.Count > 0
                    && item.LstTrainNo.Exists(p => !p.StartsWith("C") && !p.StartsWith("D") && !p.StartsWith("G"))
                    && item.LstTrainNo.Count(p => !p.StartsWith("C") && !p.StartsWith("D") && !p.StartsWith("G")) == item.LstTrainNo.Count
                    && item.LstAllSeatType.Exists(p => p.Contains("一") || p.Contains("二")))))
            {
                var res = MessageBox.Show(this, "助手检测到你设置了普通车次，但是席别勾选了一等或二等座！\n请确认是否设置错误，点[是]返回修改，点[否]继续操作！", "席别错误警告", MessageBoxButtons.YesNo, MessageBoxIcon.Error);
                if (res == System.Windows.Forms.DialogResult.Yes)
                {
                    return;
                }
            }
            //foreach (var user in LstUser)
            //{
            //    if (user.LstNowAttetion != null)
            //    {
            //        foreach (var item in user.LstNowAttetion)
            //        {
            //            if (item.LstTrainNo != null && item.LstTrainNo.Count > 0)
            //            {
            //                if (item.LstTrainNo.Exists(p => p.StartsWith("C") || p.StartsWith("D") || p.StartsWith("G"))
            //                    && item.LstAllSeatType.Exists(p => p.Contains("硬")))
            //                {
            //                    var res = MessageBox.Show(this, "助手检测到你设置了高铁动车车次，但是席别勾选了硬卧或硬座！\n是否立即返回???", "席别错误警告", MessageBoxButtons.YesNo, MessageBoxIcon.Warning);
            //                    if (res == System.Windows.Forms.DialogResult.Yes)
            //                    {
            //                        return;
            //                    }
            //                }
            //                if (item.LstTrainNo.Exists(p => !p.StartsWith("C") && !p.StartsWith("D") && !p.StartsWith("G"))
            //                    && item.LstAllSeatType.Exists(p => p.Contains("一") || p.Contains("二")))
            //                {
            //                    var res = MessageBox.Show(this, "助手检测到你设置了普通车次，但是席别勾选了一等或二等座！\n是否立即返回???", "席别错误警告", MessageBoxButtons.YesNo, MessageBoxIcon.Warning);
            //                    if (res == System.Windows.Forms.DialogResult.Yes)
            //                    {
            //                        return;
            //                    }
            //                }
            //            }
            //        }
            //    }
            //}
            if (chkAutoStart.Checked)
            {
                NewTicketHelper.StartTask(LstUser);
            }
            this.DialogResult = System.Windows.Forms.DialogResult.OK;
        }

        private void cbxFrom_Leave(object sender, EventArgs e)
        {
            if (chkTC.Checked)
            {
                chkTC.Checked = false;
                chkTC.Checked = true;
            }
        }

        private void dgNoTaskUser_CellMouseDown(object sender, DataGridViewCellMouseEventArgs e)
        {
            if (e.Button == MouseButtons.Right)
            {
                if (e.RowIndex >= 0)
                {
                    dgNoTaskUser.ClearSelection();
                    dgNoTaskUser.Rows[e.RowIndex].Selected = true;
                }
            }
        }

        private void 修改备注ToolStripMenuItem_Click(object sender, EventArgs e)
        {
            var user = GetNowSelectedUser();
            if (user == null)
                return;
            //try
            //{
            //    var user = LstUser.Find(p => p.StrNowUserName.Equals(dgNoTaskUser.SelectedRows[0].Cells[1].Value.ToString()));
            //    if (user == null)
            //    {
            //        return;
            //    }
            //    user.Remark = "";
            //    BindPassengers(user.StrNowUserName);
            //}
            //catch (Exception oe)
            //{
            //    Log.WriteError(oe);
            //}
        }

        private void 添加儿童票ToolStripMenuItem_Click(object sender, EventArgs e)
        {
            var user = GetNowSelectedUser();
            if (user == null)
                return;
            if (lvPassenger.SelectedItems != null && lvPassenger.SelectedItems.Count > 0)
            {
                var oldPassenger = user.LstTmpPassage.Find(p => p.IDCard == lvPassenger.SelectedItems[0].SubItems[4].Text);
                var newPass = new Passenger();
                if (oldPassenger == null)
                {
                    newPass.Name = lvPassenger.SelectedItems[0].SubItems[0].Text;
                    newPass.IDCard = lvPassenger.SelectedItems[0].SubItems[4].Text;
                    newPass.SetCardType(lvPassenger.SelectedItems[0].SubItems[3].Text);
                }
                else
                {
                    oldPassenger.IsChecked = true;
                    newPass.Name = oldPassenger.Name;
                    newPass.IDCard = oldPassenger.IDCard;
                    newPass.Card = oldPassenger.Card;
                    newPass.LstSeatType = oldPassenger.LstSeatType;
                }
                newPass.SetUserType("儿童票");
                newPass.IsChecked = true;
                user.LstTmpPassage.Add(newPass);
                user.LstTmpPassage = user.LstTmpPassage;
                BindPassengers(user.StrNowUserName);
            }
        }

        private List<string> GetNoCheckedType()
        {
            List<string> lstType = new List<string>();
            foreach (Control ctl in flTrainTypes.Controls)
            {
                if (ctl is CheckBox && !(ctl as CheckBox).Checked && !ctl.Text.Equals("全部") && ctl.Tag != null)
                {
                    lstType.Add(ctl.Tag.ToString());
                }
            }
            if (lstType.Count == 5)
            {
                lstType = new List<string>();
            }
            return lstType;
        }

        private void QueryTick_ValueChanged(object sender, EventArgs e)
        {
            Settings.Default.NQueryTick = (int)QueryTick.Value;
        }

        private UserEntity GetNowSelectedUser()
        {
            if (dgNoTaskUser.SelectedRows.Count <= 0)
                return null;
            return LstUser.Find(p => p.StrNowUserName.Equals(dgNoTaskUser.SelectedRows[0].Cells[1].Value.ToString()));
        }

        private List<UserEntity> GetAllSelectedUsers()
        {
            List<string> lstSeleUser = new List<string>();
            foreach (DataGridViewRow row in dgNoTaskUser.Rows)
            {
                if (row.Cells[0].EditedFormattedValue.ToString().ToLower() == "true")
                {
                    lstSeleUser.Add(row.Cells[1].Value.ToString());
                }
            }
            return LstUser.FindAll(p => lstSeleUser.Contains(p.StrNowUserName));
        }

        private void btnFrom12306_Click(object sender, EventArgs e)
        {
            if (bgLoadPassenger.IsBusy)
            {
                bgLoadPassenger.CancelAsync();
            }
            tsmFromWeb.Enabled = false;
            bgLoadPassenger.RunWorkerAsync();
        }

        private void bgLoadPassenger_DoWork(object sender, DoWorkEventArgs e)
        {
            var user = GetNowSelectedUser();
            if (user == null)
                return;
            BindPassengers(user.StrNowUserName, true);
        }

        private void bgLoadPassenger_RunWorkerCompleted(object sender, RunWorkerCompletedEventArgs e)
        {
            tsmFromWeb.Enabled = true;
        }

        private void chkNextDay_CheckedChanged(object sender, EventArgs e)
        {
            dtRefreshTick.Checked = chkNextDay.Checked;
            dtRefreshTick.Value = CommonMethod.DtWebStartDate(true);
        }
    }
}
