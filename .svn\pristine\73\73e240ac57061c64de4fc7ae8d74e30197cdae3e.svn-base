﻿using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Threading;
using System.Web;
using System.Web.Script.Serialization;
using CommonLib;

namespace Account.Web
{
    public class PayUtil
    {
        static PayUtil()
        {
            PayProcessThread();
        }

        private const string strPayUrl = "http://ocr.oldfish.cn:9090/";
        private const string strAccountUrl = "http://ocr.oldfish.cn:2020/";
        private const string strProductName = "OCR助手";
        public static BlockingCollection<PayEntity> PayPool = new BlockingCollection<PayEntity>();
        private static JavaScriptSerializer JavaScriptSerializer = new JavaScriptSerializer();

        public static bool ValidateMd5(string payId, string param, string price, string reallyPrice, string type, string remark, string fromSign)
        {
            var sign = CommonEncryptHelper.MD5(payId + param + type + price + reallyPrice + ConfigHelper.PayToken);
            var result = Equals(fromSign, sign);
            if (result)
            {
                PayPool.Add(new PayEntity()
                {
                    payId = payId,
                    param = param,
                    price = price,
                    realPrice = reallyPrice,
                    remark = remark,
                    type = type,
                    sign = fromSign
                });
            }
            return result;
        }

        private static BlockingCollection<string> PayedPool = new BlockingCollection<string>();

        private static void PayProcessThread()
        {
            new Thread(thread =>
                {
                    try
                    {
                        foreach (var pay in PayPool.GetConsumingEnumerable())
                        {
                            if (PayedPool.Any(p => Equals(p, pay.payId)))
                            {
                                continue;
                            }

                            var payStr = JavaScriptSerializer.Serialize(pay);
                            if (string.IsNullOrEmpty(pay.param) || string.IsNullOrEmpty(pay.remark))
                            {
                                LogHelper.Log.Info("支付参数为空，不处理订单！" + payStr);
                                continue;
                            }

                            PayedPool.Add(pay.payId);

                            var isSuccess = false;
                            try
                            {
                                var regType = "";
                                var regDays = 0;
                                var remark = "";

                                var user = CodeHelper.GetCodeByAccountId(pay.param);
                                if (user == null)
                                {
                                    LogHelper.Log.Info("用户信息不存在，不处理订单：" + payStr);
                                }

                                if (string.IsNullOrEmpty(user.StrRemark) ||
                                    (!string.IsNullOrEmpty(user.StrRemark) &&
                                     !user.StrRemark.Contains(pay.payId)))
                                {
                                    var userType = Code.GetCanRegUserTypes().FirstOrDefault(p => pay.remark.EndsWith(p.Type.ToString()));
                                    if (userType != null && userType.UserChargeType.Count > 0)
                                    {
                                        regType = userType.Type.ToString();
                                        var priceType = userType.UserChargeType.FirstOrDefault(p => pay.remark.Contains(p.Name)) ??
                                                     userType.UserChargeType.FirstOrDefault();
                                        if (priceType != null)
                                        {
                                            var oldPrice = BoxUtil.GetDoubleFromObject(pay.price);
                                            var newPrice = priceType.Price;
                                            isSuccess = Equals(oldPrice, newPrice);
                                            remark = string.Format("{2}:{0},{1}元", priceType.Name + userType.Name, pay.realPrice, pay.payId);
                                            if (isSuccess)
                                            {
                                                switch (priceType.Name)
                                                {
                                                    case "1月":
                                                    case "一月":
                                                        regDays = 30;
                                                        break;
                                                    case "1季":
                                                    case "一季":
                                                    case "1季度":
                                                    case "一季度":
                                                        regDays = 30 * 3;
                                                        break;
                                                    case "半年":
                                                        regDays = 30 * 6;
                                                        break;
                                                    case "1年":
                                                    case "一年":
                                                        regDays = 365;
                                                        break;
                                                    case "3年":
                                                    case "三年":
                                                        regDays = 365 * 3;
                                                        break;
                                                    case "5年":
                                                    case "五年":
                                                        regDays = 365 * 5;
                                                        break;
                                                    case "终身":
                                                    case "终生":
                                                        regDays = 365 * 100;
                                                        break;
                                                }
                                            }
                                            else
                                            {
                                                LogHelper.Log.Info("价格与实际不匹配，不处理订单：" + payStr);
                                            }
                                        }
                                        else
                                        {
                                            LogHelper.Log.Info("价格类型未找到，不处理订单：" + payStr);
                                        }
                                    }
                                    else
                                    {
                                        LogHelper.Log.Info("用户类型未找到，不处理订单：" + payStr);
                                    }
                                }
                                else
                                {
                                    LogHelper.Log.Info("已经处理过了，不处理订单：" + payStr);
                                }

                                if (isSuccess)
                                {
                                    user.StrType = regType;
                                    user.DtExpire = (ServerTime.LocalTime > user.DtExpire ? ServerTime.LocalTime : user.DtExpire).AddDays(regDays);
                                    user.StrRemark = remark;
                                    isSuccess = CodeHelper.AddOrUpdateCode(user);
                                }
                            }
                            catch (Exception oe)
                            {
                                Console.WriteLine(oe.Message);
                            }
                            LogHelper.Log.Info("PayInfo 处理" + (isSuccess ? "成功" : "失败") + ":" + payStr);
                        }
                    }
                    catch (Exception oe)
                    {
                        LogHelper.Log.Error("PayProcess Error", oe);
                    }
                })
            { IsBackground = true, Priority = ThreadPriority.Highest }.Start();
        }

        public static string GetPayUrl(string price, string orderNo, string remark, string account)
        {
            var result = "";
            if (!string.IsNullOrEmpty(remark) && !string.IsNullOrEmpty(account))
            {
                result = "";
                var url = strPayUrl + "testOrder?isHtml=1&type=0&price=" + price
                          + "&return_url=" + HttpUtility.UrlEncode(strAccountUrl + "Pay.aspx")
                          + "&notify_url=" + HttpUtility.UrlEncode(strAccountUrl + "Pay.ashx")
                          + "&payId=" + orderNo
                          + "&remark=" + HttpUtility.UrlEncode(strProductName + "-" + remark)
                          + "&param=" + account;
                var html = new WebClient().DownloadString(url);
                if (html.Trim().StartsWith("<script>"))
                {
                    result = strPayUrl + "payPage/pay.html?orderId=" + CommonLib.CommonHelper.SubString(html, "orderId=", "'</script>").Trim();
                }
                if (string.IsNullOrEmpty(result))
                {
                    result = strAccountUrl + "?desc=" +
                              HttpUtility.UrlEncode(string.Format("{0},共{1}元", remark, price));
                }
            }
            if (string.IsNullOrEmpty(result))
            {
                result = strAccountUrl + "?desc=" + HttpUtility.UrlEncode(strProductName);
            }
            return result;
        }
    }

    public class PayEntity
    {
        public string payId { get; set; }

        public string price { get; set; }

        public string type { get; set; }

        public string realPrice { get; set; }

        public string remark { get; set; }

        public string param { get; set; }

        public string sign { get; set; }

    }
}