﻿<?xml version="1.0" encoding="utf-8"?>
<!--
https://go.microsoft.com/fwlink/?LinkID=208121.
-->
<Project>
  <PropertyGroup>
    <_PublishTargetUrl>C:\Users\<USER>\Desktop\Code.Process.Web</_PublishTargetUrl>
    <History>True|2025-03-06T14:34:45.2560480Z||;True|2025-03-06T22:32:27.0887573+08:00||;True|2025-03-06T22:19:59.7846487+08:00||;True|2025-03-05T22:46:25.2156984+08:00||;True|2025-03-04T20:04:46.9612665+08:00||;True|2025-03-03T11:12:28.7311352+08:00||;True|2025-03-02T11:22:53.5401284+08:00||;True|2025-03-02T02:00:05.9396456+08:00||;True|2025-03-02T01:22:12.4822758+08:00||;True|2025-02-26T14:34:43.6786406+08:00||;True|2025-02-24T22:14:09.6204902+08:00||;True|2025-02-23T20:55:20.5481308+08:00||;True|2025-02-23T20:54:42.2467062+08:00||;True|2025-01-10T18:39:33.2357752+08:00||;True|2025-01-10T17:34:19.4624180+08:00||;True|2025-01-10T17:34:05.0205018+08:00||;True|2024-12-24T20:06:17.9347497+08:00||;True|2024-12-17T16:55:23.3469915+08:00||;True|2024-08-12T14:17:37.0349499+08:00||;True|2024-08-12T11:42:05.1927455+08:00||;True|2024-07-18T10:46:07.5832281+08:00||;True|2024-07-03T14:50:30.9729988+08:00||;True|2024-07-01T10:17:17.1410744+08:00||;True|2024-06-14T09:51:13.9591854+08:00||;True|2024-06-14T09:51:03.5260372+08:00||;True|2024-05-21T15:08:42.8107055+08:00||;True|2024-05-15T17:07:31.9431756+08:00||;True|2024-05-15T16:59:52.6324438+08:00||;True|2024-05-06T17:53:52.1725194+08:00||;True|2024-04-03T18:06:43.3741744+08:00||;True|2024-04-01T17:08:58.5289829+08:00||;True|2024-03-28T16:49:36.0056829+08:00||;True|2024-03-15T17:43:53.0674127+08:00||;True|2024-03-14T18:40:33.9020886+08:00||;True|2024-02-01T18:49:28.5765646+08:00||;True|2024-02-01T18:47:19.6825081+08:00||;True|2024-01-26T17:55:34.1174813+08:00||;True|2024-01-25T16:56:48.8680828+08:00||;True|2024-01-25T16:55:54.9063386+08:00||;True|2024-01-16T18:28:45.4886514+08:00||;True|2024-01-15T20:02:57.0001426+08:00||;True|2024-01-15T19:47:06.7696420+08:00||;True|2024-01-15T19:41:46.5371423+08:00||;True|2024-01-12T18:19:26.8999195+08:00||;True|2024-01-08T19:08:13.1458276+08:00||;True|2024-01-05T14:08:00.6299001+08:00||;True|2024-01-03T11:59:45.9713380+08:00||;True|2024-01-03T11:35:32.5687943+08:00||;True|2024-01-03T11:20:35.8514041+08:00||;True|2024-01-03T10:51:29.4497217+08:00||;True|2024-01-02T20:13:53.5662746+08:00||;True|2024-01-02T19:44:01.2375486+08:00||;True|2023-12-30T00:32:00.4465902+08:00||;True|2023-12-30T00:30:42.5147019+08:00||;True|2023-12-30T00:20:29.4644403+08:00||;True|2023-12-29T23:58:01.7206723+08:00||;True|2023-12-29T23:02:51.9098187+08:00||;True|2023-12-29T22:53:41.5763917+08:00||;True|2023-12-29T22:38:33.6075560+08:00||;True|2023-12-29T22:09:25.4304038+08:00||;True|2023-12-29T21:32:43.7047421+08:00||;True|2023-12-29T15:32:12.2794962+08:00||;True|2023-12-28T23:59:21.9372103+08:00||;True|2023-12-18T21:05:16.9699280+08:00||;True|2023-12-08T23:37:12.6329528+08:00||;True|2023-12-08T23:14:10.7498727+08:00||;True|2023-12-04T21:35:44.9716512+08:00||;True|2023-12-03T23:30:24.0522236+08:00||;True|2023-12-02T23:05:20.1599644+08:00||;True|2023-12-02T22:42:12.2460168+08:00||;True|2023-12-02T22:41:21.6605930+08:00||;True|2023-12-02T22:31:18.3303067+08:00||;True|2023-12-02T22:19:58.4546425+08:00||;True|2023-12-02T22:18:58.1197797+08:00||;True|2023-12-02T22:18:16.4232227+08:00||;True|2023-12-02T22:17:41.6378776+08:00||;True|2023-12-02T21:55:45.1037235+08:00||;True|2023-12-02T21:52:51.6417283+08:00||;True|2023-12-02T21:39:37.9522854+08:00||;True|2023-12-02T21:34:39.2710999+08:00||;True|2023-12-02T21:30:43.8442272+08:00||;True|2023-12-02T21:14:09.9479106+08:00||;True|2023-12-02T21:09:23.2176852+08:00||;True|2023-12-02T21:07:42.2817818+08:00||;True|2023-12-02T21:05:42.6844239+08:00||;True|2023-12-02T21:04:33.9175443+08:00||;True|2023-12-02T21:02:18.1347187+08:00||;True|2023-12-02T19:35:03.8084264+08:00||;True|2023-12-02T19:32:31.9421027+08:00||;True|2023-12-02T19:23:44.8781272+08:00||;True|2023-12-02T19:21:52.0284309+08:00||;True|2023-12-02T19:18:56.6168326+08:00||;True|2023-11-15T10:59:54.1920587+08:00||;True|2023-11-14T11:21:45.5690869+08:00||;True|2023-11-02T13:45:17.6129283+08:00||;True|2023-10-13T19:08:16.7914543+08:00||;True|2023-08-30T17:26:50.3355757+08:00||;</History>
    <LastFailureDetails />
  </PropertyGroup>
  <ItemGroup>
    <File Include="AutoRefresh.bat">
      <publishTime>01/12/2024 15:33:13</publishTime>
    </File>
    <File Include="bin/Aliyun.OSS.dll">
      <publishTime>02/07/2021 02:23:08</publishTime>
    </File>
    <File Include="bin/Aliyun.OSS.pdb">
      <publishTime>02/07/2021 02:23:08</publishTime>
    </File>
    <File Include="bin/Code.Common.dll">
      <publishTime>03/06/2025 22:34:43</publishTime>
    </File>
    <File Include="bin/Code.Common.dll.config">
      <publishTime>01/09/2023 18:45:54</publishTime>
    </File>
    <File Include="bin/Code.Common.pdb">
      <publishTime>03/06/2025 22:34:43</publishTime>
    </File>
    <File Include="bin/Code.Process.Common.dll">
      <publishTime>01/16/2024 18:28:42</publishTime>
    </File>
    <File Include="bin/Code.Process.Common.dll.config">
      <publishTime>01/09/2023 18:45:54</publishTime>
    </File>
    <File Include="bin/Code.Process.Common.pdb">
      <publishTime>01/16/2024 18:28:42</publishTime>
    </File>
    <File Include="bin/Code.Process.Web.dll">
      <publishTime>03/06/2025 22:34:43</publishTime>
    </File>
    <File Include="bin/Code.Process.Web.pdb">
      <publishTime>03/06/2025 22:34:43</publishTime>
    </File>
    <File Include="bin/CommonLib.dll">
      <publishTime>03/06/2025 22:34:40</publishTime>
    </File>
    <File Include="bin/CommonLib.dll.config">
      <publishTime>05/23/2024 16:48:52</publishTime>
    </File>
    <File Include="bin/CommonLib.pdb">
      <publishTime>03/06/2025 22:34:40</publishTime>
    </File>
    <File Include="bin/COSXML.dll">
      <publishTime>01/03/2023 16:38:18</publishTime>
    </File>
    <File Include="bin/de/PdfSharp.resources.dll">
      <publishTime>02/04/2019 13:21:52</publishTime>
    </File>
    <File Include="bin/DocOcr.dll">
      <publishTime>03/06/2025 22:34:41</publishTime>
    </File>
    <File Include="bin/DocOcr.dll.config">
      <publishTime>12/09/2022 18:12:08</publishTime>
    </File>
    <File Include="bin/DocOcr.pdb">
      <publishTime>03/06/2025 22:34:41</publishTime>
    </File>
    <File Include="bin/HanZiOcr.dll">
      <publishTime>03/06/2025 22:34:41</publishTime>
    </File>
    <File Include="bin/HanZiOcr.dll.config">
      <publishTime>01/09/2023 18:45:54</publishTime>
    </File>
    <File Include="bin/HanZiOcr.pdb">
      <publishTime>03/06/2025 22:34:41</publishTime>
    </File>
    <File Include="bin/Log4Net.config">
      <publishTime>12/02/2023 22:41:55</publishTime>
    </File>
    <File Include="bin/log4net.dll">
      <publishTime>06/10/2021 16:46:13</publishTime>
    </File>
    <File Include="bin/MathOcr.dll">
      <publishTime>03/06/2025 22:34:41</publishTime>
    </File>
    <File Include="bin/MathOcr.dll.config">
      <publishTime>11/16/2022 16:44:22</publishTime>
    </File>
    <File Include="bin/MathOcr.pdb">
      <publishTime>03/06/2025 22:34:41</publishTime>
    </File>
    <File Include="bin/Microsoft.CodeDom.Providers.DotNetCompilerPlatform.dll">
      <publishTime>09/05/2018 16:10:50</publishTime>
    </File>
    <File Include="bin/Newtonsoft.Json.dll">
      <publishTime>03/18/2021 04:03:36</publishTime>
    </File>
    <File Include="bin/Notice.Common.dll">
      <publishTime>04/26/2024 15:39:51</publishTime>
    </File>
    <File Include="bin/Notice.Common.pdb">
      <publishTime>04/26/2024 15:39:51</publishTime>
    </File>
    <File Include="bin/Notice.Process.Common.dll">
      <publishTime>10/19/2023 14:45:44</publishTime>
    </File>
    <File Include="bin/Notice.Process.Common.pdb">
      <publishTime>10/19/2023 14:45:44</publishTime>
    </File>
    <File Include="bin/PdfSharp.dll">
      <publishTime>02/04/2019 13:21:52</publishTime>
    </File>
    <File Include="bin/roslyn/csc.exe">
      <publishTime>08/08/2018 05:38:48</publishTime>
    </File>
    <File Include="bin/roslyn/csc.exe.config">
      <publishTime>08/08/2018 12:18:12</publishTime>
    </File>
    <File Include="bin/roslyn/csc.rsp">
      <publishTime>08/08/2018 12:09:42</publishTime>
    </File>
    <File Include="bin/roslyn/csi.exe">
      <publishTime>08/08/2018 05:38:48</publishTime>
    </File>
    <File Include="bin/roslyn/csi.exe.config">
      <publishTime>08/08/2018 12:18:34</publishTime>
    </File>
    <File Include="bin/roslyn/csi.rsp">
      <publishTime>08/08/2018 12:09:56</publishTime>
    </File>
    <File Include="bin/roslyn/Microsoft.Build.Tasks.CodeAnalysis.dll">
      <publishTime>08/08/2018 05:38:48</publishTime>
    </File>
    <File Include="bin/roslyn/Microsoft.CodeAnalysis.CSharp.dll">
      <publishTime>08/08/2018 05:38:48</publishTime>
    </File>
    <File Include="bin/roslyn/Microsoft.CodeAnalysis.CSharp.Scripting.dll">
      <publishTime>08/08/2018 05:38:48</publishTime>
    </File>
    <File Include="bin/roslyn/Microsoft.CodeAnalysis.dll">
      <publishTime>08/08/2018 05:38:48</publishTime>
    </File>
    <File Include="bin/roslyn/Microsoft.CodeAnalysis.Scripting.dll">
      <publishTime>08/08/2018 05:38:48</publishTime>
    </File>
    <File Include="bin/roslyn/Microsoft.CodeAnalysis.VisualBasic.dll">
      <publishTime>08/08/2018 05:38:48</publishTime>
    </File>
    <File Include="bin/roslyn/Microsoft.CSharp.Core.targets">
      <publishTime>08/08/2018 12:09:42</publishTime>
    </File>
    <File Include="bin/roslyn/Microsoft.DiaSymReader.Native.amd64.dll">
      <publishTime>12/05/2017 09:36:44</publishTime>
    </File>
    <File Include="bin/roslyn/Microsoft.DiaSymReader.Native.x86.dll">
      <publishTime>12/05/2017 09:36:44</publishTime>
    </File>
    <File Include="bin/roslyn/Microsoft.Managed.Core.targets">
      <publishTime>08/08/2018 12:09:42</publishTime>
    </File>
    <File Include="bin/roslyn/Microsoft.VisualBasic.Core.targets">
      <publishTime>08/08/2018 12:09:42</publishTime>
    </File>
    <File Include="bin/roslyn/Microsoft.Win32.Primitives.dll">
      <publishTime>11/05/2016 18:55:32</publishTime>
    </File>
    <File Include="bin/roslyn/System.AppContext.dll">
      <publishTime>11/05/2016 18:55:34</publishTime>
    </File>
    <File Include="bin/roslyn/System.Collections.Immutable.dll">
      <publishTime>05/16/2018 03:29:34</publishTime>
    </File>
    <File Include="bin/roslyn/System.Console.dll">
      <publishTime>11/05/2016 18:55:48</publishTime>
    </File>
    <File Include="bin/roslyn/System.Diagnostics.DiagnosticSource.dll">
      <publishTime>11/05/2016 18:55:52</publishTime>
    </File>
    <File Include="bin/roslyn/System.Diagnostics.FileVersionInfo.dll">
      <publishTime>11/05/2016 18:55:56</publishTime>
    </File>
    <File Include="bin/roslyn/System.Diagnostics.StackTrace.dll">
      <publishTime>11/05/2016 18:55:52</publishTime>
    </File>
    <File Include="bin/roslyn/System.Globalization.Calendars.dll">
      <publishTime>11/05/2016 18:56:02</publishTime>
    </File>
    <File Include="bin/roslyn/System.IO.Compression.dll">
      <publishTime>11/05/2016 18:56:08</publishTime>
    </File>
    <File Include="bin/roslyn/System.IO.Compression.ZipFile.dll">
      <publishTime>11/05/2016 18:56:04</publishTime>
    </File>
    <File Include="bin/roslyn/System.IO.FileSystem.dll">
      <publishTime>11/05/2016 18:56:08</publishTime>
    </File>
    <File Include="bin/roslyn/System.IO.FileSystem.Primitives.dll">
      <publishTime>11/05/2016 18:56:08</publishTime>
    </File>
    <File Include="bin/roslyn/System.Net.Http.dll">
      <publishTime>11/05/2016 18:56:30</publishTime>
    </File>
    <File Include="bin/roslyn/System.Net.Sockets.dll">
      <publishTime>11/05/2016 18:56:34</publishTime>
    </File>
    <File Include="bin/roslyn/System.Reflection.Metadata.dll">
      <publishTime>05/16/2018 03:29:44</publishTime>
    </File>
    <File Include="bin/roslyn/System.Runtime.InteropServices.RuntimeInformation.dll">
      <publishTime>11/05/2016 18:57:00</publishTime>
    </File>
    <File Include="bin/roslyn/System.Security.Cryptography.Algorithms.dll">
      <publishTime>11/05/2016 18:57:14</publishTime>
    </File>
    <File Include="bin/roslyn/System.Security.Cryptography.Encoding.dll">
      <publishTime>11/05/2016 18:57:08</publishTime>
    </File>
    <File Include="bin/roslyn/System.Security.Cryptography.Primitives.dll">
      <publishTime>11/05/2016 18:57:18</publishTime>
    </File>
    <File Include="bin/roslyn/System.Security.Cryptography.X509Certificates.dll">
      <publishTime>11/05/2016 18:57:18</publishTime>
    </File>
    <File Include="bin/roslyn/System.Text.Encoding.CodePages.dll">
      <publishTime>11/05/2016 18:57:20</publishTime>
    </File>
    <File Include="bin/roslyn/System.Threading.Tasks.Extensions.dll">
      <publishTime>11/05/2016 18:57:24</publishTime>
    </File>
    <File Include="bin/roslyn/System.ValueTuple.dll">
      <publishTime>11/05/2016 18:57:30</publishTime>
    </File>
    <File Include="bin/roslyn/System.Xml.ReaderWriter.dll">
      <publishTime>11/05/2016 18:57:36</publishTime>
    </File>
    <File Include="bin/roslyn/System.Xml.XmlDocument.dll">
      <publishTime>11/05/2016 18:57:34</publishTime>
    </File>
    <File Include="bin/roslyn/System.Xml.XPath.dll">
      <publishTime>11/05/2016 18:57:40</publishTime>
    </File>
    <File Include="bin/roslyn/System.Xml.XPath.XDocument.dll">
      <publishTime>11/05/2016 18:57:34</publishTime>
    </File>
    <File Include="bin/roslyn/vbc.exe">
      <publishTime>08/08/2018 05:38:48</publishTime>
    </File>
    <File Include="bin/roslyn/vbc.exe.config">
      <publishTime>08/08/2018 12:18:18</publishTime>
    </File>
    <File Include="bin/roslyn/vbc.rsp">
      <publishTime>08/08/2018 12:09:46</publishTime>
    </File>
    <File Include="bin/roslyn/VBCSCompiler.exe">
      <publishTime>08/08/2018 05:38:48</publishTime>
    </File>
    <File Include="bin/roslyn/VBCSCompiler.exe.config">
      <publishTime>08/08/2018 12:18:16</publishTime>
    </File>
    <File Include="bin/ServiceStack.Text.dll">
      <publishTime>06/06/2023 19:11:00</publishTime>
    </File>
    <File Include="bin/System.Buffers.dll">
      <publishTime>02/19/2020 10:05:18</publishTime>
    </File>
    <File Include="bin/System.Diagnostics.DiagnosticSource.dll">
      <publishTime>05/09/2023 14:26:36</publishTime>
    </File>
    <File Include="bin/System.Memory.dll">
      <publishTime>05/08/2022 11:31:02</publishTime>
    </File>
    <File Include="bin/System.Numerics.Vectors.dll">
      <publishTime>05/15/2018 13:29:44</publishTime>
    </File>
    <File Include="bin/System.Runtime.CompilerServices.Unsafe.dll">
      <publishTime>05/09/2023 14:26:36</publishTime>
    </File>
    <File Include="bin/TableOcr.dll">
      <publishTime>03/06/2025 22:34:41</publishTime>
    </File>
    <File Include="bin/TableOcr.dll.config">
      <publishTime>11/16/2022 16:44:22</publishTime>
    </File>
    <File Include="bin/TableOcr.pdb">
      <publishTime>03/06/2025 22:34:41</publishTime>
    </File>
    <File Include="bin/TransOcr.dll">
      <publishTime>03/06/2025 22:34:41</publishTime>
    </File>
    <File Include="bin/TransOcr.dll.config">
      <publishTime>11/16/2022 16:44:21</publishTime>
    </File>
    <File Include="bin/TransOcr.pdb">
      <publishTime>03/06/2025 22:34:41</publishTime>
    </File>
    <File Include="CreateRefreshTask.bat">
      <publishTime>12/29/2023 15:31:14</publishTime>
    </File>
    <File Include="Default.aspx">
      <publishTime>01/16/2024 18:11:47</publishTime>
    </File>
    <File Include="Global.asax">
      <publishTime>07/28/2023 17:33:09</publishTime>
    </File>
    <File Include="Log4Net.config">
      <publishTime>12/02/2023 22:41:55</publishTime>
    </File>
    <File Include="UpdateSite.bat">
      <publishTime>01/15/2024 19:41:19</publishTime>
    </File>
    <File Include="Web.config">
      <publishTime>01/15/2024 19:41:37</publishTime>
    </File>
  </ItemGroup>
</Project>