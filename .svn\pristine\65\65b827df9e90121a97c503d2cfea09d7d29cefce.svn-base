﻿using System;
using System.IO;
using System.Net.Security;
using System.Net.Sockets;
using System.Security.Authentication;
using System.Security.Cryptography.X509Certificates;
using System.Text;
using System.Threading;

namespace OcrLiteLib
{
    public abstract class HttpServer
    {
        private bool is_active = true;
        private TcpListener listener;
        protected int port;
        private static X509Certificate serverCertificate = new X509Certificate();
        public HttpServer(int port)
        {
            this.port = port;
        }
        private static void DisplayCertificateInformation(SslStream stream)
        {
            Console.WriteLine("Certificate revocation list checked: {0}", stream.CheckCertRevocationStatus);
            X509Certificate localCertificate = stream.LocalCertificate;
            if (stream.LocalCertificate != null)
            {
                Console.WriteLine("Local cert was issued to {0} and is valid from {1} until {2}.", localCertificate.Subject, localCertificate.GetEffectiveDateString(), localCertificate.GetExpirationDateString());
            }
            else
            {
                Console.WriteLine("Local certificate is null.");
            }
            X509Certificate remoteCertificate = stream.RemoteCertificate;
            if (stream.RemoteCertificate != null)
            {
                Console.WriteLine("Remote cert was issued to {0} and is valid from {1} until {2}.", remoteCertificate.Subject, remoteCertificate.GetEffectiveDateString(), remoteCertificate.GetExpirationDateString());
                return;
            }
            Console.WriteLine("Remote certificate is null.");
        }
        private static void DisplaySecurityLevel(SslStream stream)
        {
            Console.WriteLine("Cipher: {0} strength {1}", stream.CipherAlgorithm, stream.CipherStrength);
            Console.WriteLine("Hash: {0} strength {1}", stream.HashAlgorithm, stream.HashStrength);
            Console.WriteLine("Key exchange: {0} strength {1}", stream.KeyExchangeAlgorithm, stream.KeyExchangeStrength);
            Console.WriteLine("Protocol: {0}", stream.SslProtocol);
        }
        private static void DisplaySecurityServices(SslStream stream)
        {
            Console.WriteLine("Is authenticated: {0} as server? {1}", stream.IsAuthenticated, stream.IsServer);
            Console.WriteLine("IsSigned: {0}", stream.IsSigned);
            Console.WriteLine("Is Encrypted: {0}", stream.IsEncrypted);
        }
        private static void DisplayStreamProperties(SslStream stream)
        {
            Console.WriteLine("Can read: {0}, write {1}", stream.CanRead, stream.CanWrite);
            Console.WriteLine("Can timeout: {0}", stream.CanTimeout);
        }
        public abstract void handleGETRequest(HttpProcessor p);
        public abstract void handlePOSTRequest(HttpProcessor p, StreamReader inputData);
        public void Listen()
        {
            this.listener = new TcpListener(this.port);
            this.listener.Start();
            while (this.is_active)
            {
                try
                {
                    HttpProcessor @object = new HttpProcessor(this.listener.AcceptTcpClient(), this);
                    new Thread(new ThreadStart(@object.process)).Start();
                    Thread.Sleep(1);
                }
                catch
                {
                }
            }
        }
        private static void ProcessClient(TcpClient client)
        {
            SslStream sslStream = new SslStream(client.GetStream(), false);
            try
            {
                sslStream.AuthenticateAsServer(HttpServer.serverCertificate, false, SslProtocols.Tls, true);
                HttpServer.DisplaySecurityLevel(sslStream);
                HttpServer.DisplaySecurityServices(sslStream);
                HttpServer.DisplayCertificateInformation(sslStream);
                HttpServer.DisplayStreamProperties(sslStream);
                sslStream.ReadTimeout = 1000;
                sslStream.WriteTimeout = 5000;
                Console.WriteLine("Waiting for client message...");
                string arg = HttpServer.ReadMessage(sslStream);
                Console.WriteLine("Received: {0}", arg);
                byte[] bytes = Encoding.UTF8.GetBytes("Hello from the server.");
                Console.WriteLine("Sending hello message.");
                sslStream.Write(bytes);
            }
            catch (AuthenticationException ex)
            {
                Console.WriteLine("Exception: {0}", ex.Message);
                if (ex.InnerException != null)
                {
                    Console.WriteLine("Inner exception: {0}", ex.InnerException.Message);
                }
                Console.WriteLine("Authentication failed - closing the connection.");
                sslStream.Close();
                client.Close();
            }
            finally
            {
                sslStream.Close();
                client.Close();
            }
        }
        private static string ReadMessage(SslStream sslStream)
        {
            byte[] array = new byte[2048];
            StringBuilder stringBuilder = new StringBuilder();
            int num;
            do
            {
                num = sslStream.Read(array, 0, array.Length);
                Decoder decoder = Encoding.UTF8.GetDecoder();
                char[] array2 = new char[decoder.GetCharCount(array, 0, num)];
                decoder.GetChars(array, 0, num, array2, 0);
                stringBuilder.Append(array2);
            }
            while (stringBuilder.ToString().IndexOf("") == -1 && num != 0);
            return stringBuilder.ToString();
        }
    }
}
