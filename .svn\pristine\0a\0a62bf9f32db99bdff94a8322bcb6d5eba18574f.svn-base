﻿using CommonLib;
using System;
using System.Collections.Generic;
using System.Collections.Specialized;
using System.IO;

namespace HanZiOcr
{
    /// <summary>
    /// https://open.ai.xdf.cn/ability/GeneralPrinting/?id=36
    /// </summary>
    public class XinDongFangRec : BaseOcrRec
    {
        public XinDongFangRec()
        {
            OcrType = HanZiOcrType.新东方;

            MaxExecPerTime = 18;

            LstJsonPreProcessArray = new List<object>() { "result", "words_result" };
            IsSupportVertical = true;
        }

        protected override string GetHtml(OcrContent content)
        {
            var byt = Convert.FromBase64String(content.strBase64);
            var result = PostFileResult(byt);
            return result;
        }

        private string PostFileResult(byte[] content)
        {
            var result = "";
            try
            {
                var url = "https://open-api.ai.xdf.cn/xdf-ai-platform-practice/print_form_recognition";
                var file = new UploadFileInfo()
                {
                    Name = "file",
                    Filename = "1.jpg",
                    ContentType = "image/png",
                    Stream = new MemoryStream(content)
                };
                NameValueCollection header = new NameValueCollection() { { "u2-token", "da3fbd49-9875-47a3-8053-e5ffaa88e196" } };//需要登录获取
                result = PostFile(url, new[] { file }, null, header);
            }
            catch (Exception)
            {

            }
            return result;
        }

    }
}