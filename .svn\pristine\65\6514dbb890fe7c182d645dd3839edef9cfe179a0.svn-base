﻿using CommonLib;
using System;
using System.Collections.Generic;
using System.Collections.Specialized;
using System.IO;
using System.Web;

namespace TableOcr
{
    /// <summary>
    /// http://developer.hanvon.com/table/toAllTable.do
    /// </summary>
    public class HanWangRec : BaseTableRec
    {
        public HanWangRec()
        {
            OcrGroup = OcrGroupType.汉王;
            OcrType = TableOcrType.汉王;
            MaxExecPerTime = 20;
            //IsSupportUrlOcr = true;

            LstJsonPreProcessArray = new List<object>() { "pictureData", "body", "tables" };
            LstJsonNextProcessArray = new List<object> { "rows", "|^[]|" };
            LstJsonResultProcessArray = new List<object>() { "content" };
            LstRowIndex = new List<object>() { "rowIndex" };
            LstColumnIndex = new List<object>() { "columnIndex" };
        }

        protected override string GetHtml(OcrContent content)
        {
            var result = "";
            try
            {
                var url = "http://developer.hanvon.com/table/allTableUpload.do";
                var file = new UploadFileInfo()
                {
                    Name = "accessFile",
                    Filename = Guid.NewGuid().ToString() + "." + content.fileExt,
                    ContentType = ApplicationTypeHelper.GetApplicationType(content.fileExt),
                    Stream = new MemoryStream(Convert.FromBase64String(content.strBase64))
                };
                result = PostFile(url, new[] { file }, null);
            }
            catch (Exception)
            {

            }
            return result;
        }

    }
}