﻿using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Net;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using System.Web.Script.Serialization;

namespace CommonLib
{
    public class ServerInfo
    {
        public static DateTime DtNowVersion = DateTime.Parse("2025-04-12 13:00:00");

        private static List<WebInfo> lstAllSite = new List<WebInfo>();
        public static WebInfo HostAccount { get; set; } = new WebInfo() { Host = "ocr.oldfish.cn" };
        public static WebInfo HostUpdate { get; set; } = new WebInfo() { Host = "cdn.oldfish.cn", Https = false };

        private static string strOcrAgent;

        public static bool IsTestModel = false;

        public static void SetTestMode(bool isTest)
        {
            IsTestModel = isTest;
            if (isTest)
            {
                HostAccount = new WebInfo() { Host = "test.oldfish.cn" };
            }
        }

        public static bool Init()
        {
            if (!IsTestModel)
            {
                GetRealIp();
                SetServerUrl();
            }
            return lstAllSite?.Count > 0;
        }

        public static SiteType IsSelfHost(string host)
        {
            return string.IsNullOrEmpty(host) ? SiteType.Default : (HostAccount?.Host?.Contains(host) == true ? SiteType.Account : SiteType.Default);
        }

        public static Uri SetAddress(Uri address, SiteType selfHost)
        {
            var strIp = selfHost == SiteType.Account ? HostAccount?.Ip : "";
            if (!string.IsNullOrEmpty(strIp))
                address = new Uri(address.Scheme + "://" + strIp + (address.Port != 80 ? ":" + address.Port : "") + address.PathAndQuery, false);
            return address;
        }

        public static void SetUserAgent(HttpWebRequest request)
        {
            if (string.IsNullOrEmpty(strOcrAgent))
            {
                strOcrAgent = string.Format("OcrServerAgent/{0}", DtNowVersion.ToString("yyyy-MM-dd HH:mm:ss"));
            }
            request.UserAgent = strOcrAgent;
        }


        private static void SetServerUrl()
        {
            if (lstAllSite.Count > 0
                && (HostAccount == null
                || !lstAllSite.Any(p => Equals(HostAccount.Ip, p.Ip) && Equals(HostAccount.Host, p.Host) && Equals(HostAccount.Https, p.Https))))
            {
                HostAccount = GetNewSite(SiteType.Account);
            }
            if (lstAllSite.Count > 0
                && (HostUpdate == null
                || !lstAllSite.Any(p => Equals(HostUpdate.Ip, p.Ip) && Equals(HostUpdate.Host, p.Host) && Equals(HostUpdate.Https, p.Https))))
            {
                HostUpdate = GetNewSite(SiteType.Update);
            }
        }

        public static void ReportError(string host, string ip)
        {
            var siteType = IsSelfHost(host);
            if (Equals(siteType, SiteType.Default))
            {
                return;
            }
            switch (siteType)
            {
                case SiteType.Account:
                    //如果跟当前Site一致，特殊处理，否则跳过
                    if (Equals(HostAccount.Ip, ip) && HostAccount.Host.Contains(host))
                    {
                        var site = GetNewSite(siteType, host, ip);
                        if (site != null && !string.IsNullOrEmpty(site.Host))
                        {
                            HostAccount = site;
                        }
                    }
                    break;
            }
        }

        private static WebInfo GetNewSite(SiteType type, string host = null, string ip = null)
        {
            var site = new WebInfo();
            var lstSite = lstAllSite.Where(p => Equals(p.Type, type)).ToList();
            if (lstSite.Count <= 0)
            {
                lstSite = lstAllSite.Where(p => Equals(p.Type, SiteType.Default)).ToList();
            }
            //如果池子中不存在，重新设置Host
            if ((string.IsNullOrEmpty(host) && string.IsNullOrEmpty(ip)) || !lstSite.Any(p => p.Host.Contains(host) && Equals(p.Ip, ip)))
            {
                site = lstSite.FirstOrDefault();
            }
            else
            {
                site = lstSite.FirstOrDefault(p => !p.Host.Contains(host) || !Equals(p.Ip, ip));
            }
            return site;
        }

        private static void GetRealIp()
        {
            //Stopwatch stopwatch = Stopwatch.StartNew();
            ConcurrentBag<SiteMain> sites = new ConcurrentBag<SiteMain>();
            Parallel.For(1, 3, (index) =>
            {
                var tmp = GetSiteFromWeb(index);
                if (tmp != null && tmp.web != null && tmp.web.Count > 0)
                    sites.Add(tmp);
            });
            if (sites.Count <= 0)
            {
                return;
            }
            var site = sites.OrderByDescending(p => DateTime.Parse(p.update)).ToList()[0];
            //Console.WriteLine("======耗时：" + stopwatch.ElapsedMilliseconds.ToString("F0") + "ms");
            if (site != null)
            {
                site.web.ForEach(p =>
                {
                    if (string.IsNullOrEmpty(p.Host))
                    {
                        p.Host = site.defaultHost;
                    }
                });
                //site.web[0].Host = "oldfish.azurewebsites.net";
                //site.web[0].Ip = null;
                lstAllSite = site.web;
            }
        }

        private static SiteMain GetSiteFromWeb(int type)
        {
            switch (type)
            {
                case 1:
                    return InitByCName("ocr.oldfish.cn");
                case 2:
                    return InitFromCDN();
                default:
                    return null;
            }
        }

        private static SiteMain InitFromCDN()
        {
            var html = WebClientSyncExt.GetHtml(ServerInfo.HostUpdate?.FullUrl + "site.json?t=" + DateTime.Now.Ticks, 5);
            if (!string.IsNullOrEmpty(html))
            {
                html = Regex.Unescape(html);
            }
            return GetSiteFromStr(html);
        }

        private static List<string> lstDns = new List<string>() { "************", "***************", "*********", "*******", "*******", "" };
        private static JavaScriptSerializer JavaScriptSerializer = new JavaScriptSerializer();

        public static SiteMain InitByCName(string host)
        {
            var result = string.Empty;

            var lstTask = lstDns.AsParallel().Select(dns => Task.Factory.StartNew(() =>
            {
                var strUrl = GetCNameFromNsLookUp(host, dns);

                if (string.IsNullOrEmpty(strUrl) || !strUrl.Contains("."))
                {
                    System.Threading.Thread.Sleep(5000);
                }
                else
                {
                    result = strUrl.Replace("\"\r\n\t\"", "");
                }
            })).ToArray();
            Task.WaitAny(lstTask);
            return GetSiteFromStr(result);
        }

        private static SiteMain GetSiteFromStr(string html)
        {
            SiteMain site = DeserializeJson<SiteMain>(html);
            return site;
        }

        private static T DeserializeJson<T>(string html)
        {
            var result = default(T);
            try
            {
                if (!string.IsNullOrWhiteSpace(html) && (html.Trim().StartsWith("{") || html.Trim().StartsWith("[")))
                    result = JavaScriptSerializer.Deserialize<T>(html);
            }
            catch (Exception oe)
            {
                Console.WriteLine(oe.Message);
            }

            return result;
        }

        private const string strCName = "\"";

        static string GetCNameFromNsLookUp(string strHost, string strNsServer)
        {
            var result = "";
            var strTmp = ExecCmd(string.Format("nslookup -qt=TXT {0} {1}", strHost, strNsServer));
            if (strTmp.Contains(strHost))
                if (strTmp.IndexOf(strCName) > 0)
                {
                    result = CommonHelper.SubString(strTmp, strCName);
                    result = result.Substring(0, result.LastIndexOf(strCName));
                }

            return result;
        }

        static string ExecCmd(string str)
        {
            //process用于调用外部程序
            using (var p = new Process())
            {
                //调用cmd.exe
                p.StartInfo.FileName = "cmd.exe";
                //不显示程序窗口
                p.StartInfo.CreateNoWindow = true;
                //是否指定操作系统外壳进程启动程序
                p.StartInfo.UseShellExecute = false;
                //可能接受来自调用程序的输入信息
                //重定向标准输入
                p.StartInfo.RedirectStandardInput = true;
                //重定向标准输出
                p.StartInfo.RedirectStandardOutput = true;
                //重定向错误输出
                p.StartInfo.RedirectStandardError = true;
                //启动程序
                p.Start();
                //输入命令
                p.StandardInput.WriteLine(str);
                //一定要关闭。
                p.StandardInput.WriteLine("exit");
                return p.StandardOutput.ReadToEnd();
            }
        }
    }

    public class SiteMain
    {
        public string update { get; set; }

        public List<WebInfo> web { get; set; }

        public string defaultHost { get; set; }
    }

    public class WebInfo
    {
        public string Host { get; set; }

        public SiteType Type { get; set; }

        public string Ip { get; set; }

        public string FullUrl { get { return string.Format("http{1}://{0}/", Host, Https ? "s" : ""); } }

        public bool Https { get; set; } = true;
    }

    public enum SiteType
    {
        Default = 0,
        Account = 1,
        Code = 2,
        Update = 3
    }
}
