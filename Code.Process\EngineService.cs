﻿using Code.Process.Common;
using CommonLib;
using System.ServiceProcess;
using System.Threading.Tasks;

namespace Code.Process.Console
{
    partial class EngineService : ServiceBase
    {
        public EngineService()
        {
            InitializeComponent();
        }

        protected override void OnStart(string[] args)
        {
            ProcessNew.StartProcess();
        }

        protected override void OnStop()
        {
            //BaseRecHelper.Report();
            //处理服务的关闭
            ProcessNew.StopProgress();
        }
    }
}
