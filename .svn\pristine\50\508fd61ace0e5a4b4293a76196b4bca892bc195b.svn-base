﻿using CommonLib;
using System.Collections.Generic;
using System.Collections.Specialized;

namespace HanZiOcr
{
    /// <summary>
    /// https://ai.100tal.com/product/ocr-ptr
    /// https://openai.100tal.com/documents/article/page?fromWhichSys=console&id=48
    /// </summary>
    public class XueErSiEduRec : BaseOcrRec
    {
        public XueErSiEduRec()
        {
            OcrGroup = OcrGroupType.学而思;
            OcrType = HanZiOcrType.学而思教育;
            MaxExecPerTime = 20;

            IsSupportUrlOcr = true;

            LstJsonPreProcessArray = new List<object>() { "data", "result" };
            LstJsonNextProcessArray = new List<object>() { "char_info" };
            IsSupportVertical = true;
            IsDesrializeVerticalByLocation = true;
            StrResultJsonSpilt = "char";
            LstVerticalLocation = new List<object>() { "pos" };
        }

        protected override string GetHtml(OcrContent content)
        {
            return processByContent(content.strBase64, null);
        }

        public override string GetHtmlByUrl(OcrContent content)
        {
            return processByContent(null, content.url);
        }

        private string processByContent(string strBase64, string imgUrl)
        {
            var strPost = "{"
                            + (string.IsNullOrEmpty(imgUrl) ? "" : "\"image_url\":\"" + imgUrl + "\",")
                            + "\"function\":0,\"detect_direction\":true"
                            + (string.IsNullOrEmpty(strBase64) ? "" : ",\"image_base64\":\"" + strBase64 + "\"")
                            + ",\"method\":\"/aiimage/comeducation\""
                         + "}";
            var heads = new NameValueCollection() {
                { "auth","HyZUOAMwjG03sMTmSFPjF+w2VhU="},
                { "access_token","undefined"},
            };
            var result = WebClientSyncExt.GetHtml("https://ai.100tal.com/openapi/try/service", strPost, ExecTimeOutSeconds, heads);
            //"{\"code\":0,\"msg\":\"成功\",\"data\":\"[{\\\"confidence\\\":1,\\\"word\\\":[{\\\"content\\\":\\\"请点击下图中所有的靴子\\\"},{\\\"content\\\":\\\"火柴\\\"}]}]\",\"content\":null,\"audioPath\":null}"
            return result;
        }
    }
}