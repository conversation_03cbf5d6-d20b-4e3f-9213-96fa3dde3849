﻿<Project ToolsVersion="15.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="..\packages\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.2.0.1\build\net46\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.props" Condition="Exists('..\packages\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.2.0.1\build\net46\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.props')" />
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProductVersion>
    </ProductVersion>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectGuid>{0658BF46-1107-41C7-900A-F0A2B41037F4}</ProjectGuid>
    <ProjectTypeGuids>{349c5851-65df-11da-9384-00065b846f21};{fae04ec0-301f-11d3-bf4b-00c04f79efbc}</ProjectTypeGuids>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>Code.Process.Web</RootNamespace>
    <AssemblyName>Code.Process.Web</AssemblyName>
    <TargetFrameworkVersion>v4.7.2</TargetFrameworkVersion>
    <UseIISExpress>true</UseIISExpress>
    <Use64BitIISExpress />
    <IISExpressSSLPort>44398</IISExpressSSLPort>
    <IISExpressAnonymousAuthentication />
    <IISExpressWindowsAuthentication />
    <IISExpressUseClassicPipelineMode />
    <UseGlobalApplicationHostFile />
    <NuGetPackageImportStamp>
    </NuGetPackageImportStamp>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="log4net, Version=********, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\DLL\log4net.dll</HintPath>
    </Reference>
    <Reference Include="System.Data" />
    <Reference Include="System.IO.Compression.FileSystem" />
    <Reference Include="System.Web.DynamicData" />
    <Reference Include="System.Web.ApplicationServices" />
    <Reference Include="System" />
    <Reference Include="System.Core" />
    <Reference Include="System.Web.Extensions" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Web" />
    <Reference Include="System.Configuration" />
    <Reference Include="System.EnterpriseServices" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Reference Include="Microsoft.CodeDom.Providers.DotNetCompilerPlatform">
      <HintPath>..\packages\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.2.0.1\lib\net45\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.dll</HintPath>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <Content Include="Default.aspx" />
    <Content Include="Global.asax" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="CommonUpdate.cs" />
    <Compile Include="Global.asax.cs">
      <DependentUpon>Global.asax</DependentUpon>
    </Compile>
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="TimerTaskService.cs" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="Log4Net.config">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="AutoRefresh.bat" />
    <Content Include="CreateRefreshTask.bat" />
    <None Include="packages.config" />
    <None Include="Properties\PublishProfiles\FolderProfile.pubxml" />
    <None Include="Properties\PublishProfiles\oldfish - Web Deploy.pubxml" />
    <None Include="Properties\PublishProfiles\oldfishaustraliacentral - Web Deploy.pubxml" />
    <None Include="Properties\PublishProfiles\oldfishaustraliaeast - Web Deploy.pubxml" />
    <None Include="Properties\PublishProfiles\oldfishaustraliasoutheast - Web Deploy.pubxml" />
    <None Include="Properties\PublishProfiles\oldfishcanadacentral - Web Deploy.pubxml" />
    <None Include="Properties\PublishProfiles\oldfishcanadaeast - Web Deploy.pubxml" />
    <None Include="Properties\PublishProfiles\oldfishCentralIndia - Web Deploy.pubxml" />
    <None Include="Properties\PublishProfiles\oldfisheastus - Web Deploy.pubxml" />
    <None Include="Properties\PublishProfiles\oldfisheastus2 - Web Deploy.pubxml" />
    <None Include="Properties\PublishProfiles\oldfishfrancecentral - Web Deploy.pubxml" />
    <None Include="Properties\PublishProfiles\oldfishGermanyWestCentral - Web Deploy.pubxml" />
    <None Include="Properties\PublishProfiles\oldfishjp - Web Deploy.pubxml" />
    <None Include="Properties\PublishProfiles\oldfishko - Web Deploy.pubxml" />
    <None Include="Properties\PublishProfiles\oldfishkocentral - Web Deploy.pubxml" />
    <None Include="Properties\PublishProfiles\oldfishkojpwest - Web Deploy.pubxml" />
    <None Include="Properties\PublishProfiles\oldfishnorthcentralus - Web Deploy.pubxml" />
    <None Include="Properties\PublishProfiles\oldfishnortheurope - Web Deploy.pubxml" />
    <None Include="Properties\PublishProfiles\oldfishNorwayEast - Web Deploy.pubxml" />
    <None Include="Properties\PublishProfiles\oldfishpolandcentral - FTP.pubxml" />
    <None Include="Properties\PublishProfiles\oldfishpolandcentral - FTP1.pubxml" />
    <None Include="Properties\PublishProfiles\oldfishpolandcentral - Web Deploy.pubxml" />
    <None Include="Properties\PublishProfiles\oldfishpolandcentral - Web Deploy1.pubxml" />
    <None Include="Properties\PublishProfiles\oldfishqatarcentral - FTP.pubxml" />
    <None Include="Properties\PublishProfiles\oldfishqatarcentral - Web Deploy.pubxml" />
    <None Include="Properties\PublishProfiles\oldfishSouthAfricaNorth - Web Deploy.pubxml" />
    <None Include="Properties\PublishProfiles\oldfishsouthcentralus - Web Deploy.pubxml" />
    <None Include="Properties\PublishProfiles\oldfishsouthindia - Web Deploy.pubxml" />
    <None Include="Properties\PublishProfiles\oldfishSwedenCentral - Web Deploy.pubxml" />
    <None Include="Properties\PublishProfiles\oldfishswitzerlandnorth - Web Deploy.pubxml" />
    <None Include="Properties\PublishProfiles\oldfishuaenorth - Web Deploy.pubxml" />
    <None Include="Properties\PublishProfiles\oldfishuksouth - Web Deploy.pubxml" />
    <None Include="Properties\PublishProfiles\oldfishukwest - Web Deploy.pubxml" />
    <None Include="Properties\PublishProfiles\oldfishwestcentralus - Web Deploy.pubxml" />
    <None Include="Properties\PublishProfiles\oldfishwesteurope - Web Deploy.pubxml" />
    <None Include="Properties\PublishProfiles\oldfishwestus - Web Deploy.pubxml" />
    <None Include="Properties\PublishProfiles\oldfishwestus3 - Web Deploy.pubxml" />
    <None Include="Properties\PublishProfiles\oldfishxjp - Web Deploy.pubxml" />
    <None Include="Properties\PublishProfiles\中国-香港.pubxml" />
    <None Include="Properties\PublishProfiles\加拿大-安大略.pubxml" />
    <None Include="Properties\PublishProfiles\加拿大-魁北克.pubxml" />
    <None Include="Properties\PublishProfiles\南非-豪登.pubxml" />
    <None Include="Properties\PublishProfiles\印度-泰米尔纳德.pubxml" />
    <None Include="Properties\PublishProfiles\印度-马哈拉施特拉.pubxml" />
    <None Include="Properties\PublishProfiles\巴西-圣保罗.pubxml" />
    <None Include="Properties\PublishProfiles\德国-黑森.pubxml" />
    <None Include="Properties\PublishProfiles\挪威-奥斯陆.pubxml" />
    <None Include="Properties\PublishProfiles\新加坡.pubxml" />
    <None Include="Properties\PublishProfiles\日本-东京.pubxml" />
    <None Include="Properties\PublishProfiles\日本-大阪.pubxml" />
    <None Include="Properties\PublishProfiles\法国-法兰西岛.pubxml" />
    <None Include="Properties\PublishProfiles\澳大利亚-新南威尔士.pubxml" />
    <None Include="Properties\PublishProfiles\澳大利亚-维多利亚.pubxml" />
    <None Include="Properties\PublishProfiles\澳大利亚-首都领地.pubxml" />
    <None Include="Properties\PublishProfiles\爱尔兰-都柏林.pubxml" />
    <None Include="Properties\PublishProfiles\瑞典-耶夫勒堡.pubxml" />
    <None Include="Properties\PublishProfiles\瑞士-苏黎世.pubxml" />
    <None Include="Properties\PublishProfiles\美国-亚利桑那.pubxml" />
    <None Include="Properties\PublishProfiles\美国-伊利诺伊.pubxml" />
    <None Include="Properties\PublishProfiles\美国-加利福尼亚.pubxml" />
    <None Include="Properties\PublishProfiles\美国-弗吉尼亚.pubxml" />
    <None Include="Properties\PublishProfiles\美国-弗吉尼亚2.pubxml" />
    <None Include="Properties\PublishProfiles\美国-得克萨斯.pubxml" />
    <None Include="Properties\PublishProfiles\美国-怀俄明.pubxml" />
    <None Include="Properties\PublishProfiles\美国-艾奥瓦.pubxml" />
    <None Include="Properties\PublishProfiles\英国-威尔士.pubxml" />
    <None Include="Properties\PublishProfiles\英国-英格兰.pubxml" />
    <None Include="Properties\PublishProfiles\荷兰-北荷兰.pubxml" />
    <None Include="Properties\PublishProfiles\阿联酋-迪拜.pubxml" />
    <None Include="Properties\PublishProfiles\韩国-釜山.pubxml" />
    <None Include="Properties\PublishProfiles\韩国-首尔.pubxml" />
    <Content Include="Web.config" />
    <Content Include="UpdateSite.bat" />
    <None Include="Web.Debug.config">
      <DependentUpon>Web.config</DependentUpon>
    </None>
    <None Include="Web.Release.config">
      <DependentUpon>Web.config</DependentUpon>
    </None>
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\Code.Process.Common\Code.Process.Common.csproj">
      <Project>{623361cf-3e9c-4c1a-b519-2612f0349fc3}</Project>
      <Name>Code.Process.Common</Name>
    </ProjectReference>
    <ProjectReference Include="..\CommonLib\CommonLib.csproj">
      <Project>{fc03a7d4-8ef2-4dea-a15a-c099eb77b0eb}</Project>
      <Name>CommonLib</Name>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup />
  <PropertyGroup>
    <VisualStudioVersion Condition="'$(VisualStudioVersion)' == ''">10.0</VisualStudioVersion>
    <VSToolsPath Condition="'$(VSToolsPath)' == ''">$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v$(VisualStudioVersion)</VSToolsPath>
  </PropertyGroup>
  <Import Project="$(MSBuildBinPath)\Microsoft.CSharp.targets" />
  <Import Project="$(VSToolsPath)\WebApplications\Microsoft.WebApplication.targets" Condition="'$(VSToolsPath)' != ''" />
  <Import Project="$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v10.0\WebApplications\Microsoft.WebApplication.targets" Condition="false" />
  <ProjectExtensions>
    <VisualStudio>
      <FlavorProperties GUID="{349c5851-65df-11da-9384-00065b846f21}">
        <WebProjectProperties>
          <UseIIS>True</UseIIS>
          <AutoAssignPort>True</AutoAssignPort>
          <DevelopmentServerPort>62635</DevelopmentServerPort>
          <DevelopmentServerVPath>/</DevelopmentServerVPath>
          <IISUrl>http://localhost:44398/</IISUrl>
          <NTLMAuthentication>False</NTLMAuthentication>
          <UseCustomServer>False</UseCustomServer>
          <CustomServerUrl>
          </CustomServerUrl>
          <SaveServerSettingsInUserFile>False</SaveServerSettingsInUserFile>
        </WebProjectProperties>
      </FlavorProperties>
    </VisualStudio>
  </ProjectExtensions>
  <Target Name="EnsureNuGetPackageBuildImports" BeforeTargets="PrepareForBuild">
    <PropertyGroup>
      <ErrorText>This project references NuGet package(s) that are missing on this computer. Use NuGet Package Restore to download them.  For more information, see http://go.microsoft.com/fwlink/?LinkID=322105. The missing file is {0}.</ErrorText>
    </PropertyGroup>
    <Error Condition="!Exists('..\packages\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.2.0.1\build\net46\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.props')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.2.0.1\build\net46\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.props'))" />
  </Target>
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>