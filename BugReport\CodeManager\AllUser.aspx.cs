﻿using System;
using System.Collections.Generic;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Data;
using ToolCommon;

namespace BugReprot
{
    public partial class AllUser : System.Web.UI.Page
    {
        protected void Page_Load(object sender, EventArgs e)
        {
            
        }

        protected void btnOK_Click(object sender, EventArgs e)
        {
            if (BoxUtil.GetStringFromObject(Request.QueryString["pwd"]) != CommonHelper.StrPWD)
                return;
            DataTable dtTmp = CodeHelper.GetAllCode();
            lblCount.Text = dtTmp.Rows.Count.ToString();
            gvDataSource.DataSource = dtTmp;
            gvDataSource.DataBind();
        }

        protected void btnVacuum_Click(object sender, EventArgs e)
        {
            if (BoxUtil.GetStringFromObject(Request.QueryString["pwd"]) != CommonHelper.StrPWD)
                return;
            CodeHelper.Vacuum();
        }
    }
}