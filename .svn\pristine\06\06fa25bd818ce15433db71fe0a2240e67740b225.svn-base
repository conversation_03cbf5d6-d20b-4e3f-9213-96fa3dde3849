﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Security;
using System.Web.SessionState;
using System.Net;
using System.Threading;
using System.Security.Cryptography.X509Certificates;
using System.Net.Security;

namespace GoogleAPI
{
    public class Global : System.Web.HttpApplication
    {

        protected void Application_Start(object sender, EventArgs e)
        {
            try
            {
                ServicePointManager.ServerCertificateValidationCallback = ((object obj, X509Certificate certificate, X509Chain chain, SslPolicyErrors sslPolicyErrors) => true);

                ServicePointManager.DefaultConnectionLimit = int.MaxValue;
                //ServicePointManager.MaxServicePoints = int.MaxValue;
                ThreadPool.SetMinThreads(100, 100);
                if (ThreadPool.SetMaxThreads(Environment.ProcessorCount * 200, Environment.ProcessorCount * 100))
                {
                    Console.WriteLine();
                }
            }
            catch { }
        }

        protected void Session_Start(object sender, EventArgs e)
        {

        }

        protected void Application_BeginRequest(object sender, EventArgs e)
        {

        }

        protected void Application_AuthenticateRequest(object sender, EventArgs e)
        {

        }

        protected void Application_Error(object sender, EventArgs e)
        {

        }

        protected void Session_End(object sender, EventArgs e)
        {

        }

        protected void Application_End(object sender, EventArgs e)
        {

        }
    }
}