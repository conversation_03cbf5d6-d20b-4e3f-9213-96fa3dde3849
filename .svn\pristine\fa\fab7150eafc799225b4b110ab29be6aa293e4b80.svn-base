let __importScript=filename=>{fetch(filename).then(e=>e.text()).then(jsText=>eval(jsText))},JsBeautifier=function(){var e=function(e){var t={};function n(i){if(t[i])return t[i].exports;var s=t[i]={i:i,l:!1,exports:{}};return e[i].call(s.exports,s,s.exports,n),s.l=!0,s.exports}return n.m=e,n.c=t,n.d=function(e,t,i){n.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:i})},n.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},n.t=function(e,t){if(1&t&&(e=n(e)),8&t)return e;if(4&t&&"object"==typeof e&&e&&e.__esModule)return e;var i=Object.create(null);if(n.r(i),Object.defineProperty(i,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var s in e)n.d(i,s,function(t){return e[t]}.bind(null,s));return i},n.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return n.d(t,"a",t),t},n.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},n.p="",n(n.s=0)}([function(e,t,n){"use strict";var i=n(1).Beautifier,s=n(5).Options;e.exports=function(e,t){return new i(e,t).beautify()},e.exports.defaultOptions=function(){return new s}},function(e,t,n){"use strict";var i=n(2).Output,s=n(3).Token,r=n(4),a=n(5).Options,o=n(7).Tokenizer,l=n(7).line_starters,c=n(7).positionable_operators,_=n(7).TOKEN;function p(e,t){t.multiline_frame||t.mode===m.ForInitializer||t.mode===m.Conditional||e.remove_indent(t.start_line_index)}function u(e,t){return-1!==t.indexOf(e)}function d(e,t){return e&&e.type===_.RESERVED&&e.text===t}function h(e,t){return e&&e.type===_.RESERVED&&u(e.text,t)}var f=["case","return","do","if","throw","else","await","break","continue","async"],b=function(e){for(var t={},n=0;n<e.length;n++)t[e[n].replace(/-/g,"_")]=e[n];return t}(["before-newline","after-newline","preserve-newline"]),g=[b.before_newline,b.preserve_newline],m={BlockStatement:"BlockStatement",Statement:"Statement",ObjectLiteral:"ObjectLiteral",ArrayLiteral:"ArrayLiteral",ForInitializer:"ForInitializer",Conditional:"Conditional",Expression:"Expression"};function v(e){return e===m.ArrayLiteral}function y(e){return u(e,[m.Expression,m.ForInitializer,m.Conditional])}function w(e,t){t=t||{},this._source_text=e||"",this._output=null,this._tokens=null,this._last_last_text=null,this._flags=null,this._previous_flags=null,this._flag_store=null,this._options=new a(t)}w.prototype.create_flags=function(e,t){var n=0;return e&&(n=e.indentation_level,!this._output.just_added_newline()&&e.line_indent_level>n&&(n=e.line_indent_level)),{mode:t,parent:e,last_token:e?e.last_token:new s(_.START_BLOCK,""),last_word:e?e.last_word:"",declaration_statement:!1,declaration_assignment:!1,multiline_frame:!1,inline_frame:!1,if_block:!1,else_block:!1,do_block:!1,do_while:!1,import_block:!1,in_case_statement:!1,in_case:!1,case_body:!1,indentation_level:n,alignment:0,line_indent_level:e?e.line_indent_level:n,start_line_index:this._output.get_line_number(),ternary_depth:0}},w.prototype._reset=function(e){var t=e.match(/^[\t ]*/)[0];this._last_last_text="",this._output=new i(this._options,t),this._output.raw=this._options.test_output_raw,this._flag_store=[],this.set_mode(m.BlockStatement);var n=new o(e,this._options);return this._tokens=n.tokenize(),e},w.prototype.beautify=function(){if(this._options.disabled)return this._source_text;var e=this._reset(this._source_text),t=this._options.eol;"auto"===this._options.eol&&(t="\n",e&&r.lineBreak.test(e||"")&&(t=e.match(r.lineBreak)[0]));for(var n=this._tokens.next();n;)this.handle_token(n),this._last_last_text=this._flags.last_token.text,this._flags.last_token=n,n=this._tokens.next();return this._output.get_code(t)},w.prototype.handle_token=function(e,t){e.type===_.START_EXPR?this.handle_start_expr(e):e.type===_.END_EXPR?this.handle_end_expr(e):e.type===_.START_BLOCK?this.handle_start_block(e):e.type===_.END_BLOCK?this.handle_end_block(e):e.type===_.WORD?this.handle_word(e):e.type===_.RESERVED?this.handle_word(e):e.type===_.SEMICOLON?this.handle_semicolon(e):e.type===_.STRING?this.handle_string(e):e.type===_.EQUALS?this.handle_equals(e):e.type===_.OPERATOR?this.handle_operator(e):e.type===_.COMMA?this.handle_comma(e):e.type===_.BLOCK_COMMENT?this.handle_block_comment(e,t):e.type===_.COMMENT?this.handle_comment(e,t):e.type===_.DOT?this.handle_dot(e):e.type===_.EOF?this.handle_eof(e):(e.type,_.UNKNOWN,this.handle_unknown(e,t))},w.prototype.handle_whitespace_and_comments=function(e,t){var n=e.newlines,i=this._options.keep_array_indentation&&v(this._flags.mode);if(e.comments_before)for(var s=e.comments_before.next();s;)this.handle_whitespace_and_comments(s,t),this.handle_token(s,t),s=e.comments_before.next();if(i)for(var r=0;r<n;r+=1)this.print_newline(r>0,t);else if(this._options.max_preserve_newlines&&n>this._options.max_preserve_newlines&&(n=this._options.max_preserve_newlines),this._options.preserve_newlines&&n>1){this.print_newline(!1,t);for(var a=1;a<n;a+=1)this.print_newline(!0,t)}};var k=["async","break","continue","return","throw","yield"];w.prototype.allow_wrap_or_preserved_newline=function(e,t){if(t=void 0!==t&&t,!this._output.just_added_newline()){var n=this._options.preserve_newlines&&e.newlines||t;if(u(this._flags.last_token.text,c)||u(e.text,c)){var i=u(this._flags.last_token.text,c)&&u(this._options.operator_position,g)||u(e.text,c);n=n&&i}if(n)this.print_newline(!1,!0);else if(this._options.wrap_line_length){if(h(this._flags.last_token,k))return;this._output.current_line.get_character_count()+e.text.length+(this._output.space_before_token?1:0)>=this._options.wrap_line_length&&this.print_newline(!1,!0)}}},w.prototype.print_newline=function(e,t){if(!t&&";"!==this._flags.last_token.text&&","!==this._flags.last_token.text&&"="!==this._flags.last_token.text&&(this._flags.last_token.type!==_.OPERATOR||"--"===this._flags.last_token.text||"++"===this._flags.last_token.text))for(var n=this._tokens.peek();!(this._flags.mode!==m.Statement||this._flags.if_block&&d(n,"else")||this._flags.do_block);)this.restore_mode();this._output.add_new_line(e)&&(this._flags.multiline_frame=!0)},w.prototype.print_token_line_indentation=function(e){this._output.just_added_newline()&&(this._options.keep_array_indentation&&v(this._flags.mode)&&e.newlines?(this._output.current_line.push(e.whitespace_before),this._output.space_before_token=!1):this._output.set_indent(this._flags.indentation_level,this._flags.alignment)&&(this._flags.line_indent_level=this._flags.indentation_level))},w.prototype.print_token=function(e,t){if(this._output.raw)this._output.add_raw_token(e);else{if(this._options.comma_first&&e.previous&&e.previous.type===_.COMMA&&this._output.just_added_newline()&&","===this._output.previous_line.last()){var n=this._output.previous_line.pop();this._output.previous_line.is_empty()&&(this._output.previous_line.push(n),this._output.trim(!0),this._output.current_line.pop(),this._output.trim()),this.print_token_line_indentation(e),this._output.add_token(","),this._output.space_before_token=!0}t=t||e.text,this.print_token_line_indentation(e),this._output.add_token(t)}},w.prototype.indent=function(){this._flags.indentation_level+=1},w.prototype.deindent=function(){this._flags.indentation_level>0&&(!this._flags.parent||this._flags.indentation_level>this._flags.parent.indentation_level)&&(this._flags.indentation_level-=1)},w.prototype.set_mode=function(e){this._flags?(this._flag_store.push(this._flags),this._previous_flags=this._flags):this._previous_flags=this.create_flags(null,e),this._flags=this.create_flags(this._previous_flags,e)},w.prototype.restore_mode=function(){this._flag_store.length>0&&(this._previous_flags=this._flags,this._flags=this._flag_store.pop(),this._previous_flags.mode===m.Statement&&p(this._output,this._previous_flags))},w.prototype.start_of_object_property=function(){return this._flags.parent.mode===m.ObjectLiteral&&this._flags.mode===m.Statement&&(":"===this._flags.last_token.text&&0===this._flags.ternary_depth||h(this._flags.last_token,["get","set"]))},w.prototype.start_of_statement=function(e){var t=!1;return!!(t=(t=(t=(t=(t=(t=(t=t||h(this._flags.last_token,["var","let","const"])&&e.type===_.WORD)||d(this._flags.last_token,"do"))||!(this._flags.parent.mode===m.ObjectLiteral&&this._flags.mode===m.Statement)&&h(this._flags.last_token,k)&&!e.newlines)||d(this._flags.last_token,"else")&&!(d(e,"if")&&!e.comments_before))||this._flags.last_token.type===_.END_EXPR&&(this._previous_flags.mode===m.ForInitializer||this._previous_flags.mode===m.Conditional))||this._flags.last_token.type===_.WORD&&this._flags.mode===m.BlockStatement&&!this._flags.in_case&&!("--"===e.text||"++"===e.text)&&"function"!==this._last_last_text&&e.type!==_.WORD&&e.type!==_.RESERVED)||this._flags.mode===m.ObjectLiteral&&(":"===this._flags.last_token.text&&0===this._flags.ternary_depth||h(this._flags.last_token,["get","set"])))&&(this.set_mode(m.Statement),this.indent(),this.handle_whitespace_and_comments(e,!0),this.start_of_object_property()||this.allow_wrap_or_preserved_newline(e,h(e,["do","for","if","while"])),!0)},w.prototype.handle_start_expr=function(e){this.start_of_statement(e)||this.handle_whitespace_and_comments(e);var t=m.Expression;if("["===e.text){if(this._flags.last_token.type===_.WORD||")"===this._flags.last_token.text)return h(this._flags.last_token,l)&&(this._output.space_before_token=!0),this.set_mode(t),this.print_token(e),this.indent(),void(this._options.space_in_paren&&(this._output.space_before_token=!0));t=m.ArrayLiteral,v(this._flags.mode)&&("["!==this._flags.last_token.text&&(","!==this._flags.last_token.text||"]"!==this._last_last_text&&"}"!==this._last_last_text)||this._options.keep_array_indentation||this.print_newline()),u(this._flags.last_token.type,[_.START_EXPR,_.END_EXPR,_.WORD,_.OPERATOR])||(this._output.space_before_token=!0)}else{if(this._flags.last_token.type===_.RESERVED)"for"===this._flags.last_token.text?(this._output.space_before_token=this._options.space_before_conditional,t=m.ForInitializer):u(this._flags.last_token.text,["if","while"])?(this._output.space_before_token=this._options.space_before_conditional,t=m.Conditional):u(this._flags.last_word,["await","async"])?this._output.space_before_token=!0:"import"===this._flags.last_token.text&&""===e.whitespace_before?this._output.space_before_token=!1:(u(this._flags.last_token.text,l)||"catch"===this._flags.last_token.text)&&(this._output.space_before_token=!0);else if(this._flags.last_token.type===_.EQUALS||this._flags.last_token.type===_.OPERATOR)this.start_of_object_property()||this.allow_wrap_or_preserved_newline(e);else if(this._flags.last_token.type===_.WORD){this._output.space_before_token=!1;var n=this._tokens.peek(-3);if(this._options.space_after_named_function&&n){var i=this._tokens.peek(-4);h(n,["async","function"])||"*"===n.text&&h(i,["async","function"])?this._output.space_before_token=!0:this._flags.mode===m.ObjectLiteral&&("{"!==n.text&&","!==n.text&&("*"!==n.text||"{"!==i.text&&","!==i.text)||(this._output.space_before_token=!0))}}else this.allow_wrap_or_preserved_newline(e);(this._flags.last_token.type===_.RESERVED&&("function"===this._flags.last_word||"typeof"===this._flags.last_word)||"*"===this._flags.last_token.text&&(u(this._last_last_text,["function","yield"])||this._flags.mode===m.ObjectLiteral&&u(this._last_last_text,["{",","])))&&(this._output.space_before_token=this._options.space_after_anon_function)}";"===this._flags.last_token.text||this._flags.last_token.type===_.START_BLOCK?this.print_newline():this._flags.last_token.type!==_.END_EXPR&&this._flags.last_token.type!==_.START_EXPR&&this._flags.last_token.type!==_.END_BLOCK&&"."!==this._flags.last_token.text&&this._flags.last_token.type!==_.COMMA||this.allow_wrap_or_preserved_newline(e,e.newlines),this.set_mode(t),this.print_token(e),this._options.space_in_paren&&(this._output.space_before_token=!0),this.indent()},w.prototype.handle_end_expr=function(e){for(;this._flags.mode===m.Statement;)this.restore_mode();this.handle_whitespace_and_comments(e),this._flags.multiline_frame&&this.allow_wrap_or_preserved_newline(e,"]"===e.text&&v(this._flags.mode)&&!this._options.keep_array_indentation),this._options.space_in_paren&&(this._flags.last_token.type!==_.START_EXPR||this._options.space_in_empty_paren?this._output.space_before_token=!0:(this._output.trim(),this._output.space_before_token=!1)),"]"===e.text&&this._options.keep_array_indentation?(this.print_token(e),this.restore_mode()):(this.restore_mode(),this.print_token(e)),p(this._output,this._previous_flags),this._flags.do_while&&this._previous_flags.mode===m.Conditional&&(this._previous_flags.mode=m.Expression,this._flags.do_block=!1,this._flags.do_while=!1)},w.prototype.handle_start_block=function(e){this.handle_whitespace_and_comments(e);var t=this._tokens.peek(),n=this._tokens.peek(1);"switch"===this._flags.last_word&&this._flags.last_token.type===_.END_EXPR?(this.set_mode(m.BlockStatement),this._flags.in_case_statement=!0):n&&(u(n.text,[":",","])&&u(t.type,[_.STRING,_.WORD,_.RESERVED])||u(t.text,["get","set","..."])&&u(n.type,[_.WORD,_.RESERVED]))?u(this._last_last_text,["class","interface"])?this.set_mode(m.BlockStatement):this.set_mode(m.ObjectLiteral):this._flags.last_token.type===_.OPERATOR&&"=>"===this._flags.last_token.text?this.set_mode(m.BlockStatement):u(this._flags.last_token.type,[_.EQUALS,_.START_EXPR,_.COMMA,_.OPERATOR])||h(this._flags.last_token,["return","throw","import","default"])?this.set_mode(m.ObjectLiteral):this.set_mode(m.BlockStatement);var i=!t.comments_before&&"}"===t.text,s=i&&"function"===this._flags.last_word&&this._flags.last_token.type===_.END_EXPR;if(this._options.brace_preserve_inline){var r=0,a=null;this._flags.inline_frame=!0;do{if(r+=1,(a=this._tokens.peek(r-1)).newlines){this._flags.inline_frame=!1;break}}while(a.type!==_.EOF&&(a.type!==_.END_BLOCK||a.opened!==e))}("expand"===this._options.brace_style||"none"===this._options.brace_style&&e.newlines)&&!this._flags.inline_frame?this._flags.last_token.type!==_.OPERATOR&&(s||this._flags.last_token.type===_.EQUALS||h(this._flags.last_token,f)&&"else"!==this._flags.last_token.text)?this._output.space_before_token=!0:this.print_newline(!1,!0):(!v(this._previous_flags.mode)||this._flags.last_token.type!==_.START_EXPR&&this._flags.last_token.type!==_.COMMA||((this._flags.last_token.type===_.COMMA||this._options.space_in_paren)&&(this._output.space_before_token=!0),(this._flags.last_token.type===_.COMMA||this._flags.last_token.type===_.START_EXPR&&this._flags.inline_frame)&&(this.allow_wrap_or_preserved_newline(e),this._previous_flags.multiline_frame=this._previous_flags.multiline_frame||this._flags.multiline_frame,this._flags.multiline_frame=!1)),this._flags.last_token.type!==_.OPERATOR&&this._flags.last_token.type!==_.START_EXPR&&(this._flags.last_token.type!==_.START_BLOCK||this._flags.inline_frame?this._output.space_before_token=!0:this.print_newline())),this.print_token(e),this.indent(),i||this._options.brace_preserve_inline&&this._flags.inline_frame||this.print_newline()},w.prototype.handle_end_block=function(e){for(this.handle_whitespace_and_comments(e);this._flags.mode===m.Statement;)this.restore_mode();var t=this._flags.last_token.type===_.START_BLOCK;this._flags.inline_frame&&!t?this._output.space_before_token=!0:"expand"===this._options.brace_style?t||this.print_newline():t||(v(this._flags.mode)&&this._options.keep_array_indentation?(this._options.keep_array_indentation=!1,this.print_newline(),this._options.keep_array_indentation=!0):this.print_newline()),this.restore_mode(),this.print_token(e)},w.prototype.handle_word=function(e){if(e.type===_.RESERVED)if(u(e.text,["set","get"])&&this._flags.mode!==m.ObjectLiteral)e.type=_.WORD;else if("import"===e.text&&"("===this._tokens.peek().text)e.type=_.WORD;else if(u(e.text,["as","from"])&&!this._flags.import_block)e.type=_.WORD;else if(this._flags.mode===m.ObjectLiteral){":"===this._tokens.peek().text&&(e.type=_.WORD)}if(this.start_of_statement(e)?h(this._flags.last_token,["var","let","const"])&&e.type===_.WORD&&(this._flags.declaration_statement=!0):!e.newlines||y(this._flags.mode)||this._flags.last_token.type===_.OPERATOR&&"--"!==this._flags.last_token.text&&"++"!==this._flags.last_token.text||this._flags.last_token.type===_.EQUALS||!this._options.preserve_newlines&&h(this._flags.last_token,["var","let","const","set","get"])?this.handle_whitespace_and_comments(e):(this.handle_whitespace_and_comments(e),this.print_newline()),this._flags.do_block&&!this._flags.do_while){if(d(e,"while"))return this._output.space_before_token=!0,this.print_token(e),this._output.space_before_token=!0,void(this._flags.do_while=!0);this.print_newline(),this._flags.do_block=!1}if(this._flags.if_block)if(!this._flags.else_block&&d(e,"else"))this._flags.else_block=!0;else{for(;this._flags.mode===m.Statement;)this.restore_mode();this._flags.if_block=!1,this._flags.else_block=!1}if(this._flags.in_case_statement&&h(e,["case","default"]))return this.print_newline(),(this._flags.case_body||this._options.jslint_happy)&&(this.deindent(),this._flags.case_body=!1),this.print_token(e),void(this._flags.in_case=!0);if(this._flags.last_token.type!==_.COMMA&&this._flags.last_token.type!==_.START_EXPR&&this._flags.last_token.type!==_.EQUALS&&this._flags.last_token.type!==_.OPERATOR||this.start_of_object_property()||this.allow_wrap_or_preserved_newline(e),d(e,"function"))return(u(this._flags.last_token.text,["}",";"])||this._output.just_added_newline()&&!u(this._flags.last_token.text,["(","[","{",":","=",","])&&this._flags.last_token.type!==_.OPERATOR)&&(this._output.just_added_blankline()||e.comments_before||(this.print_newline(),this.print_newline(!0))),this._flags.last_token.type===_.RESERVED||this._flags.last_token.type===_.WORD?h(this._flags.last_token,["get","set","new","export"])||h(this._flags.last_token,k)?this._output.space_before_token=!0:d(this._flags.last_token,"default")&&"export"===this._last_last_text?this._output.space_before_token=!0:"declare"===this._flags.last_token.text?this._output.space_before_token=!0:this.print_newline():this._flags.last_token.type===_.OPERATOR||"="===this._flags.last_token.text?this._output.space_before_token=!0:(this._flags.multiline_frame||!y(this._flags.mode)&&!v(this._flags.mode))&&this.print_newline(),this.print_token(e),void(this._flags.last_word=e.text);var t="NONE";(this._flags.last_token.type===_.END_BLOCK?this._previous_flags.inline_frame?t="SPACE":h(e,["else","catch","finally","from"])?"expand"===this._options.brace_style||"end-expand"===this._options.brace_style||"none"===this._options.brace_style&&e.newlines?t="NEWLINE":(t="SPACE",this._output.space_before_token=!0):t="NEWLINE":this._flags.last_token.type===_.SEMICOLON&&this._flags.mode===m.BlockStatement?t="NEWLINE":this._flags.last_token.type===_.SEMICOLON&&y(this._flags.mode)?t="SPACE":this._flags.last_token.type===_.STRING?t="NEWLINE":this._flags.last_token.type===_.RESERVED||this._flags.last_token.type===_.WORD||"*"===this._flags.last_token.text&&(u(this._last_last_text,["function","yield"])||this._flags.mode===m.ObjectLiteral&&u(this._last_last_text,["{",","]))?t="SPACE":this._flags.last_token.type===_.START_BLOCK?t=this._flags.inline_frame?"SPACE":"NEWLINE":this._flags.last_token.type===_.END_EXPR&&(this._output.space_before_token=!0,t="NEWLINE"),h(e,l)&&")"!==this._flags.last_token.text&&(t=this._flags.inline_frame||"else"===this._flags.last_token.text||"export"===this._flags.last_token.text?"SPACE":"NEWLINE"),h(e,["else","catch","finally"]))?(this._flags.last_token.type!==_.END_BLOCK||this._previous_flags.mode!==m.BlockStatement||"expand"===this._options.brace_style||"end-expand"===this._options.brace_style||"none"===this._options.brace_style&&e.newlines)&&!this._flags.inline_frame?this.print_newline():(this._output.trim(!0),"}"!==this._output.current_line.last()&&this.print_newline(),this._output.space_before_token=!0):"NEWLINE"===t?h(this._flags.last_token,f)?this._output.space_before_token=!0:"declare"===this._flags.last_token.text&&h(e,["var","let","const"])?this._output.space_before_token=!0:this._flags.last_token.type!==_.END_EXPR?this._flags.last_token.type===_.START_EXPR&&h(e,["var","let","const"])||":"===this._flags.last_token.text||(d(e,"if")&&d(e.previous,"else")?this._output.space_before_token=!0:this.print_newline()):h(e,l)&&")"!==this._flags.last_token.text&&this.print_newline():this._flags.multiline_frame&&v(this._flags.mode)&&","===this._flags.last_token.text&&"}"===this._last_last_text?this.print_newline():"SPACE"===t&&(this._output.space_before_token=!0);!e.previous||e.previous.type!==_.WORD&&e.previous.type!==_.RESERVED||(this._output.space_before_token=!0),this.print_token(e),this._flags.last_word=e.text,e.type===_.RESERVED&&("do"===e.text?this._flags.do_block=!0:"if"===e.text?this._flags.if_block=!0:"import"===e.text?this._flags.import_block=!0:this._flags.import_block&&d(e,"from")&&(this._flags.import_block=!1))},w.prototype.handle_semicolon=function(e){this.start_of_statement(e)?this._output.space_before_token=!1:this.handle_whitespace_and_comments(e);for(var t=this._tokens.peek();!(this._flags.mode!==m.Statement||this._flags.if_block&&d(t,"else")||this._flags.do_block);)this.restore_mode();this._flags.import_block&&(this._flags.import_block=!1),this.print_token(e)},w.prototype.handle_string=function(e){this.start_of_statement(e)?this._output.space_before_token=!0:(this.handle_whitespace_and_comments(e),this._flags.last_token.type===_.RESERVED||this._flags.last_token.type===_.WORD||this._flags.inline_frame?this._output.space_before_token=!0:this._flags.last_token.type===_.COMMA||this._flags.last_token.type===_.START_EXPR||this._flags.last_token.type===_.EQUALS||this._flags.last_token.type===_.OPERATOR?this.start_of_object_property()||this.allow_wrap_or_preserved_newline(e):this.print_newline()),this.print_token(e)},w.prototype.handle_equals=function(e){this.start_of_statement(e)||this.handle_whitespace_and_comments(e),this._flags.declaration_statement&&(this._flags.declaration_assignment=!0),this._output.space_before_token=!0,this.print_token(e),this._output.space_before_token=!0},w.prototype.handle_comma=function(e){this.handle_whitespace_and_comments(e,!0),this.print_token(e),this._output.space_before_token=!0,this._flags.declaration_statement?(y(this._flags.parent.mode)&&(this._flags.declaration_assignment=!1),this._flags.declaration_assignment?(this._flags.declaration_assignment=!1,this.print_newline(!1,!0)):this._options.comma_first&&this.allow_wrap_or_preserved_newline(e)):this._flags.mode===m.ObjectLiteral||this._flags.mode===m.Statement&&this._flags.parent.mode===m.ObjectLiteral?(this._flags.mode===m.Statement&&this.restore_mode(),this._flags.inline_frame||this.print_newline()):this._options.comma_first&&this.allow_wrap_or_preserved_newline(e)},w.prototype.handle_operator=function(e){var t="*"===e.text&&(h(this._flags.last_token,["function","yield"])||u(this._flags.last_token.type,[_.START_BLOCK,_.COMMA,_.END_BLOCK,_.SEMICOLON])),n=u(e.text,["-","+"])&&(u(this._flags.last_token.type,[_.START_BLOCK,_.START_EXPR,_.EQUALS,_.OPERATOR])||u(this._flags.last_token.text,l)||","===this._flags.last_token.text);if(this.start_of_statement(e));else{var i=!t;this.handle_whitespace_and_comments(e,i)}if(h(this._flags.last_token,f))return this._output.space_before_token=!0,void this.print_token(e);if("*"!==e.text||this._flags.last_token.type!==_.DOT)if("::"!==e.text){if(this._flags.last_token.type===_.OPERATOR&&u(this._options.operator_position,g)&&this.allow_wrap_or_preserved_newline(e),":"===e.text&&this._flags.in_case)return this._flags.case_body=!0,this.indent(),this.print_token(e),this.print_newline(),void(this._flags.in_case=!1);var s=!0,r=!0,a=!1;if(":"===e.text?0===this._flags.ternary_depth?s=!1:(this._flags.ternary_depth-=1,a=!0):"?"===e.text&&(this._flags.ternary_depth+=1),!n&&!t&&this._options.preserve_newlines&&u(e.text,c)){var o=":"===e.text,p=o&&a,d=o&&!a;switch(this._options.operator_position){case b.before_newline:return this._output.space_before_token=!d,this.print_token(e),o&&!p||this.allow_wrap_or_preserved_newline(e),void(this._output.space_before_token=!0);case b.after_newline:return this._output.space_before_token=!0,!o||p?this._tokens.peek().newlines?this.print_newline(!1,!0):this.allow_wrap_or_preserved_newline(e):this._output.space_before_token=!1,this.print_token(e),void(this._output.space_before_token=!0);case b.preserve_newline:return d||this.allow_wrap_or_preserved_newline(e),s=!(this._output.just_added_newline()||d),this._output.space_before_token=s,this.print_token(e),void(this._output.space_before_token=!0)}}if(t){this.allow_wrap_or_preserved_newline(e),s=!1;var v=this._tokens.peek();r=v&&u(v.type,[_.WORD,_.RESERVED])}else"..."===e.text?(this.allow_wrap_or_preserved_newline(e),s=this._flags.last_token.type===_.START_BLOCK,r=!1):(u(e.text,["--","++","!","~"])||n)&&(this._flags.last_token.type!==_.COMMA&&this._flags.last_token.type!==_.START_EXPR||this.allow_wrap_or_preserved_newline(e),s=!1,r=!1,!e.newlines||"--"!==e.text&&"++"!==e.text||this.print_newline(!1,!0),";"===this._flags.last_token.text&&y(this._flags.mode)&&(s=!0),this._flags.last_token.type===_.RESERVED?s=!0:this._flags.last_token.type===_.END_EXPR?s=!("]"===this._flags.last_token.text&&("--"===e.text||"++"===e.text)):this._flags.last_token.type===_.OPERATOR&&(s=u(e.text,["--","-","++","+"])&&u(this._flags.last_token.text,["--","-","++","+"]),u(e.text,["+","-"])&&u(this._flags.last_token.text,["--","++"])&&(r=!0)),(this._flags.mode!==m.BlockStatement||this._flags.inline_frame)&&this._flags.mode!==m.Statement||"{"!==this._flags.last_token.text&&";"!==this._flags.last_token.text||this.print_newline());this._output.space_before_token=this._output.space_before_token||s,this.print_token(e),this._output.space_before_token=r}else this.print_token(e);else this.print_token(e)},w.prototype.handle_block_comment=function(e,t){if(this._output.raw)return this._output.add_raw_token(e),void(e.directives&&"end"===e.directives.preserve&&(this._output.raw=this._options.test_output_raw));if(e.directives)return this.print_newline(!1,t),this.print_token(e),"start"===e.directives.preserve&&(this._output.raw=!0),void this.print_newline(!1,!0);if(!r.newline.test(e.text)&&!e.newlines)return this._output.space_before_token=!0,this.print_token(e),void(this._output.space_before_token=!0);var n,i=function(e){for(var t=[],n=(e=e.replace(r.allLineBreaks,"\n")).indexOf("\n");-1!==n;)t.push(e.substring(0,n)),n=(e=e.substring(n+1)).indexOf("\n");return e.length&&t.push(e),t}(e.text),s=!1,a=!1,o=e.whitespace_before,l=o.length;if(this.print_newline(!1,t),this.print_token(e,i[0]),i.length>1){for(s=function(e,t){for(var n=0;n<e.length;n++)if(e[n].trim().charAt(0)!==t)return!1;return!0}(i=i.slice(1),"*"),a=function(e,t){for(var n,i=0,s=e.length;i<s;i++)if((n=e[i])&&0!==n.indexOf(t))return!1;return!0}(i,o),s&&(this._flags.alignment=1),n=0;n<i.length;n++)this.print_newline(!1,!0),s?this.print_token(e,i[n].replace(/^\s+/g,"")):a&&i[n]?this.print_token(e,i[n].substring(l)):this._output.add_token(i[n]);this._flags.alignment=0}this.print_newline(!1,t)},w.prototype.handle_comment=function(e,t){e.newlines?this.print_newline(!1,t):this._output.trim(!0),this._output.space_before_token=!0,this.print_token(e),this.print_newline(!1,t)},w.prototype.handle_dot=function(e){this.start_of_statement(e)||this.handle_whitespace_and_comments(e,!0),h(this._flags.last_token,f)?this._output.space_before_token=!1:this.allow_wrap_or_preserved_newline(e,")"===this._flags.last_token.text&&this._options.break_chained_methods),this._options.unindent_chained_methods&&this._output.just_added_newline()&&this.deindent(),this.print_token(e)},w.prototype.handle_unknown=function(e,t){this.print_token(e),"\n"===e.text[e.text.length-1]&&this.print_newline(!1,t)},w.prototype.handle_eof=function(e){for(;this._flags.mode===m.Statement;)this.restore_mode();this.handle_whitespace_and_comments(e)},e.exports.Beautifier=w},function(e,t,n){"use strict";function i(e){this.__parent=e,this.__character_count=0,this.__indent_count=-1,this.__alignment_count=0,this.__items=[]}function s(e,t){this.__cache=[""],this.__indent_size=e.indent_size,this.__indent_string=e.indent_char,e.indent_with_tabs||(this.__indent_string=new Array(e.indent_size+1).join(e.indent_char)),t=t||"",e.indent_level>0&&(t=new Array(e.indent_level+1).join(this.__indent_string)),this.__base_string=t,this.__base_string_length=t.length}function r(e,t){this.__indent_cache=new s(e,t),this.raw=!1,this._end_with_newline=e.end_with_newline,this.indent_size=e.indent_size,this.__lines=[],this.previous_line=null,this.current_line=null,this.space_before_token=!1,this.__add_outputline()}i.prototype.item=function(e){return e<0?this.__items[this.__items.length+e]:this.__items[e]},i.prototype.has_match=function(e){for(var t=this.__items.length-1;t>=0;t--)if(this.__items[t].match(e))return!0;return!1},i.prototype.set_indent=function(e,t){this.__indent_count=e||0,this.__alignment_count=t||0,this.__character_count=this.__parent.get_indent_size(this.__indent_count,this.__alignment_count)},i.prototype.get_character_count=function(){return this.__character_count},i.prototype.is_empty=function(){return 0===this.__items.length},i.prototype.last=function(){return this.is_empty()?null:this.__items[this.__items.length-1]},i.prototype.push=function(e){this.__items.push(e),this.__character_count+=e.length},i.prototype.push_raw=function(e){this.push(e);var t=e.lastIndexOf("\n");-1!==t&&(this.__character_count=e.length-t)},i.prototype.pop=function(){var e=null;return this.is_empty()||(e=this.__items.pop(),this.__character_count-=e.length),e},i.prototype.remove_indent=function(){this.__indent_count>0&&(this.__indent_count-=1,this.__character_count-=this.__parent.indent_size)},i.prototype.trim=function(){for(;" "===this.last();)this.__items.pop(),this.__character_count-=1},i.prototype.toString=function(){var e="";return this.is_empty()||(e=this.__parent.get_indent_string(this.__indent_count,this.__alignment_count),e+=this.__items.join("")),e},s.prototype.get_indent_size=function(e,t){var n=this.__base_string_length;return t=t||0,e<0&&(n=0),n+=e*this.__indent_size,n+=t},s.prototype.get_indent_string=function(e,t){var n=this.__base_string;return t=t||0,e<0&&(e=0,n=""),t+=e*this.__indent_size,this.__ensure_cache(t),n+=this.__cache[t]},s.prototype.__ensure_cache=function(e){for(;e>=this.__cache.length;)this.__add_column()},s.prototype.__add_column=function(){var e=this.__cache.length,t=0,n="";this.__indent_size&&e>=this.__indent_size&&(e-=(t=Math.floor(e/this.__indent_size))*this.__indent_size,n=new Array(t+1).join(this.__indent_string)),e&&(n+=new Array(e+1).join(" ")),this.__cache.push(n)},r.prototype.__add_outputline=function(){this.previous_line=this.current_line,this.current_line=new i(this),this.__lines.push(this.current_line)},r.prototype.get_line_number=function(){return this.__lines.length},r.prototype.get_indent_string=function(e,t){return this.__indent_cache.get_indent_string(e,t)},r.prototype.get_indent_size=function(e,t){return this.__indent_cache.get_indent_size(e,t)},r.prototype.is_empty=function(){return!this.previous_line&&this.current_line.is_empty()},r.prototype.add_new_line=function(e){return!(this.is_empty()||!e&&this.just_added_newline())&&(this.raw||this.__add_outputline(),!0)},r.prototype.get_code=function(e){var t=this.__lines.join("\n").replace(/[\r\n\t ]+$/,"");return this._end_with_newline&&(t+="\n"),"\n"!==e&&(t=t.replace(/[\n]/g,e)),t},r.prototype.set_indent=function(e,t){return e=e||0,t=t||0,this.__lines.length>1?(this.current_line.set_indent(e,t),!0):(this.current_line.set_indent(),!1)},r.prototype.add_raw_token=function(e){for(var t=0;t<e.newlines;t++)this.__add_outputline();this.current_line.push(e.whitespace_before),this.current_line.push_raw(e.text),this.space_before_token=!1},r.prototype.add_token=function(e){this.add_space_before_token(),this.current_line.push(e)},r.prototype.add_space_before_token=function(){this.space_before_token&&!this.just_added_newline()&&this.current_line.push(" "),this.space_before_token=!1},r.prototype.remove_indent=function(e){for(var t=this.__lines.length;e<t;)this.__lines[e].remove_indent(),e++},r.prototype.trim=function(e){for(e=void 0!==e&&e,this.current_line.trim(this.indent_string,this.baseIndentString);e&&this.__lines.length>1&&this.current_line.is_empty();)this.__lines.pop(),this.current_line=this.__lines[this.__lines.length-1],this.current_line.trim();this.previous_line=this.__lines.length>1?this.__lines[this.__lines.length-2]:null},r.prototype.just_added_newline=function(){return this.current_line.is_empty()},r.prototype.just_added_blankline=function(){return this.is_empty()||this.current_line.is_empty()&&this.previous_line.is_empty()},r.prototype.ensure_empty_line_above=function(e,t){for(var n=this.__lines.length-2;n>=0;){var s=this.__lines[n];if(s.is_empty())break;if(0!==s.item(0).indexOf(e)&&s.item(-1)!==t){this.__lines.splice(n+1,0,new i(this)),this.previous_line=this.__lines[this.__lines.length-2];break}n--}},e.exports.Output=r},function(e,t,n){"use strict";e.exports.Token=function(e,t,n,i){this.type=e,this.text=t,this.comments_before=null,this.newlines=n||0,this.whitespace_before=i||"",this.parent=null,this.next=null,this.previous=null,this.opened=null,this.closed=null,this.directives=null}},function(e,t,n){"use strict";t.identifier=new RegExp("[$@A-Z_a-zªµºÀ-ÖØ-öø-ˁˆ-ˑˠ-ˤˬˮͰ-ʹͶͷͺ-ͽΆΈ-ΊΌΎ-ΡΣ-ϵϷ-ҁҊ-ԧԱ-Ֆՙա-ևא-תװ-ײؠ-يٮٯٱ-ۓەۥۦۮۯۺ-ۼۿܐܒ-ܯݍ-ޥޱߊ-ߪߴߵߺࠀ-ࠕࠚࠤࠨࡀ-ࡘࢠࢢ-ࢬऄ-हऽॐक़-ॡॱ-ॷॹ-ॿঅ-ঌএঐও-নপ-রলশ-হঽৎড়ঢ়য়-ৡৰৱਅ-ਊਏਐਓ-ਨਪ-ਰਲਲ਼ਵਸ਼ਸਹਖ਼-ੜਫ਼ੲ-ੴઅ-ઍએ-ઑઓ-નપ-રલળવ-હઽૐૠૡଅ-ଌଏଐଓ-ନପ-ରଲଳଵ-ହଽଡ଼ଢ଼ୟ-ୡୱஃஅ-ஊஎ-ஐஒ-கஙசஜஞடணதந-பம-ஹௐఅ-ఌఎ-ఐఒ-నప-ళవ-హఽౘౙౠౡಅ-ಌಎ-ಐಒ-ನಪ-ಳವ-ಹಽೞೠೡೱೲഅ-ഌഎ-ഐഒ-ഺഽൎൠൡൺ-ൿඅ-ඖක-නඳ-රලව-ෆก-ะาำเ-ๆກຂຄງຈຊຍດ-ທນ-ຟມ-ຣລວສຫອ-ະາຳຽເ-ໄໆໜ-ໟༀཀ-ཇཉ-ཬྈ-ྌက-ဪဿၐ-ၕၚ-ၝၡၥၦၮ-ၰၵ-ႁႎႠ-ჅჇჍა-ჺჼ-ቈቊ-ቍቐ-ቖቘቚ-ቝበ-ኈኊ-ኍነ-ኰኲ-ኵኸ-ኾዀዂ-ዅወ-ዖዘ-ጐጒ-ጕጘ-ፚᎀ-ᎏᎠ-Ᏼᐁ-ᙬᙯ-ᙿᚁ-ᚚᚠ-ᛪᛮ-ᛰᜀ-ᜌᜎ-ᜑᜠ-ᜱᝀ-ᝑᝠ-ᝬᝮ-ᝰក-ឳៗៜᠠ-ᡷᢀ-ᢨᢪᢰ-ᣵᤀ-ᤜᥐ-ᥭᥰ-ᥴᦀ-ᦫᧁ-ᧇᨀ-ᨖᨠ-ᩔᪧᬅ-ᬳᭅ-ᭋᮃ-ᮠᮮᮯᮺ-ᯥᰀ-ᰣᱍ-ᱏᱚ-ᱽᳩ-ᳬᳮ-ᳱᳵᳶᴀ-ᶿḀ-ἕἘ-Ἕἠ-ὅὈ-Ὅὐ-ὗὙὛὝὟ-ώᾀ-ᾴᾶ-ᾼιῂ-ῄῆ-ῌῐ-ΐῖ-Ίῠ-Ῥῲ-ῴῶ-ῼⁱⁿₐ-ₜℂℇℊ-ℓℕℙ-ℝℤΩℨK-ℭℯ-ℹℼ-ℿⅅ-ⅉⅎⅠ-ↈⰀ-Ⱞⰰ-ⱞⱠ-ⳤⳫ-ⳮⳲⳳⴀ-ⴥⴧⴭⴰ-ⵧⵯⶀ-ⶖⶠ-ⶦⶨ-ⶮⶰ-ⶶⶸ-ⶾⷀ-ⷆⷈ-ⷎⷐ-ⷖⷘ-ⷞⸯ々-〇〡-〩〱-〵〸-〼ぁ-ゖゝ-ゟァ-ヺー-ヿㄅ-ㄭㄱ-ㆎㆠ-ㆺㇰ-ㇿ㐀-䶵一-鿌ꀀ-ꒌꓐ-ꓽꔀ-ꘌꘐ-ꘟꘪꘫꙀ-ꙮꙿ-ꚗꚠ-ꛯꜗ-ꜟꜢ-ꞈꞋ-ꞎꞐ-ꞓꞠ-Ɦꟸ-ꠁꠃ-ꠅꠇ-ꠊꠌ-ꠢꡀ-ꡳꢂ-ꢳꣲ-ꣷꣻꤊ-ꤥꤰ-ꥆꥠ-ꥼꦄ-ꦲꧏꨀ-ꨨꩀ-ꩂꩄ-ꩋꩠ-ꩶꩺꪀ-ꪯꪱꪵꪶꪹ-ꪽꫀꫂꫛ-ꫝꫠ-ꫪꫲ-ꫴꬁ-ꬆꬉ-ꬎꬑ-ꬖꬠ-ꬦꬨ-ꬮꯀ-ꯢ가-힣ힰ-ퟆퟋ-ퟻ豈-舘並-龎ﬀ-ﬆﬓ-ﬗיִײַ-ﬨשׁ-זּטּ-לּמּנּסּףּפּצּ-ﮱﯓ-ﴽﵐ-ﶏﶒ-ﷇﷰ-ﷻﹰ-ﹴﹶ-ﻼＡ-Ｚａ-ｚｦ-ﾾￂ-ￇￊ-ￏￒ-ￗￚ-ￜ][$0-9A-Z_a-zªµºÀ-ÖØ-öø-ˁˆ-ˑˠ-ˤˬˮͰ-ʹͶͷͺ-ͽΆΈ-ΊΌΎ-ΡΣ-ϵϷ-ҁҊ-ԧԱ-Ֆՙա-ևא-תװ-ײؠ-يٮٯٱ-ۓەۥۦۮۯۺ-ۼۿܐܒ-ܯݍ-ޥޱߊ-ߪߴߵߺࠀ-ࠕࠚࠤࠨࡀ-ࡘࢠࢢ-ࢬऄ-हऽॐक़-ॡॱ-ॷॹ-ॿঅ-ঌএঐও-নপ-রলশ-হঽৎড়ঢ়য়-ৡৰৱਅ-ਊਏਐਓ-ਨਪ-ਰਲਲ਼ਵਸ਼ਸਹਖ਼-ੜਫ਼ੲ-ੴઅ-ઍએ-ઑઓ-નપ-રલળવ-હઽૐૠૡଅ-ଌଏଐଓ-ନପ-ରଲଳଵ-ହଽଡ଼ଢ଼ୟ-ୡୱஃஅ-ஊஎ-ஐஒ-கஙசஜஞடணதந-பம-ஹௐఅ-ఌఎ-ఐఒ-నప-ళవ-హఽౘౙౠౡಅ-ಌಎ-ಐಒ-ನಪ-ಳವ-ಹಽೞೠೡೱೲഅ-ഌഎ-ഐഒ-ഺഽൎൠൡൺ-ൿඅ-ඖක-නඳ-රලව-ෆก-ะาำเ-ๆກຂຄງຈຊຍດ-ທນ-ຟມ-ຣລວສຫອ-ະາຳຽເ-ໄໆໜ-ໟༀཀ-ཇཉ-ཬྈ-ྌက-ဪဿၐ-ၕၚ-ၝၡၥၦၮ-ၰၵ-ႁႎႠ-ჅჇჍა-ჺჼ-ቈቊ-ቍቐ-ቖቘቚ-ቝበ-ኈኊ-ኍነ-ኰኲ-ኵኸ-ኾዀዂ-ዅወ-ዖዘ-ጐጒ-ጕጘ-ፚᎀ-ᎏᎠ-Ᏼᐁ-ᙬᙯ-ᙿᚁ-ᚚᚠ-ᛪᛮ-ᛰᜀ-ᜌᜎ-ᜑᜠ-ᜱᝀ-ᝑᝠ-ᝬᝮ-ᝰក-ឳៗៜᠠ-ᡷᢀ-ᢨᢪᢰ-ᣵᤀ-ᤜᥐ-ᥭᥰ-ᥴᦀ-ᦫᧁ-ᧇᨀ-ᨖᨠ-ᩔᪧᬅ-ᬳᭅ-ᭋᮃ-ᮠᮮᮯᮺ-ᯥᰀ-ᰣᱍ-ᱏᱚ-ᱽᳩ-ᳬᳮ-ᳱᳵᳶᴀ-ᶿḀ-ἕἘ-Ἕἠ-ὅὈ-Ὅὐ-ὗὙὛὝὟ-ώᾀ-ᾴᾶ-ᾼιῂ-ῄῆ-ῌῐ-ΐῖ-Ίῠ-Ῥῲ-ῴῶ-ῼⁱⁿₐ-ₜℂℇℊ-ℓℕℙ-ℝℤΩℨK-ℭℯ-ℹℼ-ℿⅅ-ⅉⅎⅠ-ↈⰀ-Ⱞⰰ-ⱞⱠ-ⳤⳫ-ⳮⳲⳳⴀ-ⴥⴧⴭⴰ-ⵧⵯⶀ-ⶖⶠ-ⶦⶨ-ⶮⶰ-ⶶⶸ-ⶾⷀ-ⷆⷈ-ⷎⷐ-ⷖⷘ-ⷞⸯ々-〇〡-〩〱-〵〸-〼ぁ-ゖゝ-ゟァ-ヺー-ヿㄅ-ㄭㄱ-ㆎㆠ-ㆺㇰ-ㇿ㐀-䶵一-鿌ꀀ-ꒌꓐ-ꓽꔀ-ꘌꘐ-ꘟꘪꘫꙀ-ꙮꙿ-ꚗꚠ-ꛯꜗ-ꜟꜢ-ꞈꞋ-ꞎꞐ-ꞓꞠ-Ɦꟸ-ꠁꠃ-ꠅꠇ-ꠊꠌ-ꠢꡀ-ꡳꢂ-ꢳꣲ-ꣷꣻꤊ-ꤥꤰ-ꥆꥠ-ꥼꦄ-ꦲꧏꨀ-ꨨꩀ-ꩂꩄ-ꩋꩠ-ꩶꩺꪀ-ꪯꪱꪵꪶꪹ-ꪽꫀꫂꫛ-ꫝꫠ-ꫪꫲ-ꫴꬁ-ꬆꬉ-ꬎꬑ-ꬖꬠ-ꬦꬨ-ꬮꯀ-ꯢ가-힣ힰ-ퟆퟋ-ퟻ豈-舘並-龎ﬀ-ﬆﬓ-ﬗיִײַ-ﬨשׁ-זּטּ-לּמּנּסּףּפּצּ-ﮱﯓ-ﴽﵐ-ﶏﶒ-ﷇﷰ-ﷻﹰ-ﹴﹶ-ﻼＡ-Ｚａ-ｚｦ-ﾾￂ-ￇￊ-ￏￒ-ￗￚ-ￜ̀-ͯ҃-֑҇-ׇֽֿׁׂׅׄؐ-ؚؠ-ىٲ-ۓۧ-ۨۻ-ۼܰ-݊ࠀ-ࠔࠛ-ࠣࠥ-ࠧࠩ-࠭ࡀ-ࡗࣤ-ࣾऀ-ःऺ-़ा-ॏ॑-ॗॢ-ॣ०-९ঁ-ঃ়া-ৄেৈৗয়-ৠਁ-ਃ਼ਾ-ੂੇੈੋ-੍ੑ੦-ੱੵઁ-ઃ઼ા-ૅે-ૉો-્ૢ-ૣ૦-૯ଁ-ଃ଼ା-ୄେୈୋ-୍ୖୗୟ-ୠ୦-୯ஂா-ூெ-ைொ-்ௗ௦-௯ఁ-ఃె-ైొ-్ౕౖౢ-ౣ౦-౯ಂಃ಼ಾ-ೄೆ-ೈೊ-್ೕೖೢ-ೣ೦-೯ംഃെ-ൈൗൢ-ൣ൦-൯ංඃ්ා-ුූෘ-ෟෲෳิ-ฺเ-ๅ๐-๙ິ-ູ່-ໍ໐-໙༘༙༠-༩༹༵༷ཁ-ཇཱ-྄྆-྇ྍ-ྗྙ-ྼ࿆က-ဩ၀-၉ၧ-ၭၱ-ၴႂ-ႍႏ-ႝ፝-፟ᜎ-ᜐᜠ-ᜰᝀ-ᝐᝲᝳក-ឲ៝០-៩᠋-᠍᠐-᠙ᤠ-ᤫᤰ-᤻ᥑ-ᥭᦰ-ᧀᧈ-ᧉ᧐-᧙ᨀ-ᨕᨠ-ᩓ᩠-᩿᩼-᪉᪐-᪙ᭆ-ᭋ᭐-᭙᭫-᭳᮰-᮹᯦-᯳ᰀ-ᰢ᱀-᱉ᱛ-ᱽ᳐-᳒ᴀ-ᶾḁ-ἕ‌‍‿⁀⁔⃐-⃥⃜⃡-⃰ⶁ-ⶖⷠ-ⷿ〡-〨゙゚Ꙁ-ꙭꙴ-꙽ꚟ꛰-꛱ꟸ-ꠀ꠆ꠋꠣ-ꠧꢀ-ꢁꢴ-꣄꣐-꣙ꣳ-ꣷ꤀-꤉ꤦ-꤭ꤰ-ꥅꦀ-ꦃ꦳-꧀ꨀ-ꨧꩀ-ꩁꩌ-ꩍ꩐-꩙ꩻꫠ-ꫩꫲ-ꫳꯀ-ꯡ꯬꯭꯰-꯹ﬠ-ﬨ︀-️︠-︦︳︴﹍-﹏０-９＿]*","g");t.newline=/[\n\r\u2028\u2029]/,t.lineBreak=new RegExp("\r\n|"+t.newline.source),t.allLineBreaks=new RegExp(t.lineBreak.source,"g")},function(e,t,n){"use strict";var i=n(6).Options,s=["before-newline","after-newline","preserve-newline"];function r(e){i.call(this,e,"js");var t=this.raw_options.brace_style||null;"expand-strict"===t?this.raw_options.brace_style="expand":"collapse-preserve-inline"===t?this.raw_options.brace_style="collapse,preserve-inline":void 0!==this.raw_options.braces_on_own_line&&(this.raw_options.brace_style=this.raw_options.braces_on_own_line?"expand":"collapse");var n=this._get_selection_list("brace_style",["collapse","expand","end-expand","none","preserve-inline"]);this.brace_preserve_inline=!1,this.brace_style="collapse";for(var r=0;r<n.length;r++)"preserve-inline"===n[r]?this.brace_preserve_inline=!0:this.brace_style=n[r];this.unindent_chained_methods=this._get_boolean("unindent_chained_methods"),this.break_chained_methods=this._get_boolean("break_chained_methods"),this.space_in_paren=this._get_boolean("space_in_paren"),this.space_in_empty_paren=this._get_boolean("space_in_empty_paren"),this.jslint_happy=this._get_boolean("jslint_happy"),this.space_after_anon_function=this._get_boolean("space_after_anon_function"),this.space_after_named_function=this._get_boolean("space_after_named_function"),this.keep_array_indentation=this._get_boolean("keep_array_indentation"),this.space_before_conditional=this._get_boolean("space_before_conditional",!0),this.unescape_strings=this._get_boolean("unescape_strings"),this.e4x=this._get_boolean("e4x"),this.comma_first=this._get_boolean("comma_first"),this.operator_position=this._get_selection("operator_position",s),this.test_output_raw=this._get_boolean("test_output_raw"),this.jslint_happy&&(this.space_after_anon_function=!0)}r.prototype=new i,e.exports.Options=r},function(e,t,n){"use strict";function i(e,t){this.raw_options=s(e,t),this.disabled=this._get_boolean("disabled"),this.eol=this._get_characters("eol","auto"),this.end_with_newline=this._get_boolean("end_with_newline"),this.indent_size=this._get_number("indent_size",4),this.indent_char=this._get_characters("indent_char"," "),this.indent_level=this._get_number("indent_level"),this.preserve_newlines=this._get_boolean("preserve_newlines",!0),this.max_preserve_newlines=this._get_number("max_preserve_newlines",32786),this.preserve_newlines||(this.max_preserve_newlines=0),this.indent_with_tabs=this._get_boolean("indent_with_tabs","\t"===this.indent_char),this.indent_with_tabs&&(this.indent_char="\t",1===this.indent_size&&(this.indent_size=4)),this.wrap_line_length=this._get_number("wrap_line_length",this._get_number("max_char"))}function s(e,t){var n,i={};for(n in e=r(e))n!==t&&(i[n]=e[n]);if(t&&e[t])for(n in e[t])i[n]=e[t][n];return i}function r(e){var t,n={};for(t in e){n[t.replace(/-/g,"_")]=e[t]}return n}i.prototype._get_array=function(e,t){var n=this.raw_options[e],i=t||[];return"object"==typeof n?null!==n&&"function"==typeof n.concat&&(i=n.concat()):"string"==typeof n&&(i=n.split(/[^a-zA-Z0-9_\/\-]+/)),i},i.prototype._get_boolean=function(e,t){var n=this.raw_options[e];return void 0===n?!!t:!!n},i.prototype._get_characters=function(e,t){var n=this.raw_options[e],i=t||"";return"string"==typeof n&&(i=n.replace(/\\r/,"\r").replace(/\\n/,"\n").replace(/\\t/,"\t")),i},i.prototype._get_number=function(e,t){var n=this.raw_options[e];t=parseInt(t,10),isNaN(t)&&(t=0);var i=parseInt(n,10);return isNaN(i)&&(i=t),i},i.prototype._get_selection=function(e,t,n){var i=this._get_selection_list(e,t,n);if(1!==i.length)throw new Error("Invalid Option Value: The option '"+e+"' can only be one of the following values:\n"+t+"\nYou passed in: '"+this.raw_options[e]+"'");return i[0]},i.prototype._get_selection_list=function(e,t,n){if(!t||0===t.length)throw new Error("Selection list cannot be empty.");if(n=n||[t[0]],!this._is_valid_selection(n,t))throw new Error("Invalid Default Value!");var i=this._get_array(e,n);if(!this._is_valid_selection(i,t))throw new Error("Invalid Option Value: The option '"+e+"' can contain only the following values:\n"+t+"\nYou passed in: '"+this.raw_options[e]+"'");return i},i.prototype._is_valid_selection=function(e,t){return e.length&&t.length&&!e.some(function(e){return-1===t.indexOf(e)})},e.exports.Options=i,e.exports.normalizeOpts=r,e.exports.mergeOpts=s},function(e,t,n){"use strict";var i=n(8).InputScanner,s=n(9).Tokenizer,r=n(9).TOKEN,a=n(11).Directives,o=n(4);function l(e,t){return-1!==t.indexOf(e)}var c={START_EXPR:"TK_START_EXPR",END_EXPR:"TK_END_EXPR",START_BLOCK:"TK_START_BLOCK",END_BLOCK:"TK_END_BLOCK",WORD:"TK_WORD",RESERVED:"TK_RESERVED",SEMICOLON:"TK_SEMICOLON",STRING:"TK_STRING",EQUALS:"TK_EQUALS",OPERATOR:"TK_OPERATOR",COMMA:"TK_COMMA",BLOCK_COMMENT:"TK_BLOCK_COMMENT",COMMENT:"TK_COMMENT",DOT:"TK_DOT",UNKNOWN:"TK_UNKNOWN",START:r.START,RAW:r.RAW,EOF:r.EOF},_=new a(/\/\*/,/\*\//),p=/0[xX][0123456789abcdefABCDEF]*|0[oO][01234567]*|0[bB][01]*|\d+n|(?:\.\d+|\d+\.?\d*)(?:[eE][+-]?\d+)?/g,u=/[0-9]/,d=/[^\d\.]/,h=">>> === !== << && >= ** != == <= >> || < / - + > : & % ? ^ | *".split(" "),f=">>>= ... >>= <<= === >>> !== **= => ^= :: /= << <= == && -= >= >> != -- += ** || ++ %= &= *= |= = ! ? > < : / ^ - + * & % ~ |";f=(f=f.replace(/[-[\]{}()*+?.,\\^$|#]/g,"\\$&")).replace(/ /g,"|");var b,g=new RegExp(f,"g"),m=/#![^\n\r\u2028\u2029]*(?:\r\n|[\n\r\u2028\u2029])?/g,v=/#include[^\n\r\u2028\u2029]*(?:\r\n|[\n\r\u2028\u2029])?/g,y="continue,try,throw,return,var,let,const,if,switch,case,default,for,while,break,function,import,export".split(","),w=y.concat(["do","in","of","else","get","set","new","catch","finally","typeof","yield","async","await","from","as"]),k=new RegExp("^(?:"+w.join("|")+")$"),x=/\/\*(?:[\s\S]*?)((?:\*\/)|$)/g,N=/\/\/(?:[^\n\r\u2028\u2029]*)/g,E=/(?:(?:<\?php|<\?=)[\s\S]*?\?>)|(?:<%[\s\S]*?%>)/g,C=function(e,t){s.call(this,e,t),this._whitespace_pattern=/[\n\r\u2028\u2029\t\u000B\u00A0\u1680\u180e\u2000-\u200a\u202f\u205f\u3000\ufeff ]+/g,this._newline_pattern=/([^\n\r\u2028\u2029]*)(\r\n|[\n\r\u2028\u2029])?/g};(C.prototype=new s)._is_comment=function(e){return e.type===c.COMMENT||e.type===c.BLOCK_COMMENT||e.type===c.UNKNOWN},C.prototype._is_opening=function(e){return e.type===c.START_BLOCK||e.type===c.START_EXPR},C.prototype._is_closing=function(e,t){return(e.type===c.END_BLOCK||e.type===c.END_EXPR)&&t&&("]"===e.text&&"["===t.text||")"===e.text&&"("===t.text||"}"===e.text&&"{"===t.text)},C.prototype._reset=function(){b=!1},C.prototype._get_next_token=function(e,t){this._readWhitespace();var n=null,i=this._input.peek();return n=(n=(n=(n=(n=(n=(n=(n=(n=n||this._read_singles(i))||this._read_word(e))||this._read_comment(i))||this._read_string(i))||this._read_regexp(i,e))||this._read_xml(i,e))||this._read_non_javascript(i))||this._read_punctuation())||this._create_token(c.UNKNOWN,this._input.next())},C.prototype._read_word=function(e){var t;return""!==(t=this._input.read(o.identifier))?e.type!==c.DOT&&(e.type!==c.RESERVED||"set"!==e.text&&"get"!==e.text)&&k.test(t)?"in"===t||"of"===t?this._create_token(c.OPERATOR,t):this._create_token(c.RESERVED,t):this._create_token(c.WORD,t):""!==(t=this._input.read(p))?this._create_token(c.WORD,t):void 0},C.prototype._read_singles=function(e){var t=null;return null===e?t=this._create_token(c.EOF,""):"("===e||"["===e?t=this._create_token(c.START_EXPR,e):")"===e||"]"===e?t=this._create_token(c.END_EXPR,e):"{"===e?t=this._create_token(c.START_BLOCK,e):"}"===e?t=this._create_token(c.END_BLOCK,e):";"===e?t=this._create_token(c.SEMICOLON,e):"."===e&&d.test(this._input.peek(1))?t=this._create_token(c.DOT,e):","===e&&(t=this._create_token(c.COMMA,e)),t&&this._input.next(),t},C.prototype._read_punctuation=function(){var e=this._input.read(g);if(""!==e)return"="===e?this._create_token(c.EQUALS,e):this._create_token(c.OPERATOR,e)},C.prototype._read_non_javascript=function(e){var t="";if("#"===e){if(this._is_first_token()&&(t=this._input.read(m)))return this._create_token(c.UNKNOWN,t.trim()+"\n");if(t=this._input.read(v))return this._create_token(c.UNKNOWN,t.trim()+"\n");e=this._input.next();var n="#";if(this._input.hasNext()&&this._input.testChar(u)){do{n+=e=this._input.next()}while(this._input.hasNext()&&"#"!==e&&"="!==e);return"#"===e||("["===this._input.peek()&&"]"===this._input.peek(1)?(n+="[]",this._input.next(),this._input.next()):"{"===this._input.peek()&&"}"===this._input.peek(1)&&(n+="{}",this._input.next(),this._input.next())),this._create_token(c.WORD,n)}this._input.back()}else if("<"===e){if("?"===this._input.peek(1)||"%"===this._input.peek(1)){if(t=this._input.read(E))return t=t.replace(o.allLineBreaks,"\n"),this._create_token(c.STRING,t)}else if(this._input.match(/<\!--/g)){for(e="\x3c!--";this._input.hasNext()&&!this._input.testChar(o.newline);)e+=this._input.next();return b=!0,this._create_token(c.COMMENT,e)}}else if("-"===e&&b&&this._input.match(/-->/g))return b=!1,this._create_token(c.COMMENT,"--\x3e");return null},C.prototype._read_comment=function(e){var t=null;if("/"===e){var n="";if("*"===this._input.peek(1)){n=this._input.read(x);var i=_.get_directives(n);i&&"start"===i.ignore&&(n+=_.readIgnored(this._input)),n=n.replace(o.allLineBreaks,"\n"),(t=this._create_token(c.BLOCK_COMMENT,n)).directives=i}else"/"===this._input.peek(1)&&(n=this._input.read(N),t=this._create_token(c.COMMENT,n))}return t},C.prototype._read_string=function(e){if("`"===e||"'"===e||'"'===e){var t=this._input.next();return this.has_char_escapes=!1,t+="`"===e?this._read_string_recursive("`",!0,"${"):this._read_string_recursive(e),this.has_char_escapes&&this._options.unescape_strings&&(t=function(e){var t="",n=0,s=new i(e),r=null;for(;s.hasNext();)if((r=s.match(/([\s]|[^\\]|\\\\)+/g))&&(t+=r[0]),"\\"===s.peek()){if(s.next(),"x"===s.peek())r=s.match(/x([0-9A-Fa-f]{2})/g);else{if("u"!==s.peek()){t+="\\",s.hasNext()&&(t+=s.next());continue}r=s.match(/u([0-9A-Fa-f]{4})/g)}if(!r)return e;if((n=parseInt(r[1],16))>126&&n<=255&&0===r[0].indexOf("x"))return e;if(n>=0&&n<32){t+="\\"+r[0];continue}t+=34===n||39===n||92===n?"\\"+String.fromCharCode(n):String.fromCharCode(n)}return t}(t)),this._input.peek()===e&&(t+=this._input.next()),this._create_token(c.STRING,t)}return null},C.prototype._allow_regexp_or_xml=function(e){return e.type===c.RESERVED&&l(e.text,["return","case","throw","else","do","typeof","yield"])||e.type===c.END_EXPR&&")"===e.text&&e.opened.previous.type===c.RESERVED&&l(e.opened.previous.text,["if","while","for"])||l(e.type,[c.COMMENT,c.START_EXPR,c.START_BLOCK,c.START,c.END_BLOCK,c.OPERATOR,c.EQUALS,c.EOF,c.SEMICOLON,c.COMMA])},C.prototype._read_regexp=function(e,t){if("/"===e&&this._allow_regexp_or_xml(t)){for(var n=this._input.next(),i=!1,s=!1;this._input.hasNext()&&(i||s||this._input.peek()!==e)&&!this._input.testChar(o.newline);)n+=this._input.peek(),i?i=!1:(i="\\"===this._input.peek(),"["===this._input.peek()?s=!0:"]"===this._input.peek()&&(s=!1)),this._input.next();return this._input.peek()===e&&(n+=this._input.next(),n+=this._input.read(o.identifier)),this._create_token(c.STRING,n)}return null};var R=/<()([-a-zA-Z:0-9_.]+|{[\s\S]+?}|!\[CDATA\[[\s\S]*?\]\])(\s+{[\s\S]+?}|\s+[-a-zA-Z:0-9_.]+|\s+[-a-zA-Z:0-9_.]+\s*=\s*('[^']*'|"[^"]*"|{[\s\S]+?}))*\s*(\/?)\s*>/g,M=/[\s\S]*?<(\/?)([-a-zA-Z:0-9_.]+|{[\s\S]+?}|!\[CDATA\[[\s\S]*?\]\])(\s+{[\s\S]+?}|\s+[-a-zA-Z:0-9_.]+|\s+[-a-zA-Z:0-9_.]+\s*=\s*('[^']*'|"[^"]*"|{[\s\S]+?}))*\s*(\/?)\s*>/g;C.prototype._read_xml=function(e,t){if(this._options.e4x&&"<"===e&&this._input.test(R)&&this._allow_regexp_or_xml(t)){var n="",i=this._input.match(R);if(i){for(var s=i[2].replace(/^{\s+/,"{").replace(/\s+}$/,"}"),r=0===s.indexOf("{"),a=0;i;){var l=!!i[1],_=i[2];if(!(!!i[i.length-1]||"![CDATA["===_.slice(0,8))&&(_===s||r&&_.replace(/^{\s+/,"{").replace(/\s+}$/,"}"))&&(l?--a:++a),n+=i[0],a<=0)break;i=this._input.match(M)}return i||(n+=this._input.match(/[\s\S]*/g)[0]),n=n.replace(o.allLineBreaks,"\n"),this._create_token(c.STRING,n)}}return null},C.prototype._read_string_recursive=function(e,t,n){for(var i,s="",r=!1;this._input.hasNext()&&(i=this._input.peek(),r||i!==e&&(t||!o.newline.test(i)));)(r||t)&&o.newline.test(i)?("\r"===i&&"\n"===this._input.peek(1)&&(this._input.next(),i=this._input.peek()),s+="\n"):s+=i,r?("x"!==i&&"u"!==i||(this.has_char_escapes=!0),r=!1):r="\\"===i,this._input.next(),n&&-1!==s.indexOf(n,s.length-n.length)&&(s+="`"===e?this._read_string_recursive("}",t,"`"):this._read_string_recursive("`",t,"${"),this._input.hasNext()&&(s+=this._input.next()));return s},e.exports.Tokenizer=C,e.exports.TOKEN=c,e.exports.positionable_operators=h.slice(),e.exports.line_starters=y.slice()},function(e,t,n){"use strict";function i(e){this.__input=e||"",this.__input_length=this.__input.length,this.__position=0}i.prototype.restart=function(){this.__position=0},i.prototype.back=function(){this.__position>0&&(this.__position-=1)},i.prototype.hasNext=function(){return this.__position<this.__input_length},i.prototype.next=function(){var e=null;return this.hasNext()&&(e=this.__input.charAt(this.__position),this.__position+=1),e},i.prototype.peek=function(e){var t=null;return e=e||0,(e+=this.__position)>=0&&e<this.__input_length&&(t=this.__input.charAt(e)),t},i.prototype.test=function(e,t){if(t=t||0,t+=this.__position,e.lastIndex=t,t>=0&&t<this.__input_length){var n=e.exec(this.__input);return n&&n.index===t}return!1},i.prototype.testChar=function(e,t){var n=this.peek(t);return null!==n&&e.test(n)},i.prototype.match=function(e){e.lastIndex=this.__position;var t=e.exec(this.__input);return t&&t.index===this.__position?this.__position+=t[0].length:t=null,t},i.prototype.read=function(e){var t="",n=this.match(e);return n&&(t=n[0]),t},i.prototype.readUntil=function(e,t){var n,i=this.__position;e.lastIndex=this.__position;var s=e.exec(this.__input);return i=s?t?s.index+s[0].length:s.index:this.__input_length,n=this.__input.substring(this.__position,i),this.__position=i,n},i.prototype.readUntilAfter=function(e){return this.readUntil(e,!0)},i.prototype.peekUntilAfter=function(e){var t=this.__position,n=this.readUntilAfter(e);return this.__position=t,n},i.prototype.lookBack=function(e){var t=this.__position-1;return t>=e.length&&this.__input.substring(t-e.length,t).toLowerCase()===e},e.exports.InputScanner=i},function(e,t,n){"use strict";var i=n(8).InputScanner,s=n(3).Token,r=n(10).TokenStream,a={START:"TK_START",RAW:"TK_RAW",EOF:"TK_EOF"},o=function(e,t){this._input=new i(e),this._options=t||{},this.__tokens=null,this.__newline_count=0,this.__whitespace_before_token="",this._whitespace_pattern=/[\n\r\t ]+/g,this._newline_pattern=/([^\n\r]*)(\r\n|[\n\r])?/g};o.prototype.tokenize=function(){var e;this._input.restart(),this.__tokens=new r,this._reset();for(var t=new s(a.START,""),n=null,i=[],o=new r;t.type!==a.EOF;){for(e=this._get_next_token(t,n);this._is_comment(e);)o.add(e),e=this._get_next_token(t,n);o.isEmpty()||(e.comments_before=o,o=new r),e.parent=n,this._is_opening(e)?(i.push(n),n=e):n&&this._is_closing(e,n)&&(e.opened=n,n.closed=e,n=i.pop(),e.parent=n),e.previous=t,t.next=e,this.__tokens.add(e),t=e}return this.__tokens},o.prototype._is_first_token=function(){return this.__tokens.isEmpty()},o.prototype._reset=function(){},o.prototype._get_next_token=function(e,t){this._readWhitespace();var n=this._input.read(/.+/g);return n?this._create_token(a.RAW,n):this._create_token(a.EOF,"")},o.prototype._is_comment=function(e){return!1},o.prototype._is_opening=function(e){return!1},o.prototype._is_closing=function(e,t){return!1},o.prototype._create_token=function(e,t){var n=new s(e,t,this.__newline_count,this.__whitespace_before_token);return this.__newline_count=0,this.__whitespace_before_token="",n},o.prototype._readWhitespace=function(){var e=this._input.read(this._whitespace_pattern);if(" "===e)this.__whitespace_before_token=e;else if(""!==e){this._newline_pattern.lastIndex=0;for(var t=this._newline_pattern.exec(e);t[2];)this.__newline_count+=1,t=this._newline_pattern.exec(e);this.__whitespace_before_token=t[1]}},e.exports.Tokenizer=o,e.exports.TOKEN=a},function(e,t,n){"use strict";function i(e){this.__tokens=[],this.__tokens_length=this.__tokens.length,this.__position=0,this.__parent_token=e}i.prototype.restart=function(){this.__position=0},i.prototype.isEmpty=function(){return 0===this.__tokens_length},i.prototype.hasNext=function(){return this.__position<this.__tokens_length},i.prototype.next=function(){var e=null;return this.hasNext()&&(e=this.__tokens[this.__position],this.__position+=1),e},i.prototype.peek=function(e){var t=null;return e=e||0,(e+=this.__position)>=0&&e<this.__tokens_length&&(t=this.__tokens[e]),t},i.prototype.add=function(e){this.__parent_token&&(e.parent=this.__parent_token),this.__tokens.push(e),this.__tokens_length+=1},e.exports.TokenStream=i},function(e,t,n){"use strict";function i(e,t){e="string"==typeof e?e:e.source,t="string"==typeof t?t:t.source,this.__directives_block_pattern=new RegExp(e+/ beautify( \w+[:]\w+)+ /.source+t,"g"),this.__directive_pattern=/ (\w+)[:](\w+)/g,this.__directives_end_ignore_pattern=new RegExp("(?:[\\s\\S]*?)((?:"+e+/\sbeautify\signore:end\s/.source+t+")|$)","g")}i.prototype.get_directives=function(e){if(!e.match(this.__directives_block_pattern))return null;var t={};this.__directive_pattern.lastIndex=0;for(var n=this.__directive_pattern.exec(e);n;)t[n[1]]=n[2],n=this.__directive_pattern.exec(e);return t},i.prototype.readIgnored=function(e){return e.read(this.__directives_end_ignore_pattern)},e.exports.Directives=i}]);return"function"==typeof define&&define.amd?define([],function(){return{js_beautify:e}}):"undefined"!=typeof exports?exports.js_beautify=e:"undefined"!=typeof window?window.js_beautify=e:"undefined"!=typeof global&&(global.js_beautify=e),e}();window.js_beautify=JsBeautifier,function(){function e(){self.onmessage=function(e){var t=function(e,t){var n=(t=t||{}).indent_size||4,i=t.indent_char||" ";"string"==typeof n&&(n=parseInt(n,10));var s,r=/^\s+$/,a=-1;function o(){return s=e.charAt(++a)}function l(){return e.charAt(a+1)}function c(t){for(var n=a;o();)if("\\"===s)o(),o();else{if(s===t)break;if("\n"===s)break}return e.substring(n,a+1)}function _(){for(var e=a;r.test(l());)a++;return a!==e}function p(){var e=a;do{}while(r.test(o()));return a!==e+1}function u(){var t=a;for(o();o();)if("*"===s&&"/"===l()){a++;break}return e.substring(t,a+1)}var d,h=e.match(/^[\r\n]*[\t ]*/)[0],f=Array(n+1).join(i),b={"{":function(e){b.singleSpace(),g.push(e),b.newLine()},"}":function(e){b.newLine(),g.push(e),b.newLine()},newLine:function(e){if(!e)for(;r.test(g[g.length-1]);)g.pop();g.length&&g.push("\n"),h&&g.push(h)},singleSpace:function(){g.length&&!r.test(g[g.length-1])&&g.push(" ")}},g=[];for(h&&g.push(h);;){var m=p();if(!s)break;"{"===s?(h+=f,b["{"](s)):"}"===s?(h=h.slice(0,-n),b["}"](s)):'"'===s||"'"===s?g.push(c(s)):";"===s?g.push(s,"\n",h):"/"===s&&"*"===l()?(b.newLine(),g.push(u(),"\n",h)):"("===s?(d="url",e.substring(a-d.length,a).toLowerCase()===d?(g.push(s),_(),o()&&(")"!==s&&'"'!==s&&"'"!==s?g.push(c(")")):a--)):(m&&b.singleSpace(),g.push(s),_())):")"===s?g.push(s):","===s?(_(),g.push(s),b.singleSpace()):"]"===s?g.push(s):"["===s||"="===s?(_(),g.push(s)):(m&&b.singleSpace(),g.push(s))}return g.join("").replace(/[\n ]+$/,"")}(e.data.source_text,e.data.options);self.postMessage(t)}}window.css_beautify=function(t,n,i){"use strict";let s=new Worker(URL.createObjectURL(new Blob(["("+e.toString()+")()"],{type:"text/javascript"})));s.onmessage=function(e){i&&i(e.data)},s.postMessage({source_text:(t||"").trim(),options:n})}}();let highlightWebWorker=()=>{!function(e){var t="object"==typeof window&&window||"object"==typeof self&&self;"undefined"==typeof exports||exports.nodeType?t&&(t.hljs=e({}),"function"==typeof define&&define.amd&&define([],function(){return t.hljs})):e(exports)}(function(e){var t=[],n=Object.keys,i={},s={},r=!0,a=/^(no-?highlight|plain|text)$/i,o=/\blang(?:uage)?-([\w-]+)\b/i,l=/((^(<[^>]+>|\t|)+|(?:\n)))/gm,c={case_insensitive:"cI",lexemes:"l",contains:"c",keywords:"k",subLanguage:"sL",className:"cN",begin:"b",beginKeywords:"bK",end:"e",endsWithParent:"eW",illegal:"i",excludeBegin:"eB",excludeEnd:"eE",returnBegin:"rB",returnEnd:"rE",variants:"v",IDENT_RE:"IR",UNDERSCORE_IDENT_RE:"UIR",NUMBER_RE:"NR",C_NUMBER_RE:"CNR",BINARY_NUMBER_RE:"BNR",RE_STARTERS_RE:"RSR",BACKSLASH_ESCAPE:"BE",APOS_STRING_MODE:"ASM",QUOTE_STRING_MODE:"QSM",PHRASAL_WORDS_MODE:"PWM",C_LINE_COMMENT_MODE:"CLCM",C_BLOCK_COMMENT_MODE:"CBCM",HASH_COMMENT_MODE:"HCM",NUMBER_MODE:"NM",C_NUMBER_MODE:"CNM",BINARY_NUMBER_MODE:"BNM",CSS_NUMBER_MODE:"CSSNM",REGEXP_MODE:"RM",TITLE_MODE:"TM",UNDERSCORE_TITLE_MODE:"UTM",COMMENT:"C",beginRe:"bR",endRe:"eR",illegalRe:"iR",lexemesRe:"lR",terminators:"t",terminator_end:"tE"},_="</span>",p="Could not find the language '{}', did you forget to load/include a language module?",u={classPrefix:"hljs-",tabReplace:null,useBR:!1,languages:void 0},d="of and for in not or if then".split(" ");function h(e){return e.replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;")}function f(e){return e.nodeName.toLowerCase()}function b(e){return a.test(e)}function g(e){var t,n={},i=Array.prototype.slice.call(arguments,1);for(t in e)n[t]=e[t];return i.forEach(function(e){for(t in e)n[t]=e[t]}),n}function m(e){var t=[];return function e(n,i){for(var s=n.firstChild;s;s=s.nextSibling)3===s.nodeType?i+=s.nodeValue.length:1===s.nodeType&&(t.push({event:"start",offset:i,node:s}),i=e(s,i),f(s).match(/br|hr|img|input/)||t.push({event:"stop",offset:i,node:s}));return i}(e,0),t}function v(e,n,i){var s=0,r="",a=[];function o(){return e.length&&n.length?e[0].offset!==n[0].offset?e[0].offset<n[0].offset?e:n:"start"===n[0].event?e:n:e.length?e:n}function l(e){r+="<"+f(e)+t.map.call(e.attributes,function(e){return" "+e.nodeName+'="'+h(e.value).replace(/"/g,"&quot;")+'"'}).join("")+">"}function c(e){r+="</"+f(e)+">"}function _(e){("start"===e.event?l:c)(e.node)}for(;e.length||n.length;){var p=o();if(r+=h(i.substring(s,p[0].offset)),s=p[0].offset,p===e){for(a.reverse().forEach(c);_(p.splice(0,1)[0]),(p=o())===e&&p.length&&p[0].offset===s;);a.reverse().forEach(l)}else"start"===p[0].event?a.push(p[0].node):a.pop(),_(p.splice(0,1)[0])}return r+h(i.substr(s))}function y(e){if(c&&!e.langApiRestored){for(var t in e.langApiRestored=!0,c)e[t]&&(e[c[t]]=e[t]);(e.c||[]).concat(e.v||[]).forEach(y)}}function w(e){function t(e){return e&&e.source||e}function i(n,i){return new RegExp(t(n),"m"+(e.cI?"i":"")+(i?"g":""))}if(e.c&&-1!=e.c.indexOf("self")){if(!r)throw new Error("ERR: contains `self` is not supported at the top-level of a language.  See documentation.");e.c=e.c.filter(function(e){return"self"!=e})}!function s(r,a){r.compiled||(r.compiled=!0,r.k=r.k||r.bK,r.k&&(r.k=function(e,t){var i={};return"string"==typeof e?s("keyword",e):n(e).forEach(function(t){s(t,e[t])}),i;function s(e,n){t&&(n=n.toLowerCase()),n.split(" ").forEach(function(t){var n,s,r=t.split("|");i[r[0]]=[e,(n=r[0],(s=r[1])?Number(s):function(e){return-1!=d.indexOf(e.toLowerCase())}(n)?0:1)]})}}(r.k,e.cI)),r.lR=i(r.l||/\w+/,!0),a&&(r.bK&&(r.b="\\b("+r.bK.split(" ").join("|")+")\\b"),r.b||(r.b=/\B|\b/),r.bR=i(r.b),r.endSameAsBegin&&(r.e=r.b),r.e||r.eW||(r.e=/\B|\b/),r.e&&(r.eR=i(r.e)),r.tE=t(r.e)||"",r.eW&&a.tE&&(r.tE+=(r.e?"|":"")+a.tE)),r.i&&(r.iR=i(r.i)),null==r.relevance&&(r.relevance=1),r.c||(r.c=[]),r.c=Array.prototype.concat.apply([],r.c.map(function(e){return(t="self"===e?r:e).v&&!t.cached_variants&&(t.cached_variants=t.v.map(function(e){return g(t,{v:null},e)})),t.cached_variants?t.cached_variants:function e(t){return!!t&&(t.eW||e(t.starts))}(t)?[g(t,{starts:t.starts?g(t.starts):null})]:Object.isFrozen(t)?[g(t)]:[t];var t})),r.c.forEach(function(e){s(e,r)}),r.starts&&s(r.starts,a),r.t=function(e){var n,s,r={},a=[],o={},l=1;function c(e,t){r[l]=e,a.push([e,t]),l+=new RegExp(t.toString()+"|").exec("").length-1+1}for(var _=0;_<e.c.length;_++)c(s=e.c[_],s.bK?"\\.?(?:"+s.b+")\\.?":s.b);e.tE&&c("end",e.tE),e.i&&c("illegal",e.i);var p=a.map(function(e){return e[1]});return n=i(function(e,n){for(var i=/\[(?:[^\\\]]|\\.)*\]|\(\??|\\([1-9][0-9]*)|\\./,s=0,r="",a=0;a<e.length;a++){var o=s+=1,l=t(e[a]);for(0<a&&(r+="|"),r+="(";0<l.length;){var c=i.exec(l);if(null==c){r+=l;break}r+=l.substring(0,c.index),l=l.substring(c.index+c[0].length),"\\"==c[0][0]&&c[1]?r+="\\"+String(Number(c[1])+o):(r+=c[0],"("==c[0]&&s++)}r+=")"}return r}(p),!0),o.lastIndex=0,o.exec=function(t){var i;if(0===a.length)return null;n.lastIndex=o.lastIndex;var s=n.exec(t);if(!s)return null;for(var l=0;l<s.length;l++)if(null!=s[l]&&null!=r[""+l]){i=r[""+l];break}return"string"==typeof i?(s.type=i,s.extra=[e.i,e.tE]):(s.type="begin",s.rule=i),s},o}(r))}(e)}function k(e,t,n,s){var a=t;function o(e,t,n,i){if(!n&&""===t)return"";if(!e)return t;var s='<span class="'+(i?"":u.classPrefix);return(s+=e+'">')+t+(n?"":_)}function l(){y+=(null!=m.sL?function(){var e="string"==typeof m.sL;if(e&&!i[m.sL])return h(N);var t=e?k(m.sL,N,!0,v[m.sL]):x(N,m.sL.length?m.sL:void 0);return 0<m.relevance&&(E+=t.relevance),e&&(v[m.sL]=t.top),o(t.language,t.value,!1,!0)}:function(){var e,t,n,i,s,r,a;if(!m.k)return h(N);for(i="",t=0,m.lR.lastIndex=0,n=m.lR.exec(N);n;)i+=h(N.substring(t,n.index)),s=m,r=n,a=b.cI?r[0].toLowerCase():r[0],(e=s.k.hasOwnProperty(a)&&s.k[a])?(E+=e[1],i+=o(e[0],h(n[0]))):i+=h(n[0]),t=m.lR.lastIndex,n=m.lR.exec(N);return i+h(N.substr(t))})(),N=""}function c(e){y+=e.cN?o(e.cN,"",!0):"",m=Object.create(e,{parent:{value:m}})}var d={};function f(e,t){var i=t&&t[0];if(N+=e,null==i)return l(),0;if("begin"==d.type&&"end"==t.type&&d.index==t.index&&""===i)return N+=a.slice(t.index,t.index+1),1;if("begin"===(d=t).type)return function(e){var t=e[0],n=e.rule;return n&&n.endSameAsBegin&&(n.eR=new RegExp(t.replace(/[-\/\\^$*+?.()|[\]{}]/g,"\\$&"),"m")),n.skip?N+=t:(n.eB&&(N+=t),l(),n.rB||n.eB||(N=t)),c(n),n.rB?0:t.length}(t);if("illegal"===t.type&&!n)throw new Error('Illegal lexeme "'+i+'" for mode "'+(m.cN||"<unnamed>")+'"');if("end"===t.type){var s=function(e){var t=e[0],n=a.substr(e.index),i=function e(t,n){if(s=n,(r=(i=t.eR)&&i.exec(s))&&0===r.index){for(;t.endsParent&&t.parent;)t=t.parent;return t}var i,s,r;if(t.eW)return e(t.parent,n)}(m,n);if(i){var s=m;for(s.skip?N+=t:(s.rE||s.eE||(N+=t),l(),s.eE&&(N=t));m.cN&&(y+=_),m.skip||m.sL||(E+=m.relevance),(m=m.parent)!==i.parent;);return i.starts&&(i.endSameAsBegin&&(i.starts.eR=i.eR),c(i.starts)),s.rE?0:t.length}}(t);if(null!=s)return s}return N+=i,i.length}var b=M(e);if(!b)throw console.error(p.replace("{}",e)),new Error('Unknown language: "'+e+'"');w(b);var g,m=s||b,v={},y="";for(g=m;g!==b;g=g.parent)g.cN&&(y=o(g.cN,"",!0)+y);var N="",E=0;try{for(var C,R,S=0;m.t.lastIndex=S,C=m.t.exec(a);)R=f(a.substring(S,C.index),C),S=C.index+R;for(f(a.substr(S)),g=m;g.parent;g=g.parent)g.cN&&(y+=_);return{relevance:E,value:y,i:!1,language:e,top:m}}catch(t){if(t.message&&-1!==t.message.indexOf("Illegal"))return{i:!0,relevance:0,value:h(a)};if(r)return{relevance:0,value:h(a),language:e,top:m,errorRaised:t};throw t}}function x(e,t){t=t||u.languages||n(i);var s={relevance:0,value:h(e)},r=s;return t.filter(M).filter(S).forEach(function(t){var n=k(t,e,!1);n.language=t,n.relevance>r.relevance&&(r=n),n.relevance>s.relevance&&(r=s,s=n)}),r.language&&(s.second_best=r),s}function N(e){return u.tabReplace||u.useBR?e.replace(l,function(e,t){return u.useBR&&"\n"===e?"<br>":u.tabReplace?t.replace(/\t/g,u.tabReplace):""}):e}function E(e){var t,n,i,r,a,l,c,_,d,h,f=function(e){var t,n,i,s,r=e.className+" ";if(r+=e.parentNode?e.parentNode.className:"",n=o.exec(r)){var a=M(n[1]);return a||(console.warn(p.replace("{}",n[1])),console.warn("Falling back to no-highlight mode for this block.",e)),a?n[1]:"no-highlight"}for(t=0,i=(r=r.split(/\s+/)).length;t<i;t++)if(b(s=r[t])||M(s))return s}(e);b(f)||(u.useBR?(t=document.createElement("div")).innerHTML=e.innerHTML.replace(/\n/g,"").replace(/<br[ \/]*>/g,"\n"):t=e,a=t.textContent,i=f?k(f,a,!0):x(a),(n=m(t)).length&&((r=document.createElement("div")).innerHTML=i.value,i.value=v(n,m(r),a)),i.value=N(i.value),e.innerHTML=i.value,e.className=(l=e.className,c=f,_=i.language,d=c?s[c]:_,h=[l.trim()],l.match(/\bhljs\b/)||h.push("hljs"),-1===l.indexOf(d)&&h.push(d),h.join(" ").trim()),e.result={language:i.language,re:i.relevance},i.second_best&&(e.second_best={language:i.second_best.language,re:i.second_best.relevance}))}function C(){if(!C.called){C.called=!0;var e=document.querySelectorAll("pre code");t.forEach.call(e,E)}}var R={disableAutodetect:!0};function M(e){return e=(e||"").toLowerCase(),i[e]||i[s[e]]}function S(e){var t=M(e);return t&&!t.disableAutodetect}return e.highlight=k,e.highlightAuto=x,e.fixMarkup=N,e.highlightBlock=E,e.configure=function(e){u=g(u,e)},e.initHighlighting=C,e.initHighlightingOnLoad=function(){window.addEventListener("DOMContentLoaded",C,!1),window.addEventListener("load",C,!1)},e.registerLanguage=function(t,n){var a;try{a=n(e)}catch(n){if(console.error("Language definition for '{}' could not be registered.".replace("{}",t)),!r)throw n;console.error(n),a=R}y(i[t]=a),a.rawDefinition=n.bind(null,e),a.aliases&&a.aliases.forEach(function(e){s[e]=t})},e.listLanguages=function(){return n(i)},e.getLanguage=M,e.requireLanguage=function(e){var t=M(e);if(t)return t;throw new Error("The '{}' language is required, but not loaded.".replace("{}",e))},e.autoDetection=S,e.inherit=g,e.debugMode=function(){r=!1},e.IR=e.IDENT_RE="[a-zA-Z]\\w*",e.UIR=e.UNDERSCORE_IDENT_RE="[a-zA-Z_]\\w*",e.NR=e.NUMBER_RE="\\b\\d+(\\.\\d+)?",e.CNR=e.C_NUMBER_RE="(-?)(\\b0[xX][a-fA-F0-9]+|(\\b\\d+(\\.\\d*)?|\\.\\d+)([eE][-+]?\\d+)?)",e.BNR=e.BINARY_NUMBER_RE="\\b(0b[01]+)",e.RSR=e.RE_STARTERS_RE="!|!=|!==|%|%=|&|&&|&=|\\*|\\*=|\\+|\\+=|,|-|-=|/=|/|:|;|<<|<<=|<=|<|===|==|=|>>>=|>>=|>=|>>>|>>|>|\\?|\\[|\\{|\\(|\\^|\\^=|\\||\\|=|\\|\\||~",e.BE=e.BACKSLASH_ESCAPE={b:"\\\\[\\s\\S]",relevance:0},e.ASM=e.APOS_STRING_MODE={cN:"string",b:"'",e:"'",i:"\\n",c:[e.BE]},e.QSM=e.QUOTE_STRING_MODE={cN:"string",b:'"',e:'"',i:"\\n",c:[e.BE]},e.PWM=e.PHRASAL_WORDS_MODE={b:/\b(a|an|the|are|I'm|isn't|don't|doesn't|won't|but|just|should|pretty|simply|enough|gonna|going|wtf|so|such|will|you|your|they|like|more)\b/},e.C=e.COMMENT=function(t,n,i){var s=e.inherit({cN:"comment",b:t,e:n,c:[]},i||{});return s.c.push(e.PWM),s.c.push({cN:"doctag",b:"(?:TODO|FIXME|NOTE|BUG|XXX):",relevance:0}),s},e.CLCM=e.C_LINE_COMMENT_MODE=e.C("//","$"),e.CBCM=e.C_BLOCK_COMMENT_MODE=e.C("/\\*","\\*/"),e.HCM=e.HASH_COMMENT_MODE=e.C("#","$"),e.NM=e.NUMBER_MODE={cN:"number",b:e.NR,relevance:0},e.CNM=e.C_NUMBER_MODE={cN:"number",b:e.CNR,relevance:0},e.BNM=e.BINARY_NUMBER_MODE={cN:"number",b:e.BNR,relevance:0},e.CSSNM=e.CSS_NUMBER_MODE={cN:"number",b:e.NR+"(%|em|ex|ch|rem|vw|vh|vmin|vmax|cm|mm|in|pt|pc|px|deg|grad|rad|turn|s|ms|Hz|kHz|dpi|dpcm|dppx)?",relevance:0},e.RM=e.REGEXP_MODE={cN:"regexp",b:/\//,e:/\/[gimuy]*/,i:/\n/,c:[e.BE,{b:/\[/,e:/\]/,relevance:0,c:[e.BE]}]},e.TM=e.TITLE_MODE={cN:"title",b:e.IR,relevance:0},e.UTM=e.UNDERSCORE_TITLE_MODE={cN:"title",b:e.UIR,relevance:0},e.METHOD_GUARD={b:"\\.\\s*"+e.UIR,relevance:0},[e.BE,e.ASM,e.QSM,e.PWM,e.C,e.CLCM,e.CBCM,e.HCM,e.NM,e.CNM,e.BNM,e.CSSNM,e.RM,e.TM,e.UTM,e.METHOD_GUARD].forEach(function(e){!function e(t){Object.freeze(t);var n="function"==typeof t;return Object.getOwnPropertyNames(t).forEach(function(i){!t.hasOwnProperty(i)||null===t[i]||"object"!=typeof t[i]&&"function"!=typeof t[i]||n&&("caller"===i||"callee"===i||"arguments"===i)||Object.isFrozen(t[i])||e(t[i])}),t}(e)}),e.registerLanguage("apache",function(e){var t={cN:"number",b:"[\\$%]\\d+"};return{aliases:["apacheconf"],cI:!0,c:[e.HCM,{cN:"section",b:"</?",e:">"},{cN:"attribute",b:/\w+/,relevance:0,k:{nomarkup:"order deny allow setenv rewriterule rewriteengine rewritecond documentroot sethandler errordocument loadmodule options header listen serverroot servername"},starts:{e:/$/,relevance:0,k:{literal:"on off all"},c:[{cN:"meta",b:"\\s\\[",e:"\\]$"},{cN:"variable",b:"[\\$%]\\{",e:"\\}",c:["self",t]},t,e.QSM]}}],i:/\S/}}),e.registerLanguage("bash",function(e){var t={cN:"variable",v:[{b:/\$[\w\d#@][\w\d_]*/},{b:/\$\{(.*?)}/}]},n={cN:"string",b:/"/,e:/"/,c:[e.BE,t,{cN:"variable",b:/\$\(/,e:/\)/,c:[e.BE]}]};return{aliases:["sh","zsh"],l:/\b-?[a-z\._]+\b/,k:{keyword:"if then else elif fi for while in do done case esac function",literal:"true false",built_in:"break cd continue eval exec exit export getopts hash pwd readonly return shift test times trap umask unset alias bind builtin caller command declare echo enable help let local logout mapfile printf read readarray source type typeset ulimit unalias set shopt autoload bg bindkey bye cap chdir clone comparguments compcall compctl compdescribe compfiles compgroups compquote comptags comptry compvalues dirs disable disown echotc echoti emulate fc fg float functions getcap getln history integer jobs kill limit log noglob popd print pushd pushln rehash sched setcap setopt stat suspend ttyctl unfunction unhash unlimit unsetopt vared wait whence where which zcompile zformat zftp zle zmodload zparseopts zprof zpty zregexparse zsocket zstyle ztcp",_:"-ne -eq -lt -gt -f -d -e -s -l -a"},c:[{cN:"meta",b:/^#![^\n]+sh\s*$/,relevance:10},{cN:"function",b:/\w[\w\d_]*\s*\(\s*\)\s*\{/,rB:!0,c:[e.inherit(e.TM,{b:/\w[\w\d_]*/})],relevance:0},e.HCM,n,{cN:"",b:/\\"/},{cN:"string",b:/'/,e:/'/},t]}}),e.registerLanguage("coffeescript",function(e){var t={keyword:"in if for while finally new do return else break catch instanceof throw try this switch continue typeof delete debugger super yield import export from as default await then unless until loop of by when and or is isnt not",literal:"true false null undefined yes no on off",built_in:"npm require console print module global window document"},n="[A-Za-z$_][0-9A-Za-z$_]*",i={cN:"subst",b:/#\{/,e:/}/,k:t},s=[e.BNM,e.inherit(e.CNM,{starts:{e:"(\\s*/)?",relevance:0}}),{cN:"string",v:[{b:/'''/,e:/'''/,c:[e.BE]},{b:/'/,e:/'/,c:[e.BE]},{b:/"""/,e:/"""/,c:[e.BE,i]},{b:/"/,e:/"/,c:[e.BE,i]}]},{cN:"regexp",v:[{b:"///",e:"///",c:[i,e.HCM]},{b:"//[gim]{0,3}(?=\\W)",relevance:0},{b:/\/(?![ *]).*?(?![\\]).\/[gim]{0,3}(?=\W)/}]},{b:"@"+n},{sL:"javascript",eB:!0,eE:!0,v:[{b:"```",e:"```"},{b:"`",e:"`"}]}];i.c=s;var r=e.inherit(e.TM,{b:n}),a="(\\(.*\\))?\\s*\\B[-=]>",o={cN:"params",b:"\\([^\\(]",rB:!0,c:[{b:/\(/,e:/\)/,k:t,c:["self"].concat(s)}]};return{aliases:["coffee","cson","iced"],k:t,i:/\/\*/,c:s.concat([e.C("###","###"),e.HCM,{cN:"function",b:"^\\s*"+n+"\\s*=\\s*"+a,e:"[-=]>",rB:!0,c:[r,o]},{b:/[:\(,=]\s*/,relevance:0,c:[{cN:"function",b:a,e:"[-=]>",rB:!0,c:[o]}]},{cN:"class",bK:"class",e:"$",i:/[:="\[\]]/,c:[{bK:"extends",eW:!0,i:/[:="\[\]]/,c:[r]},r]},{b:n+":",e:":",rB:!0,rE:!0,relevance:0}])}}),e.registerLanguage("cpp",function(e){function t(e){return"(?:"+e+")?"}var n="[a-zA-Z_]\\w*::",i=(t(n),t("<.*?>"),{cN:"keyword",b:"\\b[a-z\\d_]*_t\\b"}),s={cN:"string",v:[{b:'(u8?|U|L)?"',e:'"',i:"\\n",c:[e.BE]},{b:"(u8?|U|L)?'(\\\\(x[0-9A-Fa-f]{2}|u[0-9A-Fa-f]{4,8}|[0-7]{3}|\\S)|.)",e:"'",i:"."},{b:/(?:u8?|U|L)?R"([^()\\ ]{0,16})\((?:.|\n)*?\)\1"/}]},r={cN:"number",v:[{b:"\\b(0b[01']+)"},{b:"(-?)\\b([\\d']+(\\.[\\d']*)?|\\.[\\d']+)(u|U|l|L|ul|UL|f|F|b|B)"},{b:"(-?)(\\b0[xX][a-fA-F0-9']+|(\\b[\\d']+(\\.[\\d']*)?|\\.[\\d']+)([eE][-+]?[\\d']+)?)"}],relevance:0},a={cN:"meta",b:/#\s*[a-z]+\b/,e:/$/,k:{"meta-keyword":"if else elif endif define undef warning error line pragma _Pragma ifdef ifndef include"},c:[{b:/\\\n/,relevance:0},e.inherit(s,{cN:"meta-string"}),{cN:"meta-string",b:/<.*?>/,e:/$/,i:"\\n"},e.CLCM,e.CBCM]},o={cN:"title",b:t(n)+e.IR,relevance:0},l=t(n)+e.IR+"\\s*\\(",c={keyword:"int float while private char char8_t char16_t char32_t catch import module export virtual operator sizeof dynamic_cast|10 typedef const_cast|10 const for static_cast|10 union namespace unsigned long volatile static protected bool template mutable if public friend do goto auto void enum else break extern using asm case typeid wchar_tshort reinterpret_cast|10 default double register explicit signed typename try this switch continue inline delete alignas alignof constexpr consteval constinit decltype concept co_await co_return co_yield requires noexcept static_assert thread_local restrict final override atomic_bool atomic_char atomic_schar atomic_uchar atomic_short atomic_ushort atomic_int atomic_uint atomic_long atomic_ulong atomic_llong atomic_ullong new throw return and and_eq bitand bitor compl not not_eq or or_eq xor xor_eq",built_in:"std string wstring cin cout cerr clog stdin stdout stderr stringstream istringstream ostringstream auto_ptr deque list queue stack vector map set bitset multiset multimap unordered_set unordered_map unordered_multiset unordered_multimap array shared_ptr abort terminate abs acos asin atan2 atan calloc ceil cosh cos exit exp fabs floor fmod fprintf fputs free frexp fscanf future isalnum isalpha iscntrl isdigit isgraph islower isprint ispunct isspace isupper isxdigit tolower toupper labs ldexp log10 log malloc realloc memchr memcmp memcpy memset modf pow printf putchar puts scanf sinh sin snprintf sprintf sqrt sscanf strcat strchr strcmp strcpy strcspn strlen strncat strncmp strncpy strpbrk strrchr strspn strstr tanh tan vfprintf vprintf vsprintf endl initializer_list unique_ptr _Bool complex _Complex imaginary _Imaginary",literal:"true false nullptr NULL"},_=[i,e.CLCM,e.CBCM,r,s],p={v:[{b:/=/,e:/;/},{b:/\(/,e:/\)/},{bK:"new throw return else",e:/;/}],k:c,c:_.concat([{b:/\(/,e:/\)/,k:c,c:_.concat(["self"]),relevance:0}]),relevance:0},u={cN:"function",b:"((decltype\\(auto\\)|(?:[a-zA-Z_]\\w*::)?[a-zA-Z_]\\w*(?:<.*?>)?)[\\*&\\s]+)+"+l,rB:!0,e:/[{;=]/,eE:!0,k:c,i:/[^\w\s\*&:<>]/,c:[{b:"decltype\\(auto\\)",k:c,relevance:0},{b:l,rB:!0,c:[o],relevance:0},{cN:"params",b:/\(/,e:/\)/,k:c,relevance:0,c:[e.CLCM,e.CBCM,s,r,i,{b:/\(/,e:/\)/,k:c,relevance:0,c:["self",e.CLCM,e.CBCM,s,r,i]}]},i,e.CLCM,e.CBCM,a]};return{aliases:["c","cc","h","c++","h++","hpp","hh","hxx","cxx"],k:c,i:"</",c:[].concat(p,u,_,[a,{b:"\\b(deque|list|queue|stack|vector|map|set|bitset|multiset|multimap|unordered_map|unordered_set|unordered_multiset|unordered_multimap|array)\\s*<",e:">",k:c,c:["self",i]},{b:e.IR+"::",k:c},{cN:"class",bK:"class struct",e:/[{;:]/,c:[{b:/</,e:/>/,c:["self"]},e.TM]}]),exports:{preprocessor:a,strings:s,k:c}}}),e.registerLanguage("cs",function(e){var t={keyword:"abstract as base bool break byte case catch char checked const continue decimal default delegate do double enum event explicit extern finally fixed float for foreach goto if implicit in int interface internal is lock long object operator out override params private protected public readonly ref sbyte sealed short sizeof stackalloc static string struct switch this try typeof uint ulong unchecked unsafe ushort using virtual void volatile while add alias ascending async await by descending dynamic equals from get global group into join let nameof on orderby partial remove select set value var when where yield",literal:"null false true"},n={cN:"number",v:[{b:"\\b(0b[01']+)"},{b:"(-?)\\b([\\d']+(\\.[\\d']*)?|\\.[\\d']+)(u|U|l|L|ul|UL|f|F|b|B)"},{b:"(-?)(\\b0[xX][a-fA-F0-9']+|(\\b[\\d']+(\\.[\\d']*)?|\\.[\\d']+)([eE][-+]?[\\d']+)?)"}],relevance:0},i={cN:"string",b:'@"',e:'"',c:[{b:'""'}]},s=e.inherit(i,{i:/\n/}),r={cN:"subst",b:"{",e:"}",k:t},a=e.inherit(r,{i:/\n/}),o={cN:"string",b:/\$"/,e:'"',i:/\n/,c:[{b:"{{"},{b:"}}"},e.BE,a]},l={cN:"string",b:/\$@"/,e:'"',c:[{b:"{{"},{b:"}}"},{b:'""'},r]},c=e.inherit(l,{i:/\n/,c:[{b:"{{"},{b:"}}"},{b:'""'},a]});r.c=[l,o,i,e.ASM,e.QSM,n,e.CBCM],a.c=[c,o,s,e.ASM,e.QSM,n,e.inherit(e.CBCM,{i:/\n/})];var _={v:[l,o,i,e.ASM,e.QSM]},p=e.IR+"(<"+e.IR+"(\\s*,\\s*"+e.IR+")*>)?(\\[\\])?";return{aliases:["csharp","c#"],k:t,i:/::/,c:[e.C("///","$",{rB:!0,c:[{cN:"doctag",v:[{b:"///",relevance:0},{b:"\x3c!--|--\x3e"},{b:"</?",e:">"}]}]}),e.CLCM,e.CBCM,{cN:"meta",b:"#",e:"$",k:{"meta-keyword":"if else elif endif define undef warning error line region endregion pragma checksum"}},_,n,{bK:"class interface",e:/[{;=]/,i:/[^\s:,]/,c:[e.TM,e.CLCM,e.CBCM]},{bK:"namespace",e:/[{;=]/,i:/[^\s:]/,c:[e.inherit(e.TM,{b:"[a-zA-Z](\\.?\\w)*"}),e.CLCM,e.CBCM]},{cN:"meta",b:"^\\s*\\[",eB:!0,e:"\\]",eE:!0,c:[{cN:"meta-string",b:/"/,e:/"/}]},{bK:"new return throw await else",relevance:0},{cN:"function",b:"("+p+"\\s+)+"+e.IR+"\\s*\\(",rB:!0,e:/\s*[{;=]/,eE:!0,k:t,c:[{b:e.IR+"\\s*\\(",rB:!0,c:[e.TM],relevance:0},{cN:"params",b:/\(/,e:/\)/,eB:!0,eE:!0,k:t,relevance:0,c:[_,n,e.CBCM]},e.CLCM,e.CBCM]}]}}),e.registerLanguage("css",function(e){var t={b:/(?:[A-Z\_\.\-]+|--[a-zA-Z0-9_-]+)\s*:/,rB:!0,e:";",eW:!0,c:[{cN:"attribute",b:/\S/,e:":",eE:!0,starts:{eW:!0,eE:!0,c:[{b:/[\w-]+\(/,rB:!0,c:[{cN:"built_in",b:/[\w-]+/},{b:/\(/,e:/\)/,c:[e.ASM,e.QSM,e.CSSNM]}]},e.CSSNM,e.QSM,e.ASM,e.CBCM,{cN:"number",b:"#[0-9A-Fa-f]+"},{cN:"meta",b:"!important"}]}}]};return{cI:!0,i:/[=\/|'\$]/,c:[e.CBCM,{cN:"selector-id",b:/#[A-Za-z0-9_-]+/},{cN:"selector-class",b:/\.[A-Za-z0-9_-]+/},{cN:"selector-attr",b:/\[/,e:/\]/,i:"$",c:[e.ASM,e.QSM]},{cN:"selector-pseudo",b:/:(:)?[a-zA-Z0-9\_\-\+\(\)"'.]+/},{b:"@(page|font-face)",l:"@[a-z-]+",k:"@page @font-face"},{b:"@",e:"[{;]",i:/:/,rB:!0,c:[{cN:"keyword",b:/@\-?\w[\w]*(\-\w+)*/},{b:/\s/,eW:!0,eE:!0,relevance:0,k:"and or not only",c:[{b:/[a-z-]+:/,cN:"attribute"},e.ASM,e.QSM,e.CSSNM]}]},{cN:"selector-tag",b:"[a-zA-Z-][a-zA-Z0-9_-]*",relevance:0},{b:"{",e:"}",i:/\S/,c:[e.CBCM,t]}]}}),e.registerLanguage("diff",function(e){return{aliases:["patch"],c:[{cN:"meta",relevance:10,v:[{b:/^@@ +\-\d+,\d+ +\+\d+,\d+ +@@$/},{b:/^\*\*\* +\d+,\d+ +\*\*\*\*$/},{b:/^\-\-\- +\d+,\d+ +\-\-\-\-$/}]},{cN:"comment",v:[{b:/Index: /,e:/$/},{b:/={3,}/,e:/$/},{b:/^\-{3}/,e:/$/},{b:/^\*{3} /,e:/$/},{b:/^\+{3}/,e:/$/},{b:/^\*{15}$/}]},{cN:"addition",b:"^\\+",e:"$"},{cN:"deletion",b:"^\\-",e:"$"},{cN:"addition",b:"^\\!",e:"$"}]}}),e.registerLanguage("go",function(e){var t={keyword:"break default func interface select case map struct chan else goto package switch const fallthrough if range type continue for import return var go defer bool byte complex64 complex128 float32 float64 int8 int16 int32 int64 string uint8 uint16 uint32 uint64 int uint uintptr rune",literal:"true false iota nil",built_in:"append cap close complex copy imag len make new panic print println real recover delete"};return{aliases:["golang"],k:t,i:"</",c:[e.CLCM,e.CBCM,{cN:"string",v:[e.QSM,e.ASM,{b:"`",e:"`"}]},{cN:"number",v:[{b:e.CNR+"[i]",relevance:1},e.CNM]},{b:/:=/},{cN:"function",bK:"func",e:"\\s*(\\{|$)",eE:!0,c:[e.TM,{cN:"params",b:/\(/,e:/\)/,k:t,i:/["']/}]}]}}),e.registerLanguage("http",function(e){var t="HTTP/[0-9\\.]+";return{aliases:["https"],i:"\\S",c:[{b:"^"+t,e:"$",c:[{cN:"number",b:"\\b\\d{3}\\b"}]},{b:"^[A-Z]+ (.*?) "+t+"$",rB:!0,e:"$",c:[{cN:"string",b:" ",e:" ",eB:!0,eE:!0},{b:t},{cN:"keyword",b:"[A-Z]+"}]},{cN:"attribute",b:"^\\w",e:": ",eE:!0,i:"\\n|\\s|=",starts:{e:"$",relevance:0}},{b:"\\n\\n",starts:{sL:[],eW:!0}}]}}),e.registerLanguage("ini",function(e){var t={cN:"number",relevance:0,v:[{b:/([\+\-]+)?[\d]+_[\d_]+/},{b:e.NR}]},n=e.C();n.v=[{b:/;/,e:/$/},{b:/#/,e:/$/}];var i={cN:"variable",v:[{b:/\$[\w\d"][\w\d_]*/},{b:/\$\{(.*?)}/}]},s={cN:"literal",b:/\bon|off|true|false|yes|no\b/},r={cN:"string",c:[e.BE],v:[{b:"'''",e:"'''",relevance:10},{b:'"""',e:'"""',relevance:10},{b:'"',e:'"'},{b:"'",e:"'"}]};return{aliases:["toml"],cI:!0,i:/\S/,c:[n,{cN:"section",b:/\[+/,e:/\]+/},{b:/^[a-z0-9\[\]_\.-]+(?=\s*=\s*)/,cN:"attr",starts:{e:/$/,c:[n,{b:/\[/,e:/\]/,c:[n,s,i,r,t,"self"],relevance:0},s,i,r,t]}}]}}),e.registerLanguage("java",function(e){var t="false synchronized int abstract float private char boolean var static null if const for true while long strictfp finally protected import native final void enum else break transient catch instanceof byte super volatile case assert short package default double public try this switch continue throws protected public private module requires exports do";return{aliases:["jsp"],k:t,i:/<\/|#/,c:[e.C("/\\*\\*","\\*/",{relevance:0,c:[{b:/\w+@/,relevance:0},{cN:"doctag",b:"@[A-Za-z]+"}]}),e.CLCM,e.CBCM,e.ASM,e.QSM,{cN:"class",bK:"class interface",e:/[{;=]/,eE:!0,k:"class interface",i:/[:"\[\]]/,c:[{bK:"extends implements"},e.UTM]},{bK:"new throw return else",relevance:0},{cN:"function",b:"([À-ʸa-zA-Z_$][À-ʸa-zA-Z_$0-9]*(<[À-ʸa-zA-Z_$][À-ʸa-zA-Z_$0-9]*(\\s*,\\s*[À-ʸa-zA-Z_$][À-ʸa-zA-Z_$0-9]*)*>)?\\s+)+"+e.UIR+"\\s*\\(",rB:!0,e:/[{;=]/,eE:!0,k:t,c:[{b:e.UIR+"\\s*\\(",rB:!0,relevance:0,c:[e.UTM]},{cN:"params",b:/\(/,e:/\)/,k:t,relevance:0,c:[e.ASM,e.QSM,e.CNM,e.CBCM]},e.CLCM,e.CBCM]},{cN:"number",b:"\\b(0[bB]([01]+[01_]+[01]+|[01]+)|0[xX]([a-fA-F0-9]+[a-fA-F0-9_]+[a-fA-F0-9]+|[a-fA-F0-9]+)|(([\\d]+[\\d_]+[\\d]+|[\\d]+)(\\.([\\d]+[\\d_]+[\\d]+|[\\d]+))?|\\.([\\d]+[\\d_]+[\\d]+|[\\d]+))([eE][-+]?\\d+)?)[lLfF]?",relevance:0},{cN:"meta",b:"@[A-Za-z]+"}]}}),e.registerLanguage("javascript",function(e){var t={b:/<[A-Za-z0-9\\._:-]+/,e:/\/[A-Za-z0-9\\._:-]+>|\/>/},n="[A-Za-z$_][0-9A-Za-z$_]*",i={keyword:"in of if for while finally var new function do return void else break catch instanceof with throw case default try this switch continue typeof delete let yield const export super debugger as async await static import from as",literal:"true false null undefined NaN Infinity",built_in:"eval isFinite isNaN parseFloat parseInt decodeURI decodeURIComponent encodeURI encodeURIComponent escape unescape Object Function Boolean Error EvalError InternalError RangeError ReferenceError StopIteration SyntaxError TypeError URIError Number Math Date String RegExp Array Float32Array Float64Array Int16Array Int32Array Int8Array Uint16Array Uint32Array Uint8Array Uint8ClampedArray ArrayBuffer DataView JSON Intl arguments require module console window document Symbol Set Map WeakSet WeakMap Proxy Reflect Promise"},s={cN:"number",v:[{b:"\\b(0[bB][01]+)n?"},{b:"\\b(0[oO][0-7]+)n?"},{b:e.CNR+"n?"}],relevance:0},r={cN:"subst",b:"\\$\\{",e:"\\}",k:i,c:[]},a={b:"html`",e:"",starts:{e:"`",rE:!1,c:[e.BE,r],sL:"xml"}},o={b:"css`",e:"",starts:{e:"`",rE:!1,c:[e.BE,r],sL:"css"}},l={cN:"string",b:"`",e:"`",c:[e.BE,r]};r.c=[e.ASM,e.QSM,a,o,l,s,e.RM];var c=r.c.concat([e.CBCM,e.CLCM]);return{aliases:["js","jsx","mjs","cjs"],k:i,c:[{cN:"meta",relevance:10,b:/^\s*['"]use (strict|asm)['"]/},{cN:"meta",b:/^#!/,e:/$/},e.ASM,e.QSM,a,o,l,e.CLCM,e.C("/\\*\\*","\\*/",{relevance:0,c:[{cN:"doctag",b:"@[A-Za-z]+",c:[{cN:"type",b:"\\{",e:"\\}",relevance:0},{cN:"variable",b:n+"(?=\\s*(-)|$)",endsParent:!0,relevance:0},{b:/(?=[^\n])\s/,relevance:0}]}]}),e.CBCM,s,{b:/[{,\n]\s*/,relevance:0,c:[{b:n+"\\s*:",rB:!0,relevance:0,c:[{cN:"attr",b:n,relevance:0}]}]},{b:"("+e.RSR+"|\\b(case|return|throw)\\b)\\s*",k:"return throw case",c:[e.CLCM,e.CBCM,e.RM,{cN:"function",b:"(\\(.*?\\)|"+n+")\\s*=>",rB:!0,e:"\\s*=>",c:[{cN:"params",v:[{b:n},{b:/\(\s*\)/},{b:/\(/,e:/\)/,eB:!0,eE:!0,k:i,c:c}]}]},{cN:"",b:/\s/,e:/\s*/,skip:!0},{v:[{b:"<>",e:"</>"},{b:t.b,e:t.e}],sL:"xml",c:[{b:t.b,e:t.e,skip:!0,c:["self"]}]}],relevance:0},{cN:"function",bK:"function",e:/\{/,eE:!0,c:[e.inherit(e.TM,{b:n}),{cN:"params",b:/\(/,e:/\)/,eB:!0,eE:!0,c:c}],i:/\[|%/},{b:/\$[(.]/},e.METHOD_GUARD,{cN:"class",bK:"class",e:/[{;=]/,eE:!0,i:/[:"\[\]]/,c:[{bK:"extends"},e.UTM]},{bK:"constructor get set",e:/\{/,eE:!0}],i:/#(?!!)/}}),e.registerLanguage("json",function(e){var t={literal:"true false null"},n=[e.CLCM,e.CBCM],i=[e.QSM,e.CNM],s={e:",",eW:!0,eE:!0,c:i,k:t},r={b:"{",e:"}",c:[{cN:"attr",b:/"/,e:/"/,c:[e.BE],i:"\\n"},e.inherit(s,{b:/:/})].concat(n),i:"\\S"},a={b:"\\[",e:"\\]",c:[e.inherit(s)],i:"\\S"};return i.push(r,a),n.forEach(function(e){i.push(e)}),{c:i,k:t,i:"\\S"}}),e.registerLanguage("kotlin",function(e){var t={keyword:"abstract as val var vararg get set class object open private protected public noinline crossinline dynamic final enum if else do while for when throw try catch finally import package is in fun override companion reified inline lateinit init interface annotation data sealed internal infix operator out by constructor super tailrec where const inner suspend typealias external expect actual trait volatile transient native default",built_in:"Byte Short Char Int Long Boolean Float Double Void Unit Nothing",literal:"true false null"},n={cN:"symbol",b:e.UIR+"@"},i={cN:"subst",b:"\\${",e:"}",c:[e.CNM]},s={cN:"variable",b:"\\$"+e.UIR},r={cN:"string",v:[{b:'"""',e:'"""(?=[^"])',c:[s,i]},{b:"'",e:"'",i:/\n/,c:[e.BE]},{b:'"',e:'"',i:/\n/,c:[e.BE,s,i]}]};i.c.push(r);var a={cN:"meta",b:"@(?:file|property|field|get|set|receiver|param|setparam|delegate)\\s*:(?:\\s*"+e.UIR+")?"},o={cN:"meta",b:"@"+e.UIR,c:[{b:/\(/,e:/\)/,c:[e.inherit(r,{cN:"meta-string"})]}]},l=e.C("/\\*","\\*/",{c:[e.CBCM]}),c={v:[{cN:"type",b:e.UIR},{b:/\(/,e:/\)/,c:[]}]},_=c;return _.v[1].c=[c],c.v[1].c=[_],{aliases:["kt"],k:t,c:[e.C("/\\*\\*","\\*/",{relevance:0,c:[{cN:"doctag",b:"@[A-Za-z]+"}]}),e.CLCM,l,{cN:"keyword",b:/\b(break|continue|return|this)\b/,starts:{c:[{cN:"symbol",b:/@\w+/}]}},n,a,o,{cN:"function",bK:"fun",e:"[(]|$",rB:!0,eE:!0,k:t,i:/fun\s+(<.*>)?[^\s\(]+(\s+[^\s\(]+)\s*=/,relevance:5,c:[{b:e.UIR+"\\s*\\(",rB:!0,relevance:0,c:[e.UTM]},{cN:"type",b:/</,e:/>/,k:"reified",relevance:0},{cN:"params",b:/\(/,e:/\)/,endsParent:!0,k:t,relevance:0,c:[{b:/:/,e:/[=,\/]/,eW:!0,c:[c,e.CLCM,l],relevance:0},e.CLCM,l,a,o,r,e.CNM]},l]},{cN:"class",bK:"class interface trait",e:/[:\{(]|$/,eE:!0,i:"extends implements",c:[{bK:"public protected internal private constructor"},e.UTM,{cN:"type",b:/</,e:/>/,eB:!0,eE:!0,relevance:0},{cN:"type",b:/[,:]\s*/,e:/[<\(,]|$/,eB:!0,rE:!0},a,o]},r,{cN:"meta",b:"^#!/usr/bin/env",e:"$",i:"\n"},{cN:"number",b:"\\b(0[bB]([01]+[01_]+[01]+|[01]+)|0[xX]([a-fA-F0-9]+[a-fA-F0-9_]+[a-fA-F0-9]+|[a-fA-F0-9]+)|(([\\d]+[\\d_]+[\\d]+|[\\d]+)(\\.([\\d]+[\\d_]+[\\d]+|[\\d]+))?|\\.([\\d]+[\\d_]+[\\d]+|[\\d]+))([eE][-+]?\\d+)?)[lLfF]?",relevance:0}]}}),e.registerLanguage("less",function(e){function t(e){return{cN:"string",b:"~?"+e+".*?"+e}}function n(e,t,n){return{cN:e,b:t,relevance:n}}var i="[\\w-]+",s="("+i+"|@{"+i+"})",r=[],a=[],o={b:"\\(",e:"\\)",c:a,relevance:0};a.push(e.CLCM,e.CBCM,t("'"),t('"'),e.CSSNM,{b:"(url|data-uri)\\(",starts:{cN:"string",e:"[\\)\\n]",eE:!0}},n("number","#[0-9A-Fa-f]+\\b"),o,n("variable","@@?"+i,10),n("variable","@{"+i+"}"),n("built_in","~?`[^`]*?`"),{cN:"attribute",b:i+"\\s*:",e:":",rB:!0,eE:!0},{cN:"meta",b:"!important"});var l=a.concat({b:"{",e:"}",c:r}),c={bK:"when",eW:!0,c:[{bK:"and not"}].concat(a)},_={b:s+"\\s*:",rB:!0,e:"[;}]",relevance:0,c:[{cN:"attribute",b:s,e:":",eE:!0,starts:{eW:!0,i:"[<=$]",relevance:0,c:a}}]},p={cN:"keyword",b:"@(import|media|charset|font-face|(-[a-z]+-)?keyframes|supports|document|namespace|page|viewport|host)\\b",starts:{e:"[;{}]",rE:!0,c:a,relevance:0}},u={cN:"variable",v:[{b:"@"+i+"\\s*:",relevance:15},{b:"@"+i}],starts:{e:"[;}]",rE:!0,c:l}},d={v:[{b:"[\\.#:&\\[>]",e:"[;{}]"},{b:s,e:"{"}],rB:!0,rE:!0,i:"[<='$\"]",relevance:0,c:[e.CLCM,e.CBCM,c,n("keyword","all\\b"),n("variable","@{"+i+"}"),n("selector-tag",s+"%?",0),n("selector-id","#"+s),n("selector-class","\\."+s,0),n("selector-tag","&",0),{cN:"selector-attr",b:"\\[",e:"\\]"},{cN:"selector-pseudo",b:/:(:)?[a-zA-Z0-9\_\-\+\(\)"'.]+/},{b:"\\(",e:"\\)",c:l},{b:"!important"}]};return r.push(e.CLCM,e.CBCM,p,u,_,d),{cI:!0,i:"[=>'/<($\"]",c:r}}),e.registerLanguage("lua",function(e){var t="\\[=*\\[",n="\\]=*\\]",i={b:t,e:n,c:["self"]},s=[e.C("--(?!"+t+")","$"),e.C("--"+t,n,{c:[i],relevance:10})];return{l:e.UIR,k:{literal:"true false nil",keyword:"and break do else elseif end for goto if in local not or repeat return then until while",built_in:"_G _ENV _VERSION __index __newindex __mode __call __metatable __tostring __len __gc __add __sub __mul __div __mod __pow __concat __unm __eq __lt __le assert collectgarbage dofile error getfenv getmetatable ipairs load loadfile loadstringmodule next pairs pcall print rawequal rawget rawset require select setfenvsetmetatable tonumber tostring type unpack xpcall arg selfcoroutine resume yield status wrap create running debug getupvalue debug sethook getmetatable gethook setmetatable setlocal traceback setfenv getinfo setupvalue getlocal getregistry getfenv io lines write close flush open output type read stderr stdin input stdout popen tmpfile math log max acos huge ldexp pi cos tanh pow deg tan cosh sinh random randomseed frexp ceil floor rad abs sqrt modf asin min mod fmod log10 atan2 exp sin atan os exit setlocale date getenv difftime remove time clock tmpname rename execute package preload loadlib loaded loaders cpath config path seeall string sub upper len gfind rep find match char dump gmatch reverse byte format gsub lower table setn insert getn foreachi maxn foreach concat sort remove"},c:s.concat([{cN:"function",bK:"function",e:"\\)",c:[e.inherit(e.TM,{b:"([_a-zA-Z]\\w*\\.)*([_a-zA-Z]\\w*:)?[_a-zA-Z]\\w*"}),{cN:"params",b:"\\(",eW:!0,c:s}].concat(s)},e.CNM,e.ASM,e.QSM,{cN:"string",b:t,e:n,c:[i],relevance:5}])}}),e.registerLanguage("makefile",function(e){var t={cN:"variable",v:[{b:"\\$\\("+e.UIR+"\\)",c:[e.BE]},{b:/\$[@%<?\^\+\*]/}]},n={cN:"string",b:/"/,e:/"/,c:[e.BE,t]},i={cN:"variable",b:/\$\([\w-]+\s/,e:/\)/,k:{built_in:"subst patsubst strip findstring filter filter-out sort word wordlist firstword lastword dir notdir suffix basename addsuffix addprefix join wildcard realpath abspath error warning shell origin flavor foreach if or and call eval file value"},c:[t]},s={b:"^"+e.UIR+"\\s*(?=[:+?]?=)"},r={cN:"section",b:/^[^\s]+:/,e:/$/,c:[t]};return{aliases:["mk","mak"],k:"define endef undefine ifdef ifndef ifeq ifneq else endif include -include sinclude override export unexport private vpath",l:/[\w-]+/,c:[e.HCM,t,n,i,s,{cN:"meta",b:/^\.PHONY:/,e:/$/,k:{"meta-keyword":".PHONY"},l:/[\.\w]+/},r]}}),e.registerLanguage("xml",function(e){var t={cN:"symbol",b:"&[a-z]+;|&#[0-9]+;|&#x[a-f0-9]+;"},n={b:"\\s",c:[{cN:"meta-keyword",b:"#?[a-z_][a-z1-9_-]+",i:"\\n"}]},i=e.inherit(n,{b:"\\(",e:"\\)"}),s=e.inherit(e.ASM,{cN:"meta-string"}),r=e.inherit(e.QSM,{cN:"meta-string"}),a={eW:!0,i:/</,relevance:0,c:[{cN:"attr",b:"[A-Za-z0-9\\._:-]+",relevance:0},{b:/=\s*/,relevance:0,c:[{cN:"string",endsParent:!0,v:[{b:/"/,e:/"/,c:[t]},{b:/'/,e:/'/,c:[t]},{b:/[^\s"'=<>`]+/}]}]}]};return{aliases:["html","xhtml","rss","atom","xjb","xsd","xsl","plist","wsf","svg"],cI:!0,c:[{cN:"meta",b:"<![a-z]",e:">",relevance:10,c:[n,r,s,i,{b:"\\[",e:"\\]",c:[{cN:"meta",b:"<![a-z]",e:">",c:[n,i,r,s]}]}]},e.C("\x3c!--","--\x3e",{relevance:10}),{b:"<\\!\\[CDATA\\[",e:"\\]\\]>",relevance:10},t,{cN:"meta",b:/<\?xml/,e:/\?>/,relevance:10},{b:/<\?(php)?/,e:/\?>/,sL:"php",c:[{b:"/\\*",e:"\\*/",skip:!0},{b:'b"',e:'"',skip:!0},{b:"b'",e:"'",skip:!0},e.inherit(e.ASM,{i:null,cN:null,c:null,skip:!0}),e.inherit(e.QSM,{i:null,cN:null,c:null,skip:!0})]},{cN:"tag",b:"<style(?=\\s|>)",e:">",k:{name:"style"},c:[a],starts:{e:"</style>",rE:!0,sL:["css","xml"]}},{cN:"tag",b:"<script(?=\\s|>)",e:">",k:{name:"script"},c:[a],starts:{e:"<\/script>",rE:!0,sL:["actionscript","javascript","handlebars","xml"]}},{cN:"tag",b:"</?",e:"/?>",c:[{cN:"name",b:/[^\/><\s]+/,relevance:0},a]}]}}),e.registerLanguage("markdown",function(e){return{aliases:["md","mkdown","mkd"],c:[{cN:"section",v:[{b:"^#{1,6}",e:"$"},{b:"^.+?\\n[=-]{2,}$"}]},{b:"<",e:">",sL:"xml",relevance:0},{cN:"bullet",b:"^\\s*([*+-]|(\\d+\\.))\\s+"},{cN:"strong",b:"[*_]{2}.+?[*_]{2}"},{cN:"emphasis",v:[{b:"\\*.+?\\*"},{b:"_.+?_",relevance:0}]},{cN:"quote",b:"^>\\s+",e:"$"},{cN:"code",v:[{b:"^```\\w*\\s*$",e:"^```[ ]*$"},{b:"`.+?`"},{b:"^( {4}|\\t)",e:"$",relevance:0}]},{b:"^[-\\*]{3,}",e:"$"},{b:"\\[.+?\\][\\(\\[].*?[\\)\\]]",rB:!0,c:[{cN:"string",b:"\\[",e:"\\]",eB:!0,rE:!0,relevance:0},{cN:"link",b:"\\]\\(",e:"\\)",eB:!0,eE:!0},{cN:"symbol",b:"\\]\\[",e:"\\]",eB:!0,eE:!0}],relevance:10},{b:/^\[[^\n]+\]:/,rB:!0,c:[{cN:"symbol",b:/\[/,e:/\]/,eB:!0,eE:!0},{cN:"link",b:/:\s*/,e:/$/,eB:!0}]}]}}),e.registerLanguage("nginx",function(e){var t={cN:"variable",v:[{b:/\$\d+/},{b:/\$\{/,e:/}/},{b:"[\\$\\@]"+e.UIR}]},n={eW:!0,l:"[a-z/_]+",k:{literal:"on off yes no true false none blocked debug info notice warn error crit select break last permanent redirect kqueue rtsig epoll poll /dev/poll"},relevance:0,i:"=>",c:[e.HCM,{cN:"string",c:[e.BE,t],v:[{b:/"/,e:/"/},{b:/'/,e:/'/}]},{b:"([a-z]+):/",e:"\\s",eW:!0,eE:!0,c:[t]},{cN:"regexp",c:[e.BE,t],v:[{b:"\\s\\^",e:"\\s|{|;",rE:!0},{b:"~\\*?\\s+",e:"\\s|{|;",rE:!0},{b:"\\*(\\.[a-z\\-]+)+"},{b:"([a-z\\-]+\\.)+\\*"}]},{cN:"number",b:"\\b\\d{1,3}\\.\\d{1,3}\\.\\d{1,3}\\.\\d{1,3}(:\\d{1,5})?\\b"},{cN:"number",b:"\\b\\d+[kKmMgGdshdwy]*\\b",relevance:0},t]};return{aliases:["nginxconf"],c:[e.HCM,{b:e.UIR+"\\s+{",rB:!0,e:"{",c:[{cN:"section",b:e.UIR}],relevance:0},{b:e.UIR+"\\s",e:";|{",rB:!0,c:[{cN:"attribute",b:e.UIR,starts:n}],relevance:0}],i:"[^\\s\\}]"}}),e.registerLanguage("objectivec",function(e){var t=/[a-zA-Z@][a-zA-Z0-9_]*/,n="@interface @class @protocol @implementation";return{aliases:["mm","objc","obj-c"],k:{keyword:"int float while char export sizeof typedef const struct for union unsigned long volatile static bool mutable if do return goto void enum else break extern asm case short default double register explicit signed typename this switch continue wchar_t inline readonly assign readwrite self @synchronized id typeof nonatomic super unichar IBOutlet IBAction strong weak copy in out inout bycopy byref oneway __strong __weak __block __autoreleasing @private @protected @public @try @property @end @throw @catch @finally @autoreleasepool @synthesize @dynamic @selector @optional @required @encode @package @import @defs @compatibility_alias __bridge __bridge_transfer __bridge_retained __bridge_retain __covariant __contravariant __kindof _Nonnull _Nullable _Null_unspecified __FUNCTION__ __PRETTY_FUNCTION__ __attribute__ getter setter retain unsafe_unretained nonnull nullable null_unspecified null_resettable class instancetype NS_DESIGNATED_INITIALIZER NS_UNAVAILABLE NS_REQUIRES_SUPER NS_RETURNS_INNER_POINTER NS_INLINE NS_AVAILABLE NS_DEPRECATED NS_ENUM NS_OPTIONS NS_SWIFT_UNAVAILABLE NS_ASSUME_NONNULL_BEGIN NS_ASSUME_NONNULL_END NS_REFINED_FOR_SWIFT NS_SWIFT_NAME NS_SWIFT_NOTHROW NS_DURING NS_HANDLER NS_ENDHANDLER NS_VALUERETURN NS_VOIDRETURN",literal:"false true FALSE TRUE nil YES NO NULL",built_in:"BOOL dispatch_once_t dispatch_queue_t dispatch_sync dispatch_async dispatch_once"},l:t,i:"</",c:[{cN:"built_in",b:"\\b(AV|CA|CF|CG|CI|CL|CM|CN|CT|MK|MP|MTK|MTL|NS|SCN|SK|UI|WK|XC)\\w+"},e.CLCM,e.CBCM,e.CNM,e.QSM,e.ASM,{cN:"string",v:[{b:'@"',e:'"',i:"\\n",c:[e.BE]}]},{cN:"meta",b:/#\s*[a-z]+\b/,e:/$/,k:{"meta-keyword":"if else elif endif define undef warning error line pragma ifdef ifndef include"},c:[{b:/\\\n/,relevance:0},e.inherit(e.QSM,{cN:"meta-string"}),{cN:"meta-string",b:/<.*?>/,e:/$/,i:"\\n"},e.CLCM,e.CBCM]},{cN:"class",b:"("+n.split(" ").join("|")+")\\b",e:"({|$)",eE:!0,k:n,l:t,c:[e.UTM]},{b:"\\."+e.UIR,relevance:0}]}}),e.registerLanguage("perl",function(e){var t="getpwent getservent quotemeta msgrcv scalar kill dbmclose undef lc ma syswrite tr send umask sysopen shmwrite vec qx utime local oct semctl localtime readpipe do return format read sprintf dbmopen pop getpgrp not getpwnam rewinddir qqfileno qw endprotoent wait sethostent bless s|0 opendir continue each sleep endgrent shutdown dump chomp connect getsockname die socketpair close flock exists index shmgetsub for endpwent redo lstat msgctl setpgrp abs exit select print ref gethostbyaddr unshift fcntl syscall goto getnetbyaddr join gmtime symlink semget splice x|0 getpeername recv log setsockopt cos last reverse gethostbyname getgrnam study formline endhostent times chop length gethostent getnetent pack getprotoent getservbyname rand mkdir pos chmod y|0 substr endnetent printf next open msgsnd readdir use unlink getsockopt getpriority rindex wantarray hex system getservbyport endservent int chr untie rmdir prototype tell listen fork shmread ucfirst setprotoent else sysseek link getgrgid shmctl waitpid unpack getnetbyname reset chdir grep split require caller lcfirst until warn while values shift telldir getpwuid my getprotobynumber delete and sort uc defined srand accept package seekdir getprotobyname semop our rename seek if q|0 chroot sysread setpwent no crypt getc chown sqrt write setnetent setpriority foreach tie sin msgget map stat getlogin unless elsif truncate exec keys glob tied closedirioctl socket readlink eval xor readline binmode setservent eof ord bind alarm pipe atan2 getgrent exp time push setgrent gt lt or ne m|0 break given say state when",n={cN:"subst",b:"[$@]\\{",e:"\\}",k:t},i={b:"->{",e:"}"},s={v:[{b:/\$\d/},{b:/[\$%@](\^\w\b|#\w+(::\w+)*|{\w+}|\w+(::\w*)*)/},{b:/[\$%@][^\s\w{]/,relevance:0}]},r=[e.BE,n,s],a=[s,e.HCM,e.C("^\\=\\w","\\=cut",{eW:!0}),i,{cN:"string",c:r,v:[{b:"q[qwxr]?\\s*\\(",e:"\\)",relevance:5},{b:"q[qwxr]?\\s*\\[",e:"\\]",relevance:5},{b:"q[qwxr]?\\s*\\{",e:"\\}",relevance:5},{b:"q[qwxr]?\\s*\\|",e:"\\|",relevance:5},{b:"q[qwxr]?\\s*\\<",e:"\\>",relevance:5},{b:"qw\\s+q",e:"q",relevance:5},{b:"'",e:"'",c:[e.BE]},{b:'"',e:'"'},{b:"`",e:"`",c:[e.BE]},{b:"{\\w+}",c:[],relevance:0},{b:"-?\\w+\\s*\\=\\>",c:[],relevance:0}]},{cN:"number",b:"(\\b0[0-7_]+)|(\\b0x[0-9a-fA-F_]+)|(\\b[1-9][0-9_]*(\\.[0-9_]+)?)|[0_]\\b",relevance:0},{b:"(\\/\\/|"+e.RSR+"|\\b(split|return|print|reverse|grep)\\b)\\s*",k:"split return print reverse grep",relevance:0,c:[e.HCM,{cN:"regexp",b:"(s|tr|y)/(\\\\.|[^/])*/(\\\\.|[^/])*/[a-z]*",relevance:10},{cN:"regexp",b:"(m|qr)?/",e:"/[a-z]*",c:[e.BE],relevance:0}]},{cN:"function",bK:"sub",e:"(\\s*\\(.*?\\))?[;{]",eE:!0,relevance:5,c:[e.TM]},{b:"-\\w\\b",relevance:0},{b:"^__DATA__$",e:"^__END__$",sL:"mojolicious",c:[{b:"^@@.*",e:"$",cN:"comment"}]}];return n.c=a,{aliases:["pl","pm"],l:/[\w\.]+/,k:t,c:i.c=a}}),e.registerLanguage("php",function(e){var t={b:"\\$+[a-zA-Z_-ÿ][a-zA-Z0-9_-ÿ]*"},n={cN:"meta",b:/<\?(php)?|\?>/},i={cN:"string",c:[e.BE,n],v:[{b:'b"',e:'"'},{b:"b'",e:"'"},e.inherit(e.ASM,{i:null}),e.inherit(e.QSM,{i:null})]},s={v:[e.BNM,e.CNM]};return{aliases:["php","php3","php4","php5","php6","php7"],cI:!0,k:"and include_once list abstract global private echo interface as static endswitch array null if endwhile or const for endforeach self var while isset public protected exit foreach throw elseif include __FILE__ empty require_once do xor return parent clone use __CLASS__ __LINE__ else break print eval new catch __METHOD__ case exception default die require __FUNCTION__ enddeclare final try switch continue endfor endif declare unset true false trait goto instanceof insteadof __DIR__ __NAMESPACE__ yield finally",c:[e.HCM,e.C("//","$",{c:[n]}),e.C("/\\*","\\*/",{c:[{cN:"doctag",b:"@[A-Za-z]+"}]}),e.C("__halt_compiler.+?;",!1,{eW:!0,k:"__halt_compiler",l:e.UIR}),{cN:"string",b:/<<<['"]?\w+['"]?$/,e:/^\w+;?$/,c:[e.BE,{cN:"subst",v:[{b:/\$\w+/},{b:/\{\$/,e:/\}/}]}]},n,{cN:"keyword",b:/\$this\b/},t,{b:/(::|->)+[a-zA-Z_\x7f-\xff][a-zA-Z0-9_\x7f-\xff]*/},{cN:"function",bK:"function",e:/[;{]/,eE:!0,i:"\\$|\\[|%",c:[e.UTM,{cN:"params",b:"\\(",e:"\\)",c:["self",t,e.CBCM,i,s]}]},{cN:"class",bK:"class interface",e:"{",eE:!0,i:/[:\(\$"]/,c:[{bK:"extends implements"},e.UTM]},{bK:"namespace",e:";",i:/[\.']/,c:[e.UTM]},{bK:"use",e:";",c:[e.UTM]},{b:"=>"},i,s]}}),e.registerLanguage("plaintext",function(e){return{disableAutodetect:!0}}),e.registerLanguage("properties",function(e){var t="[ \\t\\f]*",n="("+t+"[:=]"+t+"|[ \\t\\f]+)",i="([^\\\\\\W:= \\t\\f\\n]|\\\\.)+",s="([^\\\\:= \\t\\f\\n]|\\\\.)+",r={e:n,relevance:0,starts:{cN:"string",e:/$/,relevance:0,c:[{b:"\\\\\\n"}]}};return{cI:!0,i:/\S/,c:[e.C("^\\s*[!#]","$"),{b:i+n,rB:!0,c:[{cN:"attr",b:i,endsParent:!0,relevance:0}],starts:r},{b:s+n,rB:!0,relevance:0,c:[{cN:"meta",b:s,endsParent:!0,relevance:0}],starts:r},{cN:"attr",relevance:0,b:s+t+"$"}]}}),e.registerLanguage("python",function(e){var t={keyword:"and elif is global as in if from raise for except finally print import pass return exec else break not with class assert yield try while continue del or def lambda async await nonlocal|10",built_in:"Ellipsis NotImplemented",literal:"False None True"},n={cN:"meta",b:/^(>>>|\.\.\.) /},i={cN:"subst",b:/\{/,e:/\}/,k:t,i:/#/},s={b:/\{\{/,relevance:0},r={cN:"string",c:[e.BE],v:[{b:/(u|b)?r?'''/,e:/'''/,c:[e.BE,n],relevance:10},{b:/(u|b)?r?"""/,e:/"""/,c:[e.BE,n],relevance:10},{b:/(fr|rf|f)'''/,e:/'''/,c:[e.BE,n,s,i]},{b:/(fr|rf|f)"""/,e:/"""/,c:[e.BE,n,s,i]},{b:/(u|r|ur)'/,e:/'/,relevance:10},{b:/(u|r|ur)"/,e:/"/,relevance:10},{b:/(b|br)'/,e:/'/},{b:/(b|br)"/,e:/"/},{b:/(fr|rf|f)'/,e:/'/,c:[e.BE,s,i]},{b:/(fr|rf|f)"/,e:/"/,c:[e.BE,s,i]},e.ASM,e.QSM]},a={cN:"number",relevance:0,v:[{b:e.BNR+"[lLjJ]?"},{b:"\\b(0o[0-7]+)[lLjJ]?"},{b:e.CNR+"[lLjJ]?"}]},o={cN:"params",b:/\(/,e:/\)/,c:["self",n,a,r,e.HCM]};return i.c=[r,a,n],{aliases:["py","gyp","ipython"],k:t,i:/(<\/|->|\?)|=>/,c:[n,a,{bK:"if",relevance:0},r,e.HCM,{v:[{cN:"function",bK:"def"},{cN:"class",bK:"class"}],e:/:/,i:/[${=;\n,]/,c:[e.UTM,o,{b:/->/,eW:!0,k:"None"}]},{cN:"meta",b:/^[\t ]*@/,e:/$/},{b:/\b(print|exec)\(/}]}}),e.registerLanguage("ruby",function(e){var t="[a-zA-Z_]\\w*[!?=]?|[-+~]\\@|<<|>>|=~|===?|<=>|[<>]=?|\\*\\*|[-/+%^&*~`|]|\\[\\]=?",n={keyword:"and then defined module in return redo if BEGIN retry end for self when next until do begin unless END rescue else break undef not super class case require yield alias while ensure elsif or include attr_reader attr_writer attr_accessor",literal:"true false nil"},i={cN:"doctag",b:"@[A-Za-z]+"},s={b:"#<",e:">"},r=[e.C("#","$",{c:[i]}),e.C("^\\=begin","^\\=end",{c:[i],relevance:10}),e.C("^__END__","\\n$")],a={cN:"subst",b:"#\\{",e:"}",k:n},o={cN:"string",c:[e.BE,a],v:[{b:/'/,e:/'/},{b:/"/,e:/"/},{b:/`/,e:/`/},{b:"%[qQwWx]?\\(",e:"\\)"},{b:"%[qQwWx]?\\[",e:"\\]"},{b:"%[qQwWx]?{",e:"}"},{b:"%[qQwWx]?<",e:">"},{b:"%[qQwWx]?/",e:"/"},{b:"%[qQwWx]?%",e:"%"},{b:"%[qQwWx]?-",e:"-"},{b:"%[qQwWx]?\\|",e:"\\|"},{b:/\B\?(\\\d{1,3}|\\x[A-Fa-f0-9]{1,2}|\\u[A-Fa-f0-9]{4}|\\?\S)\b/},{b:/<<[-~]?'?(\w+)(?:.|\n)*?\n\s*\1\b/,rB:!0,c:[{b:/<<[-~]?'?/},{b:/\w+/,endSameAsBegin:!0,c:[e.BE,a]}]}]},l={cN:"params",b:"\\(",e:"\\)",endsParent:!0,k:n},c=[o,s,{cN:"class",bK:"class module",e:"$|;",i:/=/,c:[e.inherit(e.TM,{b:"[A-Za-z_]\\w*(::\\w+)*(\\?|\\!)?"}),{b:"<\\s*",c:[{b:"("+e.IR+"::)?"+e.IR}]}].concat(r)},{cN:"function",bK:"def",e:"$|;",c:[e.inherit(e.TM,{b:t}),l].concat(r)},{b:e.IR+"::"},{cN:"symbol",b:e.UIR+"(\\!|\\?)?:",relevance:0},{cN:"symbol",b:":(?!\\s)",c:[o,{b:t}],relevance:0},{cN:"number",b:"(\\b0[0-7_]+)|(\\b0x[0-9a-fA-F_]+)|(\\b[1-9][0-9_]*(\\.[0-9_]+)?)|[0_]\\b",relevance:0},{b:"(\\$\\W)|((\\$|\\@\\@?)(\\w+))"},{cN:"params",b:/\|/,e:/\|/,k:n},{b:"("+e.RSR+"|unless)\\s*",k:"unless",c:[s,{cN:"regexp",c:[e.BE,a],i:/\n/,v:[{b:"/",e:"/[a-z]*"},{b:"%r{",e:"}[a-z]*"},{b:"%r\\(",e:"\\)[a-z]*"},{b:"%r!",e:"![a-z]*"},{b:"%r\\[",e:"\\][a-z]*"}]}].concat(r),relevance:0}].concat(r);a.c=c;var _=[{b:/^\s*=>/,starts:{e:"$",c:l.c=c}},{cN:"meta",b:"^([>?]>|[\\w#]+\\(\\w+\\):\\d+:\\d+>|(\\w+-)?\\d+\\.\\d+\\.\\d(p\\d+)?[^>]+>)",starts:{e:"$",c:c}}];return{aliases:["rb","gemspec","podspec","thor","irb"],k:n,i:/\/\*/,c:r.concat(_).concat(c)}}),e.registerLanguage("rust",function(e){var t="([ui](8|16|32|64|128|size)|f(32|64))?",n="drop i8 i16 i32 i64 i128 isize u8 u16 u32 u64 u128 usize f32 f64 str char bool Box Option Result String Vec Copy Send Sized Sync Drop Fn FnMut FnOnce ToOwned Clone Debug PartialEq PartialOrd Eq Ord AsRef AsMut Into From Default Iterator Extend IntoIterator DoubleEndedIterator ExactSizeIterator SliceConcatExt ToString assert! assert_eq! bitflags! bytes! cfg! col! concat! concat_idents! debug_assert! debug_assert_eq! env! panic! file! format! format_args! include_bin! include_str! line! local_data_key! module_path! option_env! print! println! select! stringify! try! unimplemented! unreachable! vec! write! writeln! macro_rules! assert_ne! debug_assert_ne!";return{aliases:["rs"],k:{keyword:"abstract as async await become box break const continue crate do dyn else enum extern false final fn for if impl in let loop macro match mod move mut override priv pub ref return self Self static struct super trait true try type typeof unsafe unsized use virtual where while yield",literal:"true false Some None Ok Err",built_in:n},l:e.IR+"!?",i:"</",c:[e.CLCM,e.C("/\\*","\\*/",{c:["self"]}),e.inherit(e.QSM,{b:/b?"/,i:null}),{cN:"string",v:[{b:/r(#*)"(.|\n)*?"\1(?!#)/},{b:/b?'\\?(x\w{2}|u\w{4}|U\w{8}|.)'/}]},{cN:"symbol",b:/'[a-zA-Z_][a-zA-Z0-9_]*/},{cN:"number",v:[{b:"\\b0b([01_]+)"+t},{b:"\\b0o([0-7_]+)"+t},{b:"\\b0x([A-Fa-f0-9_]+)"+t},{b:"\\b(\\d[\\d_]*(\\.[0-9_]+)?([eE][+-]?[0-9_]+)?)"+t}],relevance:0},{cN:"function",bK:"fn",e:"(\\(|<)",eE:!0,c:[e.UTM]},{cN:"meta",b:"#\\!?\\[",e:"\\]",c:[{cN:"meta-string",b:/"/,e:/"/}]},{cN:"class",bK:"type",e:";",c:[e.inherit(e.UTM,{endsParent:!0})],i:"\\S"},{cN:"class",bK:"trait enum struct union",e:"{",c:[e.inherit(e.UTM,{endsParent:!0})],i:"[\\w\\d]"},{b:e.IR+"::",k:{built_in:n}},{b:"->"}]}}),e.registerLanguage("scss",function(e){var t="@[a-z-]+",n={cN:"variable",b:"(\\$[a-zA-Z-][a-zA-Z0-9_-]*)\\b"},i={cN:"number",b:"#[0-9A-Fa-f]+"};return e.CSSNM,e.QSM,e.ASM,e.CBCM,{cI:!0,i:"[=/|']",c:[e.CLCM,e.CBCM,{cN:"selector-id",b:"\\#[A-Za-z0-9_-]+",relevance:0},{cN:"selector-class",b:"\\.[A-Za-z0-9_-]+",relevance:0},{cN:"selector-attr",b:"\\[",e:"\\]",i:"$"},{cN:"selector-tag",b:"\\b(a|abbr|acronym|address|area|article|aside|audio|b|base|big|blockquote|body|br|button|canvas|caption|cite|code|col|colgroup|command|datalist|dd|del|details|dfn|div|dl|dt|em|embed|fieldset|figcaption|figure|footer|form|frame|frameset|(h[1-6])|head|header|hgroup|hr|html|i|iframe|img|input|ins|kbd|keygen|label|legend|li|link|map|mark|meta|meter|nav|noframes|noscript|object|ol|optgroup|option|output|p|param|pre|progress|q|rp|rt|ruby|samp|script|section|select|small|span|strike|strong|style|sub|sup|table|tbody|td|textarea|tfoot|th|thead|time|title|tr|tt|ul|var|video)\\b",relevance:0},{cN:"selector-pseudo",b:":(visited|valid|root|right|required|read-write|read-only|out-range|optional|only-of-type|only-child|nth-of-type|nth-last-of-type|nth-last-child|nth-child|not|link|left|last-of-type|last-child|lang|invalid|indeterminate|in-range|hover|focus|first-of-type|first-line|first-letter|first-child|first|enabled|empty|disabled|default|checked|before|after|active)"},{cN:"selector-pseudo",b:"::(after|before|choices|first-letter|first-line|repeat-index|repeat-item|selection|value)"},n,{cN:"attribute",b:"\\b(src|z-index|word-wrap|word-spacing|word-break|width|widows|white-space|visibility|vertical-align|unicode-bidi|transition-timing-function|transition-property|transition-duration|transition-delay|transition|transform-style|transform-origin|transform|top|text-underline-position|text-transform|text-shadow|text-rendering|text-overflow|text-indent|text-decoration-style|text-decoration-line|text-decoration-color|text-decoration|text-align-last|text-align|tab-size|table-layout|right|resize|quotes|position|pointer-events|perspective-origin|perspective|page-break-inside|page-break-before|page-break-after|padding-top|padding-right|padding-left|padding-bottom|padding|overflow-y|overflow-x|overflow-wrap|overflow|outline-width|outline-style|outline-offset|outline-color|outline|orphans|order|opacity|object-position|object-fit|normal|none|nav-up|nav-right|nav-left|nav-index|nav-down|min-width|min-height|max-width|max-height|mask|marks|margin-top|margin-right|margin-left|margin-bottom|margin|list-style-type|list-style-position|list-style-image|list-style|line-height|letter-spacing|left|justify-content|initial|inherit|ime-mode|image-orientation|image-resolution|image-rendering|icon|hyphens|height|font-weight|font-variant-ligatures|font-variant|font-style|font-stretch|font-size-adjust|font-size|font-language-override|font-kerning|font-feature-settings|font-family|font|float|flex-wrap|flex-shrink|flex-grow|flex-flow|flex-direction|flex-basis|flex|filter|empty-cells|display|direction|cursor|counter-reset|counter-increment|content|column-width|column-span|column-rule-width|column-rule-style|column-rule-color|column-rule|column-gap|column-fill|column-count|columns|color|clip-path|clip|clear|caption-side|break-inside|break-before|break-after|box-sizing|box-shadow|box-decoration-break|bottom|border-width|border-top-width|border-top-style|border-top-right-radius|border-top-left-radius|border-top-color|border-top|border-style|border-spacing|border-right-width|border-right-style|border-right-color|border-right|border-radius|border-left-width|border-left-style|border-left-color|border-left|border-image-width|border-image-source|border-image-slice|border-image-repeat|border-image-outset|border-image|border-color|border-collapse|border-bottom-width|border-bottom-style|border-bottom-right-radius|border-bottom-left-radius|border-bottom-color|border-bottom|border|background-size|background-repeat|background-position|background-origin|background-image|background-color|background-clip|background-attachment|background-blend-mode|background|backface-visibility|auto|animation-timing-function|animation-play-state|animation-name|animation-iteration-count|animation-fill-mode|animation-duration|animation-direction|animation-delay|animation|align-self|align-items|align-content)\\b",i:"[^\\s]"},{b:"\\b(whitespace|wait|w-resize|visible|vertical-text|vertical-ideographic|uppercase|upper-roman|upper-alpha|underline|transparent|top|thin|thick|text|text-top|text-bottom|tb-rl|table-header-group|table-footer-group|sw-resize|super|strict|static|square|solid|small-caps|separate|se-resize|scroll|s-resize|rtl|row-resize|ridge|right|repeat|repeat-y|repeat-x|relative|progress|pointer|overline|outside|outset|oblique|nowrap|not-allowed|normal|none|nw-resize|no-repeat|no-drop|newspaper|ne-resize|n-resize|move|middle|medium|ltr|lr-tb|lowercase|lower-roman|lower-alpha|loose|list-item|line|line-through|line-edge|lighter|left|keep-all|justify|italic|inter-word|inter-ideograph|inside|inset|inline|inline-block|inherit|inactive|ideograph-space|ideograph-parenthesis|ideograph-numeric|ideograph-alpha|horizontal|hidden|help|hand|groove|fixed|ellipsis|e-resize|double|dotted|distribute|distribute-space|distribute-letter|distribute-all-lines|disc|disabled|default|decimal|dashed|crosshair|collapse|col-resize|circle|char|center|capitalize|break-word|break-all|bottom|both|bolder|bold|block|bidi-override|below|baseline|auto|always|all-scroll|absolute|table|table-cell)\\b"},{b:":",e:";",c:[n,i,e.CSSNM,e.QSM,e.ASM,{cN:"meta",b:"!important"}]},{b:"@(page|font-face)",l:t,k:"@page @font-face"},{b:"@",e:"[{;]",rB:!0,k:"and or not only",c:[{b:t,cN:"keyword"},n,e.QSM,e.ASM,i,e.CSSNM]}]}}),e.registerLanguage("shell",function(e){return{aliases:["console"],c:[{cN:"meta",b:"^\\s{0,3}[/\\w\\d\\[\\]()@-]*[>%$#]",starts:{e:"$",sL:"bash"}}]}}),e.registerLanguage("sql",function(e){var t=e.C("--","$");return{cI:!0,i:/[<>{}*]/,c:[{bK:"begin end start commit rollback savepoint lock alter create drop rename call delete do handler insert load replace select truncate update set show pragma grant merge describe use explain help declare prepare execute deallocate release unlock purge reset change stop analyze cache flush optimize repair kill install uninstall checksum restore check backup revoke comment values with",e:/;/,eW:!0,l:/[\w\.]+/,k:{keyword:"as abort abs absolute acc acce accep accept access accessed accessible account acos action activate add addtime admin administer advanced advise aes_decrypt aes_encrypt after agent aggregate ali alia alias all allocate allow alter always analyze ancillary and anti any anydata anydataset anyschema anytype apply archive archived archivelog are as asc ascii asin assembly assertion associate asynchronous at atan atn2 attr attri attrib attribu attribut attribute attributes audit authenticated authentication authid authors auto autoallocate autodblink autoextend automatic availability avg backup badfile basicfile before begin beginning benchmark between bfile bfile_base big bigfile bin binary_double binary_float binlog bit_and bit_count bit_length bit_or bit_xor bitmap blob_base block blocksize body both bound bucket buffer_cache buffer_pool build bulk by byte byteordermark bytes cache caching call calling cancel capacity cascade cascaded case cast catalog category ceil ceiling chain change changed char_base char_length character_length characters characterset charindex charset charsetform charsetid check checksum checksum_agg child choose chr chunk class cleanup clear client clob clob_base clone close cluster_id cluster_probability cluster_set clustering coalesce coercibility col collate collation collect colu colum column column_value columns columns_updated comment commit compact compatibility compiled complete composite_limit compound compress compute concat concat_ws concurrent confirm conn connec connect connect_by_iscycle connect_by_isleaf connect_by_root connect_time connection consider consistent constant constraint constraints constructor container content contents context contributors controlfile conv convert convert_tz corr corr_k corr_s corresponding corruption cos cost count count_big counted covar_pop covar_samp cpu_per_call cpu_per_session crc32 create creation critical cross cube cume_dist curdate current current_date current_time current_timestamp current_user cursor curtime customdatum cycle data database databases datafile datafiles datalength date_add date_cache date_format date_sub dateadd datediff datefromparts datename datepart datetime2fromparts day day_to_second dayname dayofmonth dayofweek dayofyear days db_role_change dbtimezone ddl deallocate declare decode decompose decrement decrypt deduplicate def defa defau defaul default defaults deferred defi defin define degrees delayed delegate delete delete_all delimited demand dense_rank depth dequeue des_decrypt des_encrypt des_key_file desc descr descri describ describe descriptor deterministic diagnostics difference dimension direct_load directory disable disable_all disallow disassociate discardfile disconnect diskgroup distinct distinctrow distribute distributed div do document domain dotnet double downgrade drop dumpfile duplicate duration each edition editionable editions element ellipsis else elsif elt empty enable enable_all enclosed encode encoding encrypt end end-exec endian enforced engine engines enqueue enterprise entityescaping eomonth error errors escaped evalname evaluate event eventdata events except exception exceptions exchange exclude excluding execu execut execute exempt exists exit exp expire explain explode export export_set extended extent external external_1 external_2 externally extract failed failed_login_attempts failover failure far fast feature_set feature_value fetch field fields file file_name_convert filesystem_like_logging final finish first first_value fixed flash_cache flashback floor flush following follows for forall force foreign form forma format found found_rows freelist freelists freepools fresh from from_base64 from_days ftp full function general generated get get_format get_lock getdate getutcdate global global_name globally go goto grant grants greatest group group_concat group_id grouping grouping_id groups gtid_subtract guarantee guard handler hash hashkeys having hea head headi headin heading heap help hex hierarchy high high_priority hosts hour hours http id ident_current ident_incr ident_seed identified identity idle_time if ifnull ignore iif ilike ilm immediate import in include including increment index indexes indexing indextype indicator indices inet6_aton inet6_ntoa inet_aton inet_ntoa infile initial initialized initially initrans inmemory inner innodb input insert install instance instantiable instr interface interleaved intersect into invalidate invisible is is_free_lock is_ipv4 is_ipv4_compat is_not is_not_null is_used_lock isdate isnull isolation iterate java join json json_exists keep keep_duplicates key keys kill language large last last_day last_insert_id last_value lateral lax lcase lead leading least leaves left len lenght length less level levels library like like2 like4 likec limit lines link list listagg little ln load load_file lob lobs local localtime localtimestamp locate locator lock locked log log10 log2 logfile logfiles logging logical logical_reads_per_call logoff logon logs long loop low low_priority lower lpad lrtrim ltrim main make_set makedate maketime managed management manual map mapping mask master master_pos_wait match matched materialized max maxextents maximize maxinstances maxlen maxlogfiles maxloghistory maxlogmembers maxsize maxtrans md5 measures median medium member memcompress memory merge microsecond mid migration min minextents minimum mining minus minute minutes minvalue missing mod mode model modification modify module monitoring month months mount move movement multiset mutex name name_const names nan national native natural nav nchar nclob nested never new newline next nextval no no_write_to_binlog noarchivelog noaudit nobadfile nocheck nocompress nocopy nocycle nodelay nodiscardfile noentityescaping noguarantee nokeep nologfile nomapping nomaxvalue nominimize nominvalue nomonitoring none noneditionable nonschema noorder nopr nopro noprom nopromp noprompt norely noresetlogs noreverse normal norowdependencies noschemacheck noswitch not nothing notice notnull notrim novalidate now nowait nth_value nullif nulls num numb numbe nvarchar nvarchar2 object ocicoll ocidate ocidatetime ociduration ociinterval ociloblocator ocinumber ociref ocirefcursor ocirowid ocistring ocitype oct octet_length of off offline offset oid oidindex old on online only opaque open operations operator optimal optimize option optionally or oracle oracle_date oradata ord ordaudio orddicom orddoc order ordimage ordinality ordvideo organization orlany orlvary out outer outfile outline output over overflow overriding package pad parallel parallel_enable parameters parent parse partial partition partitions pascal passing password password_grace_time password_lock_time password_reuse_max password_reuse_time password_verify_function patch path patindex pctincrease pctthreshold pctused pctversion percent percent_rank percentile_cont percentile_disc performance period period_add period_diff permanent physical pi pipe pipelined pivot pluggable plugin policy position post_transaction pow power pragma prebuilt precedes preceding precision prediction prediction_cost prediction_details prediction_probability prediction_set prepare present preserve prior priority private private_sga privileges procedural procedure procedure_analyze processlist profiles project prompt protection public publishingservername purge quarter query quick quiesce quota quotename radians raise rand range rank raw read reads readsize rebuild record records recover recovery recursive recycle redo reduced ref reference referenced references referencing refresh regexp_like register regr_avgx regr_avgy regr_count regr_intercept regr_r2 regr_slope regr_sxx regr_sxy reject rekey relational relative relaylog release release_lock relies_on relocate rely rem remainder rename repair repeat replace replicate replication required reset resetlogs resize resource respect restore restricted result result_cache resumable resume retention return returning returns reuse reverse revoke right rlike role roles rollback rolling rollup round row row_count rowdependencies rowid rownum rows rtrim rules safe salt sample save savepoint sb1 sb2 sb4 scan schema schemacheck scn scope scroll sdo_georaster sdo_topo_geometry search sec_to_time second seconds section securefile security seed segment select self semi sequence sequential serializable server servererror session session_user sessions_per_user set sets settings sha sha1 sha2 share shared shared_pool short show shrink shutdown si_averagecolor si_colorhistogram si_featurelist si_positionalcolor si_stillimage si_texture siblings sid sign sin size size_t sizes skip slave sleep smalldatetimefromparts smallfile snapshot some soname sort soundex source space sparse spfile split sql sql_big_result sql_buffer_result sql_cache sql_calc_found_rows sql_small_result sql_variant_property sqlcode sqldata sqlerror sqlname sqlstate sqrt square standalone standby start starting startup statement static statistics stats_binomial_test stats_crosstab stats_ks_test stats_mode stats_mw_test stats_one_way_anova stats_t_test_ stats_t_test_indep stats_t_test_one stats_t_test_paired stats_wsr_test status std stddev stddev_pop stddev_samp stdev stop storage store stored str str_to_date straight_join strcmp strict string struct stuff style subdate subpartition subpartitions substitutable substr substring subtime subtring_index subtype success sum suspend switch switchoffset switchover sync synchronous synonym sys sys_xmlagg sysasm sysaux sysdate sysdatetimeoffset sysdba sysoper system system_user sysutcdatetime table tables tablespace tablesample tan tdo template temporary terminated tertiary_weights test than then thread through tier ties time time_format time_zone timediff timefromparts timeout timestamp timestampadd timestampdiff timezone_abbr timezone_minute timezone_region to to_base64 to_date to_days to_seconds todatetimeoffset trace tracking transaction transactional translate translation treat trigger trigger_nestlevel triggers trim truncate try_cast try_convert try_parse type ub1 ub2 ub4 ucase unarchived unbounded uncompress under undo unhex unicode uniform uninstall union unique unix_timestamp unknown unlimited unlock unnest unpivot unrecoverable unsafe unsigned until untrusted unusable unused update updated upgrade upped upper upsert url urowid usable usage use use_stored_outlines user user_data user_resources users using utc_date utc_timestamp uuid uuid_short validate validate_password_strength validation valist value values var var_samp varcharc vari varia variab variabl variable variables variance varp varraw varrawc varray verify version versions view virtual visible void wait wallet warning warnings week weekday weekofyear wellformed when whene whenev wheneve whenever where while whitespace window with within without work wrapped xdb xml xmlagg xmlattributes xmlcast xmlcolattval xmlelement xmlexists xmlforest xmlindex xmlnamespaces xmlpi xmlquery xmlroot xmlschema xmlserialize xmltable xmltype xor year year_to_month years yearweek",literal:"true false null unknown",built_in:"array bigint binary bit blob bool boolean char character date dec decimal float int int8 integer interval number numeric real record serial serial8 smallint text time timestamp tinyint varchar varchar2 varying void"},c:[{cN:"string",b:"'",e:"'",c:[{b:"''"}]},{cN:"string",b:'"',e:'"',c:[{b:'""'}]},{cN:"string",b:"`",e:"`"},e.CNM,e.CBCM,t,e.HCM]},e.CBCM,t,e.HCM]}}),e.registerLanguage("swift",function(e){var t={keyword:"#available #colorLiteral #column #else #elseif #endif #file #fileLiteral #function #if #imageLiteral #line #selector #sourceLocation _ __COLUMN__ __FILE__ __FUNCTION__ __LINE__ Any as as! as? associatedtype associativity break case catch class continue convenience default defer deinit didSet do dynamic dynamicType else enum extension fallthrough false fileprivate final for func get guard if import in indirect infix init inout internal is lazy left let mutating nil none nonmutating open operator optional override postfix precedence prefix private protocol Protocol public repeat required rethrows return right self Self set static struct subscript super switch throw throws true try try! try? Type typealias unowned var weak where while willSet",literal:"true false nil",built_in:"abs advance alignof alignofValue anyGenerator assert assertionFailure bridgeFromObjectiveC bridgeFromObjectiveCUnconditional bridgeToObjectiveC bridgeToObjectiveCUnconditional c contains count countElements countLeadingZeros debugPrint debugPrintln distance dropFirst dropLast dump encodeBitsAsWords enumerate equal fatalError filter find getBridgedObjectiveCType getVaList indices insertionSort isBridgedToObjectiveC isBridgedVerbatimToObjectiveC isUniquelyReferenced isUniquelyReferencedNonObjC join lazy lexicographicalCompare map max maxElement min minElement numericCast overlaps partition posix precondition preconditionFailure print println quickSort readLine reduce reflect reinterpretCast reverse roundUpToAlignment sizeof sizeofValue sort split startsWith stride strideof strideofValue swap toString transcode underestimateCount unsafeAddressOf unsafeBitCast unsafeDowncast unsafeUnwrap unsafeReflect withExtendedLifetime withObjectAtPlusZero withUnsafePointer withUnsafePointerToObject withUnsafeMutablePointer withUnsafeMutablePointers withUnsafePointer withUnsafePointers withVaList zip"},n=e.C("/\\*","\\*/",{c:["self"]}),i={cN:"subst",b:/\\\(/,e:"\\)",k:t,c:[]},s={cN:"string",c:[e.BE,i],v:[{b:/"""/,e:/"""/},{b:/"/,e:/"/}]},r={cN:"number",b:"\\b([\\d_]+(\\.[\\deE_]+)?|0x[a-fA-F0-9_]+(\\.[a-fA-F0-9p_]+)?|0b[01_]+|0o[0-7_]+)\\b",relevance:0};return i.c=[r],{k:t,c:[s,e.CLCM,n,{cN:"type",b:"\\b[A-Z][\\wÀ-ʸ']*[!?]"},{cN:"type",b:"\\b[A-Z][\\wÀ-ʸ']*",relevance:0},r,{cN:"function",bK:"func",e:"{",eE:!0,c:[e.inherit(e.TM,{b:/[A-Za-z$_][0-9A-Za-z$_]*/}),{b:/</,e:/>/},{cN:"params",b:/\(/,e:/\)/,endsParent:!0,k:t,c:["self",r,s,e.CBCM,{b:":"}],i:/["']/}],i:/\[|%/},{cN:"class",bK:"struct protocol class extension enum",k:t,e:"\\{",eE:!0,c:[e.inherit(e.TM,{b:/[A-Za-z$_][\u00C0-\u02B80-9A-Za-z$_]*/})]},{cN:"meta",b:"(@discardableResult|@warn_unused_result|@exported|@lazy|@noescape|@NSCopying|@NSManaged|@objc|@objcMembers|@convention|@required|@noreturn|@IBAction|@IBDesignable|@IBInspectable|@IBOutlet|@infix|@prefix|@postfix|@autoclosure|@testable|@available|@nonobjc|@NSApplicationMain|@UIApplicationMain|@dynamicMemberLookup|@propertyWrapper)"},{bK:"import",e:/$/,c:[e.CLCM,n]}]}}),e.registerLanguage("typescript",function(e){var t="[A-Za-z$_][0-9A-Za-z$_]*",n={keyword:"in if for while finally var new function do return void else break catch instanceof with throw case default try this switch continue typeof delete let yield const class public private protected get set super static implements enum export import declare type namespace abstract as from extends async await",literal:"true false null undefined NaN Infinity",built_in:"eval isFinite isNaN parseFloat parseInt decodeURI decodeURIComponent encodeURI encodeURIComponent escape unescape Object Function Boolean Error EvalError InternalError RangeError ReferenceError StopIteration SyntaxError TypeError URIError Number Math Date String RegExp Array Float32Array Float64Array Int16Array Int32Array Int8Array Uint16Array Uint32Array Uint8Array Uint8ClampedArray ArrayBuffer DataView JSON Intl arguments require module console window document any number boolean string void Promise"},i={cN:"meta",b:"@"+t},s={b:"\\(",e:/\)/,k:n,c:["self",e.QSM,e.ASM,e.NM]},r={cN:"params",b:/\(/,e:/\)/,eB:!0,eE:!0,k:n,c:[e.CLCM,e.CBCM,i,s]},a={cN:"number",v:[{b:"\\b(0[bB][01]+)n?"},{b:"\\b(0[oO][0-7]+)n?"},{b:e.CNR+"n?"}],relevance:0},o={cN:"subst",b:"\\$\\{",e:"\\}",k:n,c:[]},l={b:"html`",e:"",starts:{e:"`",rE:!1,c:[e.BE,o],sL:"xml"}},c={b:"css`",e:"",starts:{e:"`",rE:!1,c:[e.BE,o],sL:"css"}},_={cN:"string",b:"`",e:"`",c:[e.BE,o]};return o.c=[e.ASM,e.QSM,l,c,_,a,e.RM],{aliases:["ts"],k:n,c:[{cN:"meta",b:/^\s*['"]use strict['"]/},e.ASM,e.QSM,l,c,_,e.CLCM,e.CBCM,a,{b:"("+e.RSR+"|\\b(case|return|throw)\\b)\\s*",k:"return throw case",c:[e.CLCM,e.CBCM,e.RM,{cN:"function",b:"(\\(.*?\\)|"+e.IR+")\\s*=>",rB:!0,e:"\\s*=>",c:[{cN:"params",v:[{b:e.IR},{b:/\(\s*\)/},{b:/\(/,e:/\)/,eB:!0,eE:!0,k:n,c:["self",e.CLCM,e.CBCM]}]}]}],relevance:0},{cN:"function",bK:"function",e:/[\{;]/,eE:!0,k:n,c:["self",e.inherit(e.TM,{b:t}),r],i:/%/,relevance:0},{bK:"constructor",e:/[\{;]/,eE:!0,c:["self",r]},{b:/module\./,k:{built_in:"module"},relevance:0},{bK:"module",e:/\{/,eE:!0},{bK:"interface",e:/\{/,eE:!0,k:"interface extends"},{b:/\$[(.]/},{b:"\\."+e.IR,relevance:0},i,s]}}),e.registerLanguage("yaml",function(e){var t="true false yes no null",n={cN:"string",relevance:0,v:[{b:/'/,e:/'/},{b:/"/,e:/"/},{b:/\S+/}],c:[e.BE,{cN:"template-variable",v:[{b:"{{",e:"}}"},{b:"%{",e:"}"}]}]};return{cI:!0,aliases:["yml","YAML","yaml"],c:[{cN:"attr",v:[{b:"\\w[\\w :\\/.-]*:(?=[ \t]|$)"},{b:'"\\w[\\w :\\/.-]*":(?=[ \t]|$)'},{b:"'\\w[\\w :\\/.-]*':(?=[ \t]|$)"}]},{cN:"meta",b:"^---s*$",relevance:10},{cN:"string",b:"[\\|>]([0-9]?[+-])?[ ]*\\n( *)[\\S ]+\\n(\\2[\\S ]+\\n?)*"},{b:"<%[%=-]?",e:"[%-]?%>",sL:"ruby",eB:!0,eE:!0,relevance:0},{cN:"type",b:"!"+e.UIR},{cN:"type",b:"!!"+e.UIR},{cN:"meta",b:"&"+e.UIR+"$"},{cN:"meta",b:"\\*"+e.UIR+"$"},{cN:"bullet",b:"\\-(?=[ ]|$)",relevance:0},e.HCM,{bK:t,k:{literal:t}},{cN:"number",b:e.CNR+"\\b"},n]}}),e}),self.onmessage=(e=>{const t=self.hljs.highlightAuto(e.data);postMessage(t.value)})};window.codebeautifyContentScript=(()=>{let e="";window._codebutifydetect_=(n=>{if(!document.getElementsByTagName("pre")[0])return;let i=document.getElementsByTagName("pre")[0].textContent;window.codebeautifyContentScriptCssInject?window.codebeautifyContentScriptCssInject():chrome.runtime.sendMessage({type:"fh-dynamic-any-thing",func:((e,t)=>{let n=e=>{chrome.tabs.insertCSS({code:e,runAt:"document_end"})},i=Awesome.getContentScript("code-beautify",!0);return"string"==typeof i?n(i):i instanceof Promise&&i.then(e=>n(e)),t&&t(),!0}).toString()}),$(document.body).addClass("show-tipsbar");let s=$('<div id="fehelper_tips"><span class="desc">FeHelper检测到这可能是<i>'+n+'</i>代码，<span class="ask">是否进行美化处理？</span></span><a class="encoding">有乱码？点击修正！</a><button class="yes">代码美化</button><button class="no">放弃！</button><button class="copy hide">复制美化过的代码</button><button class="close"><span></span></button><a class="forbid">彻底关闭这个功能！&gt;&gt;</a></div>').prependTo("body");s.find("button.yes").click(t=>{s.find("button.yes,button.no").hide();let r=s.find("span.ask").text("正在努力美化，请稍候...");((t,n,i)=>{let s=n=>{let s=document.getElementsByTagName("pre")[0];e=n,s.textContent=n,s.classList.add("language-"+t.toLowerCase());let r=new Worker(URL.createObjectURL(new Blob(["("+highlightWebWorker.toString()+")()"],{type:"text/javascript"})));r.onmessage=(e=>{s.innerHTML="<ol><li><span>"+e.data.replace(/\n/gm,"</span></li><li><span>")+"</span></li></ol>",i&&i()}),r.postMessage(n)};switch(t){case"javascript":s(js_beautify(n,{brace_style:"collapse",break_chained_methods:!1,indent_char:" ",indent_scripts:"keep",indent_size:"4",keep_array_indentation:!0,preserve_newlines:!0,space_after_anon_function:!0,space_before_conditional:!0,unescape_strings:!1,wrap_line_length:"120"}));break;case"css":css_beautify(n,{},e=>s(e))}})(n,i,()=>{r.text("已为您美化完毕！"),$(document.body).removeClass("show-tipsbar").addClass("show-beautified")})}),s.find("a.forbid").click(e=>{e.preventDefault(),confirm("一旦彻底关闭，不可恢复，请确认？")&&chrome.runtime.sendMessage({type:"fh-dynamic-any-thing",func:((e,t)=>{localStorage.setItem("JS_CSS_PAGE_BEAUTIFY",0)}).toString()},()=>{alert("已关闭，如果要恢复，请在FeHelper「设置页」重新安装「代码美化工具」！")})}),s.find("button.no,button.close").click(e=>{$(document.body).removeClass("show-tipsbar").removeClass("show-beautified"),s.remove()}),s.find("button.copy").click(n=>{t(e)}),s.find("a.encoding").click(e=>{e.preventDefault(),fetch(location.href).then(e=>e.text()).then(e=>{i=e,$(document.body).hasClass("show-beautified")?s.find("button.yes").trigger("click"):$("#fehelper_tips+pre").text(e)})})});let t=function(e){let t=document.createElement("textarea");t.style.position="fixed",t.style.opacity=0,t.value=e,document.body.appendChild(t),t.select(),document.execCommand("Copy"),document.body.removeChild(t),alert("代码复制成功，随处粘贴可用！")};return function(){chrome.runtime.sendMessage({type:"fh-dynamic-any-thing",params:{tabId:window.__FH_TAB_ID__||null},func:((e,t)=>(chrome.tabs.executeScript(e.tabId,{code:"("+(()=>{let e={js:"javascript",css:"css"}[location.pathname.substring(location.pathname.lastIndexOf(".")+1).toLowerCase()],t=document.contentType.toLowerCase();return e?"text/html"===t&&(e=void 0):/\/javascript$/.test(t)?e="javascript":/\/css$/.test(t)&&(e="css"),e}).toString()+")()"},function(t){["javascript","css"].includes(t[0])&&"0"!==localStorage.getItem("JS_CSS_PAGE_BEAUTIFY")&&chrome.tabs.executeScript(e.tabId,{code:`window._codebutifydetect_('${t[0]}')`})}),t&&t(),!0)).toString()})}})();