﻿using CommonLib;
using System;
using System.Collections.Generic;
using System.Collections.Specialized;
using System.Diagnostics;
using System.IO;
using System.Net;
using System.Text;

namespace HanZiOcr
{
    /// <summary>
    /// 出国翻译官小程序或APP
    /// 调试页面：https://westus.dev.cognitive.microsoft.com/docs/services/56f91f2d778daf23d8ec6739/operations/56f91f2e778daf14a499e1fc/console
    /// </summary>
    public class MicrosoftTransRec : BaseOcrRec
    {
        public MicrosoftTransRec()
        {
            OcrType = HanZiOcrType.微软;

            MaxExecPerTime = 15;

            LstJsonPreProcessArray = new List<object>() { "regions" };
            LstJsonNextProcessArray = new List<object>() { "lines", "words" };
            IsSupportVertical = true;
            StrResultJsonSpilt = "text";
            LstVerticalLocation = new List<object>() { "boundingBox" };
            IsSupportUrlOcr = true;
        }

        protected override string GetHtml(OcrContent content)
        {
            var byt = Convert.FromBase64String(content.strBase64);
            var result = PostFileResult(byt);
            return result;
        }

        private List<string> lstSubKey = new List<string>() { "6d63581ed6cd4a0c8ea582ecde242bc8" };

        public override string GetHtmlByUrl(OcrContent content)
        {
            var url = "https://westus.api.cognitive.microsoft.com/vision/v2.0/ocr?detectOrientation=true&language=unk";
            //var url = "https://api.projectoxford.ai/vision/v2.0/ocr?detectOrientation=true&language=unk";
            var strPost = "{\"url\":\"" + content.url + "\"}";
            var strTmp = WebClientSyncExt.GetHtml(url, "", strPost, "", ExecTimeOutSeconds, new NameValueCollection() { { "ocp-apim-subscription-key", lstSubKey.GetRndItem() } });

            return strTmp;
        }

        private string PostFileResult(byte[] content)
        {
            var result = "";
            try
            {
                var url = "https://westus.api.cognitive.microsoft.com/vision/v2.0/ocr?detectOrientation=true&language=unk";
                //var url = "https://api.projectoxford.ai/vision/v2.0/ocr?detectOrientation=true&language=unk";
                var file = new UploadFileInfo()
                {
                    Name = "uploadfile",
                    Filename = "test.png",
                    ContentType = "image/png",
                    Stream = new MemoryStream(content)
                };
                var header = new NameValueCollection() {
                    { "ocp-apim-subscription-key",lstSubKey.GetRndItem()}
                };
                result = PostFile(url, new[] { file }, null, header);
            }
            catch (Exception)
            {

            }
            return result;
        }

        /*
            "left": 597,
            "top": 162,
            "width": 248,
            "height": 248
         */
        protected override LocationInfo GetLocationByStr(string locationInfoStr)
        {
            locationInfoStr = locationInfoStr?
                      .Replace("left_top", "").Replace("right_bottom", "")
                      .Replace("[", "").Replace("]", "")
                      .Replace("{", "").Replace("}", "")
                      .Replace("\"", "").Replace(":", "")
                      .Replace("\r", "").Replace("\t", "").Replace("\n", "")
                      .Replace(" ", "").Trim();
            var spilt = locationInfoStr.Split(new string[] { "," }, StringSplitOptions.RemoveEmptyEntries);

            var location = new LocationInfo()
            {
                left = BoxUtil.GetInt32FromObject(spilt[0]),
                top = BoxUtil.GetInt32FromObject(spilt[1]),
                width = BoxUtil.GetInt32FromObject(spilt[2]),
                height = BoxUtil.GetInt32FromObject(spilt[3]),
            };

            return location;
        }

    }
}