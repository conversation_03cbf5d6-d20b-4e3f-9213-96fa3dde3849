﻿using System;
using System.Collections.Generic;

using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using ToolCommon;

namespace BugReprot
{
    public partial class Code : System.Web.UI.Page
    {
        protected void Page_Load(object sender, EventArgs e)
        {
            string strOp = BoxUtil.GetStringFromObject(Request.QueryString["op"]);
            if (!string.IsNullOrEmpty(strOp))
            {
                DoOperate(strOp);
            }
            Response.End();
        }

        private void DoOperate(string strOP)
        {
            CodeEntity code = new CodeEntity();
            code.StrAppCode = BoxUtil.GetStringFromObject(Request.QueryString["app"]);
            code.DtReg = BoxUtil.GetDateTimeFromObject(Request.QueryString["reg"]);
            if (strOP != "del")
            {
                code.StrType = BoxUtil.GetStringFromObject(Request.QueryString["type"]);
                code.DtExpire = BoxUtil.GetDateTimeFromObject(Request.QueryString["exp"]);
                code.NMaxLogin = BoxUtil.GetInt32FromObject(Request.QueryString["log"]);
                code.NMaxWindow = BoxUtil.GetInt32FromObject(Request.QueryString["win"]);
                if (code.IsEnable)
                {
                    switch (strOP)
                    {
                        case "add":
                            Add(code);
                            break;
                        default:
                            break;
                    }
                }
            }
            else
            {
                Del(code);
            }
        }

        private void Add(CodeEntity code)
        {
            if (CodeHelper.AddCode(code))
            {
                Response.Write("添加成功！");
            }
            else
            {
                Response.Write("添加失败！");
            }
            CodeHelper.lstTmpCache = CodeHelper.GetAllCodeEntity();
        }

        private void Del(CodeEntity code)
        {
            if (CodeHelper.DelByAppCode(code))
            {
                Response.Write("删除成功！");
            }
            else
            {
                Response.Write("删除失败！");
            }
            CodeHelper.lstTmpCache = CodeHelper.GetAllCodeEntity();
        }
    }
}