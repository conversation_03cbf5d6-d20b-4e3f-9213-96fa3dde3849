﻿<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <PackageId>ServiceStack.ProtoBuf</PackageId>
    <AssemblyName>ServiceStack.ProtoBuf</AssemblyName>
    <TargetFrameworks>net472;netstandard2.0;net6.0</TargetFrameworks>
    <Title>Protocol Buffers support for ServiceStack. Includes typed ProtoBuf Client</Title>
    <PackageDescription>
      Add the ProtoBuf binary format and endpoint to a ServiceStack web service host.
    </PackageDescription>
    <PackageTags>ProtoBuf;Fast;Binary;Serializer;Format;ContentType;REST;Web;Services;ServiceStack</PackageTags>
  </PropertyGroup>
  <ItemGroup>
    <ProjectReference Include="..\..\..\ServiceStack.Text\src\ServiceStack.Text\ServiceStack.Text.csproj" />
    <ProjectReference Include="..\ServiceStack.Interfaces\ServiceStack.Interfaces.csproj" />
    <ProjectReference Include="..\ServiceStack.Client\ServiceStack.Client.csproj" />
    <ProjectReference Include="..\ServiceStack.Common\ServiceStack.Common.csproj" />
    <ProjectReference Include="..\ServiceStack\ServiceStack.csproj" />
    <PackageReference Include="protobuf-net" Version="3.1.4" />
  </ItemGroup>
  <ItemGroup Condition=" '$(TargetFramework)' == 'net472' ">
    <Reference Include="System" />
    <Reference Include="Microsoft.CSharp" />
  </ItemGroup>
</Project>