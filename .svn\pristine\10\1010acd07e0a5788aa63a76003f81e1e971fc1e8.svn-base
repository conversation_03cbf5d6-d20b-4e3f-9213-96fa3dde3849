﻿using CommonLib;
using Notice.Process.Common;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Configuration;
using System.Linq;
using System.Threading.Tasks;
using System.Web;

namespace Account.Web
{
    public class MsgProcessHelper
    {
        private static readonly BlockingCollection<NoticeQueueEntity> NoticePool = new BlockingCollection<NoticeQueueEntity>();
        public static void Init()
        {
            Task.Factory.StartNew(() =>
            {
                foreach (var msg in NoticePool.GetConsumingEnumerable())
                {
                    try
                    {
                        if (msg != null)
                        {
                            Task.Factory.StartNew(() =>
                            {
                                ProcessSingleMsg(msg);
                            });
                        }
                    }
                    catch (Exception oe)
                    {
                        LogHelper.Log.Error("处理消息时出错,错误原因如下:", oe);
                    }
                }
            });
        }

        public static void SendMsg(NoticeQueueEntity notice)
        {
            NoticePool.Add(notice);
        }

        private static Dictionary<string, Dictionary<string, int>> DicRegCount = new Dictionary<string, Dictionary<string, int>>();

        private static bool IsCanSendMsg(string account, string type)
        {
            var date = ServerTime.DateTime.ToString("yyyy-MM-dd");
            if (!DicRegCount.ContainsKey(date))
            {
                DicRegCount.Clear();
                DicRegCount.Add(date, new Dictionary<string, int>());
            }
            var key = string.Format("{0}-{1}", type, account);
            if (!DicRegCount[date].ContainsKey(key))
            {
                DicRegCount[date].Add(key, 0);
            }
            var result = DicRegCount[date][key] < 5;
            if (result)
            {
                DicRegCount[date][key] = DicRegCount[date][key] + 1;
            }
            return result;
        }

        private static bool ProcessSingleMsg(NoticeQueueEntity msg)
        {
            var result = false;
            switch (msg.NoticeType)
            {
                case NoticeType.邮件:
                    if (msg.Body.Length < 6)
                        msg.Body = string.Format("您的验证码为：{0}，有效期1小时！", msg.Body);
                    if (!msg.Subject.StartsWith("【"))
                    {
                        msg.Subject = string.Format("【{0}】", ShanSuSms.signCode) + msg.Subject;
                    }
                    if (!msg.Body.StartsWith("【"))
                    {
                        msg.Body = string.Format("【{0}】", ShanSuSms.signCode) + msg.Body;
                    }
                    if (msg.Body.Contains("验证码") && !msg.Body.Contains("请忽略"))
                    {
                        msg.Body += "如非本人操作，请忽略此邮件！";
                    }
                    if (!IsCanSendMsg(msg.To, "邮件"))
                    {
                        LogHelper.Log.Info(string.Format("[邮件次数超限]{0}", msg.ToString()));
                    }
                    else
                        result = MailService.SendMail(msg);
                    break;
                //case NoticeType.电话:
                //    //result = FetionService.SendVoice(msg.MobileNo);
                //    if (!result)
                //    {
                //        msg.NoticeType = NoticeType.邮件;
                //        return ProcessSingleMsg(msg);
                //    }
                //    break;
                case NoticeType.短信:
                    if (!IsCanSendMsg(msg.MobileNo, "短信"))
                    {
                        LogHelper.Log.Info(string.Format("[短信次数超限]{0}", msg.ToString()));
                    }
                    else
                    {
                        var isValidateCode = msg.Body.Length < 8 || msg.Body.Contains("验证码");
                        try
                        {
                            if (!result && isValidateCode)
                                result = ShanSuSms.SendValidateCodeMSG(msg.MobileNo, msg.Body);
                        }
                        catch { }
                        try
                        {
                            if (!result && isValidateCode)
                                result = YouLanSms.SendValidateCodeMSG(msg.MobileNo, msg.Body);
                        }
                        catch { }
                    }
                    //result = FetionService.SendVoice(msg.MobileNo);
                    if (!result)
                    {
                        msg.NoticeType = NoticeType.邮件;
                        return ProcessSingleMsg(msg);
                    }
                    break;
                //case NoticeType.QQ:
                //    //QQHelper.SendQQMsg(msg.QQ, msg.Body);
                //    break;
                //case NoticeType.其他:
                //    break;
                default:
                    break;
            }

            LogHelper.Log.Info(string.Format("[{1}]{0}", msg.ToString(), result ? "成功" : "失败"));
            return result;
        }
    }
    public class MailService
    {
        private static SendMail SendClient = new SendMail();
        public static bool SendMail(NoticeQueueEntity queue)
        {
            var isMobile = string.IsNullOrEmpty(queue.To) && !string.IsNullOrEmpty(queue.MobileNo);
            if (isMobile)
            {
                queue.To = queue.MobileNo + "@189.cn";
            }
            bool result = SendClient.Send(queue);
            if (isMobile)
            {
                queue.To = queue.MobileNo + "@139.com";
                result = SendClient.Send(queue);
            }
            return result;
        }
    }
    public class SendMail
    {
        public bool Send(NoticeQueueEntity queue)
        {
            var result = false;
            try
            {
                var account = new MailAccount();
                bool isSelf = queue.IsSendBySelf;
                while (!result && account != null)
                {
                    if (isSelf)
                    {
                        account = new MailAccount()
                        {
                            Account = queue.Email,
                            Password = queue.Password,
                        };
                        isSelf = false;
                    }
                    else
                    {
                        account = MailConfig.GetMailAccount();
                    }
                    if (account != null)
                    {
                        result = SendMessage(queue, account);
                        if (!result)
                        {
                            account.ReportError();
                        }
                        else
                        {
                            queue.SendBy = account.Account;
                        }
                    }
                }
            }
            catch (Exception oe)
            {
                ConfigHelper._Log.Error("Send出错", oe);
            }
            //LogHelper.Log.Info(string.Format("[{1}]{0}", queue.ToString(), result ? "成功" : "失败"));
            return result;
        }

        /// <summary>
        /// 同步发送邮件
        /// </summary>
        /// <param name="isSimple">是否只发送一条</param>
        /// <param name="autoReleaseSmtp">是否自动释放SmtpClient</param>
        /// <param name="isReuse">是否重用SmtpClient</param>
        private bool SendMessage(NoticeQueueEntity mailEntity, MailAccount account)
        {
            var result = false;
            MailHelper mail = new MailHelper(false);
            try
            {
                mail.SetSmtpClient(new SmtpHelper(account.Account, account.Password, true).SmtpClient, true);
                mail.From = account.Account;// Config.TestFromAddress;
                mail.FromDisplayName = MailConfig.TestFromNickName;// Config.GetAddressName(Config.TestFromAddress);

                if (mailEntity.To.Contains(";"))
                {
                    var mails = mailEntity.To.Split(new string[] { ";" }, StringSplitOptions.RemoveEmptyEntries);
                    mail.AddReceive(EmailAddrType.To, mails[0], MailConfig.TestToNickName);
                    for (int i = 1; i < mails.Length; i++)
                    {
                        mail.AddReceive(EmailAddrType.CC, mails[i], MailConfig.TestToNickName);
                    }
                }
                else
                {
                    mail.AddReceive(EmailAddrType.To, mailEntity.To, MailConfig.TestToNickName);
                }

                mail.Subject = mailEntity.Subject ?? "助手提醒";
                // Guid.NewGuid() 防止重复内容，被SMTP服务器拒绝接收邮件
                mail.Body = mailEntity.Body;
                mail.IsBodyHtml = true;

                Dictionary<MailInfoType, string> dic = mail.CheckSendMail();
                if (dic.Count > 0 && MailInfoHelper.ExistsError(dic))
                {
                    // 反馈“错误+提示”信息
                    ConfigHelper._Log.Info(MailInfoHelper.GetMailInfoStr(dic));
                }
                else
                {
                    string msg = string.Empty;
                    if (dic.Count > 0)
                    {
                        // 反馈“提示”信息
                        msg = MailInfoHelper.GetMailInfoStr(dic);
                    }

                    try
                    {
                        mail.SendOneMail();
                        result = true;
                    }
                    catch (Exception ex)
                    {
                        // 反馈异常信息
                        msg = msg + (ex.InnerException == null ? ex.Message : ex.Message + ex.InnerException.Message) + Environment.NewLine;
                        ConfigHelper._Log.Error(msg, ex);
                    }
                    finally
                    {
                        // 输出到界面
                        if (msg.Length > 0)
                            ConfigHelper._Log.Info(msg);
                    }
                }
            }
            catch (Exception oe)
            {
                ConfigHelper._Log.Error("SendMessage出错", oe);
            }
            finally
            {
                mail.Reset();
            }

            return result;
        }
    }
    public static class MailConfig
    {
        public static string TestFromNickName = "OCR文字识别助手";
        public static string TestToNickName = "助手客户";

        /// <summary>
        /// 测试收件地址
        /// </summary>
        public static string TestToAddress = "";

        private static List<MailAccount> lstMailAccount;

        public static List<MailAccount> LstMailAccount
        {
            get
            {
                if (lstMailAccount == null || lstMailAccount.Count <= 0)
                {
                    InitMailAccounts();
                }
                return MailConfig.lstMailAccount.OrderBy(p => p.NErrorCount).ToList();
            }
            set { MailConfig.lstMailAccount = value.ToList(); }
        }

        public static MailAccount GetMailAccount()
        {
            return LstMailAccount?.GetRndItem();
        }

        public static void InitMailAccounts()
        {
            try
            {
                lstMailAccount = new List<MailAccount>();
                foreach (var item in StrEamil.Split(new string[] { ";" }, StringSplitOptions.RemoveEmptyEntries))
                {
                    lstMailAccount.Add(new MailAccount()
                    {
                        Account = item,
                        Password = StrEamilPWD,
                    });
                }
            }
            catch (Exception oe)
            {
                ConfigHelper._Log.Error("初始化Email账户出错", oe);
            }
        }
        private static string strEamil = "";

        public static string StrEamil
        {
            get
            {
                if (string.IsNullOrEmpty(strEamil))
                {
                    try
                    {
                        strEamil = ConfigurationManager.AppSettings.Get("strEmailAccount");
                    }
                    catch (Exception oe)
                    {
                        strEamil = "<EMAIL>";
                    }
                }
                return strEamil;
            }
            set { strEamil = value; }
        }

        private static string strEamilPWD = "";

        public static string StrEamilPWD
        {
            get
            {
                if (string.IsNullOrEmpty(strEamilPWD))
                {
                    try
                    {
                        strEamilPWD = ConfigurationManager.AppSettings.Get("strEmailPWD");
                    }
                    catch (Exception oe)
                    {
                        strEamilPWD = "Aa@qpzs";
                    }
                }
                return strEamilPWD;
            }
            set { strEamilPWD = value; }
        }

    }

    public class MailAccount
    {
        public string Account { get; set; }

        public string Password { get; set; }

        //public bool IsSSL { get; set; }

        //public int Port { get; set; }

        //public string Smtp { get; set; }

        public long NErrorCount { get; set; }

        public void ReportError()
        {
            NErrorCount++;
        }
    }
}