﻿using System;
using System.Collections.Generic;
using System.Net;
using NewTicket.Tcp.Server;
using NewTicket;
using NewTicket.Core;
using System.Threading.Tasks;
using System.Threading;
using System.Linq;
using System.Collections.Concurrent;

namespace DhcpService
{
    public class TicketService
    {
        public List<TicketShareEntity> lstTicket;
        private ITcpServerEngine tcpServerEngine;
        private List<ClientEntity> lstClient;
        private int NExpired = 4;

        public void Connect(int prot)
        {
            try
            {
                if (tcpServerEngine == null)
                {
                    //初始化并启动服务端引擎（TCP、文本协议）
                    tcpServerEngine = NetworkEngineFactory.CreateTextTcpServerEngine(prot, new DefaultTextContractHelper("\0"));//DefaultTextContractHelper是StriveEngine内置的ITextContractHelper实现。使用UTF-8对EndToken进行编码。 
                    tcpServerEngine.ClientCountChanged += tcpServerEngine_ClientCountChanged;
                    tcpServerEngine.ClientConnected += tcpServerEngine_ClientConnected;
                    tcpServerEngine.ClientDisconnected += tcpServerEngine_ClientDisconnected;
                    tcpServerEngine.MessageReceived += tcpServerEngine_MessageReceived;
                    tcpServerEngine.MaxClientCount = int.MaxValue;
                }
                tcpServerEngine.Initialize();
                if (lstClient == null)
                {
                    lstClient = new List<ClientEntity>();
                }
                if (lstTicket == null)
                {
                    lstTicket = new List<TicketShareEntity>();
                }
            }
            catch (Exception oe)
            {
                //Console.WriteLine(oe);
                //MessageBox.Show(ee.Message);
            }
        }

        public void Close()
        {
            try
            {
                if (tcpServerEngine != null)
                {
                    tcpServerEngine.ChangeListenerState(false);
                    tcpServerEngine.CloseAllClient();
                    tcpServerEngine.DisposeAsyn();
                    tcpServerEngine.Dispose();
                    tcpServerEngine = null;
                    lstClient = null;
                    lstTicket = null;
                }
            }
            catch (Exception oe)
            {
                Console.WriteLine(oe.Message);
            }
        }

        void tcpServerEngine_MessageReceived(IPEndPoint client, byte[] bMsg)
        {
            try
            {
                if (bMsg != null && bMsg.Length > 0)
                {
                    string msg = System.Text.Encoding.UTF8.GetString(bMsg); //消息使用UTF-8编码
                    msg = string.IsNullOrEmpty(msg) ? "" : msg;
                    if (!string.IsNullOrEmpty(msg))
                    {
                        msg = msg.Substring(0, msg.Length - 1); //将结束标记"\0"剔除
                        RecivedMsg(client, msg);
                    }
                    else
                    {
                        Console.WriteLine("MessageReceived异常,接收消息长度为：" + msg.Length);
                    }
                }
            }
            catch (Exception oe)
            {
                Console.WriteLine(oe);
            }
        }

        void tcpServerEngine_ClientDisconnected(IPEndPoint ipe)
        {
            ShowOnLineEvent(ipe, false);
        }

        void tcpServerEngine_ClientConnected(IPEndPoint ipe)
        {
            ShowOnLineEvent(ipe, true);
        }

        /// <summary>
        /// 上下线消息
        /// </summary>
        /// <param name="ip"></param>
        /// <param name="isOn"></param>
        private void ShowOnLineEvent(IPEndPoint ip, bool isOn = true)
        {
            try
            {
                if (lstClient == null)
                {
                    lstClient = new List<ClientEntity>();
                }
                if (ip != null)
                {
                    if (isOn)
                    {
                        if (!lstClient.Exists(p => p != null && p.IP == ip))
                        {
                            lstClient.Add(new ClientEntity() { IP = ip, LastDate = DateTime.Now });
                        }
                    }
                    else
                    {
                        lstClient.FindAll(p => p != null && p.IsValidate && p.IP == ip).ForEach(p =>
                        {
                            p.SetOffline();
                        });
                    }
                }
            }
            catch (Exception oe)
            {
                Console.WriteLine(oe);
            }
        }

        void tcpServerEngine_ClientCountChanged(int count)
        {
            if (lstClient == null)
            {
                lstClient = new List<ClientEntity>();
            }
            if (lstClient.Count != count)
            {
                ResetConnects();
            }
        }

        private void ResetConnects()
        {
            try
            {
                var list = tcpServerEngine.GetClientList();

                lock (lstClient)
                {
                    //增加所有连接中不包含的客户端
                    foreach (var item in list)
                    {
                        if (!lstClient.Exists(p => p != null && item == p.IP))
                        {
                            ShowOnLineEvent(item, true);
                        }
                    }
                }
                //移除当前连接中不包含的客户端
                RemoveBadClient();
            }
            catch (Exception oe)
            {
                Console.WriteLine(oe);
            }
        }

        private void RemoveBadClient(string strIdentity = "")
        {
            lock (lstTicket)
            {
                if (string.IsNullOrEmpty(strIdentity))
                {
                    lstClient.RemoveAll(p => p == null || (!p.IsValidate && (string.IsNullOrEmpty(p.StrIdentity) || p.DtRemove.Value >= DateTime.Now)));
                }
                else
                {
                    lstClient.RemoveAll(p => p == null || (!p.IsValidate && (string.IsNullOrEmpty(p.StrIdentity) || p.DtRemove.Value >= DateTime.Now))
                        || (!p.IsValidate && !string.IsNullOrEmpty(p.StrIdentity) && p.StrIdentity.Equals(strIdentity)));
                }
            }
        }

        public ConcurrentDictionary<int, int> dicReviceMSG = new ConcurrentDictionary<int, int>();
        public ConcurrentDictionary<int, int> dicSendMSG = new ConcurrentDictionary<int, int>();
        public ConcurrentDictionary<int, int> dicReviceTicketMSG = new ConcurrentDictionary<int, int>();

        private void RecivedMsg(IPEndPoint client, string msg)
        {
            try
            {
                if (string.IsNullOrEmpty(msg) || msg.Contains("??"))
                {
                    return;
                }
                try
                {
                    if (!msg.StartsWith("help"))
                    {
                        if (dicReviceMSG.ContainsKey(DateTime.Now.Hour))
                        {
                            dicReviceMSG[DateTime.Now.Hour] = dicReviceMSG[DateTime.Now.Hour] + 1;
                        }
                        else
                        {
                            dicReviceMSG[DateTime.Now.Hour] = 1;
                        }
                    }
                }
                catch (Exception oe)
                {
                    Console.WriteLine("统计信息出错！" + oe.Message);
                }
                //ReciveMSG
                //help|[2015-02-05_BJP_SHH_G43_ADULT_硬卧],[2015-02-05_BJP_SHH_G43_00_硬卧]
                if (msg.StartsWith("help"))
                {
                    #region 请求查票

                    //help|24673305D384831A9ADF0AB00ABB49DD|2015-02-24 23:35:17|[2015-04-27_SHH_BJP_G104_ADULT_二等座]
                    //先判断是否已经在注册账号列表，如果不存在，该消息不群发
                    string strMachine = CommonHelper.SubString(msg, "|", "|");
                    var entity = lstClient.Find(p => p.IP == client && p.IsValidate);
                    if (entity == null)
                    {
                        ShowOnLineEvent(client, true);
                        entity = lstClient.Find(p => p.IP == client && p.IsValidate);
                    }
                    if (entity != null && !string.IsNullOrEmpty(strMachine))// && CodeHelper.IsExitsCode(strMachine)
                    {
                        try
                        {
                            lstClient.ForEach(delegate(ClientEntity tic)
                            {
                                if (tic.IsValidate && tic.IP.Address.Equals(client.Address))
                                {
                                    tic.Machine = strMachine;
                                    tic.LstTicketKey.RemoveAll(q => q.DtExpired <= DateTime.Now);
                                }
                            });
                        }
                        catch { }

                        string strOldMSG = msg;
                        bool isSendToAll = false;
                        try
                        {
                        }
                        catch (Exception oe)
                        {
                            Console.WriteLine(oe);
                        }

                        if (isSendToAll)
                        {
                            SendToAll(strOldMSG);
                        }
                    }
                    #endregion
                }
                else if (msg.StartsWith("msg"))
                {
                    SendToAll(msg);
                }
                else if (msg.StartsWith("report"))
                {
                    Report();
                }
                else if (msg.StartsWith("ticket"))
                {
                    #region 共享票相关
                    //ticket|[2015-02-16_BJP_SHH_G105_00_二等座]
                    //,[00_P3_G105_240000G1050C_VNP_AOH_O055300457M0933000009174800001_MjAxNTAyMTYjMDAjRzEwNSMwNTo0NSMwNzozNiMyNDAwMDBHMTA1MEMjVk5QI0FPSCMxMzoyMSPljJfkuqzljZcj5LiK5rW36Jm55qGlIzAxIzEwI08wNTUzMDA0NTdNMDkzMzAwMDAwOTE3NDgwMDAwMSNQMyMxNDE4OTY4NDQzNjI2IzdCQUVBMEIyRDE4MDA2NjM0NjZCQkM2NzAxQzEzOEYyNDFGM0Y1NzZDQzE2OTZENjQxOEUwNDFE],1,13:54:12 724
                    try
                    {
                        msg = msg.Substring(msg.IndexOf("|") + 1);
                        //[查票Key1]|[ticket]

                        TicketShareEntity tic = new TicketShareEntity();
                        if (msg.Contains(","))
                        {
                            tic.strKey = msg.Substring(0, msg.IndexOf(","));
                            tic.strSubCode = msg.Substring(msg.IndexOf(",") + 1);
                            if (!string.IsNullOrEmpty(tic.strKey) && !string.IsNullOrEmpty(tic.strSubCode))
                            {
                                AddTicketInfo(tic);
                            }
                        }
                        #region 发送查票信息

                        if (tic != null && !string.IsNullOrEmpty(tic.strKey) && !string.IsNullOrEmpty(tic.strSubCode))
                        {
                            SendTicketInfo(tic, client);
                        }
                        try
                        {
                            if (dicReviceTicketMSG.ContainsKey(DateTime.Now.Hour))
                            {
                                dicReviceTicketMSG[DateTime.Now.Hour] = dicReviceTicketMSG[DateTime.Now.Hour] + 1;
                            }
                            else
                            {
                                dicReviceTicketMSG[DateTime.Now.Hour] = 1;
                            }
                        }
                        catch (Exception oe)
                        {
                            Console.WriteLine("统计查票信息出错！" + oe.Message);
                        }
                        #endregion
                    }
                    catch (Exception oe)
                    {
                        Console.WriteLine(oe);
                    }
                    #endregion
                }
                else if (msg.StartsWith("id"))
                {
                    #region 重新上线相关
                    msg = msg.Substring(msg.IndexOf("|") + 1).Trim();

                    if (string.IsNullOrEmpty(msg))
                        return;
                    ClientEntity tmpClient = null;
                    for (int i = 0; i < 3; i++)
                    {
                        if (tmpClient != null)
                            break;
                        try
                        {
                            //id|guid
                            if (!lstClient.Exists(p => p != null && p.IsValidate && p.IP == client))
                            {
                                ShowOnLineEvent(client);
                            }
                            tmpClient = lstClient.FirstOrDefault(p => p != null && p.IsValidate && p.IP == client);
                        }
                        catch (Exception oe)
                        {
                            //Console.WriteLine(oe, "转换TicketKey_1出错");
                        }
                        if (tmpClient == null)
                        {
                            System.Threading.Thread.Sleep(100);
                        }
                    }
                    if (tmpClient != null)
                    {
                        if (tmpClient.LstTicketKey == null)
                            tmpClient.LstTicketKey = new List<TicketQueryEntity>();
                        var lstTmp = new List<ClientEntity>();
                        try
                        {
                            lock (lstClient)
                            {
                                lstTmp = lstClient.FindAll(p => p != null && !string.IsNullOrEmpty(p.StrIdentity) && p.StrIdentity.Equals(msg));
                            }
                        }
                        catch (Exception oe)
                        {
                            Console.WriteLine(oe);
                        }
                        try
                        {
                            foreach (var item in lstTmp)
                            {
                                if (item.LstTicketKey != null)
                                    tmpClient.LstTicketKey.AddRange(item.LstTicketKey);
                            }
                            lstTmp.ForEach(p =>
                            {
                                p.SetOffline();
                            });
                        }
                        catch (Exception oe)
                        {
                            Console.WriteLine(oe);
                        }
                        try
                        {
                            tmpClient.LstTicketKey = tmpClient.LstTicketKey.Distinct().ToList();
                            tmpClient.StrIdentity = msg;
                            //tmpClient.LstTicketKey = lstClient.FirstOrDefault(p => p != null && !p.IsValidate && !string.IsNullOrEmpty(p.StrIdentity) && p.StrIdentity.Equals(msg)).LstTicketKey;
                            RemoveBadClient(tmpClient.StrIdentity);
                        }
                        catch (Exception oe)
                        {
                            Console.WriteLine(oe);
                        }
                    }
                    else
                    {
                        Console.WriteLine("重现上线未找到之前客户端，Key：" + msg);
                    }
                    #endregion
                }
                else if (msg.StartsWith("info"))
                {
                    //string strOldMSG = "";
                    //strOldMSG += msg;
                    #region 共享票相关
                    //info|E160A6C47F28A2C4DCD92144A073FD28|1|[2015-02-16_BJP_SHH_G105_00_二等座]||
                    msg = msg.Substring(msg.IndexOf("|") + 1);
                    string strMachine = msg.Substring(0, msg.IndexOf("|"));
                    var entity = lstClient.Find(p => p.IP == client && p.IsValidate);
                    if (entity == null)
                    {
                        ShowOnLineEvent(client, true);
                        entity = lstClient.Find(p => p.IP == client && p.IsValidate);
                    }
                    if (entity == null)
                    {
                        return;
                    }
                    entity.LstTicketKey = new List<TicketQueryEntity>();
                    #endregion
                }
                else
                {
                    #region Old Operate

                    //string strOldMSG = "";
                    //strOldMSG += msg;
                    //if (msg.Contains("|"))
                    //{
                    //    #region 共享票相关
                    //    string strMachine = msg.Substring(0, msg.IndexOf("|"));
                    //    var entity = lstClient.Find(p => p.IP == client);
                    //    if (entity == null)
                    //    {
                    //        ShowOnLineEvent(client, true);
                    //        entity = lstClient.Find(p => p.IP == client);
                    //    }
                    //    if (entity == null)
                    //    {
                    //        return;
                    //    }
                    //    if (string.IsNullOrEmpty(entity.Machine) && !string.IsNullOrEmpty(strMachine) && entity.Machine != strMachine)
                    //    {
                    //        entity.Machine = strMachine;
                    //    }
                    //    try
                    //    {
                    //        msg = msg.Substring(msg.IndexOf("|") + 1);
                    //        //AppKey|是否正在抢票|[查票Key1],[查票Key2]|[查票Key1],[ticket]|
                    //        bool isQiangIng = msg.Substring(0, msg.IndexOf("|")).Equals("1");
                    //        if (!isQiangIng)
                    //        {
                    //            entity.LstTicketKey = null;
                    //        }
                    //        else
                    //        {
                    //            entity.LstTicketKey = new List<string>();
                    //            //[查票Key1],[查票Key2]|[查票Key1],[ticket]|
                    //            msg = msg.Substring(msg.IndexOf("|") + 1);
                    //            entity.LstTicketKey.AddRange(msg.Substring(0, msg.IndexOf("|")).Split(new string[] { "," }, StringSplitOptions.RemoveEmptyEntries));
                    //            //[查票Key1],[ticket]|
                    //            msg = msg.Substring(msg.IndexOf("|") + 1);
                    //            TicketEntityInfo tic = new TicketEntityInfo();
                    //            bool isForce = false;
                    //            if (msg.Contains(","))
                    //            {
                    //                #region 共享查票信息

                    //                tic.strKey = msg.Substring(0, msg.IndexOf(","));
                    //                msg = msg.Substring(msg.IndexOf(",") + ",".Length);
                    //                tic.strSubCode = msg.Substring(0, msg.IndexOf("|"));
                    //                if (!string.IsNullOrEmpty(tic.strKey) && !string.IsNullOrEmpty(tic.strSubCode))
                    //                {
                    //                    tic.strMachine = entity.Machine;
                    //                    AddTicketInfo(tic);
                    //                }
                    //                #endregion
                    //            }
                    //            else
                    //            {
                    //                #region 请求查票信息

                    //                if (entity.LstTicketKey != null && entity.LstTicketKey.Count > 0)
                    //                {
                    //                    tic = GetTicketInfo(entity.LstTicketKey);
                    //                    isForce = true;
                    //                }
                    //                #endregion
                    //            }
                    //            #region 发送查票信息

                    //            if (tic != null && !string.IsNullOrEmpty(tic.strKey) && !string.IsNullOrEmpty(tic.strSubCode))
                    //            {
                    //                SendTicketInfo(tic, client, isForce);
                    //                try
                    //                {
                    //                    if (dicReviceTicketMSG.ContainsKey(DateTime.Now.Hour.ToString()))
                    //                    {
                    //                        dicReviceTicketMSG[DateTime.Now.Hour.ToString()] = dicReviceTicketMSG[DateTime.Now.Hour.ToString()] + 1;
                    //                    }
                    //                    else
                    //                    {
                    //                        dicReviceTicketMSG[DateTime.Now.Hour.ToString()] = 1;
                    //                    }
                    //                }
                    //                catch (Exception oe)
                    //                {
                    //                    Console.WriteLine("统计查票信息出错！" + oe.Message);
                    //                }
                    //            }
                    //            #endregion
                    //        }
                    //    }
                    //    catch (Exception oe)
                    //    {
                    //        Console.WriteLine(oe, "接收查票信息异常" + strOldMSG);
                    //    }
                    //    lstClient.ForEach(delegate(TicketClientEntity tic)
                    //    {
                    //        if (tic.IP == client)
                    //            tic = entity;
                    //    });
                    //    #endregion
                    //}
                    //else
                    //{
                    //    Console.WriteLine(msg);
                    //}

                    #endregion
                }
            }
            catch (Exception oe)
            {
                //SendMSG(client, "服务器异常！");
                Console.WriteLine(oe);
            }
        }

        private void AddTicketInfo(TicketShareEntity tic)
        {
            RemoveBadTicket(tic != null ? tic.strKey : "");
            try
            {
                tic.DtAdd = DateTime.Now;
                tic.DtExpired = DateTime.Now.AddMinutes(NExpired);
                lock (lstTicket)
                {
                    lstTicket.Add(tic);
                }
            }
            catch (Exception oe)
            {
                Console.WriteLine(oe);
            }
        }

        private void RemoveBadTicket(string strKey = "")
        {
            if (lstTicket == null)
            {
                lstTicket = new List<TicketShareEntity>();
            }
            try
            {
                lstTicket.RemoveAll(p => p == null || string.IsNullOrEmpty(p.strKey) || string.IsNullOrEmpty(p.strSubCode)
                    || p.DtExpired <= DateTime.Now || (!string.IsNullOrEmpty(strKey) && p.strKey.Equals(strKey)));
            }
            catch (Exception oe)
            {
                Console.WriteLine(oe);
            }
        }

        private TicketShareEntity GetTicketInfo(List<TicketQueryEntity> lstAllKey)
        {
            if (lstAllKey != null && lstAllKey.Count > 0)
            {
                RemoveBadTicket();
                try
                {
                    lock (lstTicket)
                    {
                        return lstTicket.FirstOrDefault(p => p != null && !string.IsNullOrEmpty(p.strKey) && lstAllKey.Exists(q => q.StrKey.Equals(p.strKey)));
                    }
                }
                catch (Exception oe)
                {
                    Console.WriteLine(oe);
                }
            }
            return null;
        }

        private void SendMSGCount()
        {
            try
            {
                if (dicSendMSG.ContainsKey(DateTime.Now.Hour))
                {
                    dicSendMSG[DateTime.Now.Hour] = dicSendMSG[DateTime.Now.Hour] + 1;
                }
                else
                {
                    dicSendMSG[DateTime.Now.Hour] = 1;
                }
            }
            catch (Exception oe)
            {
                Console.WriteLine("统计发送信息出错！" + oe.Message);
            }
        }

        public void Report()
        {
            int nCount = 0;
            string strMSG = "";
            string strAllMSG = "当前在线人数" + lstClient.Count + Environment.NewLine;
            try
            {
                nCount = 0;
                strMSG = "";
                foreach (var item in dicReviceMSG)
                {
                    nCount += item.Value;
                    strMSG += string.Format("{2}【{0}】点收到{1}条信息", item.Key, item.Value, Environment.NewLine);
                }
                strAllMSG += Environment.NewLine + "当前共收到信息：" + nCount + "条" + strMSG;
            }
            catch (Exception oe)
            {
                Console.WriteLine("统计所有信息出错！" + oe.Message);
            }
            try
            {
                nCount = 0;
                strMSG = "";
                foreach (var item in dicReviceTicketMSG)
                {
                    nCount += item.Value;
                    strMSG += string.Format("{2}【{0}】点接收{1}条查票信息", item.Key, item.Value, Environment.NewLine);
                }
                strAllMSG += Environment.NewLine + Environment.NewLine + "当前共接收查票信息：" + nCount + "条" + strMSG;
            }
            catch (Exception oe)
            {
                Console.WriteLine("统计查票信息出错！" + oe.Message);
            }
            try
            {
                nCount = 0;
                strMSG = "";
                foreach (var item in dicSendMSG)
                {
                    nCount += item.Value;
                    strMSG += string.Format("{2}【{0}】点发送{1}条信息", item.Key, item.Value, Environment.NewLine);
                }
                strAllMSG += Environment.NewLine + Environment.NewLine + "当前共发送信息：" + nCount + "条" + strMSG;
            }
            catch (Exception oe)
            {
                Console.WriteLine("统计发送信息出错！" + oe.Message);
            }
            Console.WriteLine(DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss") + "统计信息如下：" + Environment.NewLine + Environment.NewLine + strAllMSG);
        }

        private void SendToAll(string strMSG)
        {
            ThreadPool.QueueUserWorkItem((object obj) =>
            {
                var lstTmpIp = lstClient.Where(p => p != null && p.IsValidate).Select(p => p.IP).ToArray();
                try
                {
                    Parallel.ForEach(lstTmpIp, ip =>
                    {
                        SendMSG(ip, strMSG);
                    });
                }
                catch (AggregateException oe)
                {
                    foreach (Exception item in oe.InnerExceptions)
                    {
                        Console.WriteLine(item);
                    }
                }
                catch (Exception oe)
                {
                    Console.WriteLine(oe);
                }
                lstTmpIp = null;
            });
        }

        private void ClearAllExpiredTicketQuery()
        {
            try
            {
                lstClient.ForEach(p =>
                {
                    if (p != null)
                    {
                        if (p.LstTicketKey == null)
                        {
                            p.LstTicketKey = new List<TicketQueryEntity>();
                        }
                        if (p.LstTicketKey.Count > 0)
                        {
                            p.LstTicketKey.RemoveAll(q => q == null || q.DtExpired <= DateTime.Now);
                        }
                    }
                });
            }
            catch (Exception oe)
            {
                Console.WriteLine(oe);
            }
        }

        private void SendTicketInfo(TicketShareEntity tic, IPEndPoint client = null, bool isForce = false)
        {
            ThreadPool.QueueUserWorkItem((object obj) =>
            {
                ClearAllExpiredTicketQuery();
                try
                {
                    var lstTmp = lstClient.FindAll(p => p != null && p.IsValidate && p.LastDate.AddSeconds(3) <= DateTime.Now
                        && p.LstTicketKey != null
                        && p.LstTicketKey.Exists(q => q.StrKey.Equals(tic.strKey)));
                    if (lstTmp != null && lstTmp.Count > 0)
                    {
                        Parallel.ForEach(lstTmp, item =>
                        {
                            if (item != null && item.IP != null && !string.IsNullOrEmpty(item.Machine))
                            {
                                try
                                {
                                    lstClient.Find(p => p != null && p.IsValidate && p.IP.Equals(item.IP)).LastDate = DateTime.Now;
                                    if (true)//CodeHelper.IsExitsCode(item.Machine)
                                    {
                                        SendMSG(item.IP, tic.strKey + "," + tic.strSubCode + "," + tic.strMachine);
                                        SendMSGCount();
                                    }
                                    else
                                    {
                                        //SendMSG(item.IP, "close");
                                    }
                                }
                                catch (Exception oe)
                                {
                                    Console.WriteLine(oe);
                                }
                            }
                        });
                    }
                    lstTmp = null;
                }
                catch (AggregateException oe)
                {
                    foreach (Exception item in oe.InnerExceptions)
                    {
                        Console.WriteLine(item);
                    }
                }
                catch (Exception oe)
                {
                    Console.WriteLine(oe);
                }
            });
        }

        private bool SendMSG(IPEndPoint client, string strMSG)
        {
            bool result = false;
            try
            {
                if (client == null || !tcpServerEngine.IsClientOnline(client))
                {
                    if (client != null)
                    {
                        ShowOnLineEvent(client, false);
                    }
                    return result;
                }
                string msg = strMSG + "\0";// "\0" 表示一个消息的结尾
                byte[] bMsg = System.Text.Encoding.UTF8.GetBytes(msg);//消息使用UTF-8编码
                tcpServerEngine.SendMessageToClient(client, bMsg);
                result = true;
            }
            catch (Exception oe)
            {
                Console.WriteLine(oe);
            }
            return result;
        }

        //public string adminMachine
        //{
        //    get
        //    {
        //        return ConfigurationManager.AppSettings.Get("adminMachine");
        //    }
        //}

        //private void SendToAdmin(string strMSG)
        //{
        //    try
        //    {
        //        if (!string.IsNullOrEmpty(adminMachine))
        //        {
        //            foreach (var item in lstClient.FindAll(p => p.Machine == adminMachine))
        //            {
        //                SendMSG(item.IP, strMSG);
        //            }
        //        }
        //    }
        //    catch (Exception oe)
        //    {
        //        Console.WriteLine(oe.Message);
        //    }
        //}
    }

    public class ClientEntity
    {
        public IPEndPoint IP { get; set; }

        public List<TicketQueryEntity> LstTicketKey = new List<TicketQueryEntity>();

        public string Machine { get; set; }

        public DateTime LastDate { get; set; }

        public string StrIdentity { get; set; }

        public void SetOffline()
        {
            this.DtRemove = DateTime.Now.AddMinutes(3);
        }

        public bool IsValidate
        {
            get { return !DtRemove.HasValue; }
        }

        public DateTime? DtRemove { get; set; }


    }

    public class TicketShareEntity
    {
        public string strKey { get; set; }

        public string strSubCode { get; set; }

        public DateTime DtExpired { get; set; }

        public string strMachine { get; set; }

        public DateTime DtAdd { get; set; }
    }

    public class TicketQueryEntity
    {
        public string StrKey { get; set; }

        public DateTime DtExpired { get; set; }
    }
}
