﻿using System.ServiceProcess;

namespace AutoProxy
{
    static class Program
    {
        /// <summary>
        /// 应用程序的主入口点。
        /// </summary>
        static void Main()
        {
            //new Service().CheckState();
            //System.Threading.Thread.Sleep(1000000);
            //Console.Read();
            //return;
            //TicketBLL.TicketService service = null;
            ////Log.WriteLog(string.Format("服务重启：{0}", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")));
            //service = new TicketBLL.TicketService();
            //service.Connect(21);
            //Console.ReadLine();
            ServiceBase[] ServicesToRun;
            ServicesToRun = new ServiceBase[] 
            { 
                new Service() 
            };
            ServiceBase.Run(ServicesToRun);
        }
    }
}
