﻿using CommonLib;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Collections.Specialized;
using System.IO;
using System.Security.Cryptography;
using System.Text;

namespace TableOcr
{
    /// <summary>
    /// https://www.pen-to-print.com/forms/template/
    /// </summary>
    public class AWSApiRec : BaseTableRec
    {
        public AWSApiRec()
        {
            OcrGroup = OcrGroupType.AWS;
            OcrType = TableOcrType.AWS_API;
            MaxExecPerTime = 20;

            LstJsonPreProcessArray = new List<object>() { "result", "tableData", "type=T" };
            LstJsonNextProcessArray = new List<object>() { "content" };

            IsCellArray = true;
            IsRowIndexAddOne = true;
        }

        protected override string GetHtml(OcrContent content)
        {
            var byts = Convert.FromBase64String(content.strBase64);
            var srcHash = SHA256Hex(byts);
            var result = PostFileResult(byts, srcHash);
            return result;
        }

        private string PostFileResult(byte[] content, string srcHash)
        {
            var result = "";
            try
            {
                var fileExt = "png";
                var url = "https://nmwe4beyw1.execute-api.us-east-1.amazonaws.com/dev/forms/analyzeFormAndTable";
                var file = new UploadFileInfo()
                {
                    Name = "srcImg",
                    Filename = "1." + fileExt,
                    ContentType = ApplicationTypeHelper.GetApplicationType(fileExt),
                    Stream = new MemoryStream(content)
                };
                var header = new NameValueCollection() {
                    { "X-Api-Key","4qlkYrXJ4Z255nLU35mnq84sr1VmMs9j1su18xlK"},
                    { "Referer","https://www.pen-to-print.com/"}
                };
                var values = new NameValueCollection() {
                { "srcHash",srcHash},
                { "appVersion","1.0"}
            };
                result = PostFile(url, new[] { file }, values, header);
            }
            catch (Exception)
            {

            }
            return BoxUtil.DeUnicode(result);
        }

        static string SHA256Hex(byte[] content)
        {
            using (SHA256 algo = SHA256.Create())
            {
                byte[] hashbytes = algo.ComputeHash(content);
                StringBuilder builder = new StringBuilder();
                for (int i = 0; i < hashbytes.Length; i++)
                {
                    builder.Append(hashbytes[i].ToString("x2"));
                }
                var n = "";
                for (int e = 0; e < 10; e++)
                    n += builder[3 + 3 * e];
                return n;
            }
        }

    }
}