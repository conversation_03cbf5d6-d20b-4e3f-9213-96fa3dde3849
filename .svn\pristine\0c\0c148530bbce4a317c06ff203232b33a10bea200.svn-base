﻿using CommonLib;
using System;
using System.Collections.Specialized;
using System.IO;

namespace ImageLib
{
    public class SouGouImageUpload : BaseImageUpload
    {
        public SouGouImageUpload()
        {
            ImageType = ImageUploadTypeEnum.搜狗;
        }

        public override string GetResult(byte[] content)
        {
            var result = GetFromSouGou2(content);
            if (string.IsNullOrEmpty(result))
                result = GetFromSouGou(content);
            if (!string.IsNullOrEmpty(result))
            {
                result += "?1.png";
            }
            return result;
        }

        private static string GetFromSouGou2(byte[] content)
        {
            var result = "";
            var url = "https://proxy.jianzhuxuezhang.com/upload2";
            var file = new UploadFileInfo()
            {
                Name = "file",
                Filename = Guid.NewGuid().ToString().ToLower().Replace("-", "") + ".png",
                ContentType = "image/png",
                Stream = new MemoryStream(content)
            };
            try
            {
                result = UploadFileRequest.Post(url, new[] { file }, null, null);
            }
            catch { }
            if (!result.StartsWith("http"))
                result = "";
            return result;
        }

        private static string GetFromSouGou(byte[] content)
        {
            var result = "";
            var url = "https://pic.sogou.com/pic/upload_pic.jsp?uuid=" + Guid.NewGuid().ToString().ToLower();
            var file = new UploadFileInfo()
            {
                Name = "pic_path",
                Filename = Guid.NewGuid().ToString().ToLower() + ".png",
                ContentType = "image/png",
                Stream = new MemoryStream(content)
            };
            try
            {
                result = UploadFileRequest.Post(url, new[] { file }, null);
            }
            catch { }
            if (!result.StartsWith("http"))
                result = "";
            return result;
        }
    }
}
