﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">x86</Platform>
    <ProductVersion>8.0.30703</ProductVersion>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectGuid>{AF46C035-36EC-4EF6-A969-14DC5A790E02}</ProjectGuid>
    <OutputType>WinExe</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>AccountTool</RootNamespace>
    <AssemblyName>AccountTool</AssemblyName>
    <TargetFrameworkVersion>v4.0</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <TargetFrameworkProfile />
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|x86' ">
    <PlatformTarget>x86</PlatformTarget>
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|x86' ">
    <PlatformTarget>x86</PlatformTarget>
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <UseVSHostingProcess>false</UseVSHostingProcess>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="OpenPop">
      <HintPath>..\References\OpenPop.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Data" />
    <Reference Include="System.Deployment" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Web" />
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="FormUserCheck.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="FormUserCheck.Designer.cs">
      <DependentUpon>FormUserCheck.cs</DependentUpon>
    </Compile>
    <Compile Include="frmReg.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmReg.Designer.cs">
      <DependentUpon>frmReg.cs</DependentUpon>
    </Compile>
    <Compile Include="HTTP.cs" />
    <Compile Include="HttpHelper.cs" />
    <Compile Include="HttpRequest.cs" />
    <Compile Include="POP3.cs" />
    <Compile Include="Program.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="RandomChinese.cs" />
    <Compile Include="RegAccount.cs" />
    <Compile Include="SundayAPI.cs" />
    <Compile Include="UserEntity.cs" />
    <Compile Include="ValidateCode.cs" />
    <Compile Include="frmRegKey.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmRegKey.Designer.cs">
      <DependentUpon>frmRegKey.cs</DependentUpon>
    </Compile>
    <Compile Include="WebClientExt.cs">
      <SubType>Component</SubType>
    </Compile>
    <EmbeddedResource Include="FormUserCheck.resx">
      <DependentUpon>FormUserCheck.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="frmReg.resx">
      <DependentUpon>frmReg.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Properties\Resources.resx">
      <Generator>ResXFileCodeGenerator</Generator>
      <LastGenOutput>Resources.Designer.cs</LastGenOutput>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <Compile Include="Properties\Resources.Designer.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Resources.resx</DependentUpon>
      <DesignTime>True</DesignTime>
    </Compile>
    <EmbeddedResource Include="frmRegKey.resx">
      <DependentUpon>frmRegKey.cs</DependentUpon>
    </EmbeddedResource>
    <None Include="app.config" />
    <None Include="Properties\Settings.settings">
      <Generator>SettingsSingleFileGenerator</Generator>
      <LastGenOutput>Settings.Designer.cs</LastGenOutput>
    </None>
    <Compile Include="Properties\Settings.Designer.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Settings.settings</DependentUpon>
      <DesignTimeSharedInput>True</DesignTimeSharedInput>
    </Compile>
    <None Include="Resources\data.bin" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\CodeReader.dll" />
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>