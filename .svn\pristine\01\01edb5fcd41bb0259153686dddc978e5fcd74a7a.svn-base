﻿using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.IO;
using System.Net;

namespace CommonLib
{
    public class CommonHelper
    {
        public static List<string> DocFileTypes = new List<string>() { "pdf", "doc", "docx", "xls", "xlsx", "ppt", "pptx", "txt" };

        public static JsonSerializerSettings PropSettings = new JsonSerializerSettings();

        public static string SubString(string strSource, string strSpilt, string strEnd = "")
        {
            return strSource.SubStringHorspool(strSpilt, strEnd).Trim();
        }

        public static string SaveBase64ToFile(string strBase64, string fileExt, bool isBase64 = true)
        {
            var result = string.Empty;
            try
            {
                byte[] byts = null;
                if (!string.IsNullOrEmpty(strBase64))
                {
                    if (isBase64)
                    {
                        strBase64 = strBase64.Replace("\\/", "/");
                        byts = new Base64Decoder().GetDecoded(strBase64);
                    }
                    else
                    {
                        byts = System.Text.Encoding.UTF8.GetBytes(strBase64);
                    }
                }
                if (byts != null && byts.Length > 0)
                {
                    var url = ServerInfo.HostAccount?.FullUrl + "code.ashx?op=savefile" + "&version=" + ServerInfo.DtNowVersion.Ticks;
                    var file = new UploadFileInfo()
                    {
                        Name = "file",
                        Filename = Guid.NewGuid().ToString().Replace("-", "") + "." + fileExt,
                        ContentType = ApplicationTypeHelper.GetApplicationType(fileExt),
                        Stream = new MemoryStream(byts)
                    };
                    result = UploadFileRequest.Post(url, new[] { file }, null);
                }
            }
            catch { }
            return result;
        }

        public static OcrFileType GetFileType(string fileType)
        {
            switch (fileType)
            {
                case "pdf":
                    return OcrFileType.PDF;
                case "ppt":
                    return OcrFileType.PPT;
                case "xls":
                    return OcrFileType.Xls;
                case "txt":
                    return OcrFileType.Txt;
                default:
                    return OcrFileType.Doc;
            }
        }
    }

    /// <summary>
    /// Base64解码类
    /// 将Base64编码的string类型转换成byte[]类型
    /// </summary>
    public class Base64Decoder
    {
        char[] source;
        int length, length2, length3;
        int blockCount;
        int paddingCount;
        public static Base64Decoder Decoder = new Base64Decoder();

        public Base64Decoder()
        {
        }

        private void init(char[] input)
        {
            int temp = 0;
            source = input;
            length = input.Length;

            for (int x = 0; x < 2; x++)
            {
                if (input[length - x - 1] == '=')
                    temp++;
            }

            paddingCount = temp;

            blockCount = length / 4;
            length2 = blockCount * 3;
        }

        public byte[] GetDecoded(string strInput)
        {
            //初始化
            init(strInput.ToCharArray());

            byte[] buffer = new byte[length];
            byte[] buffer2 = new byte[length2];

            for (int x = 0; x < length; x++)
            {
                buffer[x] = char2sixbit(source[x]);
            }

            byte b, b1, b2, b3;
            byte temp1, temp2, temp3, temp4;

            for (int x = 0; x < blockCount; x++)
            {
                temp1 = buffer[x * 4];
                temp2 = buffer[x * 4 + 1];
                temp3 = buffer[x * 4 + 2];
                temp4 = buffer[x * 4 + 3];

                b = (byte)(temp1 << 2);
                b1 = (byte)((temp2 & 48) >> 4);
                b1 += b;

                b = (byte)((temp2 & 15) << 4);
                b2 = (byte)((temp3 & 60) >> 2);
                b2 += b;

                b = (byte)((temp3 & 3) << 6);
                b3 = temp4;
                b3 += b;

                buffer2[x * 3] = b1;
                buffer2[x * 3 + 1] = b2;
                buffer2[x * 3 + 2] = b3;
            }

            length3 = length2 - paddingCount;
            byte[] result = new byte[length3];

            for (int x = 0; x < length3; x++)
            {
                result[x] = buffer2[x];
            }

            return result;
        }

        private byte char2sixbit(char c)
        {
            char[] lookupTable = new char[64]
            {
                'A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N',
                'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z',
                'a', 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'i', 'j', 'k', 'l', 'm', 'n',
                'o', 'p', 'q', 'r', 's', 't', 'u', 'v', 'w', 'x', 'y', 'z',
                '0', '1', '2', '3', '4', '5', '6', '7', '8', '9', '+', '/'
            };
            if (c == '=')
                return 0;
            else
            {
                for (int x = 0; x < 64; x++)
                {
                    if (lookupTable[x] == c)
                        return (byte)x;
                }

                return 0;
            }
        }
    }

}