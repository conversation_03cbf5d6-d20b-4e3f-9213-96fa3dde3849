﻿using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Collections.Specialized;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Timers;

namespace CommonLib
{
    public class ServerStateInfo
    {
        public string Ip { get; set; }

        public string Desc { get; set; }

        public Dictionary<OcrType, List<OcrGroupInfo>> Info { get; set; }

        public DateTime DtUpdate { get; set; }

        public string Id { get; set; }
        public object Version { get; internal set; }
        public ServerClientEntity Server { get; internal set; }

        public Dictionary<OcrGroupType, List<OcrGroupInfo>> GetByOcrType(OcrType ocrType)
        {
            var lstOcr = GetListByOcrType(ocrType);
            var dicGroup = lstOcr.GroupBy(p => p.GroupType).OrderByDescending(p => p.ToList().Count).ToDictionary(p => p.Key, q => q.ToList());
            return dicGroup;
        }

        public List<OcrGroupInfo> GetListByOcrType(OcrType ocrType)
        {
            return Info.ContainsKey(ocrType) ? Info[ocrType] : new List<OcrGroupInfo>();
        }
    }

    public class OcrGroupInfo
    {
        public int OcrType { get; set; }

        public string OcrName { get; set; }

        public OcrGroupType GroupType { get; set; }

        public EnableState State { get; set; }
    }

    public class BaseRecHelper
    {
        static Dictionary<OcrType, Dictionary<int, BaseRec>> dicOcrs = new Dictionary<OcrType, Dictionary<int, BaseRec>>();
        static Dictionary<string, List<OcrConsumerEntity>> ocrEnableTypeCache = new Dictionary<string, List<OcrConsumerEntity>>();

        private static string NowId;

        static BaseRecHelper()
        {
            NowId = Directory.GetCreationTime(AppDomain.CurrentDomain.BaseDirectory).ToString("yyyy-MM-dd");
            Task.Factory.StartNew(() =>
            {
                var dtLastReset = ServerTime.LocalTime;
                var timer = new Timer() { Interval = 1 * 60 * 1000 };
                timer.Elapsed += (sender, e) =>
                {
                    ReportToServer();
                    if (new TimeSpan(ServerTime.LocalTime.Ticks - dtLastReset.Ticks).Hours >= 7)
                    {
                        dtLastReset = ServerTime.LocalTime;
                        ResetOcrMaxExecTime();
                        AccountHelper.Reset();
                        LogHelper.Log.Info(string.Format("{0} 【MaxExecTime】次数已重置！", ServerTime.LocalTime.ToString("yyyy-MM-dd HH:mm:ss")));
                    }
                };
                timer.Start();
                LogHelper.Log.Info("【MaxExecTime】次数服务开启…");
            });
        }

        static void ResetOcrMaxExecTime()
        {
            try
            {
                foreach (var baseType in dicOcrs)
                {
                    try
                    {
                        foreach (var ocrType in baseType.Value)
                        {
                            try
                            {
                                ocrType.Value.Reset();
                            }
                            catch { }
                        }
                    }
                    catch { }
                }
            }
            catch { }
            ClearCache();
        }

        public static void InitOcrTypes(OcrType baseType, Dictionary<int, BaseRec> ocrTypes)
        {
            foreach (var item in ocrTypes.Values)
            {
                if (item.MaxExecPerTimeBack == 0)
                    item.MaxExecPerTimeBack = item.MaxExecPerTime;
            }
            if (!dicOcrs.ContainsKey(baseType))
            {
                dicOcrs.Add(baseType, ocrTypes);
            }
            else
            {
                var tmpValues = dicOcrs[baseType];
                if (tmpValues == null)
                {
                    tmpValues = new Dictionary<int, BaseRec>();
                }
                foreach (var item in ocrTypes)
                {
                    if (!tmpValues.ContainsKey(item.Key))
                    {
                        tmpValues.Add(item.Key, item.Value);
                    }
                }
                dicOcrs[baseType] = tmpValues;
            }
        }

        public static void DisableByType(OcrType baseType, int ocrType)
        {
            if (!dicOcrs.ContainsKey(baseType))
            {
                return;
            }
            if (!dicOcrs[baseType].ContainsKey(ocrType))
            {
                return;
            }
            dicOcrs[baseType].Remove(ocrType);
            ClearCache();
        }

        public static BaseRec GetInstance(int ocrType)
        {
            return (from ocrDicByType in dicOcrs.Values where ocrDicByType.ContainsKey(ocrType) select ocrDicByType[ocrType]).FirstOrDefault();
        }

        public static BaseRec GetInstance(OcrType baseType, int ocrType)
        {
            return dicOcrs[baseType][ocrType];
        }

        public static void ClearCache()
        {
            lock (ocrEnableTypeCache)
            {
                ocrEnableTypeCache.Clear();
            }
        }

        //public OcrGroupType OcrGroup { get; set; }

        //public int ProcessId { get; set; }

        public static List<OcrConsumerEntity> GetEnableRecType(bool isEnable, OcrType baseType, OcrGroupType groupType, int? processId
            , bool? isSupportVertical, string fileExt, TransLanguageTypeEnum from, TransLanguageTypeEnum to)
        {
            if (baseType != OcrType.翻译)
            {
                to = TransLanguageTypeEnum.中文;
            }
            var key = string.Format("{0}_{1}_{2}_{3}_{4}_{5}_{6}", baseType, isEnable, isSupportVertical, groupType, processId, fileExt, to);
            if (!ocrEnableTypeCache.ContainsKey(key))
            {
                List<OcrConsumerEntity> lstTmp;
                if (processId != null)
                {
                    lstTmp = dicOcrs[baseType].Values.Where(p => p.OcrType == processId).Select(p => new OcrConsumerEntity() { OcrType = p.OcrType, IsSupportUrl = p.IsSupportUrlOcr }).ToList();
                }
                else
                {
                    var lstSupport = dicOcrs[baseType].Values.Where(p => groupType == OcrGroupType.不限 || p.OcrGroup == groupType).ToList();
                    if (isSupportVertical.HasValue && isSupportVertical.Value)
                    {
                        lstSupport = lstSupport.Where(p => p.IsSupportVertical).ToList();
                    }
                    if (!string.IsNullOrEmpty(fileExt))
                    {
                        lstSupport = lstSupport.FindAll(p => p.AllowUploadFileTypes.Contains(fileExt)).ToList();
                    }
                    if (baseType == OcrType.翻译)
                    {
                        lstSupport = lstSupport.FindAll(p => p.TransLanguageDic.ContainsKey(to)).ToList();
                    }
                    if (lstSupport.Count > 0 && lstSupport.All(p => p.IsEnable() != isEnable))
                    {
                        lstSupport.ForEach(p =>
                        {
                            p.Reset();
                        });
                    }
                    lstTmp = lstSupport.OrderByDescending(p => p.IsSupportUrlOcr).ThenByDescending(p => p.MaxExecPerTime).Select(p => new OcrConsumerEntity() { OcrType = p.OcrType, IsSupportUrl = p.IsSupportUrlOcr }).ToList();
                }
                if (!ocrEnableTypeCache.ContainsKey(key))
                {
                    lock (ocrEnableTypeCache)
                    {
                        ocrEnableTypeCache.Add(key, lstTmp);
                    }
                }
            }
            return ocrEnableTypeCache[key];
        }

        public class OcrConsumerEntity
        {
            public int OcrType { get; set; }

            public bool IsSupportUrl { get; set; }
        }

        public static DateTime DtNowVersion = DateTime.Parse("2023-12-29 21:30:00");

        public static void ReportToServer()
        {
            try
            {
                var desc = string.Empty;
                var ip = DnsHelper.GetIpInfo(ref desc);
                var dicInfo = new Dictionary<OcrType, List<OcrGroupInfo>>();
                foreach (var keyValuePair in dicOcrs)
                {
                    var lstOcrInfo = new List<OcrGroupInfo>();
                    foreach (var baseRec in keyValuePair.Value.Values)
                    {
                        lstOcrInfo.Add(new OcrGroupInfo()
                        {
                            GroupType = baseRec.OcrGroup,
                            OcrName = baseRec.GetOcrTypeName(),
                            OcrType = baseRec.OcrType,
                            State = baseRec.State
                        });
                    }
                    dicInfo.Add(keyValuePair.Key, lstOcrInfo);
                }
                var state = new ServerStateInfo
                {
                    Id = NowId,
                    Info = dicInfo,
                    DtUpdate = ServerTime.DateTime,
                    Desc = desc,
                    Ip = ip,
                    Version = DtNowVersion,
                    Server = ServerUuidUtil.GetServerInfo()
                };
                var result = UploadFileRequest.Post(ConfigHelper.OcrWebSite + "code.ashx?op=serverstate", null, new NameValueCollection()
                {
                    {"info",JsonConvert.SerializeObject(state)}
                });
            }
            catch
            {
            }
        }

        public static void Report()
        {
            try
            {
                var sb = new StringBuilder();
                foreach (var item in dicOcrs.Keys)
                {
                    sb.AppendLine(string.Format("==============================【" + item.ToString() + "】统计信息开始============================"));
                    foreach (var rec in dicOcrs[item].Values.OrderByDescending(p => p.MaxExecPerTime).ToList())
                    {
                        sb.AppendLine(BaseReport(rec));
                    }
                    sb.AppendLine();
                    //sb.AppendLine(string.Format("==============================【" + item.ToString() + "】统计信息结束============================"));
                }
                LogHelper.Log.Info(sb);
            }
            catch { }
        }

        private static string BaseReport(BaseRec ocr)
        {
            return string.Format("{0}Now/Max:{1}/{2}     Total:{3}       Forbid:{4}"
                , padRightEx(string.Format("【{0}】:{1}", ocr.GetOcrTypeName() + "-" + ocr.OcrType, ocr.State), 40), ocr.NowExecTimes.ToString().PadLeft(5), ocr.MaxExecPerTime.ToString().PadRight(10), ocr.TotalExecTimes.ToString().PadRight(10), ocr.ForbidTimes.ToString().PadRight(10));
        }

        private static string padRightEx(string str, int totalByteCount)
        {
            Encoding coding = Encoding.GetEncoding("gb2312");
            int dcount = str.ToCharArray().Select(p => { return coding.GetByteCount(p.ToString()) == 2 ? 1 : 0; }).Sum();
            return str.PadRight(totalByteCount - dcount);
        }
    }
}
