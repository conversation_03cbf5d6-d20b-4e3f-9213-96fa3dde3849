/**
 * Copyright (c) 2014 by <PERSON>, http://www.mergely.com
 * All rights reserved.
 * Version: 3.3.7 2014-08-17
 */

/* required */
.mergely-column textarea { width: 80px; height: 200px; }
.mergely-column { float: left; }
.mergely-margin { float: left; }
.mergely-canvas { float: left; width: 28px; }

/* resizeable */
.mergely-resizer { width: 100%; height: 100%; }

/* style configuration */
.mergely-column { border: 1px solid #ccc; }
.mergely-active { border: 1px solid #a3d1ff; }

.mergely.a.rhs.start { border-top: 1px solid #a3d1ff; }
.mergely.a.lhs.start.end,
.mergely.a.rhs.end { border-bottom: 1px solid #a3d1ff; }
.mergely.a.rhs { background-color: #ddeeff; }
.mergely.a.lhs.start.end.first { border-bottom: 0; border-top: 1px solid #a3d1ff; }

.mergely.d.lhs { background-color: #edc0c0; }
.mergely.d.lhs.end,
.mergely.d.rhs.start.end { border-bottom: 1px solid #ff7f7f; }
.mergely.d.rhs.start.end.first { border-bottom: 0; border-top: 1px solid #ff7f7f; }
.mergely.d.lhs.start { border-top: 1px solid #ff7f7f; }

.mergely.c.lhs,
.mergely.c.rhs { background-color: #fafafa; }
.mergely.c.lhs.start,
.mergely.c.rhs.start { border-top: 1px solid #a3a3a3; }
.mergely.c.lhs.end,
.mergely.c.rhs.end { border-bottom: 1px solid #a3a3a3; }

.mergely.ch.a.rhs { background-color: #ddeeff; }
.mergely.ch.d.lhs { background-color: #edc0c0; text-decoration: line-through; color: #888; }
