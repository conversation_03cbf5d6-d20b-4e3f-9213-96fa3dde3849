﻿using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Text;

namespace CommonLib
{
    public class OcrHtmlProcess
    {
        public delegate LocationInfo GetLocationByStrDelegate(string str);
        public static ResultEntity GetSpiltTextByJsonNew(string html, bool IsProcessJsonResultByArray
            , List<object> LstJsonPreProcessArray, List<object> LstJsonNextProcessArray, List<object> LstJsonLastProcessArray, List<object> LstJsonLocationProcessArray
        , string resultJsonSpilt, string resultTransJsonSpilt
            , bool IsVerticalOcr = false, bool IsMutliPage = false, bool IsAutoPageIndex = false, bool IsSupportVerticalOcr = false
            , bool IsJsonArrayString = false, bool IsJsonArrayStringWithLocation = false
        , bool isFromLeftToRight = false, bool isFromTopToDown = false, List<object> LstVerticalLocation = null
        , Dictionary<string, string> dicProperty = null, bool isDesrializeByLocation = false, GetLocationByStrDelegate getLocationByStrDelegate = null)
        {
            var result = new ResultEntity() { html = html };
            var strJson = html;

            if (LstJsonPreProcessArray?.Count > 0)
            {
                strJson = JsonResultProcess.GetStrResultByHtml(html, LstJsonPreProcessArray);
            }

            if (string.IsNullOrEmpty(strJson))
            {
                return result;
            }

            if (LstJsonNextProcessArray?.Count > 0)
            {
                strJson = ProcessJsonResult(strJson, LstJsonNextProcessArray, true);
                //strJson = JsonResultProcess.GetStrResultByHtml(strJson, LstJsonNextProcessArray);
            }

            if (!IsProcessJsonResultByArray)
            {
                result.spiltText = strJson;
                result.autoText = strJson;
            }
            else
            {
                if (!string.IsNullOrEmpty(strJson))
                {
                    if (strJson.EndsWith(","))
                    {
                        strJson = strJson.TrimEnd(',');
                    }
                    if (!strJson.StartsWith("["))
                    {
                        strJson = string.Format("[{0}]", strJson.TrimEnd(','));
                    }
                    result.resultHtml = strJson;
                    JArray jArray = JArray.Parse(strJson);
                    if (jArray != null)
                    {
                        if (IsJsonArrayStringWithLocation)
                        {
                            StringBuilder sb = new StringBuilder();
                            var locations = JsonResultProcess.GetStrResultByHtml(html, LstJsonLocationProcessArray);
                            JArray locationArray = JArray.Parse(locations);
                            for (int i = 0; i < locationArray.Count; i++)
                            {
                                sb.AppendFormat("{0},\"words\":\"{1}\"", locationArray[i].ToString().Trim().TrimEnd('}'), jArray[i]);
                                sb.Append("},");
                            }

                            strJson = "[" + sb.ToString().Trim().TrimEnd(',') + "]";
                            jArray = JArray.Parse(strJson);
                        }
                        if (IsJsonArrayString)
                        {
                            strJson = "";
                            foreach (var item in jArray)
                            {
                                strJson += string.Format("{0}\n", item);
                            }
                            result.spiltText = strJson;
                            result.autoText = strJson;
                        }
                        else
                        {
                            if (IsSupportVerticalOcr || IsVerticalOcr)
                            {
                                try
                                {
                                    List<TextCellInfo> lstCells = new List<TextCellInfo>();
                                    if (IsMutliPage)
                                    {
                                        ProcessMutilPage(LstJsonLastProcessArray, resultJsonSpilt, resultTransJsonSpilt, IsAutoPageIndex, LstVerticalLocation, dicProperty, isDesrializeByLocation, getLocationByStrDelegate, jArray, lstCells);
                                    }
                                    else
                                    {
                                        lstCells = GetVerticalCells(resultJsonSpilt, resultTransJsonSpilt, LstVerticalLocation, dicProperty, strJson, jArray, isDesrializeByLocation, getLocationByStrDelegate);
                                    }
                                    ProcessByLstCells(IsVerticalOcr, isFromLeftToRight, isFromTopToDown, result, lstCells);
                                }
                                catch (Exception oe)
                                {
                                    Console.WriteLine(oe.Message);
                                }
                            }
                            if (string.IsNullOrEmpty(result.spiltText))
                            {
                                var strTrans = string.Empty;
                                result.spiltText = JsonResultProcess.ArrayToString(jArray, resultJsonSpilt, resultTransJsonSpilt, ref strTrans);
                                result.transText = strTrans;
                            }
                        }
                    }
                    result.autoText = string.IsNullOrEmpty(result.autoText) ? result.spiltText : result.autoText;
                }
            }
            ProcessDecodeHtml(result);
            return result;
        }

        private static void ProcessMutilPage(List<object> LstJsonLastProcessArray, string resultJsonSpilt, string resultTransJsonSpilt,
            bool IsAutoPageIndex, List<object> LstVerticalLocation, Dictionary<string, string> dicProperty, bool isDesrializeByLocation,
            GetLocationByStrDelegate getLocationByStrDelegate, JArray jArray, List<TextCellInfo> lstCells)
        {
            if (IsAutoPageIndex)
            {
                foreach (var jToken in jArray[0])
                {
                    var item = jToken as JProperty;
                    if (item == null)
                    {
                        continue;
                    }

                    var pageIndex = BoxUtil.GetInt32FromObject(item.Name, 1);

                    var lstTmp = ProcessMutilOnePage(LstJsonLastProcessArray, resultJsonSpilt, resultTransJsonSpilt, LstVerticalLocation, dicProperty, isDesrializeByLocation, getLocationByStrDelegate, item.Value?.ToString(), pageIndex);

                    lstCells.AddRange(lstTmp);
                }
            }
            else
            {
                int pageIndex = 1;
                foreach (var item in jArray)
                {
                    if (item == null)
                    {
                        continue;
                    }

                    var lstTmp = ProcessMutilOnePage(LstJsonLastProcessArray, resultJsonSpilt, resultTransJsonSpilt, LstVerticalLocation, dicProperty, isDesrializeByLocation, getLocationByStrDelegate, item.ToString(), pageIndex);

                    pageIndex++;
                    lstCells.AddRange(lstTmp);
                }
            }
            if (lstCells.Exists(p => p.PageIndex == 0))
            {
                lstCells.ForEach(p => { p.PageIndex += 1; });
            }
        }

        private static List<TextCellInfo> ProcessMutilOnePage(List<object> LstJsonLastProcessArray, string resultJsonSpilt,
            string resultTransJsonSpilt, List<object> LstVerticalLocation, Dictionary<string, string> dicProperty, bool isDesrializeByLocation,
            GetLocationByStrDelegate getLocationByStrDelegate, string strJson, int pageIndex)
        {
            if (LstJsonLastProcessArray?.Count > 0)
            {
                strJson = ProcessJsonResult(strJson, LstJsonLastProcessArray, true);
            }

            if (!strJson.StartsWith("["))
            {
                strJson = string.Format("[{0}]", strJson.TrimEnd(','));
            }

            var lstTmp = GetVerticalCells(resultJsonSpilt, resultTransJsonSpilt, LstVerticalLocation, dicProperty,
                strJson, JArray.Parse(strJson), isDesrializeByLocation, getLocationByStrDelegate);
            lstTmp.ForEach(p => p.PageIndex = pageIndex);
            return lstTmp;
        }

        public static void ProcessDecodeHtml(ResultEntity result)
        {
            if (!string.IsNullOrEmpty(result.spiltText))
            {
                result.spiltText = System.Net.WebUtility.HtmlDecode(result.spiltText).TrimEnd();
            }
            if (!string.IsNullOrEmpty(result.transText))
            {
                result.transText = System.Net.WebUtility.HtmlDecode(result.transText).TrimEnd();
            }
            if (!string.IsNullOrEmpty(result.autoText))
            {
                result.autoText = System.Net.WebUtility.HtmlDecode(result.autoText).TrimEnd();
            }
        }

        public static void ProcessByLstCells(bool IsVerticalOcr, bool isFromLeftToRight, bool isFromTopToDown, ResultEntity result, List<TextCellInfo> lstCells)
        {
            if (lstCells?.Count > 0)
            {
                if (lstCells.Exists(p => p.location != null))
                {
                    result.verticalText = JsonConvert.SerializeObject(lstCells);
                }
                //正常文字识别，从左到右,从上到下
                isFromLeftToRight = IsVerticalOcr ? isFromLeftToRight : true;
                isFromTopToDown = IsVerticalOcr ? isFromTopToDown : true;
                var lstLines = GetVerticalOcrResult(lstCells, isFromLeftToRight, isFromTopToDown);
                var strStart = lstLines?.Count > 1 ? "\t" : "";
                result.spiltText = (strStart + string.Join("\n" + strStart, lstLines.Select(p => p.words?.TrimEnd()))).TrimEnd();
                result.transText = (strStart + string.Join("\n" + strStart, lstLines.Select(p => p.trans?.TrimEnd()))).TrimEnd();
                try
                {
                    if (lstLines.Exists(p => p.rectangle.IsEmpty))
                    {
                        return;
                    }
                    var lines = new LineProcess(lstLines);
                    result.spiltLocText = lines.GetMergeResult(false, true);
                    result.transLocText = lines.GetMergeResult(true, true);
                    //OcrLineProcess.ProcessTextByLineLocation(lstLines);
                }
                catch { }
            }
        }

        private static List<TextCellInfo> GetVerticalCells(string resultJsonSpilt, string resultTransJsonSpilt, List<object> LstVerticalLocation
            , Dictionary<string, string> dicProperty, string strJson, JArray jArray, bool isDesrializeByLocation, GetLocationByStrDelegate getLocationByStrDelegate)
        {
            List<TextCellInfo> lstCells = null;
            /*{
        "probability": 0.39386045932769775,
        "rectangle": {
        "x": 17,
        "width": 387,
        "y": 158,
        "height": 17
        },
        "text": "要不要搞多域名密户端发现超时或者无法解析时换个域名再调一遍接口"
        }*/
            if (!string.IsNullOrEmpty(resultJsonSpilt) && LstVerticalLocation?.Count > 0)
            {
                lstCells = new List<TextCellInfo>();
                foreach (var item in jArray)
                {
                    var cell = new TextCellInfo()
                    {
                        words = item[resultJsonSpilt]?.ToString()
                    };
                    if (!string.IsNullOrEmpty(resultTransJsonSpilt))
                    {
                        cell.trans = item[resultTransJsonSpilt]?.ToString();
                    }
                    var locationInfo = GetPreResultByList(item, LstVerticalLocation);
                    cell.location = getLocationByStrDelegate?.Invoke(locationInfo);
                    lstCells.Add(cell);
                }
            }
            else
            {
                JsonSerializerSettings PropSettings = new JsonSerializerSettings();
                if (dicProperty?.Count > 0)
                {
                    PropSettings.ContractResolver = new PropsContractResolver(dicProperty);
                }
                if (isDesrializeByLocation)
                {
                    lstCells = new List<TextCellInfo>();
                    var lstLocations = JsonConvert.DeserializeObject<List<VLocationInfo>>(strJson, PropSettings);
                    lstLocations?.ForEach(p =>
                    {
                        var cell = new TextCellInfo()
                        {
                            words = p?.words,
                            location = new LocationInfo()
                            {
                                height = (int)p.height,
                                left = (int)p.left,
                                top = (int)p.top,
                                width = (int)p.width
                            },
                            trans = p?.trans
                        };
                        lstCells.Add(cell);
                    });
                    lstLocations.Clear();
                }
                else
                {
                    lstCells = JsonConvert.DeserializeObject<List<TextCellInfo>>(strJson, PropSettings);
                }
            }

            return lstCells;
        }

        private static double floatPercentDown = 1.25;
        private static double floatPercentUp = 0.75;
        private static List<LineInfo> GetVerticalOcrResult(List<TextCellInfo> lstCells, bool isFromLeftToRight, bool isFromTopToDown)
        {
            List<LineInfo> lstLine = new List<LineInfo>();
            try
            {
                if (lstCells?.Count > 0)
                {
                    double? left = null;
                    double? top = null;
                    double? height = null;
                    double? width = null;
                    double? lastTop = null;
                    double? lastLeft = null;
                    double? lastTopTmp = null;
                    double? lastLeftTmp = null;
                    int? lastPageTmp = null;
                    TextCellInfo cell;
                    LineInfo line = null;
                    while ((cell =
                        isFromLeftToRight ?
                        GetNextProcessFromLeftToRight(lstCells, isFromTopToDown, ref lastTop) :
                         //GetNextProcessFromRightToLeft(lstCells, isFromTopToDown, ref lastTop)
                         GetNextProcessFromRightToLeft(lstCells, isFromTopToDown, ref lastLeft)
                        ) != null)
                    {
                        bool isNextLine = cell.location == null;
                        if (!isNextLine & left.HasValue)
                        {
                            if (cell.PageIndex != lastPageTmp || lastPageTmp == null)
                            {
                                isNextLine = true;
                            }
                            else
                            {
                                if (isFromLeftToRight)
                                {
                                    if (lastTopTmp != lastTop || lastTop == null)
                                    {
                                        //从上到下
                                        if (isFromTopToDown)
                                        {
                                            //如果高度偏离1/10以上
                                            if (cell.location.top > top && cell.location.top + cell.location.height > top + height * floatPercentDown)
                                                isNextLine = true;
                                        }
                                        //从下到上
                                        else
                                        {
                                            //如果高度偏离1/3以上
                                            if (cell.location.top < top && !CheckCross(new Rectangle() { X = (int)cell.location.left, Y = (int)cell.location.top, Width = (int)cell.location.width, Height = (int)(cell.location.height * floatPercentDown) }, new Rectangle() { X = (int)cell.location.left, Y = (int)top.Value, Width = (int)width.Value, Height = (int)height.Value }))
                                                //cell.rectangle.top + cell.rectangle.height < top + height * floatPercentUp
                                                isNextLine = true;

                                        }
                                        if (!isNextLine)
                                        {
                                            //从左向右
                                            //if (cell.rectangle.left > left && !CheckCross(new rectangle() { X = cell.rectangle.left, Y = cell.rectangle.top, Width = (int)(cell.rectangle.width * floatPercentDown), Height = cell.rectangle.height }, new rectangle() { X = left.Value, Y = cell.rectangle.top, Width = width.Value, Height = height.Value }))
                                            if (cell.location.left > left && cell.location.left + cell.location.width < left + width * floatPercentDown)
                                                isNextLine = true;
                                        }
                                    }
                                }
                                else
                                {
                                    if (lastLeftTmp != lastLeft || lastLeft == null)
                                    {
                                        //如果左右偏离1/4以上
                                        if (cell.location.left < left && cell.location.left + cell.location.width < left + width * floatPercentUp)
                                            isNextLine = true;
                                    }
                                }
                            }
                        }

                        if (line != null && isNextLine)
                        {
                            ProcessTextByLine(isFromLeftToRight, isFromTopToDown, line, lstLine);
                            line = null;
                        }
                        if (line == null)
                        {
                            line = new LineInfo { lstCell = new List<TextCellInfo>(), words = string.Empty, trans = string.Empty };
                        }
                        if (isNextLine)
                        {
                            top = -1;
                            left = -1;
                            height = -1;
                            width = -1;
                        }
                        line.lstCell.Add(cell);
                        //if (!string.IsNullOrEmpty(cell.words))
                        //{
                        //    line.words += cell.words;
                        //}
                        //if (!string.IsNullOrEmpty(cell.trans))
                        //{
                        //    line.trans += cell.trans;
                        //}
                        if (cell.location != null)
                        {
                            top = cell.location.top;
                            left = cell.location.left;
                            height = cell.location.height;
                            width = cell.location.width;
                        }
                        if (lastTop.HasValue)
                        {
                            lastTopTmp = lastTop.Value;
                        }
                        if (lastLeft.HasValue)
                        {
                            lastLeftTmp = lastLeft.Value;
                        }
                        lastPageTmp = cell.PageIndex;
                        cell.IsProcessed = true;
                    }
                    if (line != null)
                    {
                        ProcessTextByLine(isFromLeftToRight, isFromTopToDown, line, lstLine);
                    }
                }
            }
            catch (Exception oe)
            {
                Console.WriteLine(oe.Message);
            }
            lstLine.ForEach(p => p.Init());
            return lstLine;
        }

        private static void ProcessTextByLine(bool isFromLeftToRight, bool isFromTopToDown, LineInfo line, List<LineInfo> lstLine)
        {
            if (isFromLeftToRight)
            {
                if (isFromTopToDown)
                {
                    line.lstCell = line.lstCell.OrderBy(p => p.location.left).ToList();
                }
                else
                {
                    line.lstCell = line.lstCell.OrderByDescending(p => p.location.top).ToList();
                }
            }
            else
            {
                if (isFromTopToDown)
                {
                    line.lstCell = line.lstCell.OrderBy(p => p.location.top).ToList();
                }
                else
                {
                    line.lstCell = line.lstCell.OrderByDescending(p => p.location.left).ToList();
                }
            }
            line.words = string.Join("", line.lstCell.Select(p => p.words?.Replace("\n", " "))).TrimEnd();
            line.trans = string.Join("", line.lstCell.Select(p => p.trans?.Replace("\n", " "))).TrimEnd();

            lstLine.Add(JsonConvertClone<LineInfo>(line));
        }

        public static T JsonConvertClone<T>(object obj) where T : class
        {
            var str = JsonConvert.SerializeObject(obj);
            return JsonConvert.DeserializeObject<T>(str, new JsonSerializerSettings() { NullValueHandling = NullValueHandling.Ignore, Converters = new List<JsonConverter>() });
        }

        private static bool CheckCross(Rectangle r1, Rectangle r2)
        {
            var c1 = new PointF(r1.Left + r1.Width / 2.0f, r1.Top + r1.Height / 2.0f);
            var c2 = new PointF(r2.Left + r2.Width / 2.0f, r2.Top + r2.Height / 2.0f);

            return (Math.Abs(c1.X - c2.X) <= r1.Width / 2.0 + r2.Width / 2.0 && Math.Abs(c2.Y - c1.Y) <= r1.Height / 2.0 + r2.Height / 2.0);
        }

        private const double NMinWidth = 5;

        private static TextCellInfo GetNextProcessFromLeftToRight(List<TextCellInfo> lstCells
            , bool isFromTopToDown, ref double? lastTop)
        {
            TextCellInfo cell = null;

            var minPage = lstCells.Exists(p => !p.IsProcessed) ? lstCells.Where(p => !p.IsProcessed).Min(p => p.PageIndex) : 0;
            var lstNoProcessed = lstCells.Where(p => !p.IsProcessed && p.PageIndex == minPage);
            if (lstNoProcessed?.Count() > 0)
            {
                double? minTop = lastTop;
                List<TextCellInfo> lstTmp = null;
                if (!minTop.HasValue)
                {
                    if (isFromTopToDown)
                    {
                        minTop = lstNoProcessed.Min(p => p.location?.top);
                    }
                    else
                    {
                        minTop = lstNoProcessed.Max(p => p.location?.top);
                    }
                }
                else
                {
                    //Console.WriteLine("上次高度：" + lastTop);
                }
                lstTmp = GetFitResultByTop(isFromTopToDown, minTop, lstNoProcessed);
                lastTop = null;
                if (lstTmp?.Count > 0)
                {
                    if (isFromTopToDown)
                    {
                        cell = lstTmp.OrderBy(p => p.location?.top).ThenBy(p => p.location?.left).FirstOrDefault();
                    }
                    else
                    {
                        cell = lstTmp.OrderByDescending(p => p.location?.top).ThenBy(p => p.location?.left).FirstOrDefault();
                    }
                    if (lstTmp.Count > 1)
                    {
                        lastTop = minTop;
                    }
                }
                else
                {
                    cell = lstNoProcessed.FirstOrDefault();
                }
            }
            return cell;
        }

        private static TextCellInfo GetNextProcessFromRightToLeft(List<TextCellInfo> lstCells
            , bool isFromTopToDown, ref double? lastLeft)
        {
            TextCellInfo cell = null;

            //var lstNoProcessed = lstCells.Where(p => !p.IsProcessed);
            //var minPage = lstNoProcessed.Min(p => p.PageIndex);
            //lstNoProcessed = lstNoProcessed.Where(p => p.PageIndex == minPage);

            var minPage = lstCells.Exists(p => !p.IsProcessed) ? lstCells.Where(p => !p.IsProcessed).Min(p => p.PageIndex) : 0;
            var lstNoProcessed = lstCells.Where(p => !p.IsProcessed && p.PageIndex == minPage);
            if (lstNoProcessed?.Count() > 0)
            {
                double? minLeft = lastLeft;
                if (!minLeft.HasValue)
                {
                    minLeft = lstNoProcessed.Max(p => p.location?.left);
                    //minWidth = lstNoProcessed.Where(p => p.rectangle?.left + p.rectangle?.width >= minLeft).Select(p => p.rectangle?.width).Average();
                }
                else
                {
                    if (!lstNoProcessed.Any(p => p.location?.left <= minLeft && p.location?.left + p.location?.width >= minLeft))
                    {
                        minLeft = lstNoProcessed.Select(p => p.location.left).Max();
                    }
                    //Console.WriteLine("上次高度：" + lastTop);
                }
                var lstTmp = GetFitResultByLeft(minLeft, lstNoProcessed);
                lastLeft = null;
                if (lstTmp?.Count > 0)
                {
                    if (isFromTopToDown)
                    {
                        cell = lstTmp.OrderBy(p => p.location?.top).ThenByDescending(p => p.location?.left).FirstOrDefault();
                    }
                    else
                    {
                        cell = lstTmp.OrderByDescending(p => p.location?.top).ThenByDescending(p => p.location?.left).FirstOrDefault();
                    }
                    if (lstTmp.Count > 1)
                    {
                        lastLeft = minLeft;
                    }
                }
                else
                {
                    cell = lstNoProcessed.FirstOrDefault();
                }
            }
            return cell;
        }

        private static List<TextCellInfo> GetFitResultByLeft(double? left, IEnumerable<TextCellInfo> lstNoProcessed)
        {
            double? minLeft;
            double? maxLeft = left;
            //minLeft = maxLeft - width * 1.5;
            minLeft = lstNoProcessed.Where(p => p.location?.left <= left && p.location?.left + p.location?.width >= left)
                .Select(p => p.location.left).Min();
            minLeft = Math.Max(minLeft.Value, NMinWidth);

            var lstTmp = lstNoProcessed
                 .Where(p => p.location?.left >= minLeft && p.location?.left <= maxLeft).ToList();
            return lstTmp;
        }

        private static List<TextCellInfo> GetFitResultByTop(bool isFromTopToDown, double? top, IEnumerable<TextCellInfo> lstNoProcessed)
        {
            double? minTop;
            double? maxTop;

            List<TextCellInfo> lstTmp;
            if (isFromTopToDown)
            {
                double? height = lstNoProcessed.Where(p => p.location?.top <= top).Select(p => p.location?.height).Max();
                if (height == null)
                {
                    top = lstNoProcessed.Where(p => p.IsProcessed == false).Select(p => p.location?.top).Min();
                    height = lstNoProcessed.Where(p => p.location?.top <= top).Select(p => p.location?.height).Max();
                }
                minTop = top;
                //maxTop = minTop + height * 1.25;
                maxTop = lstNoProcessed.Where(p => p.location?.top >= top && p.location?.top <= top + height * floatPercentUp)
                    .Select(p => p.location?.top + p.location?.height * floatPercentUp).Max();
                var tmpMaxHeight = lstNoProcessed.Where(p => p.location?.top <= maxTop).Select(p => p.location?.height).Max();
                if (tmpMaxHeight > height)
                {
                    maxTop = lstNoProcessed.Where(p => p.location?.top >= top && p.location?.top <= top + tmpMaxHeight * floatPercentUp)
                        .Select(p => p.location?.top + p.location?.height * floatPercentUp).Max();
                }
                //var tmpMaxTop = lstNoProcessed.Where(p => p.rectangle?.top >= minTop && p.rectangle?.top + p.rectangle?.height <= maxTop)
                //    .Select(p => p.rectangle.top + p.rectangle.height).Max();
            }
            else
            {
                maxTop = top;
                //minTop = maxTop - height * 1.25;
                //minTop = Math.Max(0, minTop.Value);
                minTop = lstNoProcessed.Where(p => p.location?.top <= top && p.location?.top + p.location?.height >= top)
                    .Select(p => p.location?.top).Min();
                minTop = lstNoProcessed.Where(p => p.location?.top <= minTop && p.location?.top + p.location?.height >= minTop)
                    .Select(p => p.location?.top).Min();
            }
            minTop = Math.Max(0, minTop ?? 0d);

            lstTmp = lstNoProcessed.Where(p => p.location?.top >= minTop && p.location?.top <= maxTop).ToList();
            return lstTmp;
        }

        public static string ProcessJsonResult(string jsonTmp, List<object> lstArray, bool isAll = false)
        {
            foreach (var item in lstArray)
            {
                try
                {
                    if (Equals(item, "|^[]|"))
                    {
                        jsonTmp = string.Format("[{0}]", jsonTmp.Replace("[", "").Replace("]", "").TrimEnd(','));
                        continue;
                    }
                    jsonTmp = GetTokenByName(jsonTmp, item as string, isAll);
                    if (string.IsNullOrEmpty(jsonTmp))
                    {
                        jsonTmp = null;
                        break;
                    }
                    if (jsonTmp.EndsWith(",") && !jsonTmp.StartsWith("["))
                    {
                        jsonTmp = jsonTmp.TrimEnd(',').TrimStart(',');
                        if (!jsonTmp.StartsWith("{") || !jsonTmp.EndsWith("}"))
                        {
                            if (!jsonTmp.StartsWith("[") && !jsonTmp.EndsWith("]"))
                            {
                                jsonTmp = string.Format("\"{0}\"", jsonTmp.TrimEnd('\"').TrimStart('\"'));
                            }
                        }
                        jsonTmp = string.Format("[{0}]", jsonTmp);
                    }
                }
                catch (Exception oe)
                {
                    Console.WriteLine(oe.Message);
                }
            }

            return jsonTmp;
        }

        public static string GetTokenByArray(string json, string dataName, bool isAll = false)
        {
            var obj = JArray.Parse(json);
            string token = null;
            bool isMerge = dataName?.Equals("|Merge|") == true;
            foreach (var x in obj)
            {
                try
                {
                    if (x is JArray)
                    {
                        foreach (var y in x)
                        {
                            var tmpStr = isMerge ? y.ToString() : GetTokenByName(y.ToString(), dataName, isAll);
                            if (isAll)
                            {
                                token += tmpStr?.Trim().TrimStart('[').TrimEnd(']').Trim() + ",";
                            }
                            else
                            {
                                token = tmpStr;
                            }
                        }
                    }
                    else
                    {
                        var tmpStr = "";
                        if (x is JObject)
                        {
                            tmpStr = isMerge ? x.ToString() : GetTokenByName((x as JObject).ToString(), dataName, isAll);
                        }
                        else
                        {
                            tmpStr = x.ToString();
                        }
                        if (string.IsNullOrEmpty(tmpStr))
                        {
                            continue;
                        }
                        if (isAll)
                        {
                            token += tmpStr?.Trim().TrimStart('[').TrimEnd(']').Trim() + ",";
                        }
                        else
                        {
                            token = tmpStr;
                        }
                    }
                }
                catch (Exception oe)
                {
                    Console.WriteLine(oe.Message);
                }
                if (!isAll && token != null)
                {
                    break;
                }
            }
            return token?.Trim();
        }

        public static string GetTokenByName(string json, string dataName, bool isAll = false)
        {
            if (json.StartsWith("["))
            {
                return GetTokenByArray(json, dataName, isAll);
            }
            var obj = JObject.Parse(json);
            string token = null;
            bool isReplace = dataName.Contains("|R|");
            dataName = dataName.Replace("|R|", "");
            bool isMerge = dataName.Equals("|Merge|");
            bool isProcessOthers = obj.GetValue(dataName) == null;
            foreach (var x in obj)
            {
                if (isMerge)
                {
                    token += x.Value?.ToString().Trim() + ",";
                    continue;
                }
                if (x.Key.Equals(dataName))
                {
                    if (isReplace)
                    {
                        if (x.Value.GetType() == typeof(JArray))
                        {
                            var array = JArray.Parse(x.Value.ToString());
                            if (array?.Count > 0)
                            {
                                obj[x.Key] = array[0];
                            }
                        }
                        break;
                    }
                    else
                    {
                        token = x.Value.ToString();
                    }
                }
                else if (isProcessOthers)
                {
                    if (x.Value.GetType() == typeof(JObject))
                    {
                        token = GetTokenByName(x.Value.ToString(), dataName, isAll);
                    }
                    else if (x.Value.GetType() == typeof(JArray))
                    {
                        try
                        {
                            token = GetTokenByArray(x.Value.ToString(), dataName, isAll);
                        }
                        catch (Exception oe)
                        {
                            Console.WriteLine(oe.Message);
                        }
                    }
                }
                if (token != null)
                {
                    break;
                }
            }
            if (isReplace && token == null)
            {
                token = obj?.ToString();
            }
            return token?.Trim();
        }

        public static string GetPreResultByList(JToken token, List<object> lstSpilt)
        {
            string result = null;
            if (lstSpilt?.Count > 0)
            {
                //var body = JArray.Parse(JObject.Parse(html)["resultTables"][0]["resultCells"].ToString());
                for (int i = 0; i < lstSpilt.Count; i++)
                {
                    try
                    {
                        var item = lstSpilt[i].ToString();
                        if (lstSpilt[i] is string)
                        {
                            if (item.EndsWith("|S"))
                            {
                                item = item.Replace("|S", "");
                                token = JObject.Parse(token[item].ToString());
                            }
                            else if (item.Contains("|N|"))
                            {
                                var items = item.Split(new string[] { "|N|" }, StringSplitOptions.RemoveEmptyEntries);
                                if (items.Length > 1)
                                {
                                    token = JArray.Parse(token[items[0]].ToString());
                                    foreach (var row in token)
                                    {
                                        result += row[items[1]].ToString() + "\n";
                                    }
                                }
                                else if (items.Length == 1)
                                {
                                    string[] checkItems = null;
                                    //text|C|type=formula
                                    if (items[0].Contains("|C|"))
                                    {
                                        items = items[0].Split(new string[] { "|C|" }, StringSplitOptions.RemoveEmptyEntries);
                                        checkItems = items[1].Split(new string[] { "=" }, StringSplitOptions.RemoveEmptyEntries);
                                    }
                                    token = JArray.Parse(token.ToString());
                                    foreach (var row in token)
                                    {
                                        if (checkItems?.Length == 2 && row[checkItems[0]].ToString() == checkItems[1])
                                        {
                                            result += "$";
                                        }
                                        result += row[items[0]].ToString();
                                        if (checkItems?.Length == 2 && row[checkItems[0]].ToString() == checkItems[1])
                                        {
                                            result += "$";
                                        }
                                    }
                                }
                            }
                            //itemstring|$|
                            else if (item.Contains("|$|"))
                            {
                                item = item.Replace("|$|", "");
                                result = "$" + token[item].ToString() + "$";
                            }
                            else
                            {
                                token = token[item];
                            }
                        }
                        else if (lstSpilt[i] is int)
                        {
                            if (token.Count() > 0)
                            {
                                token = token[BoxUtil.GetInt32FromObject(item)];
                            }
                            else
                            {
                                break;
                            }
                        }
                        if (token != null && i == lstSpilt.Count - 1 && string.IsNullOrEmpty(result))
                        {
                            result = token.ToString();
                        }
                    }
                    catch (Exception oe)
                    {
                        Console.WriteLine(oe.Message);
                    }
                }
            }
            return result?.Trim();
        }
    }
}
