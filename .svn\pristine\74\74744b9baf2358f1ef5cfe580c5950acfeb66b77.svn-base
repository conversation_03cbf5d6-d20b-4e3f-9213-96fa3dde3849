﻿using System;
using System.Collections.Generic;

using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using ToolCommon;
using System.Data;

namespace Account.Web
{
    public partial class IPDetail : System.Web.UI.Page
    {
        protected void Page_Load(object sender, EventArgs e)
        {

        }

        private void LoadData(int day, string strNo)
        {
            DataTable dtTmp = null;
            #region 选择的时间
            List<int> lstTime = new List<int>();
            if (chk11.Checked)
            {
                lstTime.Add(11);
            }
            if (chk12.Checked)
            {
                lstTime.Add(12);
            }
            if (chk13.Checked)
            {
                lstTime.Add(13);
            }
            if (chk14.Checked)
            {
                lstTime.Add(14);
            }
            if (chk15.Checked)
            {
                lstTime.Add(15);
            }
            if (chk16.Checked)
            {
                lstTime.Add(16);
            }
            if (chk17.Checked)
            {
                lstTime.Add(17);
            }
            if (chk18.Checked)
            {
                lstTime.Add(18);
            }
            if (chk8.Checked)
            {
                lstTime.Add(8);
            }
            if (chk9.Checked)
            {
                lstTime.Add(9);
            }
            #endregion
            if (lstTime != null && lstTime.Count > 0)
            {
                if (string.IsNullOrEmpty(strNo))
                    dtTmp = TicketHelper.GetIPDetailByDate(day, lstTime);
                else
                    dtTmp = TicketHelper.GetIPDetailByNo(strNo, lstTime);
            }
            lblCount.Text = string.Format("共{0}条", dtTmp.Rows.Count);
            gvDataSource.DataSource = dtTmp;
            gvDataSource.DataBind();
        }

        protected void btnOK_Click(object sender, EventArgs e)
        {
            if (BoxUtil.GetStringFromObject(Request.QueryString["pwd"]) != CommonHelper.StrPWD)
                return;
            int nDay = BoxUtil.GetInt32FromObject(txtDate.Text, 0);
            string strNo = txtNo.Text.Trim();
            LoadData(nDay, strNo);
        }
    }
}