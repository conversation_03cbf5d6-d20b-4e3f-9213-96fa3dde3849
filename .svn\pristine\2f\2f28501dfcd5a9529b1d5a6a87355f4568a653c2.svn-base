﻿using System;

namespace CommonLib
{
    public class LocalWaitCache<T>
    {
        LocalMemoryCache<T> _localCache;

        public LocalWaitCache(string cacheDB, TimeSpan tsExpired, bool isDeleteAfterGet = true, bool isNeverExpired = false)
        {
            _localCache = new LocalMemoryCache<T>(cacheDB, tsExpired, isNeverExpired, isDeleteAfterGet);
        }

        public T WaitLock(TimeSpan? timeSpan, bool isGetBeforeWait = true)
        {
            return WaitLock(_localCache.CacheName, timeSpan, isGetBeforeWait);
        }

        public T WaitLock(string key, TimeSpan? timeSpan, bool isGetBeforeWait = true)
        {
            var result = default(T);
            //long start = ServerTime.DateTime.Ticks;
            try
            {
                if (isGetBeforeWait)
                    result = Get(key);
                if (result == null || Equals(result, default(T)))
                {
                    if (LocalWaitLock.WaitLock(_localCache.CacheName + "-" + key, timeSpan ?? _localCache.TsExpired))
                        result = Get(key);
                }
            }
            catch (Exception oe)
            {
                LogHelper.Log.Error(string.Format("WaitLock Error:{0}", key), oe);
            }
            //LogHelper.Log.Info(string.Format("WaitLock:{1}ms,Key:{2},Result:{0}", JsonConvert.SerializeObject(result), new TimeSpan(ServerTime.DateTime.Ticks - start).TotalMilliseconds.ToString("F0"), key));
            return result;
        }

        public void Increment(string objectId)
        {
            _localCache.Increment(objectId);
        }

        public void Decrement(string objectId)
        {
            _localCache.Decrement(objectId);
        }

        public T Get()
        {
            return _localCache.Get(_localCache.CacheName);
        }

        public T Get(string key)
        {
            return _localCache.Get(key);
        }

        public void Set(T value)
        {
            Set(_localCache.CacheName, value);
        }

        public void Set(string key, T value)
        {
            _localCache.Set(key, value);
            LocalWaitLock.Set(_localCache.CacheName + "-" + key);
        }

        public void Remove(string key)
        {
            _localCache.Remove(key);
        }

        public int KeysCount(DateTime dtMin)
        {
            return _localCache.KeysCount(dtMin);
        }
    }
}
