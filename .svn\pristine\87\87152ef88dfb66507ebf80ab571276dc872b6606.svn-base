{"DebugMode": true, "ConnectionStrings": {"DefaultConnection": "northwind.sqlite", "ChinookConnection": "chinook.sqlite"}, "oauth.RedirectUrl": "http://localhost:5000/", "oauth.CallbackUrl": "http://localhost:5000/auth/{0}", "oauth.facebook.Permissions": ["email", "user_location"], "oauth.facebook.Permissions2": "email,user_location", "oauth.facebook.AppId": "531608123577340", "oauth.facebook.AppSecret": "********************************", "oauth.twitter.ConsumerKey": "*************************", "oauth.twitter.ConsumerSecret": "WNeOT6YalxXDR4iWZjc4jVjFaydoDcY8jgRrGc5FVLjsVlY2Y8", "oauth.github.Scopes": ["user"], "oauth.github.ClientId": "********************", "oauth.github.ClientSecret": "868d2b4c7b1632d1b774d6209b1c6ae522fe1954", "oauth.microsoftgraph.AppId": "8208d98e-400d-4ce9-89ba-d92610c67e13", "oauth.microsoftgraph.AppSecret": "hsrMP46|_kfkcYCWSW516?%", "oauth.microsoftgraph.SavePhoto": "true", "oauth.microsoftgraph.SavePhotoSize": "96x96"}