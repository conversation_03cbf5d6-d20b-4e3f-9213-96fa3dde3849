﻿<?xml version="1.0" encoding="utf-8"?>

<log4net>
  <!--将日志保存到本地文件-->
  <appender name="InfoLogFileAppender" type="log4net.Appender.RollingFileAppender,log4net">
    <!--日志文件路径,按文件大小方式输出时在这里指定文件名，并且前面的日志按天在文件名后自动添加当天日期形成文件-->
    <param name="File" value="Logs\" />
    <!--是否追加到文件-->
    <param name="AppendToFile" value="true" />
    <!--记录日志写入文件时，不锁定文本文件-->
    <lockingModel type="log4net.Appender.FileAppender+MinimalLock" />
    <!--Unicode编码-->
    <Encoding value="UTF-8" />
    <!--最多产生的日志文件数，value="－1"为不限文件数-->
    <param name="MaxSizeRollBackups" value="-1" />
    <!--是否只写到一个文件中-->
    <param name="StaticLogFileName" value="false" />
    <!--按照何种方式产生多个日志文件(日期[Date],文件大小[Size],混合[Composite])-->
    <param name="RollingStyle" value="Date" />
    <!--按日期产生文件夹，文件名［在日期方式与混合方式下使用］-->
    <param name="DatePattern" value="yyyyMM/dd&quot;.log&quot;" />
    <!--每个文件的大小。只在混合方式与文件大小方式下使用，超出大小的在文件名后自动增加1重新命名-->
    <param name="maximumFileSize" value="5000KB" />
    <!--记录的格式。-->
    <layout type="log4net.Layout.PatternLayout">
      <!--<param name="ConversionPattern" value="%n%m%n"/>-->
      <param name="ConversionPattern" value="%d [%t] %-5p %c %n - %m%n" />
    </layout>
    <filter type="log4net.Filter.LevelRangeFilter">
      <param name="LevelMin" value="INFO" />
      <param name="LevelMax" value="INFO" />
    </filter>
  </appender>

  <appender name="ErrorLogFileAppender" type="log4net.Appender.RollingFileAppender,log4net">
    <!--日志文件路径,按文件大小方式输出时在这里指定文件名，并且前面的日志按天在文件名后自动添加当天日期形成文件-->
    <param name="File" value="Logs\" />
    <!--是否追加到文件-->
    <param name="AppendToFile" value="true" />
    <!--记录日志写入文件时，不锁定文本文件-->
    <lockingModel type="log4net.Appender.FileAppender+MinimalLock" />
    <!--Unicode编码-->
    <Encoding value="UTF-8" />
    <!--最多产生的日志文件数，value="－1"为不限文件数-->
    <param name="MaxSizeRollBackups" value="-1" />
    <!--是否只写到一个文件中-->
    <param name="StaticLogFileName" value="false" />
    <!--按照何种方式产生多个日志文件(日期[Date],文件大小[Size],混合[Composite])-->
    <param name="RollingStyle" value="Date" />
    <!--按日期产生文件夹，文件名［在日期方式与混合方式下使用］-->
    <param name="DatePattern" value="yyyyMM/dd&quot;-Exception.log&quot;" />
    <!--每个文件的大小。只在混合方式与文件大小方式下使用，超出大小的在文件名后自动增加1重新命名-->
    <param name="maximumFileSize" value="5000KB" />
    <!--记录的格式。-->
    <layout type="log4net.Layout.PatternLayout">
      <param name="ConversionPattern" value="%d [%t] %-5p %c %n - %m%n" />
    </layout>
    <filter type="log4net.Filter.LevelRangeFilter">
      <param name="LevelMin" value="ERROR" />
      <param name="LevelMax" value="ERROR" />
    </filter>
  </appender>

  <appender name="ConsoleAppender" type="log4net.Appender.ConsoleAppender">
    <layout type="log4net.Layout.PatternLayout">
      <param name="ConversionPattern" value="%n%m%n"/>
    </layout>
  </appender>

  <root>
    <!--根据日志的级别，来决定需要记录的日志,日志的级别主要有以下几类：ALL,DEBUG,INFO,WARN,RROR,FATAL,OFF-->
    <level value="ALL" />
    <appender-ref ref="InfoLogFileAppender" />
    <appender-ref ref="ErrorLogFileAppender" />
    <appender-ref ref="ConsoleAppender" />
  </root>

</log4net>