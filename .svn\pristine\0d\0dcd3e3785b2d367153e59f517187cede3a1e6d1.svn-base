﻿<%@ Page Language="C#" AutoEventWireup="true" %>

<%@ Import Namespace="CommonLib" %>

<html>
<head>
    <meta http-equiv="X-UA-Compatible" content="IE=Edge,chrome=1"/>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <meta name="robots" content="nofollow,noarchive">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <title>OCR助手版本介绍</title>
    <link rel="stylesheet" href="./CSS/bootstrap.min.css">
    <style type="text/css">
        .overlay {
            pointer-events: none;
        }

        html, table {
            font-size: 15px;
        }

        * {
            -webkit-user-select: text !important;
            -moz-user-select: text !important;
            -ms-user-select: text !important;
            user-select: text !important;
        }

        .card-header {
            background-color: #FAFAFA;
            font-weight: bold;
            text-align: center;
        }

        .card-deck {
            margin: 0 auto;
            max-width: 700px;
        }

        .alert {
            margin: 0 auto;
            max-width: 700px;
        }

        th {
            border: 1px solid #dee2e6;
        }

        td {
            border: 1px solid #dee2e6;
        }
    </style>
</head>
<body>
    <div class="card-body">
        <table class="table table-striped">
            <thead>
                <tr>
                    <th>功能</th>
                    <%
                        var lstUserTypes = UserTypeHelper.GetCanRegUserTypes();
                        lstUserTypes.Insert(0, UserTypeHelper.GetUserType(UserTypeEnum.体验版));
                        foreach (var item in lstUserTypes)
                        {%>
                    <th><%=item.Type.ToString() %></th>
                    <%
                        } %>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>截图</td>
                    <%foreach (var item in lstUserTypes)
                        {%>
                    <td>✔</td>
                    <%
                        } %>
                </tr>
                <tr>
                    <td>贴图</td>
                    <%foreach (var item in lstUserTypes)
                        {%>
                    <td>✔</td>
                    <%
                        } %>
                </tr>
                <tr>
                    <td>截图识别</td>
                    <%foreach (var item in lstUserTypes)
                        {%>
                    <td>✔</td>
                    <%
                        } %>
                </tr>
                <tr>
                <tr>
                    <td>文件识别</td>
                    <%foreach (var item in lstUserTypes)
                        {%>
                    <td><%=item.IsSupportImageFile?"✔":"✘" %></td>
                    <%
                        } %>
                </tr>
                <tr>
                    <td>区域识别</td>
                    <%foreach (var item in lstUserTypes)
                        {%>
                    <td><%=item.IsSupportVertical?"✔":"✘" %></td>
                    <%
                        } %>
                </tr>
                <tr>
                    <td>竖排识别</td>
                    <%foreach (var item in lstUserTypes)
                        {%>
                    <td><%=item.IsSupportVertical?"✔":"✘" %></td>
                    <%
                        } %>
                </tr>
                <tr>
                    <td>划词翻译</td>
                    <%foreach (var item in lstUserTypes)
                        {%>
                    <td><%=item.IsSupportTranslate?"✔":"✘" %></td>
                    <%
                        } %>
                </tr>
                <tr>
                    <td>图片翻译</td>
                    <%foreach (var item in lstUserTypes)
                        {%>
                    <td><%=item.IsSupportTranslate?"✔":"✘" %></td>
                    <%
                        } %>
                </tr>
                <tr>
                    <td>公式识别</td>
                    <%foreach (var item in lstUserTypes)
                        {%>
                    <td><%=item.IsSupportMath?"✔":"✘" %></td>
                    <%
                        } %>
                </tr>
                <tr>
                    <td>表格识别</td>
                    <%foreach (var item in lstUserTypes)
                        {%>
                    <td><%=item.IsSupportTable?"✔":"✘" %></td>
                    <%
                        } %>
                </tr>
                <tr>
                    <td>文档识别</td>
                    <%foreach (var item in lstUserTypes)
                        {%>
                    <td><%=item.IsSupportDocFile?"✔":"✘" %></td>
                    <%
                        } %>
                </tr>
                <tr>
                    <td>文档翻译</td>
                    <%foreach (var item in lstUserTypes)
                        {%>
                    <td><%=item.IsSupportDocFile?"✔":"✘" %></td>
                    <%
                        } %>
                </tr>
                <tr>
                    <td>批量识别</td>
                    <%foreach (var item in lstUserTypes)
                        {%>
                    <td><%=item.IsSupportBatch?"✔":"✘" %></td>
                    <%
                        } %>
                </tr>
                <tr>
                    <td>自选通道</td>
                    <%foreach (var item in lstUserTypes)
                        {%>
                    <td><%=item.IsSupportPassage?"✔":"✘" %></td>
                    <%
                        } %>
                </tr>
                <tr>
                    <td>本地识别</td>
                    <%foreach (var item in lstUserTypes)
                        {%>
                    <td><%=item.IsSupportLocalOcr?"✔":"✘" %></td>
                    <%
                        } %>
                </tr>
                <tr>
                    <td>多结果</td>
                    <%foreach (var item in lstUserTypes)
                        {%>
                    <td><%=item.IsSetOtherResult?"✔":"✘" %></td>
                    <%
                        } %>
                </tr>
                <tr>
                    <td>多设备</td>
                    <%foreach (var item in lstUserTypes)
                        {%>
                    <td><b><%=item.MaxLoginCount>1?item.MaxLoginCount.ToString():"✘" %></b></td>
                    <%
                        } %>
                </tr>
                <tr>
                    <td>识别频率</td>
                    <%foreach (var item in lstUserTypes)
                        {%>
                    <td><b><%=(item.PerTimeSpan>0? (item.PerTimeSpan / 1000).ToString("F0")+"秒/"+item.PerTimeSpanExecCount+"次":"-").Replace("/1次","/次") %></b></td>
                    <%
                        } %>
                </tr>
                <tr>
                    <td><b>每日限额</b></td>
                    <%foreach (var item in lstUserTypes)
                        {%>
                    <td><b><%=item.LimitPerTokenCount > 0 ? item.LimitPerTokenCount + "次": item.LimitPerDayCount > 0 ? item.LimitPerDayCount+"次":"✘" %></b></td>
                    <%
                        } %>
                </tr>
                <tr>
                    <td>技术支持</td>
                    <%foreach (var item in lstUserTypes)
                        {%>
                    <td><%=item.MaxLoginCount > 1?"✔":"✘" %></td>
                    <%
                        } %>
                </tr>
                <tr>
                    <td>需求定制</td>
                    <%foreach (var item in lstUserTypes)
                        {%>
                    <td><%=item.IsSupportTranslate?"✔":"✘" %></td>
                    <%
                        } %>
                </tr>
            </tbody>
        </table>
        <div class="alert alert-warning">
            <%
                var firstType = UserTypeHelper.GetUserType(UserTypeEnum.专业版); %>
            <span class="text-danger">关于【每日限额】</span>
            <br>
            虽然部分OCR平台有免费配额，但大部分高精度接口都是收费的。
            <br>
            <br>
            <b><%=firstType.Type.ToString() %>-日限额 <%=firstType.LimitPerDayCount %> 次，按3分钱/次计算</b>
            <br>
            <b>费用为 <%=firstType.LimitPerDayCount %> 次 * 0.03 元 * 30 天 * 12 月 ≈ <%=(firstType.LimitPerDayCount * 0.03 * 30 * 12).ToString("F0") %>元！</b>
            <br>
            <b>实际支付给OCR平台的成本已远远超过会员费……</b>
            <br>
            <br>
            为了服务不被滥用，更为了能持续稳定的给大家提供优质服务，
            <br>
            助手不得不做限流措施，望大家理解！
            <br>
            <br>
            <span class="text-danger">温馨提示</span><br>
            <b>根据需求选择版本，有特殊需求，可联系客服定制！</b>
            <br>
            <br>
        </div>
    </div>
</body>
</html>
