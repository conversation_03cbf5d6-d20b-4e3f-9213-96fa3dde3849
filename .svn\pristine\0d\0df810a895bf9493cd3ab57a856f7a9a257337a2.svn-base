// Code generated by Microsoft (R) AutoRest Code Generator 1.0.1.0
// Changes may cause incorrect behavior and will be lost if the code is
// regenerated.

namespace AutorestClient
{
    using Microsoft.Rest;
    using Models;
    using Newtonsoft.Json;
    using System.Collections;
    using System.Collections.Generic;
    using System.IO;
    using System.Net;
    using System.Net.Http;
    using System.Threading;
    using System.Threading.Tasks;

    /// <summary>
    /// Authenticate2 operations.
    /// </summary>
    public partial class Authenticate2 : IServiceOperations<ServiceStackAutorestClient>, IAuthenticate2
    {
        /// <summary>
        /// Initializes a new instance of the Authenticate2 class.
        /// </summary>
        /// <param name='client'>
        /// Reference to the service client.
        /// </param>
        /// <exception cref="System.ArgumentNullException">
        /// Thrown when a required parameter is null
        /// </exception>
        public Authenticate2(ServiceStackAutorestClient client)
        {
            if (client == null)
            {
                throw new System.ArgumentNullException("client");
            }
            Client = client;
        }

        /// <summary>
        /// Gets a reference to the ServiceStackAutorestClient
        /// </summary>
        public ServiceStackAutorestClient Client { get; private set; }

        /// <param name='provider'>
        /// </param>
        /// <param name='state'>
        /// </param>
        /// <param name='oauthToken'>
        /// </param>
        /// <param name='oauthVerifier'>
        /// </param>
        /// <param name='userName'>
        /// </param>
        /// <param name='password'>
        /// </param>
        /// <param name='rememberMe'>
        /// </param>
        /// <param name='continueParameter'>
        /// </param>
        /// <param name='nonce'>
        /// </param>
        /// <param name='uri'>
        /// </param>
        /// <param name='response'>
        /// </param>
        /// <param name='qop'>
        /// </param>
        /// <param name='nc'>
        /// </param>
        /// <param name='cnonce'>
        /// </param>
        /// <param name='useTokenCookie'>
        /// </param>
        /// <param name='accessToken'>
        /// </param>
        /// <param name='accessTokenSecret'>
        /// </param>
        /// <param name='meta'>
        /// </param>
        /// <param name='customHeaders'>
        /// Headers that will be added to request.
        /// </param>
        /// <param name='cancellationToken'>
        /// The cancellation token.
        /// </param>
        /// <exception cref="AuthenticateResponseException">
        /// Thrown when the operation returned an invalid status code
        /// </exception>
        /// <return>
        /// A response object containing the response body and response headers.
        /// </return>
        public async Task<HttpOperationResponse<AuthenticateResponse>> GetWithHttpMessagesAsync(string provider = default(string), string state = default(string), string oauthToken = default(string), string oauthVerifier = default(string), string userName = default(string), string password = default(string), bool? rememberMe = default(bool?), string continueParameter = default(string), string nonce = default(string), string uri = default(string), string response = default(string), string qop = default(string), string nc = default(string), string cnonce = default(string), bool? useTokenCookie = default(bool?), string accessToken = default(string), string accessTokenSecret = default(string), string meta = default(string), Dictionary<string, List<string>> customHeaders = null, CancellationToken cancellationToken = default(CancellationToken))
        {
            // Tracing
            bool _shouldTrace = ServiceClientTracing.IsEnabled;
            string _invocationId = null;
            if (_shouldTrace)
            {
                _invocationId = ServiceClientTracing.NextInvocationId.ToString();
                Dictionary<string, object> tracingParameters = new Dictionary<string, object>();
                tracingParameters.Add("provider", provider);
                tracingParameters.Add("state", state);
                tracingParameters.Add("oauthToken", oauthToken);
                tracingParameters.Add("oauthVerifier", oauthVerifier);
                tracingParameters.Add("userName", userName);
                tracingParameters.Add("password", password);
                tracingParameters.Add("rememberMe", rememberMe);
                tracingParameters.Add("continueParameter", continueParameter);
                tracingParameters.Add("nonce", nonce);
                tracingParameters.Add("uri", uri);
                tracingParameters.Add("response", response);
                tracingParameters.Add("qop", qop);
                tracingParameters.Add("nc", nc);
                tracingParameters.Add("cnonce", cnonce);
                tracingParameters.Add("useTokenCookie", useTokenCookie);
                tracingParameters.Add("accessToken", accessToken);
                tracingParameters.Add("accessTokenSecret", accessTokenSecret);
                tracingParameters.Add("meta", meta);
                tracingParameters.Add("cancellationToken", cancellationToken);
                ServiceClientTracing.Enter(_invocationId, this, "Get", tracingParameters);
            }
            // Construct URL
            var _baseUrl = Client.BaseUri.AbsoluteUri;
            var _url = new System.Uri(new System.Uri(_baseUrl + (_baseUrl.EndsWith("/") ? "" : "/")), "authenticate").ToString();
            List<string> _queryParameters = new List<string>();
            if (provider != null)
            {
                _queryParameters.Add(string.Format("provider={0}", System.Uri.EscapeDataString(provider)));
            }
            if (state != null)
            {
                _queryParameters.Add(string.Format("State={0}", System.Uri.EscapeDataString(state)));
            }
            if (oauthToken != null)
            {
                _queryParameters.Add(string.Format("oauth_token={0}", System.Uri.EscapeDataString(oauthToken)));
            }
            if (oauthVerifier != null)
            {
                _queryParameters.Add(string.Format("oauth_verifier={0}", System.Uri.EscapeDataString(oauthVerifier)));
            }
            if (userName != null)
            {
                _queryParameters.Add(string.Format("UserName={0}", System.Uri.EscapeDataString(userName)));
            }
            if (password != null)
            {
                _queryParameters.Add(string.Format("Password={0}", System.Uri.EscapeDataString(password)));
            }
            if (rememberMe != null)
            {
                _queryParameters.Add(string.Format("RememberMe={0}", System.Uri.EscapeDataString(Microsoft.Rest.Serialization.SafeJsonConvert.SerializeObject(rememberMe, Client.SerializationSettings).Trim('"'))));
            }
            if (continueParameter != null)
            {
                _queryParameters.Add(string.Format("Continue={0}", System.Uri.EscapeDataString(continueParameter)));
            }
            if (nonce != null)
            {
                _queryParameters.Add(string.Format("nonce={0}", System.Uri.EscapeDataString(nonce)));
            }
            if (uri != null)
            {
                _queryParameters.Add(string.Format("uri={0}", System.Uri.EscapeDataString(uri)));
            }
            if (response != null)
            {
                _queryParameters.Add(string.Format("response={0}", System.Uri.EscapeDataString(response)));
            }
            if (qop != null)
            {
                _queryParameters.Add(string.Format("qop={0}", System.Uri.EscapeDataString(qop)));
            }
            if (nc != null)
            {
                _queryParameters.Add(string.Format("nc={0}", System.Uri.EscapeDataString(nc)));
            }
            if (cnonce != null)
            {
                _queryParameters.Add(string.Format("cnonce={0}", System.Uri.EscapeDataString(cnonce)));
            }
            if (useTokenCookie != null)
            {
                _queryParameters.Add(string.Format("UseTokenCookie={0}", System.Uri.EscapeDataString(Microsoft.Rest.Serialization.SafeJsonConvert.SerializeObject(useTokenCookie, Client.SerializationSettings).Trim('"'))));
            }
            if (accessToken != null)
            {
                _queryParameters.Add(string.Format("AccessToken={0}", System.Uri.EscapeDataString(accessToken)));
            }
            if (accessTokenSecret != null)
            {
                _queryParameters.Add(string.Format("AccessTokenSecret={0}", System.Uri.EscapeDataString(accessTokenSecret)));
            }
            if (meta != null)
            {
                _queryParameters.Add(string.Format("Meta={0}", System.Uri.EscapeDataString(meta)));
            }
            if (_queryParameters.Count > 0)
            {
                _url += "?" + string.Join("&", _queryParameters);
            }
            // Create HTTP transport objects
            var _httpRequest = new HttpRequestMessage();
            HttpResponseMessage _httpResponse = null;
            _httpRequest.Method = new HttpMethod("GET");
            _httpRequest.RequestUri = new System.Uri(_url);
            // Set Headers
            if (Client.Accept != null)
            {
                if (_httpRequest.Headers.Contains("Accept"))
                {
                    _httpRequest.Headers.Remove("Accept");
                }
                _httpRequest.Headers.TryAddWithoutValidation("Accept", Client.Accept);
            }


            if (customHeaders != null)
            {
                foreach(var _header in customHeaders)
                {
                    if (_httpRequest.Headers.Contains(_header.Key))
                    {
                        _httpRequest.Headers.Remove(_header.Key);
                    }
                    _httpRequest.Headers.TryAddWithoutValidation(_header.Key, _header.Value);
                }
            }

            // Serialize Request
            string _requestContent = null;
            // Send Request
            if (_shouldTrace)
            {
                ServiceClientTracing.SendRequest(_invocationId, _httpRequest);
            }
            cancellationToken.ThrowIfCancellationRequested();
            _httpResponse = await Client.HttpClient.SendAsync(_httpRequest, cancellationToken).ConfigureAwait(false);
            if (_shouldTrace)
            {
                ServiceClientTracing.ReceiveResponse(_invocationId, _httpResponse);
            }
            HttpStatusCode _statusCode = _httpResponse.StatusCode;
            cancellationToken.ThrowIfCancellationRequested();
            string _responseContent = null;
            if (!_httpResponse.IsSuccessStatusCode)
            {
                var ex = new AuthenticateResponseException(string.Format("Operation returned an invalid status code '{0}'", _statusCode));
                try
                {
                    _responseContent = await _httpResponse.Content.ReadAsStringAsync().ConfigureAwait(false);
                    AuthenticateResponse _errorBody =  Microsoft.Rest.Serialization.SafeJsonConvert.DeserializeObject<AuthenticateResponse>(_responseContent, Client.DeserializationSettings);
                    if (_errorBody != null)
                    {
                        ex.Body = _errorBody;
                    }
                }
                catch (JsonException)
                {
                    // Ignore the exception
                }
                ex.Request = new HttpRequestMessageWrapper(_httpRequest, _requestContent);
                ex.Response = new HttpResponseMessageWrapper(_httpResponse, _responseContent);
                if (_shouldTrace)
                {
                    ServiceClientTracing.Error(_invocationId, ex);
                }
                _httpRequest.Dispose();
                if (_httpResponse != null)
                {
                    _httpResponse.Dispose();
                }
                throw ex;
            }
            // Create Result
            var _result = new HttpOperationResponse<AuthenticateResponse>();
            _result.Request = _httpRequest;
            _result.Response = _httpResponse;
            string _defaultResponseContent = await _httpResponse.Content.ReadAsStringAsync().ConfigureAwait(false);
            try
            {
                _result.Body = Microsoft.Rest.Serialization.SafeJsonConvert.DeserializeObject<AuthenticateResponse>(_defaultResponseContent, Client.DeserializationSettings);
            }
            catch (JsonException ex)
            {
                _httpRequest.Dispose();
                if (_httpResponse != null)
                {
                    _httpResponse.Dispose();
                }
                throw new SerializationException("Unable to deserialize the response.", _defaultResponseContent, ex);
            }
            if (_shouldTrace)
            {
                ServiceClientTracing.Exit(_invocationId, _result);
            }
            return _result;
        }

        /// <param name='provider'>
        /// </param>
        /// <param name='state'>
        /// </param>
        /// <param name='oauthToken'>
        /// </param>
        /// <param name='oauthVerifier'>
        /// </param>
        /// <param name='userName'>
        /// </param>
        /// <param name='password'>
        /// </param>
        /// <param name='rememberMe'>
        /// </param>
        /// <param name='continueParameter'>
        /// </param>
        /// <param name='nonce'>
        /// </param>
        /// <param name='uri'>
        /// </param>
        /// <param name='response'>
        /// </param>
        /// <param name='qop'>
        /// </param>
        /// <param name='nc'>
        /// </param>
        /// <param name='cnonce'>
        /// </param>
        /// <param name='useTokenCookie'>
        /// </param>
        /// <param name='accessToken'>
        /// </param>
        /// <param name='accessTokenSecret'>
        /// </param>
        /// <param name='meta'>
        /// </param>
        /// <param name='body'>
        /// </param>
        /// <param name='customHeaders'>
        /// Headers that will be added to request.
        /// </param>
        /// <param name='cancellationToken'>
        /// The cancellation token.
        /// </param>
        /// <exception cref="AuthenticateResponseException">
        /// Thrown when the operation returned an invalid status code
        /// </exception>
        /// <return>
        /// A response object containing the response body and response headers.
        /// </return>
        public async Task<HttpOperationResponse<AuthenticateResponse>> CreateWithHttpMessagesAsync(string provider = default(string), string state = default(string), string oauthToken = default(string), string oauthVerifier = default(string), string userName = default(string), string password = default(string), bool? rememberMe = default(bool?), string continueParameter = default(string), string nonce = default(string), string uri = default(string), string response = default(string), string qop = default(string), string nc = default(string), string cnonce = default(string), bool? useTokenCookie = default(bool?), string accessToken = default(string), string accessTokenSecret = default(string), string meta = default(string), Authenticate body = default(Authenticate), Dictionary<string, List<string>> customHeaders = null, CancellationToken cancellationToken = default(CancellationToken))
        {
            // Tracing
            bool _shouldTrace = ServiceClientTracing.IsEnabled;
            string _invocationId = null;
            if (_shouldTrace)
            {
                _invocationId = ServiceClientTracing.NextInvocationId.ToString();
                Dictionary<string, object> tracingParameters = new Dictionary<string, object>();
                tracingParameters.Add("provider", provider);
                tracingParameters.Add("state", state);
                tracingParameters.Add("oauthToken", oauthToken);
                tracingParameters.Add("oauthVerifier", oauthVerifier);
                tracingParameters.Add("userName", userName);
                tracingParameters.Add("password", password);
                tracingParameters.Add("rememberMe", rememberMe);
                tracingParameters.Add("continueParameter", continueParameter);
                tracingParameters.Add("nonce", nonce);
                tracingParameters.Add("uri", uri);
                tracingParameters.Add("response", response);
                tracingParameters.Add("qop", qop);
                tracingParameters.Add("nc", nc);
                tracingParameters.Add("cnonce", cnonce);
                tracingParameters.Add("useTokenCookie", useTokenCookie);
                tracingParameters.Add("accessToken", accessToken);
                tracingParameters.Add("accessTokenSecret", accessTokenSecret);
                tracingParameters.Add("meta", meta);
                tracingParameters.Add("body", body);
                tracingParameters.Add("cancellationToken", cancellationToken);
                ServiceClientTracing.Enter(_invocationId, this, "Create", tracingParameters);
            }
            // Construct URL
            var _baseUrl = Client.BaseUri.AbsoluteUri;
            var _url = new System.Uri(new System.Uri(_baseUrl + (_baseUrl.EndsWith("/") ? "" : "/")), "authenticate").ToString();
            // Create HTTP transport objects
            var _httpRequest = new HttpRequestMessage();
            HttpResponseMessage _httpResponse = null;
            _httpRequest.Method = new HttpMethod("PUT");
            _httpRequest.RequestUri = new System.Uri(_url);
            // Set Headers
            if (Client.Accept != null)
            {
                if (_httpRequest.Headers.Contains("Accept"))
                {
                    _httpRequest.Headers.Remove("Accept");
                }
                _httpRequest.Headers.TryAddWithoutValidation("Accept", Client.Accept);
            }


            if (customHeaders != null)
            {
                foreach(var _header in customHeaders)
                {
                    if (_httpRequest.Headers.Contains(_header.Key))
                    {
                        _httpRequest.Headers.Remove(_header.Key);
                    }
                    _httpRequest.Headers.TryAddWithoutValidation(_header.Key, _header.Value);
                }
            }

            // Serialize Request
            string _requestContent = null;
            if(body != null)
            {
                _requestContent = Microsoft.Rest.Serialization.SafeJsonConvert.SerializeObject(body, Client.SerializationSettings);
                _httpRequest.Content = new StringContent(_requestContent, System.Text.Encoding.UTF8);
                _httpRequest.Content.Headers.ContentType =System.Net.Http.Headers.MediaTypeHeaderValue.Parse("application/json; charset=utf-8");
            }
            // Send Request
            if (_shouldTrace)
            {
                ServiceClientTracing.SendRequest(_invocationId, _httpRequest);
            }
            cancellationToken.ThrowIfCancellationRequested();
            _httpResponse = await Client.HttpClient.SendAsync(_httpRequest, cancellationToken).ConfigureAwait(false);
            if (_shouldTrace)
            {
                ServiceClientTracing.ReceiveResponse(_invocationId, _httpResponse);
            }
            HttpStatusCode _statusCode = _httpResponse.StatusCode;
            cancellationToken.ThrowIfCancellationRequested();
            string _responseContent = null;
            if (!_httpResponse.IsSuccessStatusCode)
            {
                var ex = new AuthenticateResponseException(string.Format("Operation returned an invalid status code '{0}'", _statusCode));
                try
                {
                    _responseContent = await _httpResponse.Content.ReadAsStringAsync().ConfigureAwait(false);
                    AuthenticateResponse _errorBody =  Microsoft.Rest.Serialization.SafeJsonConvert.DeserializeObject<AuthenticateResponse>(_responseContent, Client.DeserializationSettings);
                    if (_errorBody != null)
                    {
                        ex.Body = _errorBody;
                    }
                }
                catch (JsonException)
                {
                    // Ignore the exception
                }
                ex.Request = new HttpRequestMessageWrapper(_httpRequest, _requestContent);
                ex.Response = new HttpResponseMessageWrapper(_httpResponse, _responseContent);
                if (_shouldTrace)
                {
                    ServiceClientTracing.Error(_invocationId, ex);
                }
                _httpRequest.Dispose();
                if (_httpResponse != null)
                {
                    _httpResponse.Dispose();
                }
                throw ex;
            }
            // Create Result
            var _result = new HttpOperationResponse<AuthenticateResponse>();
            _result.Request = _httpRequest;
            _result.Response = _httpResponse;
            string _defaultResponseContent = await _httpResponse.Content.ReadAsStringAsync().ConfigureAwait(false);
            try
            {
                _result.Body = Microsoft.Rest.Serialization.SafeJsonConvert.DeserializeObject<AuthenticateResponse>(_defaultResponseContent, Client.DeserializationSettings);
            }
            catch (JsonException ex)
            {
                _httpRequest.Dispose();
                if (_httpResponse != null)
                {
                    _httpResponse.Dispose();
                }
                throw new SerializationException("Unable to deserialize the response.", _defaultResponseContent, ex);
            }
            if (_shouldTrace)
            {
                ServiceClientTracing.Exit(_invocationId, _result);
            }
            return _result;
        }

        /// <param name='provider'>
        /// </param>
        /// <param name='state'>
        /// </param>
        /// <param name='oauthToken'>
        /// </param>
        /// <param name='oauthVerifier'>
        /// </param>
        /// <param name='userName'>
        /// </param>
        /// <param name='password'>
        /// </param>
        /// <param name='rememberMe'>
        /// </param>
        /// <param name='continueParameter'>
        /// </param>
        /// <param name='nonce'>
        /// </param>
        /// <param name='uri'>
        /// </param>
        /// <param name='response'>
        /// </param>
        /// <param name='qop'>
        /// </param>
        /// <param name='nc'>
        /// </param>
        /// <param name='cnonce'>
        /// </param>
        /// <param name='useTokenCookie'>
        /// </param>
        /// <param name='accessToken'>
        /// </param>
        /// <param name='accessTokenSecret'>
        /// </param>
        /// <param name='meta'>
        /// </param>
        /// <param name='body'>
        /// </param>
        /// <param name='customHeaders'>
        /// Headers that will be added to request.
        /// </param>
        /// <param name='cancellationToken'>
        /// The cancellation token.
        /// </param>
        /// <exception cref="AuthenticateResponseException">
        /// Thrown when the operation returned an invalid status code
        /// </exception>
        /// <return>
        /// A response object containing the response body and response headers.
        /// </return>
        public async Task<HttpOperationResponse<AuthenticateResponse>> PostWithHttpMessagesAsync(string provider = default(string), string state = default(string), string oauthToken = default(string), string oauthVerifier = default(string), string userName = default(string), string password = default(string), bool? rememberMe = default(bool?), string continueParameter = default(string), string nonce = default(string), string uri = default(string), string response = default(string), string qop = default(string), string nc = default(string), string cnonce = default(string), bool? useTokenCookie = default(bool?), string accessToken = default(string), string accessTokenSecret = default(string), string meta = default(string), Authenticate body = default(Authenticate), Dictionary<string, List<string>> customHeaders = null, CancellationToken cancellationToken = default(CancellationToken))
        {
            // Tracing
            bool _shouldTrace = ServiceClientTracing.IsEnabled;
            string _invocationId = null;
            if (_shouldTrace)
            {
                _invocationId = ServiceClientTracing.NextInvocationId.ToString();
                Dictionary<string, object> tracingParameters = new Dictionary<string, object>();
                tracingParameters.Add("provider", provider);
                tracingParameters.Add("state", state);
                tracingParameters.Add("oauthToken", oauthToken);
                tracingParameters.Add("oauthVerifier", oauthVerifier);
                tracingParameters.Add("userName", userName);
                tracingParameters.Add("password", password);
                tracingParameters.Add("rememberMe", rememberMe);
                tracingParameters.Add("continueParameter", continueParameter);
                tracingParameters.Add("nonce", nonce);
                tracingParameters.Add("uri", uri);
                tracingParameters.Add("response", response);
                tracingParameters.Add("qop", qop);
                tracingParameters.Add("nc", nc);
                tracingParameters.Add("cnonce", cnonce);
                tracingParameters.Add("useTokenCookie", useTokenCookie);
                tracingParameters.Add("accessToken", accessToken);
                tracingParameters.Add("accessTokenSecret", accessTokenSecret);
                tracingParameters.Add("meta", meta);
                tracingParameters.Add("body", body);
                tracingParameters.Add("cancellationToken", cancellationToken);
                ServiceClientTracing.Enter(_invocationId, this, "Post", tracingParameters);
            }
            // Construct URL
            var _baseUrl = Client.BaseUri.AbsoluteUri;
            var _url = new System.Uri(new System.Uri(_baseUrl + (_baseUrl.EndsWith("/") ? "" : "/")), "authenticate").ToString();
            // Create HTTP transport objects
            var _httpRequest = new HttpRequestMessage();
            HttpResponseMessage _httpResponse = null;
            _httpRequest.Method = new HttpMethod("POST");
            _httpRequest.RequestUri = new System.Uri(_url);
            // Set Headers
            if (Client.Accept != null)
            {
                if (_httpRequest.Headers.Contains("Accept"))
                {
                    _httpRequest.Headers.Remove("Accept");
                }
                _httpRequest.Headers.TryAddWithoutValidation("Accept", Client.Accept);
            }


            if (customHeaders != null)
            {
                foreach(var _header in customHeaders)
                {
                    if (_httpRequest.Headers.Contains(_header.Key))
                    {
                        _httpRequest.Headers.Remove(_header.Key);
                    }
                    _httpRequest.Headers.TryAddWithoutValidation(_header.Key, _header.Value);
                }
            }

            // Serialize Request
            string _requestContent = null;
            if(body != null)
            {
                _requestContent = Microsoft.Rest.Serialization.SafeJsonConvert.SerializeObject(body, Client.SerializationSettings);
                _httpRequest.Content = new StringContent(_requestContent, System.Text.Encoding.UTF8);
                _httpRequest.Content.Headers.ContentType =System.Net.Http.Headers.MediaTypeHeaderValue.Parse("application/json; charset=utf-8");
            }
            // Send Request
            if (_shouldTrace)
            {
                ServiceClientTracing.SendRequest(_invocationId, _httpRequest);
            }
            cancellationToken.ThrowIfCancellationRequested();
            _httpResponse = await Client.HttpClient.SendAsync(_httpRequest, cancellationToken).ConfigureAwait(false);
            if (_shouldTrace)
            {
                ServiceClientTracing.ReceiveResponse(_invocationId, _httpResponse);
            }
            HttpStatusCode _statusCode = _httpResponse.StatusCode;
            cancellationToken.ThrowIfCancellationRequested();
            string _responseContent = null;
            if (!_httpResponse.IsSuccessStatusCode)
            {
                var ex = new AuthenticateResponseException(string.Format("Operation returned an invalid status code '{0}'", _statusCode));
                try
                {
                    _responseContent = await _httpResponse.Content.ReadAsStringAsync().ConfigureAwait(false);
                    AuthenticateResponse _errorBody =  Microsoft.Rest.Serialization.SafeJsonConvert.DeserializeObject<AuthenticateResponse>(_responseContent, Client.DeserializationSettings);
                    if (_errorBody != null)
                    {
                        ex.Body = _errorBody;
                    }
                }
                catch (JsonException)
                {
                    // Ignore the exception
                }
                ex.Request = new HttpRequestMessageWrapper(_httpRequest, _requestContent);
                ex.Response = new HttpResponseMessageWrapper(_httpResponse, _responseContent);
                if (_shouldTrace)
                {
                    ServiceClientTracing.Error(_invocationId, ex);
                }
                _httpRequest.Dispose();
                if (_httpResponse != null)
                {
                    _httpResponse.Dispose();
                }
                throw ex;
            }
            // Create Result
            var _result = new HttpOperationResponse<AuthenticateResponse>();
            _result.Request = _httpRequest;
            _result.Response = _httpResponse;
            string _defaultResponseContent = await _httpResponse.Content.ReadAsStringAsync().ConfigureAwait(false);
            try
            {
                _result.Body = Microsoft.Rest.Serialization.SafeJsonConvert.DeserializeObject<AuthenticateResponse>(_defaultResponseContent, Client.DeserializationSettings);
            }
            catch (JsonException ex)
            {
                _httpRequest.Dispose();
                if (_httpResponse != null)
                {
                    _httpResponse.Dispose();
                }
                throw new SerializationException("Unable to deserialize the response.", _defaultResponseContent, ex);
            }
            if (_shouldTrace)
            {
                ServiceClientTracing.Exit(_invocationId, _result);
            }
            return _result;
        }

        /// <param name='provider'>
        /// </param>
        /// <param name='state'>
        /// </param>
        /// <param name='oauthToken'>
        /// </param>
        /// <param name='oauthVerifier'>
        /// </param>
        /// <param name='userName'>
        /// </param>
        /// <param name='password'>
        /// </param>
        /// <param name='rememberMe'>
        /// </param>
        /// <param name='continueParameter'>
        /// </param>
        /// <param name='nonce'>
        /// </param>
        /// <param name='uri'>
        /// </param>
        /// <param name='response'>
        /// </param>
        /// <param name='qop'>
        /// </param>
        /// <param name='nc'>
        /// </param>
        /// <param name='cnonce'>
        /// </param>
        /// <param name='useTokenCookie'>
        /// </param>
        /// <param name='accessToken'>
        /// </param>
        /// <param name='accessTokenSecret'>
        /// </param>
        /// <param name='meta'>
        /// </param>
        /// <param name='customHeaders'>
        /// Headers that will be added to request.
        /// </param>
        /// <param name='cancellationToken'>
        /// The cancellation token.
        /// </param>
        /// <exception cref="AuthenticateResponseException">
        /// Thrown when the operation returned an invalid status code
        /// </exception>
        /// <return>
        /// A response object containing the response body and response headers.
        /// </return>
        public async Task<HttpOperationResponse<AuthenticateResponse>> DeleteWithHttpMessagesAsync(string provider = default(string), string state = default(string), string oauthToken = default(string), string oauthVerifier = default(string), string userName = default(string), string password = default(string), bool? rememberMe = default(bool?), string continueParameter = default(string), string nonce = default(string), string uri = default(string), string response = default(string), string qop = default(string), string nc = default(string), string cnonce = default(string), bool? useTokenCookie = default(bool?), string accessToken = default(string), string accessTokenSecret = default(string), string meta = default(string), Dictionary<string, List<string>> customHeaders = null, CancellationToken cancellationToken = default(CancellationToken))
        {
            // Tracing
            bool _shouldTrace = ServiceClientTracing.IsEnabled;
            string _invocationId = null;
            if (_shouldTrace)
            {
                _invocationId = ServiceClientTracing.NextInvocationId.ToString();
                Dictionary<string, object> tracingParameters = new Dictionary<string, object>();
                tracingParameters.Add("provider", provider);
                tracingParameters.Add("state", state);
                tracingParameters.Add("oauthToken", oauthToken);
                tracingParameters.Add("oauthVerifier", oauthVerifier);
                tracingParameters.Add("userName", userName);
                tracingParameters.Add("password", password);
                tracingParameters.Add("rememberMe", rememberMe);
                tracingParameters.Add("continueParameter", continueParameter);
                tracingParameters.Add("nonce", nonce);
                tracingParameters.Add("uri", uri);
                tracingParameters.Add("response", response);
                tracingParameters.Add("qop", qop);
                tracingParameters.Add("nc", nc);
                tracingParameters.Add("cnonce", cnonce);
                tracingParameters.Add("useTokenCookie", useTokenCookie);
                tracingParameters.Add("accessToken", accessToken);
                tracingParameters.Add("accessTokenSecret", accessTokenSecret);
                tracingParameters.Add("meta", meta);
                tracingParameters.Add("cancellationToken", cancellationToken);
                ServiceClientTracing.Enter(_invocationId, this, "Delete", tracingParameters);
            }
            // Construct URL
            var _baseUrl = Client.BaseUri.AbsoluteUri;
            var _url = new System.Uri(new System.Uri(_baseUrl + (_baseUrl.EndsWith("/") ? "" : "/")), "authenticate").ToString();
            List<string> _queryParameters = new List<string>();
            if (provider != null)
            {
                _queryParameters.Add(string.Format("provider={0}", System.Uri.EscapeDataString(provider)));
            }
            if (state != null)
            {
                _queryParameters.Add(string.Format("State={0}", System.Uri.EscapeDataString(state)));
            }
            if (oauthToken != null)
            {
                _queryParameters.Add(string.Format("oauth_token={0}", System.Uri.EscapeDataString(oauthToken)));
            }
            if (oauthVerifier != null)
            {
                _queryParameters.Add(string.Format("oauth_verifier={0}", System.Uri.EscapeDataString(oauthVerifier)));
            }
            if (userName != null)
            {
                _queryParameters.Add(string.Format("UserName={0}", System.Uri.EscapeDataString(userName)));
            }
            if (password != null)
            {
                _queryParameters.Add(string.Format("Password={0}", System.Uri.EscapeDataString(password)));
            }
            if (rememberMe != null)
            {
                _queryParameters.Add(string.Format("RememberMe={0}", System.Uri.EscapeDataString(Microsoft.Rest.Serialization.SafeJsonConvert.SerializeObject(rememberMe, Client.SerializationSettings).Trim('"'))));
            }
            if (continueParameter != null)
            {
                _queryParameters.Add(string.Format("Continue={0}", System.Uri.EscapeDataString(continueParameter)));
            }
            if (nonce != null)
            {
                _queryParameters.Add(string.Format("nonce={0}", System.Uri.EscapeDataString(nonce)));
            }
            if (uri != null)
            {
                _queryParameters.Add(string.Format("uri={0}", System.Uri.EscapeDataString(uri)));
            }
            if (response != null)
            {
                _queryParameters.Add(string.Format("response={0}", System.Uri.EscapeDataString(response)));
            }
            if (qop != null)
            {
                _queryParameters.Add(string.Format("qop={0}", System.Uri.EscapeDataString(qop)));
            }
            if (nc != null)
            {
                _queryParameters.Add(string.Format("nc={0}", System.Uri.EscapeDataString(nc)));
            }
            if (cnonce != null)
            {
                _queryParameters.Add(string.Format("cnonce={0}", System.Uri.EscapeDataString(cnonce)));
            }
            if (useTokenCookie != null)
            {
                _queryParameters.Add(string.Format("UseTokenCookie={0}", System.Uri.EscapeDataString(Microsoft.Rest.Serialization.SafeJsonConvert.SerializeObject(useTokenCookie, Client.SerializationSettings).Trim('"'))));
            }
            if (accessToken != null)
            {
                _queryParameters.Add(string.Format("AccessToken={0}", System.Uri.EscapeDataString(accessToken)));
            }
            if (accessTokenSecret != null)
            {
                _queryParameters.Add(string.Format("AccessTokenSecret={0}", System.Uri.EscapeDataString(accessTokenSecret)));
            }
            if (meta != null)
            {
                _queryParameters.Add(string.Format("Meta={0}", System.Uri.EscapeDataString(meta)));
            }
            if (_queryParameters.Count > 0)
            {
                _url += "?" + string.Join("&", _queryParameters);
            }
            // Create HTTP transport objects
            var _httpRequest = new HttpRequestMessage();
            HttpResponseMessage _httpResponse = null;
            _httpRequest.Method = new HttpMethod("DELETE");
            _httpRequest.RequestUri = new System.Uri(_url);
            // Set Headers
            if (Client.Accept != null)
            {
                if (_httpRequest.Headers.Contains("Accept"))
                {
                    _httpRequest.Headers.Remove("Accept");
                }
                _httpRequest.Headers.TryAddWithoutValidation("Accept", Client.Accept);
            }


            if (customHeaders != null)
            {
                foreach(var _header in customHeaders)
                {
                    if (_httpRequest.Headers.Contains(_header.Key))
                    {
                        _httpRequest.Headers.Remove(_header.Key);
                    }
                    _httpRequest.Headers.TryAddWithoutValidation(_header.Key, _header.Value);
                }
            }

            // Serialize Request
            string _requestContent = null;
            // Send Request
            if (_shouldTrace)
            {
                ServiceClientTracing.SendRequest(_invocationId, _httpRequest);
            }
            cancellationToken.ThrowIfCancellationRequested();
            _httpResponse = await Client.HttpClient.SendAsync(_httpRequest, cancellationToken).ConfigureAwait(false);
            if (_shouldTrace)
            {
                ServiceClientTracing.ReceiveResponse(_invocationId, _httpResponse);
            }
            HttpStatusCode _statusCode = _httpResponse.StatusCode;
            cancellationToken.ThrowIfCancellationRequested();
            string _responseContent = null;
            if (!_httpResponse.IsSuccessStatusCode)
            {
                var ex = new AuthenticateResponseException(string.Format("Operation returned an invalid status code '{0}'", _statusCode));
                try
                {
                    _responseContent = await _httpResponse.Content.ReadAsStringAsync().ConfigureAwait(false);
                    AuthenticateResponse _errorBody =  Microsoft.Rest.Serialization.SafeJsonConvert.DeserializeObject<AuthenticateResponse>(_responseContent, Client.DeserializationSettings);
                    if (_errorBody != null)
                    {
                        ex.Body = _errorBody;
                    }
                }
                catch (JsonException)
                {
                    // Ignore the exception
                }
                ex.Request = new HttpRequestMessageWrapper(_httpRequest, _requestContent);
                ex.Response = new HttpResponseMessageWrapper(_httpResponse, _responseContent);
                if (_shouldTrace)
                {
                    ServiceClientTracing.Error(_invocationId, ex);
                }
                _httpRequest.Dispose();
                if (_httpResponse != null)
                {
                    _httpResponse.Dispose();
                }
                throw ex;
            }
            // Create Result
            var _result = new HttpOperationResponse<AuthenticateResponse>();
            _result.Request = _httpRequest;
            _result.Response = _httpResponse;
            string _defaultResponseContent = await _httpResponse.Content.ReadAsStringAsync().ConfigureAwait(false);
            try
            {
                _result.Body = Microsoft.Rest.Serialization.SafeJsonConvert.DeserializeObject<AuthenticateResponse>(_defaultResponseContent, Client.DeserializationSettings);
            }
            catch (JsonException ex)
            {
                _httpRequest.Dispose();
                if (_httpResponse != null)
                {
                    _httpResponse.Dispose();
                }
                throw new SerializationException("Unable to deserialize the response.", _defaultResponseContent, ex);
            }
            if (_shouldTrace)
            {
                ServiceClientTracing.Exit(_invocationId, _result);
            }
            return _result;
        }

    }
}
