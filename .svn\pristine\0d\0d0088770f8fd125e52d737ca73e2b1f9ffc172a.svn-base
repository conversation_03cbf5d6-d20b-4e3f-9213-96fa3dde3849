﻿using CommonLib;
using System;
using System.Collections.Generic;
using System.Collections.Specialized;
using System.IO;
using System.Security.Cryptography;
using System.Text;
using System.Web;

namespace DocOcr
{
    /// <summary>
    /// 有道翻译
    /// http://fanyi.youdao.com/
    /// </summary>
    public class YouDaoRec : BaseDocOcrRec
    {
        public YouDaoRec()
        {
            OcrGroup = OcrGroupType.有道;
            OcrType = DocOcrType.有道翻译;
            MaxExecPerTime = 21;

            LstJsonPreProcessArray = new List<object>() { "body" };

            LstJsonNextProcessArray = new List<object>() { "trans" };

            StrResultJsonSpilt = "org";
            StrResultTransJsonSpilt = "tran";

            AllowUploadFileTypes = new List<string>() { "pdf", "doc", "docx", "ppt", "pptx" };//, "txt" 
            IsSupportTrans = true;

            //State = EnableState.禁用;
            InitLanguage();
        }

        #region 支持的语言

        private void InitLanguage()
        {
            TransLanguageDic = new Dictionary<TransLanguageTypeEnum, string>();
            //TransLanguageDic.Add(TransLanguageTypeEnum.自动, "AUTO");
            TransLanguageDic.Add(TransLanguageTypeEnum.中文, "zh-CHS");
            TransLanguageDic.Add(TransLanguageTypeEnum.英文, "en");
            TransLanguageDic.Add(TransLanguageTypeEnum.日语, "ja");
            TransLanguageDic.Add(TransLanguageTypeEnum.韩语, "ko");
            TransLanguageDic.Add(TransLanguageTypeEnum.西班牙语, "es");
            TransLanguageDic.Add(TransLanguageTypeEnum.法语, "fr");
            TransLanguageDic.Add(TransLanguageTypeEnum.泰语, "th");
            TransLanguageDic.Add(TransLanguageTypeEnum.阿拉伯语, "ar");
            TransLanguageDic.Add(TransLanguageTypeEnum.俄语, "ru");

            TransLanguageDic.Add(TransLanguageTypeEnum.葡萄牙语, "pt");
            TransLanguageDic.Add(TransLanguageTypeEnum.德语, "de");
            TransLanguageDic.Add(TransLanguageTypeEnum.意大利语, "it");
            TransLanguageDic.Add(TransLanguageTypeEnum.希腊语, "el");
            TransLanguageDic.Add(TransLanguageTypeEnum.荷兰语, "nl");
            TransLanguageDic.Add(TransLanguageTypeEnum.波兰语, "pl");
            TransLanguageDic.Add(TransLanguageTypeEnum.保加利亚语, "bg");
            TransLanguageDic.Add(TransLanguageTypeEnum.爱沙尼亚语, "et");
            TransLanguageDic.Add(TransLanguageTypeEnum.丹麦语, "da");
            TransLanguageDic.Add(TransLanguageTypeEnum.芬兰语, "fi");
            TransLanguageDic.Add(TransLanguageTypeEnum.捷克语, "cs");
            TransLanguageDic.Add(TransLanguageTypeEnum.罗马尼亚语, "ro");
            TransLanguageDic.Add(TransLanguageTypeEnum.斯洛文尼亚语, "sl");
            TransLanguageDic.Add(TransLanguageTypeEnum.瑞典语, "sv");
            TransLanguageDic.Add(TransLanguageTypeEnum.匈牙利语, "hu");
            TransLanguageDic.Add(TransLanguageTypeEnum.越南语, "vi");
        }

        #endregion

        string cookie = "OUTFOX_SEARCH_USER_ID=1749491324@10.169.0.84; OUTFOX_SEARCH_USER_ID_NCOO=1693536376.7836118; _ga=GA1.2.1889944799.1600660329; DICT_SESS=v2|nr2Ypha-rVUEnMYG6LUY0YY0HQFPLPK06yRLUY6MOfRk564wFnfTB0kWk4TZ6LUm0kfPLkWO4gK0QuOfUG6LTS0wu0Ml564JuR; DICT_LOGIN=1||1613634435870";

        protected override string GetHtml(OcrContent content)
        {
            InitTokens();
            var result = "";
            string from = "", to = "";
            InitLanguage(content, ref from, ref to);

            string time = UnixTime();
            var sign = appSign(time, "1." + content.fileExt);

            var byt = Convert.FromBase64String(content.strBase64);
            var fileId = PostFileResult(byt, content.fileExt, from, to, sign, time);
            if (!string.IsNullOrEmpty(fileId))
            {
                //result = GenFile(fileId);

                //var query = "0";
                //while (!string.IsNullOrEmpty(query) && !query.Equals("-1") && !query.Equals("100"))
                //{
                //    System.Threading.Thread.Sleep(500);
                //    query = GetStatus(fileId);
                //}
                result = GetResult(fileId);
            }
            return result;
        }

        //private string GenFile(string taskId)
        //{
        //    var result = "";
        //    try
        //    {
        //        var url = "https://doctrans-service.youdao.com/trandoc/doc/genOriginPage?docKey={0}&imei=&sign={1}&salt={2}&_={2}&src=new-fanyiweb";

        //        string time = UnixTime();
        //        var sign = appSign(time, taskId);

        //        var values = new NameValueCollection() {
        //        { "Sec-Fetch-Site","cross-site"},
        //        { "Sec-Fetch-Mode","cors"},
        //        { "Referer","https://pdf.youdao.com/docview.html"},
        //    };
        //        var cookie = "DICT_UGC=be3af0da19b5c5e6aa4e17bd8d90b28a|; DICT_DOCTRANS_SESSION_ID=cae16469-9aff-43c0-80f9-4bc41ec3ba1d; DICT_SESS=v2|-0_-EQ_tM0kY0HT4O4wB0wBP4YWnMeyRQuPMQFnHpyR6yRfYEh4kW0wSOLQLRHQLRkERMpunMq4ReBhfkEhLUGRk50HezOLpK0; DICT_LOGIN=5||1599444801011";

        //        result = WebClientSyncExt.GetHtml(string.Format(url, taskId, sign, time), cookie, "", "https://pdf.youdao.com/", ExecTimeOutSeconds, values);

        //        if (!string.IsNullOrEmpty(result))
        //        {
        //            result = result.Trim().TrimEnd('}') + ",\"taskId\":\"" + taskId + "\"}";
        //        }
        //    }
        //    catch (Exception)
        //    {

        //    }
        //    return result;
        //}

        private string GetStatus(string taskId)
        {
            var result = "";
            try
            {
                var url = "https://doctrans-service.youdao.com/trandoc/originProgress?key={0}&imei=&_={1}&src=new-fanyiweb";

                var values = new NameValueCollection() {
                { "Sec-Fetch-Site","cross-site"},
                { "Sec-Fetch-Mode","cors"},
                { "Referer","https://pdf.youdao.com/docview.html"},
            };
                var html = WebClientSyncExt.GetHtml(string.Format(url, taskId, ServerTime.DateTime.Ticks), cookie, "", "https://pdf.youdao.com/docview.html", ExecTimeOutSeconds, values);

                if (!string.IsNullOrEmpty(html))
                {
                    result = CommonHelper.SubString(html, "\"origin\":", ",");
                }
            }
            catch (Exception)
            {

            }
            return result;
        }

        private string GetResult(string taskId)
        {
            var result = "";
            try
            {
                var url = "https://doctrans-service.youdao.com/trandoc/doc/viewLimitPage?doc={0}&src=new-fanyiweb&page=1&_={1}";

                var values = new NameValueCollection() {
                { "Sec-Fetch-Site","cross-site"},
                { "Sec-Fetch-Mode","cors"},
                { "Referer","https://pdf.youdao.com/docview.html"},
            };
                result = WebClientSyncExt.GetHtml(string.Format(url, taskId, ServerTime.DateTime.Ticks), cookie, "", "https://pdf.youdao.com/docview.html", ExecTimeOutSeconds, values);
            }
            catch (Exception)
            {

            }
            return result;
        }

        //private string GetResult(string taskId)
        //{
        //    var result = "";
        //    try
        //    {
        //        var time = UnixTime();
        //        var sign = appSign(time, taskId, false);
        //        var url = string.Format("https://doctrans-service.youdao.com/trandoc/doc/viewFullPage?isUseTerm=false&isCheckTermUpdate=false&doc={0}&viewPage=1&salt={1}&sign={2}&_={1}&src=fanyiweb", taskId, time, sign);

        //        var values = new NameValueCollection() {
        //        { "Sec-Fetch-Site","same-site"},
        //        { "Sec-Fetch-Mode","cors"},
        //    };
        //        result = WebClientSyncExt.GetHtml(url, cookie, "", "https://pdf.youdao.com/", ExecTimeOutSeconds, values);
        //    }
        //    catch (Exception)
        //    {

        //    }
        //    return result;
        //}

        private string PostFileResult(byte[] content, string fileExt, string from, string to, string sign, string salt)
        {
            var result = "";
            try
            {
                var url = "https://doctrans-service.youdao.com/trandoc/doc/upload";
                var file = new UploadFileInfo()
                {
                    Name = "your_file",
                    Filename = "1." + fileExt,
                    ContentType = ApplicationTypeHelper.GetApplicationType(fileExt),
                    Stream = new MemoryStream(content)
                };
                var values = new NameValueCollection() {
                { "from",from},
                { "to",to},
                { "type",fileExt},
                { "filename","1."+fileExt},
                { "client","docserver"},
                { "keyfrom","new-fanyiweb"},
                { "size","1000"},
                { "sign",sign},
                { "salt",salt},
            };
                var headers = new NameValueCollection() {
                { "Sec-Fetch-Site","cross-site"},
                { "Sec-Fetch-Mode","cors"},
                { "Cookie",cookie}
            };
                result = PostFile(url, new[] { file
    }, values, headers);
                if (!string.IsNullOrEmpty(result))
                {
                    result = CommonHelper.SubString(result, "\"key\":\"", "\"");
                }
                else
                {
                    result = "";
                }
            }
            catch (Exception)
            {

            }
            return result;
        }

        private string appSign(string time, string fileName, bool isUpload = true)
        {
            //l = 1599447451060
            //a: "docEn.doc"
            //"new-fanyiweb" + l + "ydsecret://newfanyiweb.doctran/sign/0j9n2{3mLSN-$Lg]K4o0N2}" + a
            //e = "new-fanyiweb1599447451060ydsecret://newfanyiweb.doctran/sign/0j9n2{3mLSN-$Lg]K4o0N2}docEn.doc"
            //1b1a4a98a5ff3a1dc139e4de4a701a43
            string plainText = isUpload ?
                strUploadAppId + time + strUploadSecret + fileName
                : strTransAppId + time + strTransSecret + fileName;

            return ToMd5(plainText);
        }

        private string strUploadAppId = "new-fanyiweb";
        private string strTransAppId = "fanyiweb";
        private string strUploadSecret = "";
        private string strTransSecret = "";

        private const string strAppJs = "secretKey:";
        private string strJsId = "src=\"js/commons";

        private void InitTokens()
        {
            if (string.IsNullOrEmpty(strUploadSecret))
            {
                var html = WebClientSyncExt.GetHtml("https://pdf.youdao.com", "", ExecTimeOutSeconds);
                if (!string.IsNullOrEmpty(html) && html.Contains(strJsId))
                {
                    html = CommonHelper.SubString(html, strJsId, "\"");
                    if (string.IsNullOrEmpty(html))
                    {
                        return;
                    }
                    var jsUrl = "https://pdf.youdao.com/js/commons" + html;
                    html = WebClientSyncExt.GetHtml(jsUrl, "", ExecTimeOutSeconds);
                    if (!string.IsNullOrEmpty(html) && html.Contains(strAppJs))
                    {
                        html = CommonHelper.SubString(html, strAppJs);
                        strUploadSecret = CommonHelper.SubString(html, ".NEW_FANYI,\"", "\"),");
                        strTransSecret = CommonHelper.SubString(html, ".FANYI,\"", "\"),");
                    }
                    if (string.IsNullOrEmpty(strUploadSecret))
                    {
                        strTransAppId = "fanyiweb";
                        strUploadAppId = "new-fanyiweb";
                        strTransSecret = "ydsecret://fanyiweb.doctran/sign/0j9n2{3mLSN-$Lg]K4o0N2}";
                        strUploadSecret = "ydsecret://newfanyiweb.doctran/sign/0j9n2{3mLSN-$Lg]K4o0N2}";
                    }
                }
            }
        }

        private string ToMd5(string strContent)
        {
            string result;
            if (strContent == null)
            {
                result = null;
            }
            else
            {
                MD5 md = MD5.Create();
                byte[] array = md.ComputeHash(Encoding.UTF8.GetBytes(strContent));
                StringBuilder stringBuilder = new StringBuilder();
                for (int i = 0; i < array.Length; i++)
                {
                    stringBuilder.Append(array[i].ToString("x2"));
                }
                result = stringBuilder.ToString();
            }
            return result;
        }

        private string UnixTime()
        {
            return Convert.ToInt64((DateTime.UtcNow - new DateTime(1970, 1, 1, 0, 0, 0, 0)).TotalSeconds * 1000).ToString();
        }

    }
}