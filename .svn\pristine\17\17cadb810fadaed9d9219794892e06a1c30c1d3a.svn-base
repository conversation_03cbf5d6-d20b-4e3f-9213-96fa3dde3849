<PERSON>,<PERSON>,<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,MaxAbsoluteError,MaxRelativeError,MinInvokeCount,MinIterationTime,RemoveOutliers,Affinity,Jit,Platform,Runtime,AllowVeryLargeObjects,Concurrent,CpuGroups,Force,HeapAffinitizeMask,HeapCount,NoAffinitize,RetainVm,Server,Arguments,BuildConfiguration,Clock,EngineFactory,EnvironmentVariables,Toolchain,IsBaseline,InvocationCount,IterationTime,LaunchCount,RunStrategy,TargetCount,UnrollFactor,WarmupCount,N,Mean,Error,StdDev
NetCoreParseDecimal,Default,False,<PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON>,255,RyuJit,X64,Core,False,True,False,True,De<PERSON>ult,De<PERSON>ult,False,F<PERSON>e,<PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON>,1,<PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON>,16,<PERSON><PERSON><PERSON>,10000,192.23 ns,3.8236 ns,4.8357 ns
CustomParseDecimal,<PERSON><PERSON><PERSON>,<PERSON><PERSON>e,<PERSON>fault,Default,Default,<PERSON>fault,Default,Default,255,RyuJit,X64,Core,False,True,Fals<PERSON>,True,Default,Default,Fals<PERSON>,Fals<PERSON>,False,Default,Default,<PERSON>fault,<PERSON>fault,<PERSON><PERSON>ult,<PERSON>fault,<PERSON>fault,1,<PERSON>fault,Default,<PERSON>fault,<PERSON>fault,16,Default,10000,45.22 ns,0.4465 ns,0.3728 ns
