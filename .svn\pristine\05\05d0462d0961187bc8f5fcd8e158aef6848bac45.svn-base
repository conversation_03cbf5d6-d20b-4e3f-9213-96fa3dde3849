﻿using CommonLib;
using System;
using System.Collections.Generic;
using System.Collections.Specialized;
using System.IO;
using System.Security.Cryptography;
using System.Text;

namespace HanZiOcr
{
    /// <summary>
    /// https://www.pen-to-print.com/forms/template/
    /// </summary>
    public class AWSFormRec : BaseOcrRec
    {
        public AWSFormRec()
        {
            OcrGroup = OcrGroupType.AWS;
            OcrType = HanZiOcrType.AWSForm;

            MaxExecPerTime = 22;

            LstJsonPreProcessArray = new List<object>() { "result", "tableData" };
            LstJsonNextProcessArray = new List<object>() { "words" };

            IsSupportVertical = true;
            LstVerticalLocation = new List<object>() { "polygon" };
            StrResultJsonSpilt = "content";
        }

        protected override void SetProcessArray(string html)
        {
            var isEnglish = !string.IsNullOrEmpty(html) && Equals(OcrHtmlProcess.GetLang(html), "en");
            StrContactCell = isEnglish ? " " : "";
        }

        protected override string GetHtml(OcrContent content)
        {
            var url = "https://nmwe4beyw1.execute-api.us-east-1.amazonaws.com/dev/forms/analyzeFormAndTable";
            var srcHash = SHA256Hex(Convert.FromBase64String(content.strBase64));
            var header = new NameValueCollection() {
                { "X-Api-Key","4qlkYrXJ4Z255nLU35mnq84sr1VmMs9j1su18xlK"},
                { "Referer","https://www.pen-to-print.com/"}
             };
            var strPost = "{\"srcImg\":\"," + content.strBase64 + "\",\"srcHash\":\"" + srcHash + "\",\"appVersion\":1.0}";
            var result = WebClientSyncExt.GetHtml(url, "", strPost, "https://www.pen-to-print.com/", ExecTimeOutSeconds, header);
            return result;
        }

        static string SHA256Hex(byte[] content)
        {
            using (SHA256 algo = SHA256.Create())
            {
                byte[] hashbytes = algo.ComputeHash(content);
                StringBuilder builder = new StringBuilder();
                for (int i = 0; i < hashbytes.Length; i++)
                {
                    builder.Append(hashbytes[i].ToString("x2"));
                }
                var n = "";
                for (int e = 0; e < 10; e++)
                    n += builder[3 + 3 * e];
                return n;
            }
        }
    }
}