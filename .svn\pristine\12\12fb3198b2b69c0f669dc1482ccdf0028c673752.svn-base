﻿using log4net;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Net;
using System.Threading;

namespace CommonLib
{
    public enum OcrType
    {
        文本 = 0,
        竖排 = 1,
        表格 = 2,
        公式 = 3,
        翻译 = 4
    }
    public enum OcrGroupType
    {
        不限 = 0,
        百度 = 1,
        腾讯 = 2,
        阿里 = 3,
        有道 = 4,
        搜狗 = 5,
        讯飞 = 6,
        迅捷 = 7,
        VIVO = 8,
        金山 = 9,
        学而思 = 10,
        汉王 = 11,
        合合 = 12,
        字节 = 13,
        新东方 = 14,
        MathPix = 15,
        AWS = 16,
    }

    public class ConfigHelper
    {
        public static readonly ILog _Log;

        public static bool IsShowDaMa = false;

        public static bool IsExit = false;

        public static List<HanZiRecType> lstHanZiRecType = new List<HanZiRecType> { HanZiRecType.BaiDuAPI };
        public static bool IsOnInit = false;
        public static object LockObj = "";

        public static string StrCodeBasePath = "D:\\助手\\Config\\";
        public static string StrImagePath = "D:\\助手\\Image\\";
        public static string StrFixImagePath = "D:\\助手\\待整理\\";
        public static string StrFixOKImagePath = "D:\\助手\\用户整理-OK\\";
        public static string StrFixErrorImagePath = "D:\\助手\\用户整理-Error\\";
        public static string StrFixImageGuiDangPath = "D:\\助手\\整理\\";
        public static string StrBlankImagePath = "D:\\助手\\Config\\blank.jpg";
        public static string StrLocalLoginCDSPath = "D:\\助手\\Config\\cds_login.cds";
        public static string StrLocalOrderCDSPath = "D:\\助手\\Config\\cds_order.cds";
        public static string StrTmpImagePath = "\\Image\\";
        public static string StrTmpFilePath = "\\Document\\";

        public static Dictionary<string, string> DicReplace = new Dictionary<string, string>();

        static ConfigHelper()
        {
            _Log = LogManager.GetLogger("CommonLog");
        }
        public static string FilePath { get; set; }

        public static string FileHostUrl { get; set; }

        /// <summary>
        /// 打码超时时间
        /// </summary>
        public static int NGetCodeTimeOut { get; set; }

        /// <summary>
        /// 打码被屏蔽周期（秒）
        /// </summary>
        public static int NMaxExecBlackSecond { get; set; }

        /// <summary>
        /// 是否验证当前码子余额
        /// </summary>
        public static bool IsValidateCodeAmount { get; set; }

        /// <summary>
        /// 打码频率计算周期时间（毫秒）
        /// </summary>
        public static int NBlackMSencond { get; set; }

        /// <summary>
        /// 打码周期内，最多打码量
        /// </summary>
        public static int NMaxExecPerSecond { get; set; }

        public static int NMaxCodeProcessThread { get; set; }

        public static string TipMsg { get; set; }

        public static bool IsCanDaMa
        {
            get
            {
                //if (ServerTime.DateTime.Hour < 5)
                //    return false;
                return true;
            }
        }
        private static int maxThreadCount;

        public static int MaxThreadCount
        {
            get
            {
                if (ConfigHelper.maxThreadCount <= 0)
                    ConfigHelper.maxThreadCount = GetNConfigByName("MaxThreadCount", 100);
                return ConfigHelper.maxThreadCount;
            }
            set { ConfigHelper.maxThreadCount = value; }
        }

        public static bool IsRegToGeRen { get; set; }

        public static bool IsRegToProfessional { get; set; }

        public static int NRegSendDays { get; set; } = 30;

        public static int NMaxUserCount { get; set; } = 3;

        public static string PayToken { get; set; }

        public static string DefaultSiteHost { get; set; }

        /// <summary>
        /// 能否预览文件
        /// </summary>
        public static bool IsCanViewFile { get; set; }

        /// <summary>
        /// 最低版本
        /// </summary>
        public static DateTime MinVersionDate { get; set; }

        public static string MinVersionStr { get; set; }

        /// <summary>
        /// 是否在Frame中加载支付页面
        /// </summary>
        public static bool IsPayByFrame { get; set; }

        /// <summary>
        /// 识别服务别名
        /// </summary>
        public static string OcrServer { get; set; }

        public static string OcrHost { get; set; }

        /// <summary>
        /// 是否记录所有请求
        /// </summary>
        public static bool IsLogRequest { get; set; }

        /// <summary>
        /// 是否启用IP黑名单
        /// </summary>
        public static bool IsBlackIpEnable { get; set; }

        public static bool GetBConfigByName(string strName, string defaultStr = "true")
        {
            var result = GetConfigByName(strName).ToLower().Equals(defaultStr);
            return result;
        }

        public static bool GetBoolByName(string strName, string defaultStr = "1")
        {
            var result = !string.IsNullOrEmpty(strName) && strName.ToLower().Equals(defaultStr);
            return result;
        }

        public static int GetNConfigByName(string strName, int nDefault = 1000)
        {
            int result;
            if (!int.TryParse(GetConfigByName(strName), out result))
            {
                result = nDefault;
            }
            return result;
        }

        public static double GetDConfigByName(string strName, double nDefault = 1.0d)
        {
            double result;
            if (!double.TryParse(GetConfigByName(strName), out result))
            {
                result = nDefault;
            }
            return result;
        }

        public static string GetConfigByName(string strName)
        {
            var result = "";
            try
            {
                result = ConfigurationManager.AppSettings[strName];
                if (string.IsNullOrEmpty(result))
                {
                    result = "";
                }
            }
            catch (Exception oe)
            {
                Console.WriteLine(oe.Message);
            }
            return result;
        }

        public static DateTime DtStart { get; set; }

        public static bool IsUseSelfPay { get; set; }
        public static int PayTimeOutMinute { get; set; } = 10;
        public static double PayMaxRandom { get; set; } = 0.1;
        public static string PayQQKeFu { get; set; }
        /// <summary>
        /// 支付金额规则 true：递增0.01, false：递减0.01
        /// </summary>
        public static bool IsPayPriceAdd { get; set; }
        public static string PayReturnUrl { get; set; }

        /// <summary>
        /// 支付宝动态码
        /// </summary>
        public static string PayZFBDTM { get; set; }

        /// <summary>
        /// 支付宝静态码
        /// </summary>
        public static string PayZFBJTM { get; set; }

        /// <summary>
        /// 微信静态码
        /// </summary>
        public static string PayWXJTM { get; set; }
        public static string StrOuterStaticUrl { get; set; }
        public static string StrInnerStaticUrl { get; set; }
        public static string StrInnerCDNUrl { get; set; }
        public static string StrOuterCDNUrl { get; set; }

        public static void InitConfig()
        {
            NMaxCodeProcessThread = GetNConfigByName("NMaxCodeProcessThread", 1);
            if (NMaxCodeProcessThread <= 0)
            {
                NMaxCodeProcessThread = 1;
            }
            NMaxExecPerSecond = GetNConfigByName("NMaxExecPerSecond", 30);
            NBlackMSencond = GetNConfigByName("NBlackMSencond", 1000);
            NMaxExecBlackSecond = GetNConfigByName("NMaxExecBlackSecond", 30);
            NGetCodeTimeOut = GetNConfigByName("NGetCodeTimeOut", 5);
            IsValidateCodeAmount = GetBConfigByName("IsValidateCodeAmount");
            FileHostUrl = GetConfigByName("FileHostUrl");
            FilePath = GetConfigByName("FilePath");
            IsRegToProfessional = GetBConfigByName("IsRegToProfessional");
            IsRegToGeRen = GetBConfigByName("IsRegToGeRen");
            NRegSendDays = GetNConfigByName("NRegSendDays", 30);

            TipMsg = GetConfigByName("TipMsg");
            PayToken = GetConfigByName("PayToken");
            DefaultSiteHost = GetConfigByName("DefaultSiteHost");

            IsCanViewFile = GetBConfigByName("IsCanViewFile");

            if (DateTime.TryParse(GetConfigByName("MinVersionDate"), out DateTime dtMinDate))
            {
                MinVersionDate = dtMinDate;
            }
            MinVersionStr = GetConfigByName("MinVersionStr").Replace("\\n", "\n");

            IsPayByFrame = GetBConfigByName("IsPayByFrame");

            OcrServer = GetConfigByName("OcrServer");

            NMaxUserCount = GetNConfigByName("NMaxUserCount", 30);

            DtStart = ServerTime.DateTime;

            IsLogRequest = GetBConfigByName("IsLogRequest");

            IsBlackIpEnable = GetBConfigByName("IsBlackIpEnable");


            IsUseSelfPay = GetBConfigByName("IsUseSelfPay");
            PayTimeOutMinute = GetNConfigByName("PayTimeOutMinute", 10);
            PayQQKeFu = GetConfigByName("PayQQKeFu");
            PayReturnUrl = GetConfigByName("PayReturnUrl");

            IsPayPriceAdd = GetBConfigByName("IsPayPriceAdd");
            PayMaxRandom = GetDConfigByName("PayMaxRandom", 0.1);

            //点击获取支付宝userId https://www.dedemao.com/alipay/authorize.php?scope=auth_base
            //支付宝APP扫码查看 http://ocr.oldfish.cn:9090/enQrcode?url=https://www.dedemao.com/alipay/authorize.php?scope=auth_base
            PayZFBDTM = GetConfigByName("PayZFBDTM");
            PayZFBJTM = GetConfigByName("PayZFBJTM");
            PayWXJTM = GetConfigByName("PayWXJTM");

            StrInnerStaticUrl = GetConfigByName("StrInnerStaticUrl");
            StrOuterStaticUrl = GetConfigByName("StrOuterStaticUrl");
            StrInnerCDNUrl = GetConfigByName("StrInnerCDNUrl");
            StrOuterCDNUrl = GetConfigByName("StrOuterCDNUrl");
        }

        public static string GetFlagName(string strName, string site)
        {
            return string.Format("{0}{1}", site, strName);
        }

        public static string GetFlagName(string strName, UserTypeEnum site)
        {
            return string.Format("{0}{1}", site.ToString(), strName);
        }

        public static void InitThread()
        {
            try
            {
                ServicePointManager.SecurityProtocol = SecurityProtocolType.Ssl3
                                                       | SecurityProtocolType.Tls
                                                       | SecurityProtocolType.Tls11
                                                       | SecurityProtocolType.Tls12
                                                       | (SecurityProtocolType)0x3000; //Tls13
            }
            catch
            {
                ServicePointManager.SecurityProtocol = SecurityProtocolType.Ssl3
                                                       | SecurityProtocolType.Tls
                                                       | SecurityProtocolType.Tls11
                                                       | SecurityProtocolType.Tls12;
            }

            try
            {
                ServicePointManager.ServerCertificateValidationCallback = (obj, certificate, chain, sslPolicyErrors) => true;
                ServicePointManager.DefaultConnectionLimit = int.MaxValue;
                ServicePointManager.MaxServicePoints = int.MaxValue;
                //ServicePointManager.MaxServicePoints = int.MaxValue;
                ServicePointManager.MaxServicePointIdleTime = int.MaxValue;
                ServicePointManager.UseNagleAlgorithm = false;
                //ServicePointManager.CheckCertificateRevocationList = false;
                ServicePointManager.Expect100Continue = false;
                //ServicePointManager.SetTcpKeepAlive(true, 1000, 200);
                GlobalProxySelection.Select = GlobalProxySelection.GetEmptyWebProxy();
                //WebRequest.DefaultWebProxy = null;

                var workerThreads = 0;
                var completionPortThreads = 0;
                ThreadPool.GetAvailableThreads(out workerThreads, out completionPortThreads);
                Console.WriteLine("默认配置：工作线程：{0}，完成端口：{1}", workerThreads, completionPortThreads);
                ThreadPool.SetMinThreads(200, 100);
                if (Environment.ProcessorCount * 200 > workerThreads)
                {
                    if (ThreadPool.SetMaxThreads(Environment.ProcessorCount * 200, Environment.ProcessorCount * 100))
                    {
                        Console.WriteLine();
                    }
                    ThreadPool.GetAvailableThreads(out workerThreads, out completionPortThreads);
                    Console.WriteLine("修改配置：工作线程：{0}，完成端口：{1}", workerThreads, completionPortThreads);
                }
                else
                {
                    Console.WriteLine("默认配置大于要修改的配置，无需修改");
                }
            }
            catch
            {
                // ignored
            }
        }

    }

}