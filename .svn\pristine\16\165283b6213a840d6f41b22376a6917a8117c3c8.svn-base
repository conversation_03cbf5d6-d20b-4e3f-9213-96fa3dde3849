﻿using System;
using System.Collections.Generic;
using System.Collections.Specialized;
using System.Globalization;
using System.IO;
using System.Net;
using System.Text;

namespace CommonLib
{

    public class UploadFileInfo
    {

        public UploadFileInfo()
        {
            ContentType = "application/octet-stream";
        }
        public string Name { get; set; }
        public string Filename { get; set; }
        public string ContentType { get; set; }
        public Stream Stream { get; set; }
    }
    public class UploadFileRequest
    {

        public static string Post(string url, IEnumerable<UploadFileInfo> files, NameValueCollection values, NameValueCollection headers = null)
        {
            var result = "";
            try
            {
                byte[] resultByt = PostByte(url, files, values, headers);
                result = Encoding.UTF8.GetString(resultByt);
            }
            catch (Exception oe)
            {
                if (oe as WebException != null)
                {
                    using (var response = (oe as WebException).Response as HttpWebResponse)
                    {
                        if (response != null)
                        {
                            try
                            {
                                result = new StreamReader(response.GetResponseStream()).ReadToEnd();
                            }
                            catch { }
                            throw new BadApiException(oe.Message, oe.InnerException, response.StatusCode);
                        }
                    }
                }
            }
            return result;
        }

        static byte[] PostByte(string url, IEnumerable<UploadFileInfo> files, NameValueCollection values, NameValueCollection headers = null)
        {
            var request = WebRequest.Create(url) as HttpWebRequest;
            request.Method = "POST";
            request.ReadWriteTimeout = 60000;
            request.Timeout = 60000;
            var boundary = "---------------------------" + ServerTime.DateTime.Ticks.ToString("x", NumberFormatInfo.InvariantInfo);
            request.ContentType = "multipart/form-data; boundary=" + boundary;
            request.UserAgent = "Mozilla/5.0 (Windows NT 6.1; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.163 Safari/537.36";
            boundary = "--" + boundary;
            if (url.Contains("wxapp.translator.qq.com"))
            {
                request.KeepAlive = false;
            }
            else
                request.KeepAlive = true;
            request.Referer = url;
            if (headers != null)
            {
                foreach (string name in headers.Keys)
                {
                    if (Equals(name.ToLower(), "user-agent"))
                    {
                        request.UserAgent = headers[name];
                    }
                    else if (Equals(name.ToLower(), "referer"))
                    {
                        request.Referer = headers[name];
                    }
                    else
                    {
                        request.Headers.Add(name, headers[name]);
                    }
                }
            }

            using (var requestStream = request.GetRequestStream())
            {
                if (values != null)
                {
                    // Write the values
                    foreach (string name in values.Keys)
                    {
                        var buffer = Encoding.UTF8.GetBytes(boundary + Environment.NewLine);
                        requestStream.Write(buffer, 0, buffer.Length);
                        buffer = Encoding.UTF8.GetBytes(string.Format("Content-Disposition: form-data; name=\"{0}\"{1}{1}", name, Environment.NewLine));
                        requestStream.Write(buffer, 0, buffer.Length);
                        buffer = Encoding.UTF8.GetBytes(values[name] + Environment.NewLine);
                        requestStream.Write(buffer, 0, buffer.Length);
                    }
                }

                // Write the files
                if (files != null)
                {
                    foreach (var file in files)
                    {
                        if (file == null)
                        {
                            continue;
                        }
                        var buffer = Encoding.UTF8.GetBytes(boundary + Environment.NewLine);
                        requestStream.Write(buffer, 0, buffer.Length);

                        buffer = Encoding.UTF8.GetBytes(string.Format("Content-Disposition: form-data; name=\"{0}\"; filename=\"{1}\"{2}", file.Name, file.Filename, Environment.NewLine));
                        requestStream.Write(buffer, 0, buffer.Length);

                        buffer = Encoding.UTF8.GetBytes(string.Format("Content-Type: {0}{1}{1}", file.ContentType, Environment.NewLine));
                        requestStream.Write(buffer, 0, buffer.Length);

                        //buffer = Encoding.UTF8.GetBytes(string.Format("Content-Type: {0}{1}", file.ContentType, Environment.NewLine));
                        //requestStream.Write(buffer, 0, buffer.Length);

                        file.Stream.CopyTo(requestStream);
                        buffer = Encoding.UTF8.GetBytes(Environment.NewLine);
                        requestStream.Write(buffer, 0, buffer.Length);
                    }
                }

                var boundaryBuffer = Encoding.UTF8.GetBytes(boundary + "--");
                requestStream.Write(boundaryBuffer, 0, boundaryBuffer.Length);
            }

            using (var response = request.GetResponse())
            using (var responseStream = response.GetResponseStream())
            using (var stream = new MemoryStream())
            {
                responseStream.CopyTo(stream);
                return stream.ToArray();
            }
        }
    }
}
