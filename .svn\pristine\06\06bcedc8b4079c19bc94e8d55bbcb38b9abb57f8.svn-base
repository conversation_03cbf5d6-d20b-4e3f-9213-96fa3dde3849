﻿<Project Sdk="Microsoft.NET.Sdk.Web">

    <PropertyGroup>
        <TargetFramework>net6.0</TargetFramework>
    </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Google.Protobuf" Version="3.15.0" />
    <PackageReference Include="Grpc.Net.Client" Version="2.46.0" />
    <PackageReference Include="Grpc.Tools" Version="2.46.1">
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
      <PrivateAssets>all</PrivateAssets>
    </PackageReference>
    
    <PackageReference Include="System.Runtime" Version="4.3.1" />
    <PackageReference Include="System.Reflection.Emit" Version="4.7.0" />
    <PackageReference Include="System.Reflection.Emit.LightWeight" Version="4.7.0" />
    <PackageReference Include="Microsoft.CSharp" Version="4.7.0" />
    <PackageReference Include="System.Memory" Version="4.5.4" />
  </ItemGroup>
  
  <ItemGroup>
    <ProjectReference Include="..\..\..\ServiceStack.Text\src\ServiceStack.Text\ServiceStack.Text.csproj" />
    <ProjectReference Include="..\..\src\ServiceStack.Api.OpenApi\ServiceStack.Api.OpenApi.csproj" />
    <ProjectReference Include="..\..\src\ServiceStack.Client\ServiceStack.Client.csproj" />
    <ProjectReference Include="..\..\src\ServiceStack.Common\ServiceStack.Common.csproj" />
    <ProjectReference Include="..\..\src\ServiceStack.Interfaces\ServiceStack.Interfaces.csproj" />
    <ProjectReference Include="..\..\src\ServiceStack.Mvc\ServiceStack.Mvc.csproj" />
    <ProjectReference Include="..\..\src\ServiceStack\ServiceStack.csproj" />
    <ProjectReference Include="..\..\src\ServiceStack.Extensions\ServiceStack.Extensions.csproj" />
  </ItemGroup>

  <ItemGroup>
<!--    <Protobuf Include="Protos\services.proto" GrpcServices="Client" />-->
  </ItemGroup>

</Project>
