﻿using System;
using System.Data;
using System.Configuration;


namespace ToolCommon
{
    /// <summary>
    ///SearchEntity 的摘要说明
    /// </summary>
    public class SearchEntity
    {
        private string tableName = "";

        /// <summary>
        /// 查询的表名
        /// </summary>
        public string TableName
        {
            get { return tableName; }
            set { tableName = value; }
        }

        private string key = "";

        /// <summary>
        /// 表或多表连接的主键
        /// </summary>
        public string Key
        {
            get { return key; }
            set { key = value; }
        }

        private string strSearch;

        /// <summary>
        /// 查询字段（表前面字段）
        /// </summary>
        public string StrSearch
        {
            get { return strSearch; }
            set { strSearch = value; }
        }

        private string strCondition = string.Empty;

        /// <summary>
        /// 查询条件（where后边的字段）
        /// </summary>
        public string StrCondition
        {
            get { return strCondition; }
            set { strCondition = value; }
        }

        private string strSort = string.Empty;

        /// <summary>
        /// 需要排序的字段(必须指定)
        /// </summary>
        public string StrSort
        {
            get
            {
                if (!string.IsNullOrEmpty(strSort))
                    return strSort;
                else
                {
                    if (!string.IsNullOrEmpty(key))
                        return key;
                    return "Rank";
                }
            }
            set { strSort = value; }
        }

        private int sortStyle = 0;

        /// <summary>
        /// 排序类型（0为升序、1为降序）
        /// </summary>
        public int SortStyle
        {
            get { return sortStyle; }
            set { sortStyle = value; }
        }

        private int isDist = 0;

        /// <summary>
        /// 是否使用Distinct字段（0为不使用、1为使用）
        /// </summary>
        public int IsDist
        {
            get { return isDist; }
            set { isDist = value; }
        }

        private int pageSize;

        /// <summary>
        /// 页面数据大小
        /// </summary>
        public int PageSize
        {
            get { return pageSize; }
            set { pageSize = value; }
        }

        private int pagIndex;

        public int NowPageIndex
        {
            get { return pagIndex; }
            set { pagIndex = value; }
        }

        /// <summary>
        /// 当前页码
        /// </summary>
        public int PagIndex
        {
            get { return pagIndex; }
            set { pagIndex = value; }
        }

        private int totalPage;

        /// <summary>
        /// 分页后的总页数
        /// </summary>
        public int TotalPage
        {
            get { return totalPage; }
            set { totalPage = value; }
        }

        private int allDataCount;

        /// <summary>
        /// 返回的数据总数
        /// </summary>
        public int AllDataCount
        {
            get { return allDataCount; }
            set { allDataCount = value; }
        }

        private string _joinCondition;

        /// <summary>
        /// 关联表
        /// </summary>
        public string JoinCondition
        {
            get { return _joinCondition; }
            set { _joinCondition = value; }
        }
    }
}