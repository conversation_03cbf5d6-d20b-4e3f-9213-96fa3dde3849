﻿using CommonLib;
using Newtonsoft.Json;
using System;
using System.Linq;
using System.Threading;
using System.Web;

namespace Code.MainSite
{
    /// <summary>
    ///     Code 的摘要说明
    /// </summary>
    public class Code : IHttpHandler
    {


        public void ProcessRequest(HttpContext context)
        {
            if (context.Request.Params.Count <= 0 || !context.Request.Params.AllKeys.Contains("op"))
            {
                return;
            }
            context.Response.ContentType = "text/plain";
            var action = context.Request.Params["op"];
            var result = "";
            switch (action)
            {
                case "path":
                    result = AppDomain.CurrentDomain.RelativeSearchPath;
                    break;
                case "time":
                    result = ServerTime.DateTime.Ticks.ToString("F0");
                    break;
                case "siteinfo":
                    #region SiteInfo

                    //result += CustomImageHelper.ReportToday();
                    result += Environment.NewLine + "服务器名称：" + context.Server.MachineName;
                    //服务器名称  
                    result += Environment.NewLine + "服务器IP地址：" + context.Request.ServerVariables["LOCAL_ADDR"];
                    //服务器IP地址  
                    result += Environment.NewLine + "HTTP访问端口：" + context.Request.ServerVariables["SERVER_PORT"];
                    //HTTP访问端口"
                    result += Environment.NewLine + ".NET版本：" + ".NET CLR" + Environment.Version.Major + "." +
                              Environment.Version.Minor + "." + Environment.Version.Build + "." + Environment.Version.Revision;
                    //.NET解释引擎版本  
                    result += Environment.NewLine + "服务器操作系统版本：" + Environment.OSVersion;
                    //服务器操作系统版本  
                    result += Environment.NewLine + "服务器IIS版本：" + context.Request.ServerVariables["SERVER_SOFTWARE"];
                    //服务器IIS版本  
                    result += Environment.NewLine + "服务器域名：" + context.Request.ServerVariables["SERVER_NAME"];
                    //服务器域名  
                    result += Environment.NewLine + "虚拟目录的绝对路径：" + context.Request.ServerVariables["APPL_RHYSICAL_PATH"];
                    //虚拟目录的绝对路径  
                    result += Environment.NewLine + "执行文件的绝对路径：" + context.Request.ServerVariables["PATH_TRANSLATED"];
                    ////执行文件的绝对路径  
                    //result += Environment.NewLine + "虚拟目录Session总数：" + context.Session.Contents.Count.ToString();
                    ////虚拟目录Session总数  
                    //result += Environment.NewLine + "虚拟目录Application总数：" + context.Application.Contents.Count.ToString();
                    //虚拟目录Application总数  
                    result += Environment.NewLine + "域名主机：" + context.Request.ServerVariables["HTTP_HOST"];
                    //域名主机  
                    result += Environment.NewLine + "服务器区域语言：" + context.Request.ServerVariables["HTTP_ACCEPT_LANGUAGE"];
                    //服务器区域语言  
                    result += Environment.NewLine + "用户信息：" + context.Request.ServerVariables["HTTP_USER_AGENT"];
                    result += Environment.NewLine + "CPU个数：" + Environment.GetEnvironmentVariable("NUMBER_OF_PROCESSORS");
                    //CPU个数  
                    result += Environment.NewLine + "CPU类型：" + Environment.GetEnvironmentVariable("PROCESSOR_IDENTIFIER");
                    //CPU类型  
                    result += Environment.NewLine + "请求来源地址：" + context.Request.Headers["X-Real-IP"];

                    #endregion
                    break;
                case "lastlog":
                    int availableWorker, availableIo, maxWorker = 0, maxIo = 0;

                    ThreadPool.GetAvailableThreads(out availableWorker, out availableIo);
                    ThreadPool.GetMaxThreads(out maxWorker, out maxIo);

                    result = string.Format("Worker:{0}/{1} IO:{2}/{3}{4}OffSet:{5}ms{4}"
                        , availableWorker, maxWorker, availableIo, maxIo, Environment.NewLine, new TimeSpan(ServerTime.OffSet).TotalMilliseconds)
                        + CommonHelper.GetLastLog();
                    //result = string.Format("STime:{0},Ticks:{1}{2}LTime:{3},Ticks:{4}{2}OffSet:{5},{6}ms{2}"
                    //    , ServerTime.DateTime.ToString("yyyy-MM-dd HH:mm:ss"), ServerTime.DateTime.Ticks, Environment.NewLine
                    //    , DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"), DateTime.Now.Ticks, ServerTime.offSet, (DateTime.Now - ServerTime.DateTime).TotalMilliseconds)
                    //    + result;
                    break;
                #region 对外识别接口
                #region Report
                case "status-day-ocr":
                    var day_ocr = ApiRequestCacheHelper.GetRequestRoot(RequestFlagConst.OcrServiceFlag, ApiRequestTypeEnum.day);
                    result = JsonConvert.SerializeObject(day_ocr);
                    break;
                case "status-week-ocr":
                    var week_ocr = ApiRequestCacheHelper.GetRequestRoot(RequestFlagConst.OcrServiceFlag, ApiRequestTypeEnum.week);
                    result = JsonConvert.SerializeObject(week_ocr);
                    break;
                case "status-month-ocr":
                    var month_acc = ApiRequestCacheHelper.GetRequestRoot(RequestFlagConst.OcrServiceFlag, ApiRequestTypeEnum.month);
                    result = JsonConvert.SerializeObject(month_acc);
                    break;
                case "status-day-ocrapi":
                    var day_ocr_api = ApiRequestCacheHelper.GetRequestRoot(RequestFlagConst.OcrApiFlag, ApiRequestTypeEnum.day);
                    result = JsonConvert.SerializeObject(day_ocr_api);
                    break;
                case "status-week-ocrapi":
                    var week_ocr_api = ApiRequestCacheHelper.GetRequestRoot(RequestFlagConst.OcrApiFlag, ApiRequestTypeEnum.week);
                    result = JsonConvert.SerializeObject(week_ocr_api);
                    break;
                case "status-month-ocrapi":
                    var month_acc_api = ApiRequestCacheHelper.GetRequestRoot(RequestFlagConst.OcrApiFlag, ApiRequestTypeEnum.month);
                    result = JsonConvert.SerializeObject(month_acc_api);
                    break;
                case "status-day-acc":
                    var day_acc = ApiRequestCacheHelper.GetRequestRoot(RequestFlagConst.AccountApiFlag, ApiRequestTypeEnum.day);
                    result = JsonConvert.SerializeObject(day_acc);
                    break;
                case "status-week-acc":
                    var week_acc = ApiRequestCacheHelper.GetRequestRoot(RequestFlagConst.AccountApiFlag, ApiRequestTypeEnum.week);
                    result = JsonConvert.SerializeObject(week_acc);
                    break;
                case "status-month-acc":
                    var month_ocr = ApiRequestCacheHelper.GetRequestRoot(RequestFlagConst.AccountApiFlag, ApiRequestTypeEnum.month);
                    result = JsonConvert.SerializeObject(month_ocr);
                    break;
                case "serverstate":
                    {
                        var msg = context.Request.Form["info"];
                        if (!string.IsNullOrEmpty(msg))
                        {
                            var content = JsonConvert.DeserializeObject<ServerStateInfo>(msg);
                            CodeProcessHelper.AddServerState(content);
                            result = "ok";
                        }
                        else
                        {
                            result = "info为空";
                        }
                    }
                    break;
                #endregion

                #endregion
                default:
                    break;
            }
            if (string.IsNullOrEmpty(result) && !action.Equals("order") && !action.Equals("code"))
            {
                result = "no";
            }
            context.Response.Write(result ?? "");
            context.Response.End();
        }

        public bool IsReusable => false;
    }

}