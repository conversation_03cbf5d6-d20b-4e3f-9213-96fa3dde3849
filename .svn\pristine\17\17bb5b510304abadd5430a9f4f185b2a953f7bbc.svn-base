﻿using CommonLib;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;

namespace Account.Web
{
    public class PayQrcodeUtil
    {
        private const string PayQrCodeKey = "PayQrCode";
        public static PayQrcode findByPriceAndType(double realPrice, int type)
        {
            var lstQrcode = GetAll();
            return lstQrcode?.FirstOrDefault(p => Equals(p.type, type) && Equals(p.price, realPrice));
        }

        public static List<PayQrcode> GetAll()
        {
            var lstQrcode = JsonConvert.DeserializeObject<List<PayQrcode>>(CodeProcessHelper.ServerConfigCache.Get(PayQrCodeKey) ?? "") ?? new List<PayQrcode>();
            return lstQrcode;
        }

        public static void AddQrCode(PayQrcode qrcode)
        {
            var lstQrcode = GetAll();
            if (!lstQrcode.Exists(p => Equals(p.type, qrcode.type) && Equals(p.price, qrcode.price)))
            {
                lstQrcode.Add(qrcode);
            }
            CodeProcessHelper.ServerConfigCache.Set(PayQrCodeKey, JsonConvert.SerializeObject(lstQrcode));
        }
    }

    public class PayQrcode
    {
        public string payUrl { get; set; }
        public double price { get; set; }
        public int type { get; set; }
        public string remark { get; set; }
    }
}