<?xml version="1.0" encoding="utf-8"?>
<!--
此文件由 Web 项目的发布/打包过程使用。可以通过编辑此 MSBuild 文件
自定义此过程的行为。为了解与此相关的更多内容，请访问 https://go.microsoft.com/fwlink/?LinkID=208121。 
-->
<Project>
  <PropertyGroup>
    <WebPublishMethod>MSDeploy</WebPublishMethod>
    <ResourceId>/subscriptions/b25523a2-a34d-4250-8d34-eb9ab4df98eb/resourceGroups/oldfish_group/providers/Microsoft.Web/sites/oldfishCentralIndia</ResourceId>
    <ResourceGroup>oldfish_group</ResourceGroup>
    <PublishProvider>AzureWebSite</PublishProvider>
    <LastUsedBuildConfiguration>Release</LastUsedBuildConfiguration>
    <LastUsedPlatform>Any CPU</LastUsedPlatform>
    <SiteUrlToLaunchAfterPublish>https://oldfishcentralindia.azurewebsites.net</SiteUrlToLaunchAfterPublish>
    <LaunchSiteAfterPublish>true</LaunchSiteAfterPublish>
    <ExcludeApp_Data>false</ExcludeApp_Data>
    <MSDeployServiceURL>oldfishcentralindia.scm.azurewebsites.net:443</MSDeployServiceURL>
    <DeployIisAppPath>oldfishCentralIndia</DeployIisAppPath>
    <RemoteSitePhysicalPath />
    <SkipExtraFilesOnServer>true</SkipExtraFilesOnServer>
    <InstallAspNetCoreSiteExtension>false</InstallAspNetCoreSiteExtension>
    <MSDeployPublishMethod>WMSVC</MSDeployPublishMethod>
    <EnableMSDeployBackup>true</EnableMSDeployBackup>
    <EnableMsDeployAppOffline>false</EnableMsDeployAppOffline>
    <UserName>oldfish</UserName>
	<Password>Aa@123456</Password>
    <_SavePWD>true</_SavePWD>
    <_DestinationType>AzureWebSite</_DestinationType>
  </PropertyGroup>
</Project>