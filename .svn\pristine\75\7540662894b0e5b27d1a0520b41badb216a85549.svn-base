﻿using CommonLib;
using System;
using System.Collections.Generic;
using System.Security.Cryptography;
using System.Text;
using System.Web;

namespace HanZiOcr
{
    /// <summary>
    /// 搜狗深智API-Demo
    /// view-source:https://deepi.sogou.com/
    /// view-source:https://deepi.sogou.com//static/js/index.8fced5520b170a868769.js
    /// ,i="sogou_ocr_just_for_deepi",a="4b66a37108dab018ace616c4ae07e644"
    /// 文档：https://deepi.sogou.com/doccenter/textcognitivedoc
    /// </summary>
    public class SouGouDeepRec : BaseOcrRec
    {
        public SouGouDeepRec()
        {
            OcrGroup = OcrGroupType.搜狗;
            OcrType = HanZiOcrType.搜狗深智;
            State = EnableState.禁用;
            MaxExecPerTime = 21;

            LstJsonPreProcessArray = new List<object>() { "result" };
            StrResultJsonSpilt = "content";

            IsSupportVertical = true;
            LstVerticalLocation = new List<object>() { "frame" };
        }

        //private const string txtService = "basicOpenOcr";

        //通用服务的名称：basicOpenOcr（简单的ocr图像识别服务），basicDetectDirectionOpenOcr（简单的ocr图像识别服务，支持方向检测，支持横竖屏）
        private List<string> lstService = new List<string>() { "basicOpenOcr", "basicDetectDirectionOpenOcr" };
        private const string txtLanguage = "zh-CHS";
        private const string sougouAppid = "sogou_ocr_just_for_deepi";
        private const string sougouKey = "4b66a37108dab018ace616c4ae07e644";
        //private const string sougouAppid = "e6c944127e699ad1e985af5358534f3e";
        //private const string sougouKey = "********************************";

        protected override string GetHtml(OcrContent content)
        {
            string txtRndTick = GetRndTick().ToString();
            string txtSign = string.Empty;
            string text6 = content.strBase64;
            if (content.strBase64.Length > 1024)
            {
                text6 = content.strBase64.Substring(0, 1024);
            }
            var service = lstService.GetRndItem();
            string txtSignKey = string.Concat(new string[] { sougouAppid, service, txtRndTick, text6, sougouKey });
            txtSign = GetSign(txtSignKey);
            string strPost = string.Concat(new string[]
            {
            "service=", service,
            "&pid=", sougouAppid,
            "&salt=", txtRndTick,
            "&lang=", txtLanguage,
            "&image=", HttpUtility.UrlEncode(content.strBase64),
            "&sign=", txtSign
            });
            //"{\"result\":[{\"groupID\":0,\"content\":\"请点击下图中所有的靴子火装\\n\",\"frame\":[\"0,2\",\"192,2\",\"192,24\",\"0,24\"]}],\"success\":1,\"zly\":\"zly\",\"ocr_time\":541.4488315582275,\"id\":\"2309563187731103744\",\"lang\":\"zh-CHS\",\"direction\":0}"
            //{"result":[{"groupID":0,"content":"请点击下图中所有的靴子火装\n","frame":["0,2","192,2","192,24","0,24"]}],"success":1,"zly":"zly","ocr_time":541.4488315582275,"id":"2309563187731103744","lang":"zh-CHS","direction":0}
            string strTmp = WebClientSyncExt.GetHtml("https://deepi.sogou.com/api/sogouService", strPost, ExecTimeOutSeconds);
            if (!string.IsNullOrEmpty(strTmp) && strTmp.Contains("\"code\":1009,"))
            {
                BaseRecHelper.DisableByType(CommonLib.OcrType.文本, OcrType.GetHashCode());
            }
            return strTmp;
        }

        private long GetRndTick()
        {
            return Convert.ToInt64((DateTime.UtcNow - new DateTime(1970, 1, 1, 0, 0, 0, 0)).TotalSeconds);
        }
        private string GetSign(string string_6)
        {
            string result;
            if (string_6 == null)
            {
                result = null;
            }
            else
            {
                MD5 md = MD5.Create();
                byte[] array = md.ComputeHash(Encoding.UTF8.GetBytes(string_6));
                StringBuilder stringBuilder = new StringBuilder();
                for (int i = 0; i < array.Length; i++)
                {
                    stringBuilder.Append(array[i].ToString("x2"));
                }
                result = stringBuilder.ToString();
            }
            return result;
        }

    }
}