﻿using CommonLib;
using System;
using System.Collections.Generic;
using System.Collections.Specialized;
using System.IO;
using System.Security.Cryptography;
using System.Text;

namespace TransOcr
{
    /// <summary>
    /// 有道智云
    /// </summary>
    public class YouDaoLiteRec : BaseOcrRec
    {
        public YouDaoLiteRec()
        {
            OcrGroup = OcrGroupType.有道;
            OcrType = TransOcrType.有道Lite;
            MaxExecPerTime = 23;

            LstJsonPreProcessArray = new List<object>() { "resRegions" };

            StrResultJsonSpilt = "context";
            StrResultTransJsonSpilt = "tranContent";
            IsSupportVertical = true;
            LstVerticalLocation = new List<object>() { "boundingBox" };

            InitLanguage();
        }
        class YouDaoAccount
        {
            public string strAppId { get; set; }

            public string strSecretId { get; set; }
        }

        static List<YouDaoAccount> lstAppAccount = new List<YouDaoAccount>
        {
            new YouDaoAccount
            {
                strAppId = "zhudytest123",
                strSecretId =  "youdaoapiv120171"
            }
        };

        #region 支持的语言

        private void InitLanguage()
        {
            TransLanguageDic = new Dictionary<TransLanguageTypeEnum, string>
            {
                { TransLanguageTypeEnum.中文, "zh-CHS" },
                { TransLanguageTypeEnum.英文, "en" },
                { TransLanguageTypeEnum.日语, "ja" },
                { TransLanguageTypeEnum.韩语, "ko" },
                { TransLanguageTypeEnum.西班牙语, "es" },
                { TransLanguageTypeEnum.法语, "fr" },
                { TransLanguageTypeEnum.泰语, "th" },
                { TransLanguageTypeEnum.阿拉伯语, "ar" },
                { TransLanguageTypeEnum.俄语, "ru" },

                { TransLanguageTypeEnum.葡萄牙语, "pt" },
                { TransLanguageTypeEnum.德语, "de" },
                { TransLanguageTypeEnum.意大利语, "it" },
                { TransLanguageTypeEnum.希腊语, "el" },
                { TransLanguageTypeEnum.荷兰语, "nl" },
                { TransLanguageTypeEnum.波兰语, "pl" },
                { TransLanguageTypeEnum.保加利亚语, "bg" },
                { TransLanguageTypeEnum.爱沙尼亚语, "et" },
                { TransLanguageTypeEnum.丹麦语, "da" },
                { TransLanguageTypeEnum.芬兰语, "fi" },
                { TransLanguageTypeEnum.捷克语, "cs" },
                { TransLanguageTypeEnum.罗马尼亚语, "ro" },
                { TransLanguageTypeEnum.斯洛文尼亚语, "sl" },
                { TransLanguageTypeEnum.瑞典语, "sv" },
                { TransLanguageTypeEnum.匈牙利语, "hu" },
                { TransLanguageTypeEnum.越南语, "vi" }
            };
        }

        #endregion

        protected override string GetHtml(OcrContent content)
        {
            string from = "", to = "";
            InitLanguage(content, ref from, ref to);
            var account = lstAppAccount.GetRndItem();
            var dic = new NameValueCollection();
            string salt = ServerTime.DateTime.Millisecond.ToString();

            dic.Add("appKey", account?.strAppId);
            dic.Add("from", from);
            dic.Add("salt", salt);
            dic.Add("to", to);
            dic.Add("type", "2");
            TimeSpan ts = (DateTime.UtcNow - new DateTime(1970, 1, 1, 0, 0, 0, DateTimeKind.Utc));
            long millis = (long)ts.TotalMilliseconds;
            string curtime = Convert.ToString(millis / 1000);
            dic.Add("curtime", curtime);
            string signStr = account?.strAppId + salt + curtime + account?.strSecretId;
            string sign = ComputeHash(signStr);
            dic.Add("sign", sign);

            var url = "https://openapi.youdao.com/ocrtransapi";
            var byt = Convert.FromBase64String(content.strBase64);
            var file = new UploadFileInfo()
            {
                Name = "file",
                Filename = "1." + content.fileExt,
                ContentType = ApplicationTypeHelper.GetApplicationType(content.fileExt),
                Stream = new MemoryStream(byt)
            };
            var result = PostFile(url, new[] { file }, dic);
            return result;
        }


        protected static string ComputeHash(string input)
        {
            using (HashAlgorithm algorithm = new SHA256CryptoServiceProvider())
            {
                byte[] inputBytes = Encoding.UTF8.GetBytes(input);
                byte[] hashedBytes = algorithm.ComputeHash(inputBytes);
                return BitConverter.ToString(hashedBytes).Replace("-", "");
            }
        }

    }
}