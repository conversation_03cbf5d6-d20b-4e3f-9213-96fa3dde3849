﻿using System;
using System.Security.Cryptography;
using System.Threading;

namespace CommonLib
{
    public class LocalWaitLock
    {
        public static bool WaitLock(string key, TimeSpan tsExpired)
        {
            var result = false;
            try
            {
                using (Semaphore semaphore = GetSemaphore(key))
                {
                    result = semaphore.WaitOne(tsExpired);
                }
            }
            catch (Exception oe)
            {
                LogHelper.Log.Error(string.Format("WaitLock Error:{0}", key), oe);
            }
            //LogHelper.Log.Info(string.Format("WaitLock Result Key:{0},Result:{1}", newKey, result != null));
            return result;
        }

        private static Semaphore GetSemaphore(string objId)
        {
            if (!Semaphore.TryOpenExisting(objId, out Semaphore mutex))
            {
                mutex = new Semaphore(0, 1, objId);
            }
            return mutex;
        }

        public static void Set(string key)
        {
            try
            {
                if (Semaphore.TryOpenExisting(key, out Semaphore mutex))
                {
                    mutex.Release();
                }
            }
            catch (Exception oe)
            {
                Console.WriteLine(oe.Message);
            }
        }
    }
}
