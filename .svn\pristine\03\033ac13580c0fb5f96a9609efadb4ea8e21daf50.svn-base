﻿using CommonLib;
using System;
using System.Linq;
using System.Net;
using System.Web;
using System.Web.UI.WebControls;

namespace Account.Web
{
    public static class CommonRequest
    {
        public static string GetCDNUrl(this HttpRequest Request)
        {
            var lang = CommonTranslate.GetCurrentLang(Request, true);
            if (Equals(lang, CommonTranslate.StrDefaultLang))
            {
                return ConfigHelper.StrInnerCDNUrl;
            }
            else
            {
                return ConfigHelper.StrOuterCDNUrl;
            }
        }

        public static string GetStaticUrl(this HttpRequest Request)
        {
            var lang = CommonTranslate.GetCurrentLang(Request, true);
            if (Equals(lang, CommonTranslate.StrDefaultLang))
            {
                return ConfigHelper.StrInnerStaticUrl;
            }
            else
            {
                return ConfigHelper.StrOuterStaticUrl;
            }
        }

        public static string GetDownLoadUrl(this HttpRequest Request)
        {
            return GetCDNUrl(Request) + "update/Setup.exe";
        }

        public static string TongJiCode = @"
	            <script type=""text/javascript"">
                    (function (c, l, a, r, i, t, y) {
                        c[a] = c[a] || function () { (c[a].q = c[a].q || []).push(arguments) };
                        t = l.createElement(r); t.async = 1; t.src = ""https://www.clarity.ms/tag/"" + i;
                        y = l.getElementsByTagName(r)[0]; y.parentNode.insertBefore(t, y);
                    })(window, document, ""clarity"", ""script"", ""onkk5hry2l"");
                    var _hmt = _hmt || [];
                    (function () {
                        var hm = document.createElement(""script"");
                        hm.src = ""https://hm.baidu.com/hm.js?5ee048d7caaf0d439cf16309a1687754"";
                        var s = document.getElementsByTagName(""script"")[0];
                        s.parentNode.insertBefore(hm, s);
                    })();
                </script>";

        public static string TranslageJs = "<script src=\"/static/js/translate.js\"></script>";
        public static string NoAutoTranslageJs = "<script type=\"text/javascript\">isNeedTrans=false;</script><script src=\"/static/js/translate.js\"></script>";

        public static void Log(this HttpRequest Request, string strDesc, bool isErrorLevel = true, bool isShort = false)
        {
            var result = string.Format("{5}{4}{0}:{1}{2}{3}"
                        , Request.HttpMethod
                        , Request.Url.ToString() + Environment.NewLine
                        , isShort ? "" : Request.Form.AllKeys.Length > 0 ? "Body:" + string.Join("|", Request.Form.AllKeys.Select(p => string.Format("{0}->{1}", p, Request.Form[p]))) + Environment.NewLine : ""
                        , isShort ? "" : Request.Headers.Count > 0 ? "Header:" + string.Join("|", Request.Headers.AllKeys.Select(p => string.Format("{0}->{1}", p, Request.Headers[p]))) + Environment.NewLine : ""
                        , isShort ? "" : Request.ServerVariables.Count > 0 ? "ServerVariables:" + string.Join("|", Request.ServerVariables.AllKeys.Select(p => string.Format("{0}->{1}", p, Request.ServerVariables[p]))) + Environment.NewLine : ""
                        , string.IsNullOrEmpty(strDesc) ? "" : strDesc + Environment.NewLine
                        );
            if (isErrorLevel)
                LogHelper.Log.Error(result);
            else
                LogHelper.Log.Info(result);
        }

        private const string StrUnknowIp = "unknown";

        /// <summary>
        /// 获取客户端IP地址
        /// </summary>
        /// <returns>若失败则返回回送地址</returns>
        public static string GetIPAddress(this HttpRequest Request)
        {
            if (Equals(Request.UserAgent, "IIS Application Initialization Preload"))
            {
                return string.Empty;
            }
            //如果客户端使用了代理服务器，则利用HTTP_X_FORWARDED_FOR找到客户端IP地址
            var userHostAddress = GetTrimIp(HttpContext.Current.Request.ServerVariables["HTTP_X_FORWARDED_FOR"]);
            if (isBadIp(userHostAddress))
            {
                userHostAddress = GetTrimIp(HttpContext.Current.Request.ServerVariables["HTTP_X_CLUSTER_CLIENT_IP"]);
            }
            if (isBadIp(userHostAddress))
            {
                userHostAddress = GetTrimIp(Request.GetValue("CF-CONNECTING-IP"));
            }
            if (isBadIp(userHostAddress))
            {
                userHostAddress = GetTrimIp(Request.GetValue("X-Real-IP"));
            }
            if (isBadIp(userHostAddress))
            {
                userHostAddress = GetTrimIp(Request.GetValue("X-Forwarded-For"));
            }
            if (isBadIp(userHostAddress))
            {
                userHostAddress = GetTrimIp(Request.GetValue("True-Client-IP"));
            }
            if (isBadIp(userHostAddress))
            {
                userHostAddress = GetTrimIp(Request.GetValue("X-Client-IP"));
            }
            if (isBadIp(userHostAddress))
            {
                userHostAddress = GetTrimIp(Request.GetValue("X-ProxyUser-Ip"));
            }
            if (isBadIp(userHostAddress))
            {
                userHostAddress = GetTrimIp(Request.GetValue("X-Original-Forwarded-For"));
            }
            //否则直接读取REMOTE_ADDR获取客户端IP地址
            if (isBadIp(userHostAddress))
            {
                userHostAddress = GetTrimIp(HttpContext.Current.Request.ServerVariables["REMOTE_ADDR"]);
            }
            if (isBadIp(userHostAddress))
            {
                userHostAddress = HttpContext.Current.Request.UserHostAddress;
            }
            if (isBadIp(userHostAddress))
            {
                userHostAddress = string.Empty;
            }
            if (string.IsNullOrEmpty(userHostAddress))
            {
                Request.Log(string.Format("IP地址为空！userHostAddress：{0}", userHostAddress), true, false);
            }
            return userHostAddress;
        }

        static string GetTrimIp(string userHostAddress)
        {
            return userHostAddress?.Replace(StrUnknowIp, "").Replace(" ", ",").Replace(";", ",").Trim()
                .Split(new string[] { "," }, StringSplitOptions.RemoveEmptyEntries)
                .FirstOrDefault(p => IPAddress.TryParse(p, out var ip));
        }

        static bool isBadIp(string userHostAddress)
        {
            return string.IsNullOrEmpty(userHostAddress) || userHostAddress.ToLower().Contains(StrUnknowIp) || IsPrivateNetwork3(userHostAddress);
        }

        static bool IsPrivateNetwork3(string ipv4Address)
        {
            if (IPAddress.TryParse(ipv4Address, out var ip))
            {
                if (ip.AddressFamily == System.Net.Sockets.AddressFamily.InterNetworkV6)
                {
                    return ip.IsIPv6LinkLocal || ip.IsIPv6SiteLocal || ip.IsIPv6Teredo;
                }
                else
                {
                    byte[] ipBytes = ip.GetAddressBytes();
                    if (ipBytes[0] == 10) return true;
                    if (ipBytes[0] == 172 && ipBytes[1] >= 16 && ipBytes[1] <= 31) return true;
                    if (ipBytes[0] == 192 && ipBytes[1] == 168) return true;
                }
                return false;
            }
            else
            {
                return true;
            }
        }
    }
}