$(document).ready((function(){var t={activeIndex:-1,init:function(){var t=this;$(".rights .card-item").on("mouseenter",(function(){t.setIndex($(this).parent().index())}))},setIndex:function(t){this.activeIndex!=t&&($(".rights .card-item").removeClass("active"),$(".rights .right-list").children().eq(t).find(".card-item").addClass("active"),this.activeIndex=t)}};({activeIndex:-1,list:[{title:"代理伙伴",text:"成为 SUBMAIL 代理伙伴， 通过自建渠道和自有资源向客户推广，销售赛邮产品。"},{title:"软件开发公司",text:"为客户提供软件定制开发等相关服务，联手打造专业、易用、成熟的产品方案。"},{title:"低代码开发平台",text:"为开发者提供高效开发接入的开发工具，满足插件嵌入等需求，可进行二次开发。"}],init:function(){this.initDots()},initDots:function(){var t="";$.each(this.list,(function(i,e){t+=`\n          <div class="dots-item">\n              <div class="text">${e.title}</div>\n          </div>\n        `})),$(".why-container .dots").empty().append(t),$(".why-container .dots-item").on("click",(function(){})),this.setDot(0)},setDot:function(t){if(t!=this.activeIndex){this.activeIndex=t,$(".why-container .dots-item").removeClass("active"),$(".why-container .dots-item").eq(t).addClass("active");this.list[t].title,this.list[t].text}}}).init(),t.init()}));