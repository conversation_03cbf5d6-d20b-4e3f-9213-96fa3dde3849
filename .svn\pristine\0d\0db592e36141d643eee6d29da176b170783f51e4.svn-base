﻿using System;
using System.Collections.Generic;
using System.Net;
using System.Net.Sockets;
namespace NewTicket
{
    public sealed class TCPNetworkStream : IDisposable
    {
        private int int_0;
        private int int_1;
        private int int_2;
        private int int_3;
        private string string_0;
        private string string_1;
        private TcpClient tcpClient_0;

        public TCPNetworkStream()
        {
            this.tcpClient_0 = null;
            this.string_0 = "";
            this.int_0 = 0;
            this.int_1 = 0;
            this.string_1 = null;
            this.int_2 = 8192;
            this.int_3 = 8192;
        }

        public TCPNetworkStream(string _serverIp, int _serverPort)
            : this(_serverIp, _serverPort, 0, null)
        {
        }

        public TCPNetworkStream(string _serverIp, int _serverPort, int iniLocalPort)
            : this(_serverIp, _serverPort, iniLocalPort, null)
        {
        }

        public TCPNetworkStream(string _serverIp, int _serverPort, int iniLocalPort, string localIP)
        {
            this.tcpClient_0 = null;
            this.string_0 = "";
            this.int_0 = 0;
            this.int_1 = 0;
            this.string_1 = null;
            this.int_2 = 8192;
            this.int_3 = 8192;
            this.string_0 = _serverIp;
            this.int_0 = _serverPort;
            this.int_1 = iniLocalPort;
            this.string_1 = localIP;
        }

        public void Dispose()
        {
            if (this.tcpClient_0 != null)
            {
                this.tcpClient_0.Close();
            }
        }

        public NetworkStream getNetworkStream()
        {
            NetworkStream stream;
            try
            {
                IPAddress address = System.Net.IPAddress.Parse(this.string_0);
                if (!((address.AddressFamily != AddressFamily.InterNetworkV6) || Socket.OSSupportsIPv6))
                {
                    throw new NotSupportedException("The IPAddress Server listened is IPv6 ,but current OS doesn't Support IPv6 !");
                }
                if (!((address.AddressFamily != AddressFamily.InterNetwork) || Socket.SupportsIPv4))
                {
                    throw new NotSupportedException("The IPAddress Server listened is IPv4 ,but current OS doesn't Support IPv4 !");
                }
                if ((this.int_1 <= 0) && (this.string_1 == null))
                {
                    this.tcpClient_0 = new TcpClient(address.AddressFamily);
                }
                else
                {
                    if (this.string_1 == null)
                    {
                        List<string> list = NetworkHelper.GetIPList(address.AddressFamily);
                        this.string_1 = list[0];
                    }
                    this.tcpClient_0 = new TcpClient(new IPEndPoint(System.Net.IPAddress.Parse(this.string_1), this.int_1));
                }
                this.tcpClient_0.Client.SetSocketOption(SocketOptionLevel.Socket, SocketOptionName.ReuseAddress, true);
                this.tcpClient_0.Client.SetSocketOption(SocketOptionLevel.Socket, SocketOptionName.SendBuffer, this.int_2);
                this.tcpClient_0.Client.SetSocketOption(SocketOptionLevel.Socket, SocketOptionName.ReceiveBuffer, this.int_3);
                this.tcpClient_0.Connect(this.string_0, this.int_0);
                this.int_1 = ((IPEndPoint)this.tcpClient_0.Client.LocalEndPoint).Port;
                this.string_1 = ((IPEndPoint)this.tcpClient_0.Client.LocalEndPoint).Address.ToString();
                stream = this.tcpClient_0.GetStream();
            }
            catch (Exception exception)
            {
                if (this.tcpClient_0 != null)
                {
                    this.tcpClient_0.Close();
                }
                throw;
            }
            return stream;
        }

        public int SocketReceiveBuffSize
        {
            get
            {
                return this.int_3;
            }
            set
            {
                this.int_3 = value;
            }
        }

        public int SocketSendBuffSize
        {
            get
            {
                return this.int_2;
            }
            set
            {
                this.int_2 = value;
            }
        }

        public int ServerPort
        {
            get
            {
                return this.int_0;
            }
            set
            {
                this.int_0 = value;
            }
        }
        public string ServerIP
        {
            get
            {
                return this.string_0;
            }
            set
            {
                this.string_0 = value;
            }
        }

        public int LocalPort
        {
            get
            {
                return this.int_1;
            }
            set
            {
                this.int_1 = value;
            }
        }


        public string LocalIP
        {
            get
            {
                return this.string_1;
            }
            set
            {
                this.string_1 = value;
            }
        }
    }

}