﻿using System;
using System.Collections.Generic;

using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Data;
using System.Configuration;
using System.IO;
using System.Text;
using CommonLib;
using System.Security.Cryptography;

namespace Code.Manager.Web
{
    public partial class RCode : System.Web.UI.Page
    {
        protected void Page_Load(object sender, EventArgs e)
        {
            if (string.IsNullOrEmpty(BoxUtil.GetStringFromObject(Request.QueryString["pwd"]).Trim()))
            {
                Response.End();
                return;
            }
        }

        /// <summary>
        /// MD5 16位加密 加密后密码为小写
        /// </summary>
        /// <param name="ConvertString"></param>
        /// <returns></returns>
        private string GetMd5Str16(string ConvertString = "")
        {
            var result = "";
            if (string.IsNullOrEmpty(ConvertString))
            {
                ConvertString = Guid.NewGuid().ToString().Replace("-", "");
            }
            try
            {
                using (MD5CryptoServiceProvider md5 = new MD5CryptoServiceProvider())
                {
                    string t2 = BitConverter.ToString(md5.ComputeHash(UTF8Encoding.Default.GetBytes(ConvertString)), 4, 8);
                    result = t2.Replace("-", "").ToLower();
                }
            }
            catch { }
            if (string.IsNullOrEmpty(result))
            {
                result = ConvertString;
            }
            return result;
        }

        protected void btnOK_Click(object sender, EventArgs e)
        {
            if (txtPwd.Text.Trim() != "aa89128")
                return;

            if (string.IsNullOrEmpty(txtApp.Text.Trim()))
            {
                txtApp.Text = Guid.NewGuid().ToString();
            }
            var strMacCode = txtApp.Text.Trim();

            //CodeInfo entity = null;

            //try
            //{
            //    CodeInfo info = new CodeInfo()
            //    {
            //        Balance = BoxUtil.GetInt64FromObject(txtAmount.Text),
            //        TotalAmount = BoxUtil.GetInt64FromObject(txtAmount.Text),
            //        DtAdd = DateTime.Now,
            //        Key = strMacCode,
            //        DtExpired = DateTime.Now.AddMonths(6),
            //        IsEnable = true,
            //        Remark = txtMoney.Text

            //    };
            //    RdsCacheHelper.YuECache.Add(info);

            //    lblMsg.Text = ("注册信息：<br />" + "===================<br />" + info + "<br />===================" + "<br />");
            //}
            //catch (Exception oe)
            //{
            //    lblMsg.Text = ("注册失败！" + oe.Message);
            //}
        }

        protected void btnQuery_Click(object sender, EventArgs e)
        {
            if (txtPwd.Text.Trim() != "aa89128")
                return;
            var strNewKey = txtApp.Text.Trim();

            if (string.IsNullOrEmpty(strNewKey))
            {
                lblMsg.Text = ("码子不能为空！");
                return;
            }

            try
            {
                StringBuilder sb = new StringBuilder();

                //var entity = RdsCacheHelper.YuECache.GetCode(strNewKey);
                //if (entity != null)
                //{
                //    sb.Append("===================<br />" + entity + "<br />===================");
                //}

                //if (sb.Length > 0)
                //{
                //    lblMsg.Text = ("注册信息：<br />" + sb.ToString() + "<br />");
                //}
                //else
                //{
                //    lblMsg.Text = ("查询失败！");
                //}
            }
            catch (Exception oe)
            {
                lblMsg.Text = ("查询失败！" + oe.Message);
            }
        }


    }
}