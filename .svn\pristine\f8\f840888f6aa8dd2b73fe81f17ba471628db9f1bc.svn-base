﻿using System;
using System.Web.Script.Serialization;

namespace CommonLib
{
    public static class CodeProcessHelper
    {
        private static JavaScriptSerializer JavaScriptSerializer = new JavaScriptSerializer();
        public static object objLock = new object();

        public static string GetFileResultHtml(string json)
        {
            var content = JavaScriptSerializer.Deserialize<OcrContent>(json);
            var result = FileResultHtmlHelper.GetDownLoadHtml(content);
            return result;
        }

        public static string GetMathResultHtml(string json)
        {
            var content = JavaScriptSerializer.Deserialize<OcrContent>(json);
            var result = MathResultHtmlHelper.GetDownLoadHtml(content);
            return result;
        }

        public static string SendToProcessPool(string strImage, string url, UserTypeEnum siteFlag, OcrType ocrType, OcrGroupType groupType,
            TransLanguageTypeEnum from, TransLanguageTypeEnum to, int contentLength, int? processId
            , bool fromLeftToRight, bool fromTopToDown, string fileExt, int timeOut)
        {
            var result = "";
            try
            {
                if (!string.IsNullOrEmpty(strImage) || !string.IsNullOrEmpty(url))
                {
                    var strKey = Guid.NewGuid().ToString().Replace("-", "");
                    OcrContent ocr = null;
                    using (var img = new CusImageEntity()
                    {
                        StrIndex = strKey,
                        DtAdd = ServerTime.DateTime.Ticks,
                        DtExpired = ServerTime.DateTime.AddSeconds(timeOut).Ticks,
                        UserType = siteFlag,
                        StrImg = strImage,
                        ImgUrl = url,
                        OcrType = ocrType,
                        IsFromLeftToRight = fromLeftToRight,
                        IsFromTopToDown = fromTopToDown,
                        OcrGroup = groupType,
                        ProcessId = processId,
                        FileExt = fileExt,
                        FromLanguage = from,
                        ToLanguage = to,
                        FileContentLength = contentLength
                    })
                    {
                        RdsCacheHelper.OcrProcessQueue.Push(img, 0, 1, 0);
                        ocr = RdsCacheHelper.OcrResult.Pop(img.StrIndex, new TimeSpan(img.DtExpired - img.DtAdd));
                    }
                    if (ocr == null)
                    {
                        ocr = new OcrContent()
                        {
                            id = strKey,
                            ocrType = ocrType,
                            url = url
                        };
                    }
                    result = JavaScriptSerializer.Serialize(ocr);
                    //LogHelper.Log.Info(result);
                }
            }
            catch (Exception oe)
            {
                LogHelper.Log.Error("获取结果出错！", oe);
            }
            return result;
        }

        public static string SendToFileStatusProcessPool(string taskId, int ocrType)
        {
            var result = "";
            try
            {
                var strKey = Guid.NewGuid().ToString();
                var img = new CusFileStatusEntity()
                {
                    OcrType = ocrType,
                    TaskId = taskId
                };
                RdsCacheHelper.FileStatusProcessQueue.Push(img, 0, 0, 5);
                var ocr = RdsCacheHelper.FileStatusResultQueue.Pop(img.TaskId, new TimeSpan(0, 0, 3));
                result = ocr?.ToString();
            }
            catch (Exception oe)
            {
                LogHelper.Log.Error("获取文件状态结果出错！", oe);
            }
            return result;
        }

        public static CusFileStatusEntity GetFileStatusFromProcessPool()
        {
            var strTmp = RdsCacheHelper.FileStatusProcessQueue.DequeueBlockingCacheMessage();
            return JavaScriptSerializer.Deserialize<CusFileStatusEntity>(strTmp);
        }

        public static CusImageEntity GetFromProcessPool()
        {
            return RdsCacheHelper.OcrProcessQueue.DequeueBlockingCacheMessage();
        }

        public static string SetOcrResult(OcrContent content)
        {
            var result = "";
            try
            {
                lock (objLock)
                {
                    RdsCacheHelper.OcrResult.Push(content.id, content, 0, 1, 0);
                }
            }
            catch (Exception oe)
            {
                LogHelper.Log.Error("设置OCR结果出错！", oe);
            }
            return result;
        }

        public static string SetFileStatusResult(ProcessStateEntity content)
        {
            var result = "";
            try
            {
                lock (objLock)
                {
                    RdsCacheHelper.FileStatusResultQueue.Push(content.taskId, JavaScriptSerializer.Serialize(content), 0, 1, 0);
                }
            }
            catch (Exception oe)
            {
                LogHelper.Log.Error("设置OCR结果出错！", oe);
            }
            return result;
        }

        public static string GetOcrResultById(string strId, TimeSpan timeSpan)
        {
            var result = "";
            try
            {
                var res = RdsCacheHelper.OcrResult.Pop(strId, timeSpan);
                if (res != null)
                {
                    result = JavaScriptSerializer.Serialize(res);
                }
            }
            catch (Exception oe)
            {
                LogHelper.Log.Error("获取结果出错！", oe);
            }
            return result;
        }

        public static string GetOtherOcrResultById(string strId)
        {
            var result = "";
            try
            {
                if (!string.IsNullOrEmpty(strId))
                {
                    var lstOcr = RdsCacheHelper.OcrResult.PopAll(strId);
                    if (lstOcr?.Count >= 0)
                    {
                        result = JavaScriptSerializer.Serialize(lstOcr);
                    }
                }
            }
            catch (Exception oe)
            {
                LogHelper.Log.Error("获取结果出错！", oe);
            }
            return result;
        }

    }
}
