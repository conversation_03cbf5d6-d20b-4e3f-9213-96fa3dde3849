﻿using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Drawing;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using CommonLib;
using DaMaLib;

namespace BaiDuAPI
{
    public static class RemoteDaMaHelper
    {
        private const int MaxExecTime = 4000;
        //private const int PerTaskMaxExecTime = 4000;

        private static List<DaMaType> _lstNowEnableDamaTypes;

        static RemoteDaMaHelper()
        {
            LimitHelper = new IpLimitManager(ConfigHelper.NMaxExecPerSecond, 1000, true);
        }

        public static IpLimitManager LimitHelper { get; set; }

        private static List<DaMaType> LstNowEnableDamaTypes
        {
            get
            {
                if (_lstNowEnableDamaTypes == null || _lstNowEnableDamaTypes.Count <= 0)
                {
                    _lstNowEnableDamaTypes = GetEnableTypes();
                }
                return _lstNowEnableDamaTypes;
            }
            set { _lstNowEnableDamaTypes = value; }
        }

        public static void AddRequestTime(string siteFlag)
        {
            RdsCacheHelper.CountCache.Increment(ConfigHelper.GetFlagName("验证码识别", siteFlag), 1);
        }

        public static string GetCode(byte[] byt, bool isLogin, string siteFlag, ref string strFrom)
        {
            var result = "";
            try
            {
                if (GuangSuCode.IsGuangSuEnable || JiSuDaMa.IsJiSu || ZhiXingCode.IsZhiXing ||
                    ZhuShouDaMaHelper.IsEnable || _360Code.Is360Enable)
                {
                    result = GetCodeByTask(byt, isLogin, siteFlag, ref strFrom);
                    //result = GetCodeByParallel(byt, isLogin, siteFlag, ref strFrom);
                    ////result = GetCodeByPer(byt, isLogin, siteFlag, ref strFrom);
                    byt = null;
                }
            }
            catch (Exception oe)
            {
                ConfigHelper._Log.Error("识别结果报错!", oe);
            }
            return result;
        }

        private static string GetCode(Bitmap img, bool isLogin, string siteFlag, ref string strCodeFrom)
        {
            var result = "";
            try
            {
                var byt = CommonCompress.ImageToByte(img);
                result = GetCode(byt, isLogin, siteFlag, ref strCodeFrom);
                byt = null;
            }
            catch (Exception oe)
            {
                ConfigHelper._Log.Error("识别结果报错!", oe);
            }
            return result;
        }

        public static string GetCode(string strCode, bool isLogin, string siteFlag, ref string strCodeFrom)
        {
            var result = "";
            try
            {
                var byt = CommonCompress.GetByteFromBase64(strCode);
                result = GetCode(byt, isLogin, siteFlag, ref strCodeFrom);
                byt = null;
            }
            catch (Exception oe)
            {
                ConfigHelper._Log.Error("识别结果报错!", oe);
            }
            finally
            {
                strCode = null;
            }
            return result;
        }

        private static List<DaMaType> GetEnableTypes()
        {
            var lstType = new List<DaMaType>();
            if (ZhuShouDaMaHelper.IsEnable)
            {
                lstType.Add(DaMaType.助手);
            }
            if (GuangSuCode.IsGuangSuEnable)
            {
                lstType.Add(DaMaType.光速);
            }
            if (JiSuDaMa.IsJiSu)
            {
                lstType.Add(DaMaType.极速);
            }
            if (ZhiXingCode.IsZhiXing)
            {
                lstType.Add(DaMaType.智行);
            }
            if (_360Code.Is360Enable)
            {
                lstType.Add(DaMaType.三六零);
            }
            return lstType;
        }

        private static string GetCodeByParallel(byte[] byt, bool isLogin, string siteFlag, ref string strFrom)
        {
            var result = "";
            try
            {
                using (var myResetEvent = new AutoResetEvent(false))
                {
                    DaMaType nDaMaType = 0;
                    var count = 0;
                    Task.Run(() =>
                    {
                        Parallel.ForEach(LstNowEnableDamaTypes, type =>
                        {
                            try
                            {
                                var tmpCode = "";
                                if (string.IsNullOrEmpty(result))
                                    tmpCode = GetCodeByType(type, byt, isLogin, siteFlag);
                                if (!string.IsNullOrEmpty(tmpCode))
                                {
                                    nDaMaType = type;
                                    result = tmpCode;
                                    myResetEvent.Set();
                                }
                                count++;
                                if (count == LstNowEnableDamaTypes.Count)
                                {
                                    myResetEvent.Set();
                                }
                            }
                            catch (Exception oe)
                            {
                                //Log.WriteError(oe);
                            }
                        });
                    });
                    myResetEvent.WaitOne(MaxExecTime);
                    strFrom = nDaMaType.GetHashCode() > 0 ? nDaMaType.ToString() : "";
                    myResetEvent.Dispose();
                }
            }
            catch (Exception oe)
            {
                ConfigHelper._Log.Error("GetCodeByParallel", oe);
            }
            finally
            {
                byt = null;
            }
            return result;
        }

        private static string GetCodeByTask(byte[] byt, bool isLogin, string siteFlag, ref string strFrom)
        {
            var result = "";
            try
            {
                var stop = Stopwatch.StartNew();
                var lstTask = LstNowEnableDamaTypes.AsParallel()
                    .Select(item =>
                    {
                        Task<string> task = null;
                        var cancel = new CancellationTokenSource(MaxExecTime);
                        try
                        {
                            task = Task.Run(() =>
                            {
                                var tmpResult = GetCodeByType(item, byt, isLogin, siteFlag);

                                if (string.IsNullOrEmpty(tmpResult) && stop.ElapsedMilliseconds < MaxExecTime)
                                {
                                    Thread.Sleep((int)(MaxExecTime - stop.ElapsedMilliseconds));
                                }
                                return string.Format("{0}-{1}", tmpResult, item);
                            }, cancel.Token);

                            task.ContinueWith(p =>
                            {
                                if (cancel != null)
                                {
                                    cancel.Cancel();
                                    cancel.Dispose();
                                    cancel = null;
                                }
                            });
                        }
                        catch (Exception oe)
                        {
                            ConfigHelper._Log.Error("GetCodeByTask_Task", oe);
                        }
                        return task;
                    }).ToArray();

                Task.WaitAny(lstTask, MaxExecTime);

                var tmpRes =
                    lstTask.Where(
                        p =>
                            p.Status == TaskStatus.RanToCompletion && !string.IsNullOrEmpty(p.Result) &&
                            !p.Result.StartsWith("-"))
                        .Select(p => p.Result).FirstOrDefault();

                if (!string.IsNullOrEmpty(tmpRes))
                {
                    //ConfigHelper._Log.InfoFormat("【{0}】", tmpRes);
                    if (tmpRes.Contains(","))
                    {
                        result = CommonHelper.SubStringHorspool(tmpRes, "", "-");
                        strFrom = CommonHelper.SubStringHorspool(tmpRes, "-");
                    }
                }
            }
            catch (Exception oe)
            {
                ConfigHelper._Log.Error("GetCodeByTask", oe);
            }
            finally
            {
                byt = null;
            }
            return result;
        }

        private static string GetCodeByPer(byte[] byt, bool isLogin, string siteFlag, ref string strFrom)
        {
            var result = "";
            var strTmpFrom = "";
            try
            {
                using (var myResetEvent = new AutoResetEvent(false))
                {
                    new Thread(q =>
                    {
                        foreach (var item in LstNowEnableDamaTypes)
                        {
                            if (string.IsNullOrEmpty(result))
                            {
                                result = GetCodeByType(item, byt, isLogin, siteFlag);
                                if (!string.IsNullOrEmpty(result))
                                {
                                    strTmpFrom = item.ToString();
                                    break;
                                }
                            }
                        }
                        myResetEvent.Set();
                    }) { Priority = ThreadPriority.Highest }.Start();
                    myResetEvent.WaitOne(MaxExecTime);
                    myResetEvent.Dispose();
                }
            }
            catch (Exception oe)
            {
                ConfigHelper._Log.Error("GetCodeByPer", oe);
            }
            finally
            {
                if (!string.IsNullOrEmpty(result))
                    strFrom = strTmpFrom;
            }
            return result;
        }

        private static string GetCodeByType(DaMaType type, byte[] byt, bool isLogin, string siteFlag)
        {
            var result = "";
            try
            {
                switch (type)
                {
                    case DaMaType.极速:
                        if (JiSuDaMa.IsJiSu)
                        {
                            result = JiSuDaMa.GetCode(byt);
                        }
                        break;
                    case DaMaType.光速:
                        if (GuangSuCode.IsGuangSuEnable)
                        {
                            result = GuangSuCode.GetCode(byt, !isLogin);
                        }
                        break;
                    case DaMaType.智行:
                        if (ZhiXingCode.IsZhiXing)
                        {
                            result = ZhiXingCode.GetCode(byt);
                        }
                        break;
                    case DaMaType.助手:
                        if (ZhuShouDaMaHelper.IsEnable)
                        {
                            result = ZhuShouDaMaHelper.GetCodeByBytes(byt, isLogin, siteFlag);
                        }
                        break;
                    case DaMaType.三六零:
                        if (_360Code.Is360Enable)
                        {
                            result = _360Code.GetCode(byt, isLogin);
                        }
                        break;
                    case DaMaType.本地:
                        break;
                    case DaMaType.其他:
                        break;
                }
            }
            catch (Exception oe)
            {
                ConfigHelper._Log.Error("GetCodeByType", oe);
            }
            return result;
        }
    }
}