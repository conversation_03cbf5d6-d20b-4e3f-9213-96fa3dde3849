﻿using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using System.Collections.Concurrent;

namespace NewTicket
{
    public static class CommonMSG
    {

        private static FesstionEntity nowFetion = new FesstionEntity();
        private static EmailEntity nowEmail = new EmailEntity();
        private static QQEntity nowQQ = new QQEntity();

        public static QQEntity NowQQ
        {
            get { return CommonMSG.nowQQ; }
            set { CommonMSG.nowQQ = value; }
        }

        public static FesstionEntity NowFetion
        {
            get { return nowFetion; }
            set { nowFetion = value; }
        }

        public static EmailEntity NowEmail
        {
            get { return nowEmail; }
            set { nowEmail = value; }
        }

        public static List<URLMSG> lstTipURL = new List<URLMSG>();

        public static void AddTipURL(string strURL)
        {
            try
            {
                lock (lstTipURL)
                {
                    if (lstTipURL == null)
                        lstTipURL = new List<URLMSG>();
                    if (!lstTipURL.Exists(url => url.StrURL.Equals(strURL)))
                    {
                        lstTipURL.Add(new URLMSG() { StrURL = strURL, IsSend = false });
                    }
                }
            }
            catch (Exception oe)
            {
                Log.WriteError(oe);
            }
        }

        public static void SendAllURL()
        {
            try
            {
                if (CommonMSG.lstTipURL != null && CommonMSG.lstTipURL.Count > 0)
                {
                    lock (CommonMSG.lstTipURL)
                    {
                        Parallel.ForEach<URLMSG>(CommonMSG.lstTipURL, uut =>
                        {
                            if (!uut.IsSend)
                            {
                                try
                                {
                                    string strTmp = CommonMethod.GetServerHtml(uut.StrURL, SiteType.tt, CommonString.HostTicketURL);
                                    uut.IsSend = !string.IsNullOrEmpty(strTmp) && strTmp.IndexOf("true", StringComparison.OrdinalIgnoreCase) >= 0;
                                }
                                catch (Exception oe)
                                {
                                    Log.WriteError(oe);
                                }
                            }
                        });
                    }
                }
            }
            catch (AggregateException oe)
            {
                foreach (Exception item in oe.InnerExceptions)
                {
                    Log.WriteError("SendAllURL异常", item);
                }
            }
            catch (Exception oe)
            {
                Log.WriteError(oe);
            }
            try
            {
                CommonMSG.lstTipURL.RemoveAll(p => p.IsSend);
            }
            catch { }
        }

        public static Queue<string> lstTipMSG = new Queue<string>();

        public static void AddSuccessMSG(string strMSG, string userName)
        {
            try
            {
                lock (lstTipMSG)
                {
                    if (!lstTipMSG.Contains(strMSG))
                    {
                        lstTipMSG.Enqueue(strMSG);
                    }
                }
            }
            catch (Exception oe)
            {
                Log.WriteError("AddSuccessMSG出错", oe);
            }
        }

        public static string GetNowSuccessMSG()
        {
            string strMSG = string.Empty;
            try
            {
                lock (lstTipMSG)
                {
                    try
                    {
                        strMSG = lstTipMSG.Dequeue();
                        //if (CommonString.isDebug)
                        //{
                        //    Console.WriteLine(string.Format("Get MSG:{0}", strMSG));
                        //}
                    }
                    catch (Exception oe)
                    {
                        Log.WriteError("GetNowSuccessMSG出错", oe);
                    }
                }
            }
            catch (Exception oe)
            {
                Log.WriteError("GetNowSuccessMSG出错", oe);
            }
            return strMSG;
        }

        public static ConcurrentBag<NoticeArgs> lstMSG = new ConcurrentBag<NoticeArgs>();
        //private static List<NoticeArgs> lstMSG = new List<NoticeArgs>();
        private static Thread msgThread = null;
        //private static Thread cacheThread = null;
        private static Thread onLineCheck = null;

        public static void AddMSG(string msg, string from = "助手", bool isShow = true)
        {
            try
            {
                if (!CommonString.isDebug && ((msg + "").Length == 0 || msg.Contains("系统忙") || msg.Contains("插件") || msg.Contains("非法请求")))
                {
                    return;
                }
                if (isShow)
                    lstMSG.Add(new NoticeArgs(from, msg, CommonString.serverTime.ToString("HH:mm:ss fff")) { });
            }
            catch { }
        }

        public static List<NoticeArgs> lstNotice = new List<NoticeArgs>();

        public static void SendMSG()
        {
            if (msgThread == null)
            {
                try
                {
                    msgThread = new Thread(delegate()
                    {
                        while (!CommonString.isExit)
                        {
                            if (lstMSG != null && lstMSG.Count > 0)
                            {
                                try
                                {
                                    lock (lstMSG)
                                    {
                                        if (CommonString.IsSingleTask)
                                            FormMain.AddMSG(lstMSG.ToArray());
                                        else
                                            NewFormMain.AddMSG(lstMSG.ToArray());
                                        lstMSG = new ConcurrentBag<NoticeArgs>();
                                    }
                                }
                                catch (Exception oe)
                                {
                                    Log.WriteError(oe);
                                }
                            }
                            System.Threading.Thread.Sleep(100);
                        }
                    });//{ IsBackground = true, Priority = ThreadPriority.Highest };
                    msgThread.Start();
                }
                catch (Exception oe)
                {
                    Log.WriteError("SendMSG出错", oe);
                }
            }
        }

    }

    public class URLMSG
    {
        private string strURL = "";

        public string StrURL
        {
            get { return strURL; }
            set { strURL = value; }
        }
        private bool isSend = false;

        public bool IsSend
        {
            get { return isSend; }
            set { isSend = value; }
        }
    }
}
