(window.webpackJsonp=window.webpackJsonp||[]).push([[25],{2093:function(e,t,n){n(2102),e.exports=n(237)},2094:function(e,t,n){var a=n(114);a(a.P,"Array",{fill:n(2095)}),n(407)("fill")},2095:function(e,t,n){"use strict";var a=n(261),r=n(607),o=n(216);e.exports=function(e){for(var t=a(this),n=o(t.length),i=arguments.length,s=r(i>1?arguments[1]:void 0,n),c=i>2?arguments[2]:void 0,l=void 0===c?n:r(c,n);l>s;)t[s++]=e;return t}},2102:function(e,t,n){"use strict";n.r(t);n(919);var a=n(226),r=n(555),o=(n(32),n(7)),i=n.n(o),s=n(8),c=n.n(s),l=n(9),u=n.n(l),p=n(10),m=n.n(p),d=n(6),h=n.n(d),f=(n(67),n(72),n(0)),v=n.n(f),y=n(5),g=n.n(y),_=(n(185),n(305),n(106),n(205),function(){function e(){i()(this,e)}return c()(e,null,[{key:"isPresent",value:function(e){return void 0!==e&&(null!==e&&""!==e)}},{key:"parse",value:function(e){var t={};return e.split("&").forEach((function(e){var n=e.split("=");n[1]&&(t[n[0]]=decodeURIComponent(n[1]))})),t}},{key:"generate",value:function(e){var t=[];for(var n in e)this.isPresent(e[n])&&t.push("".concat(n,"=").concat(e[n]));return t.join("&")}},{key:"get",value:function(){return this.parse(location.search.substring(1))}},{key:"set",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=t.merge,a=void 0!==n&&n,r=t.reload,o=void 0!==r&&r,i=t.path,s=void 0===i?null:i;if(a){var c=this.get();for(var l in c)l in e||(e[l]=c[l])}var u=this.generate(e);return s=s||location.pathname,u.length>0&&(s+="?".concat(u)),o?window.location=s:history.pushState({},document.title,s),s}},{key:"fetch",value:function(e){function t(){return e.apply(this,arguments)}return t.toString=function(){return e.toString()},t}((function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.get(),t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=this.set(e,t);return fetch(n,{headers:{"Content-Type":"application/json",Accept:"application/json"},credentials:"same-origin"}).then((function(e){return e.json()}))}))}]),e}()),E=n(26),b=n.n(E),D=n(75),N=n.n(D),w=n(157),k=(n(218),n(568)),R=n.n(k);function C(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,a=h()(e);if(t){var r=h()(this).constructor;n=Reflect.construct(a,arguments,r)}else n=a.apply(this,arguments);return m()(this,n)}}var T=function(e){u()(n,e);var t=C(n);function n(){return i()(this,n),t.apply(this,arguments)}return c()(n,[{key:"_renderIncidentMessage",value:function(){return{__html:R()(this.props.incident.message)}}},{key:"_renderIncidentTimestamp",value:function(){return{__html:this.props.incident.timestamp}}},{key:"render",value:function(){var e="impact-".concat(this.props.incident.impact," incident-title font-large"),t=Routes.incident_url(this.props.incident.code);return v.a.createElement("div",{className:"incident-data incident-container"},v.a.createElement("a",{href:t,className:e},this.props.incident.name),v.a.createElement("div",{className:"message incident-body color-primary",dangerouslySetInnerHTML:this._renderIncidentMessage()}),v.a.createElement("div",{className:"secondary font-small color-secondary",dangerouslySetInnerHTML:this._renderIncidentTimestamp()}))}}]),n}(v.a.Component);T.propTypes={incident:g.a.object.isRequired};var S=T;function x(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,a=h()(e);if(t){var r=h()(this).constructor;n=Reflect.construct(a,arguments,r)}else n=a.apply(this,arguments);return m()(this,n)}}var j=function(e){u()(n,e);var t=x(n);function n(e){var a;return i()(this,n),(a=t.call(this,e)).state={expanded:!1},a}return c()(n,[{key:"_toggleIncidents",value:function(){this.setState({expanded:!this.state.expanded})}},{key:"_buttonText",value:function(){return this.state.expanded?v.a.createElement("span",null,"- Collapse Incidents"):v.a.createElement("span",null,v.a.createElement("var",{"data-var":"show-all"},"+ Show All")," ",this.props.incidents.length," ",v.a.createElement("var",{"data-var":"incidents"},"Incidents"))}},{key:"_displayToggler",value:function(){return v.a.createElement("div",{className:"expand-incidents font-small border-color color-secondary",onClick:this._toggleIncidents.bind(this)},this._buttonText())}},{key:"_renderIncidents",value:function(){return this.state.expanded?this.props.incidents.map((function(e){return v.a.createElement(S,{key:e.code,incident:e})})):this.props.incidents.slice(0,3).map((function(e){return v.a.createElement(S,{key:e.code,incident:e})}))}},{key:"render",value:function(){return v.a.createElement("div",{className:"incident-history"},v.a.createElement("div",{className:"incident-list"},0===this.props.incidents.length?v.a.createElement("span",{className:"small"},"No incidents reported for this month."):this._renderIncidents()),this.props.incidents.length>3?this._displayToggler():null)}}]),n}(v.a.Component);j.propTypes={incidents:g.a.arrayOf(g.a.object).isRequired};var I=j,M=n(3),q=n.n(M);function P(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,a=h()(e);if(t){var r=h()(this).constructor;n=Reflect.construct(a,arguments,r)}else n=a.apply(this,arguments);return m()(this,n)}}var F=function(e){u()(n,e);var t=P(n);function n(e){var a;return i()(this,n),(a=t.call(this,e)).state={pageNumber:parseInt(_.get().page||1,10)},a._pageForward=a._pageForward.bind(q()(a)),a._pageBack=a._pageBack.bind(q()(a)),a._responseHandler=e.responseHandler,a}return c()(n,[{key:"_canPageBack",value:function(){return this.state.pageNumber<40}},{key:"_canPageForward",value:function(){return this.state.pageNumber>1}},{key:"_pageBack",value:function(e){var t=this;e.preventDefault(),this._canPageBack()&&(this.state.pageNumber+=1,_.fetch({page:this.state.pageNumber},{merge:!0}).then((function(e){t._responseHandler(e)})))}},{key:"_pageForward",value:function(e){var t=this;e.preventDefault(),this._canPageForward()&&(this.state.pageNumber-=1,_.fetch({page:this.state.pageNumber},{merge:!0}).then((function(e){t._responseHandler(e)})))}},{key:"render",value:function(){var e=b()("previous-page border-color color-secondary",{disabled:!this._canPageBack()}),t=b()("next-page border-color color-secondary",{disabled:!this._canPageForward()});return v.a.createElement("div",{className:"pagination-container"},v.a.createElement("div",{className:"pagination"},v.a.createElement("a",{href:"#back",className:e,onClick:this._pageBack},v.a.createElement("i",{className:"left-arrow"})),v.a.createElement("span",{className:"current"},v.a.createElement("span",null,this.props.startDateMonth),v.a.createElement("var",{"data-var":"year"}," ",this.props.startDateYear)," to"," ",v.a.createElement("span",null,this.props.endDateMonth),v.a.createElement("var",{"data-var":"year"}," ",this.props.endDateYear)),v.a.createElement("a",{href:"#forward",className:t,onClick:this._pageForward},v.a.createElement("i",{className:"right-arrow"}))))}}]),n}(v.a.Component);F.propTypes={startDateMonth:g.a.string.isRequired,startDateYear:g.a.number.isRequired,endDateMonth:g.a.string.isRequired,endDateYear:g.a.number.isRequired,responseHandler:g.a.func.isRequired};var Y=F;function O(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,a=h()(e);if(t){var r=h()(this).constructor;n=Reflect.construct(a,arguments,r)}else n=a.apply(this,arguments);return m()(this,n)}}var B=function(e){u()(n,e);var t=O(n);function n(e){var a;return i()(this,n),(a=t.call(this,e))._fetchFiltered=N.a.throttle((function(){_.fetch({filter:a.state.filterComponentIds.join(",")},{merge:!0}).then((function(e){a.setState({components:e.components,componentFilter:e.component_filter,months:e.months})}))}),250,{leading:!1,trailing:!0}),a.state={components:e.components,componentFilter:e.component_filter,filterComponentIds:[],months:e.months,showComponentFilter:e.show_component_filter,showItemsSelector:!1},a}return c()(n,[{key:"_toggleComponentSelector",value:function(){this.setState({showItemsSelector:!this.state.showItemsSelector})}},{key:"_changeComponent",value:function(e){var t=[];e.forEach((function(e){e.selected&&t.push(e.id),e.children.length>0&&e.children.forEach((function(e){e.selected&&t.push(e.id)}))})),this.setState({filterComponentIds:t}),this._fetchFiltered()}},{key:"_renderComponentSelector",value:function(){var e=this.state.componentFilter||[];return v.a.createElement(w.a,{items:this.props.components,className:"component-items-selector",showFooter:!1,onClose:this._toggleComponentSelector.bind(this),onToggle:this._changeComponent.bind(this),preselectedItemIds:e})}},{key:"_renderComponentSelectorButton",value:function(){if(this.state.showComponentFilter)return v.a.createElement("div",{className:"component-selector"},v.a.createElement("span",{className:b()("show-filter","color-secondary","border-color",{open:this.state.showItemsSelector}),onClick:this._toggleComponentSelector.bind(this)},"Filter Components"),this.state.showItemsSelector?this._renderComponentSelector():"")}},{key:"_handlePagination",value:function(e){this.setState({components:e.components,componentFilter:e.component_filter,months:e.months})}},{key:"_renderMonths",value:function(){return this.state.months.map((function(e){return v.a.createElement("div",{className:"month",key:"".concat(e.name).concat(e.year)},v.a.createElement("h4",{className:"month-title font-largest border-color"},e.name," ",v.a.createElement("var",{"data-var":"year"},e.year)),v.a.createElement("div",{className:"month-content"},v.a.createElement(I,{incidents:e.incidents})))}))}},{key:"render",value:function(){var e=this.state.months[2].name,t=this.state.months[2].year,n=this.state.months[0].name,a=this.state.months[0].year;return v.a.createElement("div",{className:"history-backpage"},v.a.createElement("div",{className:"history-header"},this._renderComponentSelectorButton(),v.a.createElement(Y,{startDateMonth:e,startDateYear:t,endDateMonth:n,endDateYear:a,responseHandler:this._handlePagination.bind(this)})),v.a.createElement("div",{className:"months-container"},this._renderMonths()))}}]),n}(v.a.Component);B.propTypes={components:g.a.arrayOf(g.a.object).isRequired,component_filter:g.a.array,months:g.a.arrayOf(g.a.object).isRequired,show_component_filter:g.a.bool.isRequired};var A=B,G=(n(670),n(2094),n(151));function H(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,a=h()(e);if(t){var r=h()(this).constructor;n=Reflect.construct(a,arguments,r)}else n=a.apply(this,arguments);return m()(this,n)}}var L=function(e){u()(n,e);var t=H(n);function n(){var e;i()(this,n);for(var a=arguments.length,r=new Array(a),o=0;o<a;o++)r[o]=arguments[o];return(e=t.call.apply(t,[this].concat(r)))._displayTooltip=function(t){var n=document.querySelector(".history-container").getBoundingClientRect();t.cancelable&&t.preventDefault();var a=t.target,r=t.target.getBoundingClientRect(),o=r.left-n.left+r.width/2-162.5,i=r.bottom-n.top+9;e.props.hoverDay(o,i,e.props.day,a)},e}return c()(n,[{key:"render",value:function(){var e=this,t=this.props.hidden||"#EAEAEA"===this.props.color?"":" active";return v.a.createElement("svg",{className:"day".concat(t),width:"32",height:"32",xmlns:"http://www.w3.org/2000/svg",ref:function(t){return e.svg=t},onMouseEnter:this._displayTooltip,onTouchEnd:this._displayTooltip,onMouseLeave:this.props.leaveDay},v.a.createElement("rect",{className:this.props.hidden?"hide":null,width:"32",height:"32",fill:this.props.color}))}}]),n}(f.Component);L.defaultProps={color:"#EAEAEA"},L.propTypes={day:g.a.object,color:g.a.string,hidden:g.a.bool,hoverDay:g.a.func,leaveDay:g.a.func};var U=L,W=n(152),J=n.n(W),Z=function(e){if(0===e.count)return null;var t="".concat(e.outage_type,"-outage-group-count"),n="".concat(e.outage_type,"_outage");return v.a.createElement("div",{id:t,className:"outage-count"},v.a.createElement("i",{className:"component-status page-colors text-color ".concat(n)}),v.a.createElement("span",{className:"count"},e.count," ",J.a.inflect("component",e.count))," had a ",e.outage_type," outage.")};Z.propTypes={count:g.a.number,outage_type:g.a.string};var Q=Z,V=function(e){if(0===e.hours&&0===e.minutes)return null;var t="".concat(e.outage_type,"_outage"),n="".concat(e.outage_type[0].toUpperCase()+e.outage_type.slice(1)," outage");return v.a.createElement("div",{className:"outage-field ".concat(e.outage_type)},v.a.createElement("span",{className:"label"},v.a.createElement("i",{className:"component-status page-colors text-color ".concat(t)}),n)," ",v.a.createElement("span",{className:"value-hrs"},e.hours," hrs")," ",v.a.createElement("span",{className:"value-mins"},e.minutes," mins"))};V.propTypes={outage_type:g.a.string,hours:g.a.number,minutes:g.a.number};var z=V,K=function(e){if(e.isGroup||!e.events||0===e.totalOutage&&0===e.events.length)return null;if(e.totalOutage>0&&0===e.events.length)return v.a.createElement("div",{className:"no-related-msg"},v.a.createElement("p",null,"No incidents or maintenance related to this downtime."));return v.a.createElement("div",{className:"related-events"},v.a.createElement("h3",{id:"related-event-header"},"Related"),v.a.createElement("ul",{id:"related-events-list"},e.events.map((function(e){return v.a.createElement("li",{className:"related-event",key:e.code},v.a.createElement("a",{className:"related-event-link",href:window.Routes.incident_path(e.code),target:"_blank"},(t=e.name).length<=90?t:t.substring(0,90)+"..."));var t}))))},X=n(49),$=n.n(X),ee=n(63),te=Object(ee.j)(),ne=function(e){if(!e.visible||!e.day||!e.day.date)return null;var t=v.a.createElement("div",{className:"outages"},v.a.createElement("div",{className:"no-outages-msg"},"No downtime recorded on this day.")),n=v.a.createElement("div",{className:"outages"},v.a.createElement("div",{className:"no-data-msg"},"No data exists for this day.")),a=function(t){var n=t&&$.a.parseZone(t).format().split("T")[0];return t&&$()(e.day.date,"YYYY-MM-DD").isBefore(n,"day")},r=t;if(e.isGroup&&(e.day.mc||e.day.pc))r=v.a.createElement("div",{className:"outages"},v.a.createElement(Q,{count:e.day.mc,outage_type:"major"}),v.a.createElement(Q,{count:e.day.pc,outage_type:"partial"}));else if(e.isGroup)r=function(){for(var r=0,o=0,i=0;i<e.groupComponentData.length;i++){var s=e.groupComponentData[i];s.is_showcased&&(o++,a(s.start_date)?r++:0)}return r===o?n:t}();else if(e.day.m||e.day.p){r=function(e,t){return v.a.createElement("div",{className:"outages"},v.a.createElement(z,{outage_type:"major",hours:e.hours,minutes:e.minutes}),v.a.createElement(z,{outage_type:"partial",hours:t.hours,minutes:t.minutes}))}(Object(ee.d)(e.day.m),Object(ee.d)(e.day.p))}else r=a(e.componentStartDate)?n:t;var o=$.a.parseZone(e.day.date).format("D MMM YYYY"),i=e.x,s=document.querySelector(".history-container").getBoundingClientRect(),c=(window.innerWidth-s.width)/2;i>window.innerWidth-(325+c)?i=window.innerWidth-(325+c):i<-c&&(i=-c);var l=155.5;return i!==e.x&&(l-=i-e.x),v.a.createElement("div",{id:"uptime-tooltip",onMouseEnter:e.tooltipEntered,onMouseLeave:e.tooltipLeft,style:{display:"block",left:i,top:e.y}},v.a.createElement("div",{id:"box-arrow",style:{left:l}}),v.a.createElement("div",{className:"tooltip-box"},v.a.createElement("div",{className:"tooltip-content"},v.a.createElement("div",{className:"tooltip-close".concat(te?"":" hidden")},v.a.createElement("i",{className:"fa fa-times",onClick:e.closeTooltip})),v.a.createElement("p",{className:"date"},o),r,v.a.createElement(K,{events:e.day.events,isGroup:e.isGroup,totalOutage:e.day.m+e.day.p}))))};ne.propTypes={closeTooltip:g.a.func,day:g.a.object,isGroup:g.a.bool,tooltipEntered:g.a.func,tooltipLeft:g.a.func,visible:g.a.bool,x:g.a.number,y:g.a.number,componentStartDate:g.a.string,groupComponentData:g.a.arrayOf(g.a.object)};var ae,re,oe=ne;function ie(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,a=h()(e);if(t){var r=h()(this).constructor;n=Reflect.construct(a,arguments,r)}else n=a.apply(this,arguments);return m()(this,n)}}var se=function(e){var t=function(e){return e?"".concat((100*e).toString().match(/^-?\d+(?:\.\d{0,2})?/)[0],"%"):""}(e.month.uptime_percentage);return v.a.createElement("div",{className:"calendar-month"},v.a.createElement("div",{className:"month-header"},v.a.createElement("h6",{className:"month-name"},e.month.name," ",v.a.createElement("var",{"data-var":"year"},e.month.year)),v.a.createElement("small",{className:"month-uptime"},t)),v.a.createElement(ce,{hoverDay:e.hoverDay,leaveDay:e.leaveDay,month:e.month}))},ce=function(e){var t=Array(e.month.start_offset).fill().map((function(e,t){return v.a.createElement(U,{hoverDay:function(){},hidden:!0,key:"".concat(t,"-padding")})})),n=e.month.days.map((function(t,n){return v.a.createElement(U,{day:t,color:t.color,hoverDay:e.hoverDay,leaveDay:e.leaveDay,key:n,tooltip:t.tooltip})}));return v.a.createElement("div",{className:"days"},t.concat(n))},le=function(e){u()(n,e);var t=ie(n);function n(e){var a;return i()(this,n),(a=t.call(this,e))._hoverDay=function(e,t,n,r){clearTimeout(re),clearTimeout(ae),ae=setTimeout((function(){a.setState({activeDay:n,displayTooltip:!0,touchTarget:r,x:e,y:t})}),150)},a._leaveDay=function(){clearTimeout(ae),re=setTimeout((function(){a.setState({displayTooltip:!1})}),250)},a._closeTooltip=function(){a.setState({displayTooltip:!1})},a.state={component_select_list:e.component_select_list,component:e.component,displayTooltip:!1,isGroup:e.is_group,months:e.months,group_component_data:e.group_component_data},a._changeComponent=a._changeComponent.bind(q()(a)),a._handlePagination=a._handlePagination.bind(q()(a)),a}return c()(n,[{key:"_drawMonths",value:function(){var e=this;return this.state.months.map((function(t){return v.a.createElement(se,{hoverDay:e._hoverDay,key:t.name,leaveDay:e._leaveDay,month:t})}))}},{key:"_changeComponent",value:function(e){var t=this,n=e.target.value,a="/uptime/".concat(n);_.fetch({},{path:a,merge:!0}).then((function(e){t.setState({activeDay:null,component_select_list:e.component_select_list,component:e.component,isGroup:e.is_group,months:e.months,visible:!1,group_component_data:e.group_component_data})}))}},{key:"_handlePagination",value:function(e){this.setState({component_select_list:e.component_select_list,component:e.component,months:e.months})}},{key:"_tooltipEntered",value:function(){clearTimeout(re)}},{key:"render",value:function(){var e=this.state.months[0].name,t=this.state.months[0].year,n=this.state.months[2].name,a=this.state.months[2].year;return v.a.createElement("div",{className:"uptime-calendar"},v.a.createElement("div",{className:"uptime-header"},v.a.createElement("div",{className:"component-selector"},v.a.createElement(G.a,{name:"component",defaultValue:this.state.component.id,options:this.state.component_select_list,onChange:this._changeComponent})),v.a.createElement(Y,{startDateMonth:e,startDateYear:t,endDateMonth:n,endDateYear:a,responseHandler:this._handlePagination})),v.a.createElement("div",{className:"uptime-calendar-display"},this._drawMonths(),v.a.createElement(oe,{closeTooltip:this._closeTooltip,day:this.state.activeDay,isGroup:this.state.isGroup,tooltipEntered:this._tooltipEntered,tooltipLeft:this._leaveDay,visible:this.state.displayTooltip,x:this.state.x,y:this.state.y,componentStartDate:this.state.component.start_date,groupComponentData:this.state.group_component_data})))}}]),n}(f.Component);se.propTypes={hoverDay:g.a.func.isRequired,leaveDay:g.a.func.isRequired,month:g.a.object.isRequired},ce.propTypes={hoverDay:g.a.func.isRequired,leaveDay:g.a.func.isRequired,month:g.a.object.isRequired},le.propTypes={component_select_list:g.a.arrayOf(g.a.object).isRequired,component:g.a.object.isRequired,is_group:g.a.bool.isRequired,months:g.a.arrayOf(g.a.object).isRequired,group_component_data:g.a.arrayOf(g.a.object)};var ue=le;window.pageColors=new r.a(window.pageColorData),Object(a.a)({HistoryIndex:A,UptimeCalendar:ue})},50:function(e,t){e.exports=jQuery}},[[2093,1,0]]]);
//# sourceMappingURL=status-52d317a9a05694423231.chunk.js.map