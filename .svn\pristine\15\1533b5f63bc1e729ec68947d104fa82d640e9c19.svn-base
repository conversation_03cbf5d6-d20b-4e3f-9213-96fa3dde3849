﻿using System;
using System.Threading;

namespace CommonLib
{
    public class ServerTime
    {
        //http://global.apis.naver.com/currentTime
        //https://moapi.wps.cn/time
        public static long OffSet;
        private static long _lastOffSet;

        public static DateTime DateTime => DateTime.Now.AddTicks(OffSet);

        static ServerTime()
        {
            new Thread(p =>
            {
                ProcessTimeOffSet();
            })
            { Priority = ThreadPriority.Highest, IsBackground = true }.Start();
        }

        private static bool _hasGetNtpDate;

        private static void ProcessTimeOffSet()
        {
            while (!ConfigHelper.IsExit)
            {
                var off = SNtpClient.Instance.GetNetworkTimeOffset();
                if (off != -9999)
                {
                    SetOffSet(off);
                    if (!_hasGetNtpDate)
                    {
                        _hasGetNtpDate = true;
                    }
                    Thread.Sleep(10 * 1000);
                }
                else
                {
                    Thread.Sleep(1 * 1000);
                }
            }
        }

        public static void SetHttpDate(DateTime dtHttp)
        {
            if (_hasGetNtpDate || dtHttp.Year < 2021)
                return;
            try
            {
                var off = dtHttp.Ticks - DateTime.Now.Ticks;
                SetOffSet(off);
            }
            catch { }
        }

        private static void SetOffSet(long off)
        {
            if (_lastOffSet != 0)
            {
                OffSet = (_lastOffSet + OffSet + off) / 3;
            }
            else
            {
                OffSet = off;
            }
            _lastOffSet = off;
        }
    }
}
