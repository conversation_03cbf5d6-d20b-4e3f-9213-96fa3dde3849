﻿using Enterprise.Framework.Redis;
using Newtonsoft.Json;
using Newtonsoft.Json.Converters;
using System;
using System.Collections.Generic;
using System.Linq;

namespace CommonLib
{
    public class UserCodeRecordCache : RedisCacheObject<UserCodeInfo>
    {
        protected override string CurrentObject_KeyPrefix
        {
            get { return "CodeCache:"; }
        }

        protected override string Object_DBName
        {
            get { return "OPS_Cache"; }
        }

        private readonly IsoDateTimeConverter _timeFormat = new IsoDateTimeConverter() { DateTimeFormat = "yyyy-MM-dd HH:mm:ss fff" };

        public bool Validate(string account, int timeSpan, int limitCount, int blockSecond)
        {
            var result = true;
            var accessTime = ServerTime.DateTime;
            var strKey = account;
            using (AcquireLock(strKey))
            {
                var user = Get(strKey) ?? new UserCodeInfo()
                {
                    Key = account,
                    Records = new List<CodeRecordLog>()
                };
                if (user.BlackTime.HasValue)
                {
                    result = user.BlackTime < accessTime;
                    if (result)
                    {
                        user.BlackTime = null;
                    }
                    else
                    {
                        LogHelper.Log.Info(string.Format("{2} {0}校验失败，还在黑名单中:{1}！"
                            , account
                            , user.BlackTime?.ToString("yyyy-MM-dd HH:mm:ss fff")
                            , accessTime.ToString("yyyy-MM-dd HH:mm:ss fff")));
                        return result;
                    }
                }
                if (user.Records.Count > 0)
                {
                    user.Records.RemoveAll(p => p.AddTime < accessTime.AddMilliseconds(-timeSpan));
                    if (user.Records.Exists(p => p.AddTime > accessTime))
                    {
                        LogHelper.Log.Info(string.Format("{0}存在时间抖动！当前时间:{1},最大时间：{2}"
                            , account
                            , accessTime.ToString("yyyy-MM-dd HH:mm:ss fff")
                            , user.Records.Max(p => p.AddTime).ToString("yyyy-MM-dd HH:mm:ss fff")));
                        user.Records.RemoveAll(p => p.AddTime > accessTime);
                    }
                    if (result)
                    {
                        result = user.Records.Count(p => p.Success) < limitCount;
                    }
                }
                user.Records.Add(new CodeRecordLog() { Success = result, AddTime = accessTime });
                if (!result && !user.BlackTime.HasValue)
                {
                    var lastSuccessTime = user.Records.Where(p => p.Success).Min(p => p.AddTime);
                    if (lastSuccessTime.Year < ServerTime.DateTime.Year)
                    {
                        lastSuccessTime = accessTime;
                    }
                    user.BlackTime = lastSuccessTime.AddSeconds(blockSecond);
                    //user.BlackTime = accessTime.AddSeconds(blockSecond);
                }
                Insert(strKey, user, accessTime.AddHours(1));
                if (!result)
                {
                    LogHelper.Log.Info(string.Format("{5} {0}校验失败，频次过高,TimeSpan:{1},LimitCount:{2},BlackSec:{6}{4}Detail:{3}！"
                        , account
                        , timeSpan
                        , limitCount
                        , JsonConvert.SerializeObject(user, Formatting.Indented, _timeFormat)
                        , Environment.NewLine
                        , accessTime.ToString("yyyy-MM-dd HH:mm:ss fff")
                        , blockSecond));
                }
            }
            return result;
        }
    }

    public class UserCodeInfo
    {
        public string Key { get; set; }

        public DateTime? BlackTime { get; set; }

        public List<CodeRecordLog> Records { get; set; }
    }

    public class CodeRecordLog
    {
        public bool Success { get; set; }

        public DateTime AddTime { get; set; }
    }
}