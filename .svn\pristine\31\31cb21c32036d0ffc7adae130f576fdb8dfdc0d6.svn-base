import {
    micromark
} from "./micromark.bundle.js";
marked.setOptions({
    gfm: true,
    tables: true,
    breaks: true,
    pedantic: false,
    sanitize: false,
    smartLists: true,
    smartypants: false,
    highlight: function (e) {
        return hljs.highlightAuto(e).value
    }
});

function send() {
    var e = $("input").val();
    if (!$.trim(e)) {
        return
    }
    if (window.chat_status != "pass") {
        return
    }
    $(".send-view").hide();
    $(".send-loading").css("display", "flex");
    window.chat_status = "wating";
    $("#tip").hide();
    $(".voice-end").click();
    $("input").val("");
    $(".fa-send").hide();
    $(".fa-send-o").show();
    let t = getUuid();
    var i = $(".wrapper");
    var a = i.children().length;
    var s = "<div class='mine'>";
    s += "<img class='avatar' src='img/my_default.png'>";
    s += "<div class='msg'>" + e + "</div>";
    s += "</div>";
    s += "<div class='ai'>";
    s += "<img class='avatar' src='img/sys_default.png'>";
    s += "<div class='msg' id='" + t +
        "'><div class='loading'><span></span><span></span><span></span><span></span><span></span></div>";
    s += "</div></div>";
    i.append(s);
    $(document).scrollTop(document.body.scrollHeight);
    CHAT_API.chatDialog(e, t);
}
window.dialogReload = e => {
    $(".send-view").hide();
    $(".send-loading").css("display", "flex");
    window.chat_status = "wating";
    let t = $("#" + e).html();
    let i = "<div class='loading'><span></span><span></span><span></span><span></span><span></span></div>";
    $("#" + e).html(i);
    let n = getUuid();
    $("#" + e).attr("id", n);
    CHAT_API.chatRetry(e, n, e);
};
window.dialogOutput = (i, a) => {
    let s = "未响应，请重新输入";
    if (!i) {
        s = "Request Failed"
    } else if (i && (i.result == 1 || i.result == 2)) {
        s = i.text
    }
    let n = $("#" + a);
    if (i && i.type == 1 && i.result == 1) {
        n.html("<div id='dialog-img-" + a + "'><img class='dialog-img' onerror='imgError(" + a + ")' src='" + s +
            "'></div>")
    } else {
        var o = 0;
        let e = false;
        let t = 0;
        var l = setInterval(function () {
            e = false;
            if ($(document).scrollTop() >= $(document).height() - $(window).height()) {
                e = true
            }
            t = $(document).height();
            n.html(micromark(s.slice(0, o) + "|"));
            if (e && t != $(document).height()) {
                $(document).scrollTop(document.body.scrollHeight)
            }
            o++;
            if (o > s.length) {
                s = marked.parse(toMarkdown(s));
                if (i && i.result != 1 && i.dialogId) {
                    s += "<i class='fa fa-refresh' onclick='dialogReload(\"" + a + "\")'></i>"
                }
                if (i && i.result == 1) {
                    s += "<div data-dialogId='" + a + "' class='copy-view btn-copy' data-clipboard-text='" +
                        i.text.replaceAll("````", "").replaceAll("```", "") +
                        "'><i class='fa fa-copy'></i><span>Copy</span></div>";
                    s += "<div id='copied_" + a +
                        "' class='copy-view copied-view'><i class='fa fa-check'></i><span>Copied</span></div>"
                }
                n.html(s);
                console.log(s);
                clearInterval(l)
            }
        }, 100)
    }
};
window.dialogHistory = a => {
    setTimeout(() => {
        $(".skeleton-view").hide();
        if (!a || !a.list) {
            initTip();
            return
        }
        a.list.reverse();
        var e = $(".wrapper");
        var i = "";
        a.list.forEach((e, t) => {
            i += "<div class='mine'>";
            i += "<img class='avatar' src='img/my_default.png'>";
            i += "<div class='msg'>" + e.msgText + "</div>";
            i += "</div>";
            i += "<div class='ai'>";
            i += "<img class='avatar' src='img/sys_default.png'>";
            i += "<div class='msg' id='" + e.dialogId + "'>";
            if (e.text && e.result == 1) {
                if (e.type == 1) {
                    i += "<div id='dialog-img-" + e.dialogId +
                        "'><img class='dialog-img' onerror='imgError(" + e.dialogId + ")' src='" +
                        e.text + "'></div>"
                } else {
                    e.text = toMarkdown(e.text);
                    i += marked.parse(e.text);
                    i += "<div data-dialogId='" + e.dialogId +
                        "' class='copy-view btn-copy' data-clipboard-text='" + e.text.replaceAll(
                            "````", "").replaceAll("```", "") +
                        "'><i class='fa fa-copy'></i><span>Copy</span></div>";
                    i += "<div id='copied_" + e.dialogId +
                        "' class='copy-view copied-view'><i class='fa fa-check'></i><span>Copied</span></div>"
                }
            } else {
                if (e.result == 2) {
                    i += e.text
                } else {
                    i += "未响应，请重新输入"
                }
                if (a.list.length == t + 1) {
                    i += "<i class='fa fa-refresh' onclick='dialogReload(\"" + e.dialogId +
                        "\")'></i>"
                }
            }
            i += "</div>";
            i += "</div>"
        });
        try {
            e.html(i)
        } catch (e) {
            console.log(e)
        }
        setTimeout(() => {
            $(document).scrollTop(document.body.scrollHeight)
        }, 0)
    }, 500)
};

function toMarkdown(e) {
    if (new RegExp(/```/).test(e.substr(0, 15))) return e;
    if (new RegExp(/<\w+>/).test(e) || new RegExp(/function\s+\w+\s*\(/).test(e) || new RegExp(/function\s*\(/).test(e) ||
        new RegExp(/\s+main\s*\(/).test(e) || new RegExp(/\s+(class|inteface)\s*{/).test(e) || new RegExp(
            /import\s+\w+/).test(e) || new RegExp(/\.\w+\s*{/).test(e) || new RegExp(/\s+new\s+\w+/).test(e) || new RegExp(
                /\s+return\s*\w+/).test(e) || new RegExp(/console\.\w+/).test(e)) {
        e = "````" + "\n" + e + "\n" + "````"
    }
    return e
}

function initTip() {
    var e = $("#tip");
    e.show();
    var t = "你好，我是OCR助手的好朋友ChatGPT，是一款聊天式人工智能（AI）工具，基于GPT-3的自然语言处理技术，生成自然语言与您对话。我擅长写作/翻译/思考/编程，生活中有什么问题也可以问我！";
    var i = 0;
    var a = setInterval(function () {
        e.html(t.slice(0, i) + "|");
        i++;
        if (i > t.length) {
            e.html(t.slice(0, i));
            clearInterval(a)
        }
    }, 100)
    $(".send-view").css("display", "flex");
    $(".send-loading").hide()
}

function entitiesEncode(e) {
    e = e.replace(/&/g, "&amp;");
    e = e.replace(/</g, "&lt;");
    e = e.replace(/>/g, "&gt;");
    e = e.replace(/ /g, "&nbsp;");
    e = e.replace(/"/g, "&quot;");
    return e
}

function entitiesDecode(e) {
    e = e.replace(/&amp;/g, "&");
    e = e.replace(/&lt;/g, "<");
    e = e.replace(/&gt;/g, ">");
    e = e.replace(/&nbsp;/g, " ");
    e = e.replace(/&quot;/g, "'");
    e = e.replace(/&#39;/g, "'");
    return e
}

function getPlatform() {
    var e = navigator.userAgent,
        t = /(?:Windows Phone)/.test(e),
        i = /(?:SymbianOS)/.test(e) || t,
        a = /(?:Android)/.test(e),
        s = /(?:Firefox)/.test(e),
        n = /(?:iPad|PlayBook)/.test(e) || a && !/(?:Mobile)/.test(e) || s && /(?:Tablet)/.test(e),
        o = !!e.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/),
        l = !o && !a && !i,
        d = /(?:Safari)/.test(e),
        r = /(?:micromessenger)/.test(e.toLowerCase());
    return {
        isTablet: n,
        isIos: o,
        isAndroid: a,
        isPc: l,
        isSafari: d,
        isWeixin: r
    }
}

function random(e, t) {
    return Math.floor(Math.random() * (t - e)) + e
}
window.getUuid = () => {
    return Number((new Date).getTime() + "" + random(100, 1e3))
};

function imgError(e) {
    $("#dialog-img-" + e).html("<p class='img-error-text'>图片已过期或已被清理</p>")
}
$.preloadImages = function () {
    for (var e = 0; e < arguments.length; e++) {
        var t = $("<img />").attr("src", arguments[e])
    }
};
$.preloadImages("img/my_default.png", "img/sys_default.png");

$(function () {
    if (!getPlatform().isPc) {
        $(".voice-start").css("display", "flex")
    }
    $("input").keydown(function () {
        if (event.keyCode == 13) {
            send()
        }
    });
    $("input").bind("input", function () {
        if ($.trim($(this).val())) {
            $(".fa-send-o").hide();
            $(".fa-send").show()
        } else {
            $(".fa-send").hide();
            $(".fa-send-o").show()
        }
    });
    $(".btn-send").click(function () {
        send()
    });
    if (document.URL.indexOf("?debug") > 0) {
        new VConsole
    }
    var e = new ClipboardJS(".btn-copy");
    e.on("success", function (e) {
        console.info("Text:", e.text);
        e.clearSelection();
        let t = e.trigger.dataset.dialogid;
        e.trigger.style.display = "none";
        $("#copied_" + t).show();
        setTimeout(() => {
            e.trigger.style.display = "block";
            $("#copied_" + t).hide()
        }, 1500)
    });
    e.on("error", function (e) {
        console.error("Action:", e.action);
        console.error("Trigger:", e.trigger)
    });
});