﻿using System;
using System.Collections.Generic;
using System.Text;
using System.IO;
using System.Windows.Forms;
using System.Net;
using System.Reflection;
using System.Threading;
using System.Diagnostics;
using System.Security.Cryptography.X509Certificates;
using System.Net.Security;
using NewTicket.Common;
using NewTicket.HttpItem.CurlItem;
using System.Collections.Concurrent;

namespace NewTicket
{
    public class CommonString
    {
        public static void InitString()
        {
            InitSetttings();
            CommonMethod.InitConfig();
            //ThreadPool.QueueUserWorkItem(new WaitCallback(obj =>
            //{
            //    //CommonMethod.InitNetPool(30);
            //}));
            InitDll();
            //CloudyQueryHelper.GetMobileCookie();
            //NewTicketHelper.InitPool();
            InitLocalCode();
            InitNetWorkInfo();
            InitTaskRegister();
        }

        public static string SiteCode { get { return "otn"; } }

        private static void InitTaskRegister()
        {
            if (CommonString.IsAdministrator && !TaskRegister.IsExits(".task"))
            {
                TaskRegister.AddReg(Assembly.GetExecutingAssembly().Location, ".task");
            }
        }

        public static void InitNetWorkInfo()
        {
            System.Net.NetworkInformation.NetworkChange.NetworkAvailabilityChanged += NetworkChange_NetworkAvailabilityChanged;
            System.Net.NetworkInformation.NetworkChange.NetworkAddressChanged += NetworkChange_NetworkAddressChanged;
        }

        public static bool IsCurlCanUse { get; set; }

        public static void InitCurlLib()
        {
            System.Threading.Tasks.Task.Factory.StartNew(() =>
            {
                try
                {
                    Curl.GlobalInit((int)CURLinitFlag.CURL_GLOBAL_DEFAULT);
                    IsCurlCanUse = true;
                }
                catch (Exception oe)
                {
                    Log.WriteError("初始化Curl出错！", oe);
                }
            });
        }

        public static void CleanUpCurlLib()
        {
            try
            {
                if (IsCurlCanUse)
                    Curl.GlobalCleanup();
            }
            catch (Exception oe)
            {
                Log.WriteError("关闭Curl出错！", oe);
            }
        }


        static void NetworkChange_NetworkAddressChanged(object sender, EventArgs e)
        {
            IsOnLine = System.Net.NetworkInformation.NetworkInterface.GetIsNetworkAvailable();
        }

        static void NetworkChange_NetworkAvailabilityChanged(object sender, System.Net.NetworkInformation.NetworkAvailabilityEventArgs e)
        {
            IsOnLine = e.IsAvailable;
            if (!e.IsAvailable)
            {
                CommonMSG.AddMSG("【网络中断】挂起所有操作……");
            }
            else
            {
                CommonMSG.AddMSG("【网络恢复】操作继续……");
                CommonMethod.ReportHost();
            }
        }

        private static void InitLocalCode()
        {
            //new System.Threading.Thread(ValidateCode.InitAntiVC) { Priority = ThreadPriority.Highest, IsBackground = true }.Start();
        }

        private static void InitDll()
        {
            //var strTmp = strVCodeDllPath;
            //strTmp = strAntiVCDllPath;
            //strTmp = strAntiVCPath;
            var strTmp = strDaMaTDllPath;
            strTmp = strYunDaMaDllPath;
            strTmp = strWiseDllPath;
            strTmp = null;
            RemoveTmpCache();
        }

        private static void InitCurl()
        {
            var strTmp = strScrLib;
            strTmp = strScrShim;
            InitCurlLib();
        }

        public static bool LogShowFast { get; set; }

        public static bool IsBeta { get; set; }

        ////连接超时  
        //public const int INTERNET_OPTION_CONNECT_TIMEOUT = 2;
        //public const int INTERNET_OPTION_CONNECT_RETRIES = 3;
        ////送信超时时间  
        //public const int INTERNET_OPTION_SEND_TIMEOUT = 5;
        ////受信超时时间  
        //public const int INTERNET_OPTION_RECEIVE_TIMEOUT = 6;
        //public const int INTERNET_OPEN_TYPE_PRECONFIG = 0;//使用 IE 中的连接设置
        //public const int INTERNET_OPEN_TYPE_DIRECT = 1;//直接连接到服务器
        //public const int INTERNET_OPEN_TYPE_PROXY = 3;//通过代理服务器进行连接
        //public const int INTERNET_OPTION_MAX_CONNS_PER_SERVER = 73;//最大连接数
        //public const int INTERNET_OPTION_MAX_CONNS_PER_1_0_SERVER = 74;//最大连接数

        public const string StrTicketHost = "12306.cn";

        public static void InitSetttingsBeforeStart()
        {
            CommonString.IsAdministrator = CommonMethod.IsAdministrator();
            //CommonString.InitCurl();
            try
            {
                //CommonMethod.CheckFrameWork();
                ServicePointManager.ServerCertificateValidationCallback = ((object sender, X509Certificate certificate, X509Chain chain, SslPolicyErrors sslPolicyErrors) => true);
                //ServicePointManager.Expect100Continue = false;
                ServicePointManager.DefaultConnectionLimit = int.MaxValue;
                //if (ServicePointManager.DefaultConnectionLimit < 200)
                //{
                //    ServicePointManager.DefaultConnectionLimit = 200;
                //}
                //ServicePointManager.MaxServicePoints = int.MaxValue;
                ServicePointManager.MaxServicePointIdleTime = int.MaxValue;
                ServicePointManager.UseNagleAlgorithm = false;
                //ServicePointManager.CheckCertificateRevocationList = false;
                ServicePointManager.Expect100Continue = false;
                //ServicePointManager.SetTcpKeepAlive(true, 1000, 200);
                GlobalProxySelection.Select = GlobalProxySelection.GetEmptyWebProxy();
                WebRequest.DefaultWebProxy = null;
                WebRequest.DefaultCachePolicy = CommonString.NoCachePolicy;
            }
            catch
            {
                // ignored
            }
            //try
            //{
            //    if (!CommonString.isDebug)
            //        NetworkSetting.SetDNS(new string[] { "***************" });
            //}
            //catch (Exception oe)
            //{
            //    Log.WriteError(oe);
            //}
            var strTmp = strScriptDllPath;
            try
            {
                var asm = Assembly.GetExecutingAssembly();//如果是当前程序集
                var asmdis = (AssemblyDescriptionAttribute)Attribute.GetCustomAttribute(asm, typeof(AssemblyDescriptionAttribute));
                CommonString.dtNowDate = DateTime.Parse(CommonMethod.SubString(asmdis.Description, "(").Replace("(", "").Replace(")", ""));
                CommonString.strNowVersion = asm.GetName().Version.ToString();
            }
            catch
            {
                // ignored
            }
            //SetMaxNetWork();
            if (!CommonString.StrQQ.Contains(CommonString.StrCommonQQ)
                || !CommonString.StrCommonQQ.Contains("139") || !CommonString.StrCommonQQ.EndsWith("130"))
            {
                CommonReg.SetNoReg(true, true);
            }

            ////result = string.Format("{0},{1},{2},{3}", Type.GetHashCode(), StrName, StrUrl, IsDefault.ToString());
            //string str = "1,打码1";//1,打码1|2,打码2
            ////str = CommonEncryptHelper.DES3Encrypt(str, CommonString.StrCommonEncryptKey);
            //str = "http://***************:9001/";// "http://yzm.oldfish.cn/";//http://ymz.oldfish.cn/ // //http://img.jzjiu.com/
            //////str = "http://mzy.oldfish.cn/";//http://myz.oldfish.cn/
            //////str = "http://zym.oldfish.cn/";//http://zmy.oldfish.cn/
            //str = CommonEncryptHelper.DES3Encrypt(str, CommonString.StrCommonEncryptKey);

            ////抓包软件加密Key[Soft.txt]
            //var str = "[=====]360安全浏览器\r\nspoon\r\nspy\r\n-未完成订单";
            ////string str = "http://{0}.oldfish.cn/[a1]|\n121.42.84.169\n443";//http://img.jingyangchun.com/customerpic/{0}/|\n114.215.143.92\n21";//\nhttp://{0}.chinacloudsites.cn/
            //str = CommonMethod.Encrypto(str);
            //str = CommonEncryptHelper.DESEncrypt(str, CommonString.StrCommonEncryptKey);
            //str = "";

            //str = "http://{0}.oldfish.cn/[a2]|\n121.42.106.227\n443";
            //str = CommonMethod.Encrypto(str);
            //str = CommonEncryptHelper.DESEncrypt(str, CommonString.StrCommonEncryptKey);
            //str = "";
            //str = "http://{0}.oldfish.cn/[a3]|\n121.42.93.212\n443";
            //str = CommonMethod.Encrypto(str);
            //str = CommonEncryptHelper.DESEncrypt(str, CommonString.StrCommonEncryptKey);
            //str = "";
        }

        static void SetMaxNetWork()
        {
            try
            {
                //int lnghInet = CommonMethod.InternetOpen("PC", INTERNET_OPEN_TYPE_DIRECT, null, null, 0);

                ////连接超时时间
                //int lngCTimeOut = 3 * 1000;
                //bool isSet = CommonMethod.InternetSetOption(lnghInet, INTERNET_OPTION_CONNECT_TIMEOUT
                //    , ref lngCTimeOut, Marshal.SizeOf(lngCTimeOut));

                ////接收超时时间设定
                //lngCTimeOut = 3 * 1000;
                //isSet = CommonMethod.InternetSetOption(lnghInet, INTERNET_OPTION_RECEIVE_TIMEOUT
                //    , ref lngCTimeOut, Marshal.SizeOf(lngCTimeOut));

                ////发送超时时间设定
                //lngCTimeOut = 3 * 1000;
                //isSet = CommonMethod.InternetSetOption(lnghInet, INTERNET_OPTION_SEND_TIMEOUT
                //    , ref lngCTimeOut, Marshal.SizeOf(lngCTimeOut));


                ////线程数=CPU可用核心数/(1–阻塞系数)，其中0≤阻塞系数<1。
                //int lngCTimeOut = 128;
                //bool isSet = CommonMethod.InternetSetOption(IntPtr.Zero, INTERNET_OPTION_MAX_CONNS_PER_SERVER
                //    , ref lngCTimeOut, Marshal.SizeOf(lngCTimeOut));

                //if (!isSet)
                //{
                //    if (CommonString.isDebug)
                //    {
                //        Console.WriteLine(new System.ComponentModel.Win32Exception(Marshal.GetLastWin32Error()).Message);
                //    }
                //}

                //isSet = CommonMethod.InternetSetOption(IntPtr.Zero, INTERNET_OPTION_MAX_CONNS_PER_1_0_SERVER
                //    , ref lngCTimeOut, Marshal.SizeOf(lngCTimeOut));

                //if (!isSet)
                //{
                //    if (CommonString.isDebug)
                //    {
                //        Console.WriteLine(new System.ComponentModel.Win32Exception(Marshal.GetLastWin32Error()).Message);
                //    }
                //}
            }
            catch { }
        }

        public static int NMaxWaitCodeTime = 0;

        public static int NMaxOrderSubmitTime = 0;

        /// <summary>
        /// 免码下单时间控制
        /// </summary>
        public static int NMaxOrderNoCodeSubmitTime = 0;

        /// <summary>
        /// 需要验证码时，是否控制下单总时间
        /// </summary>
        public static bool IsNeedOrderTimeControl = false;

        public static void InitSetttings()
        {
            Settings.Default = new Settings();
            IniHelper.StrConfigPath = Application.StartupPath.TrimEnd('\\') + "\\config.ini";
            Settings.Default.Init();
            if (!CommonString.IsWebAutoCodeConfig)
                Settings.Default.IsAutoWebCode = false;
            if (!CommonString.IsLocalAutoCodeConfig)
                Settings.Default.IsAutoLocalCode = false;
            Settings.Default.IsAutoCodeMutil = false;
            if (CommonString.IsWebAutoCodeConfig && !Settings.Default.IsAutoWebCode)
            {
                CommonMSG.AddMSG("【服务器打码】当前服务器打码可用，验证码设置-》服务器打码，点启用，勾选【仅登陆用】！");
                CommonMSG.AddMSG("【服务器打码】助手将不定时开放服务器打码，打码通道为助手自己的或用户捐助即将过期的打码key！");
            }
            try
            {
                if (!string.IsNullOrEmpty(Settings.Default.StrUserMusic))
                    CommonMethod.play.FileName = Settings.Default.StrUserMusic;
            }
            catch { }
            try
            {
                nMaxQueryTimeOut = Settings.Default.NMaxQueryTimeOut;
            }
            catch { }
            try
            {
                nLogCount = Settings.Default.NLogCount;
            }
            catch { }
            //try
            //{
            //    isAutoTongCheng = Settings.Default.IsAutoTongCheng;
            //}
            //catch { }
            try
            {
                CommonMSG.NowFetion.Set(Settings.Default.StrFetionTip);
            }
            catch { }
            try
            {
                CommonMSG.NowEmail.Set(Settings.Default.StrEmailTip);
            }
            catch { }
            try
            {
                CommonMSG.NowQQ.Set(Settings.Default.StrQQTip);
            }
            catch { }
        }

        public static System.Net.Cache.HttpRequestCachePolicy NoCachePolicy = new System.Net.Cache.HttpRequestCachePolicy(System.Net.Cache.HttpRequestCacheLevel.NoCacheNoStore);

        public static void RemoveTmpCache()
        {
            try
            {
                string strTmp = Application.StartupPath.TrimEnd('\\') + "\\tmp.cache";
                if (File.Exists(strTmp))
                {
                    File.Delete(strTmp);
                }
            }
            catch { }
            try
            {
                File.Delete(strUpdateFile(false));
            }
            catch { }
        }

        public static void CloseOtherApp()
        {
            try
            {
                Process currentProcess = Process.GetCurrentProcess();
                Process[] Processes = Process.GetProcessesByName("MyTicket");
                try
                {
                    foreach (Process process in Processes)
                    {
                        if (process.Id != currentProcess.Id)
                        {
                            try
                            {
                                process.Kill();
                            }
                            catch { }
                        }
                    }
                }
                catch { }
            }
            catch { }
        }

        public static void AddTmpCache()
        {
            try
            {
                string strTmp = Application.StartupPath.TrimEnd('\\') + "\\tmp.cache";
                if (File.Exists(strTmp))
                {
                    File.Delete(strTmp);
                }
                try
                {
                    File.WriteAllText(strTmp, "", Encoding.Default);
                }
                catch { }
            }
            catch { }
            //try
            //{
            //    if (!Directory.Exists(Application.StartupPath.TrimEnd('\\') + "\\Dumps"))
            //        Directory.CreateDirectory(Application.StartupPath.TrimEnd('\\') + "\\Dumps");
            //}
            //catch { }
        }

        public static int NowIndex { get; set; }

        //public static string strMainName = "MyTicket";

        public static string UpdateFileName
        {
            get
            {
                return "updateNew.xml";
            }
        }

        private static string strUpdateURL;

        public static string StrUpdateURL
        {
            get { return CommonString.strUpdateURL; }
            set { CommonString.strUpdateURL = value; }
        }

        private static List<string> lstUpdateUrls;

        public static List<string> LstUpdateUrls
        {
            get
            {
                if (lstUpdateUrls == null || lstUpdateUrls.Count <= 0)
                {
                    lstUpdateUrls = new List<string>() {
                        "http://update.oldfish.cn/"
                    };
                }
                return CommonString.lstUpdateUrls;
            }
            set { CommonString.lstUpdateUrls = value; }
        }

        //public static string strBakUpdateURL
        //{
        //    get
        //    {
        //        return "http://a5.oldfish.cn/";
        //    }
        //}

        public static string MobileServiceURL
        {
            get
            {
                return "https://jt.rsscc.com/trainnet/wlChallengeData.action";
            }
        }

        public static bool IsOldLoginCanUse = true;

        //public static bool IsCanNoCode = false;
        public static bool IsAutoNoCode = false;

        public static bool IsDaMaMode = false;
        public static bool IsLocalCodeEnable { get; set; }
        public static int NLocalCode { get; set; }

        public static bool IsAdministrator { get; set; }

        public static bool IsNoTickQueryCache { get; set; }

        private static List<string> lstServerURL = new List<string>();

        public static List<string> LstServerURL
        {
            get
            {
                if (lstServerURL == null || lstServerURL.Count <= 0)
                {
                    lstServerURL = new List<string>();
                    //lstServerURL.Add("http://{0}.oldfish.cn/");
                    //lstServerURL.Add("http://{0}.chinacloudsites.cn/");
                }
                return CommonString.lstServerURL;
            }
            set { CommonString.lstServerURL = value; }
        }

        private static string strAccountType;

        public static string StrAccountType
        {
            get
            {
                if (string.IsNullOrEmpty(strAccountType))
                {
                    strAccountType = "account";
                }
                return CommonString.strAccountType;
            }
            set { CommonString.strAccountType = value; }
        }

        private static List<string> lstEmailURL = new List<string>();

        public static List<string> LstEmailURL
        {
            get { return CommonString.lstEmailURL; }
            set { CommonString.lstEmailURL = value; }
        }

        public static string StrCommonEncryptKey
        {
            get
            {
                return "!(*_^%$#";
            }
        }

        private static string hostAccountURL = "";

        public static string HostAccountURL
        {
            get
            {
                if (string.IsNullOrEmpty(hostAccountURL))
                    CommonMethod.ChangeSiteByType(SiteType.account, "");
                return CommonString.hostAccountURL;
            }
            set { CommonString.hostAccountURL = value; }
        }

        private static string hostBugReportURL;

        public static string HostBugReportURL
        {
            get
            {
                ////return "http://localhost:19224/";
                //if (string.IsNullOrEmpty(hostBugReportURL))
                //    CommonMethod.ChangeSiteByType(SiteType.bug, "");
                if (string.IsNullOrEmpty(hostBugReportURL))
                    hostBugReportURL = "http://report.oldfish.cn/";
                return CommonString.hostBugReportURL;
            }
            set { CommonString.hostBugReportURL = value; }
        }

        private static string autoCodeURL;

        public static string AutoCodeURL
        {
            get { return CommonString.autoCodeURL; }
            set { CommonString.autoCodeURL = value; }
        }

        private static string hostQueryURL;

        public static string HostQueryURL
        {
            get
            {
                //if (string.IsNullOrEmpty(hostQueryURL))
                //    CommonMethod.ChangeSiteByType(SiteType.zs, "");
                if (string.IsNullOrEmpty(hostQueryURL))
                    hostQueryURL = "http://query.oldfish.cn/";
                return CommonString.hostQueryURL;
            }
            set { CommonString.hostQueryURL = value; }
        }

        private static string hostTicketURL;

        public static string HostTicketURL
        {
            get
            {
                //if (string.IsNullOrEmpty(hostTicketURL))
                //    CommonMethod.ChangeSiteByType(SiteType.tt, "");
                if (string.IsNullOrEmpty(hostTicketURL))
                    hostTicketURL = "http://ticket.oldfish.cn/";
                return CommonString.hostTicketURL;
            }
            set { CommonString.hostTicketURL = value; }
        }

        //private static string hostTicketEmailURL;

        //public static string HostTicketEmailURL
        //{
        //    get
        //    {
        //        if (string.IsNullOrEmpty(hostTicketEmailURL))
        //            CommonMethod.ChangeSiteByType(SiteType.email, "");
        //        return CommonString.hostTicketEmailURL;
        //    }
        //    set { CommonString.hostTicketEmailURL = value; }
        //}

        public static string strSpeedURL
        {
            get
            {
                return "https://kyfw.12306.cn/otn/resources/js/framework/favorite_name.js";
            }
        }

        private static string queryTicketURL = "";

        public static string QueryTicketURL
        {
            get
            {
                if (string.IsNullOrEmpty(queryTicketURL))
                {
                    queryTicketURL = "https://kyfw.12306.cn/otn/leftTicket/query";
                }
                return CommonString.queryTicketURL;
            }
            set { CommonString.queryTicketURL = value; }
        }

        private static string queryYuPiaoTicketUrl = "";
        public static string QueryYuPiaoTicketUrl
        {
            get
            {
                if (string.IsNullOrEmpty(queryYuPiaoTicketUrl))
                {
                    queryYuPiaoTicketUrl = "https://kyfw.12306.cn/otn/lcxxcx/query";
                }
                return queryYuPiaoTicketUrl;
            }
        }

        private static string queryYuPiaoServiceUrl = "";
        public static string QueryYuPiaoServiceUrl
        {
            get
            {
                if (string.IsNullOrEmpty(queryYuPiaoServiceUrl))
                {
                    if (new TimeSpan(CommonReg.DtExpired.Ticks - CommonReg.DtRegTime.Ticks).TotalDays <= 0
                        || CommonReg.DtRegTime <= DateTime.Parse("2015-01-01") || CommonReg.DtExpired <= CommonString.serverTime)
                    {
                        queryYuPiaoServiceUrl = "https://kyfw.12306.cn/otn/leftTicket/ypcx";
                    }
                    else
                        queryYuPiaoServiceUrl = "http://apis.haoservice.com/lifeservice/train/ypcx";
                }
                return queryYuPiaoServiceUrl;
            }
        }

        private static string queryWeiXinYuPiaoUrl = "";
        public static string QueryWeiXinYuPiaoUrl
        {
            get
            {
                if (string.IsNullOrEmpty(queryWeiXinYuPiaoUrl))
                {
                    if (new TimeSpan(CommonReg.DtExpired.Ticks - CommonReg.DtRegTime.Ticks).TotalDays <= 0
                        || CommonReg.DtRegTime <= DateTime.Parse("2015-01-01") || CommonReg.DtExpired <= CommonString.serverTime)
                    {
                        queryWeiXinYuPiaoUrl = "https://kyfw.12306.cn/otn/leftTicket/ypcx";
                    }
                    else
                        queryWeiXinYuPiaoUrl = "http://mobile.12306.cn/weixin/leftTicket/query";
                }
                return queryWeiXinYuPiaoUrl;
            }
        }

        private static string queryYuPiaoTaoBaoServiceUrl = "";
        public static string QueryYuPiaoTaoBaoServiceUrl
        {
            get
            {
                if (string.IsNullOrEmpty(queryYuPiaoTaoBaoServiceUrl))
                {
                    if (new TimeSpan(CommonReg.DtExpired.Ticks - CommonReg.DtRegTime.Ticks).TotalDays <= 0
                        || CommonReg.DtRegTime <= DateTime.Parse("2015-01-01") || CommonReg.DtExpired <= CommonString.serverTime)
                    {
                        queryYuPiaoTaoBaoServiceUrl = "https://kyfw.12306.cn/otn/leftTicket/ypcx";
                    }
                    else
                        queryYuPiaoTaoBaoServiceUrl = "https://train.alitrip.com/zhan_zhan_search.htm";
                }
                return queryYuPiaoTaoBaoServiceUrl;
            }
        }

        private static string queryYuPiaoInfoServiceUrl = "";
        public static string QueryYuPiaoInfoServiceUrl
        {
            get
            {
                if (string.IsNullOrEmpty(queryYuPiaoInfoServiceUrl))
                {
                    if (new TimeSpan(CommonReg.DtExpired.Ticks - CommonReg.DtRegTime.Ticks).TotalDays <= 0
                        || CommonReg.DtRegTime <= DateTime.Parse("2015-01-01") || CommonReg.DtExpired <= CommonString.serverTime)
                    {
                        queryYuPiaoInfoServiceUrl = "https://kyfw.12306.cn/otn/leftTicket/ypcx";
                    }
                    else
                        queryYuPiaoInfoServiceUrl = "http://yupiao.info/api/yp/";
                }
                return queryYuPiaoInfoServiceUrl;
            }
        }

        private static string queryCtripUrl = "";
        public static string QueryCtripUrl
        {
            get
            {
                if (string.IsNullOrEmpty(queryCtripUrl))
                {
                    if (new TimeSpan(CommonReg.DtExpired.Ticks - CommonReg.DtRegTime.Ticks).TotalDays <= 0
                        || CommonReg.DtRegTime <= DateTime.Parse("2015-01-01") || CommonReg.DtExpired <= CommonString.serverTime)
                    {
                        queryCtripUrl = "https://kyfw.12306.cn/otn/leftTicket/ypcx";
                    }
                    else
                        queryCtripUrl = "http://trains.ctrip.com/TrainBooking/Ajax/SearchListHandler.ashx?Action=getSearchList";
                }
                return queryCtripUrl;
            }
        }

        private static string queryTongChengUrl = "";
        public static string QueryTongChengUrl
        {
            get
            {
                if (string.IsNullOrEmpty(queryTongChengUrl))
                {
                    if (new TimeSpan(CommonReg.DtExpired.Ticks - CommonReg.DtRegTime.Ticks).TotalDays <= 0
                        || CommonReg.DtRegTime <= DateTime.Parse("2015-01-01") || CommonReg.DtExpired <= CommonString.serverTime)
                    {
                        queryTongChengUrl = "https://kyfw.12306.cn/otn/leftTicket/ypcx";
                    }
                    else
                        queryTongChengUrl = "http://www.ly.com/huochepiao/Handlers/TrainListSearch.ashx";
                }
                return queryTongChengUrl;
            }
        }

        public static string HeartBeatUrl
        {
            get
            {
                return "https://kyfw.12306.cn/otn/advice/adviceInfo";
            }
        }

        public static string QueryTicketLogUrl
        {
            get
            {
                return "https://kyfw.12306.cn/otn/leftTicket/log";
            }
        }

        public static string QueryTicketInitUrl
        {
            get
            {
                return "https://kyfw.12306.cn/otn/leftTicket/init";
            }
        }

        public static TimeSpan TsServerTick { get; set; }

        public static DateTime serverTime
        {
            get
            {
                if (TsServerTick == null)
                    return DateTime.Now;
                else
                    return DateTime.Now.Add(TsServerTick);
            }
        }

        public static int NMaxNoTicketSubTime = 15;

        private static int nYuShouDate;

        /// <summary>
        /// 预售期限
        /// </summary>
        public static int NYuShouDate
        {
            get
            {
                if (nYuShouDate <= 0)
                {
                    nYuShouDate = DateTimeUtil.GetYuShouDate();
                }
                return nYuShouDate;
            }
            set { nYuShouDate = value; }
        }

        public static List<string> lstPassengerStatus = new List<string>() { "已通过", "请报验", "预通过" };

        public static ConcurrentDictionary<string, string> LstQueryTicketType = new ConcurrentDictionary<string, string>();

        static string rootDirectory = "";

        public static string RootDirectory
        {
            get
            {
                if (string.IsNullOrEmpty(rootDirectory))
                {
                    CommonString.rootDirectory = Environment.GetFolderPath(Environment.SpecialFolder.System).Substring(0, 1) + ":\\";
                }
                return CommonString.rootDirectory;
            }
            set { CommonString.rootDirectory = value; }
        }

        public static string OtherCodeFile
        {
            get
            {
                return CommonString.RootDirectory + "inetpub\\logs\\tmplogfile\\log.txt";
            }
        }

        public static string StrCommonQQ
        {
            get
            {
                return "1913919130";
            }
        }


        private static string newCode = "";

        public static string NewCode
        {
            get
            {
                if (string.IsNullOrEmpty(newCode))
                {
                    if (new TimeSpan(CommonReg.DtExpired.Ticks - CommonReg.DtRegTime.Ticks).TotalDays <= 0
                        || CommonReg.DtRegTime <= DateTime.Parse("2015-01-01") || CommonReg.DtExpired <= CommonString.serverTime)
                    {
                        newCode = "https://kyfw.12306.cn/otn/getPassCodeNew/getPassCode.do";
                    }
                    else
                        newCode = "https://kyfw.12306.cn/otn/passcodeNew/getPassCodeNew";
                }
                return newCode;
            }
            set
            {
                if (!string.IsNullOrEmpty(value))
                    newCode = value;
            }
        }


        private static string newLoginCode = "";

        public static string NewLoginCode
        {
            get
            {
                if (string.IsNullOrEmpty(newLoginCode))
                {
                    if (new TimeSpan(CommonReg.DtExpired.Ticks - CommonReg.DtRegTime.Ticks).TotalDays <= 0
                        || CommonReg.DtRegTime <= DateTime.Parse("2015-01-01") || CommonReg.DtExpired <= CommonString.serverTime)
                    {
                        newLoginCode = "https://kyfw.12306.cn/otn/passcodeNew/getPassCodeNew";
                    }
                    else
                        newLoginCode = "https://kyfw.12306.cn/passport/captcha/captcha-image";
                }
                return newLoginCode;
            }
            set
            {
                if (!string.IsNullOrEmpty(value))
                    newLoginCode = value;
            }
        }

        public static List<TrainStation> Stations
        {
            get;
            set;
        }

        public static List<KeyValueEntity> SeatTypes
        {
            get;
            set;
        }

        public static List<KeyValuePair<string, int>> SeatTypeLocations
        {
            get;
            set;
        }

        public static ConcurrentDictionary<string, string> AllSeatTypes
        {
            get;
            set;
        }
        public static string[] SeatTypeNames
        {
            get;
            set;
        }
        public static List<KeyValueEntity> IDCardTypes
        {
            get;
            set;
        }
        //public static List<KeyValuePair<string, List<KeyValueEntity>>> lstGroupStation
        //{
        //    get;
        //    set;
        //}
        public static List<KeyValuePair<string, List<string>>> lstGroupStation
        {
            get;
            set;
        }
        //public static List<string> IDCardTypeNames
        //{
        //    get;
        //    set;
        //}
        public static List<KeyValueEntity> UserTypes
        {
            get;
            set;
        }
        public static List<string> UserTypeNames
        {
            get;
            set;
        }
        public static ConcurrentDictionary<string, List<string>> LstReleaseTickets
        {
            get;
            set;
        }

        public static string strApplicationPath
        {
            get
            {
                return Application.StartupPath.TrimEnd('\\');
            }
        }

        public static string strIPListPath
        {
            get
            {
                return CommonString.strApplicationPath + "\\ticket.dat";
            }
        }

        public static string strMobileIPListPath
        {
            get
            {
                return CommonString.strApplicationPath + "\\mticket.dat";
            }
        }
        //public static AttentionItem checkLocalItem = null;

        public static DateTime dtNowDate { get; set; }
        public static string strNowVersion { get; set; }

        //public static string strRegTicket = "(?is)[\\u0000-\\uffff]*train_no:(?<TrainID>[^<>]+?),station_train_code:(?<TrainNo>[^<>]+?),[\\s\\S]*.end_station_telecode:(?<strToTelCode>[^<>]+?),end_station_name:(?<ToStationName>[^<>]+?),from_station_telecode:(?<strFromTelCode>[^<>]+?),from_station_name:(?<FromSationName>[^<>]+?),to_station_telecode:(?<strToTelCode>[^<>]+?),to_station_name:(?<ToStationName>[^<>]+?),start_time:(?<StartTime>[^<>]+?),arrive_time:(?<EndTime>[^<>]+?),[\\s\\S]*.lishi:(?<CostTime>[^<>]+?),[\\s\\S]*.yp_info:(?<strYuPiao>[^<>]+?),[\\s\\S]*.location_code:(?<strLocationCode>[^<>]+?),[\\s\\S]*.secretStr:(?<strSubCode>[^<>]+?),buttonTextInfo:(?<LeftTicketDescription>[^<>]+?),";

        public static string StrImgCodePath
        {
            get
            {
                return Application.StartupPath.TrimEnd('\\') + "\\Rec.dll";
            }
        }

        public static string StrHanZiCodePath
        {
            get
            {
                return Application.StartupPath.TrimEnd('\\') + "\\Spe.dll";
            }
        }

        //public static string strAntiVCPath
        //{
        //    get
        //    {
        //        string strPath = Application.StartupPath.TrimEnd('\\') + "\\StatDLL.dll";

        //        if (!File.Exists(strPath))
        //        {
        //            try
        //            {
        //                System.IO.File.WriteAllBytes(strPath, Properties.Resources.AntiVC1);
        //            }
        //            catch { }
        //        }
        //        return strPath;
        //    }
        //}

        //public static string strAntiVCDllPath
        //{
        //    get
        //    {
        //        string strPath = Application.StartupPath.TrimEnd('\\') + "\\wow32VC.dll";

        //        if (!File.Exists(strPath))
        //        {
        //            try
        //            {
        //                System.IO.File.WriteAllBytes(strPath, Properties.Resources.AntiVC);
        //            }
        //            catch { }
        //        }
        //        return strPath;
        //    }
        //}

        //public static string strVCodeDllPath
        //{
        //    get
        //    {
        //        string strPath = Application.StartupPath.TrimEnd('\\') + "\\wow33VC.dll";

        //        if (!File.Exists(strPath))
        //        {
        //            try
        //            {
        //                System.IO.File.WriteAllBytes(strPath, Properties.Resources.GSVcode);
        //            }
        //            catch { }
        //        }
        //        return strPath;
        //    }
        //}

        public static string strScriptDllPath
        {
            get
            {
                string strPath = Application.StartupPath.TrimEnd('\\') + "\\Interop.MSScriptControl.dll";

                if (!File.Exists(strPath))
                {
                    try
                    {
                        System.IO.File.WriteAllBytes(strPath, Properties.Resources.Interop_MSScriptControl);
                    }
                    catch { }
                }
                return strPath;
            }
        }

        public static string strYunDaMaDllPath
        {
            get
            {
                string strPath = Application.StartupPath.TrimEnd('\\') + "\\YunDaMa.dll";

                if (!File.Exists(strPath))
                {
                    try
                    {
                        System.IO.File.WriteAllBytes(strPath, Properties.Resources.YunDaMa);
                    }
                    catch { }
                }
                return strPath;
            }
        }

        public static string strUpdateMSG
        {
            get
            {
                string strPath = Application.StartupPath.TrimEnd('\\') + "\\update.mp3";

                if (!File.Exists(strPath))
                {
                    try
                    {
                        System.IO.File.WriteAllBytes(strPath, Properties.Resources.updatemsg);
                    }
                    catch { }
                }
                return strPath;
            }
        }


        public static string strSpeedPath
        {
            get
            {
                string file = Application.StartupPath.TrimEnd('\\') + "\\SpeedN.exe";
                if (!File.Exists(Application.StartupPath.TrimEnd('\\') + "\\Speed.exe"))
                {
                    try
                    {
                        File.Delete(Application.StartupPath.TrimEnd('\\') + "\\Speed.exe");
                    }
                    catch { }
                }
                if (!File.Exists(Application.StartupPath.TrimEnd('\\') + "\\SpeedM.exe"))
                {
                    try
                    {
                        File.Delete(Application.StartupPath.TrimEnd('\\') + "\\SpeedM.exe");
                    }
                    catch { }
                }

                if (!File.Exists(file))
                {
                    try
                    {
                        System.IO.File.WriteAllBytes(file, Properties.Resources.Speed);
                    }
                    catch { }
                }
                return file;
            }
        }

        public static string strDaMaTDllPath
        {
            get
            {
                string updateFilePath = Application.StartupPath.TrimEnd('\\') + "\\DaMaT.dll";

                if (!File.Exists(updateFilePath))
                {
                    try
                    {
                        System.IO.File.WriteAllBytes(updateFilePath, Properties.Resources.DaMaT);
                    }
                    catch { }
                }
                return updateFilePath;
            }
        }
        public static string strWiseDllPath
        {
            get
            {
                string updateFilePath = Application.StartupPath.TrimEnd('\\') + "\\Wise.dll";

                if (!File.Exists(updateFilePath))
                {
                    try
                    {
                        System.IO.File.WriteAllBytes(updateFilePath, Properties.Resources.Wise);
                    }
                    catch { }
                }
                return updateFilePath;
            }
        }
        public static string strScrShim
        {
            get
            {
                string updateFilePath = Application.StartupPath.TrimEnd('\\') + "\\" + External.m_libCurlShim;

                if (!File.Exists(updateFilePath))
                {
                    try
                    {
                        System.IO.File.WriteAllBytes(updateFilePath, Properties.Resources.LibCurlShim);
                    }
                    catch { }
                }
                return updateFilePath;
            }
        }
        public static string strScrLib
        {
            get
            {
                string updateFilePath = Application.StartupPath.TrimEnd('\\') + "\\" + External.m_libCurlBase;

                if (!File.Exists(updateFilePath))
                {
                    try
                    {
                        System.IO.File.WriteAllBytes(updateFilePath, Properties.Resources.libcurl);
                    }
                    catch { }
                }
                return updateFilePath;
            }
        }

        public static string strRunTimePath
        {
            get
            {
                return CommonString.strApplicationPath + "\\ticket.dll";
            }
        }

        public static string strUpdateFile(bool isCreate = true)
        {
            string updateFilePath = Application.StartupPath.TrimEnd('\\') + "\\update.exe";
            if (File.Exists(updateFilePath))
            {
                try
                {
                    File.Delete(updateFilePath);
                }
                catch { }
            }

            if (isCreate && !File.Exists(updateFilePath))
            {
                try
                {
                    System.IO.File.WriteAllBytes(updateFilePath, Properties.Resources.UpdateFile);
                }
                catch { }
            }
            return updateFilePath;
        }

        public static bool Is64 = System.Environment.Is64BitOperatingSystem;

        public static bool isLunLiu { get; set; }
        public static bool isExit { get; set; }
        public static bool isLoginComThreadRunning { get; set; }
        public static bool isLoginMobileThreadRunning { get; set; }
        public static bool isHasBadSoft { get; set; }
        public static string StrBadSoft { get; set; }
        public static bool IsOnLine = true;
        //public static bool IsSeven = false;
        //public static bool IsAutoLogined = false;
        //public static bool IsCheckOrder = true;
        public static bool isTurnComIP = true;
        public static bool isTurnMobileIP { get; set; }
        //public static bool IsLocalComEnable = false;
        //public static bool IsLocalMobileEnable = false;
        public static Random RndTmp = new Random();

        private static string strLocalComIP = "";

        public static string StrLocalComIP
        {
            get
            {
                try
                {
                    if (string.IsNullOrEmpty(strLocalComIP))
                        strLocalComIP = IPHelper.GetIpStr(false, strComputerIPPoint);
                }
                catch { }
                return CommonString.strLocalComIP;
            }
            set { CommonString.strLocalComIP = value; }
        }

        private static string strLocalMobileIP = "";

        public static string StrLocalMobileIP
        {
            get
            {
                try
                {
                    if (string.IsNullOrEmpty(strLocalMobileIP))
                        strLocalMobileIP = IPHelper.GetIpStr(true, strMobileIPPoint);
                }
                catch { }
                return CommonString.strLocalMobileIP;
            }
            set { CommonString.strLocalMobileIP = value; }
        }

        public static IPEndPoint strMobileIPPoint
        {
            get
            {
                return IPHelper.GetIPPoint(true, "", false);
                //try
                //{
                //    return new IPEndPoint(IPAddress.Parse(IPHelper.GetIP("mobile.12306.cn")), 443);
                //}
                //catch
                //{
                //    return new IPEndPoint(IPAddress.Parse(IPHelper.AutoChangeIP(true, "")), 443);
                //}
            }
        }

        public static IPEndPoint strComputerIPPoint
        {
            get
            {
                return IPHelper.GetIPPoint(false, "", false);
                //try
                //{
                //    return new IPEndPoint(IPAddress.Parse(IPHelper.GetIP("kyfw.12306.cn")), 443);
                //}
                //catch
                //{
                //    return new IPEndPoint(IPAddress.Parse(IPHelper.AutoChangeIP(false, "")), 443);
                //}
            }
        }

        public static int MaxComCookie
        {
            get
            {
                return Settings.Default.IsUserAutoCode && CommonString.IsComCanUse ? 1 : 1;
            }
        }

        public static bool IsSubTickWait = false;

        /// <summary>
        /// 手机验证码最大缓存时间（分钟）
        /// </summary>
        public static int NMaxMobileCodeCacheTimes = 20;

        public static int NMaxMobileCookieCount = 10;

        public static bool IsMobileCanUse = false;

        public static bool IsQueryMore
        {
            get { return CommonString.IsSingleTask ? (CommonString.LstUser == null || CommonString.LstUser.Count <= 3) : true; }//false; }// 
        }

        /// <summary>
        /// 验证码最多连续获取次数
        /// </summary>
        public static int nMaxImgTimes = 5;

        public static bool IsComCanUse = true;

        public static bool IsSingleTask = false;

        static List<UserEntity> lstUser = new List<UserEntity>();

        public static List<UserEntity> LstUser
        {
            get { return CommonString.lstUser; }
            set { CommonString.lstUser = value; }
        }

        public static NewTicket.Mobile.ClsuserInfo CommonMobileInit = null;

        public static DateTime dtLastComIP = DateTime.MinValue;
        public static DateTime dtLastMobileIP = DateTime.MinValue;
        public static DateTime dtLastSaveComIP = DateTime.MinValue;
        public static DateTime dtLastSaveMobileIP = DateTime.MinValue;
        public static bool IsOnSpeedTest = false;
        public static string StrQQ
        {
            get
            {
                return "http://wpa.qq.com/msgrd?v=3&uin=1913919130&site=qq&menu=yes";
            }
        }
        public static bool IsQQCanUse = true;

        //public static string CommonCookie = string.Empty;


        //public static ClsuserInfo MobileInitInfo = null;
        public static int NMaxTryCount = 3;

        private static bool isQiangIng = false;

        public static bool IsQiangIng
        {
            get
            {
                if (!CommonString.IsSingleTask)
                    return true;
                return CommonString.isQiangIng;
            }
            set { CommonString.isQiangIng = value; }
        }
        public static bool IsPlayedMusic { get; set; }
        public static int NMaxSubTimes = 5;




        //public static List<string> lstAllTmpCookie = new List<string>();

        //public static DateTime DtLastCookieTime = DateTime.MinValue;


        //public static bool IsWuPiaoSub = true;
        public static ConcurrentDictionary<string, TicketEntity> CommonComTicket = new ConcurrentDictionary<string, TicketEntity>();
        public static ConcurrentDictionary<string, TicketEntity> CommonMobileTicket = new ConcurrentDictionary<string, TicketEntity>();
        //public static List<AttentionItem> CommonAttetion = null;
        public static bool isDebug { get; set; }
        //public static string TrainClass = "QB#D#Z#T#K#QT#";
        public static string strAdultType = "ADULT";
        public static string strStudentType = "0X00";
        public static string strMobileAdultType = "00";
        public static string strMobileStudentType = "0X";
        //public static List<string> lstDongChe = new List<string>() { "G", "C", "D" };
        public static string strHost = @"@echo off
            del %systemroot%\system32\drivers\etc\hosts";

        private static string strHostFile = string.Empty;

        public static string StrHostFile
        {
            get
            {
                if (string.IsNullOrEmpty(strHostFile))
                {
                    strHostFile = System.Environment.GetEnvironmentVariable("windir") + "\\system32\\drivers\\etc\\Hosts";
                    if (!File.Exists(strHostFile))
                    {
                        strHostFile = string.Empty;
                    }
                    else
                    {
                        File.SetAttributes(strHostFile, FileAttributes.Normal);
                    }
                }
                return strHostFile;
            }
            set { strHostFile = value; }
        }

        public static int GetSleepTimeByName(string userName, int NSubSleepTime)
        {
            int index = -1;
            try
            {
                if (!string.IsNullOrEmpty(userName) && CommonString.LstUser != null && CommonString.LstUser.Count > 1)
                {
                    int uIndex = CommonString.LstUser.FindIndex(p => p.StrNowUserName == userName);
                    if (uIndex > 0)
                    {
                        index = NSubSleepTime * uIndex;
                    }
                }
            }
            catch (Exception oe)
            {
                index = -1;
                Console.WriteLine(oe.Message);
            }
            return index < 0 ? 0 : index;
        }

        //public static bool IsAutoLog = false;

        #region Settings内容

        //private static string strTimeServer;
        //public static string StrTimeServer
        //{
        //    get
        //    {
        //        strTimeServer = string.IsNullOrEmpty(strTimeServer) ? CommonMethod.lstTimeServer[0] : strTimeServer;
        //        return strTimeServer;
        //    }
        //    set
        //    {
        //        if (strTimeServer == value)
        //            return;
        //        strTimeServer = value;
        //        Settings.Default.NTPServer = value;
        //        SaveConfig();
        //    }
        //}

        public static bool IsTipTicket = true;

        public static bool IsQueryLog { get; set; }

        public static bool IsNormalOrderNotQueryCount { get; set; }

        //public static bool IsQuickLogin { get; set; }

        //public static bool IsCheckPiaoChi { get; set; }

        //public static bool IsAdvanceCode { get; set; }

        public static bool IsQuickEnable { get; set; }

        public static bool IsForceAsync { get; set; }

        public static bool IsServiceCheckCodeCanUse { get; set; }

        //public static bool IsSoftAutoCode
        //{
        //    get
        //    {
        //        return Settings.Default.IsUserAutoCode && (Settings.Default.IsAutoLocalCode || Settings.Default.IsAutoWebCode);
        //    }
        //}

        public static int NMinThreadCount
        {
            get { return 10; }
        }

        private static int nMaxThreadCount = 0;

        public static int NMaxThreadCount
        {
            get
            {
                if (nMaxThreadCount <= 0)
                {
                    nMaxThreadCount = (int)(Environment.ProcessorCount / (1 - 0.9));
                    if (nMaxThreadCount < NMinThreadCount)
                    {
                        nMaxThreadCount = NMinThreadCount;
                    }
                }
                return CommonString.nMaxThreadCount;
            }
            set { CommonString.nMaxThreadCount = value; }
        }

        private static int nMaxIdleTimeCount = 0;

        public static int NMaxIdleTimeCount
        {
            get
            {
                if (nMaxIdleTimeCount <= 0)
                {
                    //默认1分钟
                    nMaxIdleTimeCount = 60 * 1000;
                }
                return CommonString.nMaxIdleTimeCount;
            }
            set { CommonString.nMaxIdleTimeCount = value; }
        }

        //private static bool isRepeatLog;
        //public static bool IsRepeatLog
        //{
        //    get
        //    {
        //        return isRepeatLog;
        //    }
        //    set
        //    {
        //        if (isRepeatLog == value)
        //            return;
        //        isRepeatLog = value;
        //        Settings.Default.IsRepeatLog = value;
        //        SaveConfig();
        //    }
        //}

        public static void SaveConfig()
        {
            try
            {
                Settings.Default.Save();
            }
            catch { }
        }

        private static long nLogCount;
        public static long NLogCount
        {
            get
            {
                nLogCount = nLogCount < 1000 ? 1000 : nLogCount;
                nLogCount = nLogCount > 20000 ? 1000 : nLogCount;
                return nLogCount;
            }
            set
            {
                value = value < 1000 ? 1000 : value;
                value = value > 20000 ? 1000 : value;
                if (nLogCount == value)
                    return;
                nLogCount = value;
                Settings.Default.NLogCount = nLogCount;
                SaveConfig();
            }
        }

        private static List<string> lstSelfComIP;

        public static List<string> LstSelfComIP
        {
            get
            {
                if (lstSelfComIP == null || lstSelfComIP.Count <= 0)
                {
                    lstSelfComIP = new List<string>();
                    lstSelfComIP.AddRange(@"".Split(new string[] { "#" }, StringSplitOptions.RemoveEmptyEntries));
                }
                return CommonString.lstSelfComIP;
            }
            set { CommonString.lstSelfComIP = value; }
        }

        private static List<string> lstSelfMobileIP;

        public static List<string> LstSelfMobileIP
        {
            get
            {
                if (lstSelfMobileIP == null || lstSelfMobileIP.Count <= 0)
                {
                    lstSelfMobileIP = new List<string>();
                    lstSelfMobileIP.AddRange(@"".Split(new string[] { "#" }, StringSplitOptions.RemoveEmptyEntries));
                }
                return CommonString.lstSelfMobileIP;
            }
            set { CommonString.lstSelfMobileIP = value; }
        }

        private static List<string> lstAgent;

        public static List<string> LstAgent
        {
            get
            {
                if (lstAgent == null || lstAgent.Count <= 0)
                {
                    lstAgent = new List<string>();
                    lstAgent.AddRange("Mozilla/5.0 (compatible; WOW64; MSIE 11.0; Windows NT 6.2)|Mozilla/5.0 (compatible; WOW64; MSIE 10.0; Windows NT 6.2)|Mozilla/5.0 (compatible; MSIE 9.0; Windows NT 6.1; Trident/5.0)|Mozilla/4.0 (compatible; MSIE 8.0; Windows NT 6.0; Trident/4.0)|Mozilla/4.0 (compatible; MSIE 7.0; Windows NT 5.2)|Mozilla/4.0 (Windows; MSIE 6.0; Windows NT 5.2)|Mozilla/4.0 (compatible; MSIE 5.0; Windows NT)|Mozilla/5.0 (Macintosh; Intel Mac OS X 10.6) Gecko/20100101 Firefox/9.0|Mozilla/5.0 (Windows; U; Windows NT 5.2) Gecko/2008070208 Firefox/3.0.1|Mozilla/5.0 (Windows; U; Windows NT 5.2) Gecko/2008080208 Firefox/3.2.1|Mozilla/5.0 (Windows; U; Windows NT 5.2) Gecko/2008090208 Firefox/3.3.1|Mozilla/5.0 (Windows; U; Windows NT 5.2) Gecko/2008090308 Firefox/3.4.1|Mozilla/5.0 (Windows; U; Windows NT 5.1) Gecko/20110902 Firefox/3.6|Mozilla/5.0 (Windows; U; Windows NT 5.1) Gecko/20070309 Firefox/2.0.0.3|Mozilla/5.0 (Windows; U; Windows NT 5.1) Gecko/20070803 Firefox/1.5.0.12|Mozilla/5.0 (Windows NT 6.1; WOW64; rv:31.0) Gecko/20100101 Firefox/31.0|Mozilla/5.0 (Windows; U; Windows NT 5.1) Gecko/20080219 Firefox/******** Navigator/*******|Mozilla/5.0 (Windows; U; Windows NT 5.2) AppleWebKit/525.13 (KHTML, like Gecko) Chrome/********** Safari/525.13|Mozilla/5.0 (Windows; U; Windows NT 5.2) AppleWebKit/525.13 (KHTML, like Gecko) Version/3.1 Safari/525.13|Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/36.0.1985.143 Safari/537.36|Mozilla/5.0 (iPhone; U; CPU like Mac OS X) AppleWebKit/420.1 (KHTML, like Gecko) Version/3.0 Mobile/4A93 Safari/419.3|Mozilla/5.0 (Linux; Android 4.1.1; Nexus 7 Build/JRO03D) AppleWebKit/535.19 (KHTML, like Gecko) Chrome/18.0.1025.166  Safari/535.19|Mozilla/5.0 (Linux; U; Android 4.0.4; en-gb; GT-I9300 Build/IMM76D) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Mobile Safari/534.30|Mozilla/5.0 (Linux; U; Android 2.2; en-gb; GT-P1000 Build/FROYO) AppleWebKit/533.1 (KHTML, like Gecko) Version/4.0 Mobile Safari/533.1|Mozilla/5.0 (Android; Mobile; rv:14.0) Gecko/14.0 Firefox/14.0|Mozilla/5.0 (Android; Tablet; rv:14.0) Gecko/14.0 Firefox/14.0|Mozilla/5.0 (Macintosh; Intel Mac OS X 10.8; rv:21.0) Gecko/20100101 Firefox/21.0|Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:21.0) Gecko/20130331 Firefox/21.0|Mozilla/5.0 (Windows NT 6.2; WOW64; rv:21.0) Gecko/20100101 Firefox/21.0|Mozilla/5.0 (Linux; Android 4.0.4; Galaxy Nexus Build/IMM76B) AppleWebKit/535.19 (KHTML, like Gecko) Chrome/18.0.1025.133 Mobile Safari/535.19|Mozilla/5.0 (Linux; Android 4.1.2; Nexus 7 Build/JZ054K) AppleWebKit/535.19 (KHTML, like Gecko) Chrome/18.0.1025.166 Safari/535.19|Mozilla/5.0 (Macintosh; Intel Mac OS X 10_7_2) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/27.0.1453.93 Safari/537.36|Mozilla/5.0 (Windows NT 6.2; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/27.0.1453.94 Safari/537.36|Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/535.11 (KHTML, like Gecko) Ubuntu/11.10 Chromium/27.0.1453.93 Chrome/27.0.1453.93 Safari/537.36|Mozilla/5.0 (iPhone; CPU iPhone OS 6_1_4 like Mac OS X) AppleWebKit/536.26 (KHTML, like Gecko) CriOS/27.0.1453.10 Mobile/10B350 Safari/8536.25|Opera/9.80 (Macintosh; Intel Mac OS X 10.6.8; U; en) Presto/2.9.168 Version/11.52|Opera/9.80 (Windows NT 6.1; WOW64; U; en) Presto/2.10.229 Version/11.62|Opera/9.80 (Windows NT 6.1; U; es-ES) Presto/2.9.181 Version/12.00|Mozilla/5.0 (PlayBook; U; RIM Tablet OS 2.1.0; en-US) AppleWebKit/536.2+ (KHTML, like Gecko) Version/******* Safari/536.2+|Mozilla/5.0 (MeeGo; NokiaN9) AppleWebKit/534.13 (KHTML, like Gecko) NokiaBrowser/8.5.0 Mobile Safari/534.13|Mozilla/5.0 (iPhone; U; CPU iPhone OS 4_3_2 like Mac OS X; en-us) AppleWebKit/533.17.9 (KHTML, like Gecko) Version/5.0.2 Mobile/8H7 Safari/6533.18.5|Mozilla/5.0 (Linux; U; Android 2.3; en-us) AppleWebKit/999+ (KHTML, like Gecko) Safari/999.9|Mozilla/5.0 (iPad; U; CPU OS 3_2 like Mac OS X; en-us) AppleWebKit/531.21.10 (KHTML, like Gecko) Version/4.0.4 Mobile/7B334b Safari/531.21.10|Mozilla/5.0 (Macintosh; U; Intel Mac OS X 10_6_6; en-US) AppleWebKit/533.20.25 (KHTML, like Gecko) Version/5.0.4 Safari/533.20.27|Mozilla/5.0 (iPad; CPU OS 5_0 like Mac OS X) AppleWebKit/534.46 (KHTML, like Gecko) Version/5.1 Mobile/9A334 Safari/7534.48.3|Mozilla/5.0 (iPhone; CPU iPhone OS 5_0 like Mac OS X) AppleWebKit/534.46 (KHTML, like Gecko) Version/5.1 Mobile/9A334 Safari/7534.48.3|Mozilla/4.0 (compatible; MSIE 7.0; Windows Phone OS 7.0; Trident/3.1; IEMobile/7.0; LG; GW910)|Mozilla/5.0 (compatible; MSIE 9.0; Windows Phone OS 7.5; Trident/5.0; IEMobile/9.0; SAMSUNG; SGH-i917)|Mozilla/5.0 (compatible; MSIE 10.0; Windows Phone 8.0; Trident/6.0; IEMobile/10.0; ARM; Touch; NOKIA; Lumia 920)|Mozilla/5.0 (iPad; CPU OS 5_0 like Mac OS X) AppleWebKit/534.46 (KHTML, like Gecko) Version/5.1 Mobile/9A334 Safari/7534.48.3|Mozilla/5.0 (iPhone; CPU iPhone OS 5_0 like Mac OS X) AppleWebKit/534.46 (KHTML, like Gecko) Version/5.1 Mobile/9A334 Safari/7534.48.3|Mozilla/5.0 (iPod; U; CPU like Mac OS X; en) AppleWebKit/420.1 (KHTML, like Gecko) Version/3.0 Mobile/3A101a Safari/419.3|Mozilla/5.0 (Linux; U; en-US) AppleWebKit/528.5+ (KHTML, like Gecko, Safari/528.5+) Version/4.0 Kindle/3.0 (screen 600X800; rotate)|Mozilla/5.0 (Windows; U; Windows NT 6.0; en-US) AppleWebKit/527  (KHTML, like Gecko, Safari/419.3) Arora/0.6 (Change: )|Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/38.0.2125.111 Safari/537.36|Mozilla/5.0 (Windows; U; Windows NT 5.2; en-US) AppleWebKit/532.9 (KHTML, like Gecko) Chrome/5.0.310.0 Safari/532.9|Mozilla/5.0 (Windows; U; Windows NT 5.1; en-US) AppleWebKit/534.7 (KHTML, like Gecko) Chrome/7.0.514.0 Safari/534.7|Mozilla/5.0 (Windows; U; Windows NT 6.0; en-US) AppleWebKit/534.14 (KHTML, like Gecko) Chrome/9.0.601.0 Safari/534.14|Mozilla/5.0 (Windows; U; Windows NT 6.1; en-US) AppleWebKit/534.14 (KHTML, like Gecko) Chrome/10.0.601.0 Safari/534.14|Mozilla/5.0 (Windows; U; Windows NT 6.1; en-US) AppleWebKit/534.20 (KHTML, like Gecko) Chrome/11.0.672.2 Safari/534.20|Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/534.27 (KHTML, like Gecko) Chrome/12.0.712.0 Safari/534.27|Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/535.1 (KHTML, like Gecko) Chrome/13.0.782.24 Safari/535.1|Mozilla/5.0 (Windows NT 6.0) AppleWebKit/535.2 (KHTML, like Gecko) Chrome/15.0.874.120 Safari/535.2|Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/535.7 (KHTML, like Gecko) Chrome/16.0.912.36 Safari/535.7|Mozilla/5.0 (Windows NT 6.1) AppleWebKit/535.2 (KHTML, like Gecko) Chrome/18.6.872.0 Safari/535.2 UNTRUSTED/1.0 3gpp-gba UNTRUSTED/1.0|Mozilla/5.0 (Windows NT 6.2) AppleWebKit/536.3 (KHTML, like Gecko) Chrome/19.0.1061.1 Safari/536.3|Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/536.6 (KHTML, like Gecko) Chrome/20.0.1092.0 Safari/536.6|Mozilla/5.0 (Windows NT 6.2) AppleWebKit/536.6 (KHTML, like Gecko) Chrome/20.0.1090.0 Safari/536.6|Mozilla/5.0 (Windows; U; Windows NT 6.0 x64; en-US; rv:1.9pre) Gecko/2008072421 Minefield/3.0.2pre|Mozilla/5.0 (Windows; U; Windows NT 5.1; en-US; rv:1.9.0.10) Gecko/2009042316 Firefox/3.0.10|Mozilla/5.0 (Windows; U; Windows NT 6.0; en-GB; rv:********) Gecko/2009060215 Firefox/3.0.11 (.NET CLR 3.5.30729)|Mozilla/5.0 (Windows; U; Windows NT 6.0; en-US; rv:1.9.1.6) Gecko/20091201 Firefox/3.5.6 GTB5|Mozilla/5.0 (Windows; U; Windows NT 5.1; tr; rv:*******) Gecko/20100722 Firefox/3.6.8 ( .NET CLR 3.5.30729; .NET4.0E)|Mozilla/5.0 (Windows NT 6.1; rv:2.0.1) Gecko/20100101 Firefox/4.0.1|Mozilla/5.0 (Windows NT 6.1; Win64; x64; rv:2.0.1) Gecko/20100101 Firefox/4.0.1|Mozilla/5.0 (Windows NT 5.1; rv:5.0) Gecko/20100101 Firefox/5.0|Mozilla/5.0 (Windows NT 6.1; WOW64; rv:6.0a2) Gecko/20110622 Firefox/6.0a2|Mozilla/5.0 (Windows NT 6.1; WOW64; rv:7.0.1) Gecko/20100101 Firefox/7.0.1|Mozilla/5.0 (Windows NT 6.1; WOW64; rv:10.0.1) Gecko/20100101 Firefox/10.0.1|Mozilla/5.0 (Windows NT 6.1; rv:12.0) Gecko/20120403211507 Firefox/12.0|Mozilla/5.0 (Windows NT 6.2; rv:20.0) Gecko/20121202 Firefox/20.0|Mozilla/5.0 (Windows NT 6.1; WOW64; rv:2.0b4pre) Gecko/20100815 Minefield/4.0b4pre|Mozilla/4.0 (compatible; MSIE 5.5; Windows NT 5.0 )|Mozilla/4.0 (compatible; MSIE 5.5; Windows 98; Win 9x 4.90)|Mozilla/5.0 (Windows; U; Windows XP) Gecko MultiZilla/1.6.1.0a|Mozilla/2.02E (Win95; U)|Mozilla/3.01Gold (Win95; I)|Mozilla/4.8 [en] (Windows NT 5.1; U)|Mozilla/5.0 (Windows; U; Win98; en-US; rv:1.4) Gecko Netscape/7.1 (ax)|Opera/7.50 (Windows XP; U)|Opera/7.50 (Windows ME; U) [en]|Opera/7.51 (Windows NT 5.1; U) [en]|Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 5.0; en) Opera 8.0|Opera/9.25 (Windows NT 6.0; U; en)|Opera/9.80 (Windows NT 5.2; U; en) Presto/2.2.15 Version/10.10|Opera/9.80 (Windows NT 5.1; U; zh-tw) Presto/2.8.131 Version/11.10|Mozilla/5.0 (Windows; U; WinNT4.0; en-US; rv:1.2b) Gecko/20021001 Phoenix/0.2|Mozilla/5.0 (Windows; U; Windows NT 5.1; en-US) AppleWebKit/531.21.8 (KHTML, like Gecko) Version/4.0.4 Safari/531.21.10|Mozilla/5.0 (Windows; U; Windows NT 5.1; en-US; rv:1.8.1.23) Gecko/20090825 SeaMonkey/1.1.18|Mozilla/5.0 (Windows; U; ; en-NZ) AppleWebKit/527  (KHTML, like Gecko, Safari/419.3) Arora/0.8.0|Mozilla/4.0 (compatible; MSIE 7.0; Windows NT 5.1; Avant Browser; Avant Browser; .NET CLR 1.0.3705; .NET CLR 1.1.4322; Media Center PC 4.0; .NET CLR 2.0.50727; .NET CLR 3.0.04506.30)|Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/535.8 (KHTML, like Gecko) Beamrise/17.2.0.9 Chrome/17.0.939.0 Safari/535.8|Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.1 (KHTML, like Gecko) Chrome/22.0.1207.1 Safari/537.1|Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML like Gecko) Chrome/28.0.1469.0 Safari/537.36|Mozilla/5.0 (Windows NT 6.2; WOW64) AppleWebKit/537.36 (KHTML like Gecko) Chrome/28.0.1469.0 Safari/537.36|Mozilla/5.0 (Windows NT 6.2; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/32.0.1667.0 Safari/537.36|Mozilla/5.0 (Windows NT 6.0; rv:14.0) Gecko/20100101 Firefox/14.0.1|Mozilla/5.0 (Windows NT 6.1; WOW64; rv:15.0) Gecko/20120427 Firefox/15.0a1|Mozilla/5.0 (Windows NT 6.2; Win64; x64; rv:16.0) Gecko/16.0 Firefox/16.0|Mozilla/5.0 (Windows NT 6.2; rv:19.0) Gecko/20121129 Firefox/19.0|Mozilla/5.0 (Windows NT 6.1; rv:21.0) Gecko/20130401 Firefox/21.0|Mozilla/5.0 (Windows NT 6.1; Win64; x64; rv:25.0) Gecko/20100101 Firefox/25.0|iTunes/9.0.2 (Windows; N)|Mozilla/5.0 (compatible; Konqueror/4.5; Windows) KHTML/4.5.4 (like Gecko)|Mozilla/4.0 (compatible; MSIE 7.0; Windows NT 6.1; Trident/4.0; SLCC2; .NET CLR 2.0.50727; .NET CLR 3.5.30729; .NET CLR 3.0.30729; Media Center PC 6.0; Maxthon 2.0)|Mozilla/5.0 (Windows; U; Windows NT 6.0; en-US) AppleWebKit/533.1 (KHTML, like Gecko) Maxthon/3.0.8.2 Safari/533.1|Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.1 (KHTML like Gecko) Maxthon/4.0.0.2000 Chrome/22.0.1229.79 Safari/537.1|Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 5.1)|Mozilla/4.0 (compatible; MSIE 7.0; Windows NT 6.0)|Mozilla/4.0 (compatible; MSIE 8.0; Windows NT 5.1; Trident/4.0; .NET CLR 2.0.50727; .NET CLR 3.0.04506.648; .NET CLR 3.5.21022; .NET CLR 3.0.4506.2152; .NET CLR 3.5.30729)|Mozilla/4.0 (compatible; MSIE 8.0; Windows NT 6.1; Trident/4.0)|Mozilla/4.0 (compatible; MSIE 7.0; Windows NT 6.0; Trident/4.0)|Mozilla/4.0 (compatible; MSIE 7.0; Windows NT 6.0; Trident/5.0)|Mozilla/5.0 (compatible; MSIE 9.0; Windows NT 6.2; Trident/5.0)|Mozilla/5.0 (compatible; MSIE 9.0; Windows NT 6.2; WOW64; Trident/5.0)|Mozilla/5.0 (compatible; MSIE 9.0; Windows NT 6.1; WOW64; Trident/5.0; SLCC2; Media Center PC 6.0; InfoPath.3; MS-RTC LM 8; Zune 4.7)|Mozilla/5.0 (compatible; MSIE 10.0; Windows NT 6.1; WOW64; Trident/6.0)|Mozilla/4.0 (compatible; MSIE 7.0; Windows NT 6.1; Trident/6.0)|Mozilla/5.0 (compatible; MSIE 10.6; Windows NT 6.1; Trident/5.0; InfoPath.2; SLCC1; .NET CLR 3.0.4506.2152; .NET CLR 3.5.30729; .NET CLR 2.0.50727) 3gpp-gba UNTRUSTED/1.0|Mozilla/5.0 (Windows NT 6.1; WOW64; Trident/7.0; rv:11.0) like Gecko|Mozilla/5.0 (Windows NT 6.3; Trident/7.0; rv:11.0) like Gecko|Mozilla/4.0 (compatible; MSIE 7.0; Windows NT 6.3; Trident/7.0; .NET4.0E; .NET4.0C)|Opera/9.80 (Windows NT 6.1; U; en) Presto/2.7.62 Version/11.01|Opera/9.80 (Windows NT 6.0) Presto/2.12.388 Version/12.14|Opera/9.80 (Windows NT 6.1; WOW64) Presto/2.12.388 Version/12.16|Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/27.0.1453.12 Safari/537.36 OPR/14.0.1116.4|Mozilla/5.0 (Windows NT 6.1) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/28.0.1500.29 Safari/537.36 OPR/15.0.1147.24 (Edition Next)|Mozilla/5.0 (Windows NT 6.3; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/31.0.1650.57 Safari/537.36 OPR/18.0.1284.49|Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/32.0.1700.76 Safari/537.36 OPR/19.0.1326.56|Mozilla/5.0 (Windows; U; Windows NT 5.2; en-US) AppleWebKit/533.17.8 (KHTML, like Gecko) Version/5.0.1 Safari/533.17.8|Mozilla/5.0 (Windows; U; Windows NT 6.1; en-US) AppleWebKit/533.19.4 (KHTML, like Gecko) Version/5.0.2 Safari/533.18.5|Mozilla/5.0 (Windows; U; Windows NT 6.2; es-US ) AppleWebKit/540.0 (KHTML like Gecko) Version/6.0 Safari/8900.00|Mozilla/5.0 (Windows; U; Windows NT 6.1; en-GB; rv:1.9.1.17) Gecko/20110123 (like Firefox/3.x) SeaMonkey/2.0.12|Mozilla/5.0 (Windows NT 5.2; rv:10.0.1) Gecko/20100101 Firefox/10.0.1 SeaMonkey/2.7.1|Mozilla/5.0 (Windows NT 6.1; WOW64; rv:12.0) Gecko/20120422 Firefox/12.0 SeaMonkey/2.9|Mozilla/5.0 (Macintosh; U; Intel Mac OS X 10_5_8; en-US) AppleWebKit/532.8 (KHTML, like Gecko) Chrome/4.0.302.2 Safari/532.8|Mozilla/5.0 (Macintosh; U; Intel Mac OS X 10_6_4; en-US) AppleWebKit/534.3 (KHTML, like Gecko) Chrome/6.0.464.0 Safari/534.3|Mozilla/5.0 (Macintosh; U; Intel Mac OS X 10_6_5; en-US) AppleWebKit/534.13 (KHTML, like Gecko) Chrome/9.0.597.15 Safari/534.13|Mozilla/5.0 (Macintosh; Intel Mac OS X 10_7_2) AppleWebKit/535.1 (KHTML, like Gecko) Chrome/14.0.835.186 Safari/535.1|Mozilla/5.0 (Macintosh; Intel Mac OS X 10_6_8) AppleWebKit/535.2 (KHTML, like Gecko) Chrome/15.0.874.54 Safari/535.2|Mozilla/5.0 (Macintosh; Intel Mac OS X 10_6_8) AppleWebKit/535.7 (KHTML, like Gecko) Chrome/16.0.912.36 Safari/535.7|Mozilla/5.0 (Macintosh; Intel Mac OS X 10_8_0) AppleWebKit/536.3 (KHTML, like Gecko) Chrome/19.0.1063.0 Safari/536.3|Mozilla/5.0 (Macintosh; Intel Mac OS X 10_8_2) AppleWebKit/537.4 (KHTML like Gecko) Chrome/22.0.1229.79 Safari/537.4|Mozilla/5.0 (Macintosh; U; Mac OS X Mach-O; en-US; rv:2.0a) Gecko/20040614 Firefox/3.0.0|Mozilla/5.0 (Macintosh; U; PPC Mac OS X 10.5; en-US; rv:1.9.0.3) Gecko/2008092414 Firefox/3.0.3|Mozilla/5.0 (Macintosh; U; Intel Mac OS X 10.5; en-US; rv:1.9.1) Gecko/20090624 Firefox/3.5|Mozilla/5.0 (Macintosh; U; Intel Mac OS X 10.6; en-US; rv:1.9.2.14) Gecko/20110218 AlexaToolbar/alxf-2.0 Firefox/3.6.14|Mozilla/5.0 (Macintosh; U; PPC Mac OS X 10.5; en-US; rv:********) Gecko/20110303 Firefox/3.6.15|Mozilla/5.0 (Macintosh; Intel Mac OS X 10.6; rv:2.0.1) Gecko/20100101 Firefox/4.0.1|Mozilla/5.0 (Macintosh; Intel Mac OS X 10.6; rv:5.0) Gecko/20100101 Firefox/5.0|Mozilla/5.0 (Macintosh; Intel Mac OS X 10.6; rv:9.0) Gecko/20100101 Firefox/9.0|Mozilla/5.0 (Macintosh; Intel Mac OS X 10_7_2; rv:10.0.1) Gecko/20100101 Firefox/10.0.1|Mozilla/5.0 (Macintosh; Intel Mac OS X 10.8; rv:16.0) Gecko/20120813 Firefox/16.0|Mozilla/4.0 (compatible; MSIE 5.15; Mac_PowerPC)|Mozilla/5.0 (Macintosh; U; PPC Mac OS X; en-US) AppleWebKit/125.4 (KHTML, like Gecko, Safari) OmniWeb/v563.15|Opera/9.0 (Macintosh; PPC Mac OS X; U; en)|Mozilla/5.0 (Macintosh; U; PPC Mac OS X; en) AppleWebKit/125.2 (KHTML, like Gecko) Safari/85.8|Mozilla/5.0 (Macintosh; U; PPC Mac OS X; en) AppleWebKit/125.2 (KHTML, like Gecko) Safari/125.8|Mozilla/5.0 (Macintosh; U; PPC Mac OS X; fr-fr) AppleWebKit/312.5 (KHTML, like Gecko) Safari/312.3|Mozilla/5.0 (Macintosh; U; PPC Mac OS X; en) AppleWebKit/418.8 (KHTML, like Gecko) Safari/419.3|Mozilla/5.0 (Macintosh; Intel Mac OS X 10.6; rv:2.0.1) Gecko/20100101 Firefox/4.0.1 Camino/2.2.1|Mozilla/5.0 (Macintosh; Intel Mac OS X 10.6; rv:2.0b6pre) Gecko/20100907 Firefox/4.0b6pre Camino/2.2a1pre|Mozilla/5.0 (Macintosh; Intel Mac OS X 10_8_4) AppleWebKit/537.31 (KHTML like Gecko) Chrome/26.0.1410.63 Safari/537.31|Mozilla/5.0 (Macintosh; Intel Mac OS X 1083) AppleWebKit/537.36 (KHTML like Gecko) Chrome/28.0.1469.0 Safari/537.36|Mozilla/5.0 (Macintosh; Intel Mac OS X 10_9_0) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/32.0.1664.3 Safari/537.36|Mozilla/5.0 (Macintosh; Intel Mac OS X 10.7; rv:20.0) Gecko/20100101 Firefox/20.0|Mozilla/5.0 (Macintosh; Intel Mac OS X 10.6; rv:25.0) Gecko/20100101 Firefox/25.0|iTunes/4.2 (Macintosh; U; PPC Mac OS X 10.2)|iTunes/9.0.3 (Macintosh; U; Intel Mac OS X 10_6_2; en-ca)|Mozilla/5.0 (Macintosh; U; Intel Mac OS X; en-US) AppleWebKit/528.16 (KHTML, like Gecko, Safari/528.16) OmniWeb/v622.8.0.112941|Mozilla/5.0 (Macintosh; U; Intel Mac OS X 10_5_6; en-US) AppleWebKit/528.16 (KHTML, like Gecko, Safari/528.16) OmniWeb/v622.8.0|Opera/9.20 (Macintosh; Intel Mac OS X; U; en)|Opera/9.64 (Macintosh; PPC Mac OS X; U; en) Presto/2.1.1|Opera/9.80 (Macintosh; Intel Mac OS X; U; en) Presto/2.6.30 Version/10.61|Opera/9.80 (Macintosh; Intel Mac OS X 10.4.11; U; en) Presto/2.7.62 Version/11.00|Opera/9.80 (Macintosh; Intel Mac OS X 10.6.8; U; fr) Presto/2.9.168 Version/11.52|Mozilla/5.0 (Macintosh; U; Intel Mac OS X 10_6_2; en-us) AppleWebKit/531.21.8 (KHTML, like Gecko) Version/4.0.4 Safari/531.21.10|Mozilla/5.0 (Macintosh; U; Intel Mac OS X 10_6_5; de-de) AppleWebKit/534.15  (KHTML, like Gecko) Version/5.0.3 Safari/533.19.4|Mozilla/5.0 (Macintosh; U; Intel Mac OS X 10_6_6; en-us) AppleWebKit/533.20.25 (KHTML, like Gecko) Version/5.0.4 Safari/533.20.27|Mozilla/5.0 (Macintosh; U; Intel Mac OS X 10_7; en-us) AppleWebKit/534.20.8 (KHTML, like Gecko) Version/5.1 Safari/534.20.8|Mozilla/5.0 (Macintosh; Intel Mac OS X 10_7_3) AppleWebKit/534.55.3 (KHTML, like Gecko) Version/5.1.3 Safari/534.53.10|Mozilla/5.0 (Macintosh; Intel Mac OS X 10_6_8) AppleWebKit/537.13+ (KHTML, like Gecko) Version/5.1.7 Safari/534.57.2|Mozilla/5.0 (Macintosh; Intel Mac OS X 10_7_5) AppleWebKit/536.26.17 (KHTML like Gecko) Version/6.0.2 Safari/536.26.17|Mozilla/5.0 (Macintosh; Intel Mac OS X 10.5; rv:10.0.1) Gecko/20100101 Firefox/10.0.1 SeaMonkey/2.7.1|Mozilla/5.0 (Macintosh; U; Intel Mac OS X 10_6_3; en-us; Silk/1.0.13.81_10003810) AppleWebKit/533.16 (KHTML, like Gecko) Version/5.0 Safari/533.16 Silk-Accelerated=true|ELinks (0.4pre5; Linux 2.6.10-ac7 i686; 80x33)|ELinks/0.9.3 (textmode; Linux 2.6.9-kanotix-8 i686; 127x41)|ELinks/0.12~pre5-4|Links/0.9.1 (Linux 2.4.24; i386;)|Links (2.1pre15; Linux 2.4.26 i686; 158x61)|Links (2.3pre1; Linux 2.6.38-8-generic x86_64; 170x48)|Lynx/2.8.5rel.1 libwww-FM/2.14 SSL-MM/1.4.1 GNUTLS/0.8.12|w3m/0.5.1|Mozilla/5.0 (X11; U; Linux i686; en-US) AppleWebKit/532.4 (KHTML, like Gecko) Chrome/4.0.237.0 Safari/532.4 Debian|Mozilla/5.0 (X11; U; Linux i686; en-US) AppleWebKit/532.8 (KHTML, like Gecko) Chrome/4.0.277.0 Safari/532.8|Mozilla/5.0 (X11; U; Linux x86_64; en-US) AppleWebKit/532.9 (KHTML, like Gecko) Chrome/5.0.309.0 Safari/532.9|Mozilla/5.0 (X11; U; Linux x86_64; en-US) AppleWebKit/534.7 (KHTML, like Gecko) Chrome/7.0.514.0 Safari/534.7|Mozilla/5.0 (X11; U; Linux x86_64; en-US) AppleWebKit/540.0 (KHTML, like Gecko) Ubuntu/10.10 Chrome/9.1.0.0 Safari/540.0|Mozilla/5.0 (X11; U; Linux x86_64; en-US) AppleWebKit/534.15 (KHTML, like Gecko) Chrome/10.0.613.0 Safari/534.15|Mozilla/5.0 (X11; U; Linux i686; en-US) AppleWebKit/534.15 (KHTML, like Gecko) Ubuntu/10.10 Chromium/10.0.613.0 Chrome/10.0.613.0 Safari/534.15|Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/534.24 (KHTML, like Gecko) Ubuntu/10.10 Chromium/12.0.703.0 Chrome/12.0.703.0 Safari/534.24|Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/535.1 (KHTML, like Gecko) Chrome/13.0.782.20 Safari/535.1|Mozilla/5.0 Slackware/13.37 (X11; U; Linux x86_64; en-US) AppleWebKit/535.1 (KHTML, like Gecko) Chrome/13.0.782.41|Mozilla/5.0 (X11; Linux i686) AppleWebKit/535.1 (KHTML, like Gecko) Ubuntu/11.04 Chromium/14.0.825.0 Chrome/14.0.825.0 Safari/535.1|Mozilla/5.0 (X11; Linux i686) AppleWebKit/535.2 (KHTML, like Gecko) Ubuntu/11.10 Chromium/15.0.874.120 Chrome/15.0.874.120 Safari/535.2|Mozilla/5.0 (X11; U; Linux; i686; en-US; rv:1.6) Gecko Epiphany/1.2.5|Mozilla/5.0 (X11; U; Linux i586; en-US; rv:1.7.3) Gecko/20040924 Epiphany/1.4.4 (Ubuntu)|Mozilla/5.0 (X11; U; Linux i686; en-US; rv:1.6) Gecko/20040614 Firefox/0.8|Mozilla/5.0 (X11; U; Linux x86_64; sv-SE; rv:********) Gecko/20080207 Ubuntu/7.10 (gutsy) Firefox/********|Mozilla/5.0 (X11; U; Linux i686; en-US; rv:********) Gecko/2009060309 Ubuntu/9.10 (karmic) Firefox/3.0.11|Mozilla/5.0 (X11; U; Linux i686; en-US; rv:*******) Gecko/20090803 Ubuntu/9.04 (jaunty) Shiretoko/3.5.2|Mozilla/5.0 (X11; U; Linux x86_64; en-US; rv:*******) Gecko/20091107 Firefox/3.5.5|Mozilla/5.0 (X11; U; Linux x86_64; en-US; rv:*******) Gecko/20091020 Linux Mint/8 (Helena) Firefox/3.5.3|Mozilla/5.0 (X11; U; Linux x86_64; en-US; rv:*******) Gecko/20100915 Gentoo Firefox/3.6.9|Mozilla/5.0 (X11; U; Linux i686; pl-PL; rv:*******) Gecko/20121223 Ubuntu/9.25 (jaunty) Firefox/3.8|Mozilla/5.0 (X11; Linux i686; rv:2.0b6pre) Gecko/20100907 Firefox/4.0b6pre|Mozilla/5.0 (X11; Linux i686 on x86_64; rv:2.0.1) Gecko/20100101 Firefox/4.0.1|Mozilla/5.0 (X11; Linux i686; rv:2.0.1) Gecko/20100101 Firefox/4.0.1|Mozilla/5.0 (X11; Linux x86_64; rv:2.0.1) Gecko/20100101 Firefox/4.0.1|Mozilla/5.0 (X11; Linux x86_64; rv:2.2a1pre) Gecko/20100101 Firefox/4.2a1pre|Mozilla/5.0 (X11; Linux i686; rv:5.0) Gecko/20100101 Firefox/5.0|Mozilla/5.0 (X11; Linux i686; rv:6.0) Gecko/20100101 Firefox/6.0|Mozilla/5.0 (X11; Linux x86_64; rv:7.0a1) Gecko/20110623 Firefox/7.0a1|Mozilla/5.0 (X11; Linux i686; rv:8.0) Gecko/20100101 Firefox/8.0|Mozilla/5.0 (X11; Linux x86_64; rv:10.0.1) Gecko/20100101 Firefox/10.0.1|Mozilla/5.0 (X11; U; Linux i686; en-US; rv:1.9.1.16) Gecko/20120421 Gecko Firefox/11.0|Mozilla/5.0 (X11; Linux i686; rv:12.0) Gecko/20100101 Firefox/12.0|Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:14.0) Gecko/20100101 Firefox/14.0.1|Mozilla/5.0 (X11; U; Linux; i686; en-US; rv:1.6) Gecko Galeon/1.3.14|Mozilla/5.0 (X11; U; Linux ppc; en-US; rv:1.8.1.13) Gecko/20080313 Iceape/1.1.9 (Debian-1.1.9-5)|Mozilla/5.0 (X11; U; Linux i686; pt-PT; rv:1.9.2.3) Gecko/20100402 Iceweasel/3.6.3 (like Firefox/3.6.3) GTB7.0|Mozilla/5.0 (X11; Linux x86_64; rv:5.0) Gecko/20100101 Firefox/5.0 Iceweasel/5.0|Mozilla/5.0 (X11; Linux i686; rv:6.0a2) Gecko/20110615 Firefox/6.0a2 Iceweasel/6.0a2|Konqueror/3.0-rc4; (Konqueror/3.0-rc4; i686 Linux;;datecode)|Mozilla/5.0 (compatible; Konqueror/3.3; Linux 2.6.8-gentoo-r3; X11;|Mozilla/5.0 (compatible; Konqueror/3.5; Linux 2.6.30-7.dmz.1-liquorix-686; X11) KHTML/3.5.10 (like Gecko) (Debian package 4:3.5.10.dfsg.1-1 b1)|Mozilla/5.0 (compatible; Konqueror/3.5; Linux; en_US) KHTML/3.5.6 (like Gecko) (Kubuntu)|Mozilla/5.0 (X11; Linux x86_64; en-US; rv:2.0b2pre) Gecko/20100712 Minefield/4.0b2pre|Mozilla/5.0 (X11; U; Linux; i686; en-US; rv:1.6) Gecko Debian/1.6-7|MSIE (MSIE 6.0; X11; Linux; i686) Opera 7.23|Mozilla/5.0 (X11; U; Linux x86_64; en-US; rv:1.9.1.17) Gecko/20110123 SeaMonkey/2.0.12|Mozilla/5.0 (X11; U; Linux i686; en-US; rv:1.8.1) Gecko/20061024 Firefox/2.0 (Swiftfox)|Mozilla/5.0 (X11; U; Linux; en-US) AppleWebKit/527  (KHTML, like Gecko, Safari/419.3) Arora/0.10.1|Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/536.5 (KHTML, like Gecko) Chrome/19.0.1084.9 Safari/536.5|Mozilla/5.0 (X11; CrOS i686 2268.111.0) AppleWebKit/536.11 (KHTML, like Gecko) Chrome/20.0.1132.57 Safari/536.11|Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.4 (KHTML like Gecko) Chrome/22.0.1229.56 Safari/537.4|Mozilla/5.0 (X11; Linux i686) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/28.0.1478.0 Safari/537.36|Mozilla/5.0 (X11; Linux i686) AppleWebKit/537.22 (KHTML like Gecko) Ubuntu Chromium/25.0.1364.160 Chrome/25.0.1364.160 Safari/537.22|Mozilla/4.0 (compatible; Dillo 3.0)|Mozilla/5.0 (X11; U; Linux i686; en-us) AppleWebKit/528.5  (KHTML, like Gecko, Safari/528.5 ) lt-GtkLauncher|Mozilla/5.0 (X11; Linux i686; rv:16.0) Gecko/20100101 Firefox/16.0|Mozilla/5.0 (X11; U; Linux i686; rv:19.0) Gecko/20100101 Slackware/13 Firefox/19.0|Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:20.0) Gecko/20100101 Firefox/20.0|Mozilla/5.0 (X11; Linux i686; rv:20.0) Gecko/20100101 Firefox/20.0|Mozilla/5.0 (X11; Linux i686; rv:25.0) Gecko/20100101 Firefox/25.0|Mozilla/5.0 (X11; Linux i686; rv:28.0) Gecko/20100101 Firefox/28.0|Mozilla/5.0 (X11; U; Linux i686; en-US; rv:1.9.0.8) Gecko Galeon/2.0.6 (Ubuntu 2.0.6-2)|Mozilla/5.0 (X11; U; Linux i686; en-US; rv:1.8.1.16) Gecko/20080716 (Gentoo) Galeon/2.0.6|Mozilla/5.0 (X11; U; Linux x86_64; en-US; rv:1.9.1.13) Gecko/20100916 Iceape/2.0.8|Mozilla/5.0 (X11; Linux i686; rv:14.0) Gecko/20100101 Firefox/14.0.1 Iceweasel/14.0.1|Mozilla/5.0 (X11; Linux x86_64; rv:15.0) Gecko/20120724 Debian Iceweasel/15.02|Mozilla/5.0 (X11; Linux x86_64; rv:19.0) Gecko/20100101 Firefox/19.0 Iceweasel/19.0.2|Mozilla/5.0 (compatible; Konqueror/4.2; Linux) KHTML/4.2.4 (like Gecko) Slackware/13.0|Mozilla/5.0 (compatible; Konqueror/4.3; Linux) KHTML/4.3.1 (like Gecko) Fedora/4.3.1-3.fc11|Mozilla/5.0 (compatible; Konqueror/4.4; Linux) KHTML/4.4.1 (like Gecko) Fedora/4.4.1-1.fc12|Mozilla/5.0 (compatible; Konqueror/4.4; Linux 2.6.32-22-generic; X11; en_US) KHTML/4.4.3 (like Gecko) Kubuntu|Mozilla/5.0 (X11; Linux 3.8-6.dmz.1-liquorix-686) KHTML/4.8.4 (like Gecko) Konqueror/4.8|Mozilla/5.0 (X11; Linux) KHTML/4.9.1 (like Gecko) Konqueror/4.9|Midori/0.1.10 (X11; Linux i686; U; en-us) WebKit/(531).(2)|Mozilla/5.0 (X11; U; Linux x86_64; en-US; rv:1.9.0.3) Gecko/2008092814 (Debian-3.0.1-1)|Mozilla/5.0 (X11; U; Linux i686; en-US; rv:1.9a3pre) Gecko/20070330|Opera/9.64 (X11; Linux i686; U; Linux Mint; nb) Presto/2.1.1|Opera/9.80 (X11; Linux i686; U; en) Presto/2.2.15 Version/10.10|Opera/9.80 (X11; Linux x86_64; U; pl) Presto/2.7.62 Version/11.00|Opera/9.80 (X11; Linux i686) Presto/2.12.388 Version/12.16|Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/33.0.1750.166 Safari/537.36 OPR/20.0.1396.73172|Mozilla/5.0 (X11; Linux i686) AppleWebKit/534.34 (KHTML, like Gecko) QupZilla/1.2.0 Safari/534.34|Mozilla/5.0 (X11; Linux i686; rv:10.0.1) Gecko/20100101 Firefox/10.0.1 SeaMonkey/2.7.1|Mozilla/5.0 (X11; Linux i686; rv:12.0) Gecko/20120502 Firefox/12.0 SeaMonkey/2.9.1|Mozilla/5.0 (X11; U; Linux x86_64; us; rv:1.9.1.19) Gecko/20110430 shadowfox/7.0 (like Firefox/7.0|Mozilla/5.0 (X11; U; Linux i686; it; rv:1.9.2.3) Gecko/20100406 Firefox/3.6.3 (Swiftfox)|Uzbl (Webkit 1.3) (Linux i686 [i686])|ELinks (0.4.3; NetBSD 3.0.2PATCH sparc64; 141x19)|Links (2.1pre15; FreeBSD 5.3-RELEASE i386; 196x84)|Lynx/2.8.7dev.4 libwww-FM/2.14 SSL-MM/1.4.1 OpenSSL/0.9.8d|Mozilla/5.0 (X11; U; FreeBSD i386; en-US) AppleWebKit/532.0 (KHTML, like Gecko) Chrome/4.0.207.0 Safari/532.0|Mozilla/5.0 (X11; U; OpenBSD i386; en-US) AppleWebKit/533.3 (KHTML, like Gecko) Chrome/5.0.359.0 Safari/533.3|Mozilla/5.0 (X11; U; FreeBSD x86_64; en-US) AppleWebKit/534.16 (KHTML, like Gecko) Chrome/10.0.648.204 Safari/534.16|Mozilla/5.0 (X11; U; SunOS sun4m; en-US; rv:1.4b) Gecko/20030517 Mozilla Firebird/0.6|Mozilla/5.0 (X11; U; FreeBSD i386; en-US; rv:1.6) Gecko/20040406 Galeon/1.3.15|Mozilla/5.0 (compatible; Konqueror/3.5; NetBSD 4.0_RC3; X11) KHTML/3.5.7 (like Gecko)|Mozilla/5.0 (compatible; Konqueror/3.5; SunOS) KHTML/3.5.1 (like Gecko)|Mozilla/5.0 (X11; U; FreeBSD; i386; en-US; rv:1.7) Gecko|Mozilla/4.77 [en] (X11; I; IRIX;64 6.5 IP30)|Mozilla/4.8 [en] (X11; U; SunOS; 5.7 sun4u)|Mozilla/5.0 (Unknown; U; UNIX BSD/SYSV system; C -) AppleWebKit/527  (KHTML, like Gecko, Safari/419.3) Arora/0.10.2|Mozilla/5.0 (X11; FreeBSD amd64) AppleWebKit/536.5 (KHTML like Gecko) Chrome/19.0.1084.56 Safari/536.5|Mozilla/5.0 (X11; FreeBSD amd64) AppleWebKit/537.4 (KHTML like Gecko) Chrome/22.0.1229.79 Safari/537.4|Mozilla/5.0 (X11; U; OpenBSD arm; en-us) AppleWebKit/531.2  (KHTML, like Gecko) Safari/531.2  Epiphany/2.30.0|Mozilla/5.0 (X11; U; FreeBSD amd64; en-us) AppleWebKit/531.2  (KHTML, like Gecko) Safari/531.2  Epiphany/2.30.0|Mozilla/5.0 (X11; U; SunOS i86pc; en-US; rv:1.9.1b3) Gecko/20090429 Firefox/3.1b3|Mozilla/5.0 (X11; U; OpenBSD i386; en-US; rv:1.9.1) Gecko/20090702 Firefox/3.5|Mozilla/5.0 (X11; U; FreeBSD i386; de-CH; rv:*******) Gecko/20100729 Firefox/3.6.8|Mozilla/5.0 (X11; FreeBSD amd64; rv:5.0) Gecko/20100101 Firefox/5.0|Mozilla/5.0 (compatible; Konqueror/4.1; DragonFly) KHTML/4.1.4 (like Gecko)|Mozilla/5.0 (compatible; Konqueror/4.1; OpenBSD) KHTML/4.1.4 (like Gecko)|Mozilla/5.0 (compatible; Konqueror/4.5; NetBSD 5.0.2; X11; amd64; en_US) KHTML/4.5.4 (like Gecko)|Mozilla/5.0 (compatible; Konqueror/4.5; FreeBSD) KHTML/4.5.4 (like Gecko)|Mozilla/5.0 (X11; U; NetBSD amd64; en-US; rv:********) Gecko/20110308 Namoroka/3.6.15|NetSurf/1.2 (NetBSD; amd64)|Opera/9.80 (X11; FreeBSD 8.1-RELEASE i386; Edition Next) Presto/2.12.388 Version/12.10|Mozilla/5.0 (X11; U; SunOS i86pc; en-US; rv:********) Gecko/20080303 SeaMonkey/1.1.8|Mozilla/5.0 (Windows; U; Windows NT 5.1; en-US; BOLT/2.800) AppleWebKit/534.6 (KHTML, like Gecko) Version/5.0 Safari/534.6.3|Mozilla/4.0 (compatible; MSIE 6.0; Windows CE; IEMobile 6.12; Microsoft ZuneHD 4.3)|Mozilla/4.0 (compatible; MSIE 6.0; Windows CE; IEMobile 7.11)|Mozilla/4.0 (compatible; MSIE 7.0; Windows Phone OS 7.0; Trident/3.1; IEMobile/7.0) Asus;Galaxy6|Mozilla/5.0 (compatible; MSIE 9.0; Windows Phone OS 7.5; Trident/5.0; IEMobile/9.0)|Mozilla/5.0 (compatible; MSIE 10.0; Windows Phone 8.0; Trident/6.0; IEMobile/10.0; ARM; Touch)|Mozilla/1.22 (compatible; MSIE 5.01; PalmOS 3.0) EudoraWeb 2.1|Mozilla/5.0 (WindowsCE 6.0; rv:2.0.1) Gecko/20100101 Firefox/4.0.1|Mozilla/5.0 (X11; U; Linux armv61; en-US; rv:1.9.1b2pre) Gecko/20081015 Fennec/1.0a1|Mozilla/5.0 (Maemo; Linux armv7l; rv:2.0.1) Gecko/20100101 Firefox/4.0.1 Fennec/2.0.1|Mozilla/5.0 (Maemo; Linux armv7l; rv:10.0.1) Gecko/20100101 Firefox/10.0.1 Fennec/10.0.1|Mozilla/5.0 (Windows; U; Windows CE 5.1; rv:1.8.1a3) Gecko/20060610 Minimo/0.016|Mozilla/5.0 (X11; U; Linux armv6l; rv 1.8.1.5pre) Gecko/20070619 Minimo/0.020|Mozilla/5.0 (X11; U; Linux arm7tdmi; rv:1.8.1.11) Gecko/20071130 Minimo/0.025|Mozilla/4.0 (PDA; PalmOS/sony/model prmr/Revision:1.1.54 (en)) NetFront/3.0|Opera/9.51 Beta (Microsoft Windows; PPC; Opera Mobi/1718; U; en)|Opera/9.60 (J2ME/MIDP; Opera Mini/4.1.11320/608; U; en) Presto/2.2.0|Opera/9.60 (J2ME/MIDP; Opera Mini/4.2.14320/554; U; cs) Presto/2.2.0|Opera/9.80 (S60; SymbOS; Opera Mobi/499; U; ru) Presto/2.4.18 Version/10.00|Opera/10.61 (J2ME/MIDP; Opera Mini/5.1.21219/19.999; en-US; rv:1.9.3a5) WebKit/534.5 Presto/2.6.30|Opera/9.80 (Android; Opera Mini/7.5.33361/31.1543; U; en) Presto/2.8.119 Version/11.1010|POLARIS/6.01 (BREW 3.1.5; U; en-us; LG; LX265; POLARIS/6.01/WAP) MMP/2.0 profile/MIDP-2.1 Configuration/CLDC-1.1|Mozilla/5.0 (X11; U; Linux x86_64; en-gb) AppleWebKit/534.35 (KHTML, like Gecko) Chrome/11.0.696.65 Safari/534.35 Puffin/2.9174AP|Mozilla/5.0 (X11; U; Linux x86_64; en-us) AppleWebKit/534.35 (KHTML, like Gecko) Chrome/11.0.696.65 Safari/534.35 Puffin/2.9174AT|Mozilla/5.0 (iPod; U; CPU iPhone OS 6_1 like Mac OS X; en-HK) AppleWebKit/534.35 (KHTML, like Gecko) Chrome/11.0.696.65 Safari/534.35 Puffin/3.9174IP Mobile|Mozilla/5.0 (X11; U; Linux x86_64; en-AU) AppleWebKit/534.35 (KHTML, like Gecko) Chrome/11.0.696.65 Safari/534.35 Puffin/3.9174IT|Mozilla/5.0 (X11; U; Linux i686; en-gb) AppleWebKit/534.35 (KHTML, like Gecko) Chrome/11.0.696.65 Safari/534.35 Puffin/2.0.5603M|Mozilla/5.0 (Linux; U; Android 2.0; en-us; Droid Build/ESD20) AppleWebKit/530.17 (KHTML, like Gecko) Version/4.0 Mobile Safari/530.17|Mozilla/5.0 (iPad; U; CPU OS 4_2_1 like Mac OS X; ja-jp) AppleWebKit/533.17.9 (KHTML, like Gecko) Version/5.0.2 Mobile/8C148 Safari/6533.18.5|Mozilla/5.0 (iPhone; U; CPU iPhone OS 4_2_1 like Mac OS X; da-dk) AppleWebKit/533.17.9 (KHTML, like Gecko) Version/5.0.2 Mobile/8C148 Safari/6533.18.5|Mozilla/5.0 (iPad; CPU OS 6_0 like Mac OS X) AppleWebKit/536.26 (KHTML, like Gecko) Version/6.0 Mobile/10A5355d Safari/8536.25|Mozilla/5.0 (compatible; MSIE 9.0; Windows NT 6.1; Trident/5.0; XBLWP7; ZuneWP7) UCBrowser/2.9.0.263|Mozilla/5.0 (Linux; U; Android 2.3.3; en-us ; LS670 Build/GRI40) AppleWebKit/533.1 (KHTML, like Gecko) Version/4.0 Mobile Safari/533.1/UCBrowser/8.6.1.262/145/355|Mozilla/5.0 (Linux; U; Android 3.0.1; fr-fr; A500 Build/HRI66) AppleWebKit/534.13 (KHTML, like Gecko) Version/4.0 Safari/534.13|Mozilla/5.0 (Linux; U; Android 4.1; en-us; sdk Build/MR1) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.1 Safari/534.30|Mozilla/5.0 (Linux; U; Android 4.2; en-us; sdk Build/MR1) AppleWebKit/535.19 (KHTML, like Gecko) Version/4.2 Safari/535.19|Mozilla/5.0 (iPad; U; CPU OS 4_3 like Mac OS X; en-us) AppleWebKit/533.17.9 (KHTML, like Gecko) Version/5.0.2 Mobile/8F190 Safari/6533.18.5|Mozilla/5.0 (Macintosh; U; Intel Mac OS X 10_5_7;en-us) AppleWebKit/530.17 (KHTML, like Gecko) Version/4.0 Safari/530.17|Mozilla/5.0 (hp-tablet; Linux; hpwOS/3.0.2; U; de-DE) AppleWebKit/534.6 (KHTML, like Gecko) wOSBrowser/234.40.1 Safari/534.6 TouchPad/1.0|Mozilla/5.0 (Linux; U; Android 4.0.3; en-us; KFTT Build/IML74K) AppleWebKit/535.19 (KHTML, like Gecko) Silk/2.1 Mobile Safari/535.19 Silk-Accelerated=true|Mozilla/5.0 (Linux; U; Android 3.0; en-us; Xoom Build/HRI39) AppleWebKit/525.10  (KHTML, like Gecko) Version/3.0.4 Mobile Safari/523.12.2|Mozilla/5.0 (PlayBook; U; RIM Tablet OS 2.1.0; en-US) AppleWebKit/536.2+ (KHTML like Gecko) Version/******* Safari/536.2+|Mozilla/5.0 (Linux; U; Android 1.5; de-de; Galaxy Build/CUPCAKE) AppleWebKit/528.5  (KHTML, like Gecko) Version/3.1.2 Mobile Safari/525.20.1|Mozilla/5.0 (Linux; U; Android 2.2; en-ca; GT-P1000M Build/FROYO) AppleWebKit/533.1 (KHTML, like Gecko) Version/4.0 Mobile Safari/533.1|Mozilla/5.0 (Linux; U; Android 2.2; en-us; SCH-I800 Build/FROYO) AppleWebKit/533.1 (KHTML, like Gecko) Version/4.0 Mobile Safari/533.1|Mozilla/5.0 (Linux; U; Android 3.0.1; en-us; GT-P7100 Build/HRI83) AppleWebkit/534.13 (KHTML, like Gecko) Version/4.0 Safari/534.13|Mozilla/4.0 (compatible; Linux 2.6.22) NetFront/3.4 Kindle/2.0 (screen 600x800)|Mozilla/5.0 (Linux U; en-US)  AppleWebKit/528.5  (KHTML, like Gecko, Safari/528.5 ) Version/4.0 Kindle/3.0 (screen 600x800; rotate)|Mozilla/5.0 (iPad; U; CPU iPad OS 5_0_1 like Mac OS X; en-us) AppleWebKit/535.1+ (KHTML like Gecko) Version/******* Safari/6533.18.5|Mozilla/5.0 (iPad; CPU OS 7_0 like Mac OS X) AppleWebKit/537.51.1 (KHTML, like Gecko) CriOS/30.0.1599.12 Mobile/11A465 Safari/8536.25 (3B92C18B-D9DE-4CB7-A02A-22FD2AF17C8F)|Mozilla/5.0 (iPhone; U; CPU like Mac OS X; en) AppleWebKit/420  (KHTML, like Gecko) Version/3.0 Mobile/1A543a Safari/419.3|Mozilla/5.0 (iPhone; U; CPU iPhone OS 2_0 like Mac OS X; en-us) AppleWebKit/525.18.1 (KHTML, like Gecko) Version/3.1.1 Mobile/5A347 Safari/525.200|Mozilla/5.0 (iPhone; U; CPU iPhone OS 3_0 like Mac OS X; en-us) AppleWebKit/528.18 (KHTML, like Gecko) Version/4.0 Mobile/7A341 Safari/528.16|Mozilla/5.0 (iPhone; U; CPU iPhone OS 4_0 like Mac OS X; en-us) AppleWebKit/532.9 (KHTML, like Gecko) Version/4.0.5 Mobile/8A293 Safari/531.22.7|Mozilla/5.0 (iPhone; U; CPU iPhone OS 5_1_1 like Mac OS X; da-dk) AppleWebKit/534.46.0 (KHTML, like Gecko) CriOS/19.0.1084.60 Mobile/9B206 Safari/7534.48.3|UCWEB/8.8 (iPhone; CPU OS_6; en-US)AppleWebKit/534.1 U3/3.0.0 Mobile|Mozilla/5.0 (iPod; U; CPU iPhone OS 2_2_1 like Mac OS X; en-us) AppleWebKit/525.18.1 (KHTML, like Gecko) Version/3.1.1 Mobile/5H11a Safari/525.20|Mozilla/5.0 (iPod; U; CPU iPhone OS 3_1_1 like Mac OS X; en-us) AppleWebKit/528.18 (KHTML, like Gecko) Mobile/7C145|nook browser/1.0|Mozilla/5.0 (Linux; U; Android 2.3.4; en-us; BNTV250 Build/GINGERBREAD) AppleWebKit/533.1 (KHTML, like Gecko) Version/4.0 Safari/533.1|BlackBerry7100i/4.1.0 Profile/MIDP-2.0 Configuration/CLDC-1.1 VendorID/103|BlackBerry8300/4.2.2 Profile/MIDP-2.0 Configuration/CLDC-1.1 VendorID/107 UP.Link/********.0|BlackBerry8320/4.2.2 Profile/MIDP-2.0 Configuration/CLDC-1.1 VendorID/100|BlackBerry8330/4.3.0 Profile/MIDP-2.0 Configuration/CLDC-1.1 VendorID/105|BlackBerry9000/********* Profile/MIDP-2.0 Configuration/CLDC-1.1 VendorID/102|BlackBerry9530/********* Profile/MIDP-2.0 Configuration/CLDC-1.1 VendorID/102 UP.Link/********.0|BlackBerry9700/5.0.0.351 Profile/MIDP-2.1 Configuration/CLDC-1.1 VendorID/123|Mozilla/5.0 (BlackBerry; U; BlackBerry 9800; en) AppleWebKit/534.1  (KHTML, Like Gecko) Version/********* Mobile Safari/534.1|Mozilla/5.0 (BB10; Touch) AppleWebKit/537.10+ (KHTML, like Gecko) Version/10.1.0.2342 Mobile Safari/537.10+|Mozilla/5.0 (Linux; U; Android 1.5; en-us; sdk Build/CUPCAKE) AppleWebkit/528.5  (KHTML, like Gecko) Version/3.1.2 Mobile Safari/525.20.1|Mozilla/5.0 (Linux; U; Android 2.1; en-us; Nexus One Build/ERD62) AppleWebKit/530.17 (KHTML, like Gecko) Version/4.0 Mobile Safari/530.17|Mozilla/5.0 (Linux; U; Android 2.2; en-us; Nexus One Build/FRF91) AppleWebKit/533.1 (KHTML, like Gecko) Version/4.0 Mobile Safari/533.1|Mozilla/5.0 (Linux; Android 4.4; Nexus 5 Build/BuildID) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/30.0.0.0 Mobile Safari/537.36|Mozilla/5.0 (Linux; webOS/2.2.4; U; en-US) AppleWebKit/534.6 (KHTML, like Gecko) webOSBrowser/221.56 Safari/534.6 Pre/3.0|Mozilla/4.0 (compatible; MSIE 6.0; Windows CE; IEMobile 7.11) Sprint:PPC6800|Mozilla/4.0 (compatible; MSIE 6.0; Windows CE; IEMobile 7.11) XV6800|Mozilla/5.0 (Linux; U; Android 1.5; en-us; htc_bahamas Build/CRB17) AppleWebKit/528.5  (KHTML, like Gecko) Version/3.1.2 Mobile Safari/525.20.1|Mozilla/5.0 (Linux; U; Android 2.1-update1; de-de; HTC Desire 1.19.161.5 Build/ERE27) AppleWebKit/530.17 (KHTML, like Gecko) Version/4.0 Mobile Safari/530.17|HTC_Dream Mozilla/5.0 (Linux; U; Android 1.5; en-ca; Build/CUPCAKE) AppleWebKit/528.5  (KHTML, like Gecko) Version/3.1.2 Mobile Safari/525.20.1|Mozilla/5.0 (Linux; U; Android 2.2; en-us; Sprint APA9292KT Build/FRF91) AppleWebKit/533.1 (KHTML, like Gecko) Version/4.0 Mobile Safari/533.1|Mozilla/5.0 (Linux; U; Android 1.5; de-ch; HTC Hero Build/CUPCAKE) AppleWebKit/528.5  (KHTML, like Gecko) Version/3.1.2 Mobile Safari/525.20.1|Mozilla/5.0 (Linux; U; Android 2.2; en-us; ADR6300 Build/FRF91) AppleWebKit/533.1 (KHTML, like Gecko) Version/4.0 Mobile Safari/533.1|Mozilla/5.0 (Linux; U; Android 2.1; en-us; HTC Legend Build/cupcake) AppleWebKit/530.17 (KHTML, like Gecko) Version/4.0 Mobile Safari/530.17|Mozilla/5.0 (Linux; U; Android 1.5; de-de; HTC Magic Build/PLAT-RC33) AppleWebKit/528.5  (KHTML, like Gecko) Version/3.1.2 Mobile Safari/525.20.1 FirePHP/0.3|Mozilla/5.0 (Linux; U; Android 4.0.3; de-ch; HTC Sensation Build/IML74K) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Mobile Safari/534.30|HTC-ST7377/1.59.502.3 (67150) Opera/9.50 (Windows NT 5.1; U; en) UP.Link/6.3.1.17.0|Mozilla/5.0 (Linux; U; Android 1.6; en-us; HTC_TATTOO_A3288 Build/DRC79) AppleWebKit/528.5  (KHTML, like Gecko) Version/3.1.2 Mobile Safari/525.20.1|LG-LX550 AU-MIC-LX550/2.0 MMP/2.0 Profile/MIDP-2.0 Configuration/CLDC-1.1|POLARIS/6.01(BREW 3.1.5;U;en-us;LG;LX265;POLARIS/6.01/WAP;)MMP/2.0 profile/MIDP-201 Configuration /CLDC-1.1|LG-GC900/V10a Obigo/WAP2.0 Profile/MIDP-2.1 Configuration/CLDC-1.1|Mozilla/4.0 (compatible; MSIE 4.01; Windows CE; PPC; MDA Pro/1.0 Profile/MIDP-2.0 Configuration/CLDC-1.1)|Mozilla/5.0 (Linux; U; Android 1.0; en-us; dream) AppleWebKit/525.10  (KHTML, like Gecko) Version/3.0.4 Mobile Safari/523.12.2|Mozilla/5.0 (Linux; U; Android 1.5; en-us; T-Mobile G1 Build/CRB43) AppleWebKit/528.5  (KHTML, like Gecko) Version/3.1.2 Mobile Safari 525.20.1|Mozilla/5.0 (Linux; U; Android 1.5; en-gb; T-Mobile_G2_Touch Build/CUPCAKE) AppleWebKit/528.5  (KHTML, like Gecko) Version/3.1.2 Mobile Safari/525.20.1|Mozilla/5.0 (Linux; U; Android 2.2; en-us; Droid Build/FRG22D) AppleWebKit/533.1 (KHTML, like Gecko) Version/4.0 Mobile Safari/533.1|MOT-L7v/08.B7.5DR MIB/2.2.1 Profile/MIDP-2.0 Configuration/CLDC-1.1 UP.Link/6.3.0.0.0|Mozilla/5.0 (Linux; U; Android 2.0; en-us; Milestone Build/ SHOLS_U2_01.03.1) AppleWebKit/530.17 (KHTML, like Gecko) Version/4.0 Mobile Safari/530.17|Mozilla/5.0 (Linux; U; Android 2.0.1; de-de; Milestone Build/SHOLS_U2_01.14.0) AppleWebKit/530.17 (KHTML, like Gecko) Version/4.0 Mobile Safari/530.17|MOT-V9mm/00.62 UP.Browser/6.2.3.4.c.1.123 (GUI) MMP/2.0|MOTORIZR-Z8/46.00.00 Mozilla/4.0 (compatible; MSIE 6.0; Symbian OS; 356) Opera 8.65 [it] UP.Link/6.3.0.0.0|MOT-V177/0.1.75 UP.Browser/6.2.3.9.c.12 (GUI) MMP/2.0 UP.Link/6.3.1.13.0|portalmmm/2.0 N410i(c20;TB)|Nokia3230/2.0 (5.0614.0) SymbianOS/7.0s Series60/2.1 Profile/MIDP-2.0 Configuration/CLDC-1.0|Mozilla/5.0 (SymbianOS/9.2; U; Series60/3.1 Nokia5700/3.27; Profile/MIDP-2.0 Configuration/CLDC-1.1) AppleWebKit/413 (KHTML, like Gecko) Safari/413|Mozilla/5.0 (SymbianOS/9.2; U; Series60/3.1 Nokia6120c/3.70; Profile/MIDP-2.0 Configuration/CLDC-1.1) AppleWebKit/413 (KHTML, like Gecko) Safari/413|Nokia6230/2.0 (04.44) Profile/MIDP-2.0 Configuration/CLDC-1.1|Nokia6230i/2.0 (03.80) Profile/MIDP-2.0 Configuration/CLDC-1.1|Mozilla/4.1 (compatible; MSIE 5.0; Symbian OS; Nokia 6600;452) Opera 6.20 [en-US]|Nokia6630/1.0 (2.39.15) SymbianOS/8.0 Series60/2.6 Profile/MIDP-2.0 Configuration/CLDC-1.1|Nokia7250/1.0 (3.14) Profile/MIDP-1.0 Configuration/CLDC-1.0|Mozilla/4.0 (compatible; MSIE 5.0; Series80/2.0 Nokia9500/4.51 Profile/MIDP-2.0 Configuration/CLDC-1.1)|Mozilla/5.0 (Symbian/3; Series60/5.2 NokiaC6-01/011.010; Profile/MIDP-2.1 Configuration/CLDC-1.1 ) AppleWebKit/525 (KHTML, like Gecko) Version/3.0 BrowserNG/7.2.7.2 3gpp-gba|Mozilla/5.0 (Symbian/3; Series60/5.2 NokiaC7-00/012.003; Profile/MIDP-2.1 Configuration/CLDC-1.1 ) AppleWebKit/525 (KHTML, like Gecko) Version/3.0 BrowserNG/7.2.7.3 3gpp-gba|Mozilla/5.0 (SymbianOS/9.1; U; en-us) AppleWebKit/413 (KHTML, like Gecko) Safari/413 es50|Mozilla/5.0 (Symbian/3; Series60/5.2 NokiaE6-00/021.002; Profile/MIDP-2.1 Configuration/CLDC-1.1) AppleWebKit/533.4 (KHTML, like Gecko) NokiaBrowser/7.3.1.16 Mobile Safari/533.4 3gpp-gba|UCWEB/8.8 (SymbianOS/9.2; U; en-US; NokiaE63) AppleWebKit/534.1 UCBrowser/8.8.0.245 Mobile|Mozilla/5.0 (SymbianOS/9.1; U; en-us) AppleWebKit/413 (KHTML, like Gecko) Safari/413 es65|Mozilla/5.0 (Symbian/3; Series60/5.2 NokiaE7-00/010.016; Profile/MIDP-2.1 Configuration/CLDC-1.1 ) AppleWebKit/525 (KHTML, like Gecko) Version/3.0 BrowserNG/7.2.7.3 3gpp-gba|Mozilla/5.0 (SymbianOS/9.1; U; en-us) AppleWebKit/413 (KHTML, like Gecko) Safari/413 es70|Mozilla/5.0 (SymbianOS/9.2; U; Series60/3.1 NokiaE90-1/07.24.0.3; Profile/MIDP-2.0 Configuration/CLDC-1.1 ) AppleWebKit/413 (KHTML, like Gecko) Safari/413 UP.Link/6.2.3.18.0|NokiaN70-1/5.0609.2.0.1 Series60/2.8 Profile/MIDP-2.0 Configuration/CLDC-1.1 UP.Link/6.3.1.13.0|Mozilla/5.0 (SymbianOS/9.1; U; en-us) AppleWebKit/413 (KHTML, like Gecko) Safari/413|NokiaN73-1/3.0649.0.0.1 Series60/3.0 Profile/MIDP2.0 Configuration/CLDC-1.1|Mozilla/5.0 (Symbian/3; Series60/5.2 NokiaN8-00/014.002; Profile/MIDP-2.1 Configuration/CLDC-1.1; en-us) AppleWebKit/525 (KHTML, like Gecko) Version/3.0 BrowserNG/7.2.6.4 3gpp-gba|Mozilla/5.0 (SymbianOS/9.1; U; de) AppleWebKit/413 (KHTML, like Gecko) Safari/413|Mozilla/5.0 (SymbianOS/9.2; U; Series60/3.1 NokiaN95/10.0.018; Profile/MIDP-2.0 Configuration/CLDC-1.1) AppleWebKit/413 (KHTML, like Gecko) Safari/413 UP.Link/6.3.0.0.0|Mozilla/5.0 (MeeGo; NokiaN950-00/00) AppleWebKit/534.13 (KHTML, like Gecko) NokiaBrowser/8.5.0 Mobile Safari/534.13|Mozilla/5.0 (SymbianOS/9.4; Series60/5.0 NokiaN97-1/10.0.012; Profile/MIDP-2.1 Configuration/CLDC-1.1; en-us) AppleWebKit/525 (KHTML, like Gecko) WicKed/7.1.12344|Mozilla/5.0 (Symbian/3; Series60/5.2 NokiaX7-00/021.004; Profile/MIDP-2.1 Configuration/CLDC-1.1 ) AppleWebKit/533.4 (KHTML, like Gecko) NokiaBrowser/7.3.1.21 Mobile Safari/533.4 3gpp-gba|Mozilla/5.0 (webOS/1.3; U; en-US) AppleWebKit/525.27.1 (KHTML, like Gecko) Version/1.0 Safari/525.27.1 Desktop/1.0|Mozilla/4.0 (compatible; MSIE 6.0; Windows 98; PalmSource/hspr-H102; Blazer/4.0) 16;320x320|SEC-SGHE900/1.0 NetFront/3.2 Profile/MIDP-2.0 Configuration/CLDC-1.1 Opera/8.01 (J2ME/MIDP; Opera Mini/2.0.4509/1378; nl; U; ssr)|Mozilla/5.0 (Linux; U; Android 4.0.3; de-de; Galaxy S II Build/GRJ22) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Mobile Safari/534.30|User agent: Mozilla/5.0 (Linux; Android 4.3; SPH-L710 Build/JSS15J) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/32.0.1700.99 Mobile Safari/537.36|SAMSUNG-S8000/S8000XXIF3 SHP/VPP/R5 Jasmine/1.0 Nextreaming SMM-MMS/1.2.0 profile/MIDP-2.1 configuration/CLDC-1.1 FirePHP/0.3|Mozilla/5.0 (Linux; U; Android 1.5; en-us; SPH-M900 Build/CUPCAKE) AppleWebKit/528.5  (KHTML, like Gecko) Version/3.1.2 Mobile Safari/525.20.1|SAMSUNG-SGH-A867/A867UCHJ3 SHP/VPP/R5 NetFront/35 SMM-MMS/1.2.0 profile/MIDP-2.0 configuration/CLDC-1.1 UP.Link/6.3.0.0.0|SEC-SGHX210/1.0 UP.Link/6.3.1.13.0|Mozilla/5.0 (Linux; U; Android 1.5; fr-fr; GT-I5700 Build/CUPCAKE) AppleWebKit/528.5  (KHTML, like Gecko) Version/3.1.2 Mobile Safari/525.20.1|SEC-SGHX820/1.0 NetFront/3.2 Profile/MIDP-2.0 Configuration/CLDC-1.1|SonyEricssonK310iv/R4DA Browser/NetFront/3.3 Profile/MIDP-2.0 Configuration/CLDC-1.1 UP.Link/6.3.1.13.0|SonyEricssonK550i/R1JD Browser/NetFront/3.3 Profile/MIDP-2.0 Configuration/CLDC-1.1|SonyEricssonK610i/R1CB Browser/NetFront/3.3 Profile/MIDP-2.0 Configuration/CLDC-1.1|SonyEricssonK750i/R1CA Browser/SEMC-Browser/4.2 Profile/MIDP-2.0 Configuration/CLDC-1.1|Opera/9.80 (J2ME/MIDP; Opera Mini/5.0.16823/1428; U; en) Presto/2.2.0|SonyEricssonK800i/R1CB Browser/NetFront/3.3 Profile/MIDP-2.0 Configuration/CLDC-1.1 UP.Link/6.3.0.0.0|SonyEricssonK810i/R1KG Browser/NetFront/3.3 Profile/MIDP-2.0 Configuration/CLDC-1.1|Opera/8.01 (J2ME/MIDP; Opera Mini/1.0.1479/HiFi; SonyEricsson P900; no; U; ssr)|SonyEricssonS500i/R6BC Browser/NetFront/3.3 Profile/MIDP-2.0 Configuration/CLDC-1.1|Mozilla/5.0 (SymbianOS/9.4; U; Series60/5.0 SonyEricssonP100/01; Profile/MIDP-2.1 Configuration/CLDC-1.1) AppleWebKit/525 (KHTML, like Gecko) Version/3.0 Safari/525|SonyEricssonT68/R201A|SonyEricssonT100/R101|SonyEricssonT610/R201 Profile/MIDP-1.0 Configuration/CLDC-1.0|SonyEricssonT650i/R7AA Browser/NetFront/3.3 Profile/MIDP-2.0 Configuration/CLDC-1.1|SonyEricssonW580i/R6BC Browser/NetFront/3.3 Profile/MIDP-2.0 Configuration/CLDC-1.1|SonyEricssonW660i/R6AD Browser/NetFront/3.3 Profile/MIDP-2.0 Configuration/CLDC-1.1|SonyEricssonW810i/R4EA Browser/NetFront/3.3 Profile/MIDP-2.0 Configuration/CLDC-1.1 UP.Link/6.3.0.0.0|SonyEricssonW850i/R1ED Browser/NetFront/3.3 Profile/MIDP-2.0 Configuration/CLDC-1.1|SonyEricssonW950i/R100 Mozilla/4.0 (compatible; MSIE 6.0; Symbian OS; 323) Opera 8.60 [en-US]|SonyEricssonW995/R1EA Profile/MIDP-2.1 Configuration/CLDC-1.1 UNTRUSTED/1.0|Mozilla/5.0 (Linux; U; Android 1.6; es-es; SonyEricssonX10i Build/R1FA016) AppleWebKit/528.5  (KHTML, like Gecko) Version/3.1.2 Mobile Safari/525.20.1|Mozilla/5.0 (Linux; U; Android 1.6; en-us; SonyEricssonX10i Build/R1AA056) AppleWebKit/528.5  (KHTML, like Gecko) Version/3.1.2 Mobile Safari/525.20.1|Opera/9.5 (Microsoft Windows; PPC; Opera Mobi; U) SonyEricssonX1i/R2AA Profile/MIDP-2.0 Configuration/CLDC-1.1|SonyEricssonZ800/R1Y Browser/SEMC-Browser/4.1 Profile/MIDP-2.0 Configuration/CLDC-1.1 UP.Link/6.3.0.0.0|Mozilla/5.0 (Linux; U; Android 0.5; en-us) AppleWebKit/522  (KHTML, like Gecko) Safari/419.3|Mozilla/5.0 (Linux; U; Android 1.1; en-gb; dream) AppleWebKit/525.10  (KHTML, like Gecko) Version/3.0.4 Mobile Safari/523.12.2|Mozilla/5.0 (Android; Linux armv7l; rv:2.0.1) Gecko/20100101 Firefox/4.0.1 Fennec/2.0.1|Opera/9.80 (Android 4.0.4; Linux; Opera Mobi/ADR-1205181138; U; pl) Presto/2.10.254 Version/12.00|Mozilla/5.0 (Android; Linux armv7l; rv:10.0.1) Gecko/20100101 Firefox/10.0.1 Fennec/10.0.1|Mozilla/5.0 (Linux; Android 4.1.2; SHV-E250S Build/JZO54K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/30.0.1599.82 Mobile Safari/537.36|Mozilla/5.0 (Android 4.2; rv:19.0) Gecko/20121129 Firefox/19.0|Mozilla/5.0 (Linux; U; Android 4.3; en-us; sdk Build/MR1) AppleWebKit/536.23 (KHTML, like Gecko) Version/4.3 Mobile Safari/536.23|Mozilla/5.0 (iPhone; U; CPU iPhone OS 4_3 like Mac OS X; de-de) AppleWebKit/533.17.9 (KHTML, like Gecko) Mobile/8F190|Mozilla/5.0 (X11; Linux i686 on x86_64; rv:2.0.1) Gecko/20100101 Firefox/4.0.1 Fennec/2.0.1|Mozilla/5.0 (SymbianOS 9.4; Series60/5.0 NokiaN97-1/10.0.012; Profile/MIDP-2.1 Configuration/CLDC-1.1; en-us) AppleWebKit/525 (KHTML, like Gecko) WicKed/7.1.12344|Mozilla/4.0 (compatible; MSIE 6.0; Windows CE; IEMobile 8.12; MSIEMobile6.0)|Mozilla/4.0 (compatible; MSIE 7.0; Windows Phone OS 7.0; Trident/3.1; IEMobile/7.0)|DoCoMo/2.0 SH901iC(c100;TB;W24H12)|Mozilla/5.0 (X11; U; Linux i686; en-US; rv:*******) Gecko/20060909 Firefox/******* MG(Novarra-Vision/6.9)|Mozilla/5.0 (Windows NT 6.1; WOW64; rv:34.0) Gecko/20100101 Firefox/34.0".Split(new string[] { "|" }, StringSplitOptions.RemoveEmptyEntries));
                }
                return CommonString.lstAgent;
            }
            set { CommonString.lstAgent = value; }
        }

        public const string StrMobileTicketAgent = "Mozilla/5.0 (iPhone; CPU iPhone OS 7_0_4 like Mac OS X) AppleWebKit/537.51.1 (KHTML, like Gecko) Mobile/11B554a (407546549)/Worklight/6.0.0";
        public const string StrComTicketAgent = "Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/52.0.2743.116 Safari/537.36";
        public const string StrCommonAgent = "Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/52.0.2743.116 Safari/537.36";
        //{
        //    get
        //    {
        //        return "Mozilla/5.0 (Windows NT 6.3; WOW64; Trident/7.0; Touch; rv:11.0)";
        //        //return "Mozilla/4.0 (compatible; MSIE 8.0; Windows NT 5.1; Trident/4.0;)";
        //    }
        //}

        public static int NMaxRequestTimeOut = 30;

        //public static int NMaxQueryTimeOut = 3;

        public static int NMaxLogTimeOut = 1;

        public static int NYuPiaoQueryTime = 1;

        public static int NMaxSubTimeOut = 5;

        private static int nMaxQueryTimeOut;
        public static int NMaxQueryTimeOut
        {
            get
            {
                nMaxQueryTimeOut = nMaxQueryTimeOut < 1 ? 3 : nMaxQueryTimeOut;
                nMaxQueryTimeOut = nMaxQueryTimeOut > 10 ? 3 : nMaxQueryTimeOut;
                return nMaxQueryTimeOut;
            }
            set
            {
                value = value < 1 ? 3 : value;
                value = value > 10 ? 3 : value;
                if (nMaxQueryTimeOut == value)
                    return;
                nMaxQueryTimeOut = value;
                Settings.Default.NMaxQueryTimeOut = nMaxQueryTimeOut;
                SaveConfig();
            }
        }

        private static int nMaxQueryThread = -1;

        public static int NMaxQueryThread
        {
            get
            {
                if (nMaxQueryThread <= 0)
                {
                    nMaxQueryThread = Settings.Default.NMaxQueryThread;
                    if (nMaxQueryThread <= 0)
                    {
                        if (CommonString.IsSingleTask)
                        {
                            if (Environment.ProcessorCount <= 2)
                                nMaxQueryThread = 1;
                            else if (Environment.ProcessorCount <= 8)
                                nMaxQueryThread = 2;
                            else
                                nMaxQueryThread = 3;
                        }
                        else
                        {
                            if (Environment.ProcessorCount <= 2)
                                nMaxQueryThread = 1;
                            else if (Environment.ProcessorCount >= 16)
                                nMaxQueryThread = 3;
                            else
                                nMaxQueryThread = 2;
                        }
                        Settings.Default.NMaxQueryThread = nMaxQueryThread;
                    }
                }
                return CommonString.nMaxQueryThread;
            }
            set
            {
                if (value > 0 && value < 4)
                {
                    Settings.Default.NMaxQueryThread = value;
                    nMaxQueryThread = value;
                }
            }
        }

        public static List<AutoCodeEntity> LstAutoCode { get; set; }

        //private static bool isAutoTongCheng;
        //public static bool IsAutoTongCheng
        //{
        //    get
        //    {
        //        return isAutoTongCheng;
        //    }
        //    set
        //    {
        //        isAutoTongCheng = value;
        //        Settings.Default.IsAutoTongCheng = isAutoTongCheng;
        //        SaveConfig();
        //    }
        //}

        #endregion

        public static bool IsRemovePassengerIfSuccess = true;

        #region 12306 JS
        public static string strScript
        {
            get
            {
                return @"function bin216(s) {
        	            var i, l, o = '', n;
        	            s += '';
        	            b = '';
        	            for (i = 0, l = s.length; i < l; i++) {
        		            b = s.charCodeAt(i);
        		            n = b.toString(16);
        		            o += n.length < 2 ? '0' + n : n;
        	            }
        	            return o;
                    };
                    var Base32 = new function() {
        	            var delta = 0x9E3779B8;
        	            function longArrayToString(data, includeLength) {
        		            var length = data.length;
        		            var n = (length - 1) << 2;
        		            if (includeLength) {
        			            var m = data[length - 1];
        			            if ((m < n - 3) || (m > n))
        				            return null;
        			            n = m;
        		            }
        		            for ( var i = 0; i < length; i++) {
        			            data[i] = String.fromCharCode(data[i] & 0xff, data[i] >>> 8 & 0xff,
        					            data[i] >>> 16 & 0xff, data[i] >>> 24 & 0xff);
        		            }
        		            if (includeLength) {
        			            return data.join('').substring(0, n);
        		            } else {
        			            return data.join('');
        		            }
        	            };
        	            function stringToLongArray(string, includeLength) {
        		            var length = string.length;
        		            var result = [];
        		            for ( var i = 0; i < length; i += 4) {
        			            result[i >> 2] = string.charCodeAt(i)
        					            | string.charCodeAt(i + 1) << 8
        					            | string.charCodeAt(i + 2) << 16
        					            | string.charCodeAt(i + 3) << 24;
        		            }
        		            if (includeLength) {
        			            result[result.length] = length;
        		            }
        		            return result;
        	            }
        	            ;
        	            this.encrypt = function(string, key) {
        		            if (string == '') {
        			            return '';
        		            }
        		            var v = stringToLongArray(string, true);
        		            var k = stringToLongArray(key, false);
        		            if (k.length < 4) {
        			            k.length = 4;
        		            }
        		            var n = v.length - 1;
        		            var z = v[n], y = v[0];
        		            var mx, e, p, q = Math.floor(6 + 52 / (n + 1)), sum = 0;
        		            while (0 < q--) {
        			            sum = sum + delta & 0xffffffff;
        			            e = sum >>> 2 & 3;
        			            for (p = 0; p < n; p++) {
        				            y = v[p + 1];
        				            mx = (z >>> 5 ^ y << 2) + (y >>> 3 ^ z << 4) ^ (sum ^ y)
        						            + (k[p & 3 ^ e] ^ z);
        				            z = v[p] = v[p] + mx & 0xffffffff;
        			            }
        			            y = v[0];
        			            mx = (z >>> 5 ^ y << 2) + (y >>> 3 ^ z << 4) ^ (sum ^ y)
        					            + (k[p & 3 ^ e] ^ z);
        			            z = v[n] = v[n] + mx & 0xffffffff;
        		            }
        		            return longArrayToString(v, false);
        	            };
                    };
                    var keyStr = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=';
                    function encode32(input) {
        	            input = escape(input);
        	            var output = '';
        	            var chr1, chr2, chr3 = '';
        	            var enc1, enc2, enc3, enc4 = '';
        	            var i = 0;
        	            do {
        		            chr1 = input.charCodeAt(i++);
        		            chr2 = input.charCodeAt(i++);
        		            chr3 = input.charCodeAt(i++);
        		            enc1 = chr1 >> 2;
        		            enc2 = ((chr1 & 3) << 4) | (chr2 >> 4);
        		            enc3 = ((chr2 & 15) << 2) | (chr3 >> 6);
        		            enc4 = chr3 & 63;
        		            if (isNaN(chr2)) {
        			            enc3 = enc4 = 64;
        		            } else if (isNaN(chr3)) {
        			            enc4 = 64;
        		            }
        		            output = output + keyStr.charAt(enc1) + keyStr.charAt(enc2)
        				            + keyStr.charAt(enc3) + keyStr.charAt(enc4);
        		            chr1 = chr2 = chr3 = '';
        		            enc1 = enc2 = enc3 = enc4 = '';
        	            } while (i < input.length);
        	            return output;
                    };
                    function loginEncode(var1, var2) {
        	            return encode32(bin216(Base32.encrypt(var1, var2)));
                    }";
            }
        }
        #endregion

        public static bool IsNoLog { get; set; }

        public static bool IsSubByServer { get; set; }

        public static bool IsWebAutoCodeConfig { get; set; }

        public static bool IsLocalAutoCodeConfig { get; set; }

        public static bool IsGetCookieFromServer { get; set; }

        public static string StrCookieUrl { get; set; }

        private static string strDeviceCookie;

        public static string StrDeviceCookie
        {
            get
            {
                if (string.IsNullOrEmpty(strDeviceCookie))
                {
                    strDeviceCookie = "RAIL_DEVICEID=RBfSln1hj2MaREZn1zv7PeWNmuzecPoCz89JmeNGUPVcoW120bvKRbD06S2wwvntFCxHDKQ2aX4OzcIJR_gHRzBPEi81yQYLBMQFM3Xddgx4ig2alAwI2F8cHpGtySRgyZx_Z2OnWIfR2Mw_Ceg8cYqiEzTapi1P;RAIL_EXPIRATION=1516564912067;";
                }
                return CommonString.strDeviceCookie;
            }
            set { CommonString.strDeviceCookie = value; }
        }

        public static bool IsLoadStations = false;

        public static string StrOldStations = "@bjb|北京北|VAP|beijingbei|bjb|0@bjd|北京东|BOP|beijingdong|bjd|1@bji|北京|BJP|beijing|bj|2@bjn|北京南|VNP|beijingnan|bjn|3@bjx|北京西|BXP|beijingxi|bjx|4@gzn|广州南|IZQ|guangzhounan|gzn|5@cqb|重庆北|CUW|chongqingbei|cqb|6@cqi|重庆|CQW|chongqing|cq|7@cqn|重庆南|CRW|chongqingnan|cqn|8@gzd|广州东|GGQ|guangzhoudong|gzd|9@sha|上海|SHH|shanghai|sh|10@shn|上海南|SNH|shanghainan|shn|11@shq|上海虹桥|AOH|shanghaihongqiao|shhq|12@shx|上海西|SXH|shanghaixi|shx|13@tjb|天津北|TBP|tianjinbei|tjb|14@tji|天津|TJP|tianjin|tj|15@tjn|天津南|TIP|tianjinnan|tjn|16@tjx|天津西|TXP|tianjinxi|tjx|17@cch|长春|CCT|changchun|cc|18@ccn|长春南|CET|changchunnan|ccn|19@ccx|长春西|CRT|changchunxi|ccx|20@cdd|成都东|ICW|chengdudong|cdd|21@cdn|成都南|CNW|chengdunan|cdn|22@cdu|成都|CDW|chengdu|cd|23@csh|长沙|CSQ|changsha|cs|24@csn|长沙南|CWQ|changshanan|csn|25@fzh|福州|FZS|fuzhou|fz|26@fzn|福州南|FYS|fuzhounan|fzn|27@gya|贵阳|GIW|guiyang|gy|28@gzh|广州|GZQ|guangzhou|gz|29@gzx|广州西|GXQ|guangzhouxi|gzx|30@heb|哈尔滨|HBB|haerbin|heb|31@hed|哈尔滨东|VBB|haerbindong|hebd|32@hex|哈尔滨西|VAB|haerbinxi|hebx|33@hfe|合肥|HFH|hefei|hf|34@hfx|合肥西|HTH|hefeixi|hfx|35@hhd|呼和浩特东|NDC|huhehaotedong|hhhtd|36@hht|呼和浩特|HHC|huhehaote|hhht|37@hkd|海  口东|KEQ|haikoudong|hkd|38@hkd|海口东|HMQ|haikoudong|hkd|39@hko|海口|VUQ|haikou|hk|40@hzd|杭州东|HGH|hangzhoudong|hzd|41@hzh|杭州|HZH|hangzhou|hz|42@hzn|杭州南|XHH|hangzhounan|hzn|43@jna|济南|JNK|jinan|jn|44@jnd|济南东|JAK|jinandong|jnd|45@jnx|济南西|JGK|jinanxi|jnx|46@kmi|昆明|KMM|kunming|km|47@kmx|昆明西|KXM|kunmingxi|kmx|48@lsa|拉萨|LSO|lasa|ls|49@lzd|兰州东|LVJ|lanzhoudong|lzd|50@lzh|兰州|LZJ|lanzhou|lz|51@lzx|兰州西|LAJ|lanzhouxi|lzx|52@nch|南昌|NCG|nanchang|nc|53@nji|南京|NJH|nanjing|nj|54@njn|南京南|NKH|nanjingnan|njn|55@nni|南宁|NNZ|nanning|nn|56@sjb|石家庄北|VVP|shijiazhuangbei|sjzb|57@sjz|石家庄|SJP|shijiazhuang|sjz|58@sya|沈阳|SYT|shenyang|sy|59@syb|沈阳北|SBT|shenyangbei|syb|60@syd|沈阳东|SDT|shenyangdong|syd|61@tyb|太原北|TBV|taiyuanbei|tyb|62@tyd|太原东|TDV|taiyuandong|tyd|63@tyu|太原|TYV|taiyuan|ty|64@wha|武汉|WHN|wuhan|wh|65@wjx|王家营西|KNM|wangjiayingxi|wjyx|66@wln|乌鲁木齐南|WMR|wulumuqinan|wlmqn|67@xab|西安北|EAY|xianbei|xab|68@xan|西安|XAY|xian|xa|69@xan|西安南|CAY|xiannan|xan|70@xni|西宁|XNO|xining|xn|71@ych|银川|YIJ|yinchuan|yc|72@zzh|郑州|ZZF|zhengzhou|zz|73@aes|阿尔山|ART|aershan|aes|74@aka|安康|AKY|ankang|ak|75@aks|阿克苏|ASR|akesu|aks|76@alh|阿里河|AHX|alihe|alh|77@alk|阿拉山口|AKR|alashankou|alsk|78@api|安平|APT|anping|ap|79@aqi|安庆|AQH|anqing|aq|80@ash|安顺|ASW|anshun|as|81@ash|鞍山|AST|anshan|as|82@aya|安阳|AYF|anyang|ay|83@ban|北安|BAB|beian|ba|84@bbu|蚌埠|BBH|bengbu|bb|85@bch|白城|BCT|baicheng|bc|86@bha|北海|BHZ|beihai|bh|87@bhe|白河|BEL|baihe|bh|88@bji|白涧|BAP|baijian|bj|89@bji|宝鸡|BJY|baoji|bj|90@bji|滨江|BJB|binjiang|bj|91@bkt|博克图|BKX|boketu|bkt|92@bse|百色|BIZ|baise|bs|93@bss|白山市|HJL|baishanshi|bss|94@bta|北台|BTT|beitai|bt|95@btd|包头东|BDC|baotoudong|btd|96@bto|包头|BTC|baotou|bt|97@bts|北屯市|BXR|beitunshi|bts|98@bxi|本溪|BXT|benxi|bx|99@byb|白云鄂博|BEC|baiyunebo|byeb|100@byx|白银西|BXJ|baiyinxi|byx|101@bzh|亳州|BZH|bozhou|bz|102@cbi|赤壁|CBN|chibi|cb|103@cde|常德|VGQ|changde|cd|104@cde|承德|CDP|chengde|cd|105@cdi|长甸|CDT|changdian|cd|106@cfe|赤峰|CFD|chifeng|cf|107@cli|茶陵|CDG|chaling|cl|108@cna|苍南|CEH|cangnan|cn|109@cpi|昌平|CPP|changping|cp|110@cre|崇仁|CRG|chongren|cr|111@ctu|昌图|CTT|changtu|ct|112@ctz|长汀镇|CDB|changtingzhen|ctz|113@cxi|曹县|CXK|caoxian|cx|114@cxn|楚雄南|COM|chuxiongnan|cxn|115@cxt|陈相屯|CXT|chenxiangtun|cxt|116@czb|长治北|CBF|changzhibei|czb|117@czh|池州|IYH|chizhou|cz|118@czh|长征|CZJ|changzheng|cz|119@czh|常州|CZH|changzhou|cz|120@czh|郴州|CZQ|chenzhou|cz|121@czh|长治|CZF|changzhi|cz|122@czh|沧州|COP|cangzhou|cz|123@czu|崇左|CZZ|chongzuo|cz|124@dab|大安北|RNT|daanbei|dab|125@dch|大成|DCT|dacheng|dc|126@ddo|丹东|DUT|dandong|dd|127@dfh|东方红|DFB|dongfanghong|dfh|128@dgd|东莞东|DMQ|dongguandong|dgd|129@dhs|大虎山|DHD|dahushan|dhs|130@dhu|敦煌|DHJ|dunhuang|dh|131@dhu|敦化|DHL|dunhua|dh|132@dhu|德惠|DHT|dehui|dh|133@djc|东京城|DJB|dongjingcheng|djc|134@dji|大涧|DFP|dajian|dj|135@djy|都江堰|DDW|dujiangyan|djy|136@dlb|大连北|DFT|dalianbei|dlb|137@dli|大理|DKM|dali|dl|138@dli|大连|DLT|dalian|dl|139@dna|定南|DNG|dingnan|dn|140@dqi|大庆|DZX|daqing|dq|141@dsh|东胜|DOC|dongsheng|ds|142@dsq|大石桥|DQT|dashiqiao|dsq|143@dto|大同|DTV|datong|dt|144@dyi|东营|DPK|dongying|dy|145@dys|大杨树|DUX|dayangshu|dys|146@dyu|都匀|RYW|duyun|dy|147@dzh|邓州|DOF|dengzhou|dz|148@dzh|达州|RXW|dazhou|dz|149@dzh|德州|DZP|dezhou|dz|150@ejn|额济纳|EJC|ejina|ejn|151@eli|二连|RLC|erlian|el|152@esh|恩施|ESN|enshi|es|153@fdi|福鼎|FES|fuding|fd|154@fhc|凤凰机场|FJQ|fenghuangjichang|fhjc|155@fld|风陵渡|FLV|fenglingdu|fld|156@fli|涪陵|FLW|fuling|fl|157@flj|富拉尔基|FRX|fulaerji|flej|158@fsb|抚顺北|FET|fushunbei|fsb|159@fsh|佛山|FSQ|foshan|fs|160@fxn|阜新南|FXD|fuxinnan|fxn|161@fya|阜阳|FYH|fuyang|fy|162@gem|格尔木|GRO|geermu|gem|163@gha|广汉|GHW|guanghan|gh|164@gji|古交|GJV|gujiao|gj|165@glb|桂林北|GBZ|guilinbei|glb|166@gli|古莲|GRX|gulian|gl|167@gli|桂林|GLZ|guilin|gl|168@gsh|固始|GXN|gushi|gs|169@gsh|广水|GSN|guangshui|gs|170@gta|干塘|GNJ|gantang|gt|171@gyu|广元|GYW|guangyuan|gy|172@gzb|广州北|GBQ|guangzhoubei|gzb|173@gzh|赣州|GZG|ganzhou|gz|174@gzl|公主岭|GLT|gongzhuling|gzl|175@gzn|公主岭南|GBT|gongzhulingnan|gzln|176@han|淮安|AUH|huaian|ha|177@hbe|淮北|HRH|huaibei|hb|178@hbe|鹤北|HMB|hebei|hb|179@hbi|淮滨|HVN|huaibin|hb|180@hbi|河边|HBV|hebian|hb|181@hch|潢川|KCN|huangchuan|hc|182@hch|韩城|HCY|hancheng|hc|183@hda|邯郸|HDP|handan|hd|184@hdz|横道河子|HDB|hengdaohezi|hdhz|185@hga|鹤岗|HGB|hegang|hg|186@hgt|皇姑屯|HTT|huanggutun|hgt|187@hgu|红果|HEM|hongguo|hg|188@hhe|黑河|HJB|heihe|hh|189@hhu|怀化|HHQ|huaihua|hh|190@hko|汉口|HKN|hankou|hk|191@hld|葫芦岛|HLD|huludao|hld|192@hle|海拉尔|HRX|hailaer|hle|193@hll|霍林郭勒|HWD|huolinguole|hlgl|194@hlu|海伦|HLB|hailun|hl|195@hma|侯马|HMV|houma|hm|196@hmi|哈密|HMR|hami|hm|197@hna|淮南|HAH|huainan|hn|198@hna|桦南|HNB|huanan|hn|199@hnx|海宁西|EUH|hainingxi|hnx|200@hqi|鹤庆|HQM|heqing|hq|201@hrb|怀柔北|HBP|huairoubei|hrb|202@hro|怀柔|HRP|huairou|hr|203@hsd|黄石东|OSN|huangshidong|hsd|204@hsh|华山|HSY|huashan|hs|205@hsh|黄山|HKH|huangshan|hs|206@hsh|黄石|HSN|huangshi|hs|207@hsh|衡水|HSP|hengshui|hs|208@hya|衡阳|HYQ|hengyang|hy|209@hze|菏泽|HIK|heze|hz|210@hzh|贺州|HXZ|hezhou|hz|211@hzh|汉中|HOY|hanzhong|hz|212@hzh|惠州|HCQ|huizhou|hz|213@jan|吉安|VAG|jian|ja|214@jan|集安|JAL|jian|ja|215@jbc|江边村|JBG|jiangbiancun|jbc|216@jch|晋城|JCF|jincheng|jc|217@jcj|金城江|JJZ|jinchengjiang|jcj|218@jdz|景德镇|JCG|jingdezhen|jdz|219@jfe|嘉峰|JFF|jiafeng|jf|220@jgq|加格达奇|JGX|jiagedaqi|jgdq|221@jgs|井冈山|JGG|jinggangshan|jgs|222@jhe|蛟河|JHL|jiaohe|jh|223@jhn|金华南|RNH|jinhuanan|jhn|224@jhu|金华|JBH|jinhua|jh|225@jji|九江|JJG|jiujiang|jj|226@jli|吉林|JLL|jilin|jl|227@jme|荆门|JMN|jingmen|jm|228@jms|佳木斯|JMB|jiamusi|jms|229@jni|济宁|JIK|jining|jn|230@jnn|集宁南|JAC|jiningnan|jnn|231@jqu|酒泉|JQJ|jiuquan|jq|232@jsh|江山|JUH|jiangshan|js|233@jsh|吉首|JIQ|jishou|js|234@jta|九台|JTL|jiutai|jt|235@jts|镜铁山|JVJ|jingtieshan|jts|236@jxi|鸡西|JXB|jixi|jx|237@jxx|绩溪县|JRH|jixixian|jxx|238@jyg|嘉峪关|JGJ|jiayuguan|jyg|239@jyo|江油|JFW|jiangyou|jy|240@jzh|锦州|JZD|jinzhou|jz|241@jzh|金州|JZT|jinzhou|jz|242@jzh|蓟州|JKP|jizhou|jz|243@kel|库尔勒|KLR|kuerle|kel|244@kfe|开封|KFF|kaifeng|kf|245@kla|岢岚|KLV|kelan|kl|246@kli|凯里|KLW|kaili|kl|247@ksh|喀什|KSR|kashi|ks|248@ksn|昆山南|KNH|kunshannan|ksn|249@ktu|奎屯|KTR|kuitun|kt|250@kyu|开原|KYT|kaiyuan|ky|251@lan|六安|UAH|luan|la|252@lba|灵宝|LBF|lingbao|lb|253@lcg|芦潮港|UCH|luchaogang|lcg|254@lch|隆昌|LCW|longchang|lc|255@lch|陆川|LKZ|luchuan|lc|256@lch|利川|LCN|lichuan|lc|257@lch|临川|LCG|linchuan|lc|258@lch|潞城|UTP|lucheng|lc|259@lda|鹿道|LDL|ludao|ld|260@ldi|娄底|LDQ|loudi|ld|261@lfe|临汾|LFV|linfen|lf|262@lgz|良各庄|LGP|lianggezhuang|lgz|263@lhe|临河|LHC|linhe|lh|264@lhe|漯河|LON|luohe|lh|265@lhu|绿化|LWJ|lvhua|lh|266@lhu|隆化|UHP|longhua|lh|267@lji|丽江|LHM|lijiang|lj|268@lji|临江|LQL|linjiang|lj|269@lji|龙井|LJL|longjing|lj|270@lli|吕梁|LHV|lvliang|ll|271@lli|醴陵|LLG|liling|ll|272@lln|柳林南|LKV|liulinnan|lln|273@lpi|滦平|UPP|luanping|lp|274@lps|六盘水|UMW|liupanshui|lps|275@lqi|灵丘|LVV|lingqiu|lq|276@lsh|旅顺|LST|lvshun|ls|277@lxi|兰溪|LWH|lanxi|lx|278@lxi|陇西|LXJ|longxi|lx|279@lxi|澧县|LEQ|lixian|lx|280@lxi|临西|UEP|linxi|lx|281@lya|龙岩|LYS|longyan|ly|282@lya|耒阳|LYQ|leiyang|ly|283@lya|洛阳|LYF|luoyang|ly|284@lyd|连云港东|UKH|lianyungangdong|lygd|285@lyd|洛阳东|LDF|luoyangdong|lyd|286@lyi|临沂|LVK|linyi|ly|287@lym|洛阳龙门|LLF|luoyanglongmen|lylm|288@lyu|柳园|DHR|liuyuan|ly|289@lyu|凌源|LYD|lingyuan|ly|290@lyu|辽源|LYL|liaoyuan|ly|291@lzh|立志|LZX|lizhi|lz|292@lzh|柳州|LZZ|liuzhou|lz|293@lzh|辽中|LZD|liaozhong|lz|294@mch|麻城|MCN|macheng|mc|295@mdh|免渡河|MDX|mianduhe|mdh|296@mdj|牡丹江|MDB|mudanjiang|mdj|297@meg|莫尔道嘎|MRX|moerdaoga|medg|298@mgu|明光|MGH|mingguang|mg|299@mgu|满归|MHX|mangui|mg|300@mhe|漠河|MVX|mohe|mh|301@mmi|茂名|MDQ|maoming|mm|302@mmx|茂名西|MMZ|maomingxi|mmx|303@msh|密山|MSB|mishan|ms|304@msj|马三家|MJT|masanjia|msj|305@mwe|麻尾|VAW|mawei|mw|306@mya|绵阳|MYW|mianyang|my|307@mzh|梅州|MOQ|meizhou|mz|308@mzl|满洲里|MLX|manzhouli|mzl|309@nbd|宁波东|NVH|ningbodong|nbd|310@nbo|宁波|NGH|ningbo|nb|311@nch|南岔|NCB|nancha|nc|312@nch|南充|NCW|nanchong|nc|313@nda|南丹|NDZ|nandan|nd|314@ndm|南大庙|NMP|nandamiao|ndm|315@nfe|南芬|NFT|nanfen|nf|316@nhe|讷河|NHX|nehe|nh|317@nji|嫩江|NGX|nenjiang|nj|318@nji|内江|NJW|neijiang|nj|319@npi|南平|NPS|nanping|np|320@nto|南通|NUH|nantong|nt|321@nya|南阳|NFF|nanyang|ny|322@nzs|碾子山|NZX|nianzishan|nzs|323@pds|平顶山|PEN|pingdingshan|pds|324@pji|盘锦|PVD|panjin|pj|325@pli|平凉|PIJ|pingliang|pl|326@pln|平凉南|POJ|pingliangnan|pln|327@pqu|平泉|PQP|pingquan|pq|328@psh|坪石|PSQ|pingshi|ps|329@pxi|萍乡|PXG|pingxiang|px|330@pxi|凭祥|PXZ|pingxiang|px|331@pxx|郫县西|PCW|pixianxi|pxx|332@pzh|攀枝花|PRW|panzhihua|pzh|333@qch|蕲春|QRN|qichun|qc|334@qcs|青城山|QSW|qingchengshan|qcs|335@qda|青岛|QDK|qingdao|qd|336@qhc|清河城|QYP|qinghecheng|qhc|337@qji|曲靖|QJM|qujing|qj|338@qji|黔江|QNW|qianjiang|qj|339@qjz|前进镇|QEB|qianjinzhen|qjz|340@qqe|齐齐哈尔|QHX|qiqihaer|qqhe|341@qth|七台河|QTB|qitaihe|qth|342@qxi|沁县|QVV|qinxian|qx|343@qzd|泉州东|QRS|quanzhoudong|qzd|344@qzh|泉州|QYS|quanzhou|qz|345@qzh|衢州|QEH|quzhou|qz|346@ran|融安|RAZ|rongan|ra|347@rjg|汝箕沟|RQJ|rujigou|rjg|348@rji|瑞金|RJG|ruijin|rj|349@rzh|日照|RZK|rizhao|rz|350@scp|双城堡|SCB|shuangchengpu|scp|351@sfh|绥芬河|SFB|suifenhe|sfh|352@sgd|韶关东|SGQ|shaoguandong|sgd|353@shg|山海关|SHD|shanhaiguan|shg|354@shu|绥化|SHB|suihua|sh|355@sjf|三间房|SFX|sanjianfang|sjf|356@sjt|苏家屯|SXT|sujiatun|sjt|357@sla|舒兰|SLL|shulan|sl|358@smi|三明|SMS|sanming|sm|359@smu|神木|OMY|shenmu|sm|360@smx|三门峡|SMF|sanmenxia|smx|361@sna|商南|ONY|shangnan|sn|362@sni|遂宁|NIW|suining|sn|363@spi|四平|SPT|siping|sp|364@sqi|商丘|SQF|shangqiu|sq|365@sra|上饶|SRG|shangrao|sr|366@ssh|韶山|SSQ|shaoshan|ss|367@sso|宿松|OAH|susong|ss|368@sto|汕头|OTQ|shantou|st|369@swu|邵武|SWS|shaowu|sw|370@sxi|涉县|OEP|shexian|sx|371@sya|三亚|SEQ|sanya|sy|372@sya|三  亚|JUQ|sanya|sya|373@sya|邵阳|SYQ|shaoyang|sy|374@sya|十堰|SNN|shiyan|sy|375@sys|双鸭山|SSB|shuangyashan|sys|376@syu|松原|VYT|songyuan|sy|377@szh|苏州|SZH|suzhou|sz|378@szh|深圳|SZQ|shenzhen|sz|379@szh|宿州|OXH|suzhou|sz|380@szh|随州|SZN|suizhou|sz|381@szh|朔州|SUV|shuozhou|sz|382@szx|深圳西|OSQ|shenzhenxi|szx|383@tba|塘豹|TBQ|tangbao|tb|384@teq|塔尔气|TVX|taerqi|teq|385@tgu|潼关|TGY|tongguan|tg|386@tgu|塘沽|TGP|tanggu|tg|387@the|塔河|TXX|tahe|th|388@thu|通化|THL|tonghua|th|389@tla|泰来|TLX|tailai|tl|390@tlf|吐鲁番|TFR|tulufan|tlf|391@tli|通辽|TLD|tongliao|tl|392@tli|铁岭|TLT|tieling|tl|393@tlz|陶赖昭|TPT|taolaizhao|tlz|394@tme|图们|TML|tumen|tm|395@tre|铜仁|RDQ|tongren|tr|396@tsb|唐山北|FUP|tangshanbei|tsb|397@tsf|田师府|TFT|tianshifu|tsf|398@tsh|泰山|TAK|taishan|ts|399@tsh|唐山|TSP|tangshan|ts|400@tsh|天水|TSJ|tianshui|ts|401@typ|通远堡|TYT|tongyuanpu|typ|402@tys|太阳升|TQT|taiyangsheng|tys|403@tzh|泰州|UTH|taizhou|tz|404@tzi|桐梓|TZW|tongzi|tz|405@tzx|通州西|TAP|tongzhouxi|tzx|406@wch|五常|WCB|wuchang|wc|407@wch|武昌|WCN|wuchang|wc|408@wfd|瓦房店|WDT|wafangdian|wfd|409@wha|威海|WKK|weihai|wh|410@whu|芜湖|WHH|wuhu|wh|411@whx|乌海西|WXC|wuhaixi|whx|412@wjt|吴家屯|WJT|wujiatun|wjt|413@wlo|武隆|WLW|wulong|wl|414@wlt|乌兰浩特|WWT|wulanhaote|wlht|415@wna|渭南|WNY|weinan|wn|416@wsh|威舍|WSM|weishe|ws|417@wts|歪头山|WIT|waitoushan|wts|418@wwe|武威|WUJ|wuwei|ww|419@wwn|武威南|WWJ|wuweinan|wwn|420@wxi|无锡|WXH|wuxi|wx|421@wxi|乌西|WXR|wuxi|wx|422@wyl|乌伊岭|WPB|wuyiling|wyl|423@wys|武夷山|WAS|wuyishan|wys|424@wyu|万源|WYY|wanyuan|wy|425@wzh|万州|WYW|wanzhou|wz|426@wzh|梧州|WZZ|wuzhou|wz|427@wzh|温州|RZH|wenzhou|wz|428@wzn|温州南|VRH|wenzhounan|wzn|429@xch|西昌|ECW|xichang|xc|430@xch|许昌|XCF|xuchang|xc|431@xcn|西昌南|ENW|xichangnan|xcn|432@xfa|香坊|XFB|xiangfang|xf|433@xga|轩岗|XGV|xuangang|xg|434@xgu|兴国|EUG|xingguo|xg|435@xha|宣汉|XHY|xuanhan|xh|436@xhu|新会|EFQ|xinhui|xh|437@xhu|新晃|XLQ|xinhuang|xh|438@xlt|锡林浩特|XTC|xilinhaote|xlht|439@xlx|兴隆县|EXP|xinglongxian|xlx|440@xmb|厦门北|XKS|xiamenbei|xmb|441@xme|厦门|XMS|xiamen|xm|442@xmq|厦门高崎|XBS|xiamengaoqi|xmgq|443@xsh|小市|XST|xiaoshi|xs|444@xsh|秀山|ETW|xiushan|xs|445@xta|向塘|XTG|xiangtang|xt|446@xwe|宣威|XWM|xuanwei|xw|447@xxi|新乡|XXF|xinxiang|xx|448@xya|信阳|XUN|xinyang|xy|449@xya|咸阳|XYY|xianyang|xy|450@xya|襄阳|XFN|xiangyang|xy|451@xyc|熊岳城|XYT|xiongyuecheng|xyc|452@xyi|新沂|VIH|xinyi|xy|453@xyi|兴义|XRZ|xingyi|xy|454@xyu|新余|XUG|xinyu|xy|455@xzh|徐州|XCH|xuzhou|xz|456@yan|延安|YWY|yanan|ya|457@ybi|宜宾|YBW|yibin|yb|458@ybn|亚布力南|YWB|yabulinan|ybln|459@ybs|叶柏寿|YBD|yebaishou|ybs|460@ycd|宜昌东|HAN|yichangdong|ycd|461@ych|永川|YCW|yongchuan|yc|462@ych|盐城|AFH|yancheng|yc|463@ych|宜昌|YCN|yichang|yc|464@ych|运城|YNV|yuncheng|yc|465@ych|伊春|YCB|yichun|yc|466@yci|榆次|YCV|yuci|yc|467@ycu|杨村|YBP|yangcun|yc|468@ycx|宜春西|YCG|yichunxi|ycx|469@yes|伊尔施|YET|yiershi|yes|470@yga|燕岗|YGW|yangang|yg|471@yji|永济|YIV|yongji|yj|472@yji|延吉|YJL|yanji|yj|473@yko|营口|YKT|yingkou|yk|474@yks|牙克石|YKX|yakeshi|yks|475@yli|阎良|YNY|yanliang|yl|476@yli|玉林|YLZ|yulin|yl|477@yli|榆林|ALY|yulin|yl|478@ylw|亚龙湾|TWQ|yalongwan|ylw|479@ymp|一面坡|YPB|yimianpo|ymp|480@yni|伊宁|YMR|yining|yn|481@ypg|阳平关|YAY|yangpingguan|ypg|482@ypi|玉屏|YZW|yuping|yp|483@ypi|原平|YPV|yuanping|yp|484@yqi|延庆|YNP|yanqing|yq|485@yqq|阳泉曲|YYV|yangquanqu|yqq|486@yqu|玉泉|YQB|yuquan|yq|487@yqu|阳泉|AQP|yangquan|yq|488@ysh|营山|NUW|yingshan|ys|489@ysh|玉山|YNG|yushan|ys|490@ysh|燕山|AOP|yanshan|ys|491@ysh|榆树|YRT|yushu|ys|492@yta|鹰潭|YTG|yingtan|yt|493@yta|烟台|YAK|yantai|yt|494@yth|伊图里河|YEX|yitulihe|ytlh|495@ytx|玉田县|ATP|yutianxian|ytx|496@ywu|义乌|YWH|yiwu|yw|497@yxi|阳新|YON|yangxin|yx|498@yxi|义县|YXD|yixian|yx|499@yya|益阳|AEQ|yiyang|yy|500@yya|岳阳|YYQ|yueyang|yy|501@yzh|崖州|YUQ|yazhou|yz|502@yzh|永州|AOQ|yongzhou|yz|503@yzh|扬州|YLH|yangzhou|yz|504@zbo|淄博|ZBK|zibo|zb|505@zcd|镇城底|ZDV|zhenchengdi|zcd|506@zgo|自贡|ZGW|zigong|zg|507@zha|珠海|ZHQ|zhuhai|zh|508@zhb|珠海北|ZIQ|zhuhaibei|zhb|509@zji|湛江|ZJZ|zhanjiang|zj|510@zji|镇江|ZJH|zhenjiang|zj|511@zjj|张家界|DIQ|zhangjiajie|zjj|512@zjk|张家口|ZKP|zhangjiakou|zjk|513@zjn|张家口南|ZMP|zhangjiakounan|zjkn|514@zko|周口|ZKN|zhoukou|zk|515@zlm|哲里木|ZLC|zhelimu|zlm|516@zlt|扎兰屯|ZTX|zhalantun|zlt|517@zmd|驻马店|ZDN|zhumadian|zmd|518@zqi|肇庆|ZVQ|zhaoqing|zq|519@zsz|周水子|ZIT|zhoushuizi|zsz|520@zto|昭通|ZDW|zhaotong|zt|521@zwe|中卫|ZWJ|zhongwei|zw|522@zya|资阳|ZYW|ziyang|zy|523@zyx|遵义西|ZIW|zunyixi|zyx|524@zzh|枣庄|ZEK|zaozhuang|zz|525@zzh|资中|ZZW|zizhong|zz|526@zzh|株洲|ZZQ|zhuzhou|zz|527@zzx|枣庄西|ZFK|zaozhuangxi|zzx|528@aax|昂昂溪|AAX|angangxi|aax|529@ach|阿城|ACB|acheng|ac|530@ada|安达|ADX|anda|ad|531@ade|安德|ARW|ande|ad|532@adi|安定|ADP|anding|ad|533@adu|安多|ADO|anduo|ad|534@agu|安广|AGT|anguang|ag|535@aha|敖汉|YED|aohan|ah|536@ahe|艾河|AHP|aihe|ah|537@ahu|安化|PKQ|anhua|ah|538@ajc|艾家村|AJJ|aijiacun|ajc|539@aji|鳌江|ARH|aojiang|aj|540@aji|安家|AJB|anjia|aj|541@aji|阿金|AJD|ajin|aj|542@aji|安靖|PYW|anjing|aj|543@akt|阿克陶|AER|aketao|akt|544@aky|安口窑|AYY|ankouyao|aky|545@alg|敖力布告|ALD|aolibugao|albg|546@alo|安龙|AUZ|anlong|al|547@als|阿龙山|ASX|alongshan|als|548@alu|安陆|ALN|anlu|al|549@ame|阿木尔|JTX|amuer|ame|550@anz|阿南庄|AZM|ananzhuang|anz|551@aqx|安庆西|APH|anqingxi|aqx|552@asx|鞍山西|AXT|anshanxi|asx|553@ata|安塘|ATV|antang|at|554@atb|安亭北|ASH|antingbei|atb|555@ats|阿图什|ATR|atushi|ats|556@atu|安图|ATL|antu|at|557@axi|安溪|AXS|anxi|ax|558@bao|博鳌|BWQ|boao|ba|559@bbe|北碚|BPW|beibei|bb|560@bbg|白壁关|BGV|baibiguan|bbg|561@bbn|蚌埠南|BMH|bengbunan|bbn|562@bch|巴楚|BCR|bachu|bc|563@bch|板城|BUP|bancheng|bc|564@bdh|北戴河|BEP|beidaihe|bdh|565@bdi|保定|BDP|baoding|bd|566@bdi|宝坻|BPP|baodi|bd|567@bdl|八达岭|ILP|badaling|bdl|568@bdo|巴东|BNN|badong|bd|569@bgu|柏果|BGM|baiguo|bg|570@bha|布海|BUT|buhai|bh|571@bhd|白河东|BIY|baihedong|bhd|572@bho|贲红|BVC|benhong|bh|573@bhs|宝华山|BWH|baohuashan|bhs|574@bhx|白河县|BEY|baihexian|bhx|575@bjg|白芨沟|BJJ|baijigou|bjg|576@bjg|碧鸡关|BJM|bijiguan|bjg|577@bji|北滘|IBQ|beijiao|bj|578@bji|碧江|BLQ|bijiang|bj|579@bjp|白鸡坡|BBM|baijipo|bjp|580@bjs|笔架山|BSB|bijiashan|bjs|581@bjt|八角台|BTD|bajiaotai|bjt|582@bka|保康|BKD|baokang|bk|583@bkp|白奎堡|BKB|baikuipu|bkp|584@bla|白狼|BAT|bailang|bl|585@bla|百浪|BRZ|bailang|bl|586@ble|博乐|BOR|bole|bl|587@blg|宝拉格|BQC|baolage|blg|588@bli|巴林|BLX|balin|bl|589@bli|宝林|BNB|baolin|bl|590@bli|北流|BOZ|beiliu|bl|591@bli|勃利|BLB|boli|bl|592@blk|布列开|BLR|buliekai|blk|593@bls|宝龙山|BND|baolongshan|bls|594@blx|百里峡|AAP|bailixia|blx|595@bmc|八面城|BMD|bamiancheng|bmc|596@bmq|班猫箐|BNM|banmaoqing|bmq|597@bmt|八面通|BMB|bamiantong|bmt|598@bmz|北马圈子|BRP|beimaquanzi|bmqz|599@bpn|北票南|RPD|beipiaonan|bpn|600@bqi|白旗|BQP|baiqi|bq|601@bql|宝泉岭|BQB|baoquanling|bql|602@bqu|白泉|BQL|baiquan|bq|603@bsh|巴山|BAY|bashan|bs|604@bsj|白水江|BSY|baishuijiang|bsj|605@bsp|白沙坡|BPM|baishapo|bsp|606@bss|白石山|BAL|baishishan|bss|607@bsz|白水镇|BUM|baishuizhen|bsz|608@btd|包头 东|FDC|baotoudong|btd|609@bti|坂田|BTQ|bantian|bt|610@bto|泊头|BZP|botou|bt|611@btu|北屯|BYP|beitun|bt|612@bxh|本溪湖|BHT|benxihu|bxh|613@bxi|博兴|BXK|boxing|bx|614@bxt|八仙筒|VXD|baxiantong|bxt|615@byg|白音察干|BYC|baiyinchagan|bycg|616@byh|背荫河|BYB|beiyinhe|byh|617@byi|北营|BIV|beiying|by|618@byl|巴彦高勒|BAC|bayangaole|bygl|619@byl|白音他拉|BID|baiyintala|bytl|620@byq|鲅鱼圈|BYT|bayuquan|byq|621@bys|白银市|BNJ|baiyinshi|bys|622@bys|白音胡硕|BCD|baiyinhushuo|byhs|623@bzh|巴中|IEW|bazhong|bz|624@bzh|霸州|RMP|bazhou|bz|625@bzh|北宅|BVP|beizhai|bz|626@cbb|赤壁北|CIN|chibibei|cbb|627@cbg|查布嘎|CBC|chabuga|cbg|628@cch|长城|CEJ|changcheng|cc|629@cch|长冲|CCM|changchong|cc|630@cdd|承德东|CCP|chengdedong|cdd|631@cfx|赤峰西|CID|chifengxi|cfx|632@cga|嵯岗|CAX|cuogang|cg|633@cga|柴岗|CGT|chaigang|cg|634@cge|长葛|CEF|changge|cg|635@cgp|柴沟堡|CGV|chaigoupu|cgp|636@cgu|城固|CGY|chenggu|cg|637@cgy|陈官营|CAJ|chenguanying|cgy|638@cgz|成高子|CZB|chenggaozi|cgz|639@cha|草海|WBW|caohai|ch|640@che|柴河|CHB|chaihe|ch|641@che|册亨|CHZ|ceheng|ch|642@chk|草河口|CKT|caohekou|chk|643@chk|崔黄口|CHP|cuihuangkou|chk|644@chu|巢湖|CIH|chaohu|ch|645@cjg|蔡家沟|CJT|caijiagou|cjg|646@cjh|成吉思汗|CJX|chengjisihan|cjsh|647@cji|岔江|CAM|chajiang|cj|648@cjp|蔡家坡|CJY|caijiapo|cjp|649@cle|昌乐|CLK|changle|cl|650@clg|超梁沟|CYP|chaolianggou|clg|651@cli|慈利|CUQ|cili|cl|652@cli|昌黎|CLP|changli|cl|653@clz|长岭子|CLT|changlingzi|clz|654@cmi|晨明|CMB|chenming|cm|655@cno|长农|CNJ|changnong|cn|656@cpb|昌平北|VBP|changpingbei|cpb|657@cpi|常平|DAQ|changping|cp|658@cpl|长坡岭|CPM|changpoling|cpl|659@cqi|辰清|CQB|chenqing|cq|660@csh|蔡山|CON|caishan|cs|661@csh|楚山|CSB|chushan|cs|662@csh|长寿|EFW|changshou|cs|663@csh|磁山|CSP|cishan|cs|664@csh|苍石|CST|cangshi|cs|665@csh|草市|CSL|caoshi|cs|666@csq|察素齐|CSC|chasuqi|csq|667@cst|长山屯|CVT|changshantun|cst|668@cti|长汀|CES|changting|ct|669@ctx|昌图西|CPT|changtuxi|ctx|670@cwa|春湾|CQQ|chunwan|cw|671@cxi|磁县|CIP|cixian|cx|672@cxi|岑溪|CNZ|cenxi|cx|673@cxi|辰溪|CXQ|chenxi|cx|674@cxi|磁西|CRP|cixi|cx|675@cxn|长兴南|CFH|changxingnan|cxn|676@cya|磁窑|CYK|ciyao|cy|677@cya|春阳|CAL|chunyang|cy|678@cya|城阳|CEK|chengyang|cy|679@cyc|创业村|CEX|chuangyecun|cyc|680@cyc|朝阳川|CYL|chaoyangchuan|cyc|681@cyd|朝阳地|CDD|chaoyangdi|cyd|682@cyn|朝阳南|CYD|chaoyangnan|cyn|683@cyu|长垣|CYF|changyuan|cy|684@cyz|朝阳镇|CZL|chaoyangzhen|cyz|685@czb|滁州北|CUH|chuzhoubei|czb|686@czb|常州北|ESH|changzhoubei|czb|687@czh|滁州|CXH|chuzhou|cz|688@czh|潮州|CKQ|chaozhou|cz|689@czh|常庄|CVK|changzhuang|cz|690@czl|曹子里|CFP|caozili|czl|691@czw|车转湾|CWM|chezhuanwan|czw|692@czx|郴州西|ICQ|chenzhouxi|czx|693@czx|沧州西|CBP|cangzhouxi|czx|694@dan|德安|DAG|dean|da|695@dan|大安|RAT|daan|da|696@dba|大坝|DBJ|daba|db|697@dba|大板|DBC|daban|db|698@dba|大巴|DBD|daba|db|699@dba|到保|RBT|daobao|db|700@dbi|定边|DYJ|dingbian|db|701@dbj|东边井|DBB|dongbianjing|dbj|702@dbs|德伯斯|RDT|debosi|dbs|703@dcg|打柴沟|DGJ|dachaigou|dcg|704@dch|德昌|DVW|dechang|dc|705@dda|滴道|DDB|didao|dd|706@ddg|大磴沟|DKJ|dadenggou|ddg|707@ded|刀尔登|DRD|daoerdeng|ded|708@dee|得耳布尔|DRX|deerbuer|debe|709@dfa|东方|UFQ|dongfang|df|710@dfe|丹凤|DGY|danfeng|df|711@dfe|东丰|DIL|dongfeng|df|712@dge|都格|DMM|duge|dg|713@dgt|大官屯|DTT|daguantun|dgt|714@dgu|大关|RGW|daguan|dg|715@dgu|东光|DGP|dongguang|dg|716@dha|东海|DHB|donghai|dh|717@dhc|大灰厂|DHP|dahuichang|dhc|718@dhq|大红旗|DQD|dahongqi|dhq|719@dht|大禾塘|SOQ|shaodong|dh|720@dhx|东海县|DQH|donghaixian|dhx|721@dhx|德惠西|DXT|dehuixi|dhx|722@djg|达家沟|DJT|dajiagou|djg|723@dji|东津|DKB|dongjin|dj|724@dji|杜家|DJL|dujia|dj|725@dkt|大口屯|DKP|dakoutun|dkt|726@dla|东来|RVD|donglai|dl|727@dlh|德令哈|DHO|delingha|dlh|728@dlh|大陆号|DLC|daluhao|dlh|729@dli|带岭|DLB|dailing|dl|730@dli|大林|DLD|dalin|dl|731@dlq|达拉特旗|DIC|dalateqi|dltq|732@dlt|独立屯|DTX|dulitun|dlt|733@dlu|豆罗|DLV|douluo|dl|734@dlx|达拉特西|DNC|dalatexi|dltx|735@dlx|大连西|GZT|dalianxi|dlx|736@dmc|东明村|DMD|dongmingcun|dmc|737@dmh|洞庙河|DEP|dongmiaohe|dmh|738@dmx|东明县|DNF|dongmingxian|dmx|739@dni|大拟|DNZ|dani|dn|740@dpf|大平房|DPD|dapingfang|dpf|741@dps|大盘石|RPP|dapanshi|dps|742@dpu|大埔|DPI|dapu|dp|743@dpu|大堡|DVT|dapu|dp|744@dqd|大庆东|LFX|daqingdong|dqd|745@dqh|大其拉哈|DQX|daqilaha|dqlh|746@dqi|道清|DML|daoqing|dq|747@dqs|对青山|DQB|duiqingshan|dqs|748@dqx|德清西|MOH|deqingxi|dqx|749@dqx|大庆西|RHX|daqingxi|dqx|750@dsh|东升|DRQ|dongsheng|ds|751@dsh|砀山|DKH|dangshan|ds|752@dsh|独山|RWW|dushan|ds|753@dsh|登沙河|DWT|dengshahe|dsh|754@dsp|读书铺|DPM|dushupu|dsp|755@dst|大石头|DSL|dashitou|dst|756@dsx|东胜西|DYC|dongshengxi|dsx|757@dsz|大石寨|RZT|dashizhai|dsz|758@dta|东台|DBH|dongtai|dt|759@dta|定陶|DQK|dingtao|dt|760@dta|灯塔|DGT|dengta|dt|761@dtb|大田边|DBM|datianbian|dtb|762@dth|东通化|DTL|dongtonghua|dth|763@dtu|丹徒|RUH|dantu|dt|764@dtu|大屯|DNT|datun|dt|765@dwa|东湾|DRJ|dongwan|dw|766@dwk|大武口|DFJ|dawukou|dwk|767@dwp|低窝铺|DWJ|diwopu|dwp|768@dwt|大王滩|DZZ|dawangtan|dwt|769@dwz|大湾子|DFM|dawanzi|dwz|770@dxg|大兴沟|DXL|daxinggou|dxg|771@dxi|大兴|DXX|daxing|dx|772@dxi|定西|DSJ|dingxi|dx|773@dxi|甸心|DXM|dianxin|dx|774@dxi|东乡|DXG|dongxiang|dx|775@dxi|代县|DKV|daixian|dx|776@dxi|定襄|DXV|dingxiang|dx|777@dxu|东戌|RXP|dongxu|dx|778@dxz|东辛庄|DXD|dongxinzhuang|dxz|779@dya|丹阳|DYH|danyang|dy|780@dya|德阳|DYW|deyang|dy|781@dya|大雁|DYX|dayan|dy|782@dya|当阳|DYN|dangyang|dy|783@dyb|丹阳北|EXH|danyangbei|dyb|784@dyd|大英东|IAW|dayingdong|dyd|785@dyd|东淤地|DBV|dongyudi|dyd|786@dyi|大营|DYV|daying|dy|787@dyu|定远|EWH|dingyuan|dy|788@dyu|岱岳|RYV|daiyue|dy|789@dyu|大元|DYZ|dayuan|dy|790@dyz|大营镇|DJP|dayingzhen|dyz|791@dyz|大营子|DZD|dayingzi|dyz|792@dzc|大战场|DTJ|dazhanchang|dzc|793@dzd|德州东|DIP|dezhoudong|dzd|794@dzh|东至|DCH|dongzhi|dz|795@dzh|低庄|DVQ|dizhuang|dz|796@dzh|东镇|DNV|dongzhen|dz|797@dzh|道州|DFZ|daozhou|dz|798@dzh|东庄|DZV|dongzhuang|dz|799@dzh|兑镇|DWV|duizhen|dz|800@dzh|豆庄|ROP|douzhuang|dz|801@dzh|定州|DXP|dingzhou|dz|802@dzy|大竹园|DZY|dazhuyuan|dzy|803@dzz|大杖子|DAP|dazhangzi|dzz|804@dzz|豆张庄|RZP|douzhangzhuang|dzz|805@ebi|峨边|EBW|ebian|eb|806@edm|二道沟门|RDP|erdaogoumen|edgm|807@edw|二道湾|RDX|erdaowan|edw|808@ees|鄂尔多斯|EEC|eerduosi|eeds|809@elo|二龙|RLD|erlong|el|810@elt|二龙山屯|ELA|erlongshantun|elst|811@eme|峨眉|EMW|emei|em|812@emh|二密河|RML|ermihe|emh|813@eyi|二营|RYJ|erying|ey|814@ezh|鄂州|ECN|ezhou|ez|815@fan|福安|FAS|fuan|fa|816@fch|丰城|FCG|fengcheng|fc|817@fcn|丰城南|FNG|fengchengnan|fcn|818@fdo|肥东|FIH|feidong|fd|819@fer|发耳|FEM|faer|fe|820@fha|富海|FHX|fuhai|fh|821@fha|福海|FHR|fuhai|fh|822@fhc|凤凰城|FHT|fenghuangcheng|fhc|823@fhe|汾河|FEV|fenhe|fh|824@fhu|奉化|FHH|fenghua|fh|825@fji|富锦|FIB|fujin|fj|826@fjt|范家屯|FTT|fanjiatun|fjt|827@flq|福利区|FLJ|fuliqu|flq|828@flt|福利屯|FTB|fulitun|flt|829@flz|丰乐镇|FZB|fenglezhen|flz|830@fna|阜南|FNH|funan|fn|831@fni|阜宁|AKH|funing|fn|832@fni|抚宁|FNP|funing|fn|833@fqi|福清|FQS|fuqing|fq|834@fqu|福泉|VMW|fuquan|fq|835@fsc|丰水村|FSJ|fengshuicun|fsc|836@fsh|丰顺|FUQ|fengshun|fs|837@fsh|繁峙|FSV|fanshi|fs|838@fsh|抚顺|FST|fushun|fs|839@fsk|福山口|FKP|fushankou|fsk|840@fsu|扶绥|FSZ|fusui|fs|841@ftu|冯屯|FTX|fengtun|ft|842@fty|浮图峪|FYP|futuyu|fty|843@fxd|富县东|FDY|fuxiandong|fxd|844@fxi|凤县|FXY|fengxian|fx|845@fxi|富县|FEY|fuxian|fx|846@fxi|费县|FXK|feixian|fx|847@fya|凤阳|FUH|fengyang|fy|848@fya|汾阳|FAV|fenyang|fy|849@fyb|扶余北|FBT|fuyubei|fyb|850@fyi|分宜|FYG|fenyi|fy|851@fyu|富源|FYM|fuyuan|fy|852@fyu|扶余|FYT|fuyu|fy|853@fyu|富裕|FYX|fuyu|fy|854@fzb|抚州北|FBG|fuzhoubei|fzb|855@fzh|凤州|FZY|fengzhou|fz|856@fzh|丰镇|FZC|fengzhen|fz|857@fzh|范镇|VZK|fanzhen|fz|858@gan|固安|GFP|guan|ga|859@gan|广安|VJW|guangan|ga|860@gbd|高碑店|GBP|gaobeidian|gbd|861@gbz|沟帮子|GBD|goubangzi|gbz|862@gcd|甘草店|GDJ|gancaodian|gcd|863@gch|谷城|GCN|gucheng|gc|864@gch|藁城|GEP|gaocheng|gc|865@gcu|高村|GCV|gaocun|gc|866@gcz|古城镇|GZB|guchengzhen|gcz|867@gde|广德|GRH|guangde|gd|868@gdi|贵定|GTW|guiding|gd|869@gdn|贵定南|IDW|guidingnan|gdn|870@gdo|古东|GDV|gudong|gd|871@gga|贵港|GGZ|guigang|gg|872@gga|官高|GVP|guangao|gg|873@ggm|葛根庙|GGT|gegenmiao|ggm|874@ggo|干沟|GGL|gangou|gg|875@ggu|甘谷|GGJ|gangu|gg|876@ggz|高各庄|GGP|gaogezhuang|ggz|877@ghe|甘河|GAX|ganhe|gh|878@ghe|根河|GEX|genhe|gh|879@gjd|郭家店|GDT|guojiadian|gjd|880@gjz|孤家子|GKT|gujiazi|gjz|881@gla|古浪|GLJ|gulang|gl|882@gla|皋兰|GEJ|gaolan|gl|883@glf|高楼房|GFM|gaoloufang|glf|884@glh|归流河|GHT|guiliuhe|glh|885@gli|关林|GLF|guanlin|gl|886@glu|甘洛|VOW|ganluo|gl|887@glz|郭磊庄|GLP|guoleizhuang|glz|888@gmi|高密|GMK|gaomi|gm|889@gmz|公庙子|GMC|gongmiaozi|gmz|890@gnh|工农湖|GRT|gongnonghu|gnh|891@gnn|广宁寺南|GNT|guangningsinan|gnn|892@gnw|广南卫|GNM|guangnanwei|gnw|893@gpi|高平|GPF|gaoping|gp|894@gqb|甘泉北|GEY|ganquanbei|gqb|895@gqc|共青城|GAG|gongqingcheng|gqc|896@gqk|甘旗卡|GQD|ganqika|gqk|897@gqu|甘泉|GQY|ganquan|gq|898@gqz|高桥镇|GZD|gaoqiaozhen|gqz|899@gsh|灌水|GST|guanshui|gs|900@gsh|赶水|GSW|ganshui|gs|901@gsk|孤山口|GSP|gushankou|gsk|902@gso|果松|GSL|guosong|gs|903@gsz|高山子|GSD|gaoshanzi|gsz|904@gsz|嘎什甸子|GXD|gashidianzi|gsdz|905@gta|高台|GTJ|gaotai|gt|906@gta|高滩|GAY|gaotan|gt|907@gti|古田|GTS|gutian|gt|908@gti|官厅|GTP|guanting|gt|909@gtx|官厅西|KEP|guantingxi|gtx|910@gxi|贵溪|GXG|guixi|gx|911@gya|涡阳|GYH|guoyang|gy|912@gyi|巩义|GXF|gongyi|gy|913@gyi|高邑|GIP|gaoyi|gy|914@gyn|巩义南|GYF|gongyinan|gyn|915@gyn|广元南|GAW|guangyuannan|gyn|916@gyu|固原|GUJ|guyuan|gy|917@gyu|菇园|GYL|guyuan|gy|918@gyz|公营子|GYD|gongyingzi|gyz|919@gze|光泽|GZS|guangze|gz|920@gzh|古镇|GNQ|guzhen|gz|921@gzh|固镇|GEH|guzhen|gz|922@gzh|虢镇|GZY|guozhen|gz|923@gzh|瓜州|GZJ|guazhou|gz|924@gzh|高州|GSQ|gaozhou|gz|925@gzh|盖州|GXT|gaizhou|gz|926@gzj|官字井|GOT|guanzijing|gzj|927@gzs|冠豸山|GSS|guanzhaishan|gzs|928@gzx|盖州西|GAT|gaizhouxi|gzx|929@han|淮安南|AMH|huaiannan|han|930@han|红安|HWN|hongan|ha|931@hax|海安县|HIH|haianxian|hax|932@hax|红安西|VXN|honganxi|hax|933@hba|黄柏|HBL|huangbai|hb|934@hbe|海北|HEB|haibei|hb|935@hbi|鹤壁|HAF|hebi|hb|936@hcb|会昌北|XEG|huichangbei|hcb|937@hch|华城|VCQ|huacheng|hc|938@hch|河唇|HCZ|hechun|hc|939@hch|汉川|HCN|hanchuan|hc|940@hch|海城|HCT|haicheng|hc|941@hch|合川|WKW|hechuan|hc|942@hct|黑冲滩|HCJ|heichongtan|hct|943@hcu|黄村|HCP|huangcun|hc|944@hcx|海城西|HXT|haichengxi|hcx|945@hde|化德|HGC|huade|hd|946@hdo|洪洞|HDV|hongtong|hd|947@hes|霍尔果斯|HFR|huoerguosi|hegs|948@hfe|横峰|HFG|hengfeng|hf|949@hfw|韩府湾|HXJ|hanfuwan|hfw|950@hgu|汉沽|HGP|hangu|hg|951@hgy|黄瓜园|HYM|huangguayuan|hgy|952@hgz|红光镇|IGW|hongguangzhen|hgz|953@hhe|浑河|HHT|hunhe|hh|954@hhg|红花沟|VHD|honghuagou|hhg|955@hht|黄花筒|HUD|huanghuatong|hht|956@hjd|贺家店|HJJ|hejiadian|hjd|957@hji|和静|HJR|hejing|hj|958@hji|红江|HFM|hongjiang|hj|959@hji|黑井|HIM|heijing|hj|960@hji|获嘉|HJF|huojia|hj|961@hji|河津|HJV|hejin|hj|962@hji|涵江|HJS|hanjiang|hj|963@hji|华家|HJT|huajia|hj|964@hjq|杭锦后旗|HDC|hangjinhouqi|hjhq|965@hjx|河间西|HXP|hejianxi|hjx|966@hjz|花家庄|HJM|huajiazhuang|hjz|967@hkn|河口南|HKJ|hekounan|hkn|968@hko|黄口|KOH|huangkou|hk|969@hko|湖口|HKG|hukou|hk|970@hla|呼兰|HUB|hulan|hl|971@hlb|葫芦岛北|HPD|huludaobei|hldb|972@hlh|浩良河|HHB|haolianghe|hlh|973@hlh|哈拉海|HIT|halahai|hlh|974@hli|鹤立|HOB|heli|hl|975@hli|桦林|HIB|hualin|hl|976@hli|黄陵|ULY|huangling|hl|977@hli|海林|HRB|hailin|hl|978@hli|虎林|VLB|hulin|hl|979@hli|寒岭|HAT|hanling|hl|980@hlo|和龙|HLL|helong|hl|981@hlo|海龙|HIL|hailong|hl|982@hls|哈拉苏|HAX|halasu|hls|983@hlt|呼鲁斯太|VTJ|hulusitai|hlst|984@hlz|火连寨|HLT|huolianzhai|hlz|985@hme|黄梅|VEH|huangmei|hm|986@hmy|韩麻营|HYP|hanmaying|hmy|987@hnh|黄泥河|HHL|huangnihe|hnh|988@hni|海宁|HNH|haining|hn|989@hno|惠农|HMJ|huinong|hn|990@hpi|和平|VAQ|heping|hp|991@hpz|花棚子|HZM|huapengzi|hpz|992@hqi|花桥|VQH|huaqiao|hq|993@hqi|宏庆|HEY|hongqing|hq|994@hre|怀仁|HRV|huairen|hr|995@hro|华容|HRN|huarong|hr|996@hsb|华山北|HDY|huashanbei|hsb|997@hsd|黄松甸|HDL|huangsongdian|hsd|998@hsg|和什托洛盖|VSR|heshituoluogai|hstlg|999@hsh|红山|VSB|hongshan|hs|1000@hsh|汉寿|VSQ|hanshou|hs|1001@hsh|衡山|HSQ|hengshan|hs|1002@hsh|黑水|HOT|heishui|hs|1003@hsh|惠山|VCH|huishan|hs|1004@hsh|虎什哈|HHP|hushiha|hsh|1005@hsp|红寺堡|HSJ|hongsipu|hsp|1006@hst|虎石台|HUT|hushitai|hst|1007@hsw|海石湾|HSO|haishiwan|hsw|1008@hsx|衡山西|HEQ|hengshanxi|hsx|1009@hsx|红砂岘|VSJ|hongshaxian|hsx|1010@hta|黑台|HQB|heitai|ht|1011@hta|桓台|VTK|huantai|ht|1012@hti|和田|VTR|hetian|ht|1013@hto|会同|VTQ|huitong|ht|1014@htz|海坨子|HZT|haituozi|htz|1015@hwa|黑旺|HWK|heiwang|hw|1016@hwa|海湾|RWH|haiwan|hw|1017@hxi|红星|VXB|hongxing|hx|1018@hxi|徽县|HYY|huixian|hx|1019@hxl|红兴隆|VHB|hongxinglong|hxl|1020@hxt|换新天|VTB|huanxintian|hxt|1021@hxt|红岘台|HTJ|hongxiantai|hxt|1022@hya|红彦|VIX|hongyan|hy|1023@hya|合阳|HAY|heyang|hy|1024@hya|海阳|HYK|haiyang|hy|1025@hyd|衡阳东|HVQ|hengyangdong|hyd|1026@hyi|华蓥|HUW|huaying|hy|1027@hyi|汉阴|HQY|hanyin|hy|1028@hyt|黄羊滩|HGJ|huangyangtan|hyt|1029@hyu|汉源|WHW|hanyuan|hy|1030@hyu|河源|VIQ|heyuan|hy|1031@hyu|花园|HUN|huayuan|hy|1032@hyu|湟源|HNO|huangyuan|hy|1033@hyz|黄羊镇|HYJ|huangyangzhen|hyz|1034@hzh|湖州|VZH|huzhou|hz|1035@hzh|化州|HZZ|huazhou|hz|1036@hzh|黄州|VON|huangzhou|hz|1037@hzh|霍州|HZV|huozhou|hz|1038@hzx|惠州西|VXQ|huizhouxi|hzx|1039@jba|巨宝|JRT|jubao|jb|1040@jbi|靖边|JIY|jingbian|jb|1041@jbt|金宝屯|JBD|jinbaotun|jbt|1042@jcb|晋城北|JEF|jinchengbei|jcb|1043@jch|金昌|JCJ|jinchang|jc|1044@jch|鄄城|JCK|juancheng|jc|1045@jch|交城|JNV|jiaocheng|jc|1046@jch|建昌|JFD|jianchang|jc|1047@jde|峻德|JDB|junde|jd|1048@jdi|井店|JFP|jingdian|jd|1049@jdo|鸡东|JOB|jidong|jd|1050@jdu|江都|UDH|jiangdu|jd|1051@jgs|鸡冠山|JST|jiguanshan|jgs|1052@jgt|金沟屯|VGP|jingoutun|jgt|1053@jha|静海|JHP|jinghai|jh|1054@jhe|金河|JHX|jinhe|jh|1055@jhe|锦河|JHB|jinhe|jh|1056@jhe|精河|JHR|jinghe|jh|1057@jhn|精河南|JIR|jinghenan|jhn|1058@jhu|江华|JHZ|jianghua|jh|1059@jhu|建湖|AJH|jianhu|jh|1060@jjg|纪家沟|VJD|jijiagou|jjg|1061@jji|晋江|JJS|jinjiang|jj|1062@jji|姜家|JJB|jiangjia|jj|1063@jji|江津|JJW|jiangjin|jj|1064@jke|金坑|JKT|jinkeng|jk|1065@jli|芨岭|JLJ|jiling|jl|1066@jmc|金马村|JMM|jinmacun|jmc|1067@jmd|江门东|JWQ|jiangmendong|jmd|1068@jme|角美|JES|jiaomei|jm|1069@jna|莒南|JOK|junan|jn|1070@jna|井南|JNP|jingnan|jn|1071@jou|建瓯|JVS|jianou|jo|1072@jpe|经棚|JPC|jingpeng|jp|1073@jqi|江桥|JQX|jiangqiao|jq|1074@jsa|九三|SSX|jiusan|js|1075@jsb|金山北|EGH|jinshanbei|jsb|1076@jsh|嘉善|JSH|jiashan|js|1077@jsh|京山|JCN|jingshan|js|1078@jsh|建始|JRN|jianshi|js|1079@jsh|稷山|JVV|jishan|js|1080@jsh|吉舒|JSL|jishu|js|1081@jsh|建设|JET|jianshe|js|1082@jsh|甲山|JOP|jiashan|js|1083@jsj|建三江|JIB|jiansanjiang|jsj|1084@jsn|嘉善南|EAH|jiashannan|jsn|1085@jst|金山屯|JTB|jinshantun|jst|1086@jst|江所田|JOM|jiangsuotian|jst|1087@jta|景泰|JTJ|jingtai|jt|1088@jtn|九台南|JNL|jiutainan|jtn|1089@jwe|吉文|JWX|jiwen|jw|1090@jxi|进贤|JUG|jinxian|jx|1091@jxi|莒县|JKK|juxian|jx|1092@jxi|嘉祥|JUK|jiaxiang|jx|1093@jxi|介休|JXV|jiexiu|jx|1094@jxi|嘉兴|JXH|jiaxing|jx|1095@jxi|井陉|JJP|jingxing|jx|1096@jxn|嘉兴南|EPH|jiaxingnan|jxn|1097@jxz|夹心子|JXT|jiaxinzi|jxz|1098@jya|姜堰|UEH|jiangyan|jy|1099@jya|揭阳|JRQ|jieyang|jy|1100@jya|建阳|JYS|jianyang|jy|1101@jya|简阳|JYW|jianyang|jy|1102@jye|巨野|JYK|juye|jy|1103@jyo|江永|JYZ|jiangyong|jy|1104@jyu|缙云|JYH|jinyun|jy|1105@jyu|靖远|JYJ|jingyuan|jy|1106@jyu|江源|SZL|jiangyuan|jy|1107@jyu|济源|JYF|jiyuan|jy|1108@jyx|靖远西|JXJ|jingyuanxi|jyx|1109@jzb|胶州北|JZK|jiaozhoubei|jzb|1110@jzd|焦作东|WEF|jiaozuodong|jzd|1111@jzh|金寨|JZH|jinzhai|jz|1112@jzh|靖州|JEQ|jingzhou|jz|1113@jzh|荆州|JBN|jingzhou|jz|1114@jzh|胶州|JXK|jiaozhou|jz|1115@jzh|晋州|JXP|jinzhou|jz|1116@jzn|锦州南|JOD|jinzhounan|jzn|1117@jzu|焦作|JOF|jiaozuo|jz|1118@jzw|旧庄窝|JVP|jiuzhuangwo|jzw|1119@jzz|金杖子|JYD|jinzhangzi|jzz|1120@kan|开安|KAT|kaian|ka|1121@kch|库车|KCR|kuche|kc|1122@kch|康城|KCP|kangcheng|kc|1123@kde|库都尔|KDX|kuduer|kde|1124@kdi|宽甸|KDT|kuandian|kd|1125@kdo|克东|KOB|kedong|kd|1126@kdz|昆都仑召|KDC|kundulunzhao|kdlz|1127@kji|开江|KAW|kaijiang|kj|1128@kjj|康金井|KJB|kangjinjing|kjj|1129@klq|喀喇其|KQX|kalaqi|klq|1130@klu|开鲁|KLC|kailu|kl|1131@kly|克拉玛依|KHR|kelamayi|klmy|1132@kqi|口前|KQL|kouqian|kq|1133@ksh|昆山|KSH|kunshan|ks|1134@ksh|奎山|KAB|kuishan|ks|1135@ksh|克山|KSB|keshan|ks|1136@kto|开通|KTT|kaitong|kt|1137@kxl|康熙岭|KXZ|kangxiling|kxl|1138@kya|昆阳|KAM|kunyang|ky|1139@kyh|克一河|KHX|keyihe|kyh|1140@kyx|开原西|KXT|kaiyuanxi|kyx|1141@kzh|康庄|KZP|kangzhuang|kz|1142@lbi|来宾|UBZ|laibin|lb|1143@lbi|老边|LLT|laobian|lb|1144@lbx|灵宝西|LPF|lingbaoxi|lbx|1145@lch|龙川|LUQ|longchuan|lc|1146@lch|乐昌|LCQ|lechang|lc|1147@lch|黎城|UCP|licheng|lc|1148@lch|聊城|UCK|liaocheng|lc|1149@lcu|蓝村|LCK|lancun|lc|1150@lda|两当|LDY|liangdang|ld|1151@ldo|林东|LRC|lindong|ld|1152@ldu|乐都|LDO|ledu|ld|1153@ldx|梁底下|LDP|liangdixia|ldx|1154@ldz|六道河子|LVP|liudaohezi|ldhz|1155@lfa|鲁番|LVM|lufan|lf|1156@lfa|廊坊|LJP|langfang|lf|1157@lfa|落垡|LOP|luofa|lf|1158@lfb|廊坊北|LFP|langfangbei|lfb|1159@lfu|老府|UFD|laofu|lf|1160@lga|兰岗|LNB|langang|lg|1161@lgd|龙骨甸|LGM|longgudian|lgd|1162@lgo|芦沟|LOM|lugou|lg|1163@lgo|龙沟|LGJ|longgou|lg|1164@lgu|拉古|LGB|lagu|lg|1165@lha|临海|UFH|linhai|lh|1166@lha|林海|LXX|linhai|lh|1167@lha|拉哈|LHX|laha|lh|1168@lha|凌海|JID|linghai|lh|1169@lhe|柳河|LNL|liuhe|lh|1170@lhe|六合|KLH|liuhe|lh|1171@lhu|龙华|LHP|longhua|lh|1172@lhy|滦河沿|UNP|luanheyan|lhy|1173@lhz|六合镇|LEX|liuhezhen|lhz|1174@ljd|亮甲店|LRT|liangjiadian|ljd|1175@ljd|刘家店|UDT|liujiadian|ljd|1176@ljh|刘家河|LVT|liujiahe|ljh|1177@lji|连江|LKS|lianjiang|lj|1178@lji|庐江|UJH|lujiang|lj|1179@lji|李家|LJB|lijia|lj|1180@lji|罗江|LJW|luojiang|lj|1181@lji|廉江|LJZ|lianjiang|lj|1182@lji|两家|UJT|liangjia|lj|1183@lji|龙江|LJX|longjiang|lj|1184@lji|龙嘉|UJL|longjia|lj|1185@ljk|莲江口|LHB|lianjiangkou|ljk|1186@ljl|蔺家楼|ULK|linjialou|ljl|1187@ljp|李家坪|LIJ|lijiaping|ljp|1188@lka|兰考|LKF|lankao|lk|1189@lko|林口|LKB|linkou|lk|1190@lkp|路口铺|LKQ|lukoupu|lkp|1191@lla|老莱|LAX|laolai|ll|1192@lli|拉林|LAB|lalin|ll|1193@lli|陆良|LRM|luliang|ll|1194@lli|龙里|LLW|longli|ll|1195@lli|临澧|LWQ|linli|ll|1196@lli|兰棱|LLB|lanling|ll|1197@lli|零陵|UWZ|lingling|ll|1198@llo|卢龙|UAP|lulong|ll|1199@lmd|喇嘛甸|LMX|lamadian|lmd|1200@lmd|里木店|LMB|limudian|lmd|1201@lme|洛门|LMJ|luomen|lm|1202@lna|龙南|UNG|longnan|ln|1203@lpi|梁平|UQW|liangping|lp|1204@lpi|罗平|LPM|luoping|lp|1205@lpl|落坡岭|LPP|luopoling|lpl|1206@lps|六盘山|UPJ|liupanshan|lps|1207@lps|乐平市|LPG|lepingshi|lps|1208@lqi|临清|UQK|linqing|lq|1209@lqs|龙泉寺|UQJ|longquansi|lqs|1210@lsb|乐山北|UTW|leshanbei|ls|1211@lsc|乐善村|LUM|leshancun|lsc|1212@lsd|冷水江东|UDQ|lengshuijiangdong|lsjd|1213@lsg|连山关|LGT|lianshanguan|lsg|1214@lsg|流水沟|USP|liushuigou|lsg|1215@lsh|陵水|LIQ|lingshui|ls|1216@lsh|丽水|USH|lishui|ls|1217@lsh|罗山|LRN|luoshan|ls|1218@lsh|鲁山|LAF|lushan|ls|1219@lsh|梁山|LMK|liangshan|ls|1220@lsh|灵石|LSV|lingshi|ls|1221@lsh|露水河|LUL|lushuihe|lsh|1222@lsh|庐山|LSG|lushan|ls|1223@lsp|林盛堡|LBT|linshengpu|lsp|1224@lst|柳树屯|LSD|liushutun|lst|1225@lsz|龙山镇|LAS|longshanzhen|lsz|1226@lsz|梨树镇|LSB|lishuzhen|lsz|1227@lsz|李石寨|LET|lishizhai|lsz|1228@lta|黎塘|LTZ|litang|lt|1229@lta|轮台|LAR|luntai|lt|1230@lta|芦台|LTP|lutai|lt|1231@ltb|龙塘坝|LBM|longtangba|ltb|1232@ltu|濑湍|LVZ|laituan|lt|1233@ltx|骆驼巷|LTJ|luotuoxiang|ltx|1234@lwa|李旺|VLJ|liwang|lw|1235@lwd|莱芜东|LWK|laiwudong|lwd|1236@lws|狼尾山|LRJ|langweishan|lws|1237@lwu|灵武|LNJ|lingwu|lw|1238@lwx|莱芜西|UXK|laiwuxi|lwx|1239@lxi|朗乡|LXB|langxiang|lx|1240@lxi|陇县|LXY|longxian|lx|1241@lxi|临湘|LXQ|linxiang|lx|1242@lxi|芦溪|LUG|luxi|lx|1243@lxi|莱西|LXK|laixi|lx|1244@lxi|林西|LXC|linxi|lx|1245@lxi|滦县|UXP|luanxian|lx|1246@lya|略阳|LYY|lueyang|ly|1247@lya|莱阳|LYK|laiyang|ly|1248@lya|辽阳|LYT|liaoyang|ly|1249@lyb|临沂北|UYK|linyibei|lyb|1250@lyd|凌源东|LDD|lingyuandong|lyd|1251@lyg|连云港|UIH|lianyungang|lyg|1252@lyi|临颍|LNF|linying|ly|1253@lyi|老营|LXL|laoying|ly|1254@lyo|龙游|LMH|longyou|ly|1255@lyu|罗源|LVS|luoyuan|ly|1256@lyu|林源|LYX|linyuan|ly|1257@lyu|涟源|LAQ|lianyuan|ly|1258@lyu|涞源|LYP|laiyuan|ly|1259@lyx|耒阳西|LPQ|leiyangxi|lyx|1260@lze|临泽|LEJ|linze|lz|1261@lzg|龙爪沟|LZT|longzhuagou|lzg|1262@lzh|雷州|UAQ|leizhou|lz|1263@lzh|六枝|LIW|liuzhi|lz|1264@lzh|鹿寨|LIZ|luzhai|lz|1265@lzh|来舟|LZS|laizhou|lz|1266@lzh|龙镇|LZA|longzhen|lz|1267@lzh|拉鲊|LEM|lazha|lz|1268@lzq|兰州新区|LQJ|lanzhouxinqu|lzxq|1269@mas|马鞍山|MAH|maanshan|mas|1270@mba|毛坝|MBY|maoba|mb|1271@mbg|毛坝关|MGY|maobaguan|mbg|1272@mcb|麻城北|MBN|machengbei|mcb|1273@mch|渑池|MCF|mianchi|mc|1274@mch|明城|MCL|mingcheng|mc|1275@mch|庙城|MAP|miaocheng|mc|1276@mcn|渑池南|MNF|mianchinan|mcn|1277@mcp|茅草坪|KPM|maocaoping|mcp|1278@mdh|猛洞河|MUQ|mengdonghe|mdh|1279@mds|磨刀石|MOB|modaoshi|mds|1280@mdu|弥渡|MDF|midu|md|1281@mes|帽儿山|MRB|maoershan|mes|1282@mga|明港|MGN|minggang|mg|1283@mhk|梅河口|MHL|meihekou|mhk|1284@mhu|马皇|MHZ|mahuang|mh|1285@mjg|孟家岗|MGB|mengjiagang|mjg|1286@mla|美兰|MHQ|meilan|ml|1287@mld|汨罗东|MQQ|miluodong|mld|1288@mlh|马莲河|MHB|malianhe|mlh|1289@mli|茅岭|MLZ|maoling|ml|1290@mli|庙岭|MLL|miaoling|ml|1291@mli|茂林|MLD|maolin|ml|1292@mli|穆棱|MLB|muling|ml|1293@mli|马林|MID|malin|ml|1294@mlo|马龙|MGM|malong|ml|1295@mlt|木里图|MUD|mulitu|mlt|1296@mlu|汨罗|MLQ|miluo|ml|1297@mnh|玛纳斯湖|MNR|manasihu|mnsh|1298@mni|冕宁|UGW|mianning|mn|1299@mpa|沐滂|MPQ|mupang|mp|1300@mqh|马桥河|MQB|maqiaohe|mqh|1301@mqi|闽清|MQS|minqing|mq|1302@mqu|民权|MQF|minquan|mq|1303@msh|明水河|MUT|mingshuihe|msh|1304@msh|麻山|MAB|mashan|ms|1305@msh|眉山|MSW|meishan|ms|1306@msw|漫水湾|MKW|manshuiwan|msw|1307@msz|茂舍祖|MOM|maoshezu|msz|1308@msz|米沙子|MST|mishazi|msz|1309@mxi|美溪|MEB|meixi|mx|1310@mxi|勉县|MVY|mianxian|mx|1311@mya|麻阳|MVQ|mayang|my|1312@myb|密云北|MUP|miyunbei|myb|1313@myi|米易|MMW|miyi|my|1314@myu|麦园|MYS|maiyuan|my|1315@myu|墨玉|MUR|moyu|my|1316@mzh|庙庄|MZJ|miaozhuang|mz|1317@mzh|米脂|MEY|mizhi|mz|1318@mzh|明珠|MFQ|mingzhu|mz|1319@nan|宁安|NAB|ningan|na|1320@nan|农安|NAT|nongan|na|1321@nbs|南博山|NBK|nanboshan|nbs|1322@nch|南仇|NCK|nanqiu|nc|1323@ncs|南城司|NSP|nanchengsi|ncs|1324@ncu|宁村|NCZ|ningcun|nc|1325@nde|宁德|NES|ningde|nd|1326@ngc|南观村|NGP|nanguancun|ngc|1327@ngd|南宫东|NFP|nangongdong|ngd|1328@ngl|南关岭|NLT|nanguanling|ngl|1329@ngu|宁国|NNH|ningguo|ng|1330@nha|宁海|NHH|ninghai|nh|1331@nhb|南华北|NHS|nanhuabei|nhb|1332@nhc|南河川|NHJ|nanhechuan|nhc|1333@nhz|泥河子|NHD|nihezi|nhz|1334@nji|宁家|NVT|ningjia|nj|1335@nji|南靖|NJS|nanjing|nj|1336@nji|牛家|NJB|niujia|nj|1337@nji|能家|NJD|nengjia|nj|1338@nko|南口|NKP|nankou|nk|1339@nkq|南口前|NKT|nankouqian|nkq|1340@nla|南朗|NNQ|nanlang|nl|1341@nli|乃林|NLD|nailin|nl|1342@nlk|尼勒克|NIR|nileke|nlk|1343@nlu|那罗|ULZ|naluo|nl|1344@nlx|宁陵县|NLF|ninglingxian|nlx|1345@nma|奈曼|NMD|naiman|nm|1346@nmi|宁明|NMZ|ningming|nm|1347@nmu|南木|NMX|nanmu|nm|1348@npn|南平南|NNS|nanpingnan|npn|1349@npu|那铺|NPZ|napu|np|1350@nqi|南桥|NQD|nanqiao|nq|1351@nqu|那曲|NQO|naqu|nq|1352@nqu|暖泉|NQJ|nuanquan|nq|1353@nta|南台|NTT|nantai|nt|1354@nto|南头|NOQ|nantou|nt|1355@nwu|宁武|NWV|ningwu|nw|1356@nwz|南湾子|NWP|nanwanzi|nwz|1357@nxb|南翔北|NEH|nanxiangbei|nxb|1358@nxi|宁乡|NXQ|ningxiang|nx|1359@nxi|内乡|NXF|neixiang|nx|1360@nxt|牛心台|NXT|niuxintai|nxt|1361@nyu|南峪|NUP|nanyu|ny|1362@nzg|娘子关|NIP|niangziguan|nzg|1363@nzh|南召|NAF|nanzhao|nz|1364@nzm|南杂木|NZT|nanzamu|nzm|1365@pan|蓬安|PAW|pengan|pa|1366@pan|平安|PAL|pingan|pa|1367@pay|平安驿|PNO|pinganyi|pay|1368@paz|磐安镇|PAJ|pananzhen|paz|1369@paz|平安镇|PZT|pinganzhen|paz|1370@pcd|蒲城东|PEY|puchengdong|pcd|1371@pch|蒲城|PCY|pucheng|pc|1372@pde|裴德|PDB|peide|pd|1373@pdi|偏店|PRP|piandian|pd|1374@pdx|平顶山西|BFF|pingdingshanxi|pdsx|1375@pdx|坡底下|PXJ|podixia|pdx|1376@pet|瓢儿屯|PRT|piaoertun|pet|1377@pfa|平房|PFB|pingfang|pf|1378@pga|平岗|PGL|pinggang|pg|1379@pgu|平关|PGM|pingguan|pg|1380@pgu|盘关|PAM|panguan|pg|1381@pgu|平果|PGZ|pingguo|pg|1382@phb|徘徊北|PHP|paihuaibei|phb|1383@phk|平河口|PHM|pinghekou|phk|1384@phu|平湖|PHQ|pinghu|ph|1385@pjb|盘锦北|PBD|panjinbei|pjb|1386@pjd|潘家店|PDP|panjiadian|pjd|1387@pkn|皮口南|PKT|pikounan|pk|1388@pld|普兰店|PLT|pulandian|pld|1389@pli|偏岭|PNT|pianling|pl|1390@psh|平山|PSB|pingshan|ps|1391@psh|彭山|PSW|pengshan|ps|1392@psh|皮山|PSR|pishan|ps|1393@psh|磐石|PSL|panshi|ps|1394@psh|平社|PSV|pingshe|ps|1395@psh|彭水|PHW|pengshui|ps|1396@pta|平台|PVT|pingtai|pt|1397@pti|平田|PTM|pingtian|pt|1398@pti|莆田|PTS|putian|pt|1399@ptq|葡萄菁|PTW|putaojing|ptq|1400@pwa|普湾|PWT|puwan|pw|1401@pwa|平旺|PWV|pingwang|pw|1402@pxg|平型关|PGV|pingxingguan|pxg|1403@pxi|普雄|POW|puxiong|px|1404@pxi|郫县|PWW|pixian|px|1405@pya|平洋|PYX|pingyang|py|1406@pya|彭阳|PYJ|pengyang|py|1407@pya|平遥|PYV|pingyao|py|1408@pyi|平邑|PIK|pingyi|py|1409@pyp|平原堡|PPJ|pingyuanpu|pyp|1410@pyu|平原|PYK|pingyuan|py|1411@pyu|平峪|PYP|pingyu|py|1412@pze|彭泽|PZG|pengze|pz|1413@pzh|邳州|PJH|pizhou|pz|1414@pzh|平庄|PZD|pingzhuang|pz|1415@pzi|泡子|POD|paozi|pz|1416@pzn|平庄南|PND|pingzhuangnan|pzn|1417@qan|乾安|QOT|qianan|qa|1418@qan|庆安|QAB|qingan|qa|1419@qan|迁安|QQP|qianan|qa|1420@qdb|祁东北|QRQ|qidongbei|qd|1421@qdi|七甸|QDM|qidian|qd|1422@qfd|曲阜东|QAK|qufudong|qfd|1423@qfe|庆丰|QFT|qingfeng|qf|1424@qft|奇峰塔|QVP|qifengta|qft|1425@qfu|曲阜|QFK|qufu|qf|1426@qha|琼海|QYQ|qionghai|qh|1427@qhd|秦皇岛|QTP|qinhuangdao|qhd|1428@qhe|千河|QUY|qianhe|qh|1429@qhe|清河|QIP|qinghe|qh|1430@qhm|清河门|QHD|qinghemen|qhm|1431@qhy|清华园|QHP|qinghuayuan|qhy|1432@qji|全椒|INH|quanjiao|qj|1433@qji|渠旧|QJZ|qujiu|qj|1434@qji|潜江|QJN|qianjiang|qj|1435@qji|秦家|QJB|qinjia|qj|1436@qji|綦江|QJW|qijiang|qj|1437@qjp|祁家堡|QBT|qijiapu|qjp|1438@qjx|清涧县|QNY|qingjianxian|qjx|1439@qjz|秦家庄|QZV|qinjiazhuang|qjz|1440@qlh|七里河|QLD|qilihe|qlh|1441@qli|秦岭|QLY|qinling|ql|1442@qli|渠黎|QLZ|quli|ql|1443@qlo|青龙|QIB|qinglong|ql|1444@qls|青龙山|QGH|qinglongshan|qls|1445@qme|祁门|QIH|qimen|qm|1446@qmt|前磨头|QMP|qianmotou|qmt|1447@qsh|青山|QSB|qingshan|qs|1448@qsh|确山|QSN|queshan|qs|1449@qsh|前山|QXQ|qianshan|qs|1450@qsh|清水|QUJ|qingshui|qs|1451@qsy|戚墅堰|QYH|qishuyan|qsy|1452@qti|青田|QVH|qingtian|qt|1453@qto|桥头|QAT|qiaotou|qt|1454@qtx|青铜峡|QTJ|qingtongxia|qtx|1455@qwe|前卫|QWD|qianwei|qw|1456@qwt|前苇塘|QWP|qianweitang|qwt|1457@qxi|渠县|QRW|quxian|qx|1458@qxi|祁县|QXV|qixian|qx|1459@qxi|青县|QXP|qingxian|qx|1460@qxi|桥西|QXJ|qiaoxi|qx|1461@qxu|清徐|QUV|qingxu|qx|1462@qxy|旗下营|QXC|qixiaying|qxy|1463@qya|千阳|QOY|qianyang|qy|1464@qya|沁阳|QYF|qinyang|qy|1465@qya|泉阳|QYL|quanyang|qy|1466@qyb|祁阳北|QVQ|qiyangbei|qy|1467@qyi|七营|QYJ|qiying|qy|1468@qys|庆阳山|QSJ|qingyangshan|qys|1469@qyu|清远|QBQ|qingyuan|qy|1470@qyu|清原|QYT|qingyuan|qy|1471@qzd|钦州东|QDZ|qinzhoudong|qzd|1472@qzh|钦州|QRZ|qinzhou|qz|1473@qzs|青州市|QZK|qingzhoushi|qzs|1474@ran|瑞安|RAH|ruian|ra|1475@rch|荣昌|RCW|rongchang|rc|1476@rch|瑞昌|RCG|ruichang|rc|1477@rga|如皋|RBH|rugao|rg|1478@rgu|容桂|RUQ|ronggui|rg|1479@rqi|任丘|RQP|renqiu|rq|1480@rsh|乳山|ROK|rushan|rs|1481@rsh|融水|RSZ|rongshui|rs|1482@rsh|热水|RSD|reshui|rs|1483@rxi|容县|RXZ|rongxian|rx|1484@rya|饶阳|RVP|raoyang|ry|1485@rya|汝阳|RYF|ruyang|ry|1486@ryh|绕阳河|RHD|raoyanghe|ryh|1487@rzh|汝州|ROF|ruzhou|rz|1488@sba|石坝|OBJ|shiba|sb|1489@sbc|上板城|SBP|shangbancheng|sbc|1490@sbi|施秉|AQW|shibing|sb|1491@sbn|上板城南|OBP|shangbanchengnan|sbcn|1492@sby|世博园|ZWT|shiboyuan|sby|1493@scb|双城北|SBB|shuangchengbei|scb|1494@sch|舒城|OCH|shucheng|sc|1495@sch|商城|SWN|shangcheng|sc|1496@sch|莎车|SCR|shache|sc|1497@sch|顺昌|SCS|shunchang|sc|1498@sch|神池|SMV|shenchi|sc|1499@sch|沙城|SCP|shacheng|sc|1500@sch|石城|SCT|shicheng|sc|1501@scz|山城镇|SCL|shanchengzhen|scz|1502@sda|山丹|SDJ|shandan|sd|1503@sde|顺德|ORQ|shunde|sd|1504@sde|绥德|ODY|suide|sd|1505@sdo|水洞|SIL|shuidong|sd|1506@sdu|商都|SXC|shangdu|sd|1507@sdu|十渡|SEP|shidu|sd|1508@sdw|四道湾|OUD|sidaowan|sdw|1509@sdy|顺德学院|OJQ|shundexueyuan|sdxy|1510@sfa|绅坊|OLH|shenfang|sf|1511@sfe|双丰|OFB|shuangfeng|sf|1512@sft|四方台|STB|sifangtai|sft|1513@sfu|水富|OTW|shuifu|sf|1514@sgk|三关口|OKJ|sanguankou|sgk|1515@sgl|桑根达来|OGC|sanggendalai|sgdl|1516@sgu|韶关|SNQ|shaoguan|sg|1517@sgz|上高镇|SVK|shanggaozhen|sgz|1518@sha|上杭|JBS|shanghang|sh|1519@sha|沙海|SED|shahai|sh|1520@she|松河|SBM|songhe|sh|1521@she|沙河|SHP|shahe|sh|1522@shk|沙河口|SKT|shahekou|shk|1523@shl|赛汗塔拉|SHC|saihantala|shtl|1524@shs|沙河市|VOP|shaheshi|shs|1525@shs|沙后所|SSD|shahousuo|shs|1526@sht|山河屯|SHL|shanhetun|sht|1527@shx|三河县|OXP|sanhexian|shx|1528@shy|四合永|OHD|siheyong|shy|1529@shz|三汇镇|OZW|sanhuizhen|shz|1530@shz|双河镇|SEL|shuanghezhen|shz|1531@shz|石河子|SZR|shihezi|shz|1532@shz|三合庄|SVP|sanhezhuang|shz|1533@sjd|三家店|ODP|sanjiadian|sjd|1534@sjh|水家湖|SQH|shuijiahu|sjh|1535@sjh|沈家河|OJJ|shenjiahe|sjh|1536@sjh|松江河|SJL|songjianghe|sjh|1537@sji|尚家|SJB|shangjia|sj|1538@sji|孙家|SUB|sunjia|sj|1539@sji|沈家|OJB|shenjia|sj|1540@sji|双吉|SML|shuangji|sj|1541@sji|松江|SAH|songjiang|sj|1542@sjk|三江口|SKD|sanjiangkou|sjk|1543@sjl|司家岭|OLK|sijialing|sjl|1544@sjn|松江南|IMH|songjiangnan|sjn|1545@sjn|石景山南|SRP|shijingshannan|sjsn|1546@sjt|邵家堂|SJJ|shaojiatang|sjt|1547@sjx|三江县|SOZ|sanjiangxian|sjx|1548@sjz|三家寨|SMM|sanjiazhai|sjz|1549@sjz|十家子|SJD|shijiazi|sjz|1550@sjz|松江镇|OZL|songjiangzhen|sjz|1551@sjz|施家嘴|SHM|shijiazui|sjz|1552@sjz|深井子|SWT|shenjingzi|sjz|1553@sld|什里店|OMP|shilidian|sld|1554@sle|疏勒|SUR|shule|sl|1555@slh|疏勒河|SHJ|shulehe|slh|1556@slh|舍力虎|VLD|shelihu|slh|1557@sli|石磷|SPB|shilin|sl|1558@sli|石林|SLM|shilin|sl|1559@sli|双辽|ZJD|shuangliao|sl|1560@sli|绥棱|SIB|suiling|sl|1561@sli|石岭|SOL|shiling|sl|1562@sln|石林南|LNM|shilinnan|sln|1563@slo|石龙|SLQ|shilong|sl|1564@slq|萨拉齐|SLC|salaqi|slq|1565@slu|索伦|SNT|suolun|sl|1566@slu|商洛|OLY|shangluo|sl|1567@slz|沙岭子|SLP|shalingzi|slz|1568@smb|石门县北|VFQ|shimenxianbei|smxb|1569@smn|三门峡南|SCF|sanmenxianan|smxn|1570@smx|三门县|OQH|sanmenxian|smx|1571@smx|石门县|OMQ|shimenxian|smx|1572@smx|三门峡西|SXF|sanmenxiaxi|smxx|1573@sni|肃宁|SYP|suning|sn|1574@son|宋|SOB|song|son|1575@spa|双牌|SBZ|shuangpai|sp|1576@spd|四平东|PPT|sipingdong|spd|1577@spi|遂平|SON|suiping|sp|1578@spt|沙坡头|SFJ|shapotou|spt|1579@sqi|沙桥|SQM|shaqiao|sq|1580@sqn|商丘南|SPF|shangqiunan|sqn|1581@squ|水泉|SID|shuiquan|sq|1582@sqx|石泉县|SXY|shiquanxian|sqx|1583@sqz|石桥子|SQT|shiqiaozi|sqz|1584@src|石人城|SRB|shirencheng|src|1585@sre|石人|SRL|shiren|sr|1586@ssh|山市|SQB|shanshi|ss|1587@ssh|神树|SWB|shenshu|ss|1588@ssh|鄯善|SSR|shanshan|ss|1589@ssh|三水|SJQ|sanshui|ss|1590@ssh|泗水|OSK|sishui|ss|1591@ssh|石山|SAD|shishan|ss|1592@ssh|松树|SFT|songshu|ss|1593@ssh|首山|SAT|shoushan|ss|1594@ssj|三十家|SRD|sanshijia|ssj|1595@ssp|三十里堡|SST|sanshilipu|sslp|1596@ssz|松树镇|SSL|songshuzhen|ssz|1597@sta|松桃|MZQ|songtao|st|1598@sth|索图罕|SHX|suotuhan|sth|1599@stj|三堂集|SDH|santangji|stj|1600@sto|石头|OTB|shitou|st|1601@sto|神头|SEV|shentou|st|1602@stu|沙沱|SFM|shatuo|st|1603@swa|上万|SWP|shangwan|sw|1604@swu|孙吴|SKB|sunwu|sw|1605@swx|沙湾县|SXR|shawanxian|swx|1606@sxi|歙县|OVH|shexian|sx|1607@sxi|遂溪|SXZ|suixi|sx|1608@sxi|沙县|SAS|shaxian|sx|1609@sxi|绍兴|SOH|shaoxing|sx|1610@sxi|石岘|SXL|shixian|sx|1611@sxp|上西铺|SXM|shangxipu|sxp|1612@sxz|石峡子|SXJ|shixiazi|sxz|1613@sya|沭阳|FMH|shuyang|sy|1614@sya|绥阳|SYB|suiyang|sy|1615@sya|寿阳|SYV|shouyang|sy|1616@sya|水洋|OYP|shuiyang|sy|1617@syc|三阳川|SYJ|sanyangchuan|syc|1618@syd|上腰墩|SPJ|shangyaodun|syd|1619@syi|三营|OEJ|sanying|sy|1620@syi|顺义|SOP|shunyi|sy|1621@syj|三义井|OYD|sanyijing|syj|1622@syp|三源浦|SYL|sanyuanpu|syp|1623@syu|上虞|BDH|shangyu|sy|1624@syu|三原|SAY|sanyuan|sy|1625@syu|上园|SUD|shangyuan|sy|1626@syu|水源|OYJ|shuiyuan|sy|1627@syz|桑园子|SAJ|sangyuanzi|syz|1628@szb|绥中北|SND|suizhongbei|szb|1629@szb|苏州北|OHH|suzhoubei|szb|1630@szd|宿州东|SRH|suzhoudong|szd|1631@szd|深圳东|BJQ|shenzhendong|szd|1632@szh|深州|OZP|shenzhou|sz|1633@szh|孙镇|OZY|sunzhen|sz|1634@szh|绥中|SZD|suizhong|sz|1635@szh|尚志|SZB|shangzhi|sz|1636@szh|师庄|SNM|shizhuang|sz|1637@szi|松滋|SIN|songzi|sz|1638@szo|师宗|SEM|shizong|sz|1639@szq|苏州园区|KAH|suzhouyuanqu|szyq|1640@szq|苏州新区|ITH|suzhouxinqu|szxq|1641@tan|泰安|TMK|taian|ta|1642@tan|台安|TID|taian|ta|1643@tay|通安驿|TAJ|tonganyi|tay|1644@tba|桐柏|TBF|tongbai|tb|1645@tbe|通北|TBB|tongbei|tb|1646@tch|桐城|TTH|tongcheng|tc|1647@tch|汤池|TCX|tangchi|tc|1648@tch|郯城|TZK|tancheng|tc|1649@tch|铁厂|TCL|tiechang|tc|1650@tcu|桃村|TCK|taocun|tc|1651@tda|通道|TRQ|tongdao|td|1652@tdo|田东|TDZ|tiandong|td|1653@tga|天岗|TGL|tiangang|tg|1654@tgl|土贵乌拉|TGC|tuguiwula|tgwl|1655@tgo|通沟|TOL|tonggou|tg|1656@tgu|太谷|TGV|taigu|tg|1657@tha|塔哈|THX|taha|th|1658@tha|棠海|THM|tanghai|th|1659@the|唐河|THF|tanghe|th|1660@the|泰和|THG|taihe|th|1661@thu|太湖|TKH|taihu|th|1662@tji|团结|TIX|tuanjie|tj|1663@tjj|谭家井|TNJ|tanjiajing|tjj|1664@tjt|陶家屯|TOT|taojiatun|tjt|1665@tjw|唐家湾|PDQ|tangjiawan|tjw|1666@tjz|统军庄|TZP|tongjunzhuang|tjz|1667@tka|泰康|TKX|taikang|tk|1668@tld|吐列毛杜|TMD|tuliemaodu|tlmd|1669@tlh|图里河|TEX|tulihe|tlh|1670@tli|铜陵|TJH|tongling|tl|1671@tli|田林|TFZ|tianlin|tl|1672@tli|亭亮|TIZ|tingliang|tl|1673@tli|铁力|TLB|tieli|tl|1674@tlx|铁岭西|PXT|tielingxi|tlx|1675@tmb|图们北|QSL|tumenbei|tmb|1676@tme|天门|TMN|tianmen|tm|1677@tmn|天门南|TNN|tianmennan|tmn|1678@tms|太姥山|TLS|taimushan|tms|1679@tmt|土牧尔台|TRC|tumuertai|tmet|1680@tmz|土门子|TCJ|tumenzi|tmz|1681@tna|洮南|TVT|taonan|tn|1682@tna|潼南|TVW|tongnan|tn|1683@tpc|太平川|TIT|taipingchuan|tpc|1684@tpz|太平镇|TEB|taipingzhen|tpz|1685@tqi|图强|TQX|tuqiang|tq|1686@tqi|台前|TTK|taiqian|tq|1687@tql|天桥岭|TQL|tianqiaoling|tql|1688@tqz|土桥子|TQJ|tuqiaozi|tqz|1689@tsc|汤山城|TCT|tangshancheng|tsc|1690@tsh|桃山|TAB|taoshan|ts|1691@tsz|塔石嘴|TIM|tashizui|tsz|1692@ttu|通途|TUT|tongtu|tt|1693@twh|汤旺河|THB|tangwanghe|twh|1694@txi|同心|TXJ|tongxin|tx|1695@txi|土溪|TSW|tuxi|tx|1696@txi|桐乡|TCH|tongxiang|tx|1697@tya|田阳|TRZ|tianyang|ty|1698@tyi|天义|TND|tianyi|ty|1699@tyi|汤阴|TYF|tangyin|ty|1700@tyl|驼腰岭|TIL|tuoyaoling|tyl|1701@tys|太阳山|TYJ|taiyangshan|tys|1702@tyu|汤原|TYB|tangyuan|ty|1703@tyy|塔崖驿|TYP|tayayi|tyy|1704@tzd|滕州东|TEK|tengzhoudong|tzd|1705@tzh|台州|TZH|taizhou|tz|1706@tzh|天祝|TZJ|tianzhu|tz|1707@tzh|滕州|TXK|tengzhou|tz|1708@tzh|天镇|TZV|tianzhen|tz|1709@tzl|桐子林|TEW|tongzilin|tzl|1710@tzs|天柱山|QWH|tianzhushan|tzs|1711@wan|文安|WBP|wenan|wa|1712@wan|武安|WAP|wuan|wa|1713@waz|王安镇|WVP|wanganzhen|waz|1714@wbu|吴堡|WUY|wubu|wb|1715@wca|旺苍|WEW|wangcang|wc|1716@wcg|五叉沟|WCT|wuchagou|wcg|1717@wch|文昌|WEQ|wenchang|wc|1718@wch|温春|WDB|wenchun|wc|1719@wdc|五大连池|WRB|wudalianchi|wdlc|1720@wde|文登|WBK|wendeng|wd|1721@wdg|五道沟|WDL|wudaogou|wdg|1722@wdh|五道河|WHP|wudaohe|wdh|1723@wdi|文地|WNZ|wendi|wd|1724@wdo|卫东|WVT|weidong|wd|1725@wds|武当山|WRN|wudangshan|wds|1726@wdu|望都|WDP|wangdu|wd|1727@weh|乌尔旗汗|WHX|wuerqihan|weqh|1728@wfa|潍坊|WFK|weifang|wf|1729@wft|万发屯|WFB|wanfatun|wft|1730@wfu|王府|WUT|wangfu|wf|1731@wfx|瓦房店西|WXT|wafangdianxi|wfdx|1732@wga|王岗|WGB|wanggang|wg|1733@wgo|武功|WGY|wugong|wg|1734@wgo|湾沟|WGL|wangou|wg|1735@wgt|吴官田|WGM|wuguantian|wgt|1736@wha|乌海|WVC|wuhai|wh|1737@whe|苇河|WHB|weihe|wh|1738@whu|卫辉|WHF|weihui|wh|1739@wjc|吴家川|WCJ|wujiachuan|wjc|1740@wji|五家|WUB|wujia|wj|1741@wji|威箐|WAM|weiqing|wj|1742@wji|午汲|WJP|wuji|wj|1743@wji|渭津|WJL|weijin|wj|1744@wjw|王家湾|WJJ|wangjiawan|wjw|1745@wke|倭肯|WQB|woken|wk|1746@wks|五棵树|WKT|wukeshu|wks|1747@wlb|五龙背|WBT|wulongbei|wlb|1748@wld|乌兰哈达|WLC|wulanhada|wlhd|1749@wle|万乐|WEB|wanle|wl|1750@wlg|瓦拉干|WVX|walagan|wlg|1751@wli|温岭|VHH|wenling|wl|1752@wli|五莲|WLK|wulian|wl|1753@wlq|乌拉特前旗|WQC|wulateqianqi|wltqq|1754@wls|乌拉山|WSC|wulashan|wls|1755@wlt|卧里屯|WLX|wolitun|wlt|1756@wnb|渭南北|WBY|weinanbei|wnb|1757@wne|乌奴耳|WRX|wunuer|wne|1758@wni|万宁|WNQ|wanning|wn|1759@wni|万年|WWG|wannian|wn|1760@wnn|渭南南|WVY|weinannan|wnn|1761@wnz|渭南镇|WNJ|weinanzhen|wnz|1762@wpi|沃皮|WPT|wopi|wp|1763@wqi|吴桥|WUP|wuqiao|wq|1764@wqi|汪清|WQL|wangqing|wq|1765@wqi|武清|WWP|wuqing|wq|1766@wsh|武山|WSJ|wushan|ws|1767@wsh|文水|WEV|wenshui|ws|1768@wsz|魏善庄|WSP|weishanzhuang|wsz|1769@wto|王瞳|WTP|wangtong|wt|1770@wts|五台山|WSV|wutaishan|wts|1771@wtz|王团庄|WZJ|wangtuanzhuang|wtz|1772@wwu|五五|WVR|wuwu|ww|1773@wxd|无锡东|WGH|wuxidong|wxd|1774@wxi|卫星|WVB|weixing|wx|1775@wxi|闻喜|WXV|wenxi|wx|1776@wxi|武乡|WVV|wuxiang|wx|1777@wxq|无锡新区|IFH|wuxixinqu|wxxq|1778@wxu|武穴|WXN|wuxue|wx|1779@wxu|吴圩|WYZ|wuxu|wx|1780@wya|王杨|WYB|wangyang|wy|1781@wyi|武义|RYH|wuyi|wy|1782@wyi|五营|WWB|wuying|wy|1783@wyt|瓦窑田|WIM|wayaotian|wyt|1784@wyu|五原|WYC|wuyuan|wy|1785@wzg|苇子沟|WZL|weizigou|wzg|1786@wzh|韦庄|WZY|weizhuang|wz|1787@wzh|五寨|WZV|wuzhai|wz|1788@wzt|王兆屯|WZB|wangzhaotun|wzt|1789@wzz|微子镇|WQP|weizizhen|wzz|1790@wzz|魏杖子|WKD|weizhangzi|wzz|1791@xan|新安|EAM|xinan|xa|1792@xan|兴安|XAZ|xingan|xa|1793@xax|新安县|XAF|xinanxian|xax|1794@xba|新保安|XAP|xinbaoan|xba|1795@xbc|下板城|EBP|xiabancheng|xbc|1796@xbl|西八里|XLP|xibali|xbl|1797@xch|宣城|ECH|xuancheng|xc|1798@xch|兴城|XCD|xingcheng|xc|1799@xcu|小村|XEM|xiaocun|xc|1800@xcy|新绰源|XRX|xinchuoyuan|xcy|1801@xcz|下城子|XCB|xiachengzi|xcz|1802@xcz|新城子|XCT|xinchengzi|xcz|1803@xde|喜德|EDW|xide|xd|1804@xdj|小得江|EJM|xiaodejiang|xdj|1805@xdm|西大庙|XMP|xidamiao|xdm|1806@xdo|小董|XEZ|xiaodong|xd|1807@xdo|小东|XOD|xiaodong|xd|1808@xfe|信丰|EFG|xinfeng|xf|1809@xfe|襄汾|XFV|xiangfen|xf|1810@xfe|息烽|XFW|xifeng|xf|1811@xga|新干|EGG|xingan|xg|1812@xga|孝感|XGN|xiaogan|xg|1813@xgc|西固城|XUJ|xigucheng|xgc|1814@xgu|西固|XIJ|xigu|xg|1815@xgy|夏官营|XGJ|xiaguanying|xgy|1816@xgz|西岗子|NBB|xigangzi|xgz|1817@xhe|襄河|XXB|xianghe|xh|1818@xhe|新和|XIR|xinhe|xh|1819@xhe|宣和|XWJ|xuanhe|xh|1820@xhj|斜河涧|EEP|xiehejian|xhj|1821@xht|新华屯|XAX|xinhuatun|xht|1822@xhu|新华|XHB|xinhua|xh|1823@xhu|新化|EHQ|xinhua|xh|1824@xhu|宣化|XHP|xuanhua|xh|1825@xhx|兴和西|XEC|xinghexi|xhx|1826@xhy|小河沿|XYD|xiaoheyan|xhy|1827@xhy|下花园|XYP|xiahuayuan|xhy|1828@xhz|小河镇|EKY|xiaohezhen|xhz|1829@xji|徐家|XJB|xujia|xj|1830@xji|峡江|EJG|xiajiang|xj|1831@xji|新绛|XJV|xinjiang|xj|1832@xji|辛集|ENP|xinji|xj|1833@xji|新江|XJM|xinjiang|xj|1834@xjk|西街口|EKM|xijiekou|xjk|1835@xjt|许家屯|XJT|xujiatun|xjt|1836@xjt|许家台|XTJ|xujiatai|xjt|1837@xjz|谢家镇|XMT|xiejiazhen|xjz|1838@xka|兴凯|EKB|xingkai|xk|1839@xla|小榄|EAQ|xiaolan|xl|1840@xla|香兰|XNB|xianglan|xl|1841@xld|兴隆店|XDD|xinglongdian|xld|1842@xle|新乐|ELP|xinle|xl|1843@xli|新林|XPX|xinlin|xl|1844@xli|小岭|XLB|xiaoling|xl|1845@xli|新李|XLJ|xinli|xl|1846@xli|西林|XYB|xilin|xl|1847@xli|西柳|GCT|xiliu|xl|1848@xli|仙林|XPH|xianlin|xl|1849@xlt|新立屯|XLD|xinlitun|xlt|1850@xlz|兴隆镇|XZB|xinglongzhen|xlz|1851@xlz|新立镇|XGT|xinlizhen|xlz|1852@xmi|新民|XMD|xinmin|xm|1853@xms|西麻山|XMB|ximashan|xms|1854@xmt|下马塘|XAT|xiamatang|xmt|1855@xna|孝南|XNV|xiaonan|xn|1856@xnb|咸宁北|XRN|xianningbei|xnb|1857@xni|兴宁|ENQ|xingning|xn|1858@xni|咸宁|XNN|xianning|xn|1859@xpd|犀浦东|XAW|xipudong|xpd|1860@xpi|西平|XPN|xiping|xp|1861@xpi|兴平|XPY|xingping|xp|1862@xpt|新坪田|XPM|xinpingtian|xpt|1863@xpu|霞浦|XOS|xiapu|xp|1864@xpu|溆浦|EPQ|xupu|xp|1865@xpu|犀浦|XIW|xipu|xp|1866@xqi|新青|XQB|xinqing|xq|1867@xqi|新邱|XQD|xinqiu|xq|1868@xqp|兴泉堡|XQJ|xingquanbu|xqp|1869@xrq|仙人桥|XRL|xianrenqiao|xrq|1870@xsg|小寺沟|ESP|xiaosigou|xsg|1871@xsh|杏树|XSB|xingshu|xs|1872@xsh|浠水|XZN|xishui|xs|1873@xsh|下社|XSV|xiashe|xs|1874@xsh|徐水|XSP|xushui|xs|1875@xsh|夏石|XIZ|xiashi|xs|1876@xsh|小哨|XAM|xiaoshao|xs|1877@xsp|新松浦|XOB|xinsongpu|xsp|1878@xst|杏树屯|XDT|xingshutun|xst|1879@xsw|许三湾|XSJ|xusanwan|xsw|1880@xta|湘潭|XTQ|xiangtan|xt|1881@xta|邢台|XTP|xingtai|xt|1882@xtx|仙桃西|XAN|xiantaoxi|xtx|1883@xtz|下台子|EIP|xiataizi|xtz|1884@xwe|徐闻|XJQ|xuwen|xw|1885@xwp|新窝铺|EPD|xinwopu|xwp|1886@xwu|修武|XWF|xiuwu|xw|1887@xxi|新县|XSN|xinxian|xx|1888@xxi|息县|ENN|xixian|xx|1889@xxi|西乡|XQY|xixiang|xx|1890@xxi|湘乡|XXQ|xiangxiang|xx|1891@xxi|西峡|XIF|xixia|xx|1892@xxi|孝西|XOV|xiaoxi|xx|1893@xxj|小新街|XXM|xiaoxinjie|xxj|1894@xxx|新兴县|XGQ|xinxingxian|xxx|1895@xxz|西小召|XZC|xixiaozhao|xxz|1896@xxz|小西庄|XXP|xiaoxizhuang|xxz|1897@xya|向阳|XDB|xiangyang|xy|1898@xya|旬阳|XUY|xunyang|xy|1899@xyb|旬阳北|XBY|xunyangbei|xyb|1900@xyd|襄阳东|XWN|xiangyangdong|xyd|1901@xye|兴业|SNZ|xingye|xy|1902@xyg|小雨谷|XHM|xiaoyugu|xyg|1903@xyi|信宜|EEQ|xinyi|xy|1904@xyj|小月旧|XFM|xiaoyuejiu|xyj|1905@xyq|小扬气|XYX|xiaoyangqi|xyq|1906@xyu|祥云|EXM|xiangyun|xy|1907@xyu|襄垣|EIF|xiangyuan|xy|1908@xyx|夏邑县|EJH|xiayixian|xyx|1909@xyy|新友谊|EYB|xinyouyi|xyy|1910@xyz|新阳镇|XZJ|xinyangzhen|xyz|1911@xzd|徐州东|UUH|xuzhoudong|xzd|1912@xzf|新帐房|XZX|xinzhangfang|xzf|1913@xzh|悬钟|XRP|xuanzhong|xz|1914@xzh|新肇|XZT|xinzhao|xz|1915@xzh|忻州|XXV|xinzhou|xz|1916@xzi|汐子|XZD|xizi|xz|1917@xzm|西哲里木|XRD|xizhelimu|xzlm|1918@xzz|新杖子|ERP|xinzhangzi|xzz|1919@yan|姚安|YAC|yaoan|ya|1920@yan|依安|YAX|yian|ya|1921@yan|永安|YAS|yongan|ya|1922@yax|永安乡|YNB|yonganxiang|yax|1923@ybl|亚布力|YBB|yabuli|ybl|1924@ybs|元宝山|YUD|yuanbaoshan|ybs|1925@yca|羊草|YAB|yangcao|yc|1926@ycd|秧草地|YKM|yangcaodi|ycd|1927@ych|阳澄湖|AIH|yangchenghu|ych|1928@ych|迎春|YYB|yingchun|yc|1929@ych|叶城|YER|yecheng|yc|1930@ych|盐池|YKJ|yanchi|yc|1931@ych|砚川|YYY|yanchuan|yc|1932@ych|阳春|YQQ|yangchun|yc|1933@ych|宜城|YIN|yicheng|yc|1934@ych|应城|YHN|yingcheng|yc|1935@ych|禹城|YCK|yucheng|yc|1936@ych|晏城|YEK|yancheng|yc|1937@ych|阳城|YNF|yangcheng|yc|1938@ych|阳岔|YAL|yangcha|yc|1939@ych|郓城|YPK|yuncheng|yc|1940@ych|雁翅|YAP|yanchi|yc|1941@ycl|云彩岭|ACP|yuncailing|ycl|1942@ycx|虞城县|IXH|yuchengxian|ycx|1943@ycz|营城子|YCT|yingchengzi|ycz|1944@yde|英德|YDQ|yingde|yd|1945@yde|永登|YDJ|yongdeng|yd|1946@ydi|尹地|YDM|yindi|yd|1947@ydi|永定|YGS|yongding|yd|1948@yds|雁荡山|YGH|yandangshan|yds|1949@ydu|于都|YDG|yudu|yd|1950@ydu|园墩|YAJ|yuandun|yd|1951@ydx|英德西|IIQ|yingdexi|ydx|1952@yfy|永丰营|YYM|yongfengying|yfy|1953@yga|杨岗|YRB|yanggang|yg|1954@yga|阳高|YOV|yanggao|yg|1955@ygu|阳谷|YIK|yanggu|yg|1956@yha|友好|YOB|youhao|yh|1957@yha|余杭|EVH|yuhang|yh|1958@yhc|沿河城|YHP|yanhecheng|yhc|1959@yhu|岩会|AEP|yanhui|yh|1960@yjh|羊臼河|YHM|yangjiuhe|yjh|1961@yji|永嘉|URH|yongjia|yj|1962@yji|营街|YAM|yingjie|yj|1963@yji|盐津|AEW|yanjin|yj|1964@yji|余江|YHG|yujiang|yj|1965@yji|燕郊|AJP|yanjiao|yj|1966@yji|姚家|YAT|yaojia|yj|1967@yjj|岳家井|YGJ|yuejiajing|yjj|1968@yjp|一间堡|YJT|yijianpu|yjp|1969@yjs|英吉沙|YIR|yingjisha|yjs|1970@yjs|云居寺|AFP|yunjusi|yjs|1971@yjz|燕家庄|AZK|yanjiazhuang|yjz|1972@yka|永康|RFH|yongkang|yk|1973@ykd|营口东|YGT|yingkoudong|ykd|1974@yla|银浪|YJX|yinlang|yl|1975@yla|永郎|YLW|yonglang|yl|1976@ylb|宜良北|YSM|yiliangbei|ylb|1977@yld|永乐店|YDY|yongledian|yld|1978@ylh|伊拉哈|YLX|yilaha|ylh|1979@yli|伊林|YLB|yilin|yl|1980@yli|杨陵|YSY|yangling|yl|1981@yli|彝良|ALW|yiliang|yl|1982@yli|杨林|YLM|yanglin|yl|1983@ylp|余粮堡|YLD|yuliangpu|ylp|1984@ylq|杨柳青|YQP|yangliuqing|ylq|1985@ylt|月亮田|YUM|yueliangtian|ylt|1986@yma|义马|YMF|yima|ym|1987@ymb|阳明堡|YVV|yangmingbu|ymb|1988@yme|玉门|YXJ|yumen|ym|1989@yme|云梦|YMN|yunmeng|ym|1990@ymo|元谋|YMM|yuanmou|ym|1991@yms|一面山|YST|yimianshan|yms|1992@yna|沂南|YNK|yinan|yn|1993@yna|宜耐|YVM|yinai|yn|1994@ynd|伊宁东|YNR|yiningdong|ynd|1995@yps|营盘水|YZJ|yingpanshui|yps|1996@ypu|羊堡|ABM|yangpu|yp|1997@yqb|阳泉北|YPP|yangquanbei|yqb|1998@yqi|乐清|UPH|yueqing|yq|1999@yqi|焉耆|YSR|yanqi|yq|2000@yqi|源迁|AQK|yuanqian|yq|2001@yqt|姚千户屯|YQT|yaoqianhutun|yqht|2002@yqu|阳曲|YQV|yangqu|yq|2003@ysg|榆树沟|YGP|yushugou|ysg|2004@ysh|月山|YBF|yueshan|ys|2005@ysh|玉石|YSJ|yushi|ys|2006@ysh|玉舍|AUM|yushe|ys|2007@ysh|偃师|YSF|yanshi|ys|2008@ysh|沂水|YUK|yishui|ys|2009@ysh|榆社|YSV|yushe|ys|2010@ysh|颍上|YVH|yingshang|ys|2011@ysh|窑上|ASP|yaoshang|ys|2012@ysh|元氏|YSP|yuanshi|ys|2013@ysl|杨树岭|YAD|yangshuling|ysl|2014@ysp|野三坡|AIP|yesanpo|ysp|2015@yst|榆树屯|YSX|yushutun|yst|2016@yst|榆树台|YUT|yushutai|yst|2017@ysz|鹰手营子|YIP|yingshouyingzi|ysyz|2018@yta|源潭|YTQ|yuantan|yt|2019@ytp|牙屯堡|YTZ|yatunpu|ytp|2020@yts|烟筒山|YSL|yantongshan|yts|2021@ytt|烟筒屯|YUX|yantongtun|ytt|2022@yws|羊尾哨|YWM|yangweishao|yws|2023@yxi|越西|YHW|yuexi|yx|2024@yxi|攸县|YOG|youxian|yx|2025@yxi|永修|ACG|yongxiu|yx|2026@yxx|玉溪西|YXM|yuxixi|yxx|2027@yya|弋阳|YIG|yiyang|yy|2028@yya|余姚|YYH|yuyao|yy|2029@yya|酉阳|AFW|youyang|yy|2030@yyd|岳阳东|YIQ|yueyangdong|yyd|2031@yyi|阳邑|ARP|yangyi|yy|2032@yyu|鸭园|YYL|yayuan|yy|2033@yyz|鸳鸯镇|YYJ|yuanyangzhen|yyz|2034@yzb|燕子砭|YZY|yanzibian|yzb|2035@yzh|仪征|UZH|yizheng|yz|2036@yzh|宜州|YSZ|yizhou|yz|2037@yzh|兖州|YZK|yanzhou|yz|2038@yzi|迤资|YQM|yizi|yz|2039@yzw|羊者窝|AEM|yangzhewo|yzw|2040@yzz|杨杖子|YZD|yangzhangzi|yzz|2041@zan|镇安|ZEY|zhenan|za|2042@zan|治安|ZAD|zhian|za|2043@zba|招柏|ZBP|zhaobai|zb|2044@zbw|张百湾|ZUP|zhangbaiwan|zbw|2045@zcc|中川机场|ZJJ|zhongchuanjichang|zcjc|2046@zch|枝城|ZCN|zhicheng|zc|2047@zch|子长|ZHY|zichang|zc|2048@zch|诸城|ZQK|zhucheng|zc|2049@zch|邹城|ZIK|zoucheng|zc|2050@zch|赵城|ZCV|zhaocheng|zc|2051@zda|章党|ZHT|zhangdang|zd|2052@zdi|正定|ZDP|zhengding|zd|2053@zdo|肇东|ZDB|zhaodong|zd|2054@zfp|照福铺|ZFM|zhaofupu|zfp|2055@zgt|章古台|ZGD|zhanggutai|zgt|2056@zgu|赵光|ZGB|zhaoguang|zg|2057@zhe|中和|ZHX|zhonghe|zh|2058@zhm|中华门|VNH|zhonghuamen|zhm|2059@zjb|枝江北|ZIN|zhijiangbei|zjb|2060@zjc|钟家村|ZJY|zhongjiacun|zjc|2061@zjg|朱家沟|ZUB|zhujiagou|zjg|2062@zjg|紫荆关|ZYP|zijingguan|zjg|2063@zji|周家|ZOB|zhoujia|zj|2064@zji|诸暨|ZDH|zhuji|zj|2065@zjn|镇江南|ZEH|zhenjiangnan|zjn|2066@zjt|周家屯|ZOD|zhoujiatun|zjt|2067@zjw|褚家湾|CWJ|zhujiawan|zjw|2068@zjx|湛江西|ZWQ|zhanjiangxi|zjx|2069@zjy|朱家窑|ZUJ|zhujiayao|zjy|2070@zjz|曾家坪子|ZBW|zengjiapingzi|zjpz|2071@zla|张兰|ZLV|zhanglan|zl|2072@zla|镇赉|ZLT|zhenlai|zl|2073@zli|枣林|ZIV|zaolin|zl|2074@zlt|扎鲁特|ZLD|zhalute|zlt|2075@zlx|扎赉诺尔西|ZXX|zhalainuoerxi|zlrex|2076@zmt|樟木头|ZOQ|zhangmutou|zmt|2077@zmu|中牟|ZGF|zhongmu|zm|2078@znd|中宁东|ZDJ|zhongningdong|znd|2079@zni|中宁|VNJ|zhongning|zn|2080@znn|中宁南|ZNJ|zhongningnan|znn|2081@zpi|镇平|ZPF|zhenping|zp|2082@zpi|漳平|ZPS|zhangping|zp|2083@zpu|泽普|ZPR|zepu|zp|2084@zqi|枣强|ZVP|zaoqiang|zq|2085@zqi|张桥|ZQY|zhangqiao|zq|2086@zqi|章丘|ZTK|zhangqiu|zq|2087@zrh|朱日和|ZRC|zhurihe|zrh|2088@zrl|泽润里|ZLM|zerunli|zrl|2089@zsb|中山北|ZGQ|zhongshanbei|zsb|2090@zsd|樟树东|ZOG|zhangshudong|zsd|2091@zsh|珠斯花|ZHD|zhusihua|zsh|2092@zsh|中山|ZSQ|zhongshan|zs|2093@zsh|柞水|ZSY|zhashui|zs|2094@zsh|钟山|ZSZ|zhongshan|zs|2095@zsh|樟树|ZSG|zhangshu|zs|2096@zwo|珠窝|ZOP|zhuwo|zw|2097@zwt|张维屯|ZWB|zhangweitun|zwt|2098@zwu|彰武|ZWD|zhangwu|zw|2099@zxi|棕溪|ZOY|zongxi|zx|2100@zxi|钟祥|ZTN|zhongxiang|zx|2101@zxi|资溪|ZXS|zixi|zx|2102@zxi|镇西|ZVT|zhenxi|zx|2103@zxi|张辛|ZIP|zhangxin|zx|2104@zxq|正镶白旗|ZXC|zhengxiangbaiqi|zxbq|2105@zya|紫阳|ZVY|ziyang|zy|2106@zya|枣阳|ZYN|zaoyang|zy|2107@zyb|竹园坝|ZAW|zhuyuanba|zyb|2108@zye|张掖|ZYJ|zhangye|zy|2109@zyu|镇远|ZUW|zhenyuan|zy|2110@zzd|漳州东|GOS|zhangzhoudong|zzd|2111@zzh|漳州|ZUS|zhangzhou|zz|2112@zzh|壮志|ZUX|zhuangzhi|zz|2113@zzh|子洲|ZZY|zizhou|zz|2114@zzh|中寨|ZZM|zhongzhai|zz|2115@zzh|涿州|ZXP|zhuozhou|zz|2116@zzi|咋子|ZAL|zhazi|zz|2117@zzs|卓资山|ZZC|zhuozishan|zzs|2118@zzx|株洲西|ZAQ|zhuzhouxi|zzx|2119@zzx|郑州西|XPF|zhengzhouxi|zzx|2120@abq|阿巴嘎旗|AQC|abagaqi|abgq|2121@aeb|阿尔山北|ARX|aershanbei|aesb|2122@alt|阿勒泰|AUR|aletai|alt|2123@are|安仁|ARG|anren|ar|2124@asx|安顺西|ASE|anshunxi|asx|2125@atx|安图西|AXL|antuxi|atx|2126@ayd|安阳东|ADF|anyangdong|ayd|2127@bba|博白|BBZ|bobai|bb|2128@bbu|八步|BBE|babu|bb|2129@bch|栟茶|FWH|bencha|bc|2130@bdd|保定东|BMP|baodingdong|bdd|2131@bfs|八方山|FGQ|bafangshan|bfs|2132@bgo|白沟|FEP|baigou|bg|2133@bha|滨海|FHP|binhai|bh|2134@bhb|滨海北|FCP|binhaibei|bhb|2135@bjn|宝鸡南|BBY|baojinan|bjn|2136@bjz|北井子|BRT|beijingzi|bjz|2137@bmj|白马井|BFQ|baimajing|bmj|2138@bqi|宝清|BUB|baoqing|bq|2139@bsh|璧山|FZW|bishan|bs|2140@bsp|白沙铺|BSN|baishapu|bsp|2141@bsx|白水县|BGY|baishuixian|bsx|2142@bta|板塘|NGQ|bantang|bt|2143@bxc|本溪新城|BVT|benxixincheng|bxxc|2144@bxi|彬县|BXY|binxian|bx|2145@bya|宾阳|UKZ|binyang|by|2146@byd|白洋淀|FWP|baiyangdian|byd|2147@byi|百宜|FHW|baiyi|by|2148@byn|白音华南|FNC|baiyinhuanan|byhn|2149@bzd|巴中东|BDE|bazhongdong|bzd|2150@bzh|滨州|BIK|binzhou|bz|2151@bzx|霸州西|FOP|bazhouxi|bzx|2152@cch|澄城|CUY|chengcheng|cc|2153@cgb|城固北|CBY|chenggubei|cgb|2154@cgh|查干湖|VAT|chaganhu|cgh|2155@chd|巢湖东|GUH|chaohudong|chd|2156@cji|从江|KNW|congjiang|cj|2157@cka|茶卡|CVO|chaka|ck|2158@clh|长临河|FVH|changlinhe|clh|2159@cln|茶陵南|CNG|chalingnan|cln|2160@cpd|常平东|FQQ|changpingdong|cpd|2161@cpn|常平南|FPQ|changpingnan|cpn|2162@cqq|长庆桥|CQJ|changqingqiao|cqq|2163@csb|长寿北|COW|changshoubei|csb|2164@csh|长寿湖|CSE|changshouhu|csh|2165@csh|潮汕|CBQ|chaoshan|cs|2166@csh|常山|CSU|changshan|cs|2167@csx|长沙西|RXQ|changshaxi|csx|2168@cti|朝天|CTE|chaotian|ct|2169@ctn|长汀南|CNS|changtingnan|ctn|2170@cwu|长武|CWY|changwu|cw|2171@cxi|长兴|CBH|changxing|cx|2172@cxi|苍溪|CXE|cangxi|cx|2173@cya|长阳|CYN|changyang|cy|2174@cya|潮阳|CNQ|chaoyang|cy|2175@czt|城子坦|CWT|chengzitan|czt|2176@dad|东安东|DCZ|dongandong|dad|2177@dba|德保|RBZ|debao|db|2178@dch|都昌|DCG|duchang|dc|2179@dch|东岔|DCJ|dongcha|dc|2180@dcn|东城南|IYQ|dongchengnan|dcn|2181@ddh|东戴河|RDD|dongdaihe|ddh|2182@ddx|丹东西|RWT|dandongxi|ddx|2183@deh|东二道河|DRB|dongerdaohe|dedh|2184@dfe|大丰|KRQ|dafeng|df|2185@dfn|大方南|DNE|dafangnan|dfn|2186@dgb|东港北|RGT|donggangbei|dgb|2187@dgs|大孤山|RMT|dagushan|dgs|2188@dgu|东莞|RTQ|dongguan|dg|2189@dhd|鼎湖东|UWQ|dinghudong|dhd|2190@dhs|鼎湖山|NVQ|dinghushan|dhs|2191@dji|道滘|RRQ|daojiao|dj|2192@dji|洞井|FWQ|dongjing|dj|2193@dji|垫江|DJE|dianjiang|dj|2194@dju|大苴|DIM|daju|dj|2195@dli|大荔|DNY|dali|dl|2196@dlz|大朗镇|KOQ|dalangzhen|dlz|2197@dqg|大青沟|DSD|daqinggou|dqg|2198@dqi|德清|DRH|deqing|dq|2199@dsn|砀山南|PRH|dangshannan|dsn|2200@dsn|大石头南|DAL|dashitounan|dstn|2201@dtd|当涂东|OWH|dangtudong|dtd|2202@dtx|大通西|DTO|datongxi|dtx|2203@dwa|大旺|WWQ|dawang|dw|2204@dxb|定西北|DNJ|dingxibei|dxb|2205@dxd|德兴东|DDG|dexingdong|dxd|2206@dxi|德兴|DWG|dexing|dx|2207@dxs|丹霞山|IRQ|danxiashan|dxs|2208@dyb|大冶北|DBN|dayebei|dyb|2209@dyd|都匀东|KJW|duyundong|dyd|2210@dyn|东营南|DOK|dongyingnan|dyn|2211@dyu|大余|DYG|dayu|dy|2212@dzd|定州东|DOP|dingzhoudong|dzd|2213@dzh|端州|WZQ|duanzhou|dz|2214@dzn|大足南|FQW|dazunan|dzn|2215@ems|峨眉山|IXW|emeishan|ems|2216@epg|阿房宫|EGY|epanggong|epg|2217@ezd|鄂州东|EFN|ezhoudong|ezd|2218@fcb|防城港北|FBZ|fangchenggangbei|fcgb|2219@fcd|凤城东|FDT|fengchengdong|fcd|2220@fch|富川|FDZ|fuchuan|fc|2221@fcx|繁昌西|PUH|fanchangxi|fcx|2222@fdu|丰都|FUW|fengdu|fd|2223@flb|涪陵北|FEW|fulingbei|flb|2224@fli|枫林|FLN|fenglin|fl|2225@fni|富宁|FNM|funing|fn|2226@fpi|佛坪|FUY|foping|fp|2227@fqi|法启|FQE|faqi|fq|2228@frn|芙蓉南|KCQ|furongnan|frn|2229@fsh|复盛|FAW|fusheng|fs|2230@fso|抚松|FSL|fusong|fs|2231@fsx|佛山西|FOQ|foshanxi|fsx|2232@fsz|福山镇|FZQ|fushanzhen|fsz|2233@fti|福田|NZQ|futian|ft|2234@fyb|富源北|FBM|fuyuanbei|fyb|2235@fyu|抚远|FYB|fuyuan|fy|2236@fzd|抚州东|FDG|fuzhoudong|fzd|2237@fzh|抚州|FZG|fuzhou|fz|2238@gan|高安|GCG|gaoan|ga|2239@gan|广安南|VUW|guangannan|gan|2240@gan|贵安|GAE|guian|ga|2241@gbd|高碑店东|GMP|gaobeidiandong|gbdd|2242@gch|恭城|GCZ|gongcheng|gc|2243@gcn|藁城南|GUP|gaochengnan|gcn|2244@gdb|贵定北|FMW|guidingbei|gdb|2245@gdn|葛店南|GNN|gediannan|gdn|2246@gdx|贵定县|KIW|guidingxian|gdx|2247@ghb|广汉北|GVW|guanghanbei|ghb|2248@ghu|高花|HGD|gaohua|gh|2249@gju|革居|GEM|geju|gj|2250@gli|关岭|GLE|guanling|gl|2251@glx|桂林西|GEZ|guilinxi|glx|2252@gmc|光明城|IMQ|guangmingcheng|gmc|2253@gni|广宁|FBQ|guangning|gn|2254@gns|广宁寺|GQT|guangningsi|gns|2255@gnx|广南县|GXM|guangnanxian|gnx|2256@gpi|桂平|GAZ|guiping|gp|2257@gpz|弓棚子|GPT|gongpengzi|gpz|2258@gsh|光山|GUN|guangshan|gs|2259@gsh|谷山|FFQ|gushan|gs|2260@gsl|观沙岭|FKQ|guanshaling|gsl|2261@gtb|古田北|GBS|gutianbei|gtb|2262@gtb|广通北|GPM|guangtongbei|gtb|2263@gtn|高台南|GAJ|gaotainan|gtn|2264@gtz|古田会址|STS|gutianhuizhi|gthz|2265@gyb|贵阳北|KQW|guiyangbei|gyb|2266@gyd|贵阳东|KEW|guiyangdong|gyd|2267@gyx|高邑西|GNP|gaoyixi|gyx|2268@han|惠安|HNS|huian|ha|2269@hbb|淮北北|PLH|huaibeibei|hbb|2270@hbd|鹤壁东|HFF|hebidong|hbd|2271@hcg|寒葱沟|HKB|hanconggou|hcg|2272@hch|霍城|SER|huocheng|hc|2273@hch|珲春|HUL|hunchun|hc|2274@hdd|邯郸东|HPP|handandong|hdd|2275@hdo|惠东|KDQ|huidong|hd|2276@hdp|哈达铺|HDJ|hadapu|hdp|2277@hdx|海东西|HDO|haidongxi|hdx|2278@hdx|洪洞西|HTV|hongtongxi|hdx|2279@heb|哈尔滨北|HTB|haerbinbei|hebb|2280@hfc|合肥北城|COH|hefeibeicheng|hfbc|2281@hfn|合肥南|ENH|hefeinan|hfn|2282@hga|黄冈|KGN|huanggang|hg|2283@hgd|黄冈东|KAN|huanggangdong|hgd|2284@hgd|横沟桥东|HNN|henggouqiaodong|hgqd|2285@hgx|黄冈西|KXN|huanggangxi|hgx|2286@hhe|洪河|HPB|honghe|hh|2287@hhn|怀化南|KAQ|huaihuanan|hhn|2288@hhq|黄河景区|HCF|huanghejingqu|hhjq|2289@hhu|花湖|KHN|huahu|hh|2290@hhu|惠环|KHQ|huihuan|hh|2291@hhu|后湖|IHN|houhu|hh|2292@hji|怀集|FAQ|huaiji|hj|2293@hkb|河口北|HBM|hekoubei|hkb|2294@hli|黄流|KLQ|huangliu|hl|2295@hln|黄陵南|VLY|huanglingnan|hln|2296@hme|鲘门|KMQ|houmen|hm|2297@hme|虎门|IUQ|humen|hm|2298@hmx|侯马西|HPV|houmaxi|hmx|2299@hna|衡南|HNG|hengnan|hn|2300@hnd|淮南东|HOH|huainandong|hnd|2301@hpu|合浦|HVZ|hepu|hp|2302@hqi|霍邱|FBH|huoqiu|hq|2303@hrd|怀仁东|HFV|huairendong|hrd|2304@hrd|华容东|HPN|huarongdong|hrd|2305@hrn|华容南|KRN|huarongnan|hrn|2306@hsb|黄石北|KSN|huangshibei|hsb|2307@hsb|黄山北|NYH|huangshanbei|hsb|2308@hsb|衡水北|IHP|hengshuibei|hsb|2309@hsd|贺胜桥东|HLN|heshengqiaodong|hsqd|2310@hsh|和硕|VUR|heshuo|hs|2311@hsn|花山南|KNN|huashannan|hsn|2312@hta|荷塘|KXQ|hetang|ht|2313@htd|黄土店|HKP|huangtudian|htd|2314@hyb|合阳北|HTY|heyangbei|hyb|2315@hyb|海阳北|HEK|haiyangbei|hyb|2316@hyi|槐荫|IYN|huaiyin|hy|2317@hyi|鄠邑|KXY|huyi|hyi|2318@hyk|花园口|HYT|huayuankou|hyk|2319@hzd|霍州东|HWV|huozhoudong|hzd|2320@hzn|惠州南|KNQ|huizhounan|hzn|2321@jan|建安|JUL|jianan|ja|2322@jch|泾川|JAJ|jingchuan|jc|2323@jdb|景德镇北|JDG|jingdezhenbei|jdzb|2324@jde|旌德|NSH|jingde|jd|2325@jfe|尖峰|PFQ|jianfeng|jf|2326@jha|近海|JHD|jinhai|jh|2327@jhx|蛟河西|JOL|jiaohexi|jhx|2328@jlb|军粮城北|JMP|junliangchengbei|jlcb|2329@jle|将乐|JLS|jiangle|jl|2330@jlh|贾鲁河|JLF|jialuhe|jlh|2331@jls|九郎山|KJQ|jiulangshan|jls|2332@jmb|即墨北|JVK|jimobei|jmb|2333@jmg|剑门关|JME|jianmenguan|jmg|2334@jnb|建宁县北|JCS|jianningxianbei|jnxb|2335@jni|江宁|JJH|jiangning|jn|2336@jnx|江宁西|OKH|jiangningxi|jnx|2337@jox|建瓯西|JUS|jianouxi|jox|2338@jqn|酒泉南|JNJ|jiuquannan|jqn|2339@jrx|句容西|JWH|jurongxi|jrx|2340@jsh|建水|JSM|jianshui|js|2341@jsh|尖山|JPQ|jianshan|js|2342@jss|界首市|JUN|jieshoushi|jss|2343@jxb|绩溪北|NRH|jixibei|jxb|2344@jxd|介休东|JDV|jiexiudong|jxd|2345@jxi|泾县|LOH|jingxian|jx|2346@jxi|靖西|JMZ|jingxi|jx|2347@jxn|进贤南|JXG|jinxiannan|jxn|2348@jyb|江油北|JBE|jiangyoubei|jyb|2349@jyn|嘉峪关南|JBJ|jiayuguannan|jygn|2350@jyn|简阳南|JOW|jianyangnan|jyn|2351@jyt|金银潭|JTN|jinyintan|jyt|2352@jyu|靖宇|JYL|jingyu|jy|2353@jyw|金月湾|PYQ|jinyuewan|jyw|2354@jyx|缙云西|PYH|jinyunxi|jyx|2355@jzh|晋中|JZV|jinzhong|jz|2356@jzh|景州|JEP|jingzhou|jz|2357@kfb|开封北|KBF|kaifengbei|kfb|2358@kfs|开福寺|FLQ|kaifusi|kfs|2359@khu|开化|KHU|kaihua|kh|2360@kln|凯里南|QKW|kailinan|kln|2361@klu|库伦|KLD|kulun|kl|2362@kmn|昆明南|KOM|kunmingnan|kmn|2363@kta|葵潭|KTQ|kuitan|kt|2364@kya|开阳|KVW|kaiyang|ky|2365@lad|隆安东|IDZ|longandong|lad|2366@lbb|来宾北|UCZ|laibinbei|lbb|2367@lbi|灵璧|GMH|lingbi|lb|2368@lbu|寮步|LTQ|liaobu|lb|2369@lby|绿博园|LCF|lvboyuan|lby|2370@lcb|隆昌北|NWW|longchangbei|lcb|2371@lcd|乐昌东|ILQ|lechangdong|lcd|2372@lch|临城|UUP|lincheng|lc|2373@lch|罗城|VCZ|luocheng|lc|2374@lch|陵城|LGK|lingcheng|lc|2375@lcz|老城镇|ACQ|laochengzhen|lcz|2376@ldb|龙洞堡|FVW|longdongbao|ldb|2377@ldn|乐都南|LVO|ledunan|ldn|2378@ldn|娄底南|UOQ|loudinan|ldn|2379@ldo|乐东|UQQ|ledong|ld|2380@ldy|离堆公园|INW|liduigongyuan|ldgy|2381@lfe|陆丰|LLQ|lufeng|lf|2382@lfe|龙丰|KFQ|longfeng|lf|2383@lfn|禄丰南|LQM|lufengnan|lfn|2384@lfx|临汾西|LXV|linfenxi|lfx|2385@lgn|临高南|KGQ|lingaonan|lgn|2386@lgu|麓谷|BNQ|lugu|lg|2387@lhe|滦河|UDP|luanhe|lh|2388@lhx|漯河西|LBN|luohexi|lhx|2389@ljd|罗江东|IKW|luojiangdong|ljd|2390@lji|柳江|UQZ|liujiang|lj|2391@ljn|利津南|LNK|lijinnan|ljn|2392@lkn|兰考南|LUF|lankaonan|lkn|2393@llb|兰陵北|COK|lanlingbei|llb|2394@llb|龙里北|KFW|longlibei|llb|2395@llb|沥林北|KBQ|lilinbei|llb|2396@lld|醴陵东|UKQ|lilingdong|lld|2397@lna|陇南|INJ|longnan|ln|2398@lpn|梁平南|LPE|liangpingnan|lpn|2399@lqu|礼泉|LGY|liquan|lq|2400@lsd|灵石东|UDV|lingshidong|lsd|2401@lsh|乐山|IVW|leshan|ls|2402@lsh|龙市|LAG|longshi|ls|2403@lsh|溧水|LDH|lishui|ls|2404@lwj|洛湾三江|KRW|luowansanjiang|lwsj|2405@lxb|莱西北|LBK|laixibei|lxb|2406@lya|溧阳|LEH|liyang|ly|2407@lyi|临邑|LUK|linyi|ly|2408@lyn|柳园南|LNR|liuyuannan|lyn|2409@lzb|鹿寨北|LSZ|luzhaibei|lzb|2410@lzh|阆中|LZE|langzhong|lz|2411@lzn|临泽南|LDJ|linzenan|lzn|2412@mad|马鞍山东|OMH|maanshandong|masd|2413@mch|毛陈|MHN|maochen|mc|2414@mgd|明港东|MDN|minggangdong|mgd|2415@mhn|民和南|MNO|minhenan|mhn|2416@mji|闵集|MJN|minji|mj|2417@mla|马兰|MLR|malan|ml|2418@mle|民乐|MBJ|minle|ml|2419@mle|弥勒|MLM|mile|ml|2420@mns|玛纳斯|MSR|manasi|mns|2421@mpi|牟平|MBK|muping|mp|2422@mqb|闽清北|MBS|minqingbei|mqb|2423@mqb|民权北|MIF|minquanbei|mqb|2424@msd|眉山东|IUW|meishandong|msd|2425@msh|庙山|MSN|miaoshan|ms|2426@mxi|岷县|MXJ|minxian|mx|2427@myu|门源|MYO|menyuan|my|2428@myu|暮云|KIQ|muyun|my|2429@mzb|蒙自北|MBM|mengzibei|mzb|2430@mzh|孟庄|MZF|mengzhuang|mz|2431@mzi|蒙自|MZM|mengzi|mz|2432@nbu|南部|NBE|nanbu|nb|2433@nca|南曹|NEF|nancao|nc|2434@ncb|南充北|NCE|nanchongbei|ncb|2435@nch|南城|NDG|nancheng|nc|2436@ncx|南昌西|NXG|nanchangxi|ncx|2437@ndn|宁东南|NDJ|ningdongnan|ndn|2438@ndo|宁东|NOJ|ningdong|nd|2439@nfb|南芬北|NUT|nanfenbei|nfb|2440@nfe|南丰|NFG|nanfeng|nf|2441@nhd|南湖东|NDN|nanhudong|nhd|2442@njb|内江北|NKW|neijiangbei|njb|2443@nji|南江|FIW|nanjiang|nj|2444@njk|南江口|NDQ|nanjiangkou|njk|2445@nli|南陵|LLH|nanling|nl|2446@nmu|尼木|NMO|nimu|nm|2447@nnd|南宁东|NFZ|nanningdong|nnd|2448@nnx|南宁西|NXZ|nanningxi|nnx|2449@npb|南平北|NBS|nanpingbei|npb|2450@nqn|宁强南|NOY|ningqiangnan|nqn|2451@nxi|南雄|NCQ|nanxiong|nx|2452@nyo|纳雍|NYE|nayong|ny|2453@nyz|南阳寨|NYF|nanyangzhai|nyz|2454@pan|普安|PAN|puan|pa|2455@pax|普安县|PUE|puanxian|pax|2456@pbi|屏边|PBM|pingbian|pb|2457@pbn|平坝南|PBE|pingbanan|pbn|2458@pch|平昌|PCE|pingchang|pc|2459@pdi|普定|PGW|puding|pd|2460@pdu|平度|PAK|pingdu|pd|2461@pko|皮口|PUT|pikou|pk|2462@plc|盘龙城|PNN|panlongcheng|plc|2463@pni|普宁|PEQ|puning|pn|2464@pnn|平南南|PAZ|pingnannan|pnn|2465@psb|彭山北|PPW|pengshanbei|psb|2466@psh|盘山|PUD|panshan|ps|2467@psh|坪上|PSK|pingshang|ps|2468@pxb|萍乡北|PBG|pingxiangbei|pxb|2469@pya|濮阳|PYF|puyang|py|2470@pya|鄱阳|PYG|poyang|py|2471@pyc|平遥古城|PDV|pingyaogucheng|pygc|2472@pyd|平原东|PUK|pingyuandong|pyd|2473@pzh|普者黑|PZM|puzhehei|pzh|2474@pzh|盘州|PAE|panzhou|pz|2475@pzh|彭州|PMW|pengzhou|pz|2476@qan|秦安|QGJ|qinan|qa|2477@qbd|青白江东|QFW|qingbaijiangdong|qbjd|2478@qch|青川|QCE|qingchuan|qc|2479@qdb|青岛北|QHK|qingdaobei|qdb|2480@qdo|祁东|QMQ|qidong|qd|2481@qdu|青堆|QET|qingdui|qd|2482@qfe|前锋|QFB|qianfeng|qf|2483@qjb|曲靖北|QBM|qujingbei|qjb|2484@qji|曲江|QIM|qujiang|qj|2485@qli|青莲|QEW|qinglian|ql|2486@qqn|齐齐哈尔南|QNB|qiqihaernan|qqhen|2487@qsb|清水北|QEJ|qingshuibei|qsb|2488@qsh|青神|QVW|qingshen|qs|2489@qsh|岐山|QAY|qishan|qs|2490@qsh|庆盛|QSQ|qingsheng|qs|2491@qsx|曲水县|QSO|qushuixian|qsx|2492@qxd|祁县东|QGV|qixiandong|qxd|2493@qxi|乾县|QBY|qianxian|qx|2494@qxn|旗下营南|QNC|qixiayingnan|qxyn|2495@qya|祁阳|QWQ|qiyang|qy|2496@qzn|全州南|QNZ|quanzhounan|qzn|2497@qzw|棋子湾|QZQ|qiziwan|qzw|2498@rbu|仁布|RUO|renbu|rb|2499@rcb|荣昌北|RQW|rongchangbei|rcb|2500@rch|荣成|RCK|rongcheng|rc|2501@rcx|瑞昌西|RXG|ruichangxi|rcx|2502@rdo|如东|RIH|rudong|rd|2503@rji|榕江|RVW|rongjiang|rj|2504@rkz|日喀则|RKO|rikaze|rkz|2505@rpi|饶平|RVQ|raoping|rp|2506@scl|宋城路|SFF|songchenglu|scl|2507@sdh|三道湖|SDL|sandaohu|sdh|2508@sdo|邵东|FIQ|shaodong|sd|2509@sdx|三都县|KKW|sanduxian|sdx|2510@sfa|胜芳|SUP|shengfang|sf|2511@sfb|双峰北|NFQ|shuangfengbei|sfb|2512@she|商河|SOK|shanghe|sh|2513@sho|泗洪|GQH|sihong|sh|2514@shu|四会|AHQ|sihui|sh|2515@sjd|石家庄东|SXP|shijiazhuangdong|sjzd|2516@sjn|三江南|SWZ|sanjiangnan|sjn|2517@sjz|三井子|OJT|sanjingzi|sjz|2518@slc|双流机场|IPW|shuangliujichang|sljc|2519@slx|石林西|SYM|shilinxi|slx|2520@slx|沙岭子西|IXP|shalingzixi|slzx|2521@slx|双流西|IQW|shuangliuxi|slx|2522@smb|三明北|SHS|sanmingbei|smb|2523@smi|嵩明|SVM|songming|sm|2524@sml|树木岭|FMQ|shumuling|sml|2525@snq|苏尼特左旗|ONC|sunitezuoqi|sntzq|2526@spd|山坡东|SBN|shanpodong|spd|2527@sqi|石桥|SQE|shiqiao|sq|2528@sqi|沈丘|SQN|shenqiu|sq|2529@ssb|鄯善北|SMR|shanshanbei|ssb|2530@ssb|狮山北|NSQ|shishanbei|ssb|2531@ssb|三水北|ARQ|sanshuibei|ssb|2532@ssb|松山湖北|KUQ|songshanhubei|sshb|2533@ssh|狮山|KSQ|shishan|ss|2534@ssn|三水南|RNQ|sanshuinan|ssn|2535@ssn|韶山南|INQ|shaoshannan|ssn|2536@ssu|三穗|QHW|sansui|ss|2537@sti|石梯|STE|shiti|st|2538@swe|汕尾|OGQ|shanwei|sw|2539@sxb|歙县北|NPH|shexianbei|sxb|2540@sxb|绍兴北|SLH|shaoxingbei|sxb|2541@sxd|绍兴东|SSH|shaoxingdong|sxd|2542@sxi|泗县|GPH|sixian|sx|2543@sxi|始兴|IPQ|shixing|sx|2544@sya|泗阳|MPH|siyang|sy|2545@sya|双阳|OYT|shuangyang|sy|2546@syb|邵阳北|OVQ|shaoyangbei|syb|2547@syb|松原北|OCT|songyuanbei|syb|2548@syi|山阴|SNV|shanyin|sy|2549@syn|沈阳南|SOT|shenyangnan|syn|2550@szb|深圳北|IOQ|shenzhenbei|szb|2551@szh|神州|SRQ|shenzhou|sz|2552@szs|深圳坪山|IFQ|shenzhenpingshan|szps|2553@szs|石嘴山|QQJ|shizuishan|szs|2554@szx|石柱县|OSW|shizhuxian|szx|2555@tan|台安南|TAD|taiannan|tan|2556@tcb|桃村北|TOK|taocunbei|tcb|2557@tdb|田东北|TBZ|tiandongbei|tdb|2558@tdd|土地堂东|TTN|tuditangdong|tdtd|2559@tgx|太谷西|TIV|taiguxi|tgx|2560@tha|吐哈|THR|tuha|th|2561@tha|通海|TAM|tonghai|th|2562@thb|太和北|JYN|taihebei|thb|2563@thc|天河机场|TJN|tianhejichang|thjc|2564@thj|天河街|TEN|tianhejie|thj|2565@thx|通化县|TXL|tonghuaxian|thx|2566@tji|同江|TJB|tongjiang|tj|2567@tlb|铜陵北|KXH|tonglingbei|tlb|2568@tlb|吐鲁番北|TAR|tulufanbei|tlfb|2569@tni|泰宁|TNS|taining|tn|2570@trn|铜仁南|TNW|tongrennan|trn|2571@tsn|天水南|TIJ|tianshuinan|tsn|2572@twe|通渭|TWJ|tongwei|tw|2573@txd|田心东|KQQ|tianxindong|txd|2574@txh|汤逊湖|THN|tangxunhu|txh|2575@txi|藤县|TAZ|tengxian|tx|2576@tyn|太原南|TNV|taiyuannan|tyn|2577@tyx|通远堡西|TST|tongyuanpuxi|typx|2578@tzh|通州|TOP|tongzhou|tz|2579@wdd|文登东|WGK|wendengdong|wdd|2580@wfs|五府山|WFG|wufushan|wfs|2581@whb|威虎岭北|WBL|weihulingbei|whlb|2582@whb|威海北|WHK|weihaibei|whb|2583@wlb|乌兰察布|WPC|wulanchabu|wlcb|2584@wld|五龙背东|WMT|wulongbeidong|wlbd|2585@wln|乌龙泉南|WFN|wulongquannan|wlqn|2586@wlq|乌鲁木齐|WAR|wulumuqi|wlmq|2587@wns|五女山|WET|wunvshan|wns|2588@wsh|武胜|WSE|wusheng|ws|2589@wwe|无为|IIH|wuwei|ww|2590@wws|瓦屋山|WAH|wawushan|wws|2591@wxx|闻喜西|WOV|wenxixi|wxx|2592@wyb|武义北|WDH|wuyibei|wyb|2593@wyb|武夷山北|WBS|wuyishanbei|wysb|2594@wyd|武夷山东|WCS|wuyishandong|wysd|2595@wyu|婺源|WYG|wuyuan|wy|2596@wyu|渭源|WEJ|weiyuan|wy|2597@wzb|万州北|WZE|wanzhoubei|wzb|2598@wzh|武陟|WIF|wuzhi|wz|2599@wzn|梧州南|WBZ|wuzhounan|wzn|2600@xab|兴安北|XDZ|xinganbei|xab|2601@xcd|许昌东|XVF|xuchangdong|xcd|2602@xch|项城|ERN|xiangcheng|xc|2603@xdd|新都东|EWW|xindudong|xdd|2604@xfe|西丰|XFT|xifeng|xf|2605@xfe|先锋|NQQ|xianfeng|xf|2606@xfl|湘府路|FVQ|xiangfulu|xfl|2607@xfx|襄汾西|XTV|xiangfenxi|xfx|2608@xgb|孝感北|XJN|xiaoganbei|xgb|2609@xgd|孝感东|GDN|xiaogandong|xgd|2610@xhd|西湖东|WDQ|xihudong|xhd|2611@xhn|新化南|EJQ|xinhuanan|xhn|2612@xhx|新晃西|EWQ|xinhuangxi|xhx|2613@xji|新津|IRW|xinjin|xj|2614@xjk|小金口|NKQ|xiaojinkou|xjk|2615@xjn|新津南|ITW|xinjinnan|xjn|2616@xjn|辛集南|IJP|xinjinan|xjn|2617@xnd|咸宁东|XKN|xianningdong|xnd|2618@xnn|咸宁南|UNN|xianningnan|xnn|2619@xpn|溆浦南|EMQ|xupunan|xpn|2620@xpx|西平西|EGQ|xipingxi|xpx|2621@xtb|湘潭北|EDQ|xiangtanbei|xtb|2622@xtd|邢台东|EDP|xingtaidong|xtd|2623@xwq|西乌旗|XWC|xiwuqi|xwq|2624@xwx|修武西|EXF|xiuwuxi|xwx|2625@xxb|萧县北|QSH|xiaoxianbei|xxb|2626@xxd|新乡东|EGF|xinxiangdong|xxd|2627@xyb|新余北|XBG|xinyubei|xyb|2628@xyc|西阳村|XQF|xiyangcun|xyc|2629@xyd|信阳东|OYN|xinyangdong|xyd|2630@xyd|咸阳秦都|XOY|xianyangqindu|xyqd|2631@xyo|仙游|XWS|xianyou|xy|2632@xzc|新郑机场|EZF|xinzhengjichang|xzjc|2633@xzl|香樟路|FNQ|xiangzhanglu|xzl|2634@ybl|迎宾路|YFW|yingbinlu|ybl|2635@ycb|永城北|RGH|yongchengbei|ycb|2636@ycb|运城北|ABV|yunchengbei|ycb|2637@ycd|永川东|WMW|yongchuandong|ycd|2638@ycd|禹城东|YSK|yuchengdong|ycd|2639@ych|宜春|YEG|yichun|yc|2640@ych|岳池|AWW|yuechi|yc|2641@ydh|云东海|NAQ|yundonghai|ydh|2642@ydu|姚渡|AOJ|yaodu|yd|2643@yfd|云浮东|IXQ|yunfudong|yfd|2644@yfn|永福南|YBZ|yongfunan|yfn|2645@yge|雨格|VTM|yuge|yg|2646@yhe|洋河|GTH|yanghe|yh|2647@yjb|永济北|AJV|yongjibei|yjb|2648@yji|弋江|RVH|yijiang|yj|2649@yjp|于家堡|YKP|yujiapu|yjp|2650@yjx|延吉西|YXL|yanjixi|yjx|2651@ykn|永康南|QUH|yongkangnan|ykn|2652@ylh|运粮河|YEF|yunlianghe|ylh|2653@yli|炎陵|YAG|yanling|yl|2654@yln|杨陵南|YEY|yanglingnan|yln|2655@ymi|伊敏|YMX|yimin|ym|2656@yna|郁南|YKQ|yunan|yn|2657@ypi|银瓶|KPQ|yinping|yp|2658@ysh|永寿|ASY|yongshou|ys|2659@ysh|阳朔|YCZ|yangshuo|ys|2660@ysh|云山|KZQ|yunshan|ys|2661@ysn|玉山南|YGG|yushannan|ysn|2662@yta|银滩|CTQ|yintan|yt|2663@yta|永泰|YTS|yongtai|yt|2664@ytb|鹰潭北|YKG|yingtanbei|ytb|2665@ytn|烟台南|YLK|yantainan|ytn|2666@yto|伊通|YTL|yitong|yt|2667@yxi|尤溪|YXS|youxi|yx|2668@yxi|云霄|YBS|yunxiao|yx|2669@yxi|宜兴|YUH|yixing|yx|2670@yxi|玉溪|AXM|yuxi|yx|2671@yxi|阳信|YVK|yangxin|yx|2672@yxi|应县|YZV|yingxian|yx|2673@yxn|攸县南|YXG|youxiannan|yxn|2674@yxx|洋县西|YXY|yangxianxi|yxx|2675@yyb|余姚北|CTH|yuyaobei|yyb|2676@yzh|榆中|IZJ|yuzhong|yz|2677@zan|诏安|ZDS|zhaoan|za|2678@zdc|正定机场|ZHP|zhengdingjichang|zdjc|2679@zfd|纸坊东|ZMN|zhifangdong|zfd|2680@zge|准格尔|ZEC|zhungeer|zge|2681@zhb|庄河北|ZUT|zhuanghebei|zhb|2682@zhu|昭化|ZHW|zhaohua|zh|2683@zjb|织金北|ZJE|zhijinbei|zjb|2684@zji|芷江|ZPQ|zhijiang|zj|2685@zji|织金|IZW|zhijin|zj|2686@zka|仲恺|KKQ|zhongkai|zk|2687@zko|曾口|ZKE|zengkou|zk|2688@zli|左岭|ZSN|zuoling|zl|2689@zmd|樟木头东|ZRQ|zhangmutoudong|zmtd|2690@zmx|驻马店西|ZLN|zhumadianxi|zmdx|2691@zpu|漳浦|ZCS|zhangpu|zp|2692@zqd|肇庆东|FCQ|zhaoqingdong|zqd|2693@zqi|庄桥|ZQH|zhuangqiao|zq|2694@zsh|昭山|KWQ|zhaoshan|zs|2695@zsx|钟山西|ZAZ|zhongshanxi|zsx|2696@zxi|漳县|ZXJ|zhangxian|zx|2697@zyb|资阳北|FYW|ziyangbei|zyb|2698@zyx|张掖西|ZEJ|zhangyexi|zyx|2699@zzb|资中北|WZW|zizhongbei|zzb|2700@zzd|涿州东|ZAP|zhuozhoudong|zzd|2701@zzd|枣庄东|ZNK|zaozhuangdong|zzd|2702@zzd|卓资东|ZDC|zhuozidong|zzd|2703@zzd|郑州东|ZAF|zhengzhoudong|zzd|2704@zzn|株洲南|KVQ|zhuzhounan|zzn|2705";
    }

    public enum QueryMode
    {
        快速 = 0,
        正常 = 1
    }

    #region 废弃代码
    //public struct CopyDataStruct
    //{
    //    public IntPtr dwData;

    //    public int cbData;

    //    [MarshalAs(UnmanagedType.LPStr)]
    //    public string lpData;
    //}

    //public class Win32Api
    //{
    //    #region msg
    //    public const int USER = 1024;
    //    public const int UM_1 = USER + 1;
    //    #endregion
    //    #region api
    //    [DllImport("user32.dll")]
    //    public static extern void PostMessage(IntPtr hWnd, int msg, int wParam, IntPtr lParam);
    //    #endregion

    //    public static void SendMessage(IntPtr toWndHandler, string strMsg)
    //    {
    //        IntPtr i = Marshal.StringToHGlobalAuto(strMsg);
    //        Win32Api.PostMessage(toWndHandler, Win32Api.UM_1, 0, i);
    //    }
    //}

    /// 本类封装了一些进程间通讯的细节
    //public class WINMessageUtil
    //{
    //    public const int WM_COPYDATA = 0x004A;

    //    //通过窗口的标题来查找窗口的句柄 
    //    [DllImport("User32.dll", EntryPoint = "FindWindow")]
    //    private static extern int FindWindow(string lpClassName, string lpWindowName);

    //    //在DLL库中的发送消息函数 
    //    [DllImport("User32.dll", EntryPoint = "SendMessage")]
    //    private static extern int SendMessage
    //        (
    //        int hWnd,                        // 目标窗口的句柄   
    //        int Msg,                        // 在这里是WM_COPYDATA 
    //        int wParam,                    // 第一个消息参数 
    //        ref  CopyDataStruct lParam        // 第二个消息参数 
    //        );

    //    /// 发送消息，只能传递一个自定义的消息ID和消息字符串，想传一个结构，但没成功 
    //    /// 目标进程名称，如果有多个，则给每个都发送 
    //    /// 自定义数据，可以通过这个来决定如何解析下面的strMsg 
    //    /// 传递的消息，是一个字符串 
    //    public static void SendMessage(IntPtr toWndHandler, string strMsg)
    //    {
    //        IntPtr i = Marshal.StringToHGlobalAuto(strMsg);
    //        Win32Api.PostMessage(toWndHandler, Win32Api.UM_1, 0, i);
    //        //CopyDataStruct cds;
    //        //cds.dwData = (IntPtr)100;   //这里可以传入一些自定义的数据，但只能是4字节整数   
    //        //cds.lpData = strMsg;            //消息字符串 
    //        //cds.cbData = System.Text.Encoding.Default.GetBytes(strMsg).Length + 1;  //注意，这里的长度是按字节来算的 

    //        ////发送方的窗口的句柄, 由于本系统中的接收方不关心是该消息是从哪个窗口发出的，所以就直接填0了 
    //        //int fromWindowHandler = 0;
    //        //SendMessage(toWndHandler, WM_COPYDATA, fromWindowHandler, ref cds);
    //    }

    //    /// 接收消息，得到消息字符串 
    //    /// System.Windows.Forms.Message m 
    //    /// 接收到的消息字符串 
    //    public static string ReceiveMessage(ref  System.Windows.Forms.Message m)
    //    {
    //        CopyDataStruct cds = (CopyDataStruct)m.GetLParam(typeof(CopyDataStruct));
    //        return cds.lpData;
    //    }
    //}

    //public static List<Passenger> lstVirualPassenger
    //{
    //    get;
    //    set;
    //}

    //public static string strBakIPs = "*************#**************#*************#**************#**************#*************#**************#**************#*************#*************#**************#*************#**************#*************#*************#**************#**************#**************#**************#***************#**************#*************#*************#**************#*************#*************#*************#*************#*************#*************#*************#**************#*************#*************#*************#*************#*************#*************#*************#*************#*************#*************#*************#**************#*************#*************#*************#**************#**************#*************#*************#*************#*************#*************#*************#*************#*************#**************#**************#*************#**************#*************#*************#**************#*************#*************#**************#*************#**************#61.185.133.202#124.115.20.49#113.140.51.26#61.185.133.158#61.185.133.170#119.84.111.48#124.115.20.84#124.115.20.205#119.84.113.42#124.115.20.80#124.115.20.15#124.115.20.74#61.138.219.6#124.115.20.140#222.216.188.94#119.84.111.81#124.115.20.76#222.216.188.26#124.115.20.94#218.92.221.210#116.211.118.39#124.115.20.208#124.115.20.18#112.117.216.71#219.145.161.13#124.115.20.141#124.115.20.79#219.145.161.11#116.10.190.55#112.117.216.69#222.216.188.28#124.126.126.93#124.115.20.82#124.115.20.137#222.216.188.22#220.168.132.113#124.115.20.138#119.84.111.83#220.168.132.55#116.10.190.39#113.140.51.27#222.216.188.98#116.211.118.38#124.126.126.105#124.126.126.85#112.117.216.72#219.138.64.21#222.243.110.146#61.138.219.10#124.115.20.139#61.185.133.163#58.51.95.141#59.63.173.166#218.92.220.72#219.138.64.26#222.243.110.166#222.88.91.149#119.84.113.55#218.75.201.14#59.63.173.138#112.117.216.47#222.243.110.148#59.63.173.140#59.63.173.142#101.227.66.148#219.138.64.25#59.63.173.141#60.174.241.32#222.216.188.84#220.168.132.63#220.165.142.35#58.51.95.139#61.136.166.12#220.168.132.65#218.75.201.16#220.165.142.37#61.138.219.12#222.243.110.150#218.75.201.17#60.174.241.28#59.63.173.139#222.216.188.25#220.168.132.64#61.136.166.8#61.138.219.17#58.51.95.140#101.227.66.158#114.80.143.158#222.216.188.27#124.115.20.75#116.10.190.62#116.10.190.34#220.168.132.56#124.115.20.19#61.138.219.13#222.88.93.181#222.243.110.147#211.144.81.23#222.216.188.89#106.3.62.195#216.138.64.51#222.216.188.31#61.136.166.14#124.126.126.108#222.243.110.149#218.2.83.176#222.88.93.180#101.227.66.147#220.165.142.39#101.227.66.151#112.65.220.21#61.136.166.13#218.92.221.196#61.136.166.30#61.138.219.9#58.216.21.26#101.227.66.153#58.216.21.85#183.61.133.11#222.88.91.156#124.14.10.17#58.216.21.83#61.138.219.14#120.192.92.43#220.168.132.60#218.92.226.96#218.206.207.69#61.138.219.7#61.138.219.8#120.192.90.245#120.192.92.35#211.142.194.70#125.78.240.181#218.92.220.65#218.75.201.31#124.205.178.59#116.6.73.238#118.186.8.220#61.136.166.15#222.88.91.152#58.216.21.82#61.155.141.11#118.186.8.222#120.192.90.237#218.2.83.175#58.216.21.25#218.92.221.201#58.216.21.89#221.238.24.100#218.2.83.198#120.192.90.236#61.185.133.182#222.216.188.32#121.205.7.43#120.192.92.33#220.165.142.36#183.136.208.167#222.88.93.196#125.78.240.180#218.92.226.44#220.165.142.38#120.192.90.234#121.205.7.47#211.142.194.3#218.92.226.94#115.231.82.60#211.142.194.71#218.92.226.93#58.242.249.27#220.162.97.156#58.216.21.24#14.17.101.135#220.162.97.165#121.14.228.43#61.164.242.30#121.205.7.52#42.202.148.35#116.77.73.49#122.228.228.218#58.218.214.247#218.206.207.67#220.162.97.158#121.14.228.33#220.162.97.155#120.192.90.230#120.192.90.229#61.164.241.32#58.216.21.23#61.136.166.10#61.164.242.27#211.224.7.27#115.231.82.58#42.202.148.39#218.206.207.68#113.107.56.41#220.113.9.114#121.205.7.45#121.14.228.165#211.142.194.67#119.146.206.89#222.216.188.30#58.242.249.19#115.231.84.62#121.11.92.156#122.227.2.36#58.216.21.84#121.11.94.156#121.11.94.160#121.14.228.167#61.164.241.33#113.107.56.39#121.205.7.41#121.14.228.160#115.231.82.59#111.11.27.90#220.165.142.40#61.164.241.46#183.61.133.12#120.192.92.34#120.192.90.248#115.231.82.32#115.231.84.64#115.231.82.62#222.216.188.29#111.11.27.82#58.218.214.245#42.202.148.43#122.227.2.9#61.164.242.73#116.10.190.35#112.25.35.8#61.164.242.29#183.61.140.199#61.174.241.85#183.61.133.19#58.51.95.142#219.146.68.80#121.14.228.168#219.145.161.14#183.61.140.139#42.202.148.41#125.78.240.184#211.142.194.185#120.198.232.56#122.224.7.24#58.242.249.18#121.11.94.163#119.36.195.218#183.136.208.166#183.250.179.25#61.164.242.72#220.162.97.162#183.61.133.16#211.142.194.69#120.192.92.38#115.231.82.34#122.228.228.166#175.43.124.145#119.146.206.90#125.221.95.112#122.228.228.169#115.231.82.63#121.11.94.161#121.11.92.178#115.231.82.61#112.25.35.55#112.65.220.39#42.202.148.40#58.242.249.30#183.136.217.58#175.43.124.240#115.231.82.101#58.218.214.240#220.162.97.163#183.61.140.173#222.88.93.195#115.231.82.38#113.109.56.38#120.192.92.58#58.216.21.86#183.61.133.23#120.209.142.56#120.192.90.235#42.202.148.36#119.36.195.207#58.242.249.20#123.125.92.28#42.202.148.44#222.88.93.179#218.206.207.72#125.39.19.77#183.250.179.24#113.57.187.14#120.198.232.112#124.126.126.101#183.61.133.27#113.57.187.15#113.57.187.21#112.65.220.32#175.43.124.143#218.203.13.119#175.43.124.146#115.231.82.33#222.174.239.46#183.250.179.13#183.61.133.20#183.61.133.24#42.202.148.46#175.43.20.55#121.11.92.153#175.43.124.241#218.203.13.121#183.61.133.43#163.177.242.32#116.211.118.34#122.70.142.142#123.125.92.21#61.135.175.78#112.25.35.53#61.154.102.244#60.214.70.92#122.70.142.144#183.61.133.22#58.242.249.39#211.142.194.68#58.59.19.27#218.202.111.36#175.25.243.19#120.198.232.58#222.88.93.197#122.228.228.165#58.216.21.90#123.125.92.20#183.61.140.141#120.192.246.216#112.91.29.10#60.220.213.143#175.43.20.80#120.192.246.199#117.21.189.108#122.70.142.160#218.60.98.147#211.142.194.72#61.154.102.245#122.136.46.115#119.36.195.199#218.26.67.32#163.177.242.34#14.17.101.134#120.198.232.57#123.159.205.156#175.43.124.235#121.18.230.31#60.210.11.73#120.192.90.197#61.155.141.15#58.242.249.17#122.70.142.145#223.87.1.58#112.25.35.9#124.14.10.24#120.198.232.114#61.179.107.17#60.214.70.83#120.192.246.196#223.87.1.40#183.250.179.12#223.87.1.46#58.59.19.13#61.54.219.40#110.18.244.139#120.192.246.215#119.36.195.201#175.43.124.237#120.192.199.13#14.18.17.75#60.210.18.169#218.61.27.38#112.25.35.5#121.14.228.32#123.125.92.22#60.214.70.29#111.9.120.177#221.194.176.170#60.222.223.27#122.224.7.28#183.61.133.25#61.54.219.43#175.43.124.236#115.231.82.39#113.57.187.13#110.18.244.63#121.18.230.43#182.118.15.17#60.212.19.36#113.57.187.20#122.224.7.26#121.18.238.198#61.135.175.80#113.229.252.29#112.253.19.160#116.211.118.40#113.57.187.17#110.18.244.141#175.43.124.239#14.17.101.137#61.155.141.9#124.126.126.81#119.146.206.91#123.125.92.27#120.192.199.3#60.214.70.84#125.39.19.74#61.153.56.142#122.136.46.114#101.23.128.13#60.214.70.86#106.3.62.194#60.214.70.90#218.60.106.22#211.142.22.26#121.18.230.87#112.253.19.164#60.214.70.89#120.192.246.198#175.43.124.142#218.60.98.143#60.211.208.219#112.25.35.4#175.43.124.234#125.221.95.107#122.228.228.168#101.23.128.9#218.87.111.200#223.87.1.59#112.253.19.161#111.121.194.26#218.61.27.50#175.43.124.200#175.43.124.233#60.214.70.94#182.118.15.16#112.253.19.159#218.60.98.148#121.18.230.44#182.118.15.14#60.121.19.35#211.162.127.96#112.25.35.2#113.229.252.33#60.210.18.17#60.214.70.82#111.206.169.14#122.224.7.25#110.18.244.166#112.25.35.56#101.23.128.16#121.18.230.33#60.214.70.88#61.179.107.52#101.26.37.107#60.210.11.8#218.61.27.48#218.60.98.142#112.194.138.147#101.23.128.17#122.136.46.111#122.143.27.152#60.220.213.145#120.192.92.196#202.102.254.189#122.143.27.181#122.136.46.88#202.204.80.76#223.87.1.53#58.20.139.42#121.18.230.39#112.91.29.7#123.138.60.239#61.54.219.38#60.214.70.67#183.224.42.62#60.214.70.31#111.9.120.165#175.25.243.21#163.177.242.30#60.212.19.34#122.136.46.87#60.210.11.7#122.136.46.90#182.118.15.13#202.102.254.190#101.44.11.9#123.159.205.160#223.87.1.47#123.138.60.177#218.26.67.75#60.214.70.93#58.20.139.45#218.61.27.40#123.159.205.157#101.23.128.15#110.18.244.142#117.135.194.20#218.60.106.91#218.26.67.34#112.84.105.49#123.138.60.236#60.212.19.64#124.126.126.83#60.210.18.144#110.18.244.26#121.18.230.35#60.214.70.30#123.138.60.178#112.194.138.139#112.1494.138.176#112.194.138.146#120.192.199.4#61.155.141.13#218.61.27.42#202.120.1.61#218.60.98.161#61.214.70.26#60.211.208.217#125.39.19.79#60.220.213.141#162.105.28.233#112.194.138.150#112.194.138.148#121.18.230.40#60.222.223.59#112.194.138.141#112.194.138.145#222.192.185.12#163.177.242.29#123.138.60.145#121.11.94.154#110.18.244.76#122.136.46.106#163.177.242.33#111.206.169.13#221.10.4.22#218.26.67.31#60.214.70.85#117.135.194.22#123.138.60.146#110.18.244.138#112.194.138.149#183.250.179.23#113.21.242.153#120.192.90.232#122.277.2.10#121.18.230.38#120.192.92.55#202.122.145.214#58.242.249.26#180.180.248.185#119.84.113.50#115.231.82.36#121.18.230.37#122.227.2.13#120.192.92.44#175.25.243.17#117.59.224.8#112.194.138.144#60.174.241.30#124.14.10.18#61.136.166.7#116.211.118.42#124.115.20.85#101.227.66.149#220.168.132.122#222.216.188.21#124.126.126.89#120.192.92.41#101.227.66.150#61.138.219.11#116.211.118.41#59.63.173.144#222.243.110.151#220.168.132.57#124.115.20.81#124.115.20.78#60.174.241.29#220.113.9.115#116.10.190.37#218.92.2.48#61.185.133.173#118.186.8.221#222.88.93.182#124.115.20.227#61.136.166.9#101.227.66.152#218.92.221.197#220.168.132.58#124.115.20.210#124.115.20.207#101.227.66.155#61.138.219.15#218.2.83.179#106.3.62.198#58.216.21.87#218.92.226.98#121.14.35.32#106.38.244.159#218.75.201.13#124.115.20.16#218.75.201.15#220.162.97.159#112.65.220.31#222.216.188.86#60.174.241.31#218.92.221.200#218.2.83.178#112.65.220.22#220.162.97.161#125.78.240.189#222.216.188.23#121.205.7.44#61.153.56.141#219.145.161.19#61.154.102.243#121.205.7.46#211.142.194.88#120.192.90.238#118.186.9.76#61.138.219.16#124.115.20.20#218.206.207.70#221.179.172.161#121.205.7.42#58.216.21.88#218.2.83.180#120.192.92.37#218.92.220.64#61.145.118.18#220.162.97.160#113.57.187.29#101.227.66.154#220.113.9.116#113.107.56.96#125.78.240.183#42.202.148.47#122.227.2.11#113.107.56.40#115.231.84.65#221.179.172.229#61.164.242.32#58.216.21.80#121.11.94.162#121.14.228.51#222.216.188.24#122.228.228.171#122.227.2.12#122.228.228.217#122.224.7.23#61.188.191.19#115.231.84.63#183.57.144.43#183.136.217.66#61.153.56.139#121.14.228.112#219.146.68.84#121.11.92.152#120.192.90.231#183.136.217.59#171.111.152.11#120.192.92.40#61.164.242.28#122.226.185.49#122.228.228.176#61.153..56.172#113.107.56.37#115.231.82.31#122.228.228.167#58.218.214.246#119.146.206.88#121.11.92.155#183.136.217.60#115.231.82.40#121.14.228.166#122.227.2.15#124.126.126.103#219.72.153.15#58.59.19.12#183.61.140.143#183.61.140.171#119.36.195.220#121.11.94.159#121.11.92.160#123.125.92.24#119.146.206.105#119.146.206.87#113.107.56.42#122.228.228.170#61.153.56.140#122.227.2.87#58.242.249.44#183.61.140.142#183.61.133.15#111.11.27.88#121.11.94.155#121.11.92.159#61.164.242.31#112.91.29.12#219.146.68.78#42.202.148.42#58.59.19.9#58.59.19.10#218.202.111.35#120.192.90.233#58.242.249.54#14.17.101.136#112.65.220.17#111.206.169.4#119.36.195.212#218.92.226.95#183.61.133.17#61.136.166.11#111.8.9.197#58.242.249.29#112.65.220.19#121.11.92.157#120.198.232.113#222.88.93.177#183.61.133.46#112.91.29.16#125.39.19.75#111.206.169.18#175.43.20.58#58.242.249.38#218.92.221.198#183.61.140.174#111.8.9.196#61.155.141.12#119.146.206.92#183.250.179.36#183.95.80.57#58.218.214.238#218.60.106.21#61.164.241.26#111.8.9.195#111.206.169.15#61.154.102.212#14.17.101.133#123.125.92.17#14.17.101.138#218.60.106.89#123.125.92.23#211.142.22.24#115.231.84.61#113.229.252.28#58.242.249.40#125.39.19.73#61.164.242.71#112.253.19.165#121.18.230.46#218.60.98.145#60.210.18.41#219.146.68.77#60.210.18.78#219.146.68.79#112.84.105.38#101.23.128.14#111.9.120.166#120.198.232.55#61.136.166.6#111.206.169.16#220.194.200.225#60.210.18.19#218.26.67.33#123.159.205.155#218.61.27.54#14.17.101.140#183.61.133.26#218.61.27.44#211.144.81.21#101.23.128.11#223.87.1.44#218.26.67.30#110.18.244.169#60.210.18.18#120.192.246.200#223.87.1.42#121.14.228.12#218.61.27.39#182.118.15.15#183.61.133.28#175.43.124.232#60.220.213.140#61.135.175.77#119.36.195.219#175.43.124.144#120.192.199.26#223.87.1.55#60.214.70.77#123.125.92.19#218.61.27.45#113.57.187.19#113.57.187.16#60.221.254.195#60.211.208.236#101.44.11.8#112.253.19.163#61.54.219.34#112.25.35.52#110.18.244.168#112.25.35.54#61.155.141.14#60.214.70.32#60.222.223.25#121.18.230.45#60.212.19.38#61.135.175.81#218.60.106.20#60.212.19.40#60.210.11.6#60.214.70.25#60.214.70.22#218.60.106.19#60.212.19.25#60.212.19.31#112.91.29.8#122.143.27.182#218.61.27.47#175.43.20.56#60.214.70.95#121.11.94.183#175.43.20.57#111.9.120.179#219.146.68.81#121.18.230.34#120.192.199.12#60.210.18.39#124.126.126.91#61.179.107.20#61.155.141.16#60.212.19.26#61.179.107.19#60.212.19.33#60.210.11.78#112.253.19.158#123.138.60.197#60.220.213.142#58.20.139.41#58.20.139.43#61.54.219.8#113.207.21.43#117.135.194.17#110.18.244.140#218.61.27.41#123.128.60.183#123.138.60.218#60.214.70.35#115.156.188.137#125.39.19.76#60.222.223.71#112.91.29.11#112.91.29.13#112.253.19.167#221.181.6.81#202.118.10.111#58.20.139.46#111.9.120.176#122.70.142.143#101.23.128.12#218.60.98.149#110.18.244.28#60.212.19.28#112.194.138.173#112.253.19.173#115.156.188.152#219.243.47.166#123.138.60.182#163.177.242.54#112.194.138.142#182.118.15.18#223.87.1.45#221.10.4.24#60.212.19.27#116.77.73.53#60.214.70.78#210.32.3.131#210.39.4.17#123.138.60.144#112.81.105.46#110.18.244.137#210.39.4.18#218.61.27.46#222.192.185.7#121.18.230.36#36.250.74.10#123.138.60.142#115.156.188.138#123.138.60.235#60.220.213.137#222.198.122.3#211.142.22.25#219.145.161.12#59.63.173.143#61.185.133.169#222.88.93.214#112.117.216.70#124.115.20.17#61.185.133.228#222.88.91.151#211.144.81.22#113.140.51.23#219.145.161.10#112.65.220.18#121.11.92.158#58.51.95.157#218.92.220.66#60.174.241.33#124.126.126.95#222.88.91.150#112.65.220.20#122.227.2.14#211.144.81.20#218.2.83.177#58.216.21.81#61.138.219.43#118.186.9.77#124.126.126.97#220.168.132.66#222.88.93.178#120.192.92.39#218.206.207.71#42.202.148.37#211.142.194.9#121.11.92.154#112.25.35.35#122.228.228.164#119.36.195.200#211.142.194.17#125.78.240.182#115.231.84.89#211.142.194.5#112.25.35.62#121.14.228.31#61.164.241.25#58.216.21.99#112.25.35.30#120.192.90.239#183.61.140.172#220.162.97.157#183.136.208.168#58.59.19.11#122.226.185.45#183.61.133.21#183.61.133.57#116.10.190.38#42.202.148.38#183.61.140.140#121.18.230.47#120.192.246.197#183.136.217.57#119.36.195.211#61.155.141.10#112.84.105.48#61.135.175.79#120.198.232.60#183.61.133.18#113.57.187.18#122.226.185.46#60.214.70.81#113.57.187.12#123.125.92.15#125.39.19.78#60.214.70.21#112.84.105.47#183.61.133.14#115.231.82.37#61.179.107.21#119.146.206.102#218.60.98.144#223.87.1.54#123.159.205.158#110.18.244.167#223.87.1.56#58.216.21.22#60.210.18.75#175.43.124.238#175.43.124.141#111.206.169.17#101.23.128.10#116.77.73.50#125.39.19.91#175.174.63.9#183.250.179.26#60.222.223.74#60.214.70.33#60.220.213.139#60.214.70.96#119.36.195.228#122.70.142.141#60.214.70.91#163.177.242.31#60.222.223.65#121.18.230.32#121.18.230.42#60.222.223.28#60.214.70.79#111.9.120.178#175.43.124.231#60.220.213.138#123.159.205.208#60.214.70.87#122.136.46.112#123.138.60.237#222.161.196.141#175.43.20.60#113.229.252.30#61.155.141.17#60.214.70.23#122..143.27.180#123.125.92.18#110.18.244.27#117.135.194.19#223.87.1.57#101.44.11.7#60.214.70.80#60.214.70.66#60.210.18.143#58.241.123.43#60.222.223.70#60.214.70.27#122.136.46.89#60.211.208.214#122.136.46.118#182.118.15.39#60.211.208.215#61.179.107.18#60.212.19.37#101.44.11.4#116.77.73.48#112.253.19.166#61.54.219.39#222.161.196.142#221.10.4.35#115.231.82.41#61.54.219.75#218.61.27.43#202.102.254.188#221.10.4.25#112.25.35.7#60.211.208.216#123.138.60.238#125.221.95.113#122.70.142.172#221.10.4.23#60.214.70.24#223.87.1.51#60.214.70.28#112.91.29.9#58.242.249.34#221.10.4.26#111.161.22.27#112.194.138.143#123.125.92.16#218.202.111.38#60.222.223.26#218.60.98.146#60.211.208.203#123.138.60.147#202.120.1.62#60.212.19.32#101.44.11.6#125.221.95.111#219.243.47.167#119.36.195.217#60.214.70.34#112.194.138.140#162.105.28.232#122.136.46.117#121.14.228.34#218.61.27.49#112.253.19.162#220.162.97.164#124.126.126.10#106.38.244.140#183.136.217.13#183.136.217.14#183.136.217.16#183.136.217.15#106.38.244.141#113.12.84.18#14.17.101.21#222.187.223.14#122.226.229.21#121.11.92.165#122.226.229.19#218.92.221.155#121.11.92.168#14.17.101.20#61.164.241.89#61.164.241.102#222.187.223.49#14.17.101.19#61.164.241.39#121.11.92.167#122.226.229.80#218.92.221.153#122.226.229.20#113.12.84.16#222.187.223.15#222.187.223.47#14.17.101.18#113.12.84.17#61.164.241.103#121.11.92.166#119.84.111.21#222.211.64.68#222.211.64.69#222.211.64.111#222.211.64.114#119.84.111.20#218.92.227.106#218.92.227.107#218.92.227.104#222.88.93.188#218.92.227.105#222.88.93.187#222.243.110.134#222.243.110.137#222.243.110.135#222.243.110.136#61.188.191.80#61.188.191.79#182.140.147.83#182.140.147.82#61.185.133.245#118.180.15.14#118.180.15.15#61.185.133.242#61.185.133.243#61.185.133.244#125.78.240.164#125.78.240.165#125.78.240.166#125.78.240.167#61.54.219.17#60.210.18.66#60.210.18.53#60.210.18.67#61.54.219.22#113.5.251.118#218.60.98.207#218.60.98.218#218.60.98.139#113.5.251.117#218.60.98.204#113.5.251.116#61.54.219.36#112.91.29.133#112.91.29.134#112.91.29.145#175.43.20.64#175.43.20.65#61.54.219.16#125.39.1.135#111.161.22.28#125.39.1.134#122.136.46.119#122.136.46.120#60.220.212.25#60.220.212.27#123.138.60.230#123.138.60.231#60.220.212.23#60.220.212.24#121.17.124.35#121.17.124.34#121.17.124.32#121.17.124.39#60.210.18.60#112.84.105.51#112.84.105.52#NA61.188.191.80#NA61.188.191.79#202.171.253.98#61.135.175.84#113.107.236.12#60.28.236.112#60.28.236.116#113.107.56.85#106.120.170.19#211.162.127.94#211.162.127.78#211.162.127.93#113.107.56.83#113.229.252.37#211.162.127.97#211.162.127.95#124.163.221.44#222.88.93.137#221.238.252.180#218.203.13.120#218.108.232.163#218.108.232.165#218.108.232.164#122.143.27.180#61.156.243.11#61.156.243.12#61.156.243.13#61.156.243.14#61.164.242.52#122.227.2.10#211.162.127.77#221.180.151.113#60.211.208.218#60.214.70.26#183.221.245.19#122.224.7.38#122.224.7.58#122.224.7.57#122.224.7.59#122.224.7.56#120.192.90.225#101.44.11.5#183.250.179.11#183.61.133.13#119.146.200.10#119.146.200.11#119.146.200.12#60.212.19.24#218.92.227.96#218.92.227.95#218.92.227.94#125.39.19.30#218.108.232.26#218.108.149.199#218.108.232.27#218.108.149.200#113.107.236.18#113.107.236.16#113.107.236.15#113.107.236.17#61.153.56.172#124.163.221.33#124.163.221.36#124.163.221.38#124.163.221.34#211.142.194.7#211.142.194.16#119.146.200.13#219.138.64.51#113.140.51.24#119.146.200.9#60.210.18.16#183.221.245.29#113.107.236.19#119.146.200.16#121.11.94.157#121.11.94.158#183.221.245.21#183.221.245.18#125.219.33.163#183.221.245.16#183.221.245.17#123.159.205.159#60.28.236.101#60.28.236.98#60.28.236.99#60.28.236.100#60.174.241.85#112.84.105.50#112.84.105.46#122.224.7.60#223.87.1.52#125.39.19.21#125.39.19.24#125.39.19.25#125.39.19.23#123.138.60.143#211.161.149.37#220.178.229.34#220.178.229.42#220.178.229.43#220.178.229.50#220.178.229.65#220.178.229.80#220.178.229.95#220.178.229.110#113.107.56.38#112.25.35.6#60.210.18.36#58.51.95.143#182.140.147.76#12.113.50.17#27.24.190.79#27.24.190.80#27.24.190.81#27.24.190.82#27.24.190.83#27.24.190.84#27.24.190.85#27.24.190.86#27.24.190.87#27.24.190.88#27.24.190.90#36.250.64.23#36.250.64.24#36.250.64.25#36.250.64.26#36.250.64.27#36.250.64.28#36.250.64.50#36.250.64.52#58.222.18.70#58.222.18.71#58.222.18.72#58.222.18.73#58.222.18.77#58.222.18.78#58.222.18.79#58.222.18.80#58.222.18.81#58.222.18.82#58.222.18.97#60.28.236.102#60.28.236.103#60.28.236.104#60.28.236.105#60.28.236.106#60.28.236.107#60.174.175.4#60.174.175.5#60.174.175.6#60.174.175.9#60.174.175.11#60.174.175.17#60.174.175.18#60.174.175.24#60.174.175.30#60.174.232.174#60.174.232.186#60.174.232.187#60.174.232.188#60.174.232.189#60.174.232.190#60.174.232.191#60.174.232.234#60.174.248.118#60.211.208.202#60.211.209.246#60.212.19.35#61.146.152.113#61.147.89.96#61.153.56.192#61.156.243.137#61.157.124.251#106.120.170.13#106.120.170.14#106.120.170.15#106.120.170.16#111.1.53.195#111.1.53.196#111.1.53.198#111.1.53.199#111.1.53.220#111.1.59.37#111.1.59.38#111.1.59.39#111.1.59.40#111.1.59.41#111.1.59.42#111.1.59.59#111.14.70.23#111.22.18.80#111.125.92.28#111.150.197.55#111.150.197.56#112.25.35.3#112.194.138.176#112.253.19.147#113.107.56.25#113.107.56.26#113.107.56.29#113.107.56.30#113.107.56.31#113.107.56.32#113.107.56.33#113.107.56.34#113.107.56.35#113.107.56.36#113.107.56.97#113.107.236.13#113.107.236.14#113.107.236.20#113.107.236.28#113.107.236.29#113.207.63.139#113.207.63.140#113.207.63.141#113.207.63.142#113.207.63.143#113.207.63.162#115.231.82.35#119.146.200.14#119.146.200.15#119.146.200.17#119.146.200.26#120.192.92.36#120.192.92.42#120.192.92.197#121.11.92.151#121.11.151.22#121.11.151.23#121.11.151.24#121.11.151.25#121.11.151.26#121.11.151.27#121.11.151.28#121.11.151.30#121.11.151.32#121.11.151.35#121.11.151.38#121.11.151.96#121.18.230.41#122.224.7.61#122.228.246.70#122.228.246.71#122.228.246.72#122.228.246.73#122.228.246.74#122.228.246.75#122.228.246.76#122.228.246.77#122.228.246.78#122.228.246.88#122.228.246.89#122.228.246.91#122.228.246.92#122.228.246.93#122.228.246.94#123.138.60.183#123.138.60.234#123.150.197.55#123.150.197.56#124.126.126.99#124.126.126.109#124.163.221.35#124.163.221.37#124.163.221.39#124.163.221.40#124.163.221.41#124.163.221.42#124.254.47.179#124.254.47.180#124.254.47.181#124.254.47.186#124.254.47.187#124.254.47.188#124.254.47.194#124.254.47.195#124.254.47.196#124.254.47.197#124.254.47.198#124.254.47.199#124.254.47.200#125.39.19.17#125.39.19.18#125.39.19.19#125.39.19.20#125.39.19.22#125.211.213.134#125.221.95.114#175.43.20.59#180.168.41.175#183.221.245.20#183.221.245.27#183.221.245.28#183.221.245.30#183.221.245.31#183.250.179.10#183.250.179.14#183.250.179.22#202.107.244.71#202.107.244.72#202.107.244.73#202.107.244.74#202.122.145.86#202.171.253.101#210.34.129.218#210.39.4.24#211.138.121.102#211.138.121.103#211.138.121.104#211.138.121.105#211.138.121.106#211.138.121.107#211.138.121.108#211.138.121.109#211.138.121.110#211.142.194.28#211.161.80.211#211.161.81.209#211.162.57.76#211.162.57.77#218.92.227.97#218.92.227.98#218.92.227.99#218.92.227.100#218.92.227.101#218.92.227.102#218.92.227.103#218.92.227.120#218.205.75.100#218.205.75.102#218.205.75.191#218.206.207.66#220.168.132.61#220.168.132.62#220.178.229.45#221.13.19.76#221.130.17.39#221.130.17.40#221.130.17.42#221.179.172.4#221.179.172.5#221.179.172.6#221.179.172.7#221.238.252.174#221.238.252.175#221.238.252.176#221.238.252.177#222.88.93.153#222.88.93.154#222.88.93.155#222.88.93.156#222.88.93.157#222.88.93.158#222.198.122.4#222.211.64.35#222.216.188.85#222.243.88.155#222.243.88.156#222.243.88.157#222.243.88.158#222.243.88.159#222.243.88.160#222.243.88.161#222.243.88.162#222.243.88.163#222.243.88.164#222.243.88.168#222.243.110.35#222.243.110.36#222.243.110.37#222.243.110.38#222.243.110.39#222.243.110.40#222.243.110.41#222.243.110.42#222.243.110.43#222.243.110.44#222.243.110.152#222.243.110.153#222.243.110.154#222.243.110.155#222.243.110.201#222.243.110.202#222.243.110.203#222.243.110.204#222.243.110.205#222.243.110.206#222.243.110.215#223.82.246.18#223.82.246.19#223.82.246.20#223.82.246.21#223.82.246.22#223.82.246.30";
    //"202.171.253.98#121.11.94.183#183.61.133.46#61.135.175.84#123.125.92.28#113.107.236.12#61.164.241.46#125.39.19.91#60.28.236.112#60.28.236.116#61.54.219.8#182.118.15.39#101.227.66.158#121.205.7.52#125.78.240.189#220.162.97.165#***************#**************#119.84.113.55#113.57.187.29#119.36.195.228#112.65.220.39#59.63.173.166#58.59.19.27#61.155.141.17#163.177.242.54#112.91.29.16#222.216.188.94#116.10.190.62#60.211.208.236#60.214.70.66#121.11.92.178#118.186.9.76#118.186.8.220#118.186.8.222#113.107.56.85#61.136.166.30#106.120.170.19#42.202.148.46#61.185.133.228#219.145.161.19#101.44.11.4#101.44.11.7#124.14.10.18#101.44.11.6#124.14.10.24#124.14.10.17#211.162.127.94#211.162.127.78#211.162.127.93#222.216.188.98#112.253.19.173#175.43.124.200#113.107.56.83#60.212.19.64#121.14.228.43#122.70.142.160#123.138.60.197#110.18.244.166#*************#175.43.20.80#218.61.27.54#113.229.252.37#211.162.127.97#211.162.127.96#211.162.127.95#116.77.73.49#116.77.73.50#116.77.73.53#111.9.120.166#111.9.120.176#111.9.120.179#**************#60.210.18.169#112.25.35.35#112.25.35.62#112.84.105.38#120.192.90.248#120.192.90.245#120.209.142.56#60.220.213.145#124.163.221.44#218.75.201.31#61.179.107.52#222.198.122.3#111.8.9.197#111.8.9.195#111.8.9.196#218.202.111.35#58.218.214.240#222.88.91.156#222.88.93.137#42.202.148.47#221.238.252.180#58.242.249.26#118.186.8.221#118.186.9.77#123.159.205.208#218.203.13.119#218.203.13.120#218.203.13.121#120.192.246.216#120.192.246.215#219.243.47.166#111.9.120.178#120.198.232.60#125.221.95.107#221.181.6.81#117.135.194.20#*************#61.138.219.43#112.117.216.47#202.204.80.76#211.142.194.88#218.108.232.163#218.108.232.165#218.108.232.164#218.206.207.71#218.206.207.70#218.206.207.69#111.11.27.88#111.11.27.82#218.206.207.68#218.92.220.65#183.61.140.172#218.92.220.64#61.154.102.243#61.154.102.245#218.92.220.66#183.61.140.173#61.154.102.244#183.61.140.171#122.228.228.176#58.51.95.157#222.216.188.86#211.144.81.21#202.102.254.189#60.210.11.8#60.222.223.70#122.143.27.182#60.222.223.74#122.143.27.180#60.210.11.7#202.102.254.190#60.222.223.71#60.210.11.6#122.143.27.181#202.102.254.188#60.210.11.78#120.192.246.198#120.192.246.197#120.192.246.196#120.192.246.199#211.144.81.22#211.144.81.20#210.32.3.131#60.222.223.59#219.72.153.15#61.156.243.11#61.156.243.12#61.156.243.13#61.156.243.14#61.179.107.18#61.179.107.19#61.179.107.20#61.179.107.17#60.214.70.33#60.214.70.35#60.214.70.31#60.214.70.32#124.205.178.59#202.118.10.111#61.164.242.52#120.192.90.234#120.192.90.238#120.192.90.235#120.192.90.232#120.192.90.233#120.192.90.239#61.164.241.26#61.164.241.33#58.216.21.99#61.164.241.25#122.227.2.11#122.227.2.10#122.227.2.13#42.202.148.38#61.164.241.32#42.202.148.35#42.202.148.36#218.2.83.177#115.231.82.101#218.2.83.198#115.231.84.89#211.162.127.77#14.18.17.75#183.250.179.36#120.192.92.58#222.216.188.89#218.202.111.36#221.180.151.113#222.216.188.30#116.10.190.35#222.216.188.28#222.216.188.27#*************#116.10.190.37#222.216.188.31#60.211.208.216#60.211.208.217#60.211.208.218#60.211.208.219#60.214.70.22#60.214.70.23#60.214.70.24#60.214.70.26#125.78.240.180#125.78.240.181#125.78.240.182#125.78.240.184#183.221.245.19#115.231.82.62#115.231.82.58#115.231.82.59#115.231.82.61#122.224.7.38#122.224.7.58#122.224.7.57#61.135.175.80#122.224.7.59#122.224.7.56#115.231.82.60#*************#61.145.118.18#120.192.90.225#101.44.11.5#218.206.207.67#120.192.90.237#120.192.90.231#120.198.232.57#120.198.232.56#120.198.232.58#120.198.232.55#183.250.179.23#183.250.179.12#183.250.179.25#183.250.179.13#183.250.179.26#120.192.92.41#117.59.224.8#60.222.223.65#218.2.83.175#218.2.83.176#218.2.83.178#218.2.83.180#120.192.92.44#125.221.95.111#125.221.95.113#120.192.246.200#183.250.179.24#183.250.179.11#220.168.132.113#222.216.188.84#218.2.83.179#183.61.133.12#121.11.92.156#121.11.92.153#121.11.92.154#183.61.133.13#183.61.133.14#121.11.92.155#112.253.19.161#112.253.19.162#119.146.200.10#119.146.200.11#119.146.200.12#121.14.228.12#121.14.228.32#122.70.142.141#122.70.142.142#122.70.142.144#183.61.133.11#220.162.97.157#222.216.188.25#60.212.19.24#218.92.227.96#175.43.124.142#112.253.19.158#175.43.124.143#222.216.188.23#175.43.124.145#220.162.97.155#222.216.188.24#60.212.19.25#112.253.19.159#218.92.227.95#122.70.142.145#222.216.188.22#60.212.19.26#220.162.97.158#112.91.29.7#60.212.19.27#121.14.228.31#175.43.124.146#121.14.228.112#112.91.29.9#112.91.29.11#112.91.29.10#220.162.97.156#218.92.227.94#211.142.194.67#211.142.194.68#211.142.194.72#211.142.194.69#61.154.102.212#183.61.140.199#106.3.62.195#125.39.19.30#218.60.98.161#101.23.128.16#218.108.232.26#218.108.149.199#218.108.232.27#218.108.149.200#120.192.92.40#113.107.236.18#113.107.236.16#113.107.236.15#113.107.236.17#112.91.29.8#101.26.37.107#113.57.187.18#113.57.187.16#113.57.187.15#113.57.187.17#101.227.66.155#101.227.66.153#101.227.66.152#101.227.66.154#61.136.166.7#61.136.166.8#183.61.133.43#61.136.166.6#61.136.166.9#121.11.94.163#*************#220.168.132.57#220.168.132.60#220.168.132.58#220.168.132.56#112.194.138.140#112.194.138.141#112.194.138.142#221.10.4.22#221.10.4.23#218.75.201.13#218.75.201.14#218.75.201.17#218.75.201.15#58.218.214.247#58.218.214.245#58.218.214.246#58.218.214.238#120.192.90.230#120.192.90.236#218.75.201.16#116.10.190.38#221.194.176.170#61.155.141.11#61.155.141.13#61.155.141.12#61.155.141.10#219.145.161.11#219.145.161.12#*************#219.145.161.10#123.125.92.20#123.125.92.22#61.135.175.79#61.135.175.81#61.135.175.78#121.18.230.43#121.18.230.44#121.18.230.45#219.145.161.13#61.153.56.172#116.211.118.34#*************#*************#*************#*************#*************#60.220.213.140#60.220.213.141#124.163.221.33#124.163.221.36#60.220.213.138#124.163.221.38#60.220.213.137#124.163.221.34#101.23.128.10#101.23.128.11#101.23.128.12#101.23.128.9#101.23.128.13#122.227.2.14#122.227.2.15#122.227.2.9#122.70.142.143#211.142.194.3#211.142.194.5#211.142.194.7#211.142.194.9#222.243.110.147#222.243.110.148#222.243.110.149#222.243.110.146#116.211.118.38#116.211.118.40#116.211.118.41#116.211.118.39#222.243.110.166#211.142.194.16#111.206.169.4#211.142.194.70#211.142.194.17#211.142.194.71#112.194.138.145#112.194.138.146#112.194.138.147#112.194.138.148#110.18.244.138#110.18.244.139#110.18.244.140#110.18.244.141#60.214.70.67#112.253.19.163#121.11.92.157#183.61.133.15#61.136.166.10#119.146.200.13#222.216.188.26#220.162.97.159#222.243.110.151#60.212.19.28#175.43.124.141#121.14.228.167#115.231.84.62#115.231.84.63#115.231.84.61#115.231.84.64#120.192.92.39#218.206.207.72#123.138.60.236#218.60.106.89#113.107.56.96#14.17.101.140#14.17.101.136#14.17.101.137#14.17.101.134#14.17.101.135#14.17.101.133#14.17.101.138#219.138.64.51#219.138.64.21#219.138.64.26#*************#219.138.64.25#61.185.133.173#61.185.133.158#61.185.133.202#**************#**************#**************#61.185.133.182#124.115.20.84#124.115.20.81#124.115.20.82#*************#61.185.133.169#**************#113.140.51.23#113.140.51.24#113.140.51.27#61.185.133.163#113.140.51.26#124.115.20.80#*************#*************#*************#*************#119.146.206.105#119.146.206.87#119.146.206.89#119.146.206.88#119.146.206.90#218.60.106.91#122.228.228.165#122.228.228.164#122.228.228.166#122.228.228.167#222.216.188.29#121.205.7.41#121.205.7.44#121.205.7.45#121.205.7.42#58.51.95.139#58.51.95.141#61.164.242.27#61.164.242.28#61.164.242.29#61.164.242.30#58.51.95.140#58.51.95.142#61.155.141.14#42.202.148.37#116.211.118.42#223.87.1.47#125.221.95.112#122.227.2.12#219.243.47.167#119.146.200.9#123.159.205.157#123.159.205.156#123.159.205.158#60.210.18.19#60.210.18.17#60.210.18.16#123.159.205.155#60.210.18.18#60.214.70.28#60.214.70.29#220.162.97.160#125.78.240.183#120.192.92.43#183.221.245.29#211.144.81.23#121.14.228.165#119.146.206.91#60.211.208.215#113.107.236.19#112.253.19.164#*************#119.84.113.42#*************#*************#112.117.216.69#112.117.216.70#112.117.216.72#220.165.142.35#220.165.142.38#112.117.216.71#220.165.142.37#119.146.200.16#122.227.2.87#121.11.94.155#223.87.1.42#121.11.94.156#106.3.62.194#121.11.94.157#121.11.94.154#210.39.4.18#121.11.94.158#121.11.94.159#183.221.245.21#223.87.1.45#220.165.142.40#*************#183.221.245.18#210.39.4.17#222.192.185.12#222.192.185.7#218.60.106.20#218.60.106.22#218.60.106.19#218.60.106.21#112.91.29.13#60.214.70.34#60.211.208.214#116.6.73.238#42.202.148.39#220.113.9.115#220.113.9.116#125.219.33.163#202.120.1.62#223.87.1.59#183.221.245.16#220.113.9.114#183.221.245.17#116.77.73.48#223.87.1.51#223.87.1.56#223.87.1.57#219.146.68.84#219.146.68.79#219.146.68.80#219.146.68.77#219.146.68.78#219.146.68.81#58.59.19.9#58.59.19.11#58.59.19.13#58.59.19.12#123.159.205.159#218.60.98.143#218.60.98.145#218.61.27.41#218.61.27.40#218.61.27.38#218.60.98.144#218.60.98.142#218.61.27.39#120.192.90.229#61.138.219.6#61.138.219.8#61.138.219.7#61.138.219.10#*************#*************#*************#*************#60.220.213.142#60.220.213.139#60.28.236.101#60.28.236.98#60.28.236.99#60.28.236.100#175.43.124.234#119.84.111.81#60.174.241.85#61.138.219.9#220.165.142.36#222.161.196.142#222.161.196.141#*************#112.84.105.50#112.84.105.46#112.84.105.47#112.84.105.48#42.202.148.43#42.202.148.42#42.202.148.41#42.202.148.44#58.216.21.26#58.216.21.23#58.216.21.24#58.216.21.25#122.224.7.60#58.216.21.22#122.228.228.169#60.174.241.29#60.174.241.31#60.174.241.33#60.174.241.30#222.216.188.32#223.87.1.52#222.243.110.150#125.39.19.74#125.39.19.21#125.39.19.24#125.39.19.79#125.39.19.73#125.39.19.76#125.39.19.25#125.39.19.23#119.146.206.92#125.39.19.78#125.39.19.75#113.229.252.33#113.229.252.28#113.229.252.29#113.229.252.30#211.142.22.26#211.142.22.25#211.142.22.24#115.231.84.65#36.250.74.10#124.115.20.141#183.57.144.43#218.92.2.48#60.221.254.195#111.121.194.26#218.92.226.44#175.174.63.9#60.210.11.73#122.143.27.152#222.174.239.46#121.18.238.198#123.138.60.143#123.138.60.144#211.161.149.37#61.155.141.9#218.92.226.95#218.92.226.93#218.92.226.96#218.92.226.94#61.54.219.34#61.54.219.38#61.54.219.39#182.118.15.16#182.118.15.14#182.118.15.17#182.118.15.18#61.54.219.75#111.206.169.13#111.206.169.14#111.206.169.15#111.206.169.16#218.92.221.210#220.178.229.34#220.178.229.42#220.178.229.43#220.178.229.50#220.178.229.65#220.178.229.80#220.178.229.95#220.178.229.110#218.92.221.198#117.21.189.108#119.84.111.83#114.80.143.158#218.92.221.197#218.92.221.201#218.92.221.196#122.136.46.114#122.136.46.106#122.136.46.117#163.177.242.29#163.177.242.30#163.177.242.32#163.177.242.33#175.43.20.56#175.43.20.58#175.43.20.57#175.43.20.55#119.146.206.102#58.59.19.10#42.202.148.40#58.241.123.43#218.61.27.42#223.87.1.54#113.207.21.43#113.107.56.37#113.107.56.41#113.107.56.42#113.107.56.39#113.107.56.40#218.26.67.75#218.26.67.31#218.26.67.32#218.26.67.33#218.26.67.34#59.63.173.142#59.63.173.141#59.63.173.143#59.63.173.144#113.107.56.38#60.220.213.143#101.227.66.151#112.25.35.2#112.25.35.4#112.25.35.5#112.25.35.6#112.25.35.52#112.25.35.53#112.25.35.54#112.25.35.56#218.92.221.200#112.25.35.55#218.92.226.98#60.210.18.36#60.210.18.39#61.164.242.32#58.51.95.143#183.61.140.142#183.61.140.140#183.61.140.139#183.61.140.141#183.61.140.143#*************#*************#*************#*************#171.111.152.11#182.118.15.15#183.61.140.174#111.206.169.17#123.125.92.24#111.206.169.18#222.216.188.21#175.43.20.60#175.43.124.144#61.54.219.43#**************#60.214.70.27#60.174.241.28#60.174.241.32#60.222.223.25#60.222.223.26#60.222.223.27#60.222.223.28#121.18.230.46#221.179.172.161#119.84.111.48#119.36.195.200#119.36.195.211#119.36.195.218#119.36.195.220#220.168.132.63#220.168.132.64#220.168.132.65#220.168.132.66#122.226.185.49#122.226.185.45#122.226.185.46#220.168.132.122#121.18.230.47#**************#**************#**************#182.140.147.76#**************#**************#**************#**************#61.153.56.142#61.153.56.140#61.153.56.141#61.153.56.139#**************#12.113.50.17#27.24.190.79#27.24.190.80#27.24.190.81#27.24.190.82#27.24.190.83#27.24.190.84#27.24.190.85#27.24.190.86#27.24.190.87#27.24.190.88#27.24.190.90#36.250.64.23#36.250.64.24#36.250.64.25#36.250.64.26#36.250.64.27#36.250.64.28#36.250.64.50#36.250.64.52#58.216.21.80#58.216.21.81#58.216.21.82#58.216.21.83#58.216.21.84#58.216.21.85#58.216.21.86#58.216.21.87#58.216.21.88#58.216.21.89#58.216.21.90#58.222.18.70#58.222.18.71#58.222.18.72#58.222.18.73#58.222.18.77#58.222.18.78#58.222.18.79#58.222.18.80#58.222.18.81#58.222.18.82#58.222.18.97#58.242.249.17#58.242.249.18#58.242.249.19#58.242.249.20#58.242.249.27#58.242.249.29#58.242.249.30#58.242.249.34#58.242.249.38#58.242.249.39#58.242.249.40#58.242.249.44#58.242.249.54#59.63.173.138#59.63.173.139#59.63.173.140#60.28.236.102#60.28.236.103#60.28.236.104#60.28.236.105#60.28.236.106#60.28.236.107#60.174.175.4#60.174.175.5#60.174.175.6#60.174.175.9#60.174.175.11#60.174.175.17#60.174.175.18#60.174.175.24#60.174.175.30#60.174.232.174#60.174.232.186#60.174.232.187#60.174.232.188#60.174.232.189#60.174.232.190#60.174.232.191#60.174.232.234#60.174.248.118#60.210.18.41#60.210.18.75#60.210.18.78#60.210.18.143#60.210.18.144#60.211.208.202#60.211.208.203#60.211.209.246#60.212.19.31#60.212.19.32#60.212.19.33#60.212.19.34#60.212.19.35#60.212.19.36#60.212.19.37#60.212.19.38#60.212.19.40#60.214.70.21#60.214.70.25#60.214.70.30#60.214.70.77#60.214.70.78#60.214.70.79#60.214.70.80#60.214.70.81#60.214.70.82#60.214.70.83#60.214.70.84#60.214.70.85#60.214.70.86#60.214.70.87#60.214.70.88#60.214.70.89#60.214.70.90#60.214.70.91#60.214.70.92#60.214.70.93#60.214.70.94#60.214.70.95#60.214.70.96#61.54.219.40#61.135.175.77#61.136.166.11#61.136.166.12#61.136.166.13#61.136.166.14#61.136.166.15#61.138.219.11#61.138.219.12#61.138.219.13#61.138.219.14#61.138.219.15#61.138.219.16#61.138.219.17#*************#61.146.152.113#61.147.89.96#61.153.56.192#61.155.141.15#61.155.141.16#61.156.243.137#61.157.124.251#61.164.242.31#61.164.242.71#61.164.242.72#61.164.242.73#61.179.107.21#61.185.133.170#61.188.191.19#*************#*************#*************#*************#*************#*************#*************#*************#101.23.128.14#101.23.128.15#101.23.128.17#101.44.11.8#101.44.11.9#101.227.66.147#101.227.66.148#101.227.66.149#101.227.66.150#106.3.62.198#106.120.170.13#106.120.170.14#106.120.170.15#106.120.170.16#110.18.244.26#110.18.244.27#110.18.244.28#110.18.244.63#110.18.244.76#110.18.244.137#110.18.244.142#110.18.244.167#110.18.244.168#110.18.244.169#111.1.53.195#111.1.53.196#111.1.53.198#111.1.53.199#111.1.53.220#111.1.59.37#111.1.59.38#111.1.59.39#111.1.59.40#111.1.59.41#111.1.59.42#111.1.59.59#111.9.120.165#111.9.120.177#111.14.70.23#111.22.18.80#111.125.92.28#111.150.197.55#111.150.197.56#112.25.35.3#112.25.35.7#112.25.35.8#112.25.35.9#112.25.35.30#112.65.220.17#112.65.220.18#112.65.220.19#112.65.220.20#112.65.220.21#112.65.220.22#112.65.220.31#112.65.220.32#112.84.105.49#112.91.29.12#112.194.138.139#112.194.138.143#112.194.138.144#112.194.138.149#112.194.138.150#112.194.138.173#112.194.138.176#112.253.19.147#112.253.19.160#112.253.19.165#112.253.19.166#112.253.19.167#113.21.242.153#113.57.187.12#113.57.187.13#113.57.187.14#113.57.187.19#113.57.187.20#113.57.187.21#113.107.56.25#113.107.56.26#113.107.56.29#113.107.56.30#113.107.56.31#113.107.56.32#113.107.56.33#113.107.56.34#113.107.56.35#113.107.56.36#113.107.56.97#113.107.236.13#113.107.236.14#113.107.236.20#113.107.236.28#113.107.236.29#113.207.63.139#113.207.63.140#113.207.63.141#113.207.63.142#113.207.63.143#113.207.63.162#115.156.188.137#115.156.188.138#115.156.188.152#115.231.82.31#115.231.82.32#115.231.82.33#115.231.82.34#115.231.82.35#115.231.82.36#115.231.82.37#115.231.82.38#115.231.82.39#115.231.82.40#115.231.82.41#115.231.82.63#116.10.190.34#116.10.190.39#116.10.190.55#117.135.194.17#117.135.194.19#117.135.194.22#119.36.195.199#119.36.195.201#119.36.195.207#119.36.195.212#119.36.195.217#119.36.195.219#*************#*************#119.84.113.50#*************#119.146.200.14#119.146.200.15#119.146.200.17#119.146.200.26#120.192.92.33#120.192.92.34#120.192.92.35#120.192.92.36#120.192.92.37#120.192.92.38#120.192.92.42#120.192.92.55#120.192.92.196#120.192.92.197#120.192.199.3#120.192.199.4#120.192.199.12#120.192.199.13#120.192.199.26#120.198.232.112#120.198.232.113#120.198.232.114#121.11.92.151#121.11.92.152#121.11.92.158#121.11.92.159#121.11.92.160#121.11.94.160#121.11.94.161#121.11.94.162#121.11.151.22#121.11.151.23#121.11.151.24#121.11.151.25#121.11.151.26#121.11.151.27#121.11.151.28#121.11.151.30#121.11.151.32#121.11.151.35#121.11.151.38#121.11.151.96#121.14.228.33#121.14.228.34#121.14.228.51#121.14.228.160#121.14.228.166#121.14.228.168#121.18.230.31#121.18.230.32#121.18.230.33#121.18.230.34#121.18.230.35#121.18.230.36#121.18.230.37#121.18.230.38#121.18.230.39#121.18.230.40#121.18.230.41#121.18.230.42#121.18.230.87#121.205.7.43#121.205.7.46#121.205.7.47#122.70.142.172#122.136.46.87#122.136.46.88#122.136.46.89#122.136.46.90#122.136.46.111#122.136.46.112#122.136.46.115#122.136.46.118#122.224.7.61#122.227.2.36#122.228.228.168#122.228.228.170#122.228.228.171#122.228.228.217#122.228.228.218#122.228.246.70#122.228.246.71#122.228.246.72#122.228.246.73#122.228.246.74#122.228.246.75#122.228.246.76#122.228.246.77#122.228.246.78#122.228.246.88#122.228.246.89#122.228.246.91#122.228.246.92#122.228.246.93#122.228.246.94#123.125.92.15#123.125.92.16#123.125.92.17#123.125.92.18#123.125.92.19#123.125.92.21#123.125.92.23#123.125.92.27#123.138.60.142#123.138.60.145#123.138.60.146#123.138.60.147#123.138.60.177#123.138.60.178#123.138.60.182#123.138.60.183#123.138.60.218#123.138.60.234#123.138.60.235#123.138.60.237#123.138.60.238#123.138.60.239#123.150.197.55#123.150.197.56#123.159.205.160#124.115.20.15#124.115.20.16#124.115.20.17#124.115.20.18#124.115.20.19#124.115.20.20#124.115.20.49#124.115.20.74#124.115.20.75#124.115.20.76#*************#124.115.20.78#124.115.20.79#124.115.20.85#124.115.20.94#**************#**************#124.115.20.137#124.115.20.138#124.115.20.139#124.115.20.140#124.115.20.205#**************#124.115.20.207#124.115.20.208#**************#124.115.20.210#124.115.20.227#124.126.126.81#124.126.126.83#124.126.126.85#124.126.126.89#124.126.126.91#124.126.126.93#124.126.126.95#124.126.126.97#124.126.126.99#124.126.126.101#124.126.126.103#124.126.126.105#124.126.126.108#124.126.126.109#124.163.221.35#124.163.221.37#124.163.221.39#124.163.221.40#124.163.221.41#124.163.221.42#124.254.47.179#124.254.47.180#124.254.47.181#124.254.47.186#124.254.47.187#124.254.47.188#124.254.47.194#124.254.47.195#124.254.47.196#124.254.47.197#124.254.47.198#124.254.47.199#124.254.47.200#125.39.19.17#125.39.19.18#125.39.19.19#125.39.19.20#125.39.19.22#125.39.19.77#125.211.213.134#125.221.95.114#162.105.28.232#162.105.28.233#163.177.242.34#175.25.243.17#175.25.243.19#175.43.20.59#175.43.124.231#175.43.124.232#175.43.124.233#175.43.124.235#175.43.124.236#175.43.124.237#175.43.124.238#175.43.124.239#175.43.124.240#175.43.124.241#180.168.41.175#180.180.248.185#182.118.15.13#*************#*************#**************#**************#**************#183.61.133.16#183.61.133.17#183.61.133.18#183.61.133.19#183.61.133.20#183.61.133.21#183.61.133.22#183.61.133.23#183.61.133.24#183.61.133.25#183.61.133.26#183.61.133.27#183.61.133.28#183.61.133.57#183.221.245.20#183.221.245.27#183.221.245.28#183.221.245.30#183.221.245.31#183.224.42.62#183.250.179.10#183.250.179.14#183.250.179.22#202.107.244.71#202.107.244.72#202.107.244.73#202.107.244.74#202.120.1.61#202.122.145.86#202.122.145.214#202.171.253.101#210.34.129.218#210.39.4.24#211.138.121.102#211.138.121.103#211.138.121.104#211.138.121.105#211.138.121.106#211.138.121.107#211.138.121.108#211.138.121.109#211.138.121.110#211.142.194.28#211.161.80.211#211.161.81.209#211.162.57.76#211.162.57.77#218.60.98.146#218.60.98.147#218.60.98.148#218.60.98.149#218.61.27.43#218.61.27.44#218.61.27.45#218.61.27.46#218.61.27.47#218.61.27.48#218.61.27.49#218.61.27.50#218.92.220.72#218.92.227.97#218.92.227.98#218.92.227.99#218.92.227.100#218.92.227.101#218.92.227.102#218.92.227.103#218.92.227.120#218.202.111.38#218.205.75.100#218.205.75.102#218.205.75.191#218.206.207.66#219.145.161.14#220.162.97.161#220.162.97.162#220.162.97.163#220.162.97.164#220.165.142.39#220.168.132.55#220.168.132.61#220.168.132.62#220.178.229.45#221.10.4.24#221.10.4.25#221.10.4.26#221.10.4.35#221.13.19.76#221.130.17.39#221.130.17.40#221.130.17.42#221.179.172.4#221.179.172.5#221.179.172.6#221.179.172.7#221.238.252.174#221.238.252.175#221.238.252.176#221.238.252.177#222.88.93.153#222.88.93.154#222.88.93.155#222.88.93.156#222.88.93.157#222.88.93.158#222.88.93.177#222.88.93.178#222.88.93.179#222.88.93.180#222.88.93.181#222.88.93.182#222.88.93.195#222.88.93.196#222.88.93.197#222.198.122.4#222.211.64.35#**************#222.216.188.85#222.243.88.155#222.243.88.156#222.243.88.157#222.243.88.158#222.243.88.159#222.243.88.160#222.243.88.161#222.243.88.162#222.243.88.163#222.243.88.164#222.243.88.168#222.243.110.35#222.243.110.36#222.243.110.37#222.243.110.38#222.243.110.39#222.243.110.40#222.243.110.41#222.243.110.42#222.243.110.43#222.243.110.44#222.243.110.152#222.243.110.153#222.243.110.154#222.243.110.155#222.243.110.201#222.243.110.202#222.243.110.203#222.243.110.204#222.243.110.205#222.243.110.206#222.243.110.215#223.82.246.18#223.82.246.19#223.82.246.20#223.82.246.21#223.82.246.22#223.82.246.30#223.87.1.40#223.87.1.44#223.87.1.46#223.87.1.53#223.87.1.58";

    //public static string ConfirmPassengerActionUrl
    //{
    //    get
    //    {
    //        return "https://dynamic.12306.cn/otsweb/order/confirmPassengerAction.do";
    //    }
    //}

    //public static string LoginUrl
    //{
    //    get
    //    {
    //        return "https://dynamic.12306.cn/otsweb/loginAction.do";
    //    }
    //}

    //public static string OrderAction
    //{
    //    get
    //    {
    //        return "https://dynamic.12306.cn/otsweb/order/myOrderAction.do";
    //    }
    //}

    //public static string PassengerUrl
    //{
    //    get
    //    {
    //        return "https://dynamic.12306.cn/otsweb/passengerAction.do";
    //    }
    //}

    //public static string CancelOrderUrl
    //{
    //    get
    //    {
    //        return "https://dynamic.12306.cn/otsweb/order/orderAction.do";
    //    }
    //}


    //public static string OTSWebMain
    //{
    //    get
    //    {
    //        return "https://dynamic.12306.cn/otsweb/";
    //    }
    //}

    //public static string QueryLeftTicketURL
    //{
    //    get
    //    {
    //        return "http://dynamic.12306.cn/otsquery/query/queryRemanentTicketAction.do";
    //    }
    //}

    //public static string strLoginKeyURL = "https://dynamic.12306.cn/otsweb/dynamicJsAction.do?jsversion=1836&method=loginJs";
    //public static string strQueryKeyURL = "https://dynamic.12306.cn/otsweb/dynamicJsAction.do?method=queryJs&jsversion=";
    //public static string strOrderKeyURL = "https://dynamic.12306.cn/otsweb/dynamicJsAction.do?jsversion=6481&method=orderJs";

    //public static string strPayFile = Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData).TrimEnd('\\') + "\\Order.html";



    #endregion
}
