echo off
chcp 65001

set strSite=oldfish oldfishhk oldfishaustraliacentral oldfishaustraliaeast oldfishaustraliasoutheast oldfishBrazilSouth oldfishcanadacentral oldfishcanadaeast oldfishCentralIndia oldfishCentralUS oldfisheastus oldfisheastus2 oldfishfrancecentral oldfishGermanyWestCentral oldfishIsraelCentral oldfishItalyNorth oldfishjp oldfishko oldfishkocentral oldfishkojpwest oldfishnorthcentralus oldfishnortheurope oldfishNorwayEast oldfishpolandcentral oldfishqatarcentral oldfishSouthAfricaNorth oldfishsouthcentralus oldfishsouthindia oldfishSwedenCentral oldfishswitzerlandnorth oldfishuaenorth oldfishuksouth oldfishukwest oldfishwestcentralus oldfishwesteurope oldfishwestus oldfishWestUS2 oldfishwestus3 oldfishxjp oldfishmexicocentral oldfishnewzealandnotrh oldfishspaincentral oldfishjp-1 oldfishjp-2 oldfishkocentral-1 oldfishkocentral-2 oldfishxjp-1 oldfishxjp-2 oldfishko-1 oldfishko-2 oldfishkojpwest-1 oldfishkojpwest-2 oldfishsouthindia-1 oldfishsouthindia-2 oldfishCentralIndia-1 oldfishCentralIndia-2 oldfishaustraliaeast-1 oldfishaustraliaeast-2 oldfishaustraliasoutheast-1 oldfishaustraliasoutheast-2 oldfishuaenorth-1 oldfishuaenorth-2 oldfishqatarcentral-1 oldfishqatarcentral-2 oldfishaustraliacentral-1 oldfishaustraliacentral-2

for %%i in (%strSite%) do (
    		"C:\Program Files\IIS\Microsoft Web Deploy V3\msdeploy.exe" -verb:sync -source:dirPath="C:\Users\<USER>\Desktop\Code.Process.Web",includeAcls=false -dest:dirpath=D:\home\site\wwwroot,ComputerName="https://%%i.scm.azurewebsites.net/msdeploy.axd?site=%%i",UserName=oldfish,Password=Aa@123456,AuthType='Basic' -verbose -retryAttempts:10 -retryInterval:2000
)

pause