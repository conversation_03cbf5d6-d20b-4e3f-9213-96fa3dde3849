﻿using CommonLib;
using System.Collections.Generic;
using System.Web;

namespace TableOcr
{
    /// <summary>
    /// https://cloud.baidu.com/doc/OCR/s/zjwvxzrw8/
    /// https://ai.baidu.com/forum/topic/show/954201
    /// </summary>
    public class BaiDuAPIRec : BaseTableRec
    {
        public BaiDuAPIRec()
        {
            OcrGroup = OcrGroupType.百度;
            OcrType = TableOcrType.百度云;
            MaxExecPerTime = 28;

            LstJsonPreProcessArray = new List<object>() { "result", "result_data|S", "forms", 0, "header|&|body|&|footer" };
            LstJsonResultProcessArray = new List<object>() { "word" };
            LstRowIndex = new List<object>() { "row" };
            LstColumnIndex = new List<object>() { "column" };
            RowIndexIsArray = true;

            RegAccount();
            //IsSupportUrlOcr = true;
        }

        private AccountDto account;

        private void RegAccount()
        {
            var lstAccount = new List<AccountDto>
            {
                new AccountDto
                {
                    clientId ="pe741pUxOAG1av01zk6hM5rK",
                    secretKey ="dRcL3OSPNstW0fxvChMlmpv8cabkWzqD",
                },
                new AccountDto
                {
                    clientId ="Cyb7moQatBXD42IFzE32fXI9",
                    secretKey ="nd60Zxt6qkDObEUvwf0r5CUtfltcLh27",
                },
                new AccountDto
                {
                    clientId ="5hM3gBr5a1M9YFBmwUZBwDTZ",
                    secretKey ="8CBMBalNp0CeuU2POttakt7HESKb0axs",
                },
                new AccountDto
                {
                    clientId ="iDeBwPb0lRxtRDSgnfA1HSlp",
                    secretKey ="xLUp2dmfdFoXhKR5aQY7CZVfpZyQ2xq2",
                },
                new AccountDto
                {
                    clientId ="GRhO7ZFGPuqVDGDOXcunDgmv",
                    secretKey ="Xlx27V21hGtuNOiozHyK6C6KVyzUFPAT",
                },
                new AccountDto
                {
                    clientId ="HG22jeLdj13Gz8GLNo6INiCI",
                    secretKey ="TQjWG97GPQBx9HbhGV0vmD7ipeNQkWGl",
                },
                new AccountDto
                {
                    clientId ="rmMynojL9KapDOikDTgKlImy",
                    secretKey ="3QKoI1E56u16tEMdwBnpXSPNezdoZWFD",
                },
                new AccountDto
                {
                    clientId ="aI62Wj4bu6ZOF46R7taLstZ8",
                    secretKey ="OUBy6goyji2IU4yWjlvlbFxXF6dKGgcP",
                },
                new AccountDto
                {
                    clientId ="aI62Wj4bu6ZOF46R7taLstZ8",
                    secretKey ="OUBy6goyji2IU4yWjlvlbFxXF6dKGgcP",
                },
                new AccountDto
                {
                    clientId ="Va5yQRHlA4Fq4eR",
                    secretKey ="0rDSjzQ20XUj5itV7WRtznPQS",
                },
                new AccountDto
                {
                    clientId ="r0GqRVXQH1W6FOcMa7dC",
                    secretKey ="6iXA4Km1K7bPsh7lVSK9uo2XIREA",
                },
            };
            AccountHelper.RegAccount(OcrType.GetHashCode(), lstAccount);
        }

        private const string strTokenSpilt = "\"access_token\":\"";

        private string strToken = "";

        private void InitToken()
        {
            if (account == null)
            {
                account = AccountHelper.GetAccount(OcrType.GetHashCode());
            }
            if (account != null && string.IsNullOrEmpty(strToken))
            {
                strToken = GetToken();
                if (string.IsNullOrEmpty(strToken) && account == null)
                {
                    InitToken();
                }
            }
        }
        private string GetToken()
        {
            var result = "";
            var token = WebClientSyncExt.GetHtml(string.Format("{0}?{1}"
                , "https://aip.baidubce.com/oauth/2.0/token"
                , "grant_type=client_credentials&client_id=" + account.clientId + "&client_secret=" + account.secretKey), ExecTimeOutSeconds);

            if (!string.IsNullOrEmpty(token))
            {
                if (token.Contains(strTokenSpilt))
                {
                    result = token.Substring(token.IndexOf(strTokenSpilt) + strTokenSpilt.Length);
                    result = result.Substring(0, result.IndexOf("\""));
                }
                else
                {
                    AccountHelper.ForbidAccount(OcrType.GetHashCode(), account.clientId, account.secretKey, true, token);
                    account = null;
                }
            }
            return result;
        }

        public override void Reset()
        {
            strToken = "";
            base.Reset();
        }

        protected override string GetHtml(OcrContent content)
        {
            return RequestHtmlContent(content.strBase64);
        }

        private string RequestHtmlContent(string strBase64, string imgUrl = null)
        {
            var result = string.Empty;
            InitToken();

            if (!string.IsNullOrEmpty(strToken))
            {
                var url = "https://aip.baidubce.com/rest/2.0/solution/v1/form_ocr/request";
                var strPost = string.Format("image={0}&is_sync=true&request_type=json&table_border=none"
                    , HttpUtility.UrlEncode(strBase64));
                result = WebClientSyncExt.GetHtml(url + "?access_token=" + strToken, strPost, ExecTimeOutSeconds);

                //Console.WriteLine("百度Result:" + strTmp);
                if (!string.IsNullOrEmpty(result))
                {
                    if (result.Contains("\"error_code\":1"))
                    {
                        strToken = "";

                        AccountHelper.ForbidAccount(OcrType.GetHashCode(), account.clientId, account.secretKey, false, result);
                        account = null;
                        result = "";
                    }
                }
            }
            return result;
        }
    }
}