﻿using System;
using System.Collections.Generic;
using System.Reflection;
namespace NewTicket
{
    internal enum Enum0
    {
        const_0,
        const_1
    }
    /*
    internal enum Enum1
    {
        const_0,
        const_1,
        const_2,
        const_3
    }*/

    //[EnumDescription("异常/错误严重级别")]
    public enum ErrorLevel
    {
        //[EnumDescription("致命的", 4)]
        Fatal = 0,
        //[EnumDescription("高", 3)]
        High = 1,
        //[EnumDescription("低", 1)]
        Low = 3,
        //[EnumDescription("普通", 2)]
        Standard = 2
    }
}