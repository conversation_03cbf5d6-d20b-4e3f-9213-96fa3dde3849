﻿<%@ Page Language="C#" AutoEventWireup="true" CodeBehind="Server.aspx.cs" Inherits="Code.MainSite.ServerStatus" %>

<%@ Import Namespace="CommonLib" %>
<%--https://discordstatus.com/--%>
<!DOCTYPE html>
<html>
<head runat="server">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta charset="utf-8">
    <title>OCR助手服务状态</title>
    <meta name="HandheldFriendly" content="True">
    <meta name="MobileOptimized" content="320">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0">
    <!-- Mobile IE allows us to activate ClearType technology for smoothing fonts for easy reading -->
    <meta http-equiv="cleartype" content="on">
    <!-- Le styles -->
    <link rel="stylesheet" media="screen" href="https://scm-file-new-1301864755.cos.ap-beijing.myqcloud.com/f0481b1f-3226-415a-bcc4-9b28e2fb0f9f/static/v1/css/0.8b61c27fbd03eea84593.css">
    <link rel="stylesheet" media="all" href="https://scm-file-new-1301864755.cos.ap-beijing.myqcloud.com/f0481b1f-3226-415a-bcc4-9b28e2fb0f9f/static/v1/css/status_manifest.css">
    <script src="https://scm-file-new-1301864755.cos.ap-beijing.myqcloud.com/f0481b1f-3226-415a-bcc4-9b28e2fb0f9f/static/v1/js/jquery-3.5.1.min.js"></script>
    <script>window.pageColorData = { "blue": "#3498DB", "border": "#E0E0E0", "body_background": "#FFFFFF", "font": "#2E3338", "graph": "#5865F2", "green": "#3BA55C", "light_font": "#72767D", "link": "#5865F2", "orange": "#F26522", "red": "#ED4245", "yellow": "#CB8615", "no_data": "#B3BAC5" };</script>
    <style>
        body,
        .layout-content.status.status-api .section .example-container .example-opener .color-secondary,
        .grouped-items-selector,
        .layout-content.status.status-full-history .history-nav a.current,
        div[id^="subscribe-modal"] .modal-footer,
        div[id^="subscribe-modal"],
        #uptime-tooltip .tooltip-box {
            background-color: #FFFFFF;
        }

        #uptime-tooltip .pointer-container .pointer-smaller {
            border-bottom-color: #FFFFFF;
        }

        body.status,
        .color-primary,
        .color-primary:hover,
        .layout-content.status-index .status-day .update-title.impact-none a,
        .layout-content.status-index .status-day .update-title.impact-none a:hover,
        .layout-content.status-index .timeframes-container .timeframe.active,
        .layout-content.status-full-history .month .incident-container .impact-none,
        .layout-content.status.status-index .incidents-list .incident-title.impact-none a,
        .incident-history .impact-none,
        .layout-content.status .grouped-items-selector.inline .grouped-item.active,
        .layout-content.status.status-full-history .history-nav a.current,
        .layout-content.status.status-full-history .history-nav a:not(.current):hover,
        div[id^="subscribe-modal"] .modal-header .close,
        .grouped-item-label,
        #uptime-tooltip .tooltip-box .tooltip-content .related-events .related-event a.related-event-link {
            color: #2E3338;
        }

        .layout-content.status.status-index .components-statuses .component-container .name {
            color: #2E3338;
            color: rgba(46,51,56,.8);
        }

        small,
        .layout-content.status .table-row .date,
        .color-secondary,
        .layout-content.status .grouped-items-selector.inline .grouped-item,
        .layout-content.status.status-full-history .history-footer .pagination a.disabled,
        .layout-content.status.status-full-history .history-nav a,
        #uptime-tooltip .tooltip-box .tooltip-content .related-events #related-event-header {
            color: #72767D;
        }

        body.status .layout-content.status .border-color,
        hr,
        .tooltip-base,
        .markdown-display table,
        div[id^="subscribe-modal"],
        #uptime-tooltip .tooltip-box {
            border-color: #E0E0E0;
        }

            div[id^="subscribe-modal"] .modal-footer,
            .markdown-display table td {
                border-top-color: #E0E0E0;
            }

            div[id^="subscribe-modal"] .modal-header .close:hover {
                color: #E0E0E0;
            }

            .markdown-display table td + td, .markdown-display table th + th {
                border-left-color: #E0E0E0;
            }

            div[id^="subscribe-modal"] .modal-header,
            #uptime-tooltip .pointer-container .pointer-larger {
                border-bottom-color: #E0E0E0;
            }

            #uptime-tooltip .tooltip-box .outage-field {
                border-color: rgba(224,224,224,0.0);
            }

        .layout-content.status.status-index .status-day .update-title.impact-critical a,
        .layout-content.status.status-index .status-day .update-title.impact-critical a:hover,
        .layout-content.status.status-index .page-status.status-critical,
        .layout-content.status.status-index .unresolved-incident.impact-critical .incident-title,
        .flat-button.background-red {
            background-color: #ED4245;
        }

        .layout-content.status-index .components-statuses .component-container.status-red:after,
        .layout-content.status-full-history .month .incident-container .impact-critical,
        .layout-content.status-incident .incident-name.impact-critical,
        .layout-content.status.status-index .incidents-list .incident-title.impact-critical a,
        .status-red .icon-indicator,
        .incident-history .impact-critical,
        .components-container .component-inner-container.status-red .component-status,
        .components-container .component-inner-container.status-red .icon-indicator {
            color: #ED4245;
        }

        .layout-content.status.status-index .unresolved-incident.impact-critical .updates {
            border-color: #ED4245;
        }

        .layout-content.status.status-index .status-day .update-title.impact-major a,
        .layout-content.status.status-index .status-day .update-title.impact-major a:hover,
        .layout-content.status.status-index .page-status.status-major,
        .layout-content.status.status-index .unresolved-incident.impact-major .incident-title {
            background-color: #F26522;
        }

        .layout-content.status-index .components-statuses .component-container.status-orange:after,
        .layout-content.status-full-history .month .incident-container .impact-major,
        .layout-content.status-incident .incident-name.impact-major,
        .layout-content.status.status-index .incidents-list .incident-title.impact-major a,
        .status-orange .icon-indicator,
        .incident-history .impact-major,
        .components-container .component-inner-container.status-orange .component-status,
        .components-container .component-inner-container.status-orange .icon-indicator {
            color: #F26522;
        }

        .layout-content.status.status-index .unresolved-incident.impact-major .updates {
            border-color: #F26522;
        }

        .layout-content.status.status-index .status-day .update-title.impact-minor a,
        .layout-content.status.status-index .status-day .update-title.impact-minor a:hover,
        .layout-content.status.status-index .page-status.status-minor,
        .layout-content.status.status-index .unresolved-incident.impact-minor .incident-title,
        .layout-content.status.status-index .scheduled-incidents-container .tab {
            background-color: #CB8615;
        }

        .layout-content.status-index .components-statuses .component-container.status-yellow:after,
        .layout-content.status-full-history .month .incident-container .impact-minor,
        .layout-content.status-incident .incident-name.impact-minor,
        .layout-content.status.status-index .incidents-list .incident-title.impact-minor a,
        .status-yellow .icon-indicator,
        .incident-history .impact-minor,
        .components-container .component-inner-container.status-yellow .component-status,
        .components-container .component-inner-container.status-yellow .icon-indicator,
        .layout-content.status.manage-subscriptions .confirmation-infobox .fa {
            color: #CB8615;
        }

        .layout-content.status.status-index .unresolved-incident.impact-minor .updates,
        .layout-content.status.status-index .scheduled-incidents-container {
            border-color: #CB8615;
        }

            .layout-content.status.status-index .status-day .update-title.impact-maintenance a,
            .layout-content.status.status-index .status-day .update-title.impact-maintenance a:hover,
            .layout-content.status.status-index .page-status.status-maintenance,
            .layout-content.status.status-index .unresolved-incident.impact-maintenance .incident-title,
            .layout-content.status.status-index .scheduled-incidents-container .tab {
                background-color: #3498DB;
            }

        .layout-content.status-index .components-statuses .component-container.status-blue:after,
        .layout-content.status-full-history .month .incident-container .impact-maintenance,
        .layout-content.status-incident .incident-name.impact-maintenance,
        .layout-content.status.status-index .incidents-list .incident-title.impact-maintenance a,
        .status-blue .icon-indicator,
        .incident-history .impact-maintenance,
        .components-container .component-inner-container.status-blue .component-status,
        .components-container .component-inner-container.status-blue .icon-indicator {
            color: #3498DB;
        }

        .layout-content.status.status-index .unresolved-incident.impact-maintenance .updates,
        .layout-content.status.status-index .scheduled-incidents-container {
            border-color: #3498DB;
        }

        .layout-content.status.status-index .page-status.status-none {
            background-color: #3BA55C;
        }

        .layout-content.status-index .components-statuses .component-container.status-green:after,
        .status-green .icon-indicator,
        .components-container .component-inner-container.status-green .component-status,
        .components-container .component-inner-container.status-green .icon-indicator {
            color: #3BA55C;
        }

        a,
        a:hover,
        .layout-content.status-index .page-footer span a:hover,
        .layout-content.status-index .timeframes-container .timeframe:not(.active):hover,
        .layout-content.status-incident .subheader a:hover {
            color: #5865F2;
        }

        .flat-button,
        .masthead .updates-dropdown-container .show-updates-dropdown,
        .layout-content.status-full-history .show-filter.open {
            background-color: #5865F2;
        }

        .components-section .components-uptime-link {
            color: #72767d;
        }

        .layout-content.status .shared-partial.uptime-90-days-wrapper .legend .legend-item {
            color: #72767d;
            opacity: 0.8;
        }

            .layout-content.status .shared-partial.uptime-90-days-wrapper .legend .legend-item.light {
                color: #72767d;
                opacity: 0.5;
            }

        .layout-content.status .shared-partial.uptime-90-days-wrapper .legend .spacer {
            background: #72767d;
            opacity: 0.3;
        }
    </style>

    <!-- custom css -->
    <link rel="stylesheet" type="text/css" href="https://scm-file-new-1301864755.cos.ap-beijing.myqcloud.com/f0481b1f-3226-415a-bcc4-9b28e2fb0f9f/static/v1/css/external20210513-26-1nfet2i.css">
    <!-- polyfills -->
    <script src="https://scm-file-new-1301864755.cos.ap-beijing.myqcloud.com/f0481b1f-3226-415a-bcc4-9b28e2fb0f9f/static/v1/js/polyfill.min.js"></script>
</head>
<body class="status index status-none">
    <div class="layout-content status status-index starter">
        <div class="container">
            <div class="page-status status-none">
                <span class="status font-large">OCR助手-服务状态
                </span>
                <span class="last-updated-stamp  font-small"></span>
            </div>
        </div>
    </div>
    <div class="layout-content status status-full-history starter">
        <div class="container history-container">
            <div class="history-nav border-color">
                <a class="button current border-color" id="zonglanTab" href="javascript:SwitchTab(1);">状态总览</a>
                <a class="button border-color" id="detailTab" href="javascript:SwitchTab(2);">OCR服务节点</a>
            </div>
        </div>
    </div>

    <div id="zonglan" style="display: block;">

        <div class="layout-content status status-index starter">
            <div class="container">
                <div class="components-section font-regular">
                    <i class="component-status hidden major_outage"></i>
                    <div class="components-uptime-link history-footer-link">
                        <%--近
                    <var data-var="num" data-pluralize="90">90</var>
                        天 运行状况。--%>
                    </div>
                    <div class="components-container one-column">

                        <div class="component-container border-color">

                            <div data-component-id="354mn7xfxz1h" class="component-inner-container status-green showcased" data-component-status="operational" data-js-hook="">
                                <span class="name">OCR识别服务
                                </span>
                                <span class="tooltip-base tool" title="OCR文字识别相关服务">?</span>
                                <span class="component-status " title="">正常运行
                                </span>
                                <span class="tool icon-indicator fa fa-check" title="Operational"></span>
                                <div class="shared-partial uptime-90-days-wrapper">
                                    <svg class="availability-time-line-graphic" id="uptime-component-354mn7xfxz1h" preserveaspectratio="none" height="34" viewbox="0 0 448 34">
                                        <%for (int i = 0; i < 90; i++)
                                            {
                                        %>
                                            <rect height="34" width="3" x="<%=i*5 %>" y="0" fill="#3ba55c" class="uptime-day component-354mn7xfxz1h day-<%=i %>" data-html="true"></rect>
                                        <%
                                            } %>
                                    </svg>
                                    <div class="legend ">
                                        <div class="legend-item light legend-item-date-range">
                                            <span class="availability-time-line-legend-day-count">90</span> 天前
                                        </div>
                                        <div class="spacer"></div>
                                        <div class="legend-item legend-item-uptime-value legend-item-354mn7xfxz1h">
                                            <span id="uptime-percent-354mn7xfxz1h">100.0
                                            </span>
                                            <var data-var="uptime-percent">% 正常运行时间</var>
                                        </div>
                                        <div class="spacer"></div>
                                        <div class="legend-item light legend-item-date-range">今天</div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="component-container border-color">

                            <div data-component-id="rhznvxg4v7yh" class="component-inner-container status-green showcased" data-component-status="operational" data-js-hook="">
                                <span class="name">账户及其他服务
                                </span>
                                <span class="tooltip-base tool" title="提供用户登录注册及消息通知等相关的服务">?</span>
                                <span class="component-status " title="">正常运行
                                </span>
                                <span class="tool icon-indicator fa fa-check" title="Operational"></span>
                                <div class="shared-partial uptime-90-days-wrapper">
                                    <svg class="availability-time-line-graphic" id="uptime-component-rhznvxg4v7yh" preserveaspectratio="none" height="34" viewbox="0 0 448 34">
                                        <%
                                            for (int i = 0; i < 90; i++)
                                            {
                                        %>
                                            <rect height="34" width="3" x="<%=i*5 %>" y="0" fill="#3ba55c" class="uptime-day component-rhznvxg4v7yh day-<%=i %>" data-html="true"></rect>
                                        <%
                                            } %>
                                    </svg>
                                    <div class="legend ">
                                        <div class="legend-item light legend-item-date-range">
                                            <span class="availability-time-line-legend-day-count">90</span> 天前
                                        </div>
                                        <div class="spacer"></div>
                                        <div class="legend-item legend-item-uptime-value legend-item-rhznvxg4v7yh">
                                            <span id="uptime-percent-rhznvxg4v7yh">100.0
                                            </span>
                                            <var data-var="uptime-percent">% 正常运行时间</var>
                                        </div>
                                        <div class="spacer"></div>
                                        <div class="legend-item light legend-item-date-range">今天</div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="custom-metrics-container" id="custom-metrics-container-ocrapi" style="margin-top: 20px;">
                            <%--<div class="timeframes-container" id="toggle-ocrapi">
                                <a class="font-largest no-link" id="system-metrics-ocrapi" href="javascript:void(0);"></a>
                                <a href="javascript:void(0);" class="timeframe color-secondary font-regular border-color" data-js-hook="data-time-period-toggle-ocrapi" data-time-period="month-ocrapi">月</a>
                                <a href="javascript:void(0);" class="timeframe color-secondary font-regular border-color" data-js-hook="data-time-period-toggle-ocrapi" data-time-period="week-ocrapi">
                                    <span class="translation_missing" title="translation missing: en.week">周</span>
                                </a>
                                <a href="javascript:void(0);" class="timeframe active color-secondary font-regular border-color" data-js-hook="data-time-period-toggle-ocrapi" data-time-period="day-ocrapi">天</a>
                            </div>--%>
                            <div class="metrics-container">
                                <div class="metric border-color">
                                    <div class="metric-meta font-large">
                                        <div class="metric-name color-primary">
                                           OCR识别-API
                                        </div>

                                        <div data-js-hook="metrics-display-current-5k2rt9f7pmny" class="metric-average color-secondary"><span class="translation_missing" title="translation missing: en.fetching">正在加载…</span></div>
                                    </div>
                                    <div class="metrics-display-graph">
                                        <div class="graph-container" id="metrics-display-graph-container-5k2rt9f7pmny" data-js-hook="metrics-display-graph-container-5k2rt9f7pmny"></div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="custom-metrics-container" id="custom-metrics-container-ocr">
                            <%--<div class="timeframes-container" id="toggle-ocr">
                                <a class="font-largest no-link" id="system-metrics-ocr" href="javascript:void(0);"></a>
                                <a href="javascript:void(0);" class="timeframe color-secondary font-regular border-color" data-js-hook="data-time-period-toggle-ocr" data-time-period="month-ocr">月</a>
                                <a href="javascript:void(0);" class="timeframe color-secondary font-regular border-color" data-js-hook="data-time-period-toggle-ocr" data-time-period="week-ocr">
                                    <span class="translation_missing" title="translation missing: en.week">周</span>
                                </a>
                                <a href="javascript:void(0);" class="timeframe active color-secondary font-regular border-color" data-js-hook="data-time-period-toggle-ocr" data-time-period="day-ocr">天</a>
                            </div>--%>
                            <div class="metrics-container">
                                <div class="metric border-color">
                                    <div class="metric-meta font-large">
                                        <div class="metric-name color-primary">
                                           OCR识别-Service
                                        </div>

                                        <div data-js-hook="metrics-display-current-5k2rt9f7pmny2" class="metric-average color-secondary"><span class="translation_missing" title="translation missing: en.fetching">正在加载…</span></div>
                                    </div>
                                    <div class="metrics-display-graph">
                                        <div class="graph-container" id="metrics-display-graph-container-5k2rt9f7pmny2" data-js-hook="metrics-display-graph-container-5k2rt9f7pmny2"></div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <%--<div class="custom-metrics-container" id="custom-metrics-container-acc">
                            <div class="metrics-container">
                                <div class="metric border-color">
                                    <div class="metric-meta font-large">
                                        <div class="metric-name color-primary">
                                           账户服务-API
                                        </div>

                                        <div data-js-hook="metrics-display-current-5k2rt9f7pmny1" class="metric-average color-secondary"><span class="translation_missing" title="translation missing: en.fetching">正在加载…</span></div>
                                    </div>
                                    <div class="metrics-display-graph">
                                        <div class="graph-container" id="metrics-display-graph-container-5k2rt9f7pmny1" data-js-hook="metrics-display-graph-container-5k2rt9f7pmny1"></div>
                                    </div>
                                </div>
                            </div>
                        </div>--%>

                    </div>
                    <div class="component-statuses-legend font-small" style="display: block">
                        <div class="legend-item status-green">
                            <span class="icon-indicator fa fa-check"></span>
                            正常运行
                        </div>
                        <div class="legend-item status-yellow">
                            <span class="icon-indicator fa fa-minus-square"></span>
                            高负载
                        </div>
                        <div class="legend-item status-orange">
                            <span class="icon-indicator fa fa-exclamation-triangle"></span>
                            网络抖动
                        </div>
                        <div class="breaker"></div>
                        <div class="legend-item status-red">
                            <span class="icon-indicator fa fa-times"></span>
                            服务中断
                        </div>
                        <div class="legend-item status-blue">
                            <span class="icon-indicator fa fa-wrench"></span>
                            维护中
                        </div>
                    </div>
                </div>
                <div id="uptime-tooltip">
                    <div class="pointer-container">
                        <div class="pointer-larger"></div>
                        <div class="pointer-smaller"></div>
                    </div>
                    <div class="tooltip-box">
                        <div class="tooltip-content">
                            <div class="tooltip-close">
                                <i class="fa fa-times"></i>
                            </div>
                            <div class="date"></div>
                            <div class="outages">
                                <div class="outage-field major">
                                    <span class="label">
                                        <i class="component-status page-colors text-color major_outage"></i>
                                        服务中断
                                    </span>
                                    <span class="value-hrs"></span>
                                    <span class="value-mins"></span>
                                </div>
                                <div class="outage-field partial">
                                    <span class="label">
                                        <i class="component-status page-colors text-color partial_outage"></i>
                                        网络抖动
                                    </span>
                                    <span class="value-hrs"></span>
                                    <span class="value-mins"></span>
                                </div>
                                <div class="no-outages-msg">
                                    当天运行正常.
                                </div>
                                <div class="no-data-msg">
                                    当天无数据.
                                </div>
                                <div id="major-outage-group-count" class="outage-count">
                                    <i class="component-status page-colors text-color major_outage"></i>
                                    <span class="count"></span>
                                    当天服务器故障.
                                </div>
                                <div id="partial-outage-group-count" class="outage-count">
                                    <i class="component-status page-colors text-color partial_outage"></i>
                                    <span class="count"></span>
                                    当天部分服务器异常.
                                </div>
                            </div>
                            <div class="related-events">
                                <h3 id="related-event-header">Related</h3>
                                <ul id="related-events-list"></ul>
                            </div>
                            <div class="no-related-msg">
                                <p>当天运行正常.</p>
                            </div>
                        </div>
                    </div>
                </div>

            </div>

        </div>

    </div>

    <div id="detail" style="display: none;">

        <div class="layout-content status status-index starter">
            <!-- this is outside of the .container so that the cover photo can go full width on mobile -->
            <div class="container">
                <div class="components-section font-regular">
                    <i class="component-status hidden major_outage"></i>
                    <% var lstCode = CodeProcessHelper.GetServerStateInfos().OrderByDescending(p => p.DtUpdate).ToList();
                        var strNum = "①②③④⑤⑥⑦⑧⑨⑩⑪⑫⑬⑭⑮⑯⑰⑱⑲⑳㉑㉒㉓㉔㉕㉖㉗㉘㉙㉚㉛㉜㉝㉞㉟㊱㊲㊳㊴㊵㊶㊷㊸㊹㊺㊻㊼㊽㊾㊿";
                        int index = -1;
                    %>
                    <div class="components-container one-column">
                        <div class="component-container border-color">
                            <div data-component-id="354mn7xfxz1h" class="component-inner-container status-green showcased" data-component-status="operational" data-js-hook="">
                                <span class="name">OCR识别-服务节点
                                </span>
                                <span class="tooltip-base tool" title="提供OCR文字识别服务的服务器节点信息">?</span>
                                <span class="component-status " title="">
                                    共 <%=lstCode.Count %> 个 节点
                                </span>
                            </div>
                        </div>

                        <% foreach (var stateInfo in lstCode)
                            {
                                index++;
                        %>
                        <div class="component-container border-color">
                            <div class="component-inner-container status-green " data-component-status="operational" data-js-hook="component-group-opener">
                                <span class="name">
                                    <span><%=strNum[index]+"  "+ stateInfo.Desc+" IP:"+stateInfo.Ip %>
                                    </span>
                                </span>

                                <span class="component-status tool"><%=stateInfo.DtUpdate.ToString("yyyy-MM-dd HH:mm:ss") %>
                                </span>
                                <span class="tool icon-indicator fa fa-check" title="Operational"></span>
                            </div>

                            <!-- children components -->
                            <div class="child-components-container ">
                            </div>
                            <% foreach (var keyValue in stateInfo.Info)
                                {
                            %>
                            <div class="component-container border-color is-group">
                                <div class="component-inner-container status-green " data-component-status="operational" data-js-hook="component-group-opener">

                                    <span class="name">
                                        <span class="fa group-parent-indicator color-secondary font-small fa-plus-square-o"></span>
                                        <span>
                                            <%=keyValue.Key.ToString() %>
                                        </span>
                                    </span>


                                    <span class="component-status tool tooltipstered">
                                        <%="总节点："+keyValue.Value.Count +" [停用："+keyValue.Value.Count(p=>Equals(p.State,EnableState.禁用))+"]"%>
                                    </span>

                                    <span class="tool icon-indicator fa fa-check tooltipstered"></span>

                                </div>


                                <!-- children components -->
                                <div class="child-components-container ">

                                    <%
                                        var groupInfo = stateInfo.GetByOcrType(keyValue.Key);
                                        foreach (var groupValue in groupInfo)
                                        {
                                            var disCount = groupValue.Value.Count(p => Equals(EnableState.禁用, p.State));
                                            var itemText = string.Format("节点：{0}{1}", groupValue.Value.Count, disCount > 0 ? " [停用：" + disCount + "]" : "");
                                            var descText = string.Join("、", groupValue.Value.Where(p => Equals(EnableState.启用, p.State)).Take(5).Select(p => p.OcrName).ToArray());
                                    %>
                                    <div class="component-inner-container status-green " data-component-status="operational" data-js-hook="component-group-opener">

                                        <span class="name">
                                            <%=groupValue.Key.ToString().Replace("不限","其它") %>
                                        </span>
                                        <span class="tooltip-base tool" title="<%="启用节点："+descText+"等" %>">?</span>
                                        <span class="component-status " title="">
                                            <%=itemText %>
                                        </span>

                                        <span class="tool icon-indicator fa fa-check tooltipstered"></span>

                                    </div>
                                    <%
                                        } %>
                                </div>
                            </div>
                            <%
                                } %>
                        </div>
                        <%
                            } %>
                    </div>
                    <div class="component-statuses-legend font-small" style="display: block">
                        <div class="legend-item status-green">
                            <span class="icon-indicator fa fa-check"></span>
                            正常运行
                        </div>
                        <div class="legend-item status-yellow">
                            <span class="icon-indicator fa fa-minus-square"></span>
                            高负载
                        </div>
                        <div class="legend-item status-orange">
                            <span class="icon-indicator fa fa-exclamation-triangle"></span>
                            网络抖动
                        </div>
                        <div class="breaker"></div>
                        <div class="legend-item status-red">
                            <span class="icon-indicator fa fa-times"></span>
                            服务中断
                        </div>
                        <div class="legend-item status-blue">
                            <span class="icon-indicator fa fa-wrench"></span>
                            维护中
                        </div>
                    </div>
                </div>
            </div>

        </div>

    </div>

    <div id="cpt-notification-container"></div>

    <script type="text/javascript">
        function calculateViewbox(dayCount, rectWidth, rectPadding) {
            var viewBox = [];
            if (dayCount === 90) {
                viewBox.push(0);
            } else {
                var offset = 90 - dayCount;
                viewBox.push((offset * rectWidth) + (rectPadding * (offset))); // x origin
            }
            viewBox.push(0); // y origin
            viewBox.push((rectWidth * dayCount) + (rectPadding * (dayCount - 1))); // svg width
            viewBox.push(34); // svg height
            return viewBox.join(' ');
        }

        document.addEventListener('DOMContentLoaded', function () {

            var MAX_WIDTH_30_DAYS = 600,
                MAX_WIDTH_60_DAYS = 1024,
                svgs = document.getElementsByClassName('availability-time-line-graphic'),
                rects = svgs[0].getElementsByTagName('rect'),
                rectWidth = parseInt(rects[0].getAttribute('width')),
                rectPadding = parseInt(rects[1].getAttribute('x')) - parseInt(rects[0].getAttribute('x')) - rectWidth,
                throttled = false,
                delay = 150,
                timeoutId;

            function getKeyAndCount(width) {
                if (width <= MAX_WIDTH_30_DAYS) {
                    return { dayCount: 30, uptimeKey: 'thirty' }
                } else if (width <= MAX_WIDTH_60_DAYS) {
                    return { dayCount: 60, uptimeKey: 'sixty' }
                } else {
                    return { dayCount: 90, uptimeKey: 'ninety' }
                }
            }

            function setUptimeValue(values, uptimeKey) {
                var queryID = '.legend-item-' + values.component;
                var currentUptime = document.querySelector(queryID);
                if (currentUptime) {
                    // Faster than setting innerHTML to "" then adding nodes
                    var clone = currentUptime.cloneNode(false);
                    var uptimeSpan = document.createElement('span');
                    uptimeSpan.id = 'uptime-percent-' + values.component
                    uptimeSpan.innerText = values[uptimeKey]
                    clone.appendChild(uptimeSpan);
                    var appendText = document.createTextNode(' % 正常运行时间');
                    clone.appendChild(appendText);
                    currentUptime.parentNode.replaceChild(clone, currentUptime);
                }
            }

            function setDayCount(el, dayCount) {
                // Faster than setting innerHTML to "" then adding nodes
                var clone = el.cloneNode(false);
                var dateSpan = document.createElement('span')
                dateSpan.className = "availability-time-line-legend-day-count"
                dateSpan.innerText = dayCount;
                clone.appendChild(dateSpan);
                var appendText = document.createTextNode(' 天前');
                clone.appendChild(appendText);
                el.parentNode.replaceChild(clone, el);
            }

            function resizeSvgViewBoxes() {
                var width = window.innerWidth;
                var columnInfo = getKeyAndCount(width);
                var dayCount = columnInfo.dayCount,
                    uptimeKey = columnInfo.uptimeKey;
                var newViewboxValue = calculateViewbox(dayCount, rectWidth, rectPadding);

                // If a user quickly resizes from < 450 to > 900 without stopping,
                // it will retain the same 30 day info as it wont have changed, but this only
                // impacts 30 day display as it is the only one with shortened text
                if (newViewboxValue !== svgs[0].getAttribute('viewBox')) {
                    for (var i = 0; i < svgs.length; i++) {
                        var el = svgs[i];
                        if (el.getAttribute('viewBox') !== newViewboxValue) {
                            el.setAttribute('viewBox', newViewboxValue);
                        }
                    }

                    var dayCountElements = document.querySelectorAll('.legend-item-date-range:first-of-type');

                    for (var i = 0; i < dayCountElements.length; i++) {
                        setDayCount(dayCountElements[i], dayCount);
                    }

                    uptimeValues = [{ "component": "rhznvxg4v7yh", "ninety": 100.0, "sixty": 100.0, "thirty": 100.0 }, { "component": "354mn7xfxz1h", "ninety": 100.0, "sixty": 100.0, "thirty": 100.0 }];

                    for (var i = 0; i < uptimeValues.length; i++) {
                        setUptimeValue(uptimeValues[i], uptimeKey)
                    }

                    uptimeLinkVar = document.querySelector('.components-uptime-link > var')
                    if (uptimeLinkVar) {
                        uptimeLinkVar.innerHTML = dayCount;
                    }
                }
            }

            window.addEventListener('resize', function () {
                clearTimeout(timeoutId);
                timeoutId = setTimeout(function () {
                    resizeSvgViewBoxes();
                }, delay);
            });

            resizeSvgViewBoxes();
        });</script>
    <script type="text/javascript">
        var uptimeData = <%=CodeProcessHelper.GetServerStatus()%>
        var timeoutId;
        var monthStrings = ['一月', '二月', '三月', '四月', '五月', '六月', '七月', '八月', '九月', '十月', '十一月', '十二月'];
        var EVENT_MAX_LENGTH = 90;

        // Detect device (desktop vs. touch device)
        function touchDevice() {
            return 'ontouchstart' in window ||
                window.DocumentTouch && document instanceof window.DocumentTouch ||
                navigator.maxTouchPoints > 0 ||
                window.navigator.msMaxTouchPoints > 0;
        }

        // Class is in format day-<number>. Convert to just number
        function dayNumberFromClass(className) {
            return parseInt(className.split('-')[1]);
        }

        // Class is in format component-<code>. Convert to just code
        function componentCodeFromClass(className) {
            return className.split('-')[1];
        }

        // Convert number to string pixel measurement
        function intToPixels(number) {
            return number.toString() + 'px';
        }

        function truncate(str) {
            return str.substring(0, EVENT_MAX_LENGTH) +
                (str.length > EVENT_MAX_LENGTH ? '...' : '');
        }

        // Tooltip Handling class constructor
        function UptimeTooltipHandler(frameWidth) {
            this.visible = false;
            this.activeDay = {
                hovered: false
            };
            this.tooltip = document.getElementById('uptime-tooltip');
            this.frameWidth = frameWidth === undefined ? window.innerWidth : frameWidth;
            this.scrolling = false;

            window.addEventListener('mousemove', this.tooltipListener.bind(this));
            window.addEventListener('orientationchange', this.orientationListener.bind(this));

            // on tooltip creation, determine whether to display touch-specific controls
            var tooltipCloseButton = document.querySelector('.tooltip-close');

            if (touchDevice()) {
                var componentsContainer = document.querySelector('.components-container');
                componentsContainer.addEventListener('touchstart', this.handleTouch.bind(this));

                tooltipCloseButton.addEventListener('touchstart', this.unhoverTooltip.bind(this));
            } else {
                window.addEventListener('resize', this.resizeListener.bind(this));
                // classList not supported by IE < 9
                tooltipCloseButton.className += ' hidden';
            }

            // Handle toggle of group elements
            var groupComponents = document.querySelectorAll('[data-js-hook=component-group-opener]');
            for (var i = 0; i < groupComponents.length; i++) {
                groupComponents[i].addEventListener('click', this.hideTooltip.bind(this));
            }

            var tooltipBox = document.querySelector('#uptime-tooltip .tooltip-box');
            tooltipBox.addEventListener('mouseenter', this.mouseEnteredTooltip.bind(this));
            tooltipBox.addEventListener('mouseleave', this.unhoverTooltip.bind(this));
        }

        UptimeTooltipHandler.prototype.tooltipListener = function (event) {
            if (!this.tooltipHovered) {
                this.updateHoveredDay(event);
                this.updateTooltip(event);
            }
        }

        // this handler will accommodate for mobile orientation change
        UptimeTooltipHandler.prototype.orientationListener = function (event) {
            // just close the tooltip
            this.unhoverTooltip();
        }

        UptimeTooltipHandler.prototype.resizeListener = function (event) {
            this.frameWidth = window.innerWidth;
        }

        UptimeTooltipHandler.prototype.handleTouch = function (event) {
            if (event.target.classList.contains('uptime-day')) {
                event.stopPropagation();
                this.bladeTouched(event);
            }
        }

        UptimeTooltipHandler.prototype.mouseEnteredTooltip = function () {
            // Necessary to clear the timeout set for closing the tooltip when the mouse
            // moves off the blade or timeline, so the tooltip isnt closed on hover
            clearTimeout(timeoutId);
            // Sets it to null so the timeout can be set later, as cleartimeout only
            // cancels the timer, and we need to allow it to be reset in the mouse
            // move handler below
            timeoutId = null;
            this.tooltipHovered = true;
        }

        UptimeTooltipHandler.prototype.unhoverTooltip = function () {
            this.tooltipHovered = false;
            this.activeDay.hovered = false;
            this.hideTooltip();
        }

        UptimeTooltipHandler.prototype.bladeTouched = function (event) {
            event.preventDefault();
            var classes = event.target.getAttribute('class').split(' ');
            var componentCode = componentCodeFromClass(classes[1])
            var index = dayNumberFromClass(classes[2]);

            // If open and tapped on same component and day, close tooltip
            if (this.visible && this.activeDay.component === componentCode && this.activeDay.index === index) {
                this.hideTooltip();
            } else {
                this.updateHoveredDay(event);
                this.updateTooltip(event);
            }
        }

        UptimeTooltipHandler.prototype.updateHoveredDay = function (event) {
            var classes = event.target.getAttribute('class'); // classList doesn't work in IE
            var onDay = classes != null && classes.split(' ').indexOf('uptime-day') !== -1;

            if (onDay) {
                classes = classes.split(' ');

                var componentCode = componentCodeFromClass(classes[1]);

                this.activeDay = {
                    index: dayNumberFromClass(classes[2]),
                    component: componentCode,
                    bounds: event.target.getBoundingClientRect(),
                    isGroup: uptimeData[componentCode].component.isGroup,
                    hovered: true
                }
            } else {
                this.activeDay.hovered = false;
            }
        }

        UptimeTooltipHandler.prototype.updateTooltip = function (event) {
            var classes = event.target.getAttribute('class'); // classList doesn't work in IE
            var hoveredOnGraphic = classes != null && classes.split(' ').indexOf('availability-time-line-graphic') !== -1;

            if (this.activeDay.hovered) {
                this.updateTooltipData();
                this.positionTooltip();
            } else if (this.visible && !this.activeDay.hovered && !hoveredOnGraphic) {
                // Important: since this is on mouse move it will be called multiple times
                // which will clear timeoutId and reset it to the new value, meaning
                // it is a race condition to cancel it
                if (!timeoutId) {
                    var _this = this;
                    timeoutId = setTimeout(function () {
                        _this.hideTooltip();
                        timeoutId = null;
                    }, 250);
                }
            }
        }

        UptimeTooltipHandler.prototype.updateTooltipData = function () {
            // Get the data for the day we're hovered on
            var day = uptimeData[this.activeDay.component].days[this.activeDay.index];

            // Update the date for the tooltip
            var date = new Date(day.date);

            // Get the component's start date.  Note that it will be undefined here unless it is populated in our database
            var startDay = uptimeData[this.activeDay.component].component.startDate;
            var startDate = startDay ? new Date(startDay) : null;

            // Determine whether current date falls before component's start date.
            var beforeStartDate = startDate ? date.getTime() < startDate.getTime() : false;

            // UTC necessary since days are passed yyyy-mm-dd, and new Date uses midnight UTC, so local times
            // are presented as the day before
            var dateString = date.getUTCDate() + " " + monthStrings[date.getUTCMonth()] + " " + date.getUTCFullYear();
            document.querySelector('#uptime-tooltip .date').innerHTML = dateString;

            // Update the outage fields
            if (this.activeDay.isGroup) {
                this.updateGroupOutageFields()
            } else {
                this.updateOutageFields(day.outages.p, day.outages.m, day.related_events, beforeStartDate);
            }
        }

        UptimeTooltipHandler.prototype.hoursFromSeconds = function (s) {
            return Math.floor(s / 3600);
        }

        UptimeTooltipHandler.prototype.minutesFromSeconds = function (s) {
            // If less than a minute, round up to 1 minute to show that some outage existed
            if (s > 0 && s < 60) {
                return 1;
            }

            // Otherwise use floor
            return Math.floor((s % 3600) / 60);
        }

        UptimeTooltipHandler.prototype.updateGroupOutageFields = function () {
            // Hide time info
            document.querySelector('#uptime-tooltip .outage-field.major').style.display = 'none';
            document.querySelector('#uptime-tooltip .outage-field.partial').style.display = 'none';
            document.querySelector(".related-events h3").style.display = 'none';
            document.querySelector('.no-related-msg').style.display = 'none';

            var eventList = document.getElementById("related-events-list")
            var cloneList = eventList.cloneNode(false);
            eventList.parentNode.replaceChild(cloneList, eventList);

            var partialCount = 0;
            var majorCount = 0;

            /**
               We were originally using the operationalCount as part of the no outage copy for group components,
               but ultimately decided not to use it. I opted to leave the variable in place in case we ever
               decide to use it in the future.
             */
            var operationalCount = 0;
            var noDataCount = 0;
            var showcasedComponentsCount = 0;

            var components = uptimeData[this.activeDay.component].component.group

            for (var i = 0; i < components.length; i++) {
                if (!uptimeData[components[i]]) continue;

                showcasedComponentsCount++;

                var outages = uptimeData[components[i]].days[this.activeDay.index].outages;

                var currentDay = uptimeData[components[i]].days[this.activeDay.index];
                var currentDate = new Date(currentDay.date);

                // Get the component's start date.  Note that it will be undefined here unless it is populated in our database
                var startDay = uptimeData[components[i]].component.startDate;
                var startDate = startDay ? new Date(startDay) : null;

                if (outages.p) {
                    partialCount += 1;
                }

                if (outages.m) {
                    majorCount += 1;
                }

                // Only increase operational count if component has data for this day
                if (!outages.p && !outages.m) {
                    if (startDate && currentDate.getTime() < startDate.getTime()) {
                        noDataCount += 1;
                    }
                    else {
                        operationalCount += 1;
                    }
                }
            }

            document.querySelector('#major-outage-group-count').style.display = majorCount ? 'block' : 'none';
            document.querySelector('#partial-outage-group-count').style.display = partialCount ? 'block' : 'none';

            document.querySelector('#major-outage-group-count .count').innerText = majorCount + (majorCount === 1 ? " component" : " components");
            document.querySelector('#partial-outage-group-count .count').innerText = partialCount + (partialCount === 1 ? " component" : " components ");

            // Show no data message only if we do not have data for any showcased components in the group
            var showNoDataMessage = noDataCount === showcasedComponentsCount;

            // Show no outages message if we have data for the components and no outages in that data
            document.querySelector('#uptime-tooltip .no-outages-msg').style.display = (majorCount || partialCount || showNoDataMessage) ? 'none' : 'block';
            document.querySelector('#uptime-tooltip .no-data-msg').style.display = showNoDataMessage ? 'block' : 'none';
        }

        UptimeTooltipHandler.prototype.updateOutageFields = function (partial, major, relatedEvents, beforeStartDate) {
            // Hide group info
            document.querySelector('#major-outage-group-count').style.display = 'none';
            document.querySelector('#partial-outage-group-count').style.display = 'none';

            // Show the message that no outage present, if none is present
            if (partial || major || beforeStartDate) {
                document.querySelector('#uptime-tooltip .no-outages-msg').style.display = 'none';
            } else {
                document.querySelector('#uptime-tooltip .no-outages-msg').style.display = 'block';
            }

            if (beforeStartDate) {
                document.querySelector('#uptime-tooltip .no-data-msg').style.display = 'block';
            }
            else {
                document.querySelector('#uptime-tooltip .no-data-msg').style.display = 'none';
            }

            // Update partial outage field if an outage exists, otherwise hide it
            if (partial) {
                var hrs = this.hoursFromSeconds(partial);
                var mins = this.minutesFromSeconds(partial);
                document.querySelector('#uptime-tooltip .outage-field.partial .value-hrs').innerHTML = hrs.toString() + ' hrs';
                document.querySelector('#uptime-tooltip .outage-field.partial .value-mins').innerHTML = mins.toString() + ' mins';
                document.querySelector('#uptime-tooltip .outage-field.partial').style.display = 'flex';
            } else {
                document.querySelector('#uptime-tooltip .outage-field.partial').style.display = 'none';
            }

            // Update major outage field if an outage exists, otherwise hide it
            if (major) {
                var hrs = this.hoursFromSeconds(major);
                var mins = this.minutesFromSeconds(major);
                document.querySelector('#uptime-tooltip .outage-field.major .value-hrs').innerHTML = hrs.toString() + ' hrs';
                document.querySelector('#uptime-tooltip .outage-field.major .value-mins').innerHTML = mins.toString() + ' mins';
                document.querySelector('#uptime-tooltip .outage-field.major').style.display = 'flex';
            } else {
                document.querySelector('#uptime-tooltip .outage-field.major').style.display = 'none';
            }

            var eventList = document.getElementById("related-events-list")
            var cloneList = eventList.cloneNode(false);
            document.querySelector(".related-events h3").style.display = (relatedEvents.length ? 'block' : 'none');

            for (var i = 0; i < relatedEvents.length; i++) {
                var listItem = document.createElement("li");
                listItem.className = "related-event";
                var anchor = document.createElement("a");
                anchor.className = "related-event-link";
                anchor.href = "javascript:void(0);";


                var text = document.createTextNode(truncate(relatedEvents[i].name));
                anchor.appendChild(text);
                listItem.appendChild(anchor);
                cloneList.appendChild(listItem);
            }

            displayNoRelatedMsg = ((major || partial) && !relatedEvents.length);
            document.querySelector('.no-related-msg').style.display = (displayNoRelatedMsg ? 'block' : 'none');

            eventList.parentNode.replaceChild(cloneList, eventList);
        }

        UptimeTooltipHandler.prototype.positionTooltip = function () {
            this.calculatePointerCenter();
            this.calculateBoxPosition();

            // show tooltip
            this.tooltip.style.display = 'block';

            // position pointer
            var pointer = this.tooltip.getElementsByClassName('pointer-container')[0];
            pointer.style.left = intToPixels(this.pointerCenter.x - 8);
            pointer.style.top = intToPixels(this.pointerCenter.y - 5);

            // position display box
            var box = this.tooltip.getElementsByClassName('tooltip-box')[0];
            box.style.left = intToPixels(this.boxLeft);
            box.style.top = intToPixels(this.pointerCenter.y + 5);

            this.visible = true;
        }

        UptimeTooltipHandler.prototype.calculatePointerCenter = function () {
            var bounds = this.activeDay.bounds;
            var rectLeft = bounds.left + window.pageXOffset;
            var rectBottom = bounds.bottom + window.pageYOffset;
            var rectWidth = bounds.right - bounds.left;

            this.pointerCenter = {
                x: rectLeft + Math.floor(rectWidth / 2),
                y: rectBottom + 5
            }
        }

        UptimeTooltipHandler.prototype.calculateBoxPosition = function () {
            var sideWidth = 162.5;
            if (this.pointerCenter.x - sideWidth < 0) {
                this.boxLeft = 0;
            } else if (this.pointerCenter.x + sideWidth > this.frameWidth) {
                this.boxLeft = this.frameWidth - sideWidth * 2;
            } else {
                this.boxLeft = this.pointerCenter.x - sideWidth;
            }
        }

        UptimeTooltipHandler.prototype.hideTooltip = function () {
            this.tooltip.style.display = 'none';
            this.visible = false;
        }

        new UptimeTooltipHandler();
    </script>
    <script>
        function SwitchTab(index) {
            if (index == 1) {
                $("#zonglanTab").attr("class", "button current border-color");
                $("#detailTab").attr("class", "button border-color");
                $("#zonglan").css("display", "block");
                $("#detail").css("display", "none");
            } else {
                $("#zonglanTab").attr("class", "button border-color");
                $("#detailTab").attr("class", "button current border-color");
                $("#zonglan").css("display", "none");
                $("#detail").css("display", "block");
            }
        }
        $(function () {
            $('.tool').tooltipster({
                animationDuration: 100,
                contentAsHTML: true,
                delay: 100,
                theme: 'tooltipster-borderless',
                functionInit: function (instance, helper) {
                    var $origin = $(helper.origin),
                        dataOptions = $origin.attr('data-tooltip-config');
                    if (dataOptions) {
                        dataOptions = JSON.parse(dataOptions);
                        $.each(dataOptions, function (name, option) {
                            instance.option(name, option);
                        });
                    }
                }
            });
            // clicks on first tab in subscribe popout since we won't know which is first
            // upon construction in the ruby code
            $('.updates-dropdown-nav > a').eq(0).click();

            // twitter follow button needs some margin
            $('.twitter-follow-button').css('margin-right', '6px');
        });

        $(function () {
            // open/close component groups
            HRB.utils.djshook('component-group-opener').on('click', function () {
                $(this).find('.group-parent-indicator').toggleClass('fa-plus-square-o').toggleClass('fa-minus-square-o').end().parent().toggleClass('open');
            });
        });

        $(function () {
            $(document).on('ajax:complete', '.modal.in', function (e) {
                // Close the active modal.
                $('.modal.in').modal('hide');
            });
        });
    </script>
    <script src="https://scm-file-new-1301864755.cos.ap-beijing.myqcloud.com/f0481b1f-3226-415a-bcc4-9b28e2fb0f9f/static/v1/js/status_manifest.js"></script>
    <script src="https://scm-file-new-1301864755.cos.ap-beijing.myqcloud.com/f0481b1f-3226-415a-bcc4-9b28e2fb0f9f/static/v1/js/highstock.min.js"></script>
    <script>
        //<![CDATA[
        String.prototype.commafy = function () {
            return this.replace(/(^|[^\w.])(\d{4,})/g, function ($0, $1, $2) {
                return $1 + $2.replace(/\d(?=(?:\d\d\d)+(?!\d))/g, "$&,");
            });
        }

        Number.prototype.commafy = function () {
            return String(this).commafy();
        }

        $(function () {
            SP.currentPage.numberToDecimalPlaces = function (num, dec) {
                if (dec == 0) {
                    return Math.round(num).commafy();
                }

                newnum = num.toFixed(dec)
                var finalValue;

                // this gets rid of the 100.000% thing
                if (num == parseInt(newnum))
                    finalValue = Math.round(num);
                else
                    finalValue = newnum;

                return finalValue.commafy();
            }

            SP.currentPage.getDataForTimePeriod = function (period) {
                $.ajax({
                    type: "GET",
                    // this line must be end up with "//status.*" type of URLs (no protocol, just leading slashes). customers use SSL through us or by offloading with
                    // cloudflare or something like it and the request.protocol the server sees is different than what the browser sees
                    url: "code.ashx?op=status-_".replace('_', period)
                }).done(function (metricsDisplay, textStatus, xhr) {
                    metricsDisplay = JSON.parse(metricsDisplay)
                    var nowID = period.indexOf("ocr") != -1 ? (period.indexOf("ocrapi") != -1 ? "5k2rt9f7pmny" : "5k2rt9f7pmny2") : "5k2rt9f7pmny1";
                    var summary = metricsDisplay.summary
                        , $metricSummaryLabel = HRB.utils.djshook('metrics-display-current-' + nowID)
                        , $graphContainer = HRB.utils.djshook('metrics-display-graph-container-' + nowID);
                    var errorMsg = null;

                    if (summary === "unavailable") {
                        errorMsg = 'We\'re having issues retrieving data for <strong>API Response time</strong>.</div>';
                    } else if (summary) {
                        // we need to do a basic check to make sure we have any data at all
                        var hasDataRollupsAvailable = false;
                        for (var h = 0; h < metricsDisplay.metrics.length; h++) {
                            if (metricsDisplay.metrics[h].data.length > 0) {
                                hasDataRollupsAvailable = true;
                                break;
                            }
                        }
                        if (!hasDataRollupsAvailable) {
                            errorMsg = "Oops! No data has been indexed for <strong>API Response time</strong> for this time period yet.";
                        }
                    } else {
                        errorMsg = "Failed to load. <strong>API Response time</strong>";
                    }

                    if (errorMsg !== null) {
                        $metricSummaryLabel.text("--"); // display -- instead of number
                        $graphContainer.html('<div class="small"  style="text-align:center;">' + errorMsg + '</div>'); // removed .parent().find('.metric-meta').remove() so name still shows
                        $graphContainer.removeAttr("style");
                        return;
                    }

                    $graphContainer.show();
                    $graphContainer.attr("style", "height: 120px"); // set style after error message removed it
                    // proceed since we have data
                    $metricSummaryLabel.text(SP.currentPage.numberToDecimalPlaces(summary['mean'], 0) + ' ms');

                    var metricDataPoints = {}
                        , intervalMsec = metricsDisplay.period.interval * 1000;

                    var startingBucketMsec = parseInt((new Date()).getTime() / intervalMsec) * intervalMsec;

                    for (var h = 0; h < metricsDisplay.metrics.length; h++) {
                        metricDataPoints[metricsDisplay.metrics[h].metric.id] = []
                        var data = metricsDisplay.metrics[h].data
                            , currentBucketMsec = startingBucketMsec;

                        for (var i = metricsDisplay.period.count; i > 0; i--) {
                            // loop over the data and pull out the data point with the following characteristics
                            //  -> closest to currentBucketMsec
                            //  -> without being greater than currentMsecBucket
                            //  -> without being more than 1 intervalMsec bucket away
                            var valueToUse = null;
                            for (var j = data.length - 1; j >= 0; j--) {
                                var currentTimestampMsec = data[j].timestamp * 1000;
                                if (currentTimestampMsec <= currentBucketMsec && currentTimestampMsec > (currentBucketMsec - intervalMsec)) {
                                    valueToUse = data[j].value;
                                    // console.log("Using ts:" + currenttimestampMsec + " with value:" + valueToUse + " for bucket:" + currentBucketMsec);
                                    break;
                                }
                            }

                            // local time
                            var offset = -1 * (new Date()).getTimezoneOffset() * 60 * 1000;

                            metricDataPoints[metricsDisplay.metrics[h].metric.id].push([currentBucketMsec + offset, valueToUse])
                            currentBucketMsec -= intervalMsec;
                        }

                        metricDataPoints[metricsDisplay.metrics[h].metric.id].reverse();
                    }

                    // set Global options
                    Highcharts.setOptions({
                        lang: {
                            thousandsSep: ','
                        }
                    });

                    $('#metrics-display-graph-container-' + nowID).highcharts('StockChart', {
                        plotOptions: {
                            series: {
                                animation: false,
                                color: '#5865F2',
                                connectNulls: false
                            },
                        },

                        chart: {
                            backgroundColor: '#FFFFFF'
                        },

                        title: {
                            text: '',
                            style: {
                                display: 'none',
                            }
                        },

                        credits: {
                            enabled: false
                        },

                        exporting: {
                            enabled: false
                        },

                        rangeSelector: {
                            enabled: false
                        },

                        scrollbar: {
                            enabled: false
                        },

                        navigator: {
                            enabled: false
                        },

                        xAxis: {
                            gridLineColor: 'rgba(224,224,224,.8)',
                            labels: {
                                style: {
                                    color: '#72767D'
                                }
                            },
                            lineColor: '#E0E0E0',
                        },

                        yAxis: {
                            gridLineColor: 'rgba(224,224,224,.5)',
                            labels: {
                                align: 'left',
                                x: 5,
                                y: 3,
                                style: {
                                    color: '#72767D'
                                },
                                enabled: true
                            },
                            showLastLabel: true,
                            tickPixelInterval: 40,

                        },

                        series: [
                            {
                                name: 'API Response time',
                                data: metricDataPoints['pbb6x1yjh8s3'],
                                tooltip: {
                                    valueSuffix: ' ms'
                                },
                                enableMouseTracking: ($(window).outerWidth() > 480) && true
                            }

                        ],

                        tooltip: {
                            borderWidth: 0,
                            enabled: ($(window).outerWidth() > 480) && true,
                            headerFormat: "<span style='font-size: 10px'>{point.key}  </span>",
                            hideDelay: 300,
                            pointFormat: "<span style='color:{point.color}'>●</span>  <b>{point.y}</b><br/>",
                            positioner: function () { return { x: 0, y: 0 }; },
                            shadow: false,
                            shape: "square",
                            split: false
                        }
                    });
                });
            }

            var $timePeriodTogglesOcr = HRB.utils.djshook('data-time-period-toggle-ocr');
            var $timePeriodTogglesOcrApi = HRB.utils.djshook('data-time-period-toggle-ocrapi');
            //var $timePeriodTogglesAcc = HRB.utils.djshook('data-time-period-toggle-acc');

            SP.currentPage.activeTimePeriodToggle = function (period) {
                //if (period.indexOf("ocr") == -1) {
                //    $timePeriodTogglesAcc.removeClass('active');
                //    $timePeriodTogglesAcc.filter('[data-time-period="' + period + '"]').addClass('active');
                //} else
                if (period.indexOf("ocrapi") == -1) {
                    $timePeriodTogglesOcr.removeClass('active');
                    $timePeriodTogglesOcr.filter('[data-time-period="' + period + '"]').addClass('active');
                }else {
                    $timePeriodTogglesOcrApi.removeClass('active');
                    $timePeriodTogglesOcrApi.filter('[data-time-period="' + period + '"]').addClass('active');
                }
            }

            SP.currentPage.getAndDisplayInitialChartData = function () {
                    //SP.currentPage.getDataForTimePeriod('day-acc');
                    //SP.currentPage.activeTimePeriodToggle('day-acc');
                    SP.currentPage.getDataForTimePeriod('day-ocr');
                    SP.currentPage.activeTimePeriodToggle('day-ocr');
                    SP.currentPage.getDataForTimePeriod('day-ocrapi');
                    SP.currentPage.activeTimePeriodToggle('day-ocrapi');
            }

            $timePeriodTogglesOcr.on('click', function () {
                var newPeriod = $(this).attr('data-time-period');

                SP.currentPage.activeTimePeriodToggle(newPeriod);
                SP.currentPage.getDataForTimePeriod(newPeriod);
                window.location.hash = newPeriod;

                return false;
            });

            $timePeriodTogglesOcrApi.on('click', function () {
                var newPeriod = $(this).attr('data-time-period');

                SP.currentPage.activeTimePeriodToggle(newPeriod);
                SP.currentPage.getDataForTimePeriod(newPeriod);
                window.location.hash = newPeriod;

                return false;
            });

            //$timePeriodTogglesAcc.on('click', function () {
            //    var newPeriod = $(this).attr('data-time-period');

            //    SP.currentPage.activeTimePeriodToggle(newPeriod);
            //    SP.currentPage.getDataForTimePeriod(newPeriod);

            //    return false;
            //});

            SP.currentPage.getAndDisplayInitialChartData();
        })
        //]]>
    </script>
    <script src="https://scm-file-new-1301864755.cos.ap-beijing.myqcloud.com/f0481b1f-3226-415a-bcc4-9b28e2fb0f9f/static/v1/js/common.chunk.js"></script>
    <script src="https://scm-file-new-1301864755.cos.ap-beijing.myqcloud.com/f0481b1f-3226-415a-bcc4-9b28e2fb0f9f/static/v1/js/globals.chunk.js"></script>
    <script src="https://scm-file-new-1301864755.cos.ap-beijing.myqcloud.com/f0481b1f-3226-415a-bcc4-9b28e2fb0f9f/static/v1/js/runtime.js"></script>
</body>
</html>

