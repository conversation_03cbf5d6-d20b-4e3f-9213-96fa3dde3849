﻿<?xml version="1.0" encoding="utf-8"?>

<configuration>
  <configSections>
    <section name="Enterprise.Framework.Redis" type="Enterprise.Framework.Redis.Config.RedisConfigReader,Enterprise.Framework.Redis" />
  </configSections>
  <!--
    有关 web.config 更改的说明，请参见 http://go.microsoft.com/fwlink/?LinkId=235367。

    可在 <httpRuntime> 标记上设置以下特性。
      <system.Web>
        <httpRuntime targetFramework="4.5" />
      </system.Web>
  -->
  <system.web>
    <compilation targetFramework="4.5" debug="true" />
    <customErrors mode="Off" />
    <pages controlRenderingCompatibilityVersion="4.0" />
    <httpRuntime enableVersionHeader="false" />
  </system.web>
  <appSettings>
    <add key="NMaxCodeProcessThread" value="1" />
    <add key="IsEnableCode" value="True" />
    <add key="IsAutoFix" value="False" />
    <add key="IsRDSCahceEnable" value="True" />
    <add key="IsGoogleAPIEnable" value="False" />
    <add key="IsGoogleDevEnable" value="False" />
    <add key="GoogleDevRecHost" value="http://***************:911/" />
    <add key="IsCanRecHanZi" value="True" />
    <add key="IsCanRecImg" value="False" />
    <add key="IsRecOrder" value="True" />
    <add key="RecHanZiHost" value="**************:801" />
    <add key="RecImgHost" value="**************:802" />
    <add key="IsWait" value="False" />
    <add key="NWaitSecond" value="1000" />
    <add key="NMaxExecPerSecond" value="20" />
    <add key="IsJiSu" value="False" />
    <add key="IsZhiXing" value="True" />
    <add key="Is360" value="True" />
    <add key="IsGunagSU" value="False" />
    <add key="IsLocal" value="False" />
    <add key="IsCanProcessImg" value="False" />
    <add key="IsAddToProcessImg" value="False" />
    <add key="JiSuDaMaKey" value="XLlzVX8twlbFeJE4Xk9U35g839z14301|shGmvpnbnWE5c8rwimmnfNlwoi6FmU01|afGunzga1cG1xkbW3NE7wuYRHRxqv501|qyCwajsQ8aqNxKcFyqeoXAEMqiaRWz01|18TxJubCcqPL9DeXaWDxlIt4AG54cn01|sbJNcGT1qcAQFWCD3MxrKElYE2w0hG01|JkI9RyZTwUE4pKoMNO0vmAXThlavkI01|7y8a0PftuAIyLCRic4xb4svPnnGiVs01|dowVxzTY4AuyG4WSdyOmHmQidpy2y501" />
    <add key="StrSPath" value="七星瓢虫、中国结、丹顶鹤、人参、人民币、仙人球、仪表盘、保温杯、光盘、兔子、公路、冰箱、创可贴、刺猬、剪纸、加湿器、南瓜、印章、卷尺、台球、啤酒、喷泉、围巾、土豆、垃圾桶、塑料杯、塑料瓶、墨镜、天线、天鹅、太阳能、奖状、奶瓶、安全帽、小提琴、山楂、帐篷、床、恐龙、戒指、手套、手掌印、手电筒、扳手、报纸、披萨、拉链、排风机、摩天轮、收音机、斑马、星星、本子、杏仁、树叶、核桃、档案袋、桥、梳子、椰子、楼梯、榨汁机、樱桃、毛巾、毛线、气球、水管、沙漠、油、洋葱、海滩、海豚、海豹、海鸥、游泳圈、游泳池、游艇、漏斗、灭火器、灯塔、灯笼、烤鸭、热水瓶、煤油灯、熨斗、牙膏、狗、狮子、猕猴桃、猫、猫头鹰、球拍、生姜、电子秤、电热壶、电线、电视机、电话亭、电话机、电饭煲、白菜、皮球、盒子、盘子、矿泉水、磁铁、秋千、算盘、箭头、箱子、篮球、粽子、糖葫芦、紫砂壶、红枣、红绿灯、红豆、红酒、红领巾、纸牌、纽扣、绿豆、羽绒服、老虎、肥皂盒、胶卷、自行车、航母、芒果、花瓶、花生、花轿、苍蝇拍、茶几、药片、荷叶、菠萝、萝卜、葱、蒙古包、蒸笼、薯条、蚂蚁、蚊香、蛋挞、蜂蜜、蜜蜂、蜡烛、螃蟹、衣架、袋鼠、被子、裙子、西红柿、西装、警示牌、订书机、话梅、调色板、贝壳、轮胎、辣椒酱、钟表、钥匙圈、钻石、铃铛、锣、键盘、雕像、青椒、青蛙、鞋刷、鞭炮、风筝、饭盒、馄饨、骆驼、鱼缸、鲨鱼、鸭蛋、鹰、麻绳、龙舟、消防车、火箭、门把手、青菜、办公椅" />
  </appSettings>
  <Enterprise.Framework.Redis>
    <RedisDB Name="OPS_Cache" WritePoolSize="1000" ReadPoolSize="1000">
      <RedisServer Host="ocr.oldfish.cn" Port="3600" IsReadOnly="False" />
    </RedisDB>
  </Enterprise.Framework.Redis>
  <runtime>
    <gcServer enabled="true" />
    <assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
      <dependentAssembly>
        <assemblyIdentity name="System.Runtime" publicKeyToken="b03f5f7f11d50a3a" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Threading.Tasks" publicKeyToken="b03f5f7f11d50a3a" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Newtonsoft.Json" culture="neutral" publicKeyToken="30ad4fe6b2a6aeed" />
        <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="log4net" publicKeyToken="669e0ddf0bb1aa2a" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-1.2.15.0" newVersion="1.2.15.0" />
      </dependentAssembly>
    </assemblyBinding>
  </runtime>
</configuration>