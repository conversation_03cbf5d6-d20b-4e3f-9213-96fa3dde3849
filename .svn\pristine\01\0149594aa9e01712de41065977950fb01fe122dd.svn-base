﻿
using CommonLib;
using System.Collections.Generic;

namespace HanZiOcr
{
    /// <summary>
    /// 有道AI体验中心-上传图片识别
    /// https://ai.youdao.com/product-ocr.s
    /// </summary>
    public class YouDaoRec : BaseOcrRec
    {
        public YouDaoRec()
        {
            OcrGroup = OcrGroupType.有道;
            OcrType = HanZiOcrType.有道AI;
            MaxExecPerTime = 24;

            LstJsonPreProcessArray = new List<object>() { "lines" };
            StrResultJsonSpilt = "words";
            IsSupportUrlOcr = true;
            IsSupportVertical = true;
            LstVerticalLocation = new List<object>() { "boundingBox" };
        }

        protected override string GetHtml(OcrContent content)
        {
            var strPost = "lang=auto&imgBase=data%3Aimage%2Fpng%3Bbase64%2C" + System.Web.HttpUtility.UrlEncode(content.strBase64) + "&company=";
            var strTmp = WebClientSyncExt.GetHtml("http://aidemo.youdao.com/ocrapi1", strPost, ExecTimeOutSeconds);
            //{"orientation":"UP","errorCode":"0","lines":[{"boundingBox":"88,1,532,1,532,25,88,25","words":"POST /cgi-bin/appdemo_generalocr HTTP/1.1"},{"boundingBox":"88,34,431,34,431,59,88,59","words":"http://ai.qq.com/product/ocr.shtml"},{"boundingBox":"88,69,208,69,208,92,88,92","words":"gzip,deflate"}]}
            return strTmp;
        }

        public override string GetHtmlByUrl(OcrContent content)
        {
            var strPost = "lang=auto&imgUrl=" + System.Web.HttpUtility.UrlEncode(content.url) + "&company=";
            var strTmp = WebClientSyncExt.GetHtml("http://aidemo.youdao.com/ocrapi", strPost, ExecTimeOutSeconds);
            //{"orientation":"UP","errorCode":"0","lines":[{"boundingBox":"88,1,532,1,532,25,88,25","words":"POST /cgi-bin/appdemo_generalocr HTTP/1.1"},{"boundingBox":"88,34,431,34,431,59,88,59","words":"http://ai.qq.com/product/ocr.shtml"},{"boundingBox":"88,69,208,69,208,92,88,92","words":"gzip,deflate"}]}
            return strTmp;
        }
    }
}