﻿using CommonLib;
using ImageLib;
using System;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading;
using System.Web;

namespace Code.Client.Web
{
    /// <summary>
    ///     Code 的摘要说明
    /// </summary>
    public class Code : IHttpHandler
    {

        private const string StrOcrErrorStr = "{\"ocrType\":0,\"processName\":\"温馨提示\",\"result\":{\"spiltText\":\"[resultStr]\",\"autoText\":\"[resultStr]\"},\"id\":1}";

        public void ProcessRequest(HttpContext context)
        {
            if (context.Request.Params.Count <= 0 || !context.Request.Params.AllKeys.Contains("op"))
            {
                return;
            }
            context.Response.ContentType = "text/plain";
            var action = context.Request.Params["op"];
            var result = "";
            UserTypeEnum userType = UserTypeEnum.体验版;
            switch (action)
            {
                case "path":
                    result = AppDomain.CurrentDomain.RelativeSearchPath;
                    break;
                case "time":
                    result = ServerTime.DateTime.Ticks.ToString("F0");
                    break;
                case "siteinfo":

                    #region SiteInfo

                    //result += CustomImageHelper.ReportToday();
                    result += Environment.NewLine + "服务器名称：" + context.Server.MachineName;
                    //服务器名称  
                    result += Environment.NewLine + "服务器IP地址：" + context.Request.ServerVariables["LOCAL_ADDR"];
                    //服务器IP地址  
                    result += Environment.NewLine + "HTTP访问端口：" + context.Request.ServerVariables["SERVER_PORT"];
                    //HTTP访问端口"
                    result += Environment.NewLine + ".NET版本：" + ".NET CLR" + Environment.Version.Major + "." +
                              Environment.Version.Minor + "." + Environment.Version.Build + "." + Environment.Version.Revision;
                    //.NET解释引擎版本  
                    result += Environment.NewLine + "服务器操作系统版本：" + Environment.OSVersion;
                    //服务器操作系统版本  
                    result += Environment.NewLine + "服务器IIS版本：" + context.Request.ServerVariables["SERVER_SOFTWARE"];
                    //服务器IIS版本  
                    result += Environment.NewLine + "服务器域名：" + context.Request.ServerVariables["SERVER_NAME"];
                    //服务器域名  
                    result += Environment.NewLine + "虚拟目录的绝对路径：" + context.Request.ServerVariables["APPL_RHYSICAL_PATH"];
                    //虚拟目录的绝对路径  
                    result += Environment.NewLine + "执行文件的绝对路径：" + context.Request.ServerVariables["PATH_TRANSLATED"];
                    ////执行文件的绝对路径  
                    //result += Environment.NewLine + "虚拟目录Session总数：" + context.Session.Contents.Count.ToString();
                    ////虚拟目录Session总数  
                    //result += Environment.NewLine + "虚拟目录Application总数：" + context.Application.Contents.Count.ToString();
                    //虚拟目录Application总数  
                    result += Environment.NewLine + "域名主机：" + context.Request.ServerVariables["HTTP_HOST"];
                    //域名主机  
                    result += Environment.NewLine + "服务器区域语言：" + context.Request.ServerVariables["HTTP_ACCEPT_LANGUAGE"];
                    //服务器区域语言  
                    result += Environment.NewLine + "用户信息：" + context.Request.ServerVariables["HTTP_USER_AGENT"];
                    result += Environment.NewLine + "CPU个数：" + Environment.GetEnvironmentVariable("NUMBER_OF_PROCESSORS");
                    //CPU个数  
                    result += Environment.NewLine + "CPU类型：" + Environment.GetEnvironmentVariable("PROCESSOR_IDENTIFIER");
                    //CPU类型  
                    result += Environment.NewLine + "请求来源地址：" + context.Request.Headers["X-Real-IP"];

                    #endregion

                    break;
                //case "show":
                //    BaiDuCode.IsShowDaMa = ConfigHelper.GetBoolByName(context.Request.QueryString["log"], "1");
                //    result = "当前【" + (BaiDuCode.IsShowDaMa ? "已开启" : "已关闭") + "】打码日志";
                //    break;
                case "lastlog":
                    int availableWorker, availableIo, maxWorker = 0, maxIo = 0;

                    ThreadPool.GetAvailableThreads(out availableWorker, out availableIo);
                    ThreadPool.GetMaxThreads(out maxWorker, out maxIo);

                    result = string.Format("Worker:{0}/{1} IO:{2}/{3}{4}OffSet:{5}ms{4}"
                        , availableWorker, maxWorker, availableIo, maxIo, Environment.NewLine, new TimeSpan(ServerTime.OffSet).TotalMilliseconds)
                        + CommonHelper.GetLastLog();
                    //result = string.Format("STime:{0},Ticks:{1}{2}LTime:{3},Ticks:{4}{2}OffSet:{5},{6}ms{2}"
                    //    , ServerTime.DateTime.ToString("yyyy-MM-dd HH:mm:ss"), ServerTime.DateTime.Ticks, Environment.NewLine
                    //    , DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"), DateTime.Now.Ticks, ServerTime.offSet, (DateTime.Now - ServerTime.DateTime).TotalMilliseconds)
                    //    + result;
                    break;
                case "recorder":
                    ConfigHelper.IsRecOrder = ConfigHelper.GetBoolByName(context.Request.QueryString["enable"], "1");
                    result = "当前【" + (ConfigHelper.IsRecOrder ? "已开启" : "已关闭") + "】下单识别";
                    break;
                case "limit":
                    int tmpCount;
                    if (context.Request.QueryString.AllKeys.Contains("count"))
                    {
                        tmpCount = BoxUtil.GetInt32FromObject(context.Request.QueryString["count"], 30);
                        tmpCount = tmpCount < 10 ? 30 : tmpCount;
                        ConfigHelper.NMaxExecPerSecond = tmpCount;
                    }
                    if (context.Request.QueryString.AllKeys.Contains("black"))
                    {
                        tmpCount = BoxUtil.GetInt32FromObject(context.Request.QueryString["black"], 30);
                        tmpCount = tmpCount <= 0 ? 1 : tmpCount;
                        ConfigHelper.NMaxExecBlackSecond = tmpCount;
                    }
                    result = "当前每秒打码限制：" + ConfigHelper.NMaxExecPerSecond;
                    break;
                case "google":
                    result = ConfigHelper.GoogleSearch;
                    break;
                case "proxy":

                    #region 动态代理（搜狗用）

                    var strIP = context.Request.QueryString["ip"];
                    if (!string.IsNullOrEmpty(strIP) && DnsHelper.IsIPv4(strIP))
                    {
                        if (ConfigHelper.GetBoolByName(context.Request.QueryString["forbid"], "1"))
                        {
                            if (!ConfigHelper.LstForbid.Contains(strIP))
                            {
                                ConfigHelper.LstForbid.Add(strIP);
                            }
                            if (ConfigHelper.Proxy.Equals(strIP))
                            {
                                ConfigHelper.Proxy = "";
                            }
                        }
                        else
                        {
                            if (!ConfigHelper.LstForbid.Contains(strIP))
                                ConfigHelper.Proxy = strIP;
                        }
                    }
                    result = ConfigHelper.Proxy ?? "";

                    #endregion

                    break;

                #region 对外识别接口
                case "imgUpload":
                    if (ConfigHelper.IsEnableCode && ConfigHelper.IsCanDaMa)
                    {
                        if (context.Request.Files.Count > 0)
                        {
                            try
                            {
                                var file = context.Request.Files[0];
                                using (var binaryReader = new BinaryReader(file.InputStream))
                                {
                                    var contentLength = file.ContentLength / 1024;//KB
                                    //LogHelper.Log.Info("上传文件类型：" + fileExt + "，大小" + contentLength + "KB");
                                    var byts = binaryReader.ReadBytes(file.ContentLength);
                                    result = ImageHelper.GetResult(byts);
                                }
                            }
                            catch (Exception oe)
                            {
                                LogHelper.Log.Error("上传图片出错！", oe);
                            }
                        }
                    }
                    break;
                case "line":
                case "code":
                case "codeFile":
                    if (ConfigHelper.IsEnableCode && ConfigHelper.IsCanDaMa)
                    {
                        //siteFlag = context.Request.Form["flag"];

                        if (ValidateRequest(context, ref userType))
                        {
                            var strBase64 = "";
                            var url = "";
                            int contentLength = 0;
                            var fileExt = context.Request.QueryString["ext"];
                            if (context.Request.Files.Count > 0)
                            {
                                try
                                {
                                    var file = context.Request.Files[0];
                                    if (string.IsNullOrEmpty(fileExt) && !string.IsNullOrEmpty(file.FileName))
                                        fileExt = Path.GetExtension(file.FileName).TrimStart('.').ToLower();
                                    contentLength = file.ContentLength / 1024;
                                    //LogHelper.Log.Info("上传文件类型：" + fileExt + "，大小" + contentLength + "KB");
                                    if (fileExt?.Equals("txt") == true)
                                    {
                                        using (var binaryReader = new BinaryReader(file.InputStream))
                                        {
                                            var bytes = binaryReader.ReadBytes(file.ContentLength);
                                            strBase64 = Encoding.UTF8.GetString(bytes);
                                            bytes = null;
                                        }
                                    }
                                    else
                                    {
                                        //大于300K，保存到本地。否则直接处理
                                        if (contentLength > 30)
                                        {
                                            var user = string.IsNullOrEmpty(context.Request.Headers["app"]) ? context.Request.QueryString["app"] : context.Request.Headers["app"];
                                            var fileName = string.Format("{5}\\{0}\\{1}\\{2}\\{3}.{4}"
                                                , ServerTime.DateTime.ToString("yyyy")
                                                , ServerTime.DateTime.ToString("MM")
                                                , ServerTime.DateTime.ToString("dd")
                                                , Guid.NewGuid().ToString().Replace("-", "")
                                                , fileExt
                                                , string.IsNullOrEmpty(user) ? "tmp" : user
                                                );
                                            var fileFullName = string.Format("{0}\\{1}", ConfigHelper.StrTmpImagePath, fileName);

                                            if (!Directory.Exists(Path.GetDirectoryName(fileFullName)))
                                            {
                                                Directory.CreateDirectory(Path.GetDirectoryName(fileFullName));
                                            }

                                            file.SaveAs(fileFullName);
                                            url = string.Format(ConfigHelper.FileHostUrl + fileName.Replace("\\", "/"));
                                        }
                                        else
                                        {
                                            using (var binaryReader = new BinaryReader(file.InputStream))
                                            {
                                                var bytes = binaryReader.ReadBytes(file.ContentLength);
                                                strBase64 = Convert.ToBase64String(bytes);
                                                bytes = null;
                                            }
                                        }
                                    }
                                }
                                catch (Exception oe)
                                {
                                    LogHelper.Log.Error("获取StrBase64出错！", oe);
                                }
                            }
                            else
                            {
                                url = context.Request.Form["url"];
                                strBase64 = context.Request.Form["code"];
                            }

                            if (!string.IsNullOrEmpty(strBase64) || !string.IsNullOrEmpty(url))
                            {
                                var ocrType = (OcrType)BoxUtil.GetInt32FromObject(context.Request.QueryString["type"], 0);
                                var fromLeftToRight = object.Equals(context.Request.QueryString["left"], "1");
                                var fromTopToDown = object.Equals(context.Request.QueryString["top"], "1");
                                var groupType = (OcrGroupType)BoxUtil.GetInt32FromObject(context.Request.QueryString["group"], 0);
                                var processId = BoxUtil.GetInt32NullableFromObject(context.Request.QueryString["pid"]);
                                var from = (TransLanguageTypeEnum)BoxUtil.GetInt32FromObject(context.Request.QueryString["from"], 0);
                                var to = (TransLanguageTypeEnum)BoxUtil.GetInt32FromObject(context.Request.QueryString["to"], 0);

                                if (Equals(action, "line"))
                                {
                                    if (!string.IsNullOrEmpty(strBase64))
                                    {
                                        result = WebClientSyncExt.GetHtml("http://127.0.0.1:8888/Code.do?type=line&img=" + HttpUtility.HtmlEncode(strBase64), 30);
                                    }
                                }
                                else
                                {
                                    result = CodeProcessHelper.SendToProcessPool(strBase64, url, userType, ocrType, groupType
                                        , from, to, contentLength, processId
                                        , fromLeftToRight, fromTopToDown, fileExt, ConfigHelper.NGetCodeTimeOut);
                                }
                            }
                        }
                        else
                        {
                            result = UserTypeHelper.GetLimitInfo(userType);
                            result = StrOcrErrorStr.Replace("[resultStr]", result);
                        }
                    }
                    break;
                case "fileStaus":
                    if (ConfigHelper.IsEnableCode && ConfigHelper.IsCanDaMa)
                    {
                        var taskId = context.Request.Params["taskId"];
                        var ocrType = BoxUtil.GetInt32FromObject(context.Request.Params["ocrType"], 0);

                        if (!string.IsNullOrEmpty(taskId))
                        {
                            result = CodeProcessHelper.SendToFileStatusProcessPool(taskId, ocrType);
                        }
                    }
                    break;
                case "htmlfile":
                    if (ConfigHelper.IsEnableCode && ConfigHelper.IsCanDaMa)
                    {
                        var json = context.Request.QueryString["param"];
                        if (!string.IsNullOrEmpty(json))
                        {
                            json = HttpUtility.UrlDecode(json);
                            result = CodeProcessHelper.GetFileResultHtml(json);
                            context.Response.ContentType = "text/html";
                        }
                    }
                    break;
                case "mathfile":
                    if (ConfigHelper.IsEnableCode && ConfigHelper.IsCanDaMa)
                    {
                        var json = context.Request.QueryString["param"];
                        if (!string.IsNullOrEmpty(json))
                        {
                            json = HttpUtility.UrlDecode(json);
                            result = CodeProcessHelper.GetMathResultHtml(json);
                            context.Response.ContentType = "text/html";
                        }
                    }
                    break;

                case "idcode":
                    var codeId = context.Request.Form["id"];
                    if (codeId != null)
                    {
                        result = CodeProcessHelper.GetOtherOcrResultById(codeId);
                    }
                    break;
                case "report":
                    //siteFlag = context.Request.Form["flag"];
                    var reportContent = context.Request.Form["content"];
                    if (!string.IsNullOrEmpty(reportContent) || (context.Request.Files != null && context.Request.Files.Count > 0))
                    {
                        var reportUser = context.Request.Headers["app"];
                        var reportPath = string.Format("{2}\\report\\{0}\\{1}\\"
                                        , string.IsNullOrEmpty(reportUser) ? "tmp" : reportUser
                                        , ServerTime.DateTime.ToString("yyyy-MM-dd")
                                        , ConfigHelper.StrTmpImagePath
                                        );
                        var reportGuid = Guid.NewGuid().ToString().Replace("-", "");
                        if (!Directory.Exists(reportPath))
                        {
                            Directory.CreateDirectory(reportPath);
                        }
                        try
                        {
                            for (int i = 0; i < context.Request.Files?.Count; i++)
                            {
                                var file = context.Request.Files[i];
                                var fileName = string.Format("{0}\\{1}.{2}"
                                    , reportPath
                                    , reportGuid + "-" + (i + 1)
                                    , "png"
                                    );
                                file.SaveAs(fileName);
                            }
                        }
                        catch (Exception oe)
                        {
                            LogHelper.Log.Error("保存反馈图片文件出错！", oe);
                        }

                        try
                        {
                            if (!string.IsNullOrEmpty(reportContent))
                            {
                                var fileName = string.Format("{0}\\{1}.{2}"
                                    , reportPath
                                    , reportGuid
                                    , "txt"
                                    );
                                File.WriteAllText(fileName, reportContent, Encoding.UTF8);
                            }
                        }
                        catch (Exception oe)
                        {
                            LogHelper.Log.Error("保存反馈文本出错！", oe);
                        }
                    }
                    result = "true";
                    break;

                #endregion

                #region 自动收集相关

                //case "rec":
                //    CustomImgProcess.AddImg(context.Request.Form["img"], context.Request.Form["code"],
                //        context.Request.Form["tt"] != null && context.Request.Form["tt"].Equals("1"), "",
                //        UserTypeEnum.免费版);
                //    result = "ok";
                //    break;

                #endregion

                case "order":
                    //Stopwatch stop = Stopwatch.StartNew();
                    result = SubOrder.SubmitOrder(context.Request.Form["con"], context.Request.Form["coo"],
                        context.Request.Form["cip"]
                        , BoxUtil.GetInt32FromObject(context.Request.Form["tt"]));
                    //log4net.LogManager.GetLogger("Order").InfoFormat("Date:{4},Type:{2},Sec:{3},strIP:{1},result:{0}"
                    //    , result, context.Request.Form["cip"], context.Request.Form["tt"], stop.ElapsedMilliseconds.ToString("F0"), DateTime.Now.ToString("HH:mm:ss fff"));
                    break;

                #region 图库相关

                //case "build":
                //case "buildtmp":
                //case "release":
                //    var isAll = !ConfigHelper.GetBoolByName(context.Request.QueryString["all"], "1");
                //    ImageOperate.DoProcess(action, isAll);
                //    result = "ok";
                //    break;
                //case "move":
                //    ImageOperate.SetTmpFiles(CommonHanZi.lstHanZi, context.Request.QueryString["date"]);
                //    result = "ok";
                //    break;
                //case "move1":
                //    ImageOperate.SetTmpFiles(CommonHanZi.lstHanZi, context.Request.QueryString["date"], true);
                //    result = "ok";
                //    break;
                //case "process":
                //    CustomImageHelper.ProcessTmpPic(context.Request.QueryString["loc"]
                //        , !ConfigHelper.GetBoolByName(context.Request.QueryString["tt"], "1"));
                //    result = "ok";
                //    break;

                #endregion

                default:
                    break;
            }
            if (string.IsNullOrEmpty(result) && !action.Equals("order") && !action.Equals("code"))
            {
                result = "no";
            }
            context.Response.Write(result ?? "");
            context.Response.End();
        }

        public bool IsReusable => false;

        private bool ValidateRequest(HttpContext context, ref UserTypeEnum userType)
        {
            var account = string.IsNullOrEmpty(context.Request.Headers["app"]) ? context.Request.QueryString["app"] : context.Request.Headers["app"];
            var token = string.IsNullOrEmpty(context.Request.Headers["token"]) ? context.Request.QueryString["token"] : context.Request.Headers["token"];
            var userTypeInfo = UserTypeHelper.GetUserTypeByAccount(account, token);
            userType = userTypeInfo.Type;
            //ConfigHelper._Log.Info(string.Format("用户类型信息：{0}", JsonConvert.SerializeObject(userTypeInfo)));
            //sb.AppendFormat("\n来源为空，校验是否为助手！");UserTypeEnum.免费版
            bool result = RdsCacheHelper.LimitHelper.CheckIsAble(userTypeInfo.Type, account, true, userTypeInfo.PerTimeSpan, userTypeInfo.PerTimeSpanExecCount);
            if (!result)
            {
                var nick = string.IsNullOrEmpty(context.Request.Headers["mac"]) ? context.Request.QueryString["mac"] : context.Request.Headers["mac"];
                var version = string.IsNullOrEmpty(context.Request.Headers["ver"]) ? context.Request.QueryString["ver"] : context.Request.Headers["ver"];
                ConfigHelper._Log.Error(string.Format("Account:{0},Nick:{1},Version:{2} 被限制访问！", account, nick, version));
            }
            //sb.AppendFormat("\n来源为【助手】！");

            //if (string.IsNullOrEmpty(siteFlag))
            //{
            //}
            //else
            //{
            //    var timeStr = context.Request.Form["mark"];
            //    //sb.AppendFormat("\n来源为【{0}】，开始校验时间戳:{1}！", siteFlag, timeStr);
            //    if (!string.IsNullOrEmpty(timeStr))
            //    {
            //        timeStr = DES.DecryptDES(timeStr.Replace("%2B", "+"));
            //        //sb.AppendFormat("\n时间戳：{0} ", timeStr);
            //        if (!string.IsNullOrEmpty(timeStr))
            //        {
            //            var date = BoxUtil.GetDateTimeByFormatFromObject(timeStr, "yyyyMMddHHmmss",
            //                DateTime.MinValue);
            //            if (Math.Abs(new TimeSpan(ServerTime.DateTime.Ticks - date.Ticks).TotalMinutes) <= 5)
            //            {
            //                //isError = false;
            //                result = Enum.GetNames(typeof(UserTypeEnum)).Contains(siteFlag);
            //                //if (result)
            //                //{
            //                //    result = RdsCacheHelper.LimitHelper.CheckIsAble(siteFlag, mac, isLogin, false);
            //                //}
            //            }
            //        }
            //        //ConfigHelper._Log.InfoFormat("Limit.CheckIsAble:{0},time:{1}", result, timeStr);
            //    }
            //}
            ////sb.AppendLine(string.Format("\n校验【{1}】耗时：{0}ms", stop.ElapsedMilliseconds.ToString("F0"),
            ////    isError ? "失败" : "成功"));
            return result;
        }
    }

}