using System;

namespace NewTicket
{
	public class ClientBaseSocket : SyncSocketInvokeElement
	{
		protected string m_errorString;

		protected string m_userName;

		protected string m_password;

		public string <PERSON><PERSON>ama(string token, string imgbuf, string yzmType, out int length)
		{
			string result;
			try
			{
				length = 0;
				this.m_outgoingDataAssembler.Clear();
				this.m_outgoingDataAssembler.AddRequest();
				this.m_outgoingDataAssembler.AddCommand(ProtocolKey.Dama);
				this.m_outgoingDataAssembler.AddValue(ProtocolKey.Token, token);
				this.m_outgoingDataAssembler.AddValue(ProtocolKey.Img_buf, imgbuf);
				this.m_outgoingDataAssembler.AddValue(ProtocolKey.ImgType, yzmType);
				base.SendCommand();
				string text = "发生错误";
				int num = 0;
				bool flag = base.RecvCommand("Login", out text, out num);
				bool flag2 = flag;
				if (flag2)
				{
					length = num;
					text = text.Replace("Message=", "");
					result = text;
				}
				else
				{
					result = text;
				}
			}
			catch (Exception ex)
			{
				length = -1;
				this.m_errorString = ex.Message;
				result = this.m_errorString + ex.StackTrace;
			}
			return result;
		}
	}
}
