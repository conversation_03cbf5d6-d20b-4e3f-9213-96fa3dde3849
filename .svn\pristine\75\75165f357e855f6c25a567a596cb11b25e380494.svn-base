﻿using CommonLib;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace HanZiOcr
{
    /// <summary>
    /// https://cloud.baidu.com/doc/OCR/s/zjwvxzrw8/
    /// </summary>
    public class BaiDuAPIRec : BaseOcrRec
    {
        public BaiDuAPIRec()
        {
            OcrGroup = OcrGroupType.百度;
            OcrType = HanZiOcrType.百度云;

            LstJsonPreProcessArray = new List<object>() { "words_result" };
            StrResultJsonSpilt = "words";
            MaxExecPerTime = 20;
            IsSupportUrlOcr = true;
            IsSupportVertical = true;
            RegAccount();
        }

        private AccountDto account;

        private void RegAccount()
        {
            var lstAccount = new List<AccountDto>()
            {
                new AccountDto
                {
                    clientId ="fteSLKnl0rxLa8szk98HiUjn",
                    secretKey ="2nnpoDlf75Sj5AtIPR1ayiGIv0ug8Ayu",
                },
                new AccountDto
                {
                    clientId ="vOuuiu4ccGfhef4QTictHy4a",
                    secretKey ="VtjhegyastCC09iNdIqH8XKlPsfgHZad",
                },
                new AccountDto
                {
                    clientId ="ik5YFSaLc23qu9NgIXtbZ5pe",
                    secretKey ="jO3FGWGUnTctAYGWMGGhWrBnpOlFDANf",
                },
                new AccountDto
                {
                    clientId ="APKiNrmIR0yNS0IQHDYbcW5F",
                    secretKey ="7GVbgn0wggGI1mwc1jKiZ4Be2MGBvCTy",
                },
                new AccountDto
                {
                    clientId ="GRhO7ZFGPuqVDGDOXcunDgmv",
                    secretKey ="Xlx27V21hGtuNOiozHyK6C6KVyzUFPAT",
                },
                new AccountDto
                {
                    clientId ="iDeBwPb0lRxtRDSgnfA1HSlp",
                    secretKey ="xLUp2dmfdFoXhKR5aQY7CZVfpZyQ2xq2",
                },
                new AccountDto
                {
                    clientId ="5hM3gBr5a1M9YFBmwUZBwDTZ",
                    secretKey ="8CBMBalNp0CeuU2POttakt7HESKb0axs",
                },
                new AccountDto
                {
                    clientId ="Cyb7moQatBXD42IFzE32fXI9",
                    secretKey ="nd60Zxt6qkDObEUvwf0r5CUtfltcLh27",
                },
                new AccountDto
                {
                    clientId ="pe741pUxOAG1av01zk6hM5rK",
                    secretKey ="dRcL3OSPNstW0fxvChMlmpv8cabkWzqD",
                },
                new AccountDto
                {
                    clientId ="HG22jeLdj13Gz8GLNo6INiCI",
                    secretKey ="TQjWG97GPQBx9HbhGV0vmD7ipeNQkWGl",
                },
                new AccountDto
                {
                    clientId ="qgGCQuP2NGfnQDTpvTWk9tg6",
                    secretKey ="oB8GwlHZQWFg59DaMZiF1lGsDrrh1pQx",
                },
                new AccountDto
                {
                    clientId ="YIKKfQbdpYRRYtqqTPnZ5bCE",
                    secretKey ="hBxFiPhOCn6G9GH0sHoL0kTwfrCtndDj",
                },
                new AccountDto
                {
                    clientId ="DQKYjLdtobxYh98269GWfdHU",
                    secretKey ="zQ86fYV7ftGhAIBxVvpXk4fvyIGNdKKp",
                },
                new AccountDto
                {
                    clientId ="CO7BPe9VjvEOGxYSTZ3FA6My",
                    secretKey ="MHhOA1HSTEwryGG71nng3aFqPXCcYO4b",
                },
                new AccountDto
                {
                    clientId ="fDowQhVGrfyof0oFlNAowVTY",
                    secretKey ="YegBNDm1gHoXwyEBwxOsNr8WoFMzqmVS",
                },
                new AccountDto
                {
                    clientId ="nm6Os9qqOacgxXjKv8PIp45H",
                    secretKey ="BXHhGIpNU7Wi3GDYUt0AGY5cWbWklrov",
                },
                new AccountDto
                {
                    clientId ="WhGsmv5uTul6WUVdqmQjAbv3",
                    secretKey ="owaOpOjMUVt3zXIweepNQPIpgEDxSeTt",
                },
                new AccountDto
                {
                    clientId ="SBHMsV1cYBeOXSAL0X975GCG",
                    secretKey ="LedXa2pPqKT9WmO2qU1FegDg9u2Gbe27",
                },
                new AccountDto
                {
                    clientId ="N7GWF63CRc2RM8iYHih6GBjZ",
                    secretKey ="McqgCdBOprgOjdiysA7U18FTouX1iwDz",
                },

                new AccountDto
                {
                clientId ="U7Z6NnsQNOLkdTWITItGgf1w",
                secretKey ="9cRLl0ZMWQCEkFxQeb73zC4feFsCry4e",
                },
                new AccountDto
                {
                clientId ="idGjUibmqyqzVGcWF4bhlHLA",
                secretKey ="GiKSXEmTw2DFp0ZiFsmuSYPxwCctiBmg",
                },
                new AccountDto
                {
                clientId ="qQL514Y31Em0V8pFLajY8CfC",
                secretKey ="LEjRu03p3HUub1t2qlm35Xp6GZ8Pz8tE",
                },
                new AccountDto
                {
                clientId ="ABDsQ1Lmq7hsDd3LhfIrExP6",
                secretKey ="RCc8uX5zIeFNyyaGRh7k5SX0uNKdhRV1",
                },
                new AccountDto
                {
                clientId ="0cOn0xG4CqNcqhZYgx7gVLuY",
                secretKey ="uGG6vL5ZHTWXYt6yHKmjXA23SP9MIYdh",
                },
                new AccountDto
                {
                clientId ="TUql6XvVAFreu6PQxjpWCHrs",
                secretKey ="yGuiZdloTWszzXIFw7Un9co9kj7Z8mWR",
                },
                new AccountDto
                {
                clientId ="BTaEYpznfGzpgziq3D7Zr8LZ",
                secretKey ="ap2Z4MD2sA9sByE2MwQTgNNgkiYc8N30",
                },
                new AccountDto
                {
                clientId ="9M92Xoucd01BO6MxtyALGoNS",
                secretKey ="OEKte7F4z9jNyDkmKkduEIA80iKNaE5o",
                },
                new AccountDto
                {
                clientId ="U0V5YcFhZWgYZxWPHnEc65am",
                secretKey ="fi9QE9IGGQZD0D9x4HTgcS2Rduauz1KM",
                },
                new AccountDto
                {
                clientId ="umHrd3xrZQMUPe5pHg3VNDOD",
                secretKey ="IC9zliOxE7BBpP859QG6MzrPGVfhCLxC",
                },
            };
            AccountHelper.RegAccount(OcrType.GetHashCode(), lstAccount);
        }

        private const string strTokenSpilt = "\"access_token\":\"";

        private string strToken = "";

        private void InitToken()
        {
            if (account == null)
            {
                account = AccountHelper.GetAccount(OcrType.GetHashCode());
            }
            if (account != null && string.IsNullOrEmpty(strToken))
            {
                strToken = GetToken();
                if (string.IsNullOrEmpty(strToken) && account == null)
                {
                    InitToken();
                }
            }
        }

        private string GetToken()
        {
            var result = "";
            var token = WebClientSyncExt.GetHtml(string.Format("{0}?{1}"
                , "https://aip.baidubce.com/oauth/2.0/token"
                , "grant_type=client_credentials&client_id=" + account.clientId + "&client_secret=" + account.secretKey), ExecTimeOutSeconds);

            if (!string.IsNullOrEmpty(token))
            {
                if (token.Contains(strTokenSpilt))
                {
                    result = token.Substring(token.IndexOf(strTokenSpilt) + strTokenSpilt.Length);
                    result = result.Substring(0, result.IndexOf("\""));
                }
                else
                {
                    AccountHelper.ForbidAccount(OcrType.GetHashCode(), account.clientId, account.secretKey, true, token);
                    account = null;
                }
            }
            return result;
        }

        public override void Reset()
        {
            strToken = "";
            base.Reset();
        }

        protected override string GetHtml(OcrContent content)
        {
            return RequestHtmlContent(content.strBase64);
        }

        public override string GetHtmlByUrl(OcrContent content)
        {
            return RequestHtmlContent(null, content.url);
        }

        private string RequestHtmlContent(string strBase64, string imgUrl = null)
        {
            //GetKeys(strBase64, imgUrl);
            InitToken();
            var result = "";
            if (!string.IsNullOrEmpty(strToken))
            {
                var url = string.Empty;
                //if (string.IsNullOrEmpty(imgUrl))
                //{
                //    EngineType = ConstHelper.lstBaiDuOcrWithLocationAPIs.GetRndItem();
                //    url = EngineType.Value;
                //}
                url = ConstHelper.lstBaiDuOcrWithLocationAPIs["高精度含位置版"];
                if (string.IsNullOrEmpty(url))
                {
                    url = "https://aip.baidubce.com/rest/2.0/ocr/v1/accurate";
                }
                var strPost = string.Format("{1}={0}&detect_direction=true"
                    , string.IsNullOrEmpty(imgUrl) ? HttpUtility.UrlEncode(strBase64) : HttpUtility.UrlEncode(imgUrl)
                    , string.IsNullOrEmpty(imgUrl) ? "image" : "url");
                result = WebClientSyncExt.GetHtml(url + "?access_token=" + strToken, strPost, ExecTimeOutSeconds);

                //Console.WriteLine("百度Result:" + strTmp);
                if (!string.IsNullOrEmpty(result))
                {
                    if (result.Contains("\"error_code\":1") || result.Contains("\"error_code\":6"))
                    {
                        strToken = "";
                        if (account != null)
                        {
                            AccountHelper.ForbidAccount(OcrType.GetHashCode(), account.clientId, account.secretKey, false, result);
                            account = null;
                        }
                        result = "";
                    }
                }
            }
            return result;
        }

        private string GetKeys(string strBase64, string imgUrl = null)
        {
            //https://sourcegraph.com/search?q=context:global+/aip.baidubce.com%5C/oauth%5C/2.0%5C/token%5C%3Fgrant_type%3Dclient_credentials%26client_id%3D%5Ba-zA-Z0-9_-%5D%7B10%2C%7D/&patternType=keyword&sm=0&groupBy=repo
            var strTmp = "";
            var lstKeys = new List<AccountDto>();
            var strLine = strTmp.Split(new string[] { "\n" }, StringSplitOptions.RemoveEmptyEntries);
            foreach (var item in strLine)
            {
                if (!item.Contains("grant_type=client_credentials"))
                    continue;
                var key = item.Substring(item.IndexOf("grant_type=client_credentials") + "grant_type=client_credentials".Length).Trim();
                if (!key.Contains("&client_id="))
                    continue;
                key = item.Substring(item.IndexOf("&client_id=") + "&client_id=".Length).Trim();
                var account = new AccountDto()
                {
                    clientId = key.Substring(0, key.IndexOf("&"))
                };
                key = item.Substring(item.IndexOf("&client_secret=") + "&client_secret=".Length).Trim();
                if (key.Contains(" "))
                    key = key.Substring(0, key.IndexOf(" "));
                if (key.Contains("'"))
                    key = key.Substring(0, key.IndexOf("'"));
                if (key.Contains("\""))
                    key = key.Substring(0, key.IndexOf("\""));
                if (key.Contains("\\"))
                    key = key.Substring(0, key.IndexOf("\\"));
                if (key.Contains("/"))
                    key = key.Substring(0, key.IndexOf("/"));
                if (string.IsNullOrEmpty(key))
                {

                }
                account.secretKey = key;
                if (!lstKeys.Any(p => Equals(p.clientId, account.clientId)))
                    lstKeys.Add(account);
            }
            lstKeys = lstKeys.Distinct().ToList();

            ////文字
            //var url = ConstHelper.lstBaiDuOcrWithLocationAPIs["高精度含位置版"];
            //表格
            var url = "https://aip.baidubce.com/rest/2.0/solution/v1/form_ocr/request";

            foreach (var apiKey in lstKeys)
            {
                account = apiKey;
                strToken = GetToken();
                if (string.IsNullOrEmpty(strToken))
                {
                    continue;
                }
                ////文字
                //var strPost = string.Format("{1}={0}&detect_direction=true"
                //    , string.IsNullOrEmpty(imgUrl) ? HttpUtility.UrlEncode(strBase64) : HttpUtility.UrlEncode(imgUrl)
                //    , string.IsNullOrEmpty(imgUrl) ? "image" : "url");
                //var result = WebClientSyncExt.GetHtml(url + "?access_token=" + strToken, strPost, ExecTimeOutSeconds);
                //表格
                var strPost = string.Format("image={0}&is_sync=true&request_type=json&table_border=none"
                    , HttpUtility.UrlEncode(strBase64));
                var result = WebClientSyncExt.GetHtml(url + "?access_token=" + strToken, strPost, ExecTimeOutSeconds);

                //Console.WriteLine("百度Result:" + strTmp);
                if (!string.IsNullOrEmpty(result))
                {
                    if (result.Contains("\"error_code\":1") || result.Contains("\"error_code\":6"))
                    {
                        strToken = "";
                        if (account != null)
                        {
                            AccountHelper.ForbidAccount(OcrType.GetHashCode(), account.clientId, account.secretKey, false, result);
                            account = null;
                        }
                        result = "";
                    }
                    else
                    {
                        Console.WriteLine("new AccountDto\r\n{\r\nclientId =\"" + apiKey.clientId + "\",\r\nsecretKey =\"" + apiKey.secretKey + "\",\r\n},");
                    }
                }
                else
                {
                    Console.WriteLine(apiKey);
                }

            }
            return "";
        }
    }
}