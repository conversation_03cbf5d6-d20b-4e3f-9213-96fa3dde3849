﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace CommonLib
{
    public class ApiRequestCacheHelper
    {
        private static ApiRequestCache _requestCache = new ApiRequestCache();
        private static ApiRequestHistoryCache _requestHistoryCache = new ApiRequestHistoryCache();

        private static long DtStartTicks =
            TimeZone.CurrentTimeZone.ToLocalTime(new DateTime(1970, 1, 1, 0, 0, 0, 0)).Ticks;

        private const double MaxEsTime = 30000;

        public static void Add(string key, ApiRequestLog request)
        {
            key += string.Format(":{0}", ServerTime.LocalTime.ToString("MM-dd HH:mm:00"));
            if (request.EsTime > MaxEsTime)
            {
                request.EsTime = MaxEsTime;
            }
            _requestCache.Add(key, request);
        }

        public static ApiRequestRoot GetRequestRoot(string key, ApiRequestTypeEnum type)
        {
            DateTime dtStart = ServerTime.LocalTime, dtEnd = ServerTime.LocalTime;
            var interval = 1;
            switch (type)
            {
                case ApiRequestTypeEnum.day:
                    interval = 60;
                    dtStart = ServerTime.LocalTime.AddDays(-1);
                    break;
                case ApiRequestTypeEnum.week:
                    interval = 60 * 30;
                    dtStart = ServerTime.LocalTime.AddDays(-7);
                    break;
                case ApiRequestTypeEnum.month:
                    interval = 60 * 60 * 2;
                    dtStart = ServerTime.LocalTime.AddMonths(-1);
                    break;
            }
            dtStart = dtStart.AddSeconds(-dtStart.Second).AddMilliseconds(-dtStart.Millisecond);
            dtEnd = dtEnd.AddSeconds(-dtEnd.Second).AddMilliseconds(-dtEnd.Millisecond);
            var root = new ApiRequestRoot()
            {
                period = new ApiRequestPeriod()
                {
                    //分钟
                    interval = interval,
                    identifier = type.ToString()
                },
                metrics = new List<ApiRequestMetrics>(),
                summary = new ApiRequestSummary()
            };

            var merric = new ApiRequestMetrics()
            {
                metric = new ApiRequestMetric
                {
                    name = key,
                    backfilled = true,
                    metric_identifier = "avg:discord_api.http.response_time.avg{*}",
                    created_at = ServerTime.LocalTime,
                    updated_at = ServerTime.LocalTime,
                    id = "pbb6x1yjh8s3",
                    metrics_provider_id = "4l4nqp7hhz9c",
                    metrics_display_id = "5k2rt9f7pmny",
                    most_recent_data_at = ServerTime.LocalTime,
                    last_fetched_at = ServerTime.LocalTime,
                    backfill_percentage = 1
                },
                summary = new ApiRequestSummary(),
                data = GetDayDetailCountResult(key, dtStart, dtEnd, interval / 60)
            };

            if (merric.data?.Count > 0)
            {
                root.period.count = merric.data.Count;
                var lstTmp = merric.data.Where(p => p.value > 0).ToList();
                if (lstTmp.Count > 0)
                {
                    merric.summary = new ApiRequestSummary()
                    {
                        sum = (int)lstTmp.Sum(p => p.value),
                        last = (int)lstTmp.Last().value
                    };
                    merric.summary.mean = (int)(merric.summary.sum / lstTmp.Count);
                }
            }

            root.metrics.Add(merric);

            var lastData = root.metrics.Last();
            if (lastData != null)
            {
                root.summary = lastData.summary;
            }
            return root;
        }

        //public static double? GetAverageByDate(string key, DateTime dtStart, DateTime dtEnd, int minute = 1)
        //{
        //    var lstCache = new List<ApiRequestLog>();
        //    for (; dtStart.Date <= dtEnd.Date; dtStart = dtStart.AddDays(1))
        //    {
        //        var lstTmp = GetByDate(key, dtStart, minute);
        //        lstCache.AddRange(lstTmp);
        //    }

        //    return lstCache.Average(p => p.EsTime);
        //}

        //public static List<ApiRequestLog> GetByDate(string key, DateTime dtDate, int minute = 1)
        //{
        //    var strDayKey = string.Format("{0}_{1}", key, dtDate.ToString("yyyy-MM-dd"));
        //    var lstCache = _requestCache.Get(strDayKey);
        //    if (lstCache == null)
        //    {
        //        lstCache = new List<ApiRequestLog>();
        //        var lstTmp = GetDayDetailCount(key, dtDate, dtDate, minute);
        //        foreach (var keyValue in lstTmp.Where(keyValue => keyValue.Value != null))
        //        {
        //            lstCache.AddRange(keyValue.Value);
        //        }
        //        if (dtDate.Date < ServerTime.DateTime.Date)
        //        {
        //            _requestCache.Add(strDayKey, lstCache);
        //        }
        //    }

        //    return lstCache;
        //}

        private static Dictionary<string, Dictionary<DateTime, List<ApiRequestLog>>> dicRequestCache =
            new Dictionary<string, Dictionary<DateTime, List<ApiRequestLog>>>();

        private static Dictionary<string, List<ApiRequestDatum>> dicRequestCountCache =
            new Dictionary<string, List<ApiRequestDatum>>();


        public static List<ApiRequestDatum> GetDayDetailCountResult(string key, DateTime dtStart, DateTime dtEnd, int minute = 1)
        {
            var lstResult = dicRequestCountCache.ContainsKey(key)
                ? dicRequestCountCache[key]
                : _requestHistoryCache.Get(key);
            lstResult = lstResult ?? new List<ApiRequestDatum>();

            lstResult.RemoveAll(p => p.time < dtStart);
            if (lstResult.Count > 0)
            {
                var maxDate = lstResult.Max(p => p.time);
                if (maxDate > dtStart)
                {
                    dtStart = maxDate.AddMinutes(1);
                }
            }
            if (dtStart < dtEnd)
            {
                var othersCount = GetDayDetailCount(key, dtStart, dtEnd, minute)
                    .Select(p => new ApiRequestDatum()
                    {
                        value = Math.Ceiling(p.Value == null || p.Value.Count <= 0 ? 0 : p.Value.Sum(q => q.EsTime) / p.Value.Count),
                        timestamp = (int)((p.Key.Ticks - DtStartTicks) / (10 * 1000 * 1000)),
                        time = p.Key
                    }).ToList();
                lstResult.AddRange(othersCount);
                //大于30分钟没同步
                if (new TimeSpan(dtEnd.Ticks - dtStart.Ticks).TotalMinutes > 30)
                {
                    Task.Factory.StartNew(() =>
                    {
                        _requestHistoryCache.Add(key, lstResult);
                    });
                }

                if (dicRequestCountCache.ContainsKey(key))
                {
                    dicRequestCountCache[key] = lstResult;
                }
                else
                {
                    dicRequestCountCache.Add(key, lstResult);
                }
            }

            return lstResult;
        }

        public static Dictionary<DateTime, List<ApiRequestLog>> GetDayDetailCount(string key, DateTime dtStart, DateTime dtEnd, int minute = 1)
        {
            //Stopwatch stopwatch = Stopwatch.StartNew();
            var dicDetail = new Dictionary<DateTime, List<ApiRequestLog>>();
            var dicHistory = dicRequestCache.ContainsKey(key) ? dicRequestCache[key] : new Dictionary<DateTime, List<ApiRequestLog>>();

            var keys = new List<string>();

            for (var dt = dtStart; dt <= dtEnd; dt = dt.AddMinutes(minute))
            {
                var strTmpTick = dt.ToString("yyyy-MM-dd HH:mm:ss");
                dt = DateTime.Parse(strTmpTick);
                if (dicHistory.ContainsKey(dt))
                {
                    dicDetail.Add(dt, dicHistory[dt]);
                }
                else
                {
                    keys.Add(string.Format("{0}:{1}", key, dt.ToString("MM-dd HH:mm:ss")));
                }
            }

            if (keys.Count > 0)
            {
                var results = _requestCache.Get(keys.ToArray());
                var i = 0;
                for (var dt = dtStart; dt <= dtEnd; dt = dt.AddMinutes(minute))
                {
                    dicDetail.Add(dt, results.Length > i ? results[i] : null);
                    i++;
                }

                if (dicRequestCache.ContainsKey(key))
                {
                    dicRequestCache[key] = dicDetail;
                }
                else
                {
                    dicRequestCache.Add(key, dicDetail);
                }
            }

            //LogHelper.Log.Info(key + "耗时:" + stopwatch.ElapsedMilliseconds.ToString("F0") + "ms,dtStart:" + dtStart.ToString("yyyy-MM-dd HH:mm:ss") + ",dtEnd:" + dtEnd.ToString("yyyy-MM-dd HH:mm:ss"));
            return dicDetail;
        }

    }

    public enum ApiRequestTypeEnum
    {
        day,
        week,
        month
    }

    public class ApiRequestPeriod
    {
        public int count { get; set; }
        public int interval { get; set; }
        public string identifier { get; set; }
    }


    public class ApiRequestSummary
    {
        public double sum { get; set; }
        public double mean { get; set; }
        public double last { get; set; }
    }

    public class ApiRequestDatum
    {
        public long timestamp { get; set; }
        public double value { get; set; }

        public DateTime time { get; set; }
    }

    public class ApiRequestMetrics
    {
        public ApiRequestMetric metric { get; set; }
        public ApiRequestSummary summary { get; set; }
        public List<ApiRequestDatum> data { get; set; }

    }

    public class ApiRequestRoot
    {
        public ApiRequestPeriod period { get; set; }
        public List<ApiRequestMetrics> metrics { get; set; }
        public ApiRequestSummary summary { get; set; }
    }

    public class ApiRequestMetric
    {
        public string name { get; set; }
        public string metric_identifier { get; set; }
        public DateTime created_at { get; set; }
        public DateTime updated_at { get; set; }
        public string id { get; set; }
        public string metrics_provider_id { get; set; }
        public string metrics_display_id { get; set; }
        public DateTime most_recent_data_at { get; set; }
        public bool backfilled { get; set; }
        public DateTime last_fetched_at { get; set; }
        public double backfill_percentage { get; set; }
    }
}