﻿using CommonLib;
using System;
using System.Collections.Specialized;
using System.Text;

namespace ImageLib
{
    /// <summary>
    /// https://www.textin.com/experience/crop_enhance_image
    /// </summary>
    public class HeHeImageProcess : BaseImageProcess
    {
        public HeHeImageProcess()
        {
            ImageType = ImageProcessTypeEnum.合合_图片切边增强;
        }

        public override string GetResult(byte[] content)
        {
            var result = GetFromAiWen(content);
            return result;
        }

        private const string strFileNameSpilt = "\"image\":\"";

        private string GetFromAiWen(byte[] content)
        {
            var result = "";
            try
            {
                var url = "https://api.textin.com/home/<USER>";
                var html = WebClientSyncExt.GetHtml(url, "", Encoding.UTF8.GetString(content), "https://www.textin.com/", 30
                    , new NameValueCollection() {
                    { "App-Key", "ai_demo_text_recognize_3d1" },
                    { "App-Secret","ai_demo_text_recognize_3d1"}
                    });

                if (html.Contains(strFileNameSpilt))
                {
                    result = html.Substring(html.IndexOf(strFileNameSpilt) + strFileNameSpilt.Length);
                    result = result.Substring(0, result.IndexOf("\"")).Replace("\\/", "/");
                }
            }
            catch (Exception oe)
            {

            }
            return result;
        }
    }
}
