﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <metadata name="imgList.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>17, 17</value>
  </metadata>
  <data name="imgList.ImageStream" mimetype="application/x-microsoft.net.object.binary.base64">
    <value>
        AAEAAAD/////AQAAAAAAAAAMAgAAAFdTeXN0ZW0uV2luZG93cy5Gb3JtcywgVmVyc2lvbj0yLjAuMC4w
        LCBDdWx0dXJlPW5ldXRyYWwsIFB1YmxpY0tleVRva2VuPWI3N2E1YzU2MTkzNGUwODkFAQAAACZTeXN0
        ZW0uV2luZG93cy5Gb3Jtcy5JbWFnZUxpc3RTdHJlYW1lcgEAAAAERGF0YQcCAgAAAAkDAAAADwMAAABO
        CAAAAk1TRnQBSQFMAwEBAAEQAQABEAEAARQBAAEUAQAE/wEhAQAI/wFCAU0BNgcAATYDAAEoAwABUAMA
        ARQDAAEBAQABIAYAARn/AP8AnAADJgE4AyYBOAMmATgDJgE4AyYBOAMmATgDJgE4AyYBOAMmATgDJgE4
        AyYBOAMmATgDJgE4AyYBOAMlATcDHwEs/wABAAE9AT8BQgFuAQgBQAGEAf8BCAFAAYQB/wEIAUABhAH/
        AQgBQAGEAf8BCAFAAYQB/wEIAUABhAH/AQgBQAGEAf8BCAFAAYQB/wEIAUABhAH/AQgBQAGEAf8BCAFA
        AYQB/wEIAUABhAH/AQgBQAGEAf8BCAFAAYQB/wE1ATYBNwFY/wABAAEJAUcBkQH/AREBRAGgAf8BEQFE
        AaAB/wERAUQBoAH/AREBRAGgAf8BEQFEAaAB/wERAUQBoAH/AREBRAGgAf8BEQFEAaAB/wERAUQBoAH/
        AREBRAGgAf8BEQFEAaAB/wERAUQBoAH/AREBRAGgAf8BEQFEAaAB/wEJAUcBkQH//wABAAEJAUcBkQH/
        ARYBTQGpAf8BFgFNAakB/wEWAU0BqQH/ARYBTQGpAf8BFgFNAakB/wEWAU0BqQH/ARYBTQGpAf8BFgFN
        AakB/wEWAU0BqQH/ARYBTQGpAf8BFgFNAakB/wEWAU0BqQH/ARYBTQGpAf8BFgFNAakB/wEJAUcBkQH/
        /wABAAEJAUcBkQH/ARoBVAGwAf8BGgFUAbAB/wEaAVQBsAH/ARoBVAGwAf8BEQFUAaUB/wEQAUsBlAH/
        ARgBSgGOAf8BEwFJAZIB/wESAUwBngH/ARoBVAGwAf8BGgFUAbAB/wEaAVQBsAH/ARoBVAGwAf8BGgFU
        AbAB/wEJAUcBkQH//wABAAEJAUcBkQH/AQwBRgGUAf8BDQFHAZYB/wERAUsBnAH/ARQBTwGiAf8BBwFA
        AYoB/wFaAVkBRwH/AVoBWQFHAf8BWgFZAUcB/wENAUgBlgH/ARkBVgGsAf8BFQFQAaQB/wERAUsBnQH/
        AQwBRwGVAf8BDAFHAZUB/wEJAUcBkQH//wABAAECATsBgQH/AQIBOwGBAf8BAgE7AYEB/wECATsBgQH/
        AQIBOwGBAf8BAgE7AYEB/wFaAVkBRwH/AVoBWQFHAf8BWgFZAUcB/wECATsBgQH/AQIBOwGBAf8BAgE7
        AYEB/wECATsBgQH/AQIBOwGBAf8BAgE7AYEB/wEJAUcBkQH//wABAAEFATwBhgH/ARgBTwGqAf8BGAFP
        AaoB/wEYAU8BqgH/ARgBTwGqAf8BBwE/AYoB/wKNAXEB/wHZAdwBvgH/Ao0BcQH/AQcBPwGKAf8BGAFP
        AaoB/wEYAU8BqgH/ARgBTwGqAf8BGAFPAaoB/wEYAU8BqgH/AQMBOgGBAf//AAEAAQYBPwGMAf8BIQFe
        AbgB/wEhAV4BuAH/ASEBXgG4Af8BIQFeAbgB/wETAU0BnwH/AQkBQwGOAf8BCQFDAY4B/wEJAUMBjgH/
        ARYBUQGkAf8BIQFeAbgB/wEhAV4BuAH/ASEBXgG4Af8BIQFeAbgB/wEhAV4BuAH/AQMBOgGBAf//AAEA
        AQcBRAGSAf8BIgFhAboB/wEjAWIBuwH/ASMBYgG7Af8BIwFiAbsB/wEjAWIBuwH/ASMBYgG7Af8BJAFj
        AbsB/wEkAWMBvAH/ASQBYwG8Af8BJAFkAbwB/wEkAWQBvAH/ASQBZAG8Af8BJAFkAbwB/wEkAWQBvAH/
        AQMBOwGDAf//AAEAAQgBRwGYAf8BKwFwAcQB/wEsAXIBxQH/AS0BcwHFAf8BLQFzAcYB/wEtAXQBxgH/
        AS0BdQHHAf8BLgF1AccB/wEuAXYBxwH/AS4BdgHIAf8BLgF2AcgB/wEvAXcByAH/AS8BeAHJAf8BLwF4
        AckB/wEvAXgByQH/AQUBPgGIAf//AAEAAQkBSgGdAf8BPgGUAdkB/wE+AZQB2QH/AT4BlAHZAf8BPgGU
        AdkB/wE+AZQB2QH/AT4BlAHZAf8BPgGUAdkB/wE+AZQB2QH/AT4BlAHZAf8BPgGUAdkB/wE+AZQB2QH/
        AT4BlAHZAf8BPgGUAdkB/wE+AZQB2QH/AQYBQQGOAf//AAEAAQoBTAGhAf8BJAFjAbwB/wEkAWQBvAH/
        ASQBZAG8Af8BJAFkAbwB/wEkAWMBvAH/ASQBYwG8Af8BJAFjAbsB/wEjAWMBuwH/ASMBYgG7Af8BIwFi
        AbsB/wEjAWIBuwH/ASMBYgG7Af8BIwFhAboB/wEiAWEBugH/AQgBRQGVAf//AAEAATIBMwE0AVIBCgFM
        AaEB/wEKAUwBoQH/AQoBTAGhAf8BCgFMAaEB/wEFARwBUQH/AQoBTAGhAf8BCgFMAaEB/wEKAUwBoQH/
        AQoBTAGhAf8BBQEcAVEB/wEKAUwBoQH/AQoBTAGhAf8BCgFMAaEB/wEKAUwBoQH/ATIBMwE0AVL/ABUA
        AQUBHAFRAf8QAAEFARwBUQH//wApAAFFAUgBUAGbAQUBHAFRAf8BBQEcAVEB/wEFARwBUQH/AQUBHAFR
        Af8BRQFIAVABm/8A/wD/AI8AAUIBTQE+BwABPgMAASgDAAFQAwABFAMAAQEBAAEBBQAB8BcAA/8BAAL/
        AfAJAAL/AfAJAAHAAQABMAkAAcABAAEwCQABwAEAATAJAAHAAQABMAkAAcABAAEwCQABwAEAATAJAAHA
        AQABMAkAAcABAAEwCQABwAEAATAJAAHAAQABMAkAAcABAAEwCQABwAEAATAJAAHAAQABMAkAAcABAAEw
        CQAB/gH3AfAJAAH+AQcB8AkAAv8B8AkAAv8B8AkACw==
</value>
  </data>
</root>