!function(){function i(a){return!!a.exifdata}function j(a,b){var c,d,e,f,g;for(b=b||a.match(/^data\:([^\;]+)\;base64,/im)[1]||"",a=a.replace(/^data\:([^\;]+)\;base64,/gim,""),c=atob(a),d=c.length,e=new ArrayBuffer(d),f=new Uint8Array(e),g=0;d>g;g++)f[g]=c.charCodeAt(g);return e}function k(a,b){var c=new XMLHttpRequest;c.open("GET",a,!0),c.responseType="blob",c.onload=function(){(200==this.status||0===this.status)&&b(this.response)},c.send()}function l(b,c){function d(a){var d=m(a),e=o(a);b.exifdata=d||{},b.iptcdata=e||{},c&&c.call(b)}var e,f,g;b.src?/^data\:/i.test(b.src)?(e=j(b.src),d(e)):/^blob\:/i.test(b.src)?(f=new FileReader,f.onload=function(a){d(a.target.result)},k(b.src,function(a){f.readAsArrayBuffer(a)})):(g=new XMLHttpRequest,g.onload=function(){if(200!=this.status&&0!==this.status)throw"Could not load image";d(g.response),g=null},g.open("GET",b.src,!0),g.responseType="arraybuffer",g.send(null)):window.FileReader&&(b instanceof window.Blob||b instanceof window.File)&&(f=new FileReader,f.onload=function(b){a&&console.log("Got file of length "+b.target.result.byteLength),d(b.target.result)},f.readAsArrayBuffer(b))}function m(b){var f,d,e,c=new DataView(b);if(a&&console.log("Got file of length "+b.byteLength),255!=c.getUint8(0)||216!=c.getUint8(1))return a&&console.log("Not a valid JPEG"),!1;for(d=2,e=b.byteLength;e>d;){if(255!=c.getUint8(d))return a&&console.log("Not a valid marker at offset "+d+", found: "+c.getUint8(d)),!1;if(f=c.getUint8(d+1),a&&console.log(f),225==f)return a&&console.log("Found 0xFFE1 marker"),u(c,d+4,c.getUint16(d+2)-2);d+=2+c.getUint16(d+2)}}function o(b){var d,e,f,g,h,i,c=new DataView(b);if(a&&console.log("Got file of length "+b.byteLength),255!=c.getUint8(0)||216!=c.getUint8(1))return a&&console.log("Not a valid JPEG"),!1;for(d=2,e=b.byteLength,f=function(a,b){return 56===a.getUint8(b)&&66===a.getUint8(b+1)&&73===a.getUint8(b+2)&&77===a.getUint8(b+3)&&4===a.getUint8(b+4)&&4===a.getUint8(b+5)};e>d;){if(f(c,d))return g=c.getUint8(d+7),0!==g%2&&(g+=1),0===g&&(g=4),h=d+8+g,i=c.getUint16(d+6+g),q(b,h,i);d++}}function q(a,b,c){for(var f,g,h,i,j,d=new DataView(a),e={},k=b;b+c>k;)28===d.getUint8(k)&&2===d.getUint8(k+1)&&(i=d.getUint8(k+2),i in p&&(h=d.getInt16(k+3),j=h+5,g=p[i],f=t(d,k+5,h),e.hasOwnProperty(g)?e[g]instanceof Array?e[g].push(f):e[g]=[e[g],f]:e[g]=f)),k++;return e}function r(b,c,d,e,f){var i,j,k,g=b.getUint16(d,!f),h={};for(k=0;g>k;k++)i=d+12*k+2,j=e[b.getUint16(i,!f)],!j&&a&&console.log("Unknown tag: "+b.getUint16(i,!f)),h[j]=s(b,i,c,d,f);return h}function s(a,b,c,d,e){var i,j,k,l,m,n,f=a.getUint16(b+2,!e),g=a.getUint32(b+4,!e),h=a.getUint32(b+8,!e)+c;switch(f){case 1:case 7:if(1==g)return a.getUint8(b+8,!e);for(i=g>4?h:b+8,j=[],l=0;g>l;l++)j[l]=a.getUint8(i+l);return j;case 2:return i=g>4?h:b+8,t(a,i,g-1);case 3:if(1==g)return a.getUint16(b+8,!e);for(i=g>2?h:b+8,j=[],l=0;g>l;l++)j[l]=a.getUint16(i+2*l,!e);return j;case 4:if(1==g)return a.getUint32(b+8,!e);for(j=[],l=0;g>l;l++)j[l]=a.getUint32(h+4*l,!e);return j;case 5:if(1==g)return m=a.getUint32(h,!e),n=a.getUint32(h+4,!e),k=new Number(m/n),k.numerator=m,k.denominator=n,k;for(j=[],l=0;g>l;l++)m=a.getUint32(h+8*l,!e),n=a.getUint32(h+4+8*l,!e),j[l]=new Number(m/n),j[l].numerator=m,j[l].denominator=n;return j;case 9:if(1==g)return a.getInt32(b+8,!e);for(j=[],l=0;g>l;l++)j[l]=a.getInt32(h+4*l,!e);return j;case 10:if(1==g)return a.getInt32(h,!e)/a.getInt32(h+4,!e);for(j=[],l=0;g>l;l++)j[l]=a.getInt32(h+8*l,!e)/a.getInt32(h+4+8*l,!e);return j}}function t(a,b,c){var d="";for(n=b;b+c>n;n++)d+=String.fromCharCode(a.getUint8(n));return d}function u(b,c){var h,i,j,k,l,m,n;if("Exif"!=t(b,c,4))return a&&console.log("Not valid EXIF data! "+t(b,c,4)),!1;if(m=c+6,18761==b.getUint16(m))h=!1;else{if(19789!=b.getUint16(m))return a&&console.log("Not valid TIFF data! (no 0x4949 or 0x4D4D)"),!1;h=!0}if(42!=b.getUint16(m+2,!h))return a&&console.log("Not valid TIFF data! (no 0x002A)"),!1;if(n=b.getUint32(m+4,!h),8>n)return a&&console.log("Not valid TIFF data! (First offset less than 8)",b.getUint32(m+4,!h)),!1;if(i=r(b,m,m+n,e,h),i.ExifIFDPointer){k=r(b,m,m+i.ExifIFDPointer,d,h);for(j in k){switch(j){case"LightSource":case"Flash":case"MeteringMode":case"ExposureProgram":case"SensingMethod":case"SceneCaptureType":case"SceneType":case"CustomRendered":case"WhiteBalance":case"GainControl":case"Contrast":case"Saturation":case"Sharpness":case"SubjectDistanceRange":case"FileSource":k[j]=g[j][k[j]];break;case"ExifVersion":case"FlashpixVersion":k[j]=String.fromCharCode(k[j][0],k[j][1],k[j][2],k[j][3]);break;case"ComponentsConfiguration":k[j]=g.Components[k[j][0]]+g.Components[k[j][1]]+g.Components[k[j][2]]+g.Components[k[j][3]]}i[j]=k[j]}}if(i.GPSInfoIFDPointer){l=r(b,m,m+i.GPSInfoIFDPointer,f,h);for(j in l){switch(j){case"GPSVersionID":l[j]=l[j][0]+"."+l[j][1]+"."+l[j][2]+"."+l[j][3]}i[j]=l[j]}}return i}var d,e,f,g,p,a=!1,b=this,c=function(a){return a instanceof c?a:this instanceof c?(this.EXIFwrapped=a,void 0):new c(a)};"undefined"!=typeof exports?("undefined"!=typeof module&&module.exports&&(exports=module.exports=c),exports.EXIF=c):b.EXIF=c,d=c.Tags={36864:"ExifVersion",40960:"FlashpixVersion",40961:"ColorSpace",40962:"PixelXDimension",40963:"PixelYDimension",37121:"ComponentsConfiguration",37122:"CompressedBitsPerPixel",37500:"MakerNote",37510:"UserComment",40964:"RelatedSoundFile",36867:"DateTimeOriginal",36868:"DateTimeDigitized",37520:"SubsecTime",37521:"SubsecTimeOriginal",37522:"SubsecTimeDigitized",33434:"ExposureTime",33437:"FNumber",34850:"ExposureProgram",34852:"SpectralSensitivity",34855:"ISOSpeedRatings",34856:"OECF",37377:"ShutterSpeedValue",37378:"ApertureValue",37379:"BrightnessValue",37380:"ExposureBias",37381:"MaxApertureValue",37382:"SubjectDistance",37383:"MeteringMode",37384:"LightSource",37385:"Flash",37396:"SubjectArea",37386:"FocalLength",41483:"FlashEnergy",41484:"SpatialFrequencyResponse",41486:"FocalPlaneXResolution",41487:"FocalPlaneYResolution",41488:"FocalPlaneResolutionUnit",41492:"SubjectLocation",41493:"ExposureIndex",41495:"SensingMethod",41728:"FileSource",41729:"SceneType",41730:"CFAPattern",41985:"CustomRendered",41986:"ExposureMode",41987:"WhiteBalance",41988:"DigitalZoomRation",41989:"FocalLengthIn35mmFilm",41990:"SceneCaptureType",41991:"GainControl",41992:"Contrast",41993:"Saturation",41994:"Sharpness",41995:"DeviceSettingDescription",41996:"SubjectDistanceRange",40965:"InteroperabilityIFDPointer",42016:"ImageUniqueID"},e=c.TiffTags={256:"ImageWidth",257:"ImageHeight",34665:"ExifIFDPointer",34853:"GPSInfoIFDPointer",40965:"InteroperabilityIFDPointer",258:"BitsPerSample",259:"Compression",262:"PhotometricInterpretation",274:"Orientation",277:"SamplesPerPixel",284:"PlanarConfiguration",530:"YCbCrSubSampling",531:"YCbCrPositioning",282:"XResolution",283:"YResolution",296:"ResolutionUnit",273:"StripOffsets",278:"RowsPerStrip",279:"StripByteCounts",513:"JPEGInterchangeFormat",514:"JPEGInterchangeFormatLength",301:"TransferFunction",318:"WhitePoint",319:"PrimaryChromaticities",529:"YCbCrCoefficients",532:"ReferenceBlackWhite",306:"DateTime",270:"ImageDescription",271:"Make",272:"Model",305:"Software",315:"Artist",33432:"Copyright"},f=c.GPSTags={0:"GPSVersionID",1:"GPSLatitudeRef",2:"GPSLatitude",3:"GPSLongitudeRef",4:"GPSLongitude",5:"GPSAltitudeRef",6:"GPSAltitude",7:"GPSTimeStamp",8:"GPSSatellites",9:"GPSStatus",10:"GPSMeasureMode",11:"GPSDOP",12:"GPSSpeedRef",13:"GPSSpeed",14:"GPSTrackRef",15:"GPSTrack",16:"GPSImgDirectionRef",17:"GPSImgDirection",18:"GPSMapDatum",19:"GPSDestLatitudeRef",20:"GPSDestLatitude",21:"GPSDestLongitudeRef",22:"GPSDestLongitude",23:"GPSDestBearingRef",24:"GPSDestBearing",25:"GPSDestDistanceRef",26:"GPSDestDistance",27:"GPSProcessingMethod",28:"GPSAreaInformation",29:"GPSDateStamp",30:"GPSDifferential"},g=c.StringValues={ExposureProgram:{0:"Not defined",1:"Manual",2:"Normal program",3:"Aperture priority",4:"Shutter priority",5:"Creative program",6:"Action program",7:"Portrait mode",8:"Landscape mode"},MeteringMode:{0:"Unknown",1:"Average",2:"CenterWeightedAverage",3:"Spot",4:"MultiSpot",5:"Pattern",6:"Partial",255:"Other"},LightSource:{0:"Unknown",1:"Daylight",2:"Fluorescent",3:"Tungsten (incandescent light)",4:"Flash",9:"Fine weather",10:"Cloudy weather",11:"Shade",12:"Daylight fluorescent (D 5700 - 7100K)",13:"Day white fluorescent (N 4600 - 5400K)",14:"Cool white fluorescent (W 3900 - 4500K)",15:"White fluorescent (WW 3200 - 3700K)",17:"Standard light A",18:"Standard light B",19:"Standard light C",20:"D55",21:"D65",22:"D75",23:"D50",24:"ISO studio tungsten",255:"Other"},Flash:{0:"Flash did not fire",1:"Flash fired",5:"Strobe return light not detected",7:"Strobe return light detected",9:"Flash fired, compulsory flash mode",13:"Flash fired, compulsory flash mode, return light not detected",15:"Flash fired, compulsory flash mode, return light detected",16:"Flash did not fire, compulsory flash mode",24:"Flash did not fire, auto mode",25:"Flash fired, auto mode",29:"Flash fired, auto mode, return light not detected",31:"Flash fired, auto mode, return light detected",32:"No flash function",65:"Flash fired, red-eye reduction mode",69:"Flash fired, red-eye reduction mode, return light not detected",71:"Flash fired, red-eye reduction mode, return light detected",73:"Flash fired, compulsory flash mode, red-eye reduction mode",77:"Flash fired, compulsory flash mode, red-eye reduction mode, return light not detected",79:"Flash fired, compulsory flash mode, red-eye reduction mode, return light detected",89:"Flash fired, auto mode, red-eye reduction mode",93:"Flash fired, auto mode, return light not detected, red-eye reduction mode",95:"Flash fired, auto mode, return light detected, red-eye reduction mode"},SensingMethod:{1:"Not defined",2:"One-chip color area sensor",3:"Two-chip color area sensor",4:"Three-chip color area sensor",5:"Color sequential area sensor",7:"Trilinear sensor",8:"Color sequential linear sensor"},SceneCaptureType:{0:"Standard",1:"Landscape",2:"Portrait",3:"Night scene"},SceneType:{1:"Directly photographed"},CustomRendered:{0:"Normal process",1:"Custom process"},WhiteBalance:{0:"Auto white balance",1:"Manual white balance"},GainControl:{0:"None",1:"Low gain up",2:"High gain up",3:"Low gain down",4:"High gain down"},Contrast:{0:"Normal",1:"Soft",2:"Hard"},Saturation:{0:"Normal",1:"Low saturation",2:"High saturation"},Sharpness:{0:"Normal",1:"Soft",2:"Hard"},SubjectDistanceRange:{0:"Unknown",1:"Macro",2:"Close view",3:"Distant view"},FileSource:{3:"DSC"},Components:{0:"",1:"Y",2:"Cb",3:"Cr",4:"R",5:"G",6:"B"}},p={120:"caption",110:"credit",25:"keywords",55:"dateCreated",80:"byline",85:"bylineTitle",122:"captionWriter",105:"headline",116:"copyright",15:"category"},c.getData=function(a,b){return(a instanceof Image||a instanceof HTMLImageElement)&&!a.complete?!1:(i(a)?b&&b.call(a):l(a,b),!0)},c.getTag=function(a,b){return i(a)?a.exifdata[b]:void 0},c.getAllTags=function(a){if(!i(a))return{};var b,c=a.exifdata,d={};for(b in c)c.hasOwnProperty(b)&&(d[b]=c[b]);return d},c.pretty=function(a){if(!i(a))return"";var b,c=a.exifdata,d="";for(b in c)c.hasOwnProperty(b)&&(d+="object"==typeof c[b]?c[b]instanceof Number?b+" : "+c[b]+" ["+c[b].numerator+"/"+c[b].denominator+"]\r\n":b+" : ["+c[b].length+" values]\r\n":b+" : "+c[b]+"\r\n");return d},c.readFromBinaryFile=function(a){return m(a)},"function"==typeof define&&define.amd&&define("exif-js",[],function(){return c})}.call(this);iu.widget("quadrilateral",{config:{max:1,angle:0,pointAttrs:{r:2,fill:"transparent",stroke:"red","stroke-width":1},centerAttrs:{r:1,fill:"red"},lineAttrs:{stroke:"red","stroke-width":2},textAttrs:{fill:"red"},enable:true,resizeEnable:true,diffXMin:10,diffYMin:10},init:function(){var me=this;me.list=[];if(me.config.enable){me.$element.on("mousedown",me.events.start);me.initEvent()}},initEvent:function(){$(document).on("contextmenu",function(e){if(e.target.tagName=="svg"||e.target.tagName=="rect"||e.target.tagName=="circle"||e.target.tagName=="polygon"){return false}})},destroy:function(){var me=this,e=me.events;me.$element.off("mousedown",e.start);if(me.$svg){iu.$(document).off("mouseup",e.stop).off("selectstart",false);iu.$(window).off("resize",e.resize);me.$svg.remove()}},getPoints:function(){var me=this,i=0,j,item,list,ret=[];while(list=me.list[i++]){if(!list.shaped){return false}item={};item["block_type"]=iu.$(list.label).data("id")?iu.$(list.label).data("id"):"";list=me.sortPoint(list);for(j=0;j<4;j++){item["x"+j]=+list[j]._get("cx");item["y"+j]=+list[j]._get("cy")}item["angle"]=list[0]._get("angle");ret.push(item)}ret=me.config.max>1?ret:ret[0];return ret&&(ret.length||ret.hasOwnProperty("x0"))?ret:false},getTop:function(){return this.list[this.list.length-1]},create:function(name){var me=this,node=document.createElementNS("http://www.w3.org/2000/svg",name);node._get=me._get;node._set=me._set;return node},_get:function(name){return this.getAttribute(name)},_set:function(name,value){this.setAttribute(name,value);return this},createList:function(){this.list.push([])},listSize:function(){return this.list.length},topSize:function(){return(this.list[this.list.length-1]||0).length},createSVG:function(){var me=this,$element=me.$element,svg=me.create("svg"),position=$element.position();me.svg=svg;me.$svg=iu.$(svg);me.$svg.attr("class","iu-quadrilateral").css("position","absolute").on("mousedown",me.events.start);me.resize();$element.after(svg);if(me.config.resizeEnable){iu.$(window).on("resize",me.events.resize)}},createPoint:function(){var me=this,i=me.topSize(),initial;if(i<4){initial=me.initial;initial.pointCX=initial.x;initial.pointCY=initial.y;me.point=me.insertPoint(initial.x,initial.y);me.move=i==0?me.moveCreate:me.movePoint}me.current=me.getTop()},createShape:function(){var me=this,i,list=me.current,line,start,stop,first=me.findFirst("CIRCLE");if(list.shaped){return}i=me.sortPoint(list);list[0]=i[0];list[1]=i[1];list[2]=i[2];list[3]=i[3];list.shaped=1;for(i=0;i<4;i++){start=list[i];stop=list[i+1]||list[0];line=me.create("line");iu.$(line).insertBefore(first).attr(iu.lib.copy({x1:start._get("cx"),y1:start._get("cy"),x2:stop._get("cx"),y2:stop._get("cy")},me.config.lineAttrs));line._start=start;line._stop=stop;start._out=line;stop._in=line}},createMove:function(target){var me=this,name=target.tagName.toUpperCase();if(!me.config.enable)return;me.clearMove();me.current=me.findCurrent(name,target);if(name==="CIRCLE")me.createMovePoint(target);else if(name==="POLYGON")me.createMoveRect(target);else if(name==="LINE")me.createMoveLine(target)},select:function(target){var me=this,name=target.tagName.toUpperCase();me.clearSelected();me.current=me.findCurrent(name,target);if(me.current&&me.current.length){me.current.forEach(function(item){if(item.tagName){$(item._out).attr("stroke","#0f0")}})}},clearSelected:function(){var me=this;if(me.current){if(me.current.label&&$(me.current.label).data("id")>1){return}me.current.forEach(function(item){if(item.tagName){$(item._out).attr("stroke","red")}})}},createMoveLine:function(line){var me=this,initial=me.initial;initial.lineX1=+line._get("x1")||0;initial.lineY1=+line._get("y1")||0;initial.lineX2=+line._get("x2")||0;initial.lineY2=+line._get("y2")||0;me.line=line;me.move=me.moveLine},createMovePoint:function(point){var me=this,initial=me.initial;if(point._circle){point=point._circle}initial.pointCX=+point._get("cx")||0;initial.pointCY=+point._get("cy")||0;me.point=point;me.move=me.movePoint},createMoveRect:function(){var me=this,initial=me.initial,list=me.current,i,p;for(i=0;i<4;i++){p=list[i];initial["initialCX"+i]=+p._get("cx");initial["initialCY"+i]=+p._get("cy")}me.move=me.moveRect},moveCreate:function(offset){var me=this,initial=me.initial,list=me.getTop(),x1=initial.x,y1=initial.y,x2=offset.x,y2=offset.y;if(!me.config.enable)return;if(!list.shaped){if(Math.abs(x2-x1)<10&&Math.abs(y2-y1)<10){return}me.insertPoint(x2,y1);me.insertPoint(x2,y2);me.insertPoint(x1,y2);me.createShape()}list[1]._set("cx",x2);list[2]._set("cx",x2)._set("cy",y2);list[3]._set("cy",y2);list[1]._center._set("cx",x2);list[2]._center._set("cx",x2)._set("cy",y2);list[3]._center._set("cy",y2);list[1]._in._set("x2",x2);list[2]._in._set("x1",x2)._set("x2",x2)._set("y2",y2);list[3]._in._set("x1",x2)._set("y1",y2)._set("y2",y2);list[0]._in._set("y1",y2)},moveLine:function(offset){var me=this,line=me.line,pointStart=line._start,pointStop=line._stop,initial=me.initial,diffX=offset.x-initial.x,diffY=offset.y-initial.y,x1=initial.lineX1+diffX,y1=initial.lineY1+diffY,x2=initial.lineX2+diffX,y2=initial.lineY2+diffY;line._set("x1",x1)._set("y1",y1)._set("x2",x2)._set("y2",y2);if(pointStart){pointStart._set("cx",x1)._set("cy",y1);pointStart._center._set("cx",x1)._set("cy",y1);pointStart._in._set("x2",x1)._set("y2",y1)}if(pointStop){pointStop._set("cx",x2)._set("cy",y2);pointStop._center._set("cx",x2)._set("cy",y2);pointStop._out._set("x1",x2)._set("y1",y2)}},movePoint:function(offset){var me=this,point=me.point,lineIn=point._in,lineOut=point._out,initial=me.initial,x2=offset.x-initial.x+initial.pointCX,y2=offset.y-initial.y+initial.pointCY;point._set("cx",x2)._set("cy",y2);point._center._set("cx",x2)._set("cy",y2);if(lineIn)lineIn._set("x2",x2)._set("y2",y2);if(lineOut)lineOut._set("x1",x2)._set("y1",y2)},moveRect:function(offset){var me=this,i,line,start,stop,initial=me.initial,x,y,list=me.current,diffX=offset.x-initial.x,diffY=offset.y-initial.y,maxX=me.$svg.attr("width")-1,maxY=me.$svg.attr("height")-1;for(i=0;i<4;i++){x=initial["initialCX"+i]+diffX;y=initial["initialCY"+i]+diffY;if(x<0||y<0||x>maxX||y>maxY){me.initial=offset;me.createMoveRect();return false}}for(i=0;i<4;i++){x=initial["initialCX"+i]+diffX;y=initial["initialCY"+i]+diffY;start=list[i];start._set("cx",x)._set("cy",y);start._center._set("cx",x)._set("cy",y)}for(i=0;i<4;i++){line=list[i]._out;start=line._start;stop=line._stop;line._set("x1",start._get("cx"))._set("y1",start._get("cy"))._set("x2",stop._get("cx"))._set("y2",stop._get("cy"))}},clearMove:function(){this.current=0;this.move=iu.lib.noop},destroyElement:function(){var me=this,i,item,list=me.current,svg=me.svg;i=me.list.indexOf(list);me.list.splice(i,1);i=0;while(item=list[i++]){svg.removeChild(item);if(item._in)svg.removeChild(item._in);if(item._center)svg.removeChild(item._center)}if(list.mask)svg.removeChild(list.mask);if(list.label)svg.removeChild(list.label)},destroyCurrent:function(name,target){var me=this;me.current=me.findCurrent(name,target);if(me.current){me.destroyElement()}},findCurrent:function(name,target){var me=this,i=0,j,ret,item,current;if(name==="CIRCLE"){while(ret=me.list[i++]){j=0;while(item=ret[j++]){if(item===target||item._center===target)current=ret}}}else if(name==="POLYGON"){while(ret=me.list[i++]){if(ret.mask===target)current=ret}}else if(name==="LINE"){while(ret=me.list[i++]){j=0;while(item=ret[j++]){if(item._in===target)current=ret}}}return current},findFirst:function(tagName){var i=this.svg.firstElementChild;do{if(i.tagName.toUpperCase()===tagName){return i}}while(i=i.nextElementSibling)},insertPoint:function(x,y){var me=this,point,center,text;point=me.create("circle");iu.$(point).attr(iu.lib.copy({cx:x,cy:y},me.config.pointAttrs));me.getTop().push(point);me.$svg.append(point);center=me.create("circle");iu.$(center).attr(iu.lib.copy({cx:x,cy:y},me.config.centerAttrs));me.$svg.append(center);point._center=center;center._circle=point;return point},resize:function(){var me=this,$element=me.$element,rect=$element.position();if(me.config.angle===90||me.config.angle===-90||me.config.angle===270){me.$svg.attr({width:$element.height(),height:$element.width()}).css({top:rect.top,left:rect.left})}else{me.$svg.attr({width:$element.width(),height:$element.height()}).css({top:rect.top,left:rect.left})}},externalResize:function(angle){var me=this,$element=me.$element;if(me.$svg){if(angle===90||angle===-90||angle===270){me.$svg.attr({width:$element.height(),height:$element.width()})}else{me.$svg.attr({width:$element.width(),height:$element.height()})}}},setPointsAngle:function(angle){var me=this,i,j,length;for(i=0,length=me.list.length;i<length;i++){if(me.list[i].shaped){for(j=0;j<4;j++){if(!me.list[i][j]._get("angle")){me.list[i][j]._set("angle",angle)}}}}},sortPoint:function(points){var i,p,list=[];for(i=0;i<4;i++){p=points[i];list[i]={p:p,x:+p._get("cx"),y:+p._get("cy")}}for(var j=0;j<list.length-1;j++){for(var k=0;k<list.length-1-j;k++){if(parseInt(list[k].x)>parseInt(list[k+1].x)){var temp=list[k];list[k]=list[k+1];list[k+1]=temp}}}if(list[1].y<=list[0].y){p=list[0];list[0]=list[1];list[1]=p}if(list[3].y<=list[2].y){p=list[2];list[2]=list[3];list[3]=p}return[list[0].p,list[2].p,list[3].p,list[1].p]},updateMask:function(){var me=this,i,list=me.current,points,mask,label;if(!list){return}mask=list.mask||me.create("polygon");points=[];for(i=0;i<4;i++){points.push(list[i]._get("cx")+","+list[i]._get("cy"))}mask._set("points",points.join(" "));if(!list.mask){list.mask=mask;mask._set("fill","transparent");me.svg.insertBefore(mask,me.findFirst("LINE"))}label=list.label||me.create("text");list.label=label;iu.$(label).attr(iu.lib.copy({x:list[0]._get("cx"),y:list[0]._get("cy")-5},me.config.textAttrs));me.svg.insertBefore(label,me.findFirst("LINE"))},getMousePosition:function(e){var scrollX=document.documentElement.scrollLeft||document.body.scrollLeft,scrollY=document.documentElement.scrollTop||document.body.scrollTop,x=e.pageX||e.clientX+scrollX,y=e.pageY||e.clientY+scrollY;return{x:x,y:y}},getOffsetRect:function(ele){var box=ele.getBoundingClientRect();var body=document.body,docElem=document.documentElement;var scrollTop=window.pageYOffset||docElem.scrollTop||body.scrollTop,scrollLeft=window.pageXOffset||docElem.scrollLeft||body.scrollLeft;var clientTop=docElem.clientTop||body.clientTop,clientLeft=docElem.clientLeft||body.clientLeft;var top=box.top+scrollTop-clientTop,left=box.left+scrollLeft-clientLeft;return{x:Math.round(left),y:Math.round(top)}},getSvgOffset:function(e,node){var me=this;e=e.originalEvent||e;var elePosition=me.getOffsetRect(node);var tag=e.target,x,y;x=e.pageX-(elePosition.x||0);y=e.pageY-(elePosition.y||0);return{x:x,y:y}},createOriginalPoint:function(x,y){var me=this,svg=me.svg;me.initial={x:x,y:y};if(me.listSize()<me.config.max){me.topSize()<4||me.createList()}if(me.topSize()<4){me.createPoint();if(me.topSize()===4){me.createShape()}}else{me.clearMove()}me.$svg.on("mousemove",me.events.move);iu.$(document).on("mouseup",me.events.stop).on("selectstart",false);me.events.stop()},setLabel:function(alias,id,color){var me=this,text;if(me.current&&me.current.shaped){labelItem=me.current.label;iu.$(labelItem).data("id",id);me.current.forEach(function(item){if(item.tagName){$(item._out).attr("stroke",color)}})}},removeLabel:function(){var me=this,text;if(me.current.shaped){labelItem=me.current.label;iu.$(labelItem).html("").data("id","")}},events:{start:function(e){var me=this,svg=me.svg;if(!me.config.enable)return;if(e.which===1){me.initial=iu.lib.offset(e,svg||me.element);if(svg){if(e.target===svg){me.clearSelected();if(me.listSize()<me.config.max){me.topSize()<4||me.createList()}if(me.topSize()<4){me.createPoint();if(me.topSize()===4){me.createShape()}}else{me.clearMove()}}else{me.select(e.target);me.createMove(e.target)}}else{me.createSVG();me.createList();me.createPoint()}me.$svg.on("mousemove",me.events.move);iu.$(document).on("mouseup",me.events.stop).on("selectstart",false)}else if(e.which===3){me.destroyCurrent(e.target.tagName.toUpperCase(),e.target)}else{}},move:function(e){var me=this,offset=iu.lib.offset(e,me.svg);me.move(offset)},stop:function(){var me=this,diffX,diffY,list=me.current;me.$svg.off("mousemove",me.events.move);iu.$(document).off("mouseup",me.events.stop).off("selectstart",false);if(list&&list.shaped){diffX=Math.max(Math.abs(list[1]._get("cx")-list[0]._get("cx")),Math.abs(list[2]._get("cx")-list[3]._get("cx")));diffY=Math.max(Math.abs(list[3]._get("cy")-list[0]._get("cy")),Math.abs(list[2]._get("cy")-list[1]._get("cy")));if(diffX<me.config.diffXMin&&diffY<me.config.diffYMin){me.destroyElement();return}}if(me.topSize()===4){me.updateMask()}},resize:function(){this.resize()}}});ready(function(){var RECOG_URL=$("#ocr-server").val()+"ocr_service";var IMAGE_BASE64="/image/base64";var API_API_INFO="api/detail";var API_CONF_INVOICES="conf/common_invoices";var iuZoom;var iuQuad;var service=$("#api-service").val();var $image;var $container;var image_scale;var DEMO_APP_KEY="ai_demo_"+service;var DEMO_APP_SECRET="ai_demo_"+service;var API_NEED_LANG=["business_card"];var pdfLoaded=false;var pdfContext;var pdfFile;var vm=new Vue({el:"#vue",data:{canPay:AIBASE.canPay,activeTab:0,activeSample:"",sampleList:[],base64WithHead:"",uploadedUrl:"",loading:{recog:false,getBase64:false,createPosition:false},recogInfo:{text:"",status:0},recogData:"",viewType:"text",transformSlide:0,currentSample:"",currentSampleIndex:-1,apiInfo:{},angle:0,activeRecogText:"",base64Head:DPS.base64JPG,mode:"",recogItemList:"",lineList:"",recogDataList:"",checkedLangList:[],langList:{1:"英语",2:"中文(简体)",3:"中文(繁体)",4:"日语",5:"韩语",6:"法语",7:"西班牙语",8:"葡萄牙语",9:"德语",10:"意大利语",11:"荷兰语",12:"俄语",13:"希腊语",14:"土耳其语",15:"瑞典语",16:"芬兰语",17:"丹麦语",18:"挪威语",19:"匈牙利语"},API_NEED_LANG:API_NEED_LANG,invoiceList:[],file:"",isPdf:""},methods:{init:function(){vm.getConf();vm.initEvent();vm.initSample();vm.getApiInfo()},initEvent:function(){vm.initDropUpload();vm.initUpload();iuZoom=iu.zoom($(".preview-container"));iuZoom.disable()},initDropUpload:function(){var target=document.querySelector(".preview-container");target.addEventListener("dragover",function(e){e.preventDefault();target.classList.add("dragging")});target.addEventListener("dragleave",function(){target.classList.remove("dragging")});target.addEventListener("drop",function(e){e.preventDefault();target.classList.remove("dragging");var fileList=e.dataTransfer.files;if(fileList[0]){vm.beforeUpload();vm.uploadFile(fileList[0])}})},initUpload:function(){iu.upload(".upload-item",{ext:"jpg,jpeg,png,pdf",change:function(fileList){var file=fileList[0];if(file){vm.beforeUpload();vm.uploadFile(file)}},submit:function(){return false}})},beforeUpload:function(){vm.uploadedUrl="";vm.base64WithHead="";vm.currentSampleIndex=-1;vm.currentSample="";vm.isPdf=false},initPdfJs:function(){vm.isPdf=true;if(pdfLoaded)return true;var cdn=$("#cdn-url").val();var scriptEle=document.createElement("script");if(scriptEle.readystate){scriptEle.onreadystatechange=function(){if(scriptEle.readyState==="loaded"||script.readyState==="complete"){callback();scriptEle.onreadystatechange=null}}}else{scriptEle.onload=function(e){callback()};scriptEle.onerror=function(e){callback()}}scriptEle.src=cdn+"dps/vendor/pdf/pdf.min.js";document.body.appendChild(scriptEle);var callback=function(){pdfLoaded=true;PDFJS.cMapUrl="/site/static/cmap/";PDFJS.cMapPacked=true;PDFJS.workerSrc=cdn+"dps/vendor/pdf/pdf.worker.min.js"}},initSample:function(){},getConf:function(){AIBASE.get(API_CONF_INVOICES,{},function(ret){if(ret.length){vm.invoiceList=ret}})},getApiInfo:function(){service=$("#api-service").val();if(!service)return;AIBASE.get(API_API_INFO,{service:service},function(ret){if(ret.id){vm.apiInfo=ret;if(ret.sample_list&&ret.sample_list.length){vm.sampleList=ret.sample_list}}})},readPdf:function(pdf,callback){var tmpInterval=window.setInterval(function(){if(pdfLoaded){window.clearInterval(tmpInterval);var size=pdf.size;if(size<=10*1024*1024){pdfFile=pdf;var reader=new FileReader;reader.readAsDataURL(pdf);reader.onload=function(){var result=reader.result;vm.showPDF(result)};var reader2=new FileReader;reader2.onload=function(e){var result=e.target.result;callback(result)};reader2.readAsArrayBuffer(pdf)}else{DPS.notice.warn("PDF文件过大，请控制在10M内")}}})},createPdfCanvas:function(i){var canvas=document.createElement("canvas");canvas.setAttribute("id","pdf-canvas-"+i);var parent=document.getElementsByClassName("pdf-container")[0];parent.appendChild(canvas);return canvas},showPDF:function(result){PDFJS.getDocument(result).then(function(doc){var callback=function(doc,i){if(i>=1&&i<=doc.numPages){var pdfCanvas=vm.createPdfCanvas(i);doc.getPage(i).then(function(page){var scale=1,viewport=page.getViewport(scale),renderContext,filename;pdfContext=pdfCanvas.getContext("2d");var w=$(".preview-container").width();scale=w/viewport.width;pdfCanvas.height=viewport.height*(w/viewport.width);pdfCanvas.width=w;viewport=page.getViewport(scale);renderContext={canvasContext:pdfContext,viewport:viewport};page.render(renderContext).then(function(){filename=pdfFile.name+"_"+i+".jpg";if(doc.numPages===1){}else{setTimeout(function(){callback(doc,i+1)},1)}})})}};callback(doc,1)})},readFile:function(file,callback){var size=file.size;vm.filename=file.name;function func(file){var reader1=new FileReader;reader1.onload=function(e){var result=e.target.result;vm.uploadedUrl=result};reader1.readAsDataURL(file);var reader2=new FileReader;reader2.onload=function(e){var result=e.target.result;callback(result)};reader2.readAsArrayBuffer(file)}if(size>AIBASE.maxBodySize){var max=1600;lrz(file,{width:max,height:max}).then(function(ret){func(ret.file)})}else{func(file)}},getBase64:function(url,callback){if(!url)return;var file_name=DPS.url.get("file_name",url);if(!file_name){vm.loading.getBase64=false;DPS.notice.warn("原图获取失败");return}AIBASE.get(IMAGE_BASE64,{file_name:file_name},function(ret){var base64=ret.base64;if(base64){callback(base64)}else{vm.loading.getBase64=false;DPS.notice.warn("原图获取失败")}})},uploadFile:function(file){vm.file=file;var type=file.type;if(type==="application/pdf"){vm.initPdfJs();vm.readPdf(file,function(fileData){if(fileData){vm.recog(fileData)}})}else{vm.readFile(file,function(fileData){if(fileData){vm.recog(fileData)}})}},recogSample:function(url,index){if(url){if(vm.loading.recog){DPS.notice.warn("请等待本次识别完成");return}vm.activeSample=url;vm.currentSampleIndex=index;vm.currentSample=url;vm.base64WithHead=url;vm.beforeRecog();vm.loading.recog=false;vm.loading.getBase64=true;vm.getBase64(url,function(base64){vm.loading.recog=true;vm.loading.getBase64=false;AIBASE.recog({service:service,appKey:DEMO_APP_KEY,appSecret:DEMO_APP_SECRET,base64Data:base64,complete:function(ret){vm.afterRecog(ret)}})})}},recog:function(fileData){if(vm.loading.recog){DPS.notice.warn("请等待本次识别完成");return}vm.beforeRecog();var params={};if(vm.inArray(service,API_NEED_LANG)){var str=vm.checkedLangList.join(",");if(str){params.lang=str}}AIBASE.recog({service:service,appKey:DEMO_APP_KEY,appSecret:DEMO_APP_SECRET,binaryData:fileData,params:params,complete:function(ret){vm.afterRecog(ret)}})},beforeRecog:function(){vm.loading.recog=true;vm.reset()},afterRecogModeCard:function(ret){vm.mode="card";vm.jsonBuild(ret);if(!ret.result){return}var result=vm.parseRecog(ret.result);vm.recogData=result;vm.angle=vm.recogData.image_angle;if(!result){vm.jsonBuild(ret);return}if(service==="bills_crop"||service==="bills_recognize"){var list=result.object_list?result.object_list:[];if(!list.length)return;vm.loading.createPosition=true;EXIF.getData(vm.file,function(){vm.loading.createPosition=false;var all=EXIF.getAllTags(this);var orientation=EXIF.getTag(this,"Orientation");switch(orientation){case 0:vm.angle=0;break;case 6:vm.angle=-90;break;case 8:vm.angle=90;break;case 3:vm.angle=180;break;default:vm.angle=0;break}list.forEach(function(item,i){item=vm.parseRecog(item);vm.createPosition(item.position,i+1)});vm.recogDataList=list;vm.jsonBuild(vm.replaceBase64Key(ret))})}else{var list=result.ocr_data_list||result.item_list;vm.recogItemList=list||[];vm.jsonBuild(vm.replaceBase64Key(ret))}},createPosition:function(position,label,index){if(!position)return;if(!$image){$image=$("#recog-image")}if(!$container){$container=$(".image-container")}if(!iuQuad){iuQuad=iu.quadrilateral($container,{angle:vm.angle,max:2e3,enable:false,resizeEnable:false,pointAttrs:{r:0},lineAttrs:{stroke:"rgba(255, 0, 0, .6)","stroke-width":1},diffXMin:1,diffYMin:1});iuQuad.createSVG();iuQuad.createList()}if(!image_scale){var imgObj=new Image,scale;imgObj.src=$image.attr("src");image_scale=$image.width()/imgObj.width}iuQuad.$svg.hide();var points=position;var i=0;while(i<7){iuQuad.createOriginalPoint(Math.round(points[i]*image_scale),Math.round(points[i+1]*image_scale));i+=2}$(".iu-quadrilateral polygon").each(function(){var $this=$(this);if(!$this.hasClass("has-index")){$this.addClass("has-index");$this.addClass("index-"+index)}});$(".iu-quadrilateral line").each(function(){var $this=$(this);if(!$this.hasClass("has-index")){$this.addClass("has-index");$this.addClass("index-"+index)}});iuQuad.$svg.css({top:"auto",left:"auto"});if(label){var $item=iuQuad.current.label;iu.$($item).html(label)}iuQuad.$svg.show()},polygonEvent:function(){$(".iu-quadrilateral polygon").mouseenter(function(){var $this=$(this);var className=$this.prop("className").baseVal;var index=className.match(/index-(\d+)/)[1];vm.activeText(index);$(".iu-quadrilateral line.index-"+index).addClass("active");$this.addClass("active")}).mouseleave(function(){var $this=$(this);var className=$this.prop("className").baseVal;var index=className.match(/index-(\d+)/)[1];$this.removeClass("active");$(".iu-quadrilateral line.index-"+index).removeClass("active");vm.removeActiveText()})},textEvent:function(){$(".recog-line").mouseenter(function(){var $this=$(this);var className=$this.prop("className");var index=className.match(/index-(\d+)/)[1];vm.activePolygon(index);$this.addClass("active")}).mouseleave(function(){var $this=$(this);$this.removeClass("active");vm.removeActivePolygon()})},activeText:function(index){var $item=$(".recog-line.index-"+index);vm.activeRecogText=$item.text()},removeActiveText:function(){$(".recog-line").removeClass("active");vm.activeRecogText=""},activePolygon:function(index){$(".iu-quadrilateral polygon.index-"+index).addClass("active");$(".iu-quadrilateral line.index-"+index).addClass("active")},removeActivePolygon:function(){$(".iu-quadrilateral polygon").removeClass("active");$(".iu-quadrilateral line").removeClass("active")},replaceBase64Key:function(ret){var result=ret.result;var t=result.ocr_data_list||result.item_list;if(!t)return ret;var list=JSON.parse(JSON.stringify(t));if(list&&list.length){list.forEach(function(item){var key=item.key;var value=item.value;if(vm.isImgKey(key)&&value){item.value="base64..."}})}t=JSON.parse(JSON.stringify(ret));if(result.ocr_data_list){t.result.ocr_data_list=list}else if(result.item_list){t.result.item_list=list}return t},afterRecogCommonText:function(ret){vm.mode="text";vm.jsonBuild(ret);var result=ret.result;if(!result)return;var image_angle=result.image_angle;var rotated_image_height=result.rotated_image_height;var rotated_image_width=result.rotated_image_width;var lineList=[];var positionList=[];var lines=result?result.lines:"";if(lines&&lines.length){lines.forEach(function(item){if(item.text){lineList.push(item.text);positionList.push(item.position||[])}})}vm.loading.createPosition=true;EXIF.getData(vm.file,function(){vm.loading.createPosition=false;var all=EXIF.getAllTags(this);var orientation=EXIF.getTag(this,"Orientation");switch(orientation){case 0:vm.angle=0;break;case 6:vm.angle=-90;break;case 8:vm.angle=90;break;case 3:vm.angle=180;break;default:vm.angle=0;break}if(service==="text_recog_ch_en_coordinate"){var data=vm.uploadedUrl;var image=new Image;image.src=data;var image_width=image.width;var image_height=image.height;if(image_width===rotated_image_width&&(vm.angle===-90||vm.angle===90)){vm.angle=0}}setTimeout(function(){positionList.forEach(function(position,index){vm.createPosition(position,"",index)});vm.$nextTick(function(){vm.polygonEvent();vm.textEvent()})},1);vm.lineList=lineList})},afterRecogStamp:function(ret){vm.jsonBuild(ret);var result=ret.result;if(!result)return;vm.mode="stamp";var list=result.item_list;if(!list.length)return;vm.loading.createPosition=true;EXIF.getData(vm.file,function(){vm.loading.createPosition=false;var all=EXIF.getAllTags(this);var orientation=EXIF.getTag(this,"Orientation");switch(orientation){case 0:vm.angle=0;break;case 6:vm.angle=-90;break;case 8:vm.angle=90;break;case 3:vm.angle=180;break;default:vm.angle=0;break}list.forEach(function(item,i){item=vm.parseRecog(item);vm.createPosition(item.position,"印章"+(i+1))});vm.recogItemList=list||[]})},afterRecog:function(ret){vm.loading.recog=false;var code=ret.code;var errorCode=ret.error_code;code=parseInt(code);errorCode=parseInt(errorCode);var message=ret.message||ret.error_msg;if(ret.errno===413){vm.recogNotice("图片过大，请压缩后上传识别",0);return}else if(code!==200&&code!==0&&errorCode!==200&&errorCode!==0){vm.recogNotice(message||"图片中无有效识别内容或服务不可用",0);return}if(status==="error"||status==="offline"){vm.recogNotice("网络差或服务暂不可用",0);return}if(code===200||code===0||errorCode===200||errorCode===0){if(ret.result&&ret.result.type==="other"){vm.recogNotice("非可识别图片类型",1)}else{vm.recogNotice("识别成功",1)}var modeTextList=["common_text_advance","text_recog_coordinate","hand_write","hand_write_recognize","common_text_v2","common_text_advance_ch_en","text_recog_ch_en_coordinate","common_text_base_ch_en","text_recog_ch_en","text_recog_ch_en_coordinate","text_recognize_3d1"];var stampList=["recognize_stamp"];if(modeTextList.indexOf(service)>=0){vm.afterRecogCommonText(ret)}else if(stampList.indexOf(service)>=0){vm.afterRecogStamp(ret)}else{vm.afterRecogModeCard(ret)}}},parseRecog:function(ret){var list=ret.ocr_data_list;if(isArray(list)&&list.length){list.forEach(function(item){if(item.value==="")item.value="-";if(item.value===true)item.value="true";if(item.value===false)item.value="false"})}return ret},recogNotice:function(text,status){vm.recogInfo.text=text;vm.recogInfo.status=status;var T=2e3;if(status===0)T=2e3;else T=1e3;setTimeout(function(){vm.recogInfo.text=""},T)},jsonBuild:function(data){if(vm.mode==="text"){var $jsonContainer=$(".iu-jsonview-container");var w=$(".recog-data-container").width()-50;$jsonContainer.append("<pre></pre>");$(".iu-jsonview-container pre").css({"max-width":w}).append(AIBASE.JSONFormat(data))}else{iu.jsonview.insert(data,$(".iu-jsonview-container"))}},reset:function(){vm.recogData="";vm.angle=0;vm.mode="";iuZoom.reset();iuZoom.enable();if(iuQuad){iuQuad.destroy();iuQuad=null}$image=false;$container=false;image_scale=0;$(".pdf-container").empty();$(".iu-jsonview-container").html("")},checkViewType:function(id){vm.viewType=id},slideleft:function(){if(vm.transformSlide<0){vm.transformSlide=vm.transformSlide+400}},slideright:function(){if(vm.transformSlide>-800){vm.transformSlide=vm.transformSlide-400}},cardName:function(id){return AIBASE.commonCardsName(id)},isImgKey:function(key){if(!key)return false;var array=["id_number_image","crop_image","head_portrait"];return array.indexOf(key)>-1},jsonParse:function(str){if(!str)return[];return JSON.parse(str)},checkLang:function(id){id=parseInt(id);var list=vm.checkedLangList;var index=DPS.inArray(id,list);if(index===false){list.push(id)}else{list.splice(index,1)}},inLangArray:function(id){id=parseInt(id);if(!vm)return;var list=vm.checkedLangList||[];return DPS.inArray(id,list)!==false},inArray:function(id,list){return DPS.inArray(id,list)!==false},invoiceName:function(id){var list=vm.invoiceList;var name="";if(!id||!list)return name;list.forEach(function(item){if(item.type===id)name=item.name});return name||id}}});vm.init()});