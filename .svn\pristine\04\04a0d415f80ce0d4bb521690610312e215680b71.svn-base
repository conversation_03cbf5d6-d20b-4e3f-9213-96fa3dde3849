﻿using System;
using System.Collections.Generic;

namespace NewTicket
{
    public class BlackHouse
    {
        private static List<BlackTicket> LstBlack = new List<BlackTicket>();

        public static bool IsBlack(TicketEntity tic)
        {
            bool result = false;
            try
            {
                if (Settings.Default.IsBlackHouseEnable)
                {
                    lock (LstBlack)
                    {
                        LstBlack.RemoveAll(p => p == null || string.IsNullOrEmpty(p.Key) || p.ExpireTime <= CommonString.serverTime);
                        var key = tic.AttentionItem.Key + "_" + tic.TrainNo;
                        result = LstBlack.Exists(p => p.Key.Equals(key));
                    }
                }
            }
            catch (Exception oe)
            {
                Log.WriteError("验证小黑屋", oe);
            }
            return result;
        }

        public static void AddBlack(List<AttentionItem> items, string trainNo)
        {
            try
            {
                if (Settings.Default.IsBlackHouseEnable)
                {
                    if (items != null && items.Count > 0)
                    {
                        foreach (var item in items)
                        {
                            var key = item.Key + "_" + trainNo;
                            lock (LstBlack)
                            {
                                if (!LstBlack.Exists(p => p.Key.Equals(key)))
                                    LstBlack.Add(new BlackTicket(key, Settings.Default.NBlackHouse));
                            }
                        }
                    }
                }
            }
            catch (Exception oe)
            {
                Log.WriteError("添加小黑屋出错", oe);
            }
        }

        //public static void AddBlack(TicketEntity tic)
        //{
        //    try
        //    {
        //        if (Settings.Default.IsBlackHouseEnable)
        //        {
        //            if (tic != null && tic.AttentionItem != null && !string.IsNullOrEmpty(tic.TrainNo))
        //            {
        //                Add(tic.AttentionItem.Key + "_" + tic.TrainNo);
        //            }
        //        }
        //    }
        //    catch (Exception oe)
        //    {
        //        Log.WriteError(oe);
        //    }
        //}
    }

    public class BlackTicket
    {
        public BlackTicket(string ticket, double nSec = 30)
        {
            Key = ticket;
            ExpireTime = CommonString.serverTime.AddSeconds(nSec);
        }

        public string Key { get; set; }

        public DateTime ExpireTime { get; set; }
    }
}
