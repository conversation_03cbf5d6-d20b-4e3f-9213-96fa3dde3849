﻿using System;
using System.Collections.Generic;
using System.Security.Cryptography;
using CommonLib;

namespace ImageOCR
{
    public static class RndCodeHelper
    {
        #region 随机ID

        public static string GetRndIndex(bool isLogin, string result = "")
        {
            return string.Format("{3}{0}{1}{2}", ServerTime.DateTime.ToString("HHmmssfff")
                , Next(ServerTime.DateTime.Millisecond), string.IsNullOrEmpty(result) ? "" : "-" + result.Replace(",", "-"),
                isLogin ? 0 : 1);
            //return ServerTime.DateTime.ToString("HH") + CreateIdByInt().ToString("000000000");
        }

        #endregion

        #region 随机坐标相关

        private static readonly List<string> LstTmpLoc = new List<string>();
        private static readonly Random RndTmp = new Random();

        public static string GetRndCode()
        {
            var result = "";
            try
            {
                if (LstTmpLoc == null || LstTmpLoc.Count <= 0)
                {
                    GetValidaCodeRndByTmp();
                }
                if (LstTmpLoc != null) result = LstTmpLoc[RndTmp.Next(0, LstTmpLoc.Count)];
            }
            catch (Exception)
            {
                result = "38,118";
                //Console.WriteLine(oe.Message);
            }
            return result;
        }

        private static void GetValidaCodeRndByTmp()
        {
            //lstTmpLoc = new List<string>() { "187,44", "254,43", "38,118" };
            for (var j = 1; j < 9; j += 2)
            {
                for (var i = 1; i < 5; i += 2)
                {
                    LstTmpLoc.Add(string.Format("{0},{1}", j*40, i*40));
                    LstTmpLoc.AddRange(GetNextLoc(j, i));
                }
            }
        }

        private static List<string> GetNextLoc(int x = 0, int y = 0, ImageType type = ImageType.Eight)
        {
            var lstTmp = new List<string>();
            //int width, height;
            var codes = new List<int>();
            for (var j = 1; j < 9; j += 2)
            {
                for (var i = 1; i < 5; i += 2)
                {
                    if (y == i && x == j)
                        continue;
                    lstTmp.Add(string.Format("{2},{3},{0},{1}", j*40, i*40, x*40, y*40));
                }
            }
            return lstTmp;
        }

        #endregion

        #region 随机数相关

        /// <summary>
        ///     生成小于输入值绝对值的随机数
        /// </summary>
        /// <param name="NumSides"></param>
        /// <param name="numSeeds"></param>
        /// <returns></returns>
        private static int Next(int numSeeds)
        {
            //numSeeds = Math.Abs(numSeeds);
            if (numSeeds <= 1)
            {
                return 0;
            }
            var length = 4;
            if (numSeeds <= byte.MaxValue)
            {
                length = 1;
            }
            else if (numSeeds <= short.MaxValue)
            {
                length = 2;
            }
            return Next(numSeeds, length);
        }

        private static int Next(int numSeeds, int length)
        {
            // Create a byte array to hold the random value.   
            var buffer = new byte[length];
            // Create a new instance of the RNGCryptoServiceProvider.   
            var gen = new RNGCryptoServiceProvider();
            // Fill the array with a random value.   
            gen.GetBytes(buffer);
            // Convert the byte to an uint value to make the modulus operation easier.   
            uint randomResult = 0x0; //这里用uint作为生成的随机数   
            for (var i = 0; i < length; i++)
            {
                randomResult |= (uint) buffer[i] << ((length - 1 - i)*8);
            }
            // Return the random number mod the number   
            // of sides.  The possible values are zero-based   
            return (int) (randomResult%numSeeds);
        }

        #endregion
    }
}