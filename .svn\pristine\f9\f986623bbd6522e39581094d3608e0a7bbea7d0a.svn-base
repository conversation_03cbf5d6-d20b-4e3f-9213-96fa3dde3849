// Code generated by Microsoft (R) AutoRest Code Generator 1.0.1.0
// Changes may cause incorrect behavior and will be lost if the code is
// regenerated.

namespace AutorestClient.Models
{
    using Newtonsoft.Json;
    using System.Linq;

    /// <summary>
    /// Hello
    /// </summary>
    /// <remarks>
    /// Hello
    /// </remarks>
    public partial class Hello
    {
        /// <summary>
        /// Initializes a new instance of the Hello class.
        /// </summary>
        public Hello()
        {
          CustomInit();
        }

        /// <summary>
        /// Initializes a new instance of the Hello class.
        /// </summary>
        public Hello(string name = default(string), string title = default(string))
        {
            Name = name;
            Title = title;
            CustomInit();
        }

        /// <summary>
        /// An initialization method that performs custom operations like setting defaults
        /// </summary>
        partial void CustomInit();

        /// <summary>
        /// </summary>
        [JsonProperty(PropertyName = "Name")]
        public string Name { get; set; }

        /// <summary>
        /// </summary>
        [JsonProperty(PropertyName = "Title")]
        public string Title { get; set; }

    }
}
