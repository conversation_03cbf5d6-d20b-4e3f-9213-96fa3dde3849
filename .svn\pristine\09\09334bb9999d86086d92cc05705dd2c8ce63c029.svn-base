﻿using System;
using System.IO;
using System.Security.Cryptography;
using System.Text;

namespace CommonLib
{
    public class ServerUuidUtil
    {
        /// <summary>
        /// 运行模式
        /// </summary>
        public static string Mode { get; set; }

        public static ServerClientEntity GetServerInfo()
        {
            var server = new ServerClientEntity()
            {
                DomainName = Environment.UserDomainName,
                MachineName = Environment.MachineName,
                UserName = Environment.UserName,
                ProcessorCount = Environment.ProcessorCount,
                Is64BitOperatingSystem = Environment.Is64BitOperatingSystem,
                CreateTime = Directory.GetCreationTime(AppDomain.CurrentDomain.BaseDirectory),
                MemoryUsed = (Environment.WorkingSet / 1024 / 1024).ToString() + " MB",
                OS = Environment.OSVersion.ToString(),
                Mode = Mode
            };

            server.Id = ToMd5(server.UserName + server.DomainName + server.MachineName + server.CreateTime.ToString("yyyy-MM-dd HH:mm:ss fff") + server.Mode);
            return server;
        }

        static string ToMd5(string s, int len = 32)
        {
            using (var md5Hasher = new MD5CryptoServiceProvider())
            {
                var data = md5Hasher.ComputeHash(Encoding.UTF8.GetBytes(s));
                var sb = new StringBuilder();
                foreach (var t in data)
                {
                    sb.Append(t.ToString("x2"));
                }
                var result = sb.ToString();

                return len == 32 ? result : result.Substring(8, 16);
            }
        }
    }
    public class ServerClientEntity
    {
        public string Id { get; set; }

        public string DomainName { get; set; }

        public string UserName { get; set; }

        public string MachineId { get; set; }

        public int ProcessorCount { get; set; }
        public bool Is64BitOperatingSystem { get; set; }
        public string MachineName { get; set; }
        public DateTime CreateTime { get; set; }
        public string MemoryUsed { get; set; }
        public string MachineGuid { get; set; }
        public string OS { get; set; }
        public string Mode { get; set; } = "Service";
    }
}
