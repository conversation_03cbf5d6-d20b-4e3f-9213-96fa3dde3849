$(document).ready((function(){var t=[{title:"短信",text:"短信通知/营销/验证码",src:"/sms",img_src:"/libraries_v4/view/home/<USER>/s_1.png"},{title:"邮件",text:"在线邮件发送平台",src:"/mail",img_src:"/libraries_v4/view/home/<USER>/s_6.png"},{title:"多媒体彩信",text:"图文彩信/视频彩信",src:"/mms",img_src:"/libraries_v4/view/home/<USER>/s_4.png"},{title:"语音",text:"语音通知/在线群呼",src:"/voice",img_src:"/libraries_v4/view/home/<USER>/s_3.png"},{title:"短网址",text:"快速整合/自定义域名",src:"/shorturl",img_src:"/libraries_v4/view/home/<USER>/s_5.png"},{title:"国际短信",text:"全球覆盖/多国语言",src:"/internationalsms",img_src:"/libraries_v4/view/home/<USER>/s_2.png"},{title:"智慧短信",text:"短信品宣传/短信公众号",src:"/smartsms",img_src:"/libraries_v4/view/home/<USER>/s_7.png"},{title:"免密登录",text:"一键登录/本机号码认证",src:"/onepass",img_src:"/libraries_v4/view/home/<USER>/s_10.png"},{title:"身份验证",text:"认证二要素/三要素",src:"/factor",img_src:"/libraries_v4/view/home/<USER>/s_8.png"},{title:"5G消息",text:"智能交互/富媒体卡片",src:"/rcs",img_src:"/libraries_v4/view/home/<USER>/s_9.png"}],e={bg_base_url:"/libraries_v4/view/home/<USER>/partner/landmark/",init:function(){var t=this;new Swiper(".swiper-container",{loop:!0,autoplay:0,prevButton:".swiper-button-prev",nextButton:".swiper-button-next",centeredSlides:!1,slidesPerView:3.5,breakpoints:{575:{slidesPerView:1},767:{slidesPerView:1.2},991:{slidesPerView:1.5},1199:{slidesPerView:2},1399:{slidesPerView:2.2},1599:{slidesPerView:2.5},1899:{slidesPerView:3}},onTransitionStart:function(t){$(".swiper-slide-prev .swiper-card").css("opacity",0)},onTransitionEnd:function(t){$(".swiper-slide-prev .swiper-card").css("opacity",1)},onSlideChangeStart:function(e){$(".solve-parters .bg").animate({opacity:.85},150,(function(){var e=t.bg_base_url+$(".swiper-slide-active").data("bg0"),s=t.bg_base_url+$(".swiper-slide-active").data("bg1");$(".solve-parters .bg_1").attr("src",e),$(".solve-parters .bg_2").attr("src",s)})).animate({opacity:1},150)},onSlideChangeEnd:function(t){}}),clearInterval((function(){e.autoClicksave()})),setInterval((function(){e.autoClicksave()}),1e4)},autoClicksave:function(){$(".swiper-button-next").click()}};({activeIndex:0,list:[{title:"游戏研发",text:"云通信服务在游戏研发时，可用于邀请玩家参与测试调研；在游戏运营中，可用于处理玩家的问题、投诉或建议。可以提高玩家的参与度和留存率，助力市场表现。",menus:[{title:"预约提醒",src:""},{title:"充值到账",src:""},{title:"版本更新",src:""},{title:"赛事资讯",src:""}]},{title:"社区论坛",text:"向用户发送最新通知，确保用户能够及时获取相关信息；鼓励用户之间进行互动，增强社区的社交性和互动性，吸引更多玩家关注，促进社区论坛的运营。",menus:[{title:"游戏宣传",src:""},{title:"节日营销",src:""},{title:"活动推广",src:""},{title:"唤醒促活",src:""}]},{title:"交易平台",text:"利用好云通信服务可以增加平台收入，为玩家提供更优质的服务和体验。同时也有助于提高平台的知名度和竞争力，吸引更多的玩家和用户。",menus:[{title:"注册登录",src:""},{title:"人事招聘",src:""},{title:"智能客服",src:""},{title:"赛事筹备",src:""}]}],init:function(){this.initDots(),this.setDot(0)},initDots:function(){var t=this,e="";this.list.forEach((function(t,s){e+=`<div class="dot-item color-gray" data-index="${s}"><span>${t.title}</span></div>`})),$(".solve-navs .dots").empty().append(e),$(".solve-navs .dot-item").on("click",(function(){var e=$(this).data("index");e!=t.activeIndex&&(t.activeIndex=e,t.setDot(e))}))},setDot:function(t){$(".solve-navs .dots").children().each((function(){$(this).removeClass("active")})).eq(t).addClass("active"),$(".solve-navs .content-title").empty().text(this.list[t].title),$(".solve-navs .content-text").empty().text(this.list[t].text),this.initMenus(t),this.setRandomProducts()},initMenus:function(t){var e="",s=this.list[t].menus.length;this.list[t].menus.forEach((function(t,i){e+=`<b class="menu-item color-gray fn14" data-index="${i}" style="width: ${100/s}%">${t.title}</b>`})),$(".solve-navs .menus").empty().append(e),$("#solve-navs .nav-mobile-wrap img").eq(t).show().siblings().hide(),s>=5&&($(".solve-navs .menus-wrap").css("width","100%"),$(".solve-navs .menu-item").css("font-size","12px")),$(".solve-navs .menu-item").on("mouseenter",(function(){$(this).data("index");$(".solve-navs .menus").children().each((function(){$(this).removeClass("active")}))})),$(".solve-navs .menu-item").on("click",(function(){var t=$(this).data("index");$(".solve-navs .menus-bg").css("transform",`translateX(${t}00%)`),$(".solve-navs .menus").children().each((function(){$(this).removeClass("active")}))}))},setRandomProducts:function(){var e=_.sampleSize(t,4),s="";e.forEach((function(t){s+=`\n          <div class="prod-item px-2 col-6 mt-3">\n              <a href="${t.src}" class="prod-item-content p-3 bg-linear-card">\n                  <img class="prod-img" src="${t.img_src}" alt="">\n                  <div class="ml-2 mt-1 mb-1">\n                      <b class="block">${t.title}</b>\n                      <span class="fn14 color-gray">${t.text}</span>\n                  </div>\n              </a>\n          </div>\n        `})),$(".solve-navs .prod-wrap").empty().append(s)}}).init(),e.init()}));