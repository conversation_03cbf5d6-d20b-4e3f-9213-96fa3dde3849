﻿using System.Collections.Generic;
using CommonLib;

namespace HanZiOcr
{
    /// <summary>
    /// 搜狗图片
    /// </summary>
    public class VivoRec : BaseOcrRec
    {
        public VivoRec()
        {
            OcrGroup = OcrGroupType.VIVO;
            OcrType = HanZiOcrType.VIVO;
            MaxExecPerTime = 20;

            //IsProcessJsonResultByArray = false;
            IsSupportVertical = true;
            IsPercentSize = true;
            LstJsonPreProcessArray = new List<object>() { "ocr", "wordCoordinate" };
            StrResultJsonSpilt = "words";
            LstVerticalLocation = new List<object> { "location" };
        }

        protected override string GetHtml(OcrContent content)
        {
            var url = "https://picanalysis.vivo.com.cn/picAnalysis/ocr.do?imei=1&em=unknown&model=1&product=1&elapsedtime=1&av=28&an=9&cs=0&sysVer=PD1911_A_1.5.30&appVersion=902380290&appVer=9.2.38.2.90&appPkgName=com.vivo.vtouch";
            var result = WebClientSyncExt.GetHtml(url, content.strBase64, ExecTimeOutSeconds);
            return result;
        }

    }
}