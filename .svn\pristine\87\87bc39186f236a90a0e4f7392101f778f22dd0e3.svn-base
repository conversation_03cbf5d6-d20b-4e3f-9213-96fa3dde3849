﻿using CommonLib;
using System.Collections.Generic;
using System.Collections.Specialized;

namespace HanZiOcr
{
    /// <summary>
    /// https://ai.trial-power.com/supermarket/texttest/
    /// </summary>
    public class ChuangJiRec : BaseOcrRec
    {
        public ChuangJiRec()
        {
            OcrType = HanZiOcrType.创迹AI;
            MaxExecPerTime = 15;

            LstJsonPreProcessArray = new List<object>() { "data", "originalData" };
            IsSupportVertical = true;
            StrResultJsonSpilt = "text";
            LstVerticalLocation = new List<object>() { "location" };
        }

        private const string url = "https://ai.trial-power.com/aiop_oauth/oauth/trial?action=aiservice-ocr-general";

        private List<string> LstToken = new List<string>
        {
            "M5dHQcvjO63fYDqqlCFTgBr/to13TTmV6IRT/n2XNe77YoOci1cEvDUW8IjWeT14dV5HKawidqd8S66IrYFqqicNhdQPe/mQniPI8Lumjsn/DJsZeeh97Gtyhka+5OLhwZA3pLNI7X+Vxa0fivSax+3niVhHQbWqAh3B9hJ8muA="
            //"ODCy+WwFXQ3k3yK8f/2826Mv8u6mu8phNrKkEJyenMqaUppSU0s2w8yl3X85LM7To9vGLNP96MBTe5bFtHHpa8xsXClz+Gfkmbbg1DZ2Tyfq8wMbVCpB+ISu1zpxPMs27vDyo7sDwDm/V8MMdWBAHYgiM2xIuMjwzIFfk5bWTPo="
            //, "UtpFsJsY8Dv/OC7ct6i/3CmQNEvNW4meD7Fwq9wHp+t27hOZq8Qz0Jje4FCLrOkUyjV8XCpJXAVby31qh75eD2RwigyeDdbsagvgJEAhWVerWfC4lmsMKIMmwIITVZOfgG0sRvzCImxGZLRV+QI5p2NfwqA4/bVCDdkx7ZaPVOY="
        };

        protected override string GetHtml(OcrContent content)
        {
            var strPost = "{\"image\":\"" + content.strBase64 + "\"}";
            var strTmp = WebClientSyncExt.GetHtml(url, "", strPost, "", ExecTimeOutSeconds, new NameValueCollection { { "Session-Status", LstToken.GetRndItem() } });

            return strTmp;
        }

    }
}