!function(t,n){"object"==typeof exports&&"undefined"!=typeof module?n(exports):"function"==typeof define&&define.amd?define(["exports"],n):n((t=t||self).mobx={})}(this,(function(t){function n(t){for(var n=arguments.length,i=new Array(n>1?n-1:0),r=1;r<n;r++)i[r-1]=arguments[r];throw new Error("number"==typeof t?"[MobX] minified error nr: "+t+(i.length?" "+i.map(String).join(","):"")+". Find the full error at: https://github.com/mobxjs/mobx/blob/main/packages/mobx/src/errors.ts":"[MobX] "+t)}function i(){return"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:An}function r(){Nn||n("Proxy not available")}function e(t){var n=!1;return function(){if(!n)return n=!0,t.apply(this,arguments)}}function u(t){return"function"==typeof t}function o(t){switch(typeof t){case"string":case"symbol":case"number":return!0}return!1}function s(t){return null!==t&&"object"==typeof t}function f(t){if(!s(t))return!1;var n=Object.getPrototypeOf(t);if(null==n)return!0;var i=Object.hasOwnProperty.call(n,"constructor")&&n.constructor;return"function"==typeof i&&i.toString()===Rn}function c(t){var n=null==t?void 0:t.constructor;return!!n&&("GeneratorFunction"===n.name||"GeneratorFunction"===n.displayName)}function a(t,n,i){Sn(t,n,{enumerable:!1,writable:!0,configurable:!0,value:i})}function h(t,n,i){Sn(t,n,{enumerable:!1,writable:!1,configurable:!0,value:i})}function v(t,n){var i="isMobX"+t;return n.prototype[i]=!0,function(t){return s(t)&&!0===t[i]}}function l(t){return t instanceof Map}function d(t){return t instanceof Set}function b(t){return null===t?null:"object"==typeof t?""+t:t}function y(t,n){return xn.hasOwnProperty.call(t,n)}function p(t,n){for(var i=0;i<n.length;i++){var r=n[i];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,r.key,r)}}function m(t,n,i){return n&&p(t.prototype,n),i&&p(t,i),Object.defineProperty(t,"prototype",{writable:!1}),t}function w(){return(w=Object.assign||function(t){for(var n=1;n<arguments.length;n++){var i=arguments[n];for(var r in i)Object.prototype.hasOwnProperty.call(i,r)&&(t[r]=i[r])}return t}).apply(this,arguments)}function j(t,n){t.prototype=Object.create(n.prototype),t.prototype.constructor=t,(Object.setPrototypeOf||function(t,n){return t.__proto__=n,t})(t,n)}function O(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}function A(t,n){(null==n||n>t.length)&&(n=t.length);for(var i=0,r=new Array(n);i<n;i++)r[i]=t[i];return r}function g(t,n){var i="undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(i)return(i=i.call(t)).next.bind(i);if(Array.isArray(t)||(i=function(t){if(t){if("string"==typeof t)return A(t,void 0);var n=Object.prototype.toString.call(t).slice(8,-1);return"Object"===n&&t.constructor&&(n=t.constructor.name),"Map"===n||"Set"===n?Array.from(t):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?A(t,void 0):void 0}}(t))||n&&t&&"number"==typeof t.length){i&&(t=i);var r=0;return function(){return r>=t.length?{done:!0}:{done:!1,value:t[r++]}}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function _(t){return Object.assign((function(n,i){S(n,i,t)}),t)}function S(t,n,i){y(t,Kn)||a(t,Kn,w({},t[Kn])),function(t){return"override"===t.t}(i)||(t[Kn][n]=i)}function x(t,n,i){void 0===n&&(n=kn),void 0===i&&(i=kn);var r=new In(t);return n!==kn&&Mt(r,n),i!==kn&&Vt(r,i),r}function M(t,n,i){return Pt(t)?t:Array.isArray(t)?Jn.array(t,{name:i}):f(t)?Jn.object(t,void 0,{name:i}):l(t)?Jn.map(t,{name:i}):d(t)?Jn.set(t,{name:i}):"function"!=typeof t||_t(t)||Kt(t)?t:c(t)?Ri(t):Si(i,t)}function V(t){return t}function N(t,n){return{t:t,i:n,u:R,o:k}}function R(t,n,i,r){var e;if(null!=(e=this.i)&&e.bound)return null===this.o(t,n,i,!1)?0:1;if(r===t.s)return null===this.o(t,n,i,!1)?0:2;if(_t(i.value))return 1;var u=E(t,this,n,i,!1);return Sn(r,n,u),2}function k(t,n,i,r){var e=E(t,this,n,i);return t.h(n,e,r)}function E(t,n,i,r,e){var u,o,s,f,c,a,h;void 0===e&&(e=yi.safeDescriptors);var v,l=r.value;return null!=(u=n.i)&&u.bound&&(l=l.bind(null!=(v=t.v)?v:t.s)),{value:$(null!=(o=null==(s=n.i)?void 0:s.name)?o:i.toString(),l,null!=(f=null==(c=n.i)?void 0:c.autoAction)&&f,null!=(a=n.i)&&a.bound?null!=(h=t.v)?h:t.s:void 0),configurable:!e||t.l,enumerable:!1,writable:!e}}function T(t,n){return{t:t,i:n,u:C,o:K}}function C(t,n,i,r){var e;if(r===t.s)return null===this.o(t,n,i,!1)?0:2;if(null!=(e=this.i)&&e.bound&&(!y(t.s,n)||!Kt(t.s[n]))&&null===this.o(t,n,i,!1))return 0;if(Kt(i.value))return 1;var u=L(t,0,0,i,!1,!1);return Sn(r,n,u),2}function K(t,n,i,r){var e,u=L(t,0,0,i,null==(e=this.i)?void 0:e.bound);return t.h(n,u,r)}function L(t,n,i,r,e,u){void 0===u&&(u=yi.safeDescriptors);var o,s=r.value;return Kt(s)||(s=Ri(s)),e&&((s=s.bind(null!=(o=t.v)?o:t.s)).isMobXFlow=!0),{value:s,configurable:!u||t.l,enumerable:!1,writable:!u}}function I(t,n){return{t:t,i:n,u:P,o:D}}function P(t,n,i){return null===this.o(t,n,i,!1)?0:1}function D(t,n,i,r){return t.p(n,w({},this.i,{get:i.get,set:i.set}),r)}function B(t,n){return{t:t,i:n,u:q,o:G}}function q(t,n,i){return null===this.o(t,n,i,!1)?0:1}function G(t,n,i,r){var e,u;return t.m(n,i.value,null!=(e=null==(u=this.i)?void 0:u.enhancer)?e:M,r)}function X(t){return{t:"true",i:t,u:W,o:H}}function W(t,n,i,r){var e,u,o,s;if(i.get)return Zn.u(t,n,i,r);if(i.set){var f=$(n.toString(),i.set);return r===t.s?null===t.h(n,{configurable:!yi.safeDescriptors||t.l,set:f})?0:2:(Sn(r,n,{configurable:!0,set:f}),2)}if(r!==t.s&&"function"==typeof i.value)return c(i.value)?(null!=(s=this.i)&&s.autoBind?Ri.bound:Ri).u(t,n,i,r):(null!=(o=this.i)&&o.autoBind?Si.bound:Si).u(t,n,i,r);var a,h=!1===(null==(e=this.i)?void 0:e.deep)?Jn.ref:Jn;return"function"==typeof i.value&&null!=(u=this.i)&&u.autoBind&&(i.value=i.value.bind(null!=(a=t.v)?a:t.s)),h.u(t,n,i,r)}function H(t,n,i,r){var e,u,o;return i.get?Zn.o(t,n,i,r):i.set?t.h(n,{configurable:!yi.safeDescriptors||t.l,set:$(n.toString(),i.set)},r):("function"==typeof i.value&&null!=(e=this.i)&&e.autoBind&&(i.value=i.value.bind(null!=(o=t.v)?o:t.s)),(!1===(null==(u=this.i)?void 0:u.deep)?Jn.ref:Jn).o(t,n,i,r))}function U(t){return t||Gn}function F(t){return!0===t.deep?M:!1===t.deep?V:(n=t.defaultDecorator)&&null!=(i=null==(r=n.i)?void 0:r.enhancer)?i:M;var n,i,r}function z(t,n,i){if(!o(n))return Pt(t)?t:f(t)?Jn.object(t,n,i):Array.isArray(t)?Jn.array(t,n):l(t)?Jn.map(t,n):d(t)?Jn.set(t,n):"object"==typeof t&&null!==t?t:Jn.box(t,n);S(t,n,Xn)}function $(t,n,i,r){function e(){return J(0,i,n,r||this,arguments)}return void 0===i&&(i=!1),e.isMobxAction=!0,ri&&(ei.value=t,Object.defineProperty(e,"name",ei)),e}function J(t,n,i,r,e){var u=Y(0,n);try{return i.apply(r,e)}catch(t){throw u.j=t,t}finally{Q(u)}}function Y(t,n){var i=yi.trackingDerivation,r=!n||!i;bt();var e=yi.allowStateChanges;r&&(st(),e=tt(!0));var u={O:r,A:i,g:e,_:ct(!0),S:!1,M:0,V:ii++,N:ni};return ni=u.V,u}function Q(t){ni!==t.V&&n(30),ni=t.N,void 0!==t.j&&(yi.suppressReactionErrors=!0),nt(t.g),at(t._),yt(),t.O&&ft(t.A),yi.suppressReactionErrors=!1}function Z(t,n){var i=tt(t);try{return n()}finally{nt(i)}}function tt(t){var n=yi.allowStateChanges;return yi.allowStateChanges=t,n}function nt(t){yi.allowStateChanges=t}function it(t){return t instanceof hi}function rt(t){switch(t.R){case ui.k:return!1;case ui.T:case ui.C:return!0;case ui.K:for(var n=ct(!0),i=st(),r=t.L,e=r.length,u=0;u<e;u++){var o=r[u];if(ai(o)){if(yi.disableErrorBoundaries)o.get();else try{o.get()}catch(t){return ft(i),at(n),!0}if(t.R===ui.C)return ft(i),at(n),!0}}return ht(t),ft(i),at(n),!1}}function et(t,n,i){var r=ct(!0);ht(t),t.I=new Array(t.L.length+100),t.P=0,t.D=++yi.runId;var e,u=yi.trackingDerivation;if(yi.trackingDerivation=t,yi.inBatch++,!0===yi.disableErrorBoundaries)e=n.call(i);else try{e=n.call(i)}catch(t){e=new hi(t)}return yi.inBatch--,yi.trackingDerivation=u,function(t){for(var n=t.L,i=t.L=t.I,r=ui.k,e=0,u=t.P,o=0;o<u;o++){var s=i[o];0===s.B&&(s.B=1,e!==o&&(i[e]=s),e++),s.R>r&&(r=s.R)}for(i.length=e,t.I=null,u=n.length;u--;){var f=n[u];0===f.B&&lt(f,t),f.B=0}for(;e--;){var c=i[e];1===c.B&&(c.B=0,vt(c,t))}r!==ui.k&&(t.R=r,t.q())}(t),at(r),e}function ut(t){var n=t.L;t.L=[];for(var i=n.length;i--;)lt(n[i],t);t.R=ui.T}function ot(t){var n=st();try{return t()}finally{ft(n)}}function st(){var t=yi.trackingDerivation;return yi.trackingDerivation=null,t}function ft(t){yi.trackingDerivation=t}function ct(t){var n=yi.allowStateReads;return yi.allowStateReads=t,n}function at(t){yi.allowStateReads=t}function ht(t){if(t.R!==ui.k){t.R=ui.k;for(var n=t.L,i=n.length;i--;)n[i].G=ui.k}}function vt(t,n){t.X.add(n),t.G>n.R&&(t.G=n.R)}function lt(t,n){t.X.delete(n),0===t.X.size&&dt(t)}function dt(t){!1===t.W&&(t.W=!0,yi.pendingUnobservations.push(t))}function bt(){yi.inBatch++}function yt(){if(0==--yi.inBatch){wt();for(var t=yi.pendingUnobservations,n=0;n<t.length;n++){var i=t[n];i.W=!1,0===i.X.size&&(i.H&&(i.H=!1,i.onBUO()),i instanceof ci&&i.U())}yi.pendingUnobservations=[]}}function pt(t){var n=yi.trackingDerivation;return null!==n?(n.D!==t.F&&(t.F=n.D,n.I[n.P++]=t,!t.H&&yi.trackingContext&&(t.H=!0,t.onBO())),!0):(0===t.X.size&&yi.inBatch>0&&dt(t),!1)}function mt(t){t.G!==ui.C&&(t.G=ui.C,t.X.forEach((function(t){t.R===ui.k&&t.q(),t.R=ui.C})))}function wt(){yi.inBatch>0||yi.isRunningReactions||mi(jt)}function jt(){yi.isRunningReactions=!0;for(var t=yi.pendingReactions,n=0;t.length>0;){100==++n&&(console.error("[mobx] cycle in reaction: "+t[0]),t.splice(0));for(var i=t.splice(0),r=0,e=i.length;r<e;r++)i[r].$()}yi.isRunningReactions=!1}function Ot(){return console.warn("[mobx.spy] Is a no-op in production builds"),function(){}}function At(t){return function(n,i){return u(n)?$(n.name||"<unnamed action>",n,t):u(i)?$(n,i,t):o(i)?S(n,i,t?Ai:ji):o(n)?_(N(t?"autoAction":"action",{name:n,autoAction:t})):void 0}}function gt(t){return J(0,!1,t,this,void 0)}function _t(t){return u(t)&&!0===t.isMobxAction}function St(t,n){function i(){t(u)}var r,e;void 0===n&&(n=Vn);var u,o=null!=(r=null==(e=n)?void 0:e.name)?r:"Autorun";if(n.scheduler||n.delay){var s=xt(n),f=!1;u=new pi(o,(function(){f||(f=!0,s((function(){f=!1,u.J||u.track(i)})))}),n.onError,n.requiresObservable)}else u=new pi(o,(function(){this.track(i)}),n.onError,n.requiresObservable);return u.Y(),u.Z()}function xt(t){return t.scheduler?t.scheduler:t.delay?function(n){return setTimeout(n,t.delay)}:xi}function Mt(t,n,i){return Nt("onBO",t,n,i)}function Vt(t,n,i){return Nt("onBUO",t,n,i)}function Nt(t,n,i,r){var e="function"==typeof r?bn(n,i):bn(n),o=u(r)?r:i,s=t+"L";return e[s]?e[s].add(o):e[s]=new Set([o]),function(){var t=e[s];t&&(t.delete(o),0===t.size&&delete e[s])}}function Rt(t,n,i,r){var e=Cn(n),u=fn(t,r)[Ln];bt();try{Tn(e).forEach((function(t){u.o(t,e[t],!i||!(t in i)||i[t])}))}finally{yt()}return t}function kt(t){var n,i={name:t.tt};return t.L&&t.L.length>0&&(i.dependencies=(n=t.L,Array.from(new Set(n))).map(kt)),i}function Et(t){var n={name:t.tt};return function(t){return t.X&&t.X.size>0}(t)&&(n.observers=Array.from(function(t){return t.X}(t)).map(Et)),n}function Tt(){this.message="FLOW_CANCELLED"}function Ct(t){u(t.cancel)&&t.cancel()}function Kt(t){return!0===(null==t?void 0:t.isMobXFlow)}function Lt(t,n){if(void 0===n)return ai(t);if(!1===an(t))return!1;if(!t[Ln].nt.has(n))return!1;var i=bn(t,n);return ai(i)}function It(t,n){return!!t&&(void 0!==n?!!an(t)&&t[Ln].nt.has(n):an(t)||!!t[Ln]||Pn(t)||wi(t)||ai(t))}function Pt(t){return It(t)}function Dt(t){return an(t)?t[Ln].it():Xi(t)||Ui(t)?Array.from(t.keys()):sn(t)?t.map((function(t,n){return n})):void n(5)}function Bt(t,i){return an(t)?t[Ln].rt(i):Xi(t)||Ui(t)?t.has(i):sn(t)?i>=0&&i<t.length:void n(10)}function qt(t){if(an(t))return t[Ln].et();n(38)}function Gt(t,n,i){return t.set(n,i),i}function Xt(){n("trace() is not available in production builds");for(var t=!1,i=arguments.length,r=new Array(i),e=0;e<i;e++)r[e]=arguments[e];"boolean"==typeof r[r.length-1]&&(t=r.pop());var u=Wt(r);if(!u)return n("'trace(break?)' can only be used inside a tracked computed value or a Reaction. Consider passing in the computed value or reaction explicitly");u.ut===oi.NONE&&console.log("[mobx.trace] '"+u.tt+"' tracing enabled"),u.ut=t?oi.BREAK:oi.LOG}function Wt(t){switch(t.length){case 0:return yi.trackingDerivation;case 1:return bn(t[0]);case 2:return bn(t[0],t[1])}}function Ht(t,n){void 0===n&&(n=void 0),bt();try{return t.apply(n)}finally{yt()}}function Ut(t,n,i){var r;if("number"==typeof i.timeout){var e=new Error("WHEN_TIMEOUT");r=setTimeout((function(){if(!o[Ln].J){if(o(),!i.onError)throw e;i.onError(e)}}),i.timeout)}i.name="When";var u=$("When-effect",n),o=St((function(n){Z(!1,t)&&(n.dispose(),r&&clearTimeout(r),u())}),i);return o}function Ft(t,n){var i,r=new Promise((function(r,e){var u=Ut(t,r,w({},n,{onError:e}));i=function(){u(),e(new Error("WHEN_CANCELLED"))}}));return r.cancel=i,r}function zt(t){return t[Ln]}function $t(t){return void 0!==t.ot&&t.ot.length>0}function Jt(t,n){var i=t.ot||(t.ot=[]);return i.push(n),e((function(){var t=i.indexOf(n);-1!==t&&i.splice(t,1)}))}function Yt(t,i){var r=st();try{for(var e=[].concat(t.ot||[]),u=0,o=e.length;u<o&&((i=e[u](i))&&!i.type&&n(14),i);u++);return i}finally{ft(r)}}function Qt(t){return void 0!==t.st&&t.st.length>0}function Zt(t,n){var i=t.st||(t.st=[]);return i.push(n),e((function(){var t=i.indexOf(n);-1!==t&&i.splice(t,1)}))}function tn(t,n){var i=st(),r=t.st;if(r){for(var e=0,u=(r=r.slice()).length;e<u;e++)r[e](n);ft(i)}}function nn(t,n,i,e){void 0===i&&(i="ObservableArray"),void 0===e&&(e=!1),r();var u=new Ki(i,n,e,!1);h(u.nt,Ln,u);var o=new Proxy(u.nt,Ci);if(u.v=o,t&&t.length){var s=tt(!0);u.ft(0,0,t),nt(s)}return o}function rn(t,n){"function"==typeof Array.prototype[t]&&(Li[t]=n(t))}function en(t){return function(){var n=this[Ln];n.ct.reportObserved();var i=n.at(n.nt);return i[t].apply(i,arguments)}}function un(t){return function(n,i){var r=this,e=this[Ln];return e.ct.reportObserved(),e.at(e.nt)[t]((function(t,e){return n.call(i,t,e,r)}))}}function on(t){return function(){var n=this,i=this[Ln];i.ct.reportObserved();var r=i.at(i.nt),e=arguments[0];return arguments[0]=function(t,i,r){return e(t,i,r,n)},r[t].apply(r,arguments)}}function sn(t){return s(t)&&Di(t[Ln])}function fn(t,n){var i;if(y(t,Ln))return t;var r=null!=(i=null==n?void 0:n.name)?i:"ObservableObject",e=new zi(t,new Map,String(r),function(t){var n;return t?null!=(n=t.defaultDecorator)?n:X(t):void 0}(n));return a(t,Ln,e),t}function cn(t){return Fi[t]||(Fi[t]={get:function(){return this[Ln].ht(t)},set:function(n){return this[Ln].vt(t,n)}})}function an(t){return!!s(t)&&$i(t[Ln])}function hn(t,n,i){var r;null==(r=t.s[Kn])||delete r[i]}function vn(t){Sn(Qi.prototype,""+t,function(t){return{enumerable:!1,configurable:!0,get:function(){return this[Ln].lt(t)},set:function(n){this[Ln].dt(t,n)}}}(t))}function ln(t){if(t>Ji){for(var n=Ji;n<t+100;n++)vn(n);Ji=t}}function dn(t,n,i){return new Qi(t,n,i)}function bn(t,i){if("object"==typeof t&&null!==t){if(sn(t))return void 0!==i&&n(23),t[Ln].ct;if(Ui(t))return t[Ln];if(Xi(t)){if(void 0===i)return t.bt;var r=t.yt.get(i)||t.pt.get(i);return r||n(25,i,pn(t)),r}if(an(t)){if(!i)return n(26);var e=t[Ln].nt.get(i);return e||n(27,i,pn(t)),e}if(Pn(t)||ai(t)||wi(t))return t}else if(u(t)&&wi(t[Ln]))return t[Ln];n(28)}function yn(t,i){return t||n(29),void 0!==i?yn(bn(t,i)):Pn(t)||ai(t)||wi(t)||Xi(t)||Ui(t)?t:t[Ln]?t[Ln]:void n(24,t)}function pn(t,n){var i;if(void 0!==n)i=bn(t,n);else{if(_t(t))return t.name;i=an(t)||Xi(t)||Ui(t)?yn(t):bn(t)}return i.tt}function mn(t,n,i){return void 0===i&&(i=-1),function t(n,i,r,e,o){if(n===i)return 0!==n||1/n==1/i;if(null==n||null==i)return!1;if(n!=n)return i!=i;var s=typeof n;if("function"!==s&&"object"!==s&&"object"!=typeof i)return!1;var f=Zi.call(n);if(f!==Zi.call(i))return!1;switch(f){case"[object RegExp]":case"[object String]":return""+n==""+i;case"[object Number]":return+n!=+n?+i!=+i:0==+n?1/+n==1/i:+n==+i;case"[object Date]":case"[object Boolean]":return+n==+i;case"[object Symbol]":return"undefined"!=typeof Symbol&&Symbol.valueOf.call(n)===Symbol.valueOf.call(i);case"[object Map]":case"[object Set]":r>=0&&r++}n=wn(n),i=wn(i);var c="[object Array]"===f;if(!c){if("object"!=typeof n||"object"!=typeof i)return!1;var a=n.constructor,h=i.constructor;if(a!==h&&!(u(a)&&a instanceof a&&u(h)&&h instanceof h)&&"constructor"in n&&"constructor"in i)return!1}if(0===r)return!1;r<0&&(r=-1),o=o||[];for(var v=(e=e||[]).length;v--;)if(e[v]===n)return o[v]===i;if(e.push(n),o.push(i),c){if((v=n.length)!==i.length)return!1;for(;v--;)if(!t(n[v],i[v],r-1,e,o))return!1}else{var l,d=Object.keys(n);if(v=d.length,Object.keys(i).length!==v)return!1;for(;v--;)if(!y(i,l=d[v])||!t(n[l],i[l],r-1,e,o))return!1}return e.pop(),o.pop(),!0}(t,n,i)}function wn(t){return sn(t)?t.slice():l(t)||Xi(t)||d(t)||Ui(t)?Array.from(t.entries()):t}function jn(t){return t[Symbol.iterator]=On,t}function On(){return this}var An={},gn=Object.assign,_n=Object.getOwnPropertyDescriptor,Sn=Object.defineProperty,xn=Object.prototype,Mn=[];Object.freeze(Mn);var Vn={};Object.freeze(Vn);var Nn="undefined"!=typeof Proxy,Rn=Object.toString(),kn=function(){},En=void 0!==Object.getOwnPropertySymbols,Tn="undefined"!=typeof Reflect&&Reflect.ownKeys?Reflect.ownKeys:En?function(t){return Object.getOwnPropertyNames(t).concat(Object.getOwnPropertySymbols(t))}:Object.getOwnPropertyNames,Cn=Object.getOwnPropertyDescriptors||function(t){var n={};return Tn(t).forEach((function(i){n[i]=_n(t,i)})),n},Kn=Symbol("mobx-stored-annotations"),Ln=Symbol("mobx administration"),In=function(){function t(t){void 0===t&&(t="Atom"),this.tt=void 0,this.W=!1,this.H=!1,this.X=new Set,this.B=0,this.F=0,this.G=ui.T,this.onBOL=void 0,this.onBUOL=void 0,this.tt=t}var n=t.prototype;return n.onBO=function(){this.onBOL&&this.onBOL.forEach((function(t){return t()}))},n.onBUO=function(){this.onBUOL&&this.onBUOL.forEach((function(t){return t()}))},n.reportObserved=function(){return pt(this)},n.reportChanged=function(){bt(),mt(this),yt()},n.toString=function(){return this.tt},t}(),Pn=v("Atom",In),Dn={identity:function(t,n){return t===n},structural:function(t,n){return mn(t,n)},default:function(t,n){return Object.is?Object.is(t,n):t===n?0!==t||1/t==1/n:t!=t&&n!=n},shallow:function(t,n){return mn(t,n,1)}},Bn=_({t:"override",u:function(){return 0},o:function(){n("'"+this.t+"' can only be used with 'makeObservable'")}}),qn=X(),Gn={deep:!0,name:void 0,defaultDecorator:void 0,proxy:!0};Object.freeze(Gn);var Xn=B("observable"),Wn=B("observable.ref",{enhancer:V}),Hn=B("observable.shallow",{enhancer:function(t,n,i){return null==t||an(t)||sn(t)||Xi(t)||Ui(t)?t:Array.isArray(t)?Jn.array(t,{name:i,deep:!1}):f(t)?Jn.object(t,void 0,{name:i,deep:!1}):l(t)?Jn.map(t,{name:i,deep:!1}):d(t)?Jn.set(t,{name:i,deep:!1}):void 0}}),Un=B("observable.struct",{enhancer:function(t,n){return mn(t,n)?n:t}}),Fn=_(Xn);Object.assign(z,Fn);var zn,$n,Jn=gn(z,{box:function(t,n){var i=U(n);return new si(t,F(i),i.name,!0,i.equals)},array:function(t,n){var i=U(n);return(!1===yi.useProxies||!1===i.proxy?dn:nn)(t,F(i),i.name)},map:function(t,n){var i=U(n);return new Gi(t,F(i),i.name)},set:function(t,n){var i=U(n);return new Hi(t,F(i),i.name)},object:function(t,n,i){return Rt(!1===yi.useProxies||!1===(null==i?void 0:i.proxy)?fn({},i):function(t,n){var i,e;return r(),null!=(e=(i=(t=fn(t,n))[Ln]).v)?e:i.v=new Proxy(t,ki)}({},i),t,n)},ref:_(Wn),shallow:_(Hn),deep:Fn,struct:_(Un)}),Yn=I("computed"),Qn=I("computed.struct",{equals:Dn.structural}),Zn=function(t,n){if(o(n))return S(t,n,Yn);if(f(t))return _(I("computed",t));var i=f(n)?n:{};return i.get=t,i.name||(i.name=t.name||""),new ci(i)};Object.assign(Zn,Yn),Zn.struct=_(Qn);var ti,ni=0,ii=1,ri=null!=(zn=null==($n=_n((function(){}),"name"))?void 0:$n.configurable)&&zn,ei={value:"action",configurable:!0,writable:!1,enumerable:!1};ti=Symbol.toPrimitive;var ui,oi,si=function(t,n){function i(n,i,r,e,u){var o;return void 0===r&&(r="ObservableValue"),void 0===u&&(u=Dn.default),(o=t.call(this,r)||this).enhancer=void 0,o.tt=void 0,o.equals=void 0,o.wt=!1,o.ot=void 0,o.st=void 0,o.jt=void 0,o.dehancer=void 0,o.enhancer=i,o.tt=r,o.equals=u,o.jt=i(n,void 0,r),o}j(i,t);var r=i.prototype;return r.dehanceValue=function(t){return void 0!==this.dehancer?this.dehancer(t):t},r.set=function(t){(t=this.Ot(t))!==yi.UNCHANGED&&this.At(t)},r.Ot=function(t){if($t(this)){var n=Yt(this,{object:this,type:Ti,newValue:t});if(!n)return yi.UNCHANGED;t=n.newValue}return t=this.enhancer(t,this.jt,this.tt),this.equals(this.jt,t)?yi.UNCHANGED:t},r.At=function(t){var n=this.jt;this.jt=t,this.reportChanged(),Qt(this)&&tn(this,{type:Ti,object:this,newValue:t,oldValue:n})},r.get=function(){return this.reportObserved(),this.dehanceValue(this.jt)},r.gt=function(t){return Jt(this,t)},r._t=function(t,n){return n&&t({observableKind:"value",debugObjectName:this.tt,object:this,type:Ti,newValue:this.jt,oldValue:void 0}),Zt(this,t)},r.raw=function(){return this.jt},r.toJSON=function(){return this.get()},r.toString=function(){return this.tt+"["+this.jt+"]"},r.valueOf=function(){return b(this.get())},r[n]=function(){return this.valueOf()},i}(In,ti),fi=v("ObservableValue",si),ci=function(t){function i(t){this.R=ui.T,this.L=[],this.I=null,this.H=!1,this.W=!1,this.X=new Set,this.B=0,this.D=0,this.F=0,this.G=ui.k,this.P=0,this.jt=new hi(null),this.tt=void 0,this.St=void 0,this.xt=!1,this.Mt=!1,this.derivation=void 0,this.Vt=void 0,this.ut=oi.NONE,this.Nt=void 0,this.Rt=void 0,this.kt=void 0,this.Et=void 0,this.onBOL=void 0,this.onBUOL=void 0,t.get||n(31),this.derivation=t.get,this.tt=t.name||"ComputedValue",t.set&&(this.Vt=$("ComputedValue-setter",t.set)),this.Rt=t.equals||(t.compareStructural||t.struct?Dn.structural:Dn.default),this.Nt=t.context,this.kt=t.requiresReaction,this.Et=!!t.keepAlive}var r=i.prototype;return r.q=function(){!function(t){t.G===ui.k&&(t.G=ui.K,t.X.forEach((function(t){t.R===ui.k&&(t.R=ui.K,t.q())})))}(this)},r.onBO=function(){this.onBOL&&this.onBOL.forEach((function(t){return t()}))},r.onBUO=function(){this.onBUOL&&this.onBUOL.forEach((function(t){return t()}))},r.get=function(){if(this.xt&&n(32,this.tt,this.derivation),0!==yi.inBatch||0!==this.X.size||this.Et){if(pt(this),rt(this)){var t=yi.trackingContext;this.Et&&!t&&(yi.trackingContext=this),this.trackAndCompute()&&function(t){t.G!==ui.C&&(t.G=ui.C,t.X.forEach((function(n){n.R===ui.K?n.R=ui.C:n.R===ui.k&&(t.G=ui.k)})))}(this),yi.trackingContext=t}}else rt(this)&&(this.Tt(),bt(),this.jt=this.Ct(!1),yt());var i=this.jt;if(it(i))throw i.cause;return i},r.set=function(t){if(this.Vt){this.Mt&&n(33,this.tt),this.Mt=!0;try{this.Vt.call(this.Nt,t)}finally{this.Mt=!1}}else n(34,this.tt)},r.trackAndCompute=function(){var t=this.jt,n=this.R===ui.T,i=this.Ct(!0),r=n||it(t)||it(i)||!this.Rt(t,i);return r&&(this.jt=i),r},r.Ct=function(t){this.xt=!0;var n,i=tt(!1);if(t)n=et(this,this.derivation,this.Nt);else if(!0===yi.disableErrorBoundaries)n=this.derivation.call(this.Nt);else try{n=this.derivation.call(this.Nt)}catch(t){n=new hi(t)}return nt(i),this.xt=!1,n},r.U=function(){this.Et||(ut(this),this.jt=void 0)},r._t=function(t,n){var i=this,r=!0,e=void 0;return St((function(){var u=i.get();if(!r||n){var o=st();t({observableKind:"computed",debugObjectName:i.tt,type:Ti,object:i,newValue:u,oldValue:e}),ft(o)}r=!1,e=u}))},r.Tt=function(){},r.toString=function(){return this.tt+"["+this.derivation.toString()+"]"},r.valueOf=function(){return b(this.get())},r[t]=function(){return this.valueOf()},i}(Symbol.toPrimitive),ai=v("ComputedValue",ci);!function(t){t[t.T=-1]="NOT_TRACKING_",t[t.k=0]="UP_TO_DATE_",t[t.K=1]="POSSIBLY_STALE_",t[t.C=2]="STALE_"}(ui||(ui={})),function(t){t[t.NONE=0]="NONE",t[t.LOG=1]="LOG",t[t.BREAK=2]="BREAK"}(oi||(oi={}));var hi=function(t){this.cause=void 0,this.cause=t},vi=["mobxGuid","spyListeners","enforceActions","computedRequiresReaction","reactionRequiresObservable","observableRequiresReaction","allowStateReads","disableErrorBoundaries","runId","UNCHANGED","useProxies"],li=function(){this.version=6,this.UNCHANGED={},this.trackingDerivation=null,this.trackingContext=null,this.runId=0,this.mobxGuid=0,this.inBatch=0,this.pendingUnobservations=[],this.pendingReactions=[],this.isRunningReactions=!1,this.allowStateChanges=!1,this.allowStateReads=!0,this.enforceActions=!0,this.spyListeners=[],this.globalReactionErrorHandlers=[],this.computedRequiresReaction=!1,this.reactionRequiresObservable=!1,this.observableRequiresReaction=!1,this.disableErrorBoundaries=!1,this.suppressReactionErrors=!1,this.useProxies=!0,this.verifyProxies=!1,this.safeDescriptors=!0},di=!0,bi=!1,yi=function(){var t=i();return t.__mobxInstanceCount>0&&!t.__mobxGlobals&&(di=!1),t.__mobxGlobals&&t.__mobxGlobals.version!==(new li).version&&(di=!1),di?t.__mobxGlobals?(t.__mobxInstanceCount+=1,t.__mobxGlobals.UNCHANGED||(t.__mobxGlobals.UNCHANGED={}),t.__mobxGlobals):(t.__mobxInstanceCount=1,t.__mobxGlobals=new li):(setTimeout((function(){bi||n(35)}),1),new li)}(),pi=function(){function t(t,n,i,r){void 0===t&&(t="Reaction"),void 0===r&&(r=!1),this.tt=void 0,this.Kt=void 0,this.Lt=void 0,this.It=void 0,this.L=[],this.I=[],this.R=ui.T,this.B=0,this.D=0,this.P=0,this.J=!1,this.Pt=!1,this.Dt=!1,this.Bt=!1,this.ut=oi.NONE,this.tt=t,this.Kt=n,this.Lt=i,this.It=r}var n=t.prototype;return n.q=function(){this.Y()},n.Y=function(){this.Pt||(this.Pt=!0,yi.pendingReactions.push(this),wt())},n.isScheduled=function(){return this.Pt},n.$=function(){if(!this.J){bt(),this.Pt=!1;var t=yi.trackingContext;if(yi.trackingContext=this,rt(this)){this.Dt=!0;try{this.Kt()}catch(t){this.qt(t)}}yi.trackingContext=t,yt()}},n.track=function(t){if(!this.J){bt(),this.Bt=!0;var n=yi.trackingContext;yi.trackingContext=this;var i=et(this,t,void 0);yi.trackingContext=n,this.Bt=!1,this.Dt=!1,this.J&&ut(this),it(i)&&this.qt(i.cause),yt()}},n.qt=function(t){var n=this;if(this.Lt)this.Lt(t,this);else{if(yi.disableErrorBoundaries)throw t;yi.suppressReactionErrors||console.error("[mobx] uncaught error in '"+this+"'",t),yi.globalReactionErrorHandlers.forEach((function(i){return i(t,n)}))}},n.dispose=function(){this.J||(this.J=!0,this.Bt||(bt(),ut(this),yt()))},n.Z=function(){var t=this.dispose.bind(this);return t[Ln]=this,t},n.toString=function(){return"Reaction["+this.tt+"]"},n.trace=function(t){void 0===t&&(t=!1),Xt(this,t)},t}(),mi=function(t){return t()},wi=v("Reaction",pi),ji=N("action"),Oi=N("action.bound",{bound:!0}),Ai=N("autoAction",{autoAction:!0}),gi=N("autoAction.bound",{autoAction:!0,bound:!0}),_i=At(!1);Object.assign(_i,ji);var Si=At(!0);Object.assign(Si,Ai),_i.bound=_(Oi),Si.bound=_(gi);var xi=function(t){return t()},Mi=0;Tt.prototype=Object.create(Error.prototype);var Vi=T("flow"),Ni=T("flow.bound",{bound:!0}),Ri=Object.assign((function(t,n){if(o(n))return S(t,n,Vi);var i=t,r=i.name||"<unnamed flow>",e=function(){var t,n=this,e=arguments,o=++Mi,s=_i(r+" - runid: "+o+" - init",i).apply(n,e),f=void 0,c=new Promise((function(n,i){function e(t){var n;f=void 0;try{n=_i(r+" - runid: "+o+" - yield "+h++,s.next).call(s,t)}catch(t){return i(t)}a(n)}function c(t){var n;f=void 0;try{n=_i(r+" - runid: "+o+" - yield "+h++,s.throw).call(s,t)}catch(t){return i(t)}a(n)}function a(t){if(!u(null==t?void 0:t.then))return t.done?n(t.value):(f=Promise.resolve(t.value)).then(e,c);t.then(a,i)}var h=0;t=i,e(void 0)}));return c.cancel=_i(r+" - runid: "+o+" - cancel",(function(){try{f&&Ct(f);var n=s.return(void 0),i=Promise.resolve(n.value);i.then(kn,kn),Ct(i),t(new Tt)}catch(n){t(n)}})),c};return e.isMobXFlow=!0,e}),Vi);Ri.bound=_(Ni);var ki={has:function(t,n){return zt(t).rt(n)},get:function(t,n){return zt(t).lt(n)},set:function(t,n,i){var r;return!!o(n)&&(null==(r=zt(t).dt(n,i,!0))||r)},deleteProperty:function(t,n){var i;return!!o(n)&&(null==(i=zt(t).Gt(n,!0))||i)},defineProperty:function(t,n,i){var r;return null==(r=zt(t).h(n,i))||r},ownKeys:function(t){return zt(t).et()},preventExtensions:function(){n(13)}},Ei=Symbol("mobx-keys"),Ti="update",Ci={get:function(t,n){var i=t[Ln];return n===Ln?i:"length"===n?i.Xt():"string"!=typeof n||isNaN(n)?y(Li,n)?Li[n]:t[n]:i.lt(parseInt(n))},set:function(t,n,i){var r=t[Ln];return"length"===n&&r.Wt(i),"symbol"==typeof n||isNaN(n)?t[n]=i:r.dt(parseInt(n),i),!0},preventExtensions:function(){n(15)}},Ki=function(){function t(t,n,i,r){void 0===t&&(t="ObservableArray"),this.Ht=void 0,this.Ut=void 0,this.ct=void 0,this.nt=[],this.ot=void 0,this.st=void 0,this.Ft=void 0,this.dehancer=void 0,this.v=void 0,this.zt=0,this.Ht=i,this.Ut=r,this.ct=new In(t),this.Ft=function(t,i){return n(t,i,"ObservableArray[..]")}}var i=t.prototype;return i.$t=function(t){return void 0!==this.dehancer?this.dehancer(t):t},i.at=function(t){return void 0!==this.dehancer&&t.length>0?t.map(this.dehancer):t},i.gt=function(t){return Jt(this,t)},i._t=function(t,n){return void 0===n&&(n=!1),n&&t({observableKind:"array",object:this.v,debugObjectName:this.ct.tt,type:"splice",index:0,added:this.nt.slice(),addedCount:this.nt.length,removed:[],removedCount:0}),Zt(this,t)},i.Xt=function(){return this.ct.reportObserved(),this.nt.length},i.Wt=function(t){("number"!=typeof t||isNaN(t)||t<0)&&n("Out of range: "+t);var i=this.nt.length;if(t!==i)if(t>i){for(var r=new Array(t-i),e=0;e<t-i;e++)r[e]=void 0;this.ft(i,0,r)}else this.ft(t,i-t)},i.Jt=function(t,i){t!==this.zt&&n(16),this.zt+=i,this.Ut&&i>0&&ln(t+i+1)},i.ft=function(t,n,i){var r=this,e=this.nt.length;if(void 0===t?t=0:t>e?t=e:t<0&&(t=Math.max(0,e+t)),n=1===arguments.length?e-t:null==n?0:Math.max(0,Math.min(n,e-t)),void 0===i&&(i=Mn),$t(this)){var u=Yt(this,{object:this.v,type:"splice",index:t,removedCount:n,added:i});if(!u)return Mn;n=u.removedCount,i=u.added}if(i=0===i.length?i:i.map((function(t){return r.Ft(t,void 0)})),this.Ut){var o=i.length-n;this.Jt(e,o)}var s=this.Yt(t,n,i);return 0===n&&0===i.length||this.Qt(t,i,s),this.at(s)},i.Yt=function(t,n,i){var r;if(i.length<1e4)return(r=this.nt).splice.apply(r,[t,n].concat(i));var e=this.nt.slice(t,t+n),u=this.nt.slice(t+n);this.nt.length+=i.length-n;for(var o=0;o<i.length;o++)this.nt[t+o]=i[o];for(var s=0;s<u.length;s++)this.nt[t+i.length+s]=u[s];return e},i.Zt=function(t,n,i){var r=!this.Ht&&!1,e=Qt(this),u=e||r?{observableKind:"array",object:this.v,type:Ti,debugObjectName:this.ct.tt,index:t,newValue:n,oldValue:i}:null;this.ct.reportChanged(),e&&tn(this,u)},i.Qt=function(t,n,i){var r=!this.Ht&&!1,e=Qt(this),u=e||r?{observableKind:"array",object:this.v,debugObjectName:this.ct.tt,type:"splice",index:t,removed:i,added:n,removedCount:i.length,addedCount:n.length}:null;this.ct.reportChanged(),e&&tn(this,u)},i.lt=function(t){if(t<this.nt.length)return this.ct.reportObserved(),this.$t(this.nt[t]);console.warn("[mobx.array] Attempt to read an array index ("+t+") that is out of bounds ("+this.nt.length+"). Please check length first. Out of bound indices will not be tracked by MobX")},i.dt=function(t,i){var r=this.nt;if(t<r.length){var e=r[t];if($t(this)){var u=Yt(this,{type:Ti,object:this.v,index:t,newValue:i});if(!u)return;i=u.newValue}(i=this.Ft(i,e))!==e&&(r[t]=i,this.Zt(t,i,e))}else t===r.length?this.ft(t,0,[i]):n(17,t,r.length)},t}(),Li={clear:function(){return this.splice(0)},replace:function(t){var n=this[Ln];return n.ft(0,n.nt.length,t)},toJSON:function(){return this.slice()},splice:function(t,n){for(var i=arguments.length,r=new Array(i>2?i-2:0),e=2;e<i;e++)r[e-2]=arguments[e];var u=this[Ln];switch(arguments.length){case 0:return[];case 1:return u.ft(t);case 2:return u.ft(t,n)}return u.ft(t,n,r)},spliceWithArray:function(t,n,i){return this[Ln].ft(t,n,i)},push:function(){for(var t=this[Ln],n=arguments.length,i=new Array(n),r=0;r<n;r++)i[r]=arguments[r];return t.ft(t.nt.length,0,i),t.nt.length},pop:function(){return this.splice(Math.max(this[Ln].nt.length-1,0),1)[0]},shift:function(){return this.splice(0,1)[0]},unshift:function(){for(var t=this[Ln],n=arguments.length,i=new Array(n),r=0;r<n;r++)i[r]=arguments[r];return t.ft(0,0,i),t.nt.length},reverse:function(){return yi.trackingDerivation&&n(37,"reverse"),this.replace(this.slice().reverse()),this},sort:function(){yi.trackingDerivation&&n(37,"sort");var t=this.slice();return t.sort.apply(t,arguments),this.replace(t),this},remove:function(t){var n=this[Ln],i=n.at(n.nt).indexOf(t);return i>-1&&(this.splice(i,1),!0)}};rn("concat",en),rn("flat",en),rn("includes",en),rn("indexOf",en),rn("join",en),rn("lastIndexOf",en),rn("slice",en),rn("toString",en),rn("toLocaleString",en),rn("every",un),rn("filter",un),rn("find",un),rn("findIndex",un),rn("flatMap",un),rn("forEach",un),rn("map",un),rn("some",un),rn("reduce",on),rn("reduceRight",on);var Ii,Pi,Di=v("ObservableArrayAdministration",Ki),Bi={},qi="add",Gi=function(t,i){function r(t,i,r){var e=this;void 0===i&&(i=M),void 0===r&&(r="ObservableMap"),this.Ft=void 0,this.tt=void 0,this[Ln]=Bi,this.yt=void 0,this.pt=void 0,this.bt=void 0,this.ot=void 0,this.st=void 0,this.dehancer=void 0,this.Ft=i,this.tt=r,u(Map)||n(18),this.bt=x("ObservableMap.keys()"),this.yt=new Map,this.pt=new Map,Z(!0,(function(){e.merge(t)}))}var e=r.prototype;return e.rt=function(t){return this.yt.has(t)},e.has=function(t){var n=this;if(!yi.trackingDerivation)return this.rt(t);var i=this.pt.get(t);if(!i){var r=i=new si(this.rt(t),V,"ObservableMap.key?",!1);this.pt.set(t,r),Vt(r,(function(){return n.pt.delete(t)}))}return i.get()},e.set=function(t,n){var i=this.rt(t);if($t(this)){var r=Yt(this,{type:i?Ti:qi,object:this,newValue:n,name:t});if(!r)return this;n=r.newValue}return i?this.tn(t,n):this.nn(t,n),this},e.delete=function(t){var n=this;if($t(this)&&!Yt(this,{type:"delete",object:this,name:t}))return!1;if(this.rt(t)){var i=Qt(this),r=i?{observableKind:"map",debugObjectName:this.tt,type:"delete",object:this,oldValue:this.yt.get(t).jt,name:t}:null;return Ht((function(){var i;n.bt.reportChanged(),null==(i=n.pt.get(t))||i.At(!1),n.yt.get(t).At(void 0),n.yt.delete(t)})),i&&tn(this,r),!0}return!1},e.tn=function(t,n){var i=this.yt.get(t);if((n=i.Ot(n))!==yi.UNCHANGED){var r=Qt(this),e=r?{observableKind:"map",debugObjectName:this.tt,type:Ti,object:this,oldValue:i.jt,name:t,newValue:n}:null;i.At(n),r&&tn(this,e)}},e.nn=function(t,n){var i=this;Ht((function(){var r,e=new si(n,i.Ft,"ObservableMap.key",!1);i.yt.set(t,e),n=e.jt,null==(r=i.pt.get(t))||r.At(!0),i.bt.reportChanged()}));var r=Qt(this);r&&tn(this,r?{observableKind:"map",debugObjectName:this.tt,type:qi,object:this,name:t,newValue:n}:null)},e.get=function(t){return this.has(t)?this.$t(this.yt.get(t).get()):this.$t(void 0)},e.$t=function(t){return void 0!==this.dehancer?this.dehancer(t):t},e.keys=function(){return this.bt.reportObserved(),this.yt.keys()},e.values=function(){var t=this,n=this.keys();return jn({next:function(){var i=n.next(),r=i.done;return{done:r,value:r?void 0:t.get(i.value)}}})},e.entries=function(){var t=this,n=this.keys();return jn({next:function(){var i=n.next(),r=i.done,e=i.value;return{done:r,value:r?void 0:[e,t.get(e)]}}})},e[t]=function(){return this.entries()},e.forEach=function(t,n){for(var i,r=g(this);!(i=r()).done;){var e=i.value;t.call(n,e[1],e[0],this)}},e.merge=function(t){var i=this;return Xi(t)&&(t=new Map(t)),Ht((function(){f(t)?function(t){var n=Object.keys(t);if(!En)return n;var i=Object.getOwnPropertySymbols(t);return i.length?[].concat(n,i.filter((function(n){return xn.propertyIsEnumerable.call(t,n)}))):n}(t).forEach((function(n){return i.set(n,t[n])})):Array.isArray(t)?t.forEach((function(t){return i.set(t[0],t[1])})):l(t)?(t.constructor!==Map&&n(19,t),t.forEach((function(t,n){return i.set(n,t)}))):null!=t&&n(20,t)})),this},e.clear=function(){var t=this;Ht((function(){ot((function(){for(var n,i=g(t.keys());!(n=i()).done;)t.delete(n.value)}))}))},e.replace=function(t){var i=this;return Ht((function(){for(var r,e=function(t){if(l(t)||Xi(t))return t;if(Array.isArray(t))return new Map(t);if(f(t)){var i=new Map;for(var r in t)i.set(r,t[r]);return i}return n(21,t)}(t),u=new Map,o=!1,s=g(i.yt.keys());!(r=s()).done;){var c=r.value;if(!e.has(c))if(i.delete(c))o=!0;else{var a=i.yt.get(c);u.set(c,a)}}for(var h,v=g(e.entries());!(h=v()).done;){var d=h.value,b=d[0],y=d[1],p=i.yt.has(b);if(i.set(b,y),i.yt.has(b)){var m=i.yt.get(b);u.set(b,m),p||(o=!0)}}if(!o)if(i.yt.size!==u.size)i.bt.reportChanged();else for(var w=i.yt.keys(),j=u.keys(),O=w.next(),A=j.next();!O.done;){if(O.value!==A.value){i.bt.reportChanged();break}O=w.next(),A=j.next()}i.yt=u})),this},e.toString=function(){return"[object ObservableMap]"},e.toJSON=function(){return Array.from(this)},e._t=function(t){return Zt(this,t)},e.gt=function(t){return Jt(this,t)},m(r,[{key:"size",get:function(){return this.bt.reportObserved(),this.yt.size}},{key:i,get:function(){return"Map"}}]),r}(Symbol.iterator,Symbol.toStringTag),Xi=v("ObservableMap",Gi),Wi={},Hi=function(t,i){function r(t,i,r){void 0===i&&(i=M),void 0===r&&(r="ObservableSet"),this.tt=void 0,this[Ln]=Wi,this.yt=new Set,this.ct=void 0,this.st=void 0,this.ot=void 0,this.dehancer=void 0,this.Ft=void 0,this.tt=r,u(Set)||n(22),this.ct=x(this.tt),this.Ft=function(t,n){return i(t,n,r)},t&&this.replace(t)}var e=r.prototype;return e.$t=function(t){return void 0!==this.dehancer?this.dehancer(t):t},e.clear=function(){var t=this;Ht((function(){ot((function(){for(var n,i=g(t.yt.values());!(n=i()).done;)t.delete(n.value)}))}))},e.forEach=function(t,n){for(var i,r=g(this);!(i=r()).done;){var e=i.value;t.call(n,e,e,this)}},e.add=function(t){var n=this;if($t(this)&&!Yt(this,{type:qi,object:this,newValue:t}))return this;if(!this.has(t)){Ht((function(){n.yt.add(n.Ft(t,void 0)),n.ct.reportChanged()}));var i=Qt(this);i&&tn(this,i?{observableKind:"set",debugObjectName:this.tt,type:qi,object:this,newValue:t}:null)}return this},e.delete=function(t){var n=this;if($t(this)&&!Yt(this,{type:"delete",object:this,oldValue:t}))return!1;if(this.has(t)){var i=Qt(this),r=i?{observableKind:"set",debugObjectName:this.tt,type:"delete",object:this,oldValue:t}:null;return Ht((function(){n.ct.reportChanged(),n.yt.delete(t)})),i&&tn(this,r),!0}return!1},e.has=function(t){return this.ct.reportObserved(),this.yt.has(this.$t(t))},e.entries=function(){var t=0,n=Array.from(this.keys()),i=Array.from(this.values());return jn({next:function(){var r=t;return t+=1,r<i.length?{value:[n[r],i[r]],done:!1}:{done:!0}}})},e.keys=function(){return this.values()},e.values=function(){this.ct.reportObserved();var t=this,n=0,i=Array.from(this.yt.values());return jn({next:function(){return n<i.length?{value:t.$t(i[n++]),done:!1}:{done:!0}}})},e.replace=function(t){var i=this;return Ui(t)&&(t=new Set(t)),Ht((function(){Array.isArray(t)||d(t)?(i.clear(),t.forEach((function(t){return i.add(t)}))):null!=t&&n("Cannot initialize set from "+t)})),this},e._t=function(t){return Zt(this,t)},e.gt=function(t){return Jt(this,t)},e.toJSON=function(){return Array.from(this)},e.toString=function(){return"[object ObservableSet]"},e[t]=function(){return this.values()},m(r,[{key:"size",get:function(){return this.ct.reportObserved(),this.yt.size}},{key:i,get:function(){return"Set"}}]),r}(Symbol.iterator,Symbol.toStringTag),Ui=v("ObservableSet",Hi),Fi=Object.create(null),zi=function(){function t(t,n,i,r){void 0===n&&(n=new Map),void 0===r&&(r=qn),this.s=void 0,this.nt=void 0,this.tt=void 0,this.in=void 0,this.bt=void 0,this.st=void 0,this.ot=void 0,this.v=void 0,this.l=void 0,this.rn=void 0,this.en=void 0,this.s=t,this.nt=n,this.tt=i,this.in=r,this.bt=new In("ObservableObject.keys"),this.l=f(this.s)}var i=t.prototype;return i.ht=function(t){return this.nt.get(t).get()},i.vt=function(t,n){var i=this.nt.get(t);if(i instanceof ci)return i.set(n),!0;if($t(this)){var r=Yt(this,{type:Ti,object:this.v||this.s,name:t,newValue:n});if(!r)return null;n=r.newValue}if((n=i.Ot(n))!==yi.UNCHANGED){var e=Qt(this),u=e?{type:Ti,observableKind:"object",debugObjectName:this.tt,object:this.v||this.s,oldValue:i.jt,name:t,newValue:n}:null;i.At(n),e&&tn(this,u)}return!0},i.lt=function(t){return yi.trackingDerivation&&!y(this.s,t)&&this.rt(t),this.s[t]},i.dt=function(t,n,i){return void 0===i&&(i=!1),y(this.s,t)?this.nt.has(t)?this.vt(t,n):i?Reflect.set(this.s,t,n):(this.s[t]=n,!0):this.o(t,{value:n,enumerable:!0,writable:!0,configurable:!0},this.in,i)},i.rt=function(t){if(!yi.trackingDerivation)return t in this.s;this.en||(this.en=new Map);var n=this.en.get(t);return n||(n=new si(t in this.s,V,"ObservableObject.key?",!1),this.en.set(t,n)),n.get()},i.u=function(t,i){if(!0===i&&(i=this.in),!1!==i){if(!(t in this.s)){var r;if(null!=(r=this.s[Kn])&&r[t])return;n(1,i.t,this.tt+"."+t.toString())}for(var e=this.s;e&&e!==xn;){var u=_n(e,t);if(u){var o=i.u(this,t,u,e);if(0===o)return;if(1===o)break}e=Object.getPrototypeOf(e)}hn(this,0,t)}},i.o=function(t,n,i,r){if(void 0===r&&(r=!1),!0===i&&(i=this.in),!1===i)return this.h(t,n,r);var e=i.o(this,t,n,r);return e&&hn(this,0,t),e},i.h=function(t,n,i){void 0===i&&(i=!1);try{bt();var r=this.Gt(t);if(!r)return r;if($t(this)){var e=Yt(this,{object:this.v||this.s,name:t,type:qi,newValue:n.value});if(!e)return null;var u=e.newValue;n.value!==u&&(n=w({},n,{value:u}))}if(i){if(!Reflect.defineProperty(this.s,t,n))return!1}else Sn(this.s,t,n);this.un(t,n.value)}finally{yt()}return!0},i.m=function(t,n,i,r){void 0===r&&(r=!1);try{bt();var e=this.Gt(t);if(!e)return e;if($t(this)){var u=Yt(this,{object:this.v||this.s,name:t,type:qi,newValue:n});if(!u)return null;n=u.newValue}var o=cn(t),s={configurable:!yi.safeDescriptors||this.l,enumerable:!0,get:o.get,set:o.set};if(r){if(!Reflect.defineProperty(this.s,t,s))return!1}else Sn(this.s,t,s);var f=new si(n,i,"ObservableObject.key",!1);this.nt.set(t,f),this.un(t,f.jt)}finally{yt()}return!0},i.p=function(t,n,i){void 0===i&&(i=!1);try{bt();var r=this.Gt(t);if(!r)return r;if($t(this)&&!Yt(this,{object:this.v||this.s,name:t,type:qi,newValue:void 0}))return null;n.name||(n.name="ObservableObject.key"),n.context=this.v||this.s;var e=cn(t),u={configurable:!yi.safeDescriptors||this.l,enumerable:!1,get:e.get,set:e.set};if(i){if(!Reflect.defineProperty(this.s,t,u))return!1}else Sn(this.s,t,u);this.nt.set(t,new ci(n)),this.un(t,void 0)}finally{yt()}return!0},i.Gt=function(t,n){if(void 0===n&&(n=!1),!y(this.s,t))return!0;if($t(this)&&!Yt(this,{object:this.v||this.s,name:t,type:"remove"}))return null;try{var i,r;bt();var e,u=Qt(this),o=this.nt.get(t),s=void 0;if(!o&&u&&(s=null==(e=_n(this.s,t))?void 0:e.value),n){if(!Reflect.deleteProperty(this.s,t))return!1}else delete this.s[t];o&&(this.nt.delete(t),o instanceof si&&(s=o.jt),mt(o)),this.bt.reportChanged(),null==(i=this.en)||null==(r=i.get(t))||r.set(t in this.s),u&&u&&tn(this,{type:"remove",observableKind:"object",object:this.v||this.s,debugObjectName:this.tt,oldValue:s,name:t})}finally{yt()}return!0},i._t=function(t){return Zt(this,t)},i.gt=function(t){return Jt(this,t)},i.un=function(t,n){var i,r,e=Qt(this);e&&e&&tn(this,e?{type:qi,observableKind:"object",debugObjectName:this.tt,object:this.v||this.s,name:t,newValue:n}:null),null==(i=this.en)||null==(r=i.get(t))||r.set(!0),this.bt.reportChanged()},i.et=function(){return this.bt.reportObserved(),Tn(this.s)},i.it=function(){return this.bt.reportObserved(),Object.keys(this.s)},t}(),$i=v("ObservableObjectAdministration",zi),Ji=0,Yi=function(){};Ii=Yi,Pi=Array.prototype,Object.setPrototypeOf?Object.setPrototypeOf(Ii.prototype,Pi):void 0!==Ii.prototype.__proto__?Ii.prototype.__proto__=Pi:Ii.prototype=Pi;var Qi=function(t,n,i){function r(n,i,r,e){var u;void 0===r&&(r="ObservableArray"),void 0===e&&(e=!1),u=t.call(this)||this;var o=new Ki(r,i,e,!0);if(o.v=O(u),h(O(u),Ln,o),n&&n.length){var s=tt(!0);u.spliceWithArray(0,0,n),nt(s)}return u}j(r,t);var e=r.prototype;return e.concat=function(){this[Ln].ct.reportObserved();for(var t=arguments.length,n=new Array(t),i=0;i<t;i++)n[i]=arguments[i];return Array.prototype.concat.apply(this.slice(),n.map((function(t){return sn(t)?t.slice():t})))},e[i]=function(){var t=this,n=0;return jn({next:function(){return n<t.length?{value:t[n++],done:!1}:{done:!0,value:void 0}}})},m(r,[{key:"length",get:function(){return this[Ln].Xt()},set:function(t){this[Ln].Wt(t)}},{key:n,get:function(){return"Array"}}]),r}(Yi,Symbol.toStringTag,Symbol.iterator);Object.entries(Li).forEach((function(t){var n=t[0];"concat"!==n&&a(Qi.prototype,n,t[1])})),ln(1e3);var Zi=xn.toString;["Symbol","Map","Set"].forEach((function(t){void 0===i()[t]&&n("MobX requires global '"+t+"' to be available or polyfilled")})),"object"==typeof __MOBX_DEVTOOLS_GLOBAL_HOOK__&&__MOBX_DEVTOOLS_GLOBAL_HOOK__.injectMobx({spy:Ot,extras:{getDebugName:pn},$mobx:Ln}),t.$mobx=Ln,t.FlowCancellationError=Tt,t.ObservableMap=Gi,t.ObservableSet=Hi,t.Reaction=pi,t._allowStateChanges=Z,t._allowStateChangesInsideComputed=gt,t._allowStateReadsEnd=at,t._allowStateReadsStart=ct,t._autoAction=Si,t._endAction=Q,t._getAdministration=yn,t._getGlobalState=function(){return yi},t._interceptReads=function(t,n,i){var r;return Xi(t)||sn(t)||fi(t)?r=yn(t):an(t)&&(r=yn(t,n)),r.dehancer="function"==typeof n?n:i,function(){r.dehancer=void 0}},t._isComputingDerivation=function(){return null!==yi.trackingDerivation},t._resetGlobalState=function(){var t=new li;for(var n in t)-1===vi.indexOf(n)&&(yi[n]=t[n]);yi.allowStateChanges=!yi.enforceActions},t._startAction=Y,t.action=_i,t.autorun=St,t.comparer=Dn,t.computed=Zn,t.configure=function(t){!0===t.isolateGlobalState&&function(){if((yi.pendingReactions.length||yi.inBatch||yi.isRunningReactions)&&n(36),bi=!0,di){var t=i();0==--t.__mobxInstanceCount&&(t.__mobxGlobals=void 0),yi=new li}}();var r,e,u=t.useProxies,o=t.enforceActions;if(void 0!==u&&(yi.useProxies="always"===u||"never"!==u&&"undefined"!=typeof Proxy),"ifavailable"===u&&(yi.verifyProxies=!0),void 0!==o){var s="always"===o?"always":"observed"===o;yi.enforceActions=s,yi.allowStateChanges=!0!==s&&"always"!==s}["computedRequiresReaction","reactionRequiresObservable","observableRequiresReaction","disableErrorBoundaries","safeDescriptors"].forEach((function(n){n in t&&(yi[n]=!!t[n])})),yi.allowStateReads=!yi.observableRequiresReaction,t.reactionScheduler&&(r=t.reactionScheduler,e=mi,mi=function(t){return r((function(){return e(t)}))})},t.createAtom=x,t.defineProperty=function(t,i,r){if(an(t))return t[Ln].h(i,r);n(39)},t.entries=function(t){return an(t)?Dt(t).map((function(n){return[n,t[n]]})):Xi(t)?Dt(t).map((function(n){return[n,t.get(n)]})):Ui(t)?Array.from(t.entries()):sn(t)?t.map((function(t,n){return[n,t]})):void n(7)},t.extendObservable=Rt,t.flow=Ri,t.flowResult=function(t){return t},t.get=function(t,i){if(Bt(t,i))return an(t)?t[Ln].lt(i):Xi(t)?t.get(i):sn(t)?t[i]:void n(11)},t.getAtom=bn,t.getDebugName=pn,t.getDependencyTree=function(t,n){return kt(bn(t,n))},t.getObserverTree=function(t,n){return Et(bn(t,n))},t.has=Bt,t.intercept=function(t,n,i){return u(i)?function(t,n,i){return yn(t,n).gt(i)}(t,n,i):function(t,n){return yn(t).gt(n)}(t,n)},t.isAction=_t,t.isBoxedObservable=fi,t.isComputed=function(t){return Lt(t)},t.isComputedProp=function(t,n){return Lt(t,n)},t.isFlow=Kt,t.isFlowCancellationError=function(t){return t instanceof Tt},t.isObservable=Pt,t.isObservableArray=sn,t.isObservableMap=Xi,t.isObservableObject=an,t.isObservableProp=function(t,n){return It(t,n)},t.isObservableSet=Ui,t.keys=Dt,t.makeAutoObservable=function(t,n,i){if(f(t))return Rt(t,t,n,i);var r=fn(t,i)[Ln];if(!t[Ei]){var e=Object.getPrototypeOf(t),u=new Set([].concat(Tn(t),Tn(e)));u.delete("constructor"),u.delete(Ln),a(e,Ei,u)}bt();try{t[Ei].forEach((function(t){return r.u(t,!n||!(t in n)||n[t])}))}finally{yt()}return t},t.makeObservable=function(t,n,i){var r=fn(t,i)[Ln];bt();try{null!=n||(n=function(t){return y(t,Kn)||a(t,Kn,w({},t[Kn])),t[Kn]}(t)),Tn(n).forEach((function(t){return r.u(t,n[t])}))}finally{yt()}return t},t.observable=Jn,t.observe=function(t,n,i,r){return u(i)?function(t,n,i,r){return yn(t,n)._t(i,r)}(t,n,i,r):function(t,n,i){return yn(t)._t(n,i)}(t,n,i)},t.onBecomeObserved=Mt,t.onBecomeUnobserved=Vt,t.onReactionError=function(t){return yi.globalReactionErrorHandlers.push(t),function(){var n=yi.globalReactionErrorHandlers.indexOf(t);n>=0&&yi.globalReactionErrorHandlers.splice(n,1)}},t.override=Bn,t.ownKeys=qt,t.reaction=function(t,n,i){function r(){if(d=!1,!y.J){var n=!1;y.track((function(){var i=Z(!1,(function(){return t(y)}));n=l||!b(s,i),f=s,s=i})),(l&&i.fireImmediately||!l&&n)&&a(s,f,y),l=!1}}var e;void 0===i&&(i=Vn);var u,o,s,f,c=null!=(e=i.name)?e:"Reaction",a=_i(c,i.onError?(u=i.onError,o=n,function(){try{return o.apply(this,arguments)}catch(t){u.call(this,t)}}):n),h=!i.scheduler&&!i.delay,v=xt(i),l=!0,d=!1,b=i.compareStructural?Dn.structural:i.equals||Dn.default,y=new pi(c,(function(){l||h?r():d||(d=!0,v(r))}),i.onError,i.requiresObservable);return y.Y(),y.Z()},t.remove=function(t,i){an(t)?t[Ln].Gt(i):Xi(t)||Ui(t)?t.delete(i):sn(t)?("number"!=typeof i&&(i=parseInt(i,10)),t.splice(i,1)):n(9)},t.runInAction=gt,t.set=function t(i,r,e){if(2!==arguments.length||Ui(i))an(i)?i[Ln].dt(r,e):Xi(i)?i.set(r,e):Ui(i)?i.add(r):sn(i)?("number"!=typeof r&&(r=parseInt(r,10)),r<0&&n("Invalid index: '"+r+"'"),bt(),r>=i.length&&(i.length=r+1),i[r]=e,yt()):n(8);else{bt();var u=r;try{for(var o in u)t(i,o,u[o])}finally{yt()}}},t.spy=Ot,t.toJS=function(t){return function t(n,i){if(null==n||"object"!=typeof n||n instanceof Date||!Pt(n))return n;if(fi(n)||ai(n))return t(n.get(),i);if(i.has(n))return i.get(n);if(sn(n)){var r=Gt(i,n,new Array(n.length));return n.forEach((function(n,e){r[e]=t(n,i)})),r}if(Ui(n)){var e=Gt(i,n,new Set);return n.forEach((function(n){e.add(t(n,i))})),e}if(Xi(n)){var u=Gt(i,n,new Map);return n.forEach((function(n,r){u.set(r,t(n,i))})),u}var o=Gt(i,n,{});return qt(n).forEach((function(r){xn.propertyIsEnumerable.call(n,r)&&(o[r]=t(n[r],i))})),o}(t,new Map)},t.trace=Xt,t.transaction=Ht,t.untracked=ot,t.values=function(t){return an(t)?Dt(t).map((function(n){return t[n]})):Xi(t)?Dt(t).map((function(n){return t.get(n)})):Ui(t)?Array.from(t.values()):sn(t)?t.slice():void n(6)},t.when=function(t,n,i){return 1===arguments.length||n&&"object"==typeof n?Ft(t,n):Ut(t,n,i||{})},Object.defineProperty(t,"__esModule",{value:!0})}));
//# sourceMappingURL=mobx.umd.production.min.js.map
