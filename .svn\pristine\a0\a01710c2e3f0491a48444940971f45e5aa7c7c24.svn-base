﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="12.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{623361CF-3E9C-4C1A-B519-2612F0349FC3}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>Code.Process.Common</RootNamespace>
    <AssemblyName>Code.Process.Common</AssemblyName>
    <TargetFrameworkVersion>v4.5</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <SolutionDir Condition="$(SolutionDir) == '' Or $(SolutionDir) == '*Undefined*'">..\</SolutionDir>
    <RestorePackages>true</RestorePackages>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="log4net, Version=1.2.10.0, Culture=neutral, PublicKeyToken=692fbea5521e1304, processorArchitecture=AMD64">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\DLL\log4net.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Core" />
    <Reference Include="System.Web" />
    <Reference Include="System.Web.Extensions" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="CommonProcess.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="ProcessNew.cs" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\CommonLib\CommonLib.csproj">
      <Project>{fc03a7d4-8ef2-4dea-a15a-c099eb77b0eb}</Project>
      <Name>CommonLib</Name>
    </ProjectReference>
    <ProjectReference Include="..\DocOcr\DocOcr.csproj">
      <Project>{3de4eee8-b020-4863-bbde-5118b1b7137f}</Project>
      <Name>DocOcr</Name>
    </ProjectReference>
    <ProjectReference Include="..\HanZiOcr\HanZiOcr.csproj">
      <Project>{2d245cfb-bf26-4b49-8e5c-1a25fa8dca44}</Project>
      <Name>HanZiOcr</Name>
    </ProjectReference>
    <ProjectReference Include="..\ImageLib\ImageLib.csproj">
      <Project>{B7E169A2-3104-40FB-9D1E-2FF911FA45E5}</Project>
      <Name>ImageLib</Name>
    </ProjectReference>
    <ProjectReference Include="..\ImageOCR\ImageOCR.csproj">
      <Project>{685229f9-8bf1-4e67-b75c-9855cbf0c691}</Project>
      <Name>ImageOCR</Name>
    </ProjectReference>
    <ProjectReference Include="..\MathOcr\MathOcr.csproj">
      <Project>{4208d6de-5093-4ab3-9f21-453d46a9f4cb}</Project>
      <Name>MathOcr</Name>
    </ProjectReference>
    <ProjectReference Include="..\TableZiOcr\TableOcr.csproj">
      <Project>{68876e32-6a4c-4271-a45a-6f6acc9030b0}</Project>
      <Name>TableOcr</Name>
    </ProjectReference>
    <ProjectReference Include="..\TransOcr\TransOcr.csproj">
      <Project>{00a0c26f-ae34-4b16-802c-d271fdc5a8da}</Project>
      <Name>TransOcr</Name>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup>
    <Content Include="Log4Net.config">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <Import Project="$(SolutionDir)\.nuget\NuGet.targets" Condition="Exists('$(SolutionDir)\.nuget\NuGet.targets')" />
  <Target Name="EnsureNuGetPackageBuildImports" BeforeTargets="PrepareForBuild">
    <PropertyGroup>
      <ErrorText>This project references NuGet package(s) that are missing on this computer. Enable NuGet Package Restore to download them.  For more information, see http://go.microsoft.com/fwlink/?LinkID=322105. The missing file is {0}.</ErrorText>
    </PropertyGroup>
    <Error Condition="!Exists('$(SolutionDir)\.nuget\NuGet.targets')" Text="$([System.String]::Format('$(ErrorText)', '$(SolutionDir)\.nuget\NuGet.targets'))" />
  </Target>
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>