﻿using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Collections.Specialized;
using System.Linq;
using System.Net;
using System.Reflection;
using System.Text;
using System.Threading.Tasks;

namespace CommonLib
{
    public delegate byte[] GetImage(
        CNNWebClient myClient, string Url, ref string CookieStr, string ipAddress = "", string strPost = "",
        bool isMobile = false, NameValueCollection collect = null);

    public delegate string GetNoSyncHtml(
        string Url, ref string CookieStr, out bool isCache, string ipAddress = "", string strPost = ""
        , string Referer = "", NameValueCollection collect = null, int timeOut = 2);

    public class WebClientSyncExt
    {
        private static readonly ConcurrentBag<CNNWebClient> lstCache = new ConcurrentBag<CNNWebClient>();

        public static WebProxy DefaultProxy = null;

        private static CNNWebClient GetOneClient()
        {
            try
            {
                CNNWebClient myClient = null;
                lock (lstCache)
                {
                    if (lstCache.Count > 0)
                    {
                        myClient = lstCache.FirstOrDefault(p => !p.IsBusy && !p.IsUsed);
                    }
                    if (myClient == null)
                    {
                        myClient = new CNNWebClient();
                        lstCache.Add(myClient);
                    }
                    myClient.IsUsed = true;
                }
                return myClient;
            }
            catch (Exception oe)
            {
            }
            return GetOneClient();
        }


        #region Web

        public static string GetHtml(string url, double timeOut)
        {
            return GetHtml(url, "", "", 1, timeOut > 0 ? (int)timeOut : 2);
        }

        public static string GetHtml(string url, string cookie, string post = "", int retryCount = 1, int timeOut = 2)
        {
            var strTmp = string.Empty;
            retryCount = retryCount <= 0 ? 1 : retryCount;
            for (var i = 0; i < retryCount; i++)
            {
                strTmp = GetHtml(url, ref cookie, post, "", timeOut);
                if (!string.IsNullOrEmpty(strTmp))
                    break;
            }
            if (strTmp != null && strTmp.Equals(" "))
                strTmp = "";
            return strTmp;
        }

        public static string GetHtml(string url, string strPost = "", int timeOut = 2, NameValueCollection collect = null)
        {
            return GetHtml(url, "", strPost, "", timeOut, collect);
        }

        public static string GetHtml(string Url, string CookieStr, string strPost = "", string Referer = "", int timeOut = 2, NameValueCollection collect = null)
        {
            return GetHtml(Url, ref CookieStr, strPost, Referer, timeOut, collect);
        }

        public static string GetHtml(string Url, ref string CookieStr, string strPost = "", string Referer = "", int timeOut = 2, NameValueCollection collect = null)
        {
            var result = "";
            var myClient = GetOneClient();
            try
            {
                myClient.Headers.Clear();
                //if (isMobile)
                //    myClient.Credentials = CredentialCache.DefaultCredentials;
                myClient.Timeout = timeOut;

                var isJson = !string.IsNullOrEmpty(strPost) && strPost.StartsWith("{");

                if (isJson || Url.Contains("ocr.bj.baidubce.com") || Url.Contains("googleapis.com")
                    || Url.Contains("jianwai.netease.com") || Url.Contains("api.cognitive.microsoft.com")
                    || Url.Contains("api.projectoxford.ai") || Url.Contains("face.wit.hk")
                    || Url.Contains("s.youtux.qq.com") || Url.Contains("www.bing.com")
                    || Url.Contains("ksyun.com") || Url.Contains("www.iflyrec.com")
                    || Url.Contains("paddlepaddle.org.cn")
                    || Url.Contains("m.ctrip.com") || Url.Contains("microsofttranslator.com")
                    || Url.Contains("ai.wps.cn"))
                    myClient.Headers.Add("Content-Type: application/json");
                else if (Url.Contains("12306.ie.sogou.com"))
                    myClient.Headers.Add("Content-Type: text/plain;charset=UTF-8");
                else if (Url.Contains("ai.xueersi.com"))
                {
                    if (Url.Contains("text_recognition_transfer"))
                        myClient.Headers.Add("Content-Type: application/octet-stream");
                    else
                        myClient.Headers.Add("Content-Type: application/x-www-form-urlencoded;charset=UTF-8");
                }
                else if (Url.Contains("vivo.com.cn") || Url.Contains("api.frdic.com")
                    || Url.Contains("api/sogouService2") || Url.Contains("cci_ai/service"))
                    myClient.Headers.Add("Content-Type: application/octet-stream");
                else if (Url.Contains("youtu.qq.com") || Url.Contains("grapecity.com.cn"))
                    myClient.Headers.Add("Content-Type: text/json");
                else
                    myClient.Headers.Add("Content-Type: application/x-www-form-urlencoded;charset=UTF-8");


                if (Url.Contains("youtu.qq.com") || Url.Contains("grapecity.com.cn")
                    || Url.Contains("www.xinhuokj.com") || Url.Contains("fanyi.yeekit.com"))
                {
                    myClient.Headers.Add("X-Requested-With: XMLHttpRequest");
                }

                if (isJson || Url.Contains("ocr.bj.baidubce.com") || Url.Contains("jianwai.netease.com") ||
                    Url.Contains("googleapis.com") || Url.Contains("azure.cn") || Url.Contains("youtu.qq.com")
                    || Url.Contains("s.youtux.qq.com") || Url.Contains("www.xinhuokj.com")
                    || Url.Contains("m.ctrip.com") || Url.Contains("microsofttranslator.com")
                    || Url.Contains("papago.naver.com") || Url.Contains("ocr.wdku.net"))
                    myClient.Headers.Add("Accept: application/json");
                else
                    myClient.Headers.Add("Accept: */*;");

                myClient.Headers.Add("Accept-Language: zh-CN,zh;q=0.8");
                myClient.Headers.Add("Cache-Control: no-cache");

                if (!string.IsNullOrEmpty(Referer))
                    myClient.Headers.Add("Referer: " + Referer);
                else
                    myClient.Headers.Add("Referer: " + Url);
                if (!string.IsNullOrEmpty(CookieStr))
                {
                    myClient.Headers.Add("Cookie: " + CookieStr);
                }
                myClient.Encoding = Encoding.UTF8;
                if (Url.Contains("oldfish") || Url.Contains("train") || Url.Contains("azure.cn")
                    || Url.Contains("?op=") || Url.Contains("ip.cn") || Url.Contains(".ashx")
                    || Url.Contains("googleapis.com") || Url.Contains("ocr.bj.baidubce.com")
                    || Url.Contains("face.wit.hk") || Url.Contains("vivo.com.cn") || Url.Contains("youtu.qq.com")
                    || Url.Contains("jianwai.netease.com"))
                {

                }
                if (collect != null && collect.Count > 0)
                {
                    foreach (string key in collect)
                    {
                        try
                        {
                            //    myClient.Headers.Add(string.Format("{0}: {1}", key.Trim(),
                            //        collect[key] == null ? "" : collect[key].Trim()));
                            myClient.SetHeaderValue(myClient.Headers, key.Trim(),
                                collect[key] == null ? "" : collect[key].Trim());
                        }
                        catch (Exception oe)
                        {
                            Console.WriteLine(oe.Message);
                        }
                    }
                }
                var uri = new Uri(Url, false);
                var tmp = DoWebRequest(uri, strPost, myClient);
                result = tmp.Result;
                if (myClient.ResponseHeaders != null)
                {
                    var loc = myClient.ResponseHeaders["Location"];
                    if (!string.IsNullOrEmpty(loc) && !Url.Equals(loc))
                    {
                        return GetHtml(loc, ref CookieStr, strPost, Referer, timeOut);
                    }
                }
                if (string.IsNullOrEmpty(result))
                {
                    result = "";
                }
            }
            catch (OutOfMemoryException oe)
            {
                GC.Collect();
            }
            catch { }
            finally
            {
                if (myClient.ResponseHeaders != null)
                {
                    if (!string.IsNullOrEmpty(myClient.ResponseHeaders["Date"]))
                    {
                        ServerTime.SetHttpDate(DateTime.Parse(myClient.ResponseHeaders["Date"]));
                    }
                    if (!string.IsNullOrEmpty(myClient.ResponseHeaders["Set-Cookie"]))
                    {
                        CookieStr = GetNewCookie(CookieStr, myClient.ResponseHeaders["Set-Cookie"].Trim());
                    }
                    //CookieStr = myClient.ResponseHeaders["Set-Cookie"].Replace("Path=/otn", " ").Replace("Path=/", "").Replace("path=/", "").Replace(",", "").Trim();
                }
                try
                {
                    Url = null;
                    strPost = null;
                    strPost = null;
                    Referer = null;
                    collect = null;
                }
                catch { }
                try
                {
                    if (myClient.IsBusy)
                        myClient.CancelAsync();
                }
                catch
                {
                }
                myClient.IsUsed = false;
            }
            return result;
        }

        #endregion

        private static async Task<string> DoWebRequest(Uri uri, string strPost, CNNWebClient myClient, bool isRetry = false)
        {
            var result = "";
            try
            {
                if ("application/octet-stream".Equals(myClient.Headers.Get("Content-Type")))
                {
                    var task = myClient.UploadDataTaskAsync(uri, "POST", Convert.FromBase64String(strPost));
                    task.Wait(myClient.Timeout * 1000);
                    if (task.IsCompleted)
                    {
                        result = Encoding.UTF8.GetString(task.Result);
                    }
                    if (task.Status == TaskStatus.Canceled || task.Status == TaskStatus.RanToCompletion || task.Status == TaskStatus.Faulted)
                        task.Dispose();
                }
                else
                {
                    using (var task = string.IsNullOrEmpty(strPost)
                            ? myClient.DownloadStringTaskAsync(uri)
                            : myClient.UploadStringTaskAsync(uri, strPost))
                    {
                        task.Wait(myClient.Timeout * 1000);
                        if (task.IsCompleted)
                        {
                            result = task.Result;
                        }
                        if (task.Status == TaskStatus.Canceled || task.Status == TaskStatus.RanToCompletion || task.Status == TaskStatus.Faulted)
                            task.Dispose();
                    }
                }
            }
            catch (Exception oe)
            {
                if (oe.InnerException != null)
                {
                    if (!isRetry)
                    {
                        result = DoWebRequest(uri, strPost, myClient, true).Result;
                    }
                    else
                    {
                        using (HttpWebResponse response = (oe.InnerException as WebException).Response as HttpWebResponse)
                        {
                            if (response != null)
                            {
                                switch (response.StatusCode)
                                {
                                    case HttpStatusCode.MethodNotAllowed: //405
                                    case HttpStatusCode.Unauthorized://401
                                    case HttpStatusCode.NotFound: //404
                                        result = response.StatusCode.ToString();
                                        break;
                                    default:
                                        result = new System.IO.StreamReader(response.GetResponseStream()).ReadToEnd().Trim();
                                        break;
                                }
                            }
                        }
                    }
                }
                //Console.WriteLine(oe.Message);
            }
            return result;
        }

        private static string GetNewCookie(string strCookie, string strNewCookie)
        {
            var strTmpCookie = strCookie ?? "";
            if (!string.IsNullOrEmpty(strNewCookie))
            {
                var lstTmp = new List<string>();
                lstTmp.AddRange(strNewCookie.Split(new[] { ",", ";" }, StringSplitOptions.RemoveEmptyEntries));
                var strItem = "";
                foreach (var item in lstTmp)
                {
                    if (!item.Trim().ToLower().StartsWith("path=")
                        && !item.Trim().ToLower().StartsWith("expires=")
                        && !item.Trim().ToLower().StartsWith("httponly=")
                        && !item.Trim().ToLower().StartsWith("domain=.")
                        && item.IndexOf("=") > 0)
                    {
                        strItem = SubStringHorspool(item.Trim(), "", "=") + "=";
                        if (!strTmpCookie.Contains(strItem))
                        {
                            strTmpCookie += string.Format(";{0};", item.Trim());
                        }
                        else
                        {
                            strTmpCookie = strTmpCookie.Replace(
                                strItem + SubStringHorspool(strTmpCookie, strItem, ";"), item);
                        }
                    }
                }
            }
            return strTmpCookie.Replace(" ", "").Replace(";;", ";").TrimStart(';');
        }

        public static string SubStringHorspool(string str, string strStart, string strEnd = "")
        {
            var index = 0;
            if (!string.IsNullOrEmpty(strStart))
            {
                index = str.IndexOf(strStart);
                if (index >= 0)
                {
                    str = str.Substring(index + strStart.Length);
                }
                else
                    str = "";
            }
            if (!string.IsNullOrEmpty(str) && !string.IsNullOrEmpty(strEnd))
            {
                index = str.IndexOf(strEnd);
                if (index >= 0)
                {
                    str = str.Substring(0, index);
                }
                else
                    str = "";
            }
            strStart = null;
            strEnd = null;
            return str;
        }
    }

    ///// <summary>
    ///// 过期时回调委托
    ///// </summary>
    ///// <param name="userdata"></param>
    //public delegate void TimeoutCaller(object userdata);

    public class CNNWebClient : WebClient
    {
        private int _timeOut = 3;

        private string strHost = "";

        private string strIPAddress = "";

        public bool IsUsed { get; set; }

        public string StrIPAddress
        {
            get { return strIPAddress; }
            set { strIPAddress = value; }
        }

        public string StrHost
        {
            get { return strHost; }
            set { strHost = value; }
        }

        /// <summary>
        ///     过期时间
        /// </summary>
        public int Timeout
        {
            get
            {
                if (_timeOut <= 0)
                    _timeOut = 3;
                return _timeOut;
            }
            set
            {
                if (value <= 0)
                    _timeOut = 3;
                _timeOut = value;
            }
        }

        ~CNNWebClient()
        {
            Dispose(false);
        }

        public new void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        public override string ToString()
        {
            return string.Format("{0}-{1}", StrHost, StrIPAddress);
        }

        //public bool RemoteCertificateValidationCallback(object sender, X509Certificate certificate, X509Chain chain, SslPolicyErrors errors)
        //{
        //    return true;
        //}

        /// <summary>
        ///     重写GetWebRequest,添加WebRequest对象超时时间
        /// </summary>
        /// <param name="address"></param>
        /// <returns></returns>
        protected override WebRequest GetWebRequest(Uri address)
        {
            HttpWebRequest NowRequest = null;
            //if (DateTime.Now.Second % 2 == 0)
            //{
            //    //System.Threading.Thread.Sleep(1);
            //    System.GC.Collect();
            //}

            StrHost = address.Host;
            if (!string.IsNullOrEmpty(StrIPAddress))
                address = new Uri(address.Scheme + "://" + StrIPAddress + address.PathAndQuery, true);
            //if (isMobile)
            //    ServicePointManager.ServerCertificateValidationCallback = new RemoteCertificateValidationCallback(this.RemoteCertificateValidationCallback);
            try
            {
                NowRequest = (HttpWebRequest)base.GetWebRequest(address);
            }
            catch (Exception oe)
            {
                NowRequest = (HttpWebRequest)WebRequest.Create(address);
                Console.WriteLine(oe.Message);
            }
            //address.AbsoluteUri.HorspoolIndex("leftTicket") > 0 ? (HttpWebRequest)HttpWebRequest.Create(address.AbsoluteUri) :
            NowRequest.ProtocolVersion = HttpVersion.Version11;
            //是否使用 Nagle 不使用 提高效率 
            NowRequest.ServicePoint.UseNagleAlgorithm = false;
            //最大连接数 
            NowRequest.ServicePoint.ConnectionLimit = 1000;
            //数据是否缓冲 false 提高效率
            NowRequest.AllowWriteStreamBuffering = false;
            NowRequest.ServicePoint.Expect100Continue = false;
            NowRequest.Headers.Add("Accept-Encoding: gzip, deflate");
            NowRequest.AutomaticDecompression = DecompressionMethods.Deflate | DecompressionMethods.GZip;
            NowRequest.AllowAutoRedirect = false;
            NowRequest.KeepAlive = true;
            ////request.KeepAlive = true;
            //if (address.AbsoluteUri.Contains("/query"))
            //{
            //    NowRequest.Headers.Add("If-None-Match", ServerTime.DateTime.Ticks.ToString());
            //    NowRequest.CachePolicy = new HttpRequestCachePolicy(HttpRequestCacheLevel.NoCacheNoStore);
            //    //SetHeaderValue(request.Headers, "If-Modified-Since", "0");
            //    strIPAddress = string.Format("{0}.{1}.{2}.{3}", new Random().Next(10, 120), new Random().Next(120, 250), new Random().Next(130, 250), new Random().Next(1, 250));
            //    SetHeaderValue(NowRequest.Headers, "X-Forwarded-For", strIPAddress);
            //    SetHeaderValue(NowRequest.Headers, "Proxy-Client-IP", strIPAddress);
            //    SetHeaderValue(NowRequest.Headers, "WL-Proxy-Client-IP", strIPAddress);
            //}
            ////CommonMethod.SetHeaderValue(request.Headers, "If-Modified-Since", "Wed, 31 Dec 1969 16:00:00 GMT");
            //StrHost = address.Host;
            NowRequest.UserAgent =
                "Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/45.0.2454.101 Safari/537.36";
            try
            {
                if (!NowRequest.Host.Equals(StrHost))
                    NowRequest.Host = StrHost;
                NowRequest.Timeout = 1000 * Timeout;
                //NowRequest.ReadWriteTimeout = 1000;// *Timeout;
            }
            catch
            {
            }
            NowRequest.Proxy = null;
            ////if (request.Proxy != null)
            ////    request.Proxy = GlobalProxySelection.GetEmptyWebProxy();
            ////if (NowRequest.Proxy != null)
            //if (!address.Host.Equals("12306.ie.sogou.com"))
            //{
            //    NowRequest.Proxy = null;
            //}
            //else
            //{
            //    // 在发起HTTP请求前将proxy赋值给HttpWebRequest的Proxy 属性
            //    NowRequest.Proxy = WebClientSyncExt.DefaultProxy;
            //}
            return NowRequest;
        }

        //protected override WebResponse GetWebResponse(WebRequest request)
        //{
        //    WebResponse result = null;
        //    try
        //    {
        //        result = base.GetWebResponse(request);
        //    }
        //    catch (Exception oe)
        //    {
        //        Console.WriteLine(oe.Message);
        //    }
        //    finally
        //    {
        //        ////request.Abort();
        //        //request = null;
        //    }
        //    return result;
        //}

        public void SetHeaderValue(WebHeaderCollection header, string name, string value)
        {
            var property = typeof(WebHeaderCollection).GetProperty("InnerCollection",
                BindingFlags.Instance | BindingFlags.NonPublic);
            if (property != null)
            {
                var collection = property.GetValue(header, null) as NameValueCollection;
                collection[name] = value;
            }
        }
    }
}