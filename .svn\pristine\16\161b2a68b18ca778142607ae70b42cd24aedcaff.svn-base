﻿using System;
using System.Diagnostics;
using System.Runtime.InteropServices;
using System.Timers;

namespace CommonLib
{
    public static class MemoryManager
    {
        static Timer timer;

        public static void Init()
        {
            if (timer == null)
            {
                timer = new Timer(new Random().Next(30, 120) * 1000);
                timer.Elapsed += (sender, e) =>
                {
                    ClearMemory();
                };
                timer.Start();
            }
        }

        #region 内存回收

        [DllImport("kernel32.dll", EntryPoint = "SetProcessWorkingSetSize")]
        private static extern int SetProcessWorkingSetSize(IntPtr process, int minSize, int maxSize);

        /// <summary>
        /// 释放内存
        /// </summary>
        static void ClearMemory()
        {
            try
            {
                GC.Collect();
                GC.WaitForPendingFinalizers();
                if (Environment.OSVersion.Platform == PlatformID.Win32NT)
                {
                    SetProcessWorkingSetSize(Process.GetCurrentProcess().Handle, -1, -1);
                }
            }
            catch { }
        }

        #endregion
    }
}