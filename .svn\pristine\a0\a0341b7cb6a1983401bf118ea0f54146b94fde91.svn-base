﻿using System;
using System.Collections.Generic;
using System.Linq;
using Enterprise.Framework.Redis;

namespace CommonLib
{
    public class ServerLogCache : RedisCacheObject<List<ServerComponent>>
    {
        protected override string CurrentObject_KeyPrefix
        {
            get { return "ServerLogCache:"; }
        }

        protected override string Object_DBName
        {
            get { return "OPS_Cache"; }
        }

        private string GetKey(DateTime dtDate)
        {
            return dtDate.ToString("yyyy-MM-dd");
        }

        public List<ServerComponent> GetByDate(DateTime dtDate)
        {
            return Get(GetKey(dtDate));
        }

        public void Add(DateTime dtDate, ServerComponent msg)
        {
            var old = GetByDate(dtDate) ?? new List<ServerComponent>();
            old.Add(msg);
            Insert(GetKey(dtDate), old, ServerTime.DateTime.AddDays(7));
        }
    }
}