﻿using CommonLib;
using System;
using System.Collections.Generic;
using System.Collections.Specialized;
using System.IO;

namespace HanZiOcr
{
    /// <summary>
    /// https://uutool.cn/ocr/
    /// </summary>
    public class UUkitApiRec : BaseOcrRec
    {
        public UUkitApiRec()
        {
            MaxExecPerTime = 15;
            OcrType = HanZiOcrType.UUKit;

            LstJsonPreProcessArray = new List<object>() { "data", "rows" };
            IsProcessJsonResultByArray = true;
        }

        protected override string GetHtml(OcrContent content)
        {
            var byt = Convert.FromBase64String(content.strBase64);
            var result = PostFileResult(byt);
            //"{\"code\":0,\"msg\":\"成功\",\"data\":\"[{\\\"confidence\\\":1,\\\"word\\\":[{\\\"content\\\":\\\"请点击下图中所有的靴子\\\"},{\\\"content\\\":\\\"火柴\\\"}]}]\",\"content\":null,\"audioPath\":null}"
            return result;
        }

        private string PostFileResult(byte[] content)
        {
            var result = "";
            try
            {
                var url = "https://api.uukit.com/photo/ocr/";
                var file = new UploadFileInfo()
                {
                    Name = "file",
                    Filename = "test.jpg",
                    ContentType = "image/jpg",
                    Stream = new MemoryStream(content)
                };
                result = PostFile(url, new[] { file }, null);
            }
            catch (Exception)
            {

            }
            return result;
        }

    }
}