﻿using System;
using System.Collections.Generic;

namespace CommonLib
{
    public class UserLoginCache
    {
        LocalWaitCache<UserLoginInfo> UserCache = new LocalWaitCache<UserLoginInfo>("UserLoginCache", new TimeSpan(1, 0, 0, 0), false);

        public const string TestUser = "test";

        public string GetTestUserName(string token)
        {
            return string.Format("{0}:{1}", TestUser, token);
        }

        public int GetLoginUserCount(DateTime dtMin)
        {
            return UserCache.KeysCount(dtMin);
        }

        public void Remove(string account)
        {
            UserCache.Remove(account);
        }

        public void ClearToken(string account, string token = null)
        {
            var loginInfo = GetUserInfo(account);
            if (loginInfo != null)
            {
                if (string.IsNullOrEmpty(token))
                {
                    loginInfo.LstToken.Clear();
                }
                else
                {
                    loginInfo.LstToken.RemoveAll(p => Equals(p.To<PERSON>, token));
                }
                InsertOrUpdateUser(loginInfo);
            }
        }

        public UserLoginInfo GetUserInfo(string account)
        {
            var loginInfo = UserCache.Get(account);
            if (loginInfo != null)
            {
                if (loginInfo.IsExpired && !Equals(loginInfo.UserType, UserTypeEnum.体验版))
                {
                    loginInfo.UserType = UserTypeEnum.体验版;
                }
                loginInfo.LstToken.RemoveAll(p => !p.IsValidate);
            }
            else
            {
                if (account.StartsWith(TestUser))
                {
                    loginInfo = new UserLoginInfo
                    {
                        Account = account,
                        UserType = UserTypeEnum.体验版,
                        LstToken = new List<TokenEntity>()
                    };
                }
            }
            return loginInfo;
        }

        public void InsertOrUpdateUser(UserLoginInfo loginInfo)
        {
            UserCache.Set(loginInfo.Account, loginInfo);
        }

        public bool HeartBeat(string account, string token)
        {
            bool result = false;
            if (!string.IsNullOrEmpty(account) && !string.IsNullOrEmpty(token))
            {
                var loginInfo = GetUserInfo(account);
                if (loginInfo != null)
                {
                    if (account.StartsWith(TestUser) && !loginInfo.LstToken.Exists(p => Equals(p.Token, token)))
                    {
                        loginInfo.LstToken.Add(new TokenEntity { Token = token });
                    }

                    if (loginInfo.LstToken.Exists(p => Equals(p.Token, token)))
                    {
                        result = true;
                        loginInfo.LstToken.Find(p => p.Token.Equals(token)).DtExpired = ServerTime.LocalTime.AddHours(1);
                        loginInfo.DtLastHeat = ServerTime.LocalTime;
                    }

                    InsertOrUpdateUser(loginInfo);
                }
            }
            return result;
        }

        public bool ValidateUserAndToken(string account, string token, ref UserTypeEnum userType)
        {
            bool result = false;
            if (!string.IsNullOrEmpty(account) && !string.IsNullOrEmpty(token))
            {
                var loginInfo = GetUserInfo(account);
                if (loginInfo != null)
                {
                    userType = loginInfo.UserType;
                    result = loginInfo.LstToken.Exists(p => Equals(p.Token, token));
                }
            }
            return result;
        }
    }

    public class UserLoginInfo
    {
        public string Account { get; set; }
        public string NickName { get; set; }

        public UserTypeEnum UserType { get; set; }

        public string UserTypeName { get; set; }

        public List<TokenEntity> LstToken { get; set; }

        public string Token { get; set; }

        public DateTime DtLogin { get; set; }

        public DateTime DtLastHeat { get; set; }

        public string Remark { get; set; }

        public DateTime DtExpired { get; set; }

        public bool IsExpired => DtExpired < ServerTime.LocalTime;

        public DateTime DtReg { get; set; }
        public bool IsSetOtherResult { get; set; }
        public bool IsSupportBatch { get; set; }
        public bool IsSupportMath { get; set; }

        /// <summary>
        /// 是否支持图片文件识别
        /// </summary>
        public bool IsSupportImageFile { get; set; }

        /// <summary>
        /// 是否支持文档翻译
        /// </summary>
        public bool IsSupportDocFile { get; set; }
        public bool IsSupportTable { get; set; }
        public bool IsSupportTxt { get; set; }
        public bool IsSupportVertical { get; set; }
        public bool IsSupportTranslate { get; set; }

        /// <summary>
        /// 是否支持通道切换
        /// </summary>
        public bool IsSupportPassage { get; set; }

        /// <summary>
        ///最大可选通道数
        /// </summary>
        public int MaxPassageCount { get; set; } = 1;

        /// <summary>
        /// 是否支持本地OCR识别
        /// </summary>
        public bool IsSupportLocalOcr { get; set; }

        /// <summary>
        /// 最大可上传的识别文件
        /// </summary>
        public int MaxUploadSize { get; set; } = 300;

        /// <summary>
        /// 指定时间段内执行次数
        /// </summary>
        public int PerTimeSpanExecCount { get; set; } = 1;

        /// <summary>
        /// 指定时间段内（毫秒）
        /// </summary>
        public int PerTimeSpan { get; set; } = 5000;

        /// <summary>
        /// 单账户每天最大执行次数
        /// </summary>
        public long LimitPerDayCount { get; set; } = 10000;
    }

    public class TokenEntity
    {
        public string Token { get; set; }

        public DateTime DtExpired { get; set; }

        public bool IsValidate { get { return DtExpired > ServerTime.LocalTime; } }
    }
}