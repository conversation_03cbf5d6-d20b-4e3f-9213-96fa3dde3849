﻿using CommonLib;
using System;
using System.Collections.Generic;
using System.Collections.Specialized;
using System.IO;

namespace TransOcr
{
    /// <summary>
    /// fanyi.yelky.net
    /// </summary>
    public class YiQiWangRec : BaseOcrRec
    {
        public YiQiWangRec()
        {
            OcrType = TransOcrType.依网;
            MaxExecPerTime = 15;

            LstJsonPreProcessArray = new List<object>() { "data" };
            StrResultJsonSpilt = "text_contrast";
            StrResultTransJsonSpilt = "text_translate";

            AllowUploadFileTypes.Add("pdf");

            InitLanguage();
        }

        #region 支持的语言

        private void InitLanguage()
        {
            TransLanguageDic = new Dictionary<TransLanguageTypeEnum, string>();
            TransLanguageDic.Add(TransLanguageTypeEnum.自动, "auto");
            TransLanguageDic.Add(TransLanguageTypeEnum.中文, "zh");
            TransLanguageDic.Add(TransLanguageTypeEnum.英文, "en");
            TransLanguageDic.Add(TransLanguageTypeEnum.日语, "jp");
            TransLanguageDic.Add(TransLanguageTypeEnum.韩语, "kor");
            TransLanguageDic.Add(TransLanguageTypeEnum.法语, "fra");
            TransLanguageDic.Add(TransLanguageTypeEnum.德语, "de");
            TransLanguageDic.Add(TransLanguageTypeEnum.俄语, "ru");
        }

        #endregion

        protected override string GetHtml(OcrContent content)
        {
            string from = "", to = "";
            InitLanguage(content, ref from, ref to);

            var id = Upload(content, out string strTimeSpan);
            if (!string.IsNullOrEmpty(id))
            {
                string strPost = string.Format("id={0}&time={1}&page=1&langfrom={2}&langto={3}", id, strTimeSpan, from, to);
                string result = WebClientSyncExt.GetHtml("https://fanyi.yelky.net/index/get_upload_convert_pictrans", strPost, ExecTimeOutSeconds);
                return result;
            }
            return string.Empty;
        }

        private string Upload(OcrContent content, out string strTimeSpan)
        {
            var url = "https://fanyi.yelky.net/index/upload";
            var file = new UploadFileInfo()
            {
                Name = "file",
                Filename = "1." + content.fileExt,
                ContentType = ApplicationTypeHelper.GetApplicationType(content.fileExt),
                Stream = new MemoryStream(Convert.FromBase64String(content.strBase64))
            };
            var values = new NameValueCollection() {
                { "user","default"},
                { "name","1." + content.fileExt},
                { "fileName","1." + content.fileExt}
            };
            var headers = new NameValueCollection() {
                {"User-Agent","Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/53.0.2785.143 Safari/537.36 MicroMessenger/7.0.9.501 NetType/WIFI MiniProgramEnv/Windows WindowsWechat" },
                {"Referer","https://servicewechat.com/wxb1070eabc6f9107e/116/page-frame.html" },
            };
            var html = PostFile(url, new[] { file }, values, headers);
            strTimeSpan = CommonHelper.SubString(html, "time\":", ",");
            return CommonHelper.SubString(html, "id\":\"", "\"");
        }

    }
}