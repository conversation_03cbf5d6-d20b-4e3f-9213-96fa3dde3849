﻿using CommonLib;
using System;
using System.Collections.Generic;
using System.Collections.Specialized;
using System.Diagnostics;
using System.IO;
using System.Net;
using System.Security.Cryptography;
using System.Text;

namespace HanZiOcr
{
    class YouTuKey
    {
        public string strAppId { get; set; }
        public string secretId { get; set; }
        public string secretKey { get; set; }
        public string strUserId { get; set; }
    }
    /// <summary>
    /// 腾讯优图-本地计算Sign版
    /// https://open.youtu.qq.com/content/templates/developer/api-ocr-general.html
    /// https://open.youtu.qq.com/#/open/developer/general
    /// </summary>
    public class YouTuAPIRec : BaseOcrRec
    {
        public YouTuAPIRec()
        {
            OcrGroup = OcrGroupType.腾讯;
            OcrType = HanZiOcrType.腾讯优图;
            LstJsonPreProcessArray = new List<object>() { "items" };
            StrResultJsonSpilt = "itemstring";
            MaxExecPerTime = 18;

            IsSupportVertical = true;
            DicDeserializeVerticalJson = new Dictionary<string, string> { { "left", "x" }, { "top", "y" }, { "location", "itemcoord" }, { "words", "itemstring" } };
        }

        private List<YouTuKey> lstKeys = new List<YouTuKey>() {
            new YouTuKey(){
                strAppId ="10105350",
                secretId ="AKIDdA0iGoRUjx0ru6R5rSH2c1KpNm0WuWyG",
                secretKey ="FTWQONCtSWZXN2axAufGhx228JYfeS41",
                strUserId ="993801914",
            },
            new YouTuKey(){
                strAppId ="10111054",
                secretId ="AKIDmRuBMQ00Wu66uWsIROCqfOvHwVs6n4kI",
                secretKey ="ayAKMDINI2aw65wc6OVTGsJZhgUAe1DD",
                strUserId ="3424706716",
            },
            new YouTuKey(){
                strAppId ="10168049",
                secretId ="AKIDGlJgtvUCwZ9ZosDbjkROCkAPXVUNqljQ",
                secretKey ="sdCg3Fsl5GJBvsiR1Wu8ZsFSdier1qXG",
                strUserId ="974661006"
            },
        };

        const double EXPIRED_SECONDS = 2592000;

        protected override string GetHtml(OcrContent content)
        {
            string expired = UnixTime(EXPIRED_SECONDS);
            var key = lstKeys.GetRndItem();

            var strToken = appSign(expired, key.strUserId, key.strAppId, key.secretId, key.secretKey);

            EngineType = IsVerticalOcr ? ConstHelper.lstYouTuOcrWithLocationAPIs.GetRndItem() : ConstHelper.lstYouTuOcrAPIs.GetRndItem();

            //1、
            string strPost = "{\"app_id\":\"" + key.strAppId + "\",\"image\":\"" + content.strBase64 + "\"}";
            NameValueCollection collection = new NameValueCollection() { { "Authorization", strToken } };
            var result = WebClientSyncExt.GetHtml("https://api.youtu.qq.com/youtu/ocrapi/" + EngineType.Value, strPost, ExecTimeOutSeconds, collection);

            //2、
            //string strPost = "{\"image\":\"" + strBase64 + "\",\"app_id\":\"" + strAppId + "\"}";
            //var result = HttpPostData("https://open.youtu.qq.com/youtu/ocrapi/generalocr", strPost, strToken);
            //var result = WebClientSyncExt.GetHtml("https://open.youtu.qq.com/youtu/ocrapi/generalocr", strPost, ExecTimeOutSeconds, collection);

            //"{\"errorcode\":0,\"errormsg\":\"OK\",\"items\":[{\"itemcoord\":{\"x\":0,\"y\":3,\"width\":192,\"height\":20},\"itemconf\":0.9895762801170349,\"itemstring\":\"请点击下图中所有的靴子火柴\",\"coords\":[],\"words\":[{\"character\":\"请\",\"confidence\":0.9976868629455566},{\"character\":\"点\",\"confidence\":0.9913544058799744},{\"character\":\"击\",\"confidence\":0.9694445133209229},{\"character\":\"下\",\"confidence\":0.9992054104804993},{\"character\":\"图\",\"confidence\":0.9906545281410217},{\"character\":\"中\",\"confidence\":0.9999581575393677},{\"character\":\"所\",\"confidence\":1.0},{\"character\":\"有\",\"confidence\":0.9998621940612793},{\"character\":\"的\",\"confidence\":0.9999899864196777},{\"character\":\"靴\",\"confidence\":0.9252281785011292},{\"character\":\"子\",\"confidence\":0.999544084072113},{\"character\":\"火\",\"confidence\":0.9967591166496277},{\"character\":\"柴\",\"confidence\":0.994803249835968}],\"candword\":[],\"parag\":{\"word_size\":17,\"parag_no\":0},\"coordpoint\":{\"x\":[0,3,191,3,191,22,0,22]},\"wordcoordpoint\":[]}],\"session_id\":\"\",\"angle\":0.0,\"class\":[],\"recognize_warn_code\":[],\"recognize_warn_msg\":[]}"
            return result;
        }

        private string appSign(string expired, string strUserId, string strAppId, string secretId, string secretKey)
        {
            string time = UnixTime();

            string plainText = SetOrignal(strUserId, strAppId, secretId, time, expired);

            byte[] signByteArrary = JoinByteArr(HmacSha1Sign(plainText, secretKey), System.Text.Encoding.UTF8.GetBytes(plainText));

            return Convert.ToBase64String(signByteArrary);

        }
        private string SetOrignal(string userid, string appid, string secretid, string stime, string etime = "0")
        {
            return string.Format("u={0}&a={1}&k={2}&e={3}&t={4}&r={5}&f={6}", userid, appid, secretid, etime, stime, new Random()
               .Next(0, 1000000000), "");
        }
        private string UnixTime(double expired = 0)
        {
            var time = (DateTime.Now.AddSeconds(expired).ToUniversalTime().Ticks - 621355968000000000) / 10000000;
            return time.ToString();
        }

        private byte[] HmacSha1Sign(string str, string key)
        {
            byte[] keyBytes = Encoding.UTF8.GetBytes(key);
            HMACSHA1 hmac = new HMACSHA1(keyBytes);
            byte[] inputBytes = Encoding.UTF8.GetBytes(str);
            return hmac.ComputeHash(inputBytes);
        }
        private byte[] JoinByteArr(byte[] byte_0, byte[] byte_1)
        {
            byte[] array = new byte[byte_0.Length + byte_1.Length];
            Stream stream = new MemoryStream();
            stream.Write(byte_0, 0, byte_0.Length);
            stream.Write(byte_1, 0, byte_1.Length);
            stream.Position = 0L;
            int num = stream.Read(array, 0, array.Length);
            if (num <= 0)
            {
                throw new Exception("读取错误!");
            }
            return array;
        }

    }
}