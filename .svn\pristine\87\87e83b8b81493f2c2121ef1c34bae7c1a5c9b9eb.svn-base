﻿using CommonLib;
using System;
using System.Collections.Generic;

namespace TableOcr
{
    /// <summary>
    /// 腾讯优图-Demo演示版
    /// https://open.youtu.qq.com/#/open/experience/table
    /// https://open.youtu.qq.com/#/open/developer/table
    /// </summary>
    public class YouTuRec : BaseTableRec
    {
        public YouTuRec()
        {
            OcrGroup = OcrGroupType.腾讯;
            OcrType = TableOcrType.腾讯优图Open;
            MaxExecPerTime = 15;
            IsSupportUrlOcr = true;

            LstJsonPreProcessArray = new List<object>() { "tableRes", "tables", 0, "cells" };
            LstJsonResultProcessArray = new List<object>() { "cell_content_text" };
            /*
             tl_col	是	Int32	单元格左上角的列索引
            tl_row	是	Int32	单元格左上角的行索引
            br_col	是	Int32	单元格右下角的列索引
            br_row	是	Int32	单元格右下角的行索引
             */
            LstRowIndex = new List<object>() { "tl_row", "br_row" };
            LstColumnIndex = new List<object>() { "tl_col", "br_col" };
            RowIndexIsArray = false;
            IsRowIndexAddOne = true;
        }

        private const string strSign = "\"sign\":\"";

        private string strAppId = "10009633";
        private string strToken = "";

        private void InitToken()
        {
            if (string.IsNullOrEmpty(strToken))
            {
                strToken = GetToken();
            }
        }
        private string GetToken()
        {
            var result = "";
            var token = WebClientSyncExt.GetHtml("https://open.youtu.qq.com/experience_ctl/get_sign", 5);

            if (!string.IsNullOrEmpty(token) && token.Contains(strSign))
            {
                result = token.Substring(token.IndexOf(strSign) + strSign.Length);
                result = result.Substring(0, result.IndexOf("\""));
            }
            return result;
        }

        public override void Reset()
        {
            strToken = "";
            base.Reset();
        }

        protected override string GetHtml(OcrContent content)
        {
            return PostFileResult(content.strBase64);
        }

        public override string GetHtmlByUrl(OcrContent content)
        {
            return PostFileResult(null, content.url);
        }

        private string PostFileResult(string strBase64, string imgUrl = null)
        {
            var result = "";
            //var byt = Convert.FromBase64String(strBase64);
            //InitToken();
            ////{"succ":true,"sign":"WXkLjT+ClVTBwxCprDaxodIuu+lhPTEwMDA5NjMzJms9QUtJRGpYQmR1ek9hNlF1SlpmNUpxQk5PSzdqOVBhZFhqbDhKJmU9MTUzMzI4ODQ1OCZ0PTE1MzMyMDIwNTgmcj0xNTczNjM0NjQ1JnU9MzI1NTIwNTg3Mg=="}
            //if (!string.IsNullOrEmpty(strToken))
            //{
            //string strPost = "{\"app_id\":\"" + strAppId + "\",\"image\":\"" + strBase64 + "\",\"options\":{\"language\": \"zh\"}}";
            //NameValueCollection collection = new NameValueCollection() { { "Authorization", strToken } };
            //result = WebClientSyncExt.GetHtml("https://api.youtu.qq.com/youtu/ocrapi/generalocr", strPost, 5, collection);

            //var url = PostFileResult(byt);
            //string strPost = "{\"url\":\"" + url + "\",\"app_id\":\"" + strAppId + "\"}";
            string strPost =
                string.IsNullOrEmpty(imgUrl) ?
                "{\"image\":\"" + strBase64 + "\",\"app_id\":\"" + strAppId + "\"}" :
                "{\"url\":\"" + imgUrl + "\",\"app_id\":\"" + strAppId + "\"}";
            //NameValueCollection collection = new NameValueCollection() {
            //    { "Authorization", Uri.EscapeDataString(strToken) },
            //    //{ ":authority", "open.youtu.qq.com" },
            //    //{ ":method", "POST" },
            //    //{ ":path", "/youtu/ocrapi/generalocr" },
            //    //{ ":scheme", "https" },
            //};
            result = WebClientSyncExt.GetHtml("https://open.youtu.qq.com/youtu/ocrapi/tableocr", "svc_openid=" + Guid.NewGuid().ToString().Replace("-", ""), strPost, "", ExecTimeOutSeconds);
            //}

            //var dicContent = GetContentFromJson(result);

            //"{\"errorcode\":0,\"errormsg\":\"OK\",\"items\":[{\"itemcoord\":{\"x\":0,\"y\":3,\"width\":192,\"height\":20},\"itemconf\":0.9895762801170349,\"itemstring\":\"请点击下图中所有的靴子火柴\",\"coords\":[],\"words\":[{\"character\":\"请\",\"confidence\":0.9976868629455566},{\"character\":\"点\",\"confidence\":0.9913544058799744},{\"character\":\"击\",\"confidence\":0.9694445133209229},{\"character\":\"下\",\"confidence\":0.9992054104804993},{\"character\":\"图\",\"confidence\":0.9906545281410217},{\"character\":\"中\",\"confidence\":0.9999581575393677},{\"character\":\"所\",\"confidence\":1.0},{\"character\":\"有\",\"confidence\":0.9998621940612793},{\"character\":\"的\",\"confidence\":0.9999899864196777},{\"character\":\"靴\",\"confidence\":0.9252281785011292},{\"character\":\"子\",\"confidence\":0.999544084072113},{\"character\":\"火\",\"confidence\":0.9967591166496277},{\"character\":\"柴\",\"confidence\":0.994803249835968}],\"candword\":[],\"parag\":{\"word_size\":17,\"parag_no\":0},\"coordpoint\":{\"x\":[0,3,191,3,191,22,0,22]},\"wordcoordpoint\":[]}],\"session_id\":\"\",\"angle\":0.0,\"class\":[],\"recognize_warn_code\":[],\"recognize_warn_msg\":[]}"
            return result;
        }

    }
}