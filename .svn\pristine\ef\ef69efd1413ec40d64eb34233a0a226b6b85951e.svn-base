using System;
using System.Text;
using System.Collections.Generic;
using System.Linq;

namespace CommonLib
{
    /// <summary>
    /// OCR处理时间实体，记录OCR处理过程中的各个时间节点
    /// </summary>
    [Serializable]
    public class OcrTimeEntity
    {
        #region 请求阶段时间点

        /// <summary>
        /// 用户发起请求的时间
        /// 赋值节点：客户端发起请求时记录
        /// 传递路径：客户端 → Code.ashx.cs → CodeProcessHelper.SendToProcessPool → CusImageEntity → OcrContent
        /// 关键代码：
        /// - 在Code.ashx.cs中：var clientTicks = BoxUtil.GetInt64FromObject(context.Request.QueryString["ticks"], ServerTime.DateTime.Ticks);
        /// - 在CodeProcessHelper.SendToProcessPool中：img.OcrTime.UserRequestStart = clientTicks;
        /// </summary>
        public long UserStartRequest { get; set; }

        /// <summary>
        /// 服务端接收请求的时间
        /// 赋值节点：服务端接收到用户请求时
        /// 传递路径：Code.ashx.cs → CodeProcessHelper.SendToProcessPool → CusImageEntity → OcrContent
        /// 关键代码：在CodeProcessHelper.SendToProcessPool中：img.OcrTime.ServerReceiveRequest = ServerTime.DateTime.Ticks;
        /// </summary>
        public long ServerReceivedUserRequest { get; set; }

        /// <summary>
        /// 服务端分配任务的时间
        /// 赋值节点：服务端将任务分配给OCR服务时
        /// 传递路径：CodeProcessHelper.WaitOcr → CusImageEntity → OcrContent
        /// 关键代码：在CodeProcessHelper.WaitOcr中：content.OcrTime.ServerAllocateTask = ServerTime.DateTime.Ticks;
        /// </summary>
        public long ServerAlloted { get; set; }

        #endregion

        #region 任务接收阶段时间点

        /// <summary>
        /// 客户端接收到任务的时间
        /// 赋值节点：OCR服务接收到任务时
        /// 传递路径：ProcessNew.ProcessOcrByImg → CusImageEntity → OcrContent
        /// 关键代码：在ProcessNew.ProcessOcrByImg中：img.OcrTime.ClientReceiveTask = dtReceived;
        /// </summary>
        public long OcrServerAccepted { get; set; }

        /// <summary>
        /// 客户端向服务端报告已接收的时间
        /// 赋值节点：OCR服务向服务端报告已接收任务时
        /// 传递路径：ProcessNew.OcrProcessThread → OcrTimeCache → OcrContent
        /// 关键代码：在ProcessNew.OcrProcessThread中：timeEntity.ClientReportReceived = currentTicks;
        /// </summary>
        public long OcrServerReported { get; set; }

        #endregion

        #region OCR处理阶段时间点

        /// <summary>
        /// 客户端准备处理资源的时间
        /// 赋值节点：OCR服务开始处理请求前
        /// 传递路径：CommonProcess.ProcessPerOcr → OcrContent
        /// 关键代码：在CommonProcess.ProcessPerOcr中：processEntity.OcrTime.ClientPrepareStart = ServerTime.DateTime.Ticks;
        /// </summary>
        public long OcrServerBegin { get; set; }

        /// <summary>
        /// 任务进入线程池开始调度的时间
        /// 赋值节点：任务提交到线程池时
        /// 传递路径：CommonProcess.ParallelProcessOcr → Task.Factory.StartNew前 → OcrContent
        /// 关键代码：在ParallelProcessOcr中：processEntity.OcrTime.ThreadPoolQueuedTime = ServerTime.DateTime.Ticks;
        /// </summary>
        public long ThreadPoolQueuedTime { get; set; }
        
        /// <summary>
        /// 线程池线程实际开始执行任务的时间
        /// 赋值节点：线程池线程开始执行任务时
        /// 传递路径：CommonProcess.ParallelProcessOcr → Task.Factory.StartNew执行体内 → OcrContent
        /// 关键代码：在Task执行体内：processEntity.OcrTime.ThreadPoolExecutionStartTime = ServerTime.DateTime.Ticks;
        /// </summary>
        public long ThreadPoolExecutionStartTime { get; set; }

        #region 数据获取子阶段

        /// <summary>
        /// 客户端开始获取原始数据的时间
        /// 赋值节点：OCR服务下载图片数据时
        /// 传递路径：
        ///   - 方式1：CommonProcess异步下载 → CusImageEntity → OcrContent
        ///   - 方式2：BaseRec.GetBase64ByUrl → OcrContent
        /// 关键代码：
        ///   - 在CommonProcess中：processEntity.OcrTime.ClientDownloadStart = ServerTime.DateTime.Ticks;
        ///   - 在BaseRec.GetBase64ByUrl中：content.OcrTime.ClientDownloadStart = ServerTime.DateTime.Ticks;
        /// </summary>
        public long ClientDownloadStart { get; set; }

        /// <summary>
        /// 客户端完成数据获取的时间
        /// 赋值节点：OCR服务完成图片下载时
        /// 传递路径：
        ///   - 方式1：CommonProcess异步下载 → CusImageEntity → OcrContent
        ///   - 方式2：BaseRec.GetBase64ByUrl → OcrContent
        /// 关键代码：
        ///   - 在CommonProcess中：processEntity.OcrTime.ClientDownloadEnd = ServerTime.DateTime.Ticks;
        ///   - 在BaseRec.GetBase64ByUrl中：content.OcrTime.ClientDownloadEnd = ServerTime.DateTime.Ticks;
        /// </summary>
        public long ClientDownloadEnd { get; set; }

        #endregion

        #region OCR引擎子阶段

        /// <summary>
        /// 客户端启动OCR引擎的时间
        /// 赋值节点：OCR引擎开始处理时
        /// 传递路径：BaseRec.GetResult → OcrContent
        /// 关键代码：在BaseRec.GetResult中：content.OcrTime.ClientOcrEngineStart = ServerTime.DateTime.Ticks;
        /// </summary>
        public long ClientOcrEngineStart { get; set; }

        /// <summary>
        /// 客户端OCR引擎完成处理的时间
        /// 赋值节点：OCR引擎完成处理时
        /// 传递路径：BaseRec.GetResult → OcrContent
        /// 关键代码：在BaseRec.GetResult中：content.OcrTime.ClientOcrEngineEnd = ServerTime.DateTime.Ticks;
        /// </summary>
        public long OcrServerEnd { get; set; }

        #endregion

        #region 结果处理子阶段

        /// <summary>
        /// 客户端开始解析结果的时间
        /// 赋值节点：OCR服务解析引擎返回的结果开始时
        /// 传递路径：BaseRec.GetResult → OcrContent
        /// 关键代码：在BaseRec.GetResult中：content.OcrTime.ClientParseResultStart = ServerTime.DateTime.Ticks;
        /// </summary>
        public long ClientParseResultStart { get; set; }

        /// <summary>
        /// 客户端完成结果解析的时间
        /// 赋值节点：OCR服务完成结果解析时
        /// 传递路径：BaseRec.GetResult → OcrContent
        /// 关键代码：在BaseRec.GetResult中：content.OcrTime.ClientParseResultEnd = ServerTime.DateTime.Ticks;
        /// </summary>
        public long ClientParseResultEnd { get; set; }

        #endregion

        #endregion

        #region 结果返回阶段时间点

        /// <summary>
        /// 客户端开始返回结果的时间
        /// 赋值节点：OCR服务开始将结果返回给服务端时
        /// 传递路径：ProcessNew.OcrProcessThread → OcrTimeCache → OcrContent
        /// 关键代码：在ProcessNew.OcrProcessThread中：content.OcrTime.ClientReportResultStart = ServerTime.DateTime.Ticks;
        /// </summary>
        public long OcrServerReportedResult { get; set; }

        /// <summary>
        /// 服务端接收到结果的时间
        /// 赋值节点：服务端接收到OCR服务返回的结果时
        /// 传递路径：OcrProcessHelper.SaveOcrResult → OcrContent
        /// 关键代码：在OcrProcessHelper.SaveOcrResult中：ocrContent.OcrTime.ServerReceiveResult = ServerTime.DateTime.Ticks;
        /// </summary>
        public long ServerReceivedOcrResult { get; set; }

        /// <summary>
        /// 服务端开始处理结果的时间
        /// 赋值节点：服务端处理OCR结果开始时
        /// 传递路径：OcrProcessHelper.SaveOcrResult → OcrContent
        /// 关键代码：在OcrProcessHelper.SaveOcrResult中：ocrContent.OcrTime.ServerProcessResultStart = ServerTime.DateTime.Ticks;
        /// </summary>
        public long ServerProcessResultStart { get; set; }

        /// <summary>
        /// 服务端完成结果处理的时间
        /// 赋值节点：服务端处理OCR结果完成时
        /// 传递路径：OcrProcessHelper.SaveOcrResult → OcrContent
        /// 关键代码：在OcrProcessHelper.SaveOcrResult中：ocrContent.OcrTime.ServerProcessResultEnd = ServerTime.DateTime.Ticks;
        /// </summary>
        public long ServerProcessResultEnd { get; set; }

        /// <summary>
        /// 服务端将结果返回给用户的时间
        /// 赋值节点：服务端将结果返回给用户时
        /// 传递路径：
        ///   - 方式1：OcrProcessHelper.SaveOcrResult → OcrContent
        ///   - 方式2：CodeProcessHelper.SendToProcessPool → OcrContent
        ///   - 方式3：CodeProcessHelper.InitOcrTime → OcrContent
        /// 关键代码：
        ///   - 在OcrProcessHelper.SaveOcrResult中：ocrContent.OcrTime.ServerReturnResult = ServerTime.DateTime.Ticks;
        ///   - 在CodeProcessHelper.SendToProcessPool中：ocr.OcrTime.ServerReturnResult = ServerTime.DateTime.Ticks;
        ///   - 在CodeProcessHelper.InitOcrTime中：ocrContent.OcrTime.ServerReturnResult = ServerTime.DateTime.Ticks;
        /// </summary>
        public long ServerGiveUserResult { get; set; }

        #endregion

        #region 异常处理时间点

        /// <summary>
        /// 出现异常的时间
        /// 赋值节点：发生异常时
        /// 传递路径：BaseRec.GetResult → OcrContent
        /// 关键代码：在BaseRec.GetResult中：content.OcrTime.ErrorOccurTime = ServerTime.DateTime.Ticks;
        /// </summary>
        public long ErrorOccurTime { get; set; }

        /// <summary>
        /// 开始重试的时间
        /// 赋值节点：重试OCR请求开始时
        /// 传递路径：CodeProcessHelper.WaitOcrServerState → CusImageEntity → OcrContent
        /// 关键代码：在CodeProcessHelper.WaitOcrServerState中：img.OcrTime.RetryStartTime = ServerTime.DateTime.Ticks;
        /// </summary>
        public long RetryStartTime { get; set; }

        /// <summary>
        /// 重试结束的时间
        /// 赋值节点：重试OCR请求结束时
        /// 传递路径：CodeProcessHelper.WaitOcrServerState → CusImageEntity → OcrContent
        /// 关键代码：在CodeProcessHelper.WaitOcrServerState中：img.OcrTime.RetryEndTime = ServerTime.DateTime.Ticks;
        /// </summary>
        public long RetryEndTime { get; set; }

        #endregion

        /// <summary>
        /// 生成OCR处理时间分析报告，包含时间线维度明细和耗时节点分析
        /// </summary>
        /// <returns>格式化的分析报告</returns>
        public string GenerateOcrTimeAnalysisReport()
        {
            // 检查基准时间点是否存在，没有则无法生成报告
            if (UserStartRequest <= 0)
                return "时间数据不完整，无法生成分析报告";
            
            // 基准时间：用户请求开始时间，所有时间点都相对于此计算
            long baseTime = UserStartRequest;
            
            // 最小有效耗时阈值(ms)，低于此值的耗时不显示
            const int MIN_TIME_THRESHOLD = 5;
            
            StringBuilder sb = new StringBuilder();
            
            // ========================= 第一部分：OCR处理流程时间线 =========================
            
            sb.AppendLine("一、OCR处理流程时间线");
            sb.AppendLine("===================================");
            sb.AppendLine();
            
            // 时间线节点列表
            List<OcrTimelineNode> timelineNodes = GetTimelineNodes(MIN_TIME_THRESHOLD);
            
            // 按时间戳排序，确保时间线顺序正确
            timelineNodes.Sort((a, b) => a.Timestamp.CompareTo(b.Timestamp));
            
            // 只保留有效耗时节点或关键节点，以简化输出
            var validNodes = timelineNodes.Where(n => 
                n.Duration >= MIN_TIME_THRESHOLD || n.IsKeypoint
            ).ToList();
            
            // 使用表格格式输出时间线
            sb.AppendLine(" 时间点(ms) |    组件    |       事件       |  耗时(ms) | 描述");
            sb.AppendLine("------------|------------|------------------|-----------|------------------");
            
            string currentComponent = "";
            foreach (var node in validNodes)
            {
                // 计算相对于基准时间的时间点
                double timePoint = new TimeSpan(node.Timestamp - baseTime).TotalMilliseconds;
                // 只显示有效耗时，否则显示"-"
                string delayInfo = node.Duration >= MIN_TIME_THRESHOLD ? $"{node.Duration:F0}" : "-";
                
                // 如果组件变化，添加空行提高可读性
                if (currentComponent != node.Component && currentComponent != "")
                {
                    sb.AppendLine();
                }
                currentComponent = node.Component;
                
                // 为关键延迟节点添加特殊标记和格式
                string componentDisplay = node.IsDelayNode 
                    ? $"❗{node.Component}" 
                    : node.Component;
                    
                // 为关键路径上的节点添加星标
                string eventDisplay = node.IsKeypoint 
                    ? $"★ {node.Event}" 
                    : $"   {node.Event}";
                    
                sb.AppendLine($" {timePoint,10:F0} | {componentDisplay,-10} | {eventDisplay,-16} | {delayInfo,9} | {node.Description}");
            }
            
            // ========================= 第二部分：阶段耗时分析 =========================
            
            sb.AppendLine();
            sb.AppendLine("二、OCR处理阶段耗时分析");
            sb.AppendLine("===================================");
            sb.AppendLine();
            
            // 计算并展示各主要阶段的耗时
            Dictionary<string, double> phaseTimings = GetPhaseTimings();
            if (phaseTimings.Count > 0)
            {
                sb.AppendLine("处理阶段               耗时(ms)   占总耗时(%)");
                sb.AppendLine("--------------------- ---------- --------------");
                
                // 直接计算系统总处理时间，替代GetTotalSystemTime方法
                double totalTime = ServerGiveUserResult > 0 && UserStartRequest > 0
                    ? new TimeSpan(ServerGiveUserResult - UserStartRequest).TotalMilliseconds
                    : 0;
                
                foreach (var phase in phaseTimings.OrderByDescending(p => p.Value))
                {
                    double percentage = totalTime > 0 ? (phase.Value / totalTime) * 100 : 0;
                    sb.AppendLine($"{phase.Key,-21} {phase.Value,10:F1} {percentage,14:F1}%");
                }
                
                sb.AppendLine("--------------------- ---------- --------------");
                sb.AppendLine($"{"端到端总耗时",-21} {totalTime,10:F1} {100,14:F1}%");
            }
            else
            {
                sb.AppendLine("无法计算阶段耗时，可能缺少关键时间点");
            }
            
            // ========================= 第三部分：性能瓶颈分析 =========================
            
            sb.AppendLine();
            sb.AppendLine("三、OCR处理性能瓶颈分析");
            sb.AppendLine("===================================");
            sb.AppendLine();
            
            // 计算和分析关键延迟节点
            var delayNodes = validNodes.Where(n => n.IsDelayNode && n.Duration >= MIN_TIME_THRESHOLD).ToList();
            if (delayNodes.Any())
            {
                // 直接计算系统总处理时间，替代GetTotalSystemTime方法
                double totalTime = ServerGiveUserResult > 0 && UserStartRequest > 0
                    ? new TimeSpan(ServerGiveUserResult - UserStartRequest).TotalMilliseconds
                    : 0;
                
                double totalDelayTime = delayNodes.Sum(n => n.Duration);
                double delayPercentage = totalTime > 0 ? (totalDelayTime / totalTime) * 100 : 0;
                
                sb.AppendLine($"发现 {delayNodes.Count} 个性能瓶颈，总耗时 {totalDelayTime:F1}ms，占比 {delayPercentage:F1}%");
                sb.AppendLine();
                sb.AppendLine("时间点(ms)  耗时(ms)  占比(%)  瓶颈事件");
                sb.AppendLine("---------- --------- --------- ------------------------");
                
                foreach (var delay in delayNodes.OrderByDescending(d => d.Duration))
                {
                    // 计算每个延迟节点的时间点和占比
                    double nodeTime = new TimeSpan(delay.Timestamp - baseTime).TotalMilliseconds;
                    double nodePercentage = totalTime > 0 ? (delay.Duration / totalTime) * 100 : 0;
                    
                    // 根据占比确定瓶颈严重程度
                    string severityMark = "";
                    if (nodePercentage >= 30)
                        severityMark = "⚠️⚠️⚠️ ";      // 严重瓶颈
                    else if (nodePercentage >= 20)
                        severityMark = "⚠️⚠️ ";        // 高度瓶颈
                    else if (nodePercentage >= 10)
                        severityMark = "⚠️ ";          // 中度瓶颈
                    
                    sb.AppendLine($"{nodeTime,10:F0} {delay.Duration,9:F1} {nodePercentage,9:F1}% {severityMark}{delay.Event}");
                }
            }
            else
            {
                sb.AppendLine("未发现明显的性能瓶颈");
            }
            
            return sb.ToString();
        }
        
        /// <summary>
        /// 获取OCR处理时间线节点列表
        /// </summary>
        /// <param name="minTimeThreshold">最小有效耗时阈值(ms)</param>
        /// <returns>时间线节点列表</returns>
        private List<OcrTimelineNode> GetTimelineNodes(int minTimeThreshold)
        {
            var nodes = new List<OcrTimelineNode>();
            
            // ===== 用户端节点 =====
            
            // 用户发起请求 - 整个流程的起点
            if (UserStartRequest > 0)
            {
                nodes.Add(new OcrTimelineNode
                {
                    Component = "用户端",
                    Timestamp = UserStartRequest,
                    Event = "发起请求",
                    Description = "用户通过API发起OCR请求",
                    Duration = 0,
                    IsKeypoint = true,
                    IsDelayNode = false
                });
            }
            
            // ===== 服务端节点 =====
            
            // 服务端接收请求
            if (ServerReceivedUserRequest > 0)
            {
                double requestTransferTime = ServerReceivedUserRequest > 0 && UserStartRequest > 0
                    ? new TimeSpan(ServerReceivedUserRequest - UserStartRequest).TotalMilliseconds
                    : 0;
                    
                nodes.Add(new OcrTimelineNode
                {
                    Component = "服务端",
                    Timestamp = ServerReceivedUserRequest,
                    Event = "接收请求",
                    Description = "服务端接收到用户的OCR请求",
                    Duration = requestTransferTime,
                    IsKeypoint = true,
                    IsDelayNode = false
                });
            }
            
            // 服务端分配任务
            if (ServerAlloted > 0)
            {
                double delay = ServerAlloted > 0 && ServerReceivedUserRequest > 0 
                    ? new TimeSpan(ServerAlloted - ServerReceivedUserRequest).TotalMilliseconds 
                    : 0;
                    
                nodes.Add(new OcrTimelineNode
                {
                    Component = "服务端",
                    Timestamp = ServerAlloted,
                    Event = "分配任务",
                    Description = "服务端将任务分配给OCR服务",
                    Duration = delay,
                    IsKeypoint = true,
                    IsDelayNode = false
                });
            }
            
            // ===== OCR服务节点 =====
            
            // 客户端接收任务
            if (OcrServerAccepted > 0)
            {
                // 直接计算任务分配耗时，替代GetTaskAllocationTime方法
                double taskAllocationTime = OcrServerAccepted > 0 && ServerAlloted > 0
                    ? new TimeSpan(OcrServerAccepted - ServerAlloted).TotalMilliseconds
                    : 0;
                
                nodes.Add(new OcrTimelineNode
                {
                    Component = "OCR服务",
                    Timestamp = OcrServerAccepted,
                    Event = "接收任务",
                    Description = "客户端接收到服务端分配的OCR任务",
                    Duration = taskAllocationTime,
                    IsKeypoint = true,
                    IsDelayNode = false
                });
            }
            
            // 客户端确认接收
            if (OcrServerReported > 0 && OcrServerReported > OcrServerAccepted)
            {
                double delay = new TimeSpan(OcrServerReported - OcrServerAccepted).TotalMilliseconds;
                
                if (delay >= minTimeThreshold)
                {
                    nodes.Add(new OcrTimelineNode
                    {
                        Component = "OCR服务",
                        Timestamp = OcrServerReported,
                        Event = "确认接收任务",
                        Description = "客户端向服务端确认已收到任务",
                        Duration = delay,
                        IsKeypoint = false,
                        IsDelayNode = false
                    });
                }
            }
            
            // 客户端开始准备处理
            if (OcrServerBegin > 0)
            {
                double delay = OcrServerBegin > 0 && OcrServerAccepted > 0
                    ? new TimeSpan(OcrServerBegin - OcrServerAccepted).TotalMilliseconds
                    : 0;
                    
                nodes.Add(new OcrTimelineNode
                {
                    Component = "OCR服务",
                    Timestamp = OcrServerBegin,
                    Event = "开始准备处理",
                    Description = "AddToProcess开始执行",
                    Duration = delay,
                    IsKeypoint = true,
                    IsDelayNode = delay > minTimeThreshold
                });
            }
            
            // 任务提交线程池
            if (ThreadPoolQueuedTime > 0)
            {
                double delay = ThreadPoolQueuedTime > 0 && OcrServerBegin > 0
                    ? new TimeSpan(ThreadPoolQueuedTime - OcrServerBegin).TotalMilliseconds
                    : 0;
                    
                if (delay >= minTimeThreshold)
                {
                    nodes.Add(new OcrTimelineNode
                    {
                        Component = "OCR服务",
                        Timestamp = ThreadPoolQueuedTime,
                        Event = "任务提交线程池",
                        Description = "OCR任务提交到线程池队列",
                        Duration = delay,
                        IsKeypoint = true,
                        IsDelayNode = delay > minTimeThreshold * 2
                    });
                }
            }
            
            // 线程池开始执行
            if (ThreadPoolExecutionStartTime > 0)
            {
                // 直接计算线程池调度延迟，替代GetThreadSchedulingDelay方法
                double threadSchedulingDelay = ThreadPoolExecutionStartTime > 0 && ThreadPoolQueuedTime > 0
                    ? new TimeSpan(ThreadPoolExecutionStartTime - ThreadPoolQueuedTime).TotalMilliseconds
                    : 0;
                
                if (threadSchedulingDelay >= minTimeThreshold)
                {
                    nodes.Add(new OcrTimelineNode
                    {
                        Component = "OCR服务",
                        Timestamp = ThreadPoolExecutionStartTime,
                        Event = "线程池开始执行",
                        Description = "线程池线程开始执行任务",
                        Duration = threadSchedulingDelay,
                        IsKeypoint = true,
                        IsDelayNode = threadSchedulingDelay > minTimeThreshold * 2
                    });
                }
            }
            
            // ===== OCR引擎节点 =====
            
            // OCR引擎启动/开始获取HTML
            if (ClientOcrEngineStart > 0)
            {
                double delay = ClientOcrEngineStart > 0 && ThreadPoolExecutionStartTime > 0
                    ? new TimeSpan(ClientOcrEngineStart - ThreadPoolExecutionStartTime).TotalMilliseconds
                    : 0;
                    
                nodes.Add(new OcrTimelineNode
                {
                    Component = "OCR引擎",
                    Timestamp = ClientOcrEngineStart,
                    Event = "引擎启动/获取HTML",
                    Description = "OCR引擎开始初始化并获取HTML",
                    Duration = delay,
                    IsKeypoint = true,
                    IsDelayNode = delay > minTimeThreshold * 2
                });
            }
            
            // 获取HTML完毕/开始解析
            if (ClientParseResultStart > 0)
            {
                double delay = ClientParseResultStart > 0 && ClientOcrEngineStart > 0
                    ? new TimeSpan(ClientParseResultStart - ClientOcrEngineStart).TotalMilliseconds
                    : 0;
                    
                if (delay >= minTimeThreshold)
                {
                    nodes.Add(new OcrTimelineNode
                    {
                        Component = "OCR引擎",
                        Timestamp = ClientParseResultStart,
                        Event = "HTML完毕/开始解析",
                        Description = "引擎获取HTML完成并开始解析",
                        Duration = delay,
                        IsKeypoint = true,
                        IsDelayNode = delay > minTimeThreshold * 3
                    });
                }
            }
            
            // 完成HTML解析
            if (ClientParseResultEnd > 0)
            {
                // 直接计算结果解析耗时，替代GetResultParsingTime方法
                double resultParsingTime = ClientParseResultEnd > 0 && ClientParseResultStart > 0
                    ? new TimeSpan(ClientParseResultEnd - ClientParseResultStart).TotalMilliseconds
                    : 0;
                
                if (resultParsingTime >= minTimeThreshold)
                {
                    nodes.Add(new OcrTimelineNode
                    {
                        Component = "OCR引擎",
                        Timestamp = ClientParseResultEnd,
                        Event = "完成HTML解析",
                        Description = "完成OCR结果的HTML解析",
                        Duration = resultParsingTime,
                        IsKeypoint = true,
                        IsDelayNode = false
                    });
                }
            }
            
            // OCR处理完成
            if (OcrServerEnd > 0)
            {
                double delay = OcrServerEnd > 0 && ClientParseResultEnd > 0
                    ? new TimeSpan(OcrServerEnd - ClientParseResultEnd).TotalMilliseconds
                    : 0;
                    
                if (delay >= minTimeThreshold)
                {
                    nodes.Add(new OcrTimelineNode
                    {
                        Component = "OCR引擎",
                        Timestamp = OcrServerEnd,
                        Event = "处理完成",
                        Description = "OCR引擎完成所有处理",
                        Duration = delay,
                        IsKeypoint = true,
                        IsDelayNode = false
                    });
                }
            }
            
            // ===== 结果返回节点 =====
            
            // 开始返回结果
            if (OcrServerReportedResult > 0)
            {
                double delay = OcrServerReportedResult > 0 && OcrServerEnd > 0
                    ? new TimeSpan(OcrServerReportedResult - OcrServerEnd).TotalMilliseconds
                    : 0;
                    
                if (delay >= minTimeThreshold)
                {
                    nodes.Add(new OcrTimelineNode
                    {
                        Component = "OCR服务",
                        Timestamp = OcrServerReportedResult,
                        Event = "开始返回结果",
                        Description = "客户端开始将处理结果返回给服务端",
                        Duration = delay,
                        IsKeypoint = true,
                        IsDelayNode = false
                    });
                }
            }
            
            // 服务端接收OCR结果
            if (ServerReceivedOcrResult > 0)
            {
                // 直接计算结果传输耗时，替代GetResultTransferTime方法
                double resultTransferTime = ServerReceivedOcrResult > 0 && OcrServerReportedResult > 0
                    ? new TimeSpan(ServerReceivedOcrResult - OcrServerReportedResult).TotalMilliseconds
                    : 0;
                
                nodes.Add(new OcrTimelineNode
                {
                    Component = "服务端",
                    Timestamp = ServerReceivedOcrResult,
                    Event = "接收OCR结果",
                    Description = "服务端接收到OCR服务返回的结果",
                    Duration = resultTransferTime,
                    IsKeypoint = true,
                    IsDelayNode = false
                });
            }
            
            // 服务端开始处理结果
            if (ServerProcessResultStart > 0)
            {
                double delay = ServerProcessResultStart > 0 && ServerReceivedOcrResult > 0
                    ? new TimeSpan(ServerProcessResultStart - ServerReceivedOcrResult).TotalMilliseconds
                    : 0;
                    
                if (delay >= minTimeThreshold)
                {
                    nodes.Add(new OcrTimelineNode
                    {
                        Component = "服务端",
                        Timestamp = ServerProcessResultStart,
                        Event = "开始处理结果",
                        Description = "服务端开始处理OCR结果",
                        Duration = delay,
                        IsKeypoint = false,
                        IsDelayNode = false
                    });
                }
            }
            
            // 服务端完成处理结果
            if (ServerProcessResultEnd > 0)
            {
                double delay = ServerProcessResultEnd > 0 && ServerProcessResultStart > 0
                    ? new TimeSpan(ServerProcessResultEnd - ServerProcessResultStart).TotalMilliseconds
                    : 0;
                    
                if (delay >= minTimeThreshold)
                {
                    nodes.Add(new OcrTimelineNode
                    {
                        Component = "服务端",
                        Timestamp = ServerProcessResultEnd,
                        Event = "完成处理结果",
                        Description = "服务端完成OCR结果处理",
                        Duration = delay,
                        IsKeypoint = false,
                        IsDelayNode = false
                    });
                }
            }
            
            // 服务端返回结果给用户
            if (ServerGiveUserResult > 0)
            {
                double delay = ServerGiveUserResult > 0 && ServerProcessResultEnd > 0
                    ? new TimeSpan(ServerGiveUserResult - ServerProcessResultEnd).TotalMilliseconds
                    : 0;
                    
                nodes.Add(new OcrTimelineNode
                {
                    Component = "服务端",
                    Timestamp = ServerGiveUserResult,
                    Event = "返回结果给用户",
                    Description = "服务端将处理完成的结果返回给用户",
                    Duration = delay,
                    IsKeypoint = true,
                    IsDelayNode = false
                });
            }
            
            return nodes;
        }
        
        /// <summary>
        /// 获取OCR处理各阶段耗时字典
        /// </summary>
        /// <returns>各阶段耗时字典</returns>
        private Dictionary<string, double> GetPhaseTimings()
        {
            var phaseTimings = new Dictionary<string, double>();
            
            // 1. 请求阶段：从用户发起请求到服务端接收请求
            double requestPhaseTime = ServerReceivedUserRequest > 0 && UserStartRequest > 0
                ? new TimeSpan(ServerReceivedUserRequest - UserStartRequest).TotalMilliseconds
                : 0;
            if (requestPhaseTime > 0)
                phaseTimings.Add("1. 请求阶段", requestPhaseTime);
            
            // 2. 服务端分配阶段：从服务端接收请求到OCR服务接收任务
            double allocationPhaseTime = OcrServerAccepted > 0 && ServerReceivedUserRequest > 0
                ? new TimeSpan(OcrServerAccepted - ServerReceivedUserRequest).TotalMilliseconds
                : 0;
            if (allocationPhaseTime > 0)
                phaseTimings.Add("2. 任务分配阶段", allocationPhaseTime);
            
            // 3. OCR处理准备阶段：从客户端接收任务到线程池执行
            double prepPhaseTime = ThreadPoolExecutionStartTime > 0 && OcrServerAccepted > 0
                ? new TimeSpan(ThreadPoolExecutionStartTime - OcrServerAccepted).TotalMilliseconds
                : 0;
            if (prepPhaseTime > 0)
                phaseTimings.Add("3. 处理准备阶段", prepPhaseTime);
            
            // 4. OCR处理阶段：从线程池执行到OCR处理完成
            double ocrPhaseTime = OcrServerEnd > 0 && ThreadPoolExecutionStartTime > 0
                ? new TimeSpan(OcrServerEnd - ThreadPoolExecutionStartTime).TotalMilliseconds
                : 0;
            if (ocrPhaseTime > 0)
                phaseTimings.Add("4. OCR处理阶段", ocrPhaseTime);
            
            // 5. 结果返回阶段：从OCR处理完成到返回结果给用户
            double resultPhaseTime = ServerGiveUserResult > 0 && OcrServerEnd > 0
                ? new TimeSpan(ServerGiveUserResult - OcrServerEnd).TotalMilliseconds
                : 0;
            if (resultPhaseTime > 0)
                phaseTimings.Add("5. 结果返回阶段", resultPhaseTime);
            
            return phaseTimings;
        }
        
        /// <summary>
        /// 时间线节点类，用于表示OCR处理流程中的各个时间点
        /// </summary>
        private class OcrTimelineNode
        {
            /// <summary>
            /// 组件名称：用户端、服务端、OCR服务、OCR引擎等
            /// </summary>
            public string Component { get; set; }
            
            /// <summary>
            /// 时间戳
            /// </summary>
            public long Timestamp { get; set; }
            
            /// <summary>
            /// 事件名称
            /// </summary>
            public string Event { get; set; }
            
            /// <summary>
            /// 事件描述
            /// </summary>
            public string Description { get; set; }
            
            /// <summary>
            /// 耗时（从上一节点到当前节点）
            /// </summary>
            public double Duration { get; set; }
            
            /// <summary>
            /// 是否是关键节点
            /// </summary>
            public bool IsKeypoint { get; set; }
            
            /// <summary>
            /// 是否是延迟节点（性能瓶颈）
            /// </summary>
            public bool IsDelayNode { get; set; }
        }
    }
} 