﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="12.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{2D245CFB-BF26-4B49-8E5C-1A25FA8DCA44}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>HanZiOcr</RootNamespace>
    <AssemblyName>HanZiOcr</AssemblyName>
    <TargetFrameworkVersion>v4.7.2</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <TargetFrameworkProfile />
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <Prefer32Bit>false</Prefer32Bit>
    <PlatformTarget>AnyCPU</PlatformTarget>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="Newtonsoft.Json, Version=13.0.0.0, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed, processorArchitecture=MSIL">
      <HintPath>..\packages\Newtonsoft.Json.13.0.1\lib\net45\Newtonsoft.Json.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Web" />
    <Reference Include="System.Web.Extensions" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="AWSWordSliceApiRec.cs" />
    <Compile Include="Cloud10086DocRec.cs" />
    <Compile Include="Cloud10086Rec.cs" />
    <Compile Include="HeHeNormalRec.cs" />
    <Compile Include="HeHeMutilPageRec.cs" />
    <Compile Include="SuDaOfficeRec.cs" />
    <Compile Include="AWSHandWritApiRec.cs" />
    <Compile Include="GoogleDocApiRec.cs" />
    <Compile Include="GoogleTextApiRec.cs" />
    <Compile Include="ByteDanceRec.cs" />
    <Compile Include="DaGuanShouXieRec.cs" />
    <Compile Include="DuGuangGaoJingRec.cs" />
    <Compile Include="DuGuangDianShangRec.cs" />
    <Compile Include="DuGuangShouXieRec.cs" />
    <Compile Include="FaceAIV2Rec.cs" />
    <Compile Include="AzureRec.cs" />
    <Compile Include="HanWangLiteTextRec.cs" />
    <Compile Include="QQToolRec.cs" />
    <Compile Include="YouDaoDocRec.cs" />
    <Compile Include="ShengShiRec.cs" />
    <Compile Include="TencentWanXiangAPIRec.cs" />
    <Compile Include="TencentAPIBasicRec.cs" />
    <Compile Include="TencentAPIAccurateRec.cs" />
    <Compile Include="HanWangFullTextRec.cs" />
    <Compile Include="HeHeLiteRec.cs" />
    <Compile Include="HeHeDocRec.cs" />
    <Compile Include="HeHeApiRec.cs" />
    <Compile Include="HeHeChinaEnglishRec.cs" />
    <Compile Include="DaGuanAIRec.cs" />
    <Compile Include="HaoWeiLaiEduRec.cs" />
    <Compile Include="XunFeiDevPageRec.cs" />
    <Compile Include="XunFeiDevRec.cs" />
    <Compile Include="XunFeiPageRec.cs" />
    <Compile Include="YandexRec.cs" />
    <Compile Include="SouGouBroswerRec.cs" />
    <Compile Include="YingShiRec.cs" />
    <Compile Include="HeHeRec.cs" />
    <Compile Include="HaiMaRec.cs" />
    <Compile Include="FeiJiangRec.cs" />
    <Compile Include="CloudWalkRec.cs" />
    <Compile Include="DuGuangNormalRec.cs" />
    <Compile Include="XunFeiXiaoZhiRec.cs" />
    <Compile Include="YiDaoRec.cs" />
    <Compile Include="XueErSiAPIRec.cs" />
    <Compile Include="AICloudApiRec.cs" />
    <Compile Include="FlittoAppRec.cs" />
    <Compile Include="NewOCRAPIRec.cs" />
    <Compile Include="OuLuRec.cs" />
    <Compile Include="NewOCRRec.cs" />
    <Compile Include="SouGouShouJiRec.cs" />
    <Compile Include="ConstHelper.cs" />
    <Compile Include="VivoRec.cs" />
    <Compile Include="AICloudRec.cs" />
    <Compile Include="FaceAIRec.cs" />
    <Compile Include="BaseOcrRec.cs" />
    <Compile Include="WeiXinLiteAppRec.cs" />
    <Compile Include="BaiDuAILiteAppRec.cs" />
    <Compile Include="XunJiePDFRec.cs" />
    <Compile Include="HanZiOcrType.cs" />
    <Compile Include="XunFeiLiteAppRec.cs" />
    <Compile Include="YouDaoRec.cs" />
    <Compile Include="XunFeiAPIRec.cs" />
    <Compile Include="YouDaoAPIRec.cs" />
    <Compile Include="BaiDuAPIRec.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\CommonLib\CommonLib.csproj">
      <Project>{fc03a7d4-8ef2-4dea-a15a-c099eb77b0eb}</Project>
      <Name>CommonLib</Name>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup>
    <None Include="app.config" />
    <None Include="packages.config" />
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>