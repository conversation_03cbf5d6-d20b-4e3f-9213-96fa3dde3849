﻿using CommonLib.Events;
using Newtonsoft.Json;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Collections.Specialized;
using System.IO;
using System.Linq;
using System.Net;
using System.Text;
using System.Threading;

namespace CommonLib
{
    public abstract class BaseRec
    {
        public event EventHandler<OnErrorEventArgs> OnError;
        public event EventHandler<OnStartedEventArgs> OnStart;
        public event EventHandler<OnCompletedEventArgs> OnCompleted;
        public delegate void BeginInvokeDelegate(OnReportEventArgs eventArgs);

        /// <summary>
        /// 所属DLL类型
        /// </summary>
        public virtual OcrDllType DllType { get; set; }

        public OcrGroupType OcrGroup { get; set; }

        public int OcrType { get; set; }

        /// <summary>
        /// 处理单元描述
        /// </summary>
        protected KeyValuePair<string, string> EngineType;

        public string EngineDesc
        {
            get
            {
                // return string.IsNullOrEmpty(EngineType.Key) ? "默认" : string.Format("{0}", EngineType.Key, EngineType.Value);
                return string.IsNullOrEmpty(EngineType.Key) ? "默认" : $"{EngineType.Key}";
            }
        }

        public EnableState State
        {
            get { return state; }
            set
            {
                if (!Equals(state, value))
                {
                    BaseRecHelper.ClearCache();
                }
                state = value;
            }
        }

        #region 竖排识别

        /// <summary>
        /// 是否支持竖排识别
        /// </summary>
        public bool IsSupportVertical { get; set; }

        /// <summary>
        /// 是否支持Url图片识别
        /// </summary>
        public bool IsSupportUrlOcr { get; set; }

        /// <summary>
        /// 是否支持翻译
        /// </summary>
        public bool IsSupportTrans { get; set; }

        /// <summary>
        /// 文字横排方向
        /// </summary>
        public bool IsFromLeftToRight { get; set; } = true;

        /// <summary>
        /// 文字竖排方向
        /// </summary>
        public bool IsFromTopToDown { get; set; } = true;

        /// <summary>
        /// 自动判断文本方向
        /// </summary>
        public bool IsAutoDirectionDetector { get; set; }

        /// <summary>
        /// 是否多页结果返回，需要合并每一页结果
        /// </summary>
        public bool IsMultiPage { get; set; }

        /// <summary>
        /// 是否自动获取页码
        /// </summary>
        public bool IsAutoPageIndex { get; set; }

        /// <summary>
        /// 坐标是否为比例，需要转换
        /// </summary>
        public bool IsPercentSize { get; set; }

        #endregion

        #region 竖排文字识别
        protected bool IsDesrializeVerticalByLocation { get; set; }

        protected List<object> LstVerticalLocation { get; set; }

        protected Dictionary<string, string> DicDeserializeVerticalJson { get; set; }

        #endregion

        #region 文件下载

        /// <summary>
        /// 指定返回文件类型
        /// </summary>
        public OcrFileType ResultFileType { get; set; }

        #endregion

        #region 支持处理的文件类型及语言，大小相关属性

        public List<string> AllowUploadFileTypes { get; set; } = new List<string>() { "bmp", "jpg", "png", "jpeg", "gif" };

        public Dictionary<TransLanguageTypeEnum, string> TransLanguageDic { get; set; } = new Dictionary<TransLanguageTypeEnum, string>();

        public int FileSizeLimit { get; set; }

        #endregion

        #region JSON字符串处理

        /// <summary>
        /// 结果是否为字符串数组
        /// </summary>
        protected bool IsJsonArrayString = false;

        /// <summary>
        /// 字符与Location是单独的，需要单独处理
        /// </summary>
        protected bool IsJsonArrayStringWithLocation = false;

        /// <summary>
        /// 配合JsonArrayStringWithLocation，取货位信息
        /// </summary>
        protected List<object> LstJsonLocationProcessArray;

        /// <summary>
        /// 结果是否为JSON字符串
        /// </summary>
        protected bool IsJsonResult = true;

        /// <summary>
        /// 预处理JSON
        /// </summary>
        protected List<object> LstJsonPreProcessArray;

        /// <summary>
        /// 预处理之后的JSON处理（非结果处理）
        /// </summary>
        protected List<object> LstJsonNextProcessArray;

        /// <summary>
        /// 带分页的后续JSON处理（非结果处理）
        /// </summary>
        protected List<object> LstJsonLastProcessArray;

        /// <summary>
        /// 是否以数组方式处理JSON结果
        /// </summary>
        protected bool IsProcessJsonResultByArray = true;

        /// <summary>
        /// IsJsonArrayStringWithLocation为true时
        /// 假如结果集为json对象，根据此字段取结果字符串的值
        /// </summary>
        protected string StrJsonArrayWordSpilt;

        /// <summary>
        /// JSON结果处理
        /// </summary>
        protected List<object> LstJsonResultProcessArray;

        /// <summary>
        /// 最终结果分割字符
        /// </summary>
        protected string StrResultJsonSpilt;

        /// <summary>
        /// 最终结果分割字符
        /// </summary>
        protected string StrResultTransJsonSpilt;

        #endregion

        #region 并发限制

        /// <summary>
        /// 触发被屏蔽字符串
        /// </summary>
        protected Dictionary<string, List<string>> LstForbidArray;

        /// <summary>
        /// 触发被降级字符串
        /// </summary>
        protected Dictionary<string, List<string>> LstReduceWeightArray;

        /// <summary>
        /// 空格匹配
        /// </summary>
        protected string StrSpaceKey;

        /// <summary>
        /// 空格匹配
        /// </summary>
        protected string StrSpaceValue;

        /// <summary>
        /// 换行匹配
        /// </summary>
        protected string StrLineKey;

        /// <summary>
        /// 换行匹配
        /// </summary>
        protected string StrLineValue;

        /// <summary>
        /// 单元格拼接字符串
        /// </summary>
        protected string StrContactCell = string.Empty;

        public int EmptyTimes = 0;

        /// <summary>
        /// 请求超时时间
        /// </summary>
        protected int ExecTimeOutSeconds = 20;

        public long MaxExecPerTime = int.MaxValue;

        public long MaxExecPerTimeBack;

        public long NowExecTimes = 0;

        public long TotalExecTimes = 0;

        public long ForbidTimes = 0;
        private EnableState state;

        public bool IsEnable()
        {
            return State == EnableState.启用 && (MaxExecPerTime == 0 || NowExecTimes < MaxExecPerTime);
        }

        #endregion

        #region 结果处理

        public virtual ResutypeEnum ResultType { get; set; }

        public ResultEntity GetFileResult(string autoText)
        {
            return new ResultEntity()
            {
                files = new List<DownLoadInfo>(),
                autoText = autoText,
                resultType = ResultType
            };
        }
        #endregion

        protected abstract string GetHtml(OcrContent content);

        //public virtual string GetHtml(string strBase64)
        //{
        //    return string.Empty;
        //}

        public abstract string GetOcrTypeName();

        public abstract int GetOcrType();

        public virtual string GetHtmlByUrl(OcrContent content)
        {
            return string.Empty;
        }

        private string GetBase64ByUrl(OcrContent content)
        {
            var result = string.Empty;
            if (content.OcrTime == null)
            {
                content.OcrTime = new OcrTimeEntity();
            }
            content.OcrTime.ClientDownloadStart = ServerTime.DateTime.Ticks;
            try
            {
                using (var webClient = new CNNWebClient())
                {
                    result = Convert.ToBase64String(webClient.DownloadData(content.url));
                }
            }
            catch { }
            content.OcrTime.ClientDownloadEnd = ServerTime.DateTime.Ticks;
            return result;
        }

        public virtual void InitLanguage(OcrContent content, ref string from, ref string to)
        {
            var defaultFrom = content.from.Equals(TransLanguageTypeEnum.自动)
                ? (content.to.Equals(TransLanguageTypeEnum.中文) ? TransLanguageTypeEnum.英文 : TransLanguageTypeEnum.中文)
                : TransLanguageTypeEnum.中文;
            from = GetLanguage(content.from, true, defaultFrom);
            if (!string.IsNullOrEmpty(from))
            {
                var defaultTo = from.Equals(TransLanguageDic[TransLanguageTypeEnum.中文]) ? TransLanguageTypeEnum.英文 : TransLanguageTypeEnum.中文;
                if (Equals(content.to, TransLanguageTypeEnum.自动))
                {
                    content.to = defaultTo;
                }
                to = GetLanguage(content.to, false, defaultTo);
            }
        }

        private string GetLanguage(TransLanguageTypeEnum from, bool isAutoFirst, TransLanguageTypeEnum defaultType)
        {
            var result = string.Empty;
            if (TransLanguageDic.TryGetValue(from, out result))
            {
                return result;
            }
            if (string.IsNullOrEmpty(result))
            {
                if (isAutoFirst && TransLanguageDic.TryGetValue(TransLanguageTypeEnum.自动, out result))
                {
                    return result;
                }
            }
            if (string.IsNullOrEmpty(result))
            {
                if (TransLanguageDic.TryGetValue(defaultType, out result))
                {
                    return result;
                }
                else if (TransLanguageDic.Count > 0)
                {
                    //随机取一个
                    result = TransLanguageDic.First().Value;
                }
            }
            return result;
        }

        // 一次性替换所有需要替换的字符串
        readonly string[] replaceStrings_downLeft = { "downLeft", "downRight", "topLeft", "topRight",
                                   "y", "x", "[", "]", "{", "}", "\"", ":", "\r", "\t", "\n", " " };
        // 为其他替换操作也创建静态数组
        readonly string[] replaceStrings_location = { "left_bottom", "right_top", "left_top", "vertices",
                           "y1", "x1", "y2", "x2", "y3", "x3", "y4", "x4",
                           "y", "x", "Y", "X", "[", "]", "{", "}", "\"", ":", "\r", "\t", "\n", " " };

        protected virtual LocationInfo GetLocationByStr(string locationInfo)
        {
            LocationInfo location = null;
            var sb = CommonPool.StringBuilderPool.Get();
            try
            {
                if (locationInfo.IndexOf("\"downLeft\"", StringComparison.Ordinal) >= 0)
                {
                    //vivo
                    sb.Append(locationInfo);
                    foreach (var str in replaceStrings_downLeft)
                    {
                        sb.Replace(str, "");
                    }
                    locationInfo = sb.ToString().Trim();

                    var spilt = locationInfo.Split(new[] { "," }, StringSplitOptions.RemoveEmptyEntries);
                    if (spilt.Length == 8)
                    {
                        //左上角坐标(0,1)，右上角坐标(2,3)，右下角坐标(4,5)，左下角坐标(6,7)
                        //左下角坐标(0,1)，右下角坐标(2,3)，右上角坐标(4,5)，左上角坐标(6,7)
                        location = new LocationInfo
                        {
                            left = Math.Min(BoxUtil.GetDoubleFromObject(spilt[6]), BoxUtil.GetDoubleFromObject(spilt[2])),
                            top = Math.Min(BoxUtil.GetDoubleFromObject(spilt[5]), BoxUtil.GetDoubleFromObject(spilt[7])),
                            width = Math.Max(BoxUtil.GetDoubleFromObject(spilt[4]) - BoxUtil.GetDoubleFromObject(spilt[6]),
                                BoxUtil.GetDoubleFromObject(spilt[2]) - BoxUtil.GetDoubleFromObject(spilt[0])),
                            height = Math.Max(BoxUtil.GetDoubleFromObject(spilt[1]) - BoxUtil.GetDoubleFromObject(spilt[7]),
                                BoxUtil.GetDoubleFromObject(spilt[3]) - BoxUtil.GetDoubleFromObject(spilt[5])),
                        };
                    }
                }
                else if (locationInfo.IndexOf("\"y\"", StringComparison.Ordinal) >= 0 &&
                         locationInfo.IndexOf("\"left_top\"", StringComparison.Ordinal) < 0 &&
                         locationInfo.IndexOf("\"vertices\"", StringComparison.Ordinal) < 0)
                {
                    var lstLocations = JsonConvert.DeserializeObject<List<VLocationInfo2>>(locationInfo, new JsonSerializerSettings());
                    location = GetLocationInfoByStr(lstLocations);
                }
                else
                {
                    var isLeftBottom = locationInfo.IndexOf("left_bottom", StringComparison.Ordinal) >= 0 &&
                         locationInfo.IndexOf("left_top", StringComparison.Ordinal) < 0;
                    var isLeftTop = locationInfo.IndexOf("left_top", StringComparison.Ordinal) >= 0 &&
                         locationInfo.IndexOf("left_bottom", StringComparison.Ordinal) < 0;

                    sb.Append(locationInfo);
                    foreach (var str in replaceStrings_location)
                    {
                        sb.Replace(str, "");
                    }
                    locationInfo = sb.ToString().Trim();
                    location = GetLocationInfoByStr(locationInfo, isLeftBottom, isLeftTop);
                }
            }
            catch (Exception oe)
            {
            }
            finally
            {
                CommonPool.StringBuilderPool.Return(sb);
            }
            return location;
        }

        protected LocationInfo GetLocationInfoByStr(List<VLocationInfo2> lstLocations)
        {
            LocationInfo location = null;
            if (lstLocations?.Count == 4)
            {
                var minLeft = lstLocations.Select(p => p.x).Min();
                var minTop = lstLocations.Select(p => p.y).Min();
                var maxLeft = lstLocations.Select(p => p.x).Max();
                var maxTop = lstLocations.Select(p => p.y).Max();
                location = new LocationInfo()
                {
                    left = minLeft,
                    top = minTop,
                    width = maxLeft - minLeft,
                    height = maxTop - minTop,
                };
            }
            return location;
        }

        protected LocationInfo GetLocationInfoByStr(string strTmp, bool isLeftBottom, bool isLeftTop)
        {
            LocationInfo location = null;
            if (!string.IsNullOrEmpty(strTmp))
            {
                var spilt = strTmp.Split(new[] { "," }, StringSplitOptions.RemoveEmptyEntries);
                if (spilt.Length == 8)
                {
                    double val0 = BoxUtil.GetDoubleFromObject(spilt[0]);
                    double val1 = BoxUtil.GetDoubleFromObject(spilt[1]);
                    double val2 = BoxUtil.GetDoubleFromObject(spilt[2]);
                    double val3 = BoxUtil.GetDoubleFromObject(spilt[3]);
                    double val4 = BoxUtil.GetDoubleFromObject(spilt[4]);
                    double val5 = BoxUtil.GetDoubleFromObject(spilt[5]);
                    double val6 = BoxUtil.GetDoubleFromObject(spilt[6]);
                    double val7 = BoxUtil.GetDoubleFromObject(spilt[7]);

                    //左上角坐标(0,1)，右上角坐标(2,3)，右下角坐标(4,5)，左下角坐标(6,7)
                    location = new LocationInfo
                    {
                        left = Math.Min(val0, val6),
                        top = Math.Min(val1, val3),
                        width = Math.Max(val2 - val0, val4 - val6),
                        height = Math.Max(val7 - val1, val5 - val3),
                    };
                    if (location.width < 0 && location.height < 0)
                    {
                        //右下角坐标(0,1)，左下角坐标(2,3)，左上角坐标(4,5)，右上角坐标(6,7)
                        location = new LocationInfo
                        {
                            left = Math.Min(val4, val2),
                            top = Math.Min(val5, val7),
                            width = Math.Max(val6 - val4, val0 - val2),
                            height = Math.Max(val3 - val5, val1 - val7),
                        };
                    }
                    else
                    {

                    }
                }
                else if (spilt.Length == 4)
                {
                    double val0 = BoxUtil.GetDoubleFromObject(spilt[0]);
                    double val1 = BoxUtil.GetDoubleFromObject(spilt[1]);
                    double val2 = BoxUtil.GetDoubleFromObject(spilt[2]);
                    double val3 = BoxUtil.GetDoubleFromObject(spilt[3]);
                    if (isLeftTop)
                    {
                        location = new LocationInfo
                        {
                            left = val0,
                            top = val1,
                            width = val2 - val0,
                            height = val3 - val1,
                        };
                    }
                    else if (isLeftBottom)
                    {
                        location = new LocationInfo()
                        {
                            left = val0,
                            top = val3,
                            width = val2 - val0,
                            height = val1 - val3,
                        };
                    }
                    else
                    {
                        location = new LocationInfo()
                        {
                            left = val0,
                            top = val1,
                            width = val2,
                            height = val3,
                        };
                    }
                }
            }

            return location;
        }

        public virtual ResultEntity GetProcessTextTest(string html)
        {
            return GetProcessText(new OcrContent(), html);
        }

        protected virtual ResultEntity GetProcessText(OcrContent content, string html)
        {
            if (IsPercentSize && !string.IsNullOrEmpty(content.strBase64))
            {
                try
                {
                    using (var stream = new MemoryStream(Convert.FromBase64String(content.strBase64)))
                    using (var image = System.Drawing.Image.FromStream(stream))
                    {
                        content.Size = image.Size;
                    }
                }
                catch { }
            }
            SetProcessArray(html);
            var ocrProcessConfig = new OcrProcessConfig()
            {
                IsProcessJsonResultByArray = IsProcessJsonResultByArray,
                JsonPreProcessArray = LstJsonPreProcessArray,
                JsonNextProcessArray = LstJsonNextProcessArray,
                JsonLastProcessArray = LstJsonLastProcessArray,
                JsonLocationProcessArray = LstJsonLocationProcessArray,
                ResultJsonSpilt = StrResultJsonSpilt,
                ResultTransJsonSpilt = StrResultTransJsonSpilt,
                SpaceKey = StrSpaceKey,
                SpaceValue = StrSpaceValue,
                LineKey = StrLineKey,
                LineValue = StrLineValue,
                ContactCell = StrContactCell,
                IsMutliPage = IsMultiPage,
                IsAutoPageIndex = IsAutoPageIndex,
                IsSupportVertical = IsSupportVertical,
                IsJsonArrayString = IsJsonArrayString,
                IsJsonArrayStringWithLocation = IsJsonArrayStringWithLocation,
                JsonArrayWordSpilt = StrJsonArrayWordSpilt,
                IsFromLeftToRight = IsFromLeftToRight,
                IsFromTopToDown = IsFromTopToDown,
                IsAutoDirectionDetector = IsAutoDirectionDetector,
                VerticalLocation = LstVerticalLocation,
                IsDesrializeByLocation = IsDesrializeVerticalByLocation,
                GetLocationByStrDelegate = GetLocationByStr,
                IsAutoFull2Half = content.IsAutoFull2Half,
                IsAutoSpace = content.IsAutoSpace,
                IsAutoSymbol = content.IsAutoSymbol,
                IsAutoDuplicateSymbol = content.IsAutoDuplicateSymbol,
                IsPercentSize = IsPercentSize,
                Size = content.Size,
                DicDeserializeVerticalJson = DicDeserializeVerticalJson,
            };
            var result = OcrHtmlProcess.GetSpiltTextByJsonNew(html, ResultType, ocrProcessConfig);
            return result;
        }

        //英文模式，不处理单字
        protected virtual void SetProcessArray(string html)
        {
        }

        public ProcessStateEntity QueryFileStatus(string taskId)
        {
            return QueryFileStatuMethod(taskId);
        }

        public virtual ProcessStateEntity QueryFileStatuMethod(string taskId)
        {
            return new ProcessStateEntity() { state = OcrProcessState.处理成功, desc = "处理完毕，可以下载了！", taskId = taskId };
        }

        public virtual void Reset()
        {
            //if (State != EnableState.启用)
            //{
            //    State = EnableState.启用;
            //}
            if (MaxExecPerTimeBack != 0 && MaxExecPerTimeBack != MaxExecPerTime)
            {
                MaxExecPerTime = MaxExecPerTimeBack;
                State = EnableState.启用;
            }
        }

        const string HEHE_ERROR = "\"msg\":\"获取文件失败\"";
        const string GOOGLE_ERROR = "\"message\": \"We can not access the URL";
        const string DUGUANG_ERROR = "message\":\"url";
        public OcrContent GetResult(OcrContent content)
        {
            if (content.OcrTime == null)
            {
                content.OcrTime = new OcrTimeEntity();
            }
            
            content.OcrTime.ClientOcrEngineStart = ServerTime.DateTime.Ticks;
            content.threadId = Thread.CurrentThread.ManagedThreadId;
            content.state = OcrProcessState.处理中;
            content.processId = GetOcrType();
            content.processName = GetOcrTypeName();
            OnStart?.Invoke(this, new OnStartedEventArgs(content));
            long processedTick = ServerTime.DateTime.Ticks;
            if (MaxExecPerTime == 0 || NowExecTimes < MaxExecPerTime)
            {
                Interlocked.Increment(ref NowExecTimes);
                Interlocked.Increment(ref TotalExecTimes);
                var html = string.Empty;
                try
                {
                    if (!AllowUploadFileTypes.Contains(content.fileExt))
                    {
                        //类型不支持，不处理
                        throw new Exception("文件类型不支持！");
                    }

                    processedTick = ServerTime.DateTime.Ticks;
                    //如果不支持URL，且没有下载，先下载再处理
                    if (!IsSupportUrlOcr && string.IsNullOrEmpty(content.strBase64) && !string.IsNullOrEmpty(content.url))
                    {
                        content.strBase64 = GetBase64ByUrl(content);
                    }
                    if (IsSupportUrlOcr && !string.IsNullOrEmpty(content.url))
                    {
                        html = GetHtmlByUrl(content);
                        if (!string.IsNullOrEmpty(html) && (
                            (content.processName.Contains("合合") && html.Contains(HEHE_ERROR)) ||
                            (content.processName.Contains("Google") && html.Contains(GOOGLE_ERROR)) ||
                            (content.processName.Contains("读光") && html.Contains(DUGUANG_ERROR))))
                        {
                            html = "";
                            //如果不支持URL，且没有下载，先下载再处理
                            if (string.IsNullOrEmpty(content.strBase64) && !string.IsNullOrEmpty(content.url))
                            {
                                content.strBase64 = GetBase64ByUrl(content);
                                content.url = null;
                            }
                        }
                    }
                    if (string.IsNullOrEmpty(html) && !string.IsNullOrEmpty(content.strBase64))
                    {
                        html = GetHtml(content);
                    }

                    content.OcrTime.ClientParseResultStart = ServerTime.DateTime.Ticks;

                    if (string.IsNullOrEmpty(html))
                    {
                        ReduceWeight(true, processedTick);
                        content.state = OcrProcessState.处理失败;
                    }
                    else
                    {
                        if (!ProcessForbidResultHtml(content.ocrType, html, processedTick))
                        {
                            processedTick = ServerTime.DateTime.Ticks;
                            EmptyTimes = 0;
                            content.result = GetProcessText(content, html);
                            if (content.result != null
                                && string.IsNullOrEmpty(content.result.spiltText)
                                && ResultType != ResutypeEnum.网页)
                            {
                                LogHelper.Log.Info($"【{OcrDesc}】解析结果为空，HTML：{(html?.Length > 200 ? html.Substring(0, 200) : html)}");
                            }

                            content.state = OcrProcessState.处理成功;
                        }
                        else
                        {
                            content.state = OcrProcessState.处理失败;
                        }
                        content.OcrTime.ClientParseResultEnd = ServerTime.DateTime.Ticks;
                    }
                }
                catch (Exception oe)
                {
                    SetErrorLog(oe.Message, processedTick);
                    LogHelper.Log.Error("Html:" + html, oe);
                    content.state = OcrProcessState.处理失败;
                    content.OcrTime.ErrorOccurTime = ServerTime.DateTime.Ticks;
                    OnError?.Invoke(this, new OnErrorEventArgs(content, oe));
                }
                finally
                {
                    Interlocked.Decrement(ref NowExecTimes);
                }
            }
            else
            {
                Interlocked.Increment(ref ForbidTimes);
                content.state = OcrProcessState.并发限制;
                OnError?.Invoke(this, new OnErrorEventArgs(content, new Exception("最大并发限制" + MaxExecPerTime)));
                //Console.WriteLine(string.Format("【{4}】限制次数：{1}，当前次数：{0}/{2}，拒绝次数：{3}", NowExecTimes, MaxExecPerTime, TotalExecTimes, ForbidTimes, OrcType));
            }
            content.OcrTime.OcrServerEnd = ServerTime.DateTime.Ticks;
            if (content.state != OcrProcessState.处理失败 && content.state != OcrProcessState.并发限制)
            {
                OnCompleted?.Invoke(this, new OnCompletedEventArgs(content));
            }
            return content;
        }

        private void SetErrorLog(string message, long fromTick)
        {
            LogHelper.Log.Error($"【{OcrDesc}】:{message}");
        }

        private List<string> lstForbidHttpCode = new List<string>() { HttpStatusCode.MethodNotAllowed.ToString(), HttpStatusCode.Unauthorized.ToString(), HttpStatusCode.NotFound.ToString() };
        private bool ProcessForbidResultHtml(OcrType ocrType, string html, long processedTick)
        {
            var result = lstForbidHttpCode.Contains(html, StringComparer.Ordinal) || CheckIfMatch(LstForbidArray, html);
            if (result)
            {
                Forbid(ocrType, processedTick);
            }
            else
            {
                result = CheckIfMatch(LstReduceWeightArray, html);
                if (result)
                {
                    ReduceWeight(false, processedTick);
                }
            }
            return result;
        }

        private bool CheckIfMatch(Dictionary<string, List<string>> dicCheck, string html)
        {
            var result = false;
            if (dicCheck != null)
            {
                foreach (var item in dicCheck)
                {
                    var comPareStr = item.Key;
                    if (item.Value?.Count > 0)
                    {
                        comPareStr = html.SubStringHorspool(item.Value[0], item.Value.Count > 1 ? item.Value[1] : null);
                    }
                    if (!string.IsNullOrEmpty(comPareStr) && html.Contains(comPareStr))
                    {
                        result = true;
                        break;
                    }
                }
            }
            return result;
        }

        public void Forbid(OcrType ocrType, long processedTick)
        {
            SetErrorLog("接口被屏蔽", processedTick);
            State = EnableState.禁用;
            LogHelper.Log.Info($"【{OcrDesc}】:接口被屏蔽！");
            BaseRecHelper.DisableByType(ocrType, OcrType.GetHashCode());
        }

        public void ReduceWeight(bool isEmpty = true, long processedTick = 0)
        {
            EmptyTimes++;
            if (EmptyTimes == 5)
            {
                EmptyTimes = 0;
                MaxExecPerTime -= 5;
                if (MaxExecPerTime < 1)
                {
                    State = EnableState.禁用;
                }

                SetErrorLog("接口被降权", processedTick);
                LogHelper.Log.Info($"【{OcrDesc}】:接口被降权");
                BaseRecHelper.ClearCache();
            }
            else
            {
                if (isEmpty)
                {
                    LogHelper.Log.Info($"【{OcrDesc}】:返回内容为空，当前连续失败总次数：{EmptyTimes}！");
                }
            }
        }

        public string OcrDesc
        {
            get
            {
                // return string.Format("{0}-{1}", GetOcrTypeName(), EngineDesc);
                return $"{GetOcrTypeName()}-{EngineDesc}";
            }
        }

        public string PostFile(string url, IEnumerable<UploadFileInfo> files, NameValueCollection contents, NameValueCollection headers = null)
        {
            var result = string.Empty;
            try
            {
                result = UploadFileRequest.Post(url, files, contents, headers);
            }
            catch (BadApiException exception)
            {
                LogHelper.Log.Error(OcrDesc, exception);
                switch (exception.Code)
                {
                    //case HttpStatusCode.Forbidden: //403
                    case HttpStatusCode.MethodNotAllowed: //405
                    case HttpStatusCode.Unauthorized://401
                    case HttpStatusCode.NotFound: //404
                        result = exception.Code.ToString();
                        break;
                }
            }
            catch { }
            return result;
        }

        /// <summary>
        /// 直接处理CusImageEntity，减少对象创建和参数传递的开销
        /// </summary>
        /// <param name="processEntity">OCR处理实体</param>
        /// <returns>OCR内容结果</returns>
        public OcrContent GetResult(CusImageEntity processEntity)
        {
            var content = new OcrContent
            {
                id = processEntity.StrIndex,
                url = processEntity.ImgUrl,
                strBase64 = processEntity.StrImg,
                ocrType = processEntity.OcrType,
                from = processEntity.FromLanguage,
                to = processEntity.ToLanguage,
                fileExt = processEntity.FileExt,
                IsAutoDuplicateSymbol = processEntity.IsAutoDuplicateSymbol,
                IsAutoFull2Half = processEntity.IsAutoFull2Half,
                IsAutoSpace = processEntity.IsAutoSpace,
                IsAutoSymbol = processEntity.IsAutoSymbol,
                OcrTime = processEntity.OcrTime
            };
            
            // 直接调用原有的GetResult方法
            content = GetResult(content);
            
            // 处理状态
            switch (content.state)
            {
                case OcrProcessState.类型不支持:
                case OcrProcessState.处理失败:
                case OcrProcessState.并发限制:
                    processEntity.State = ProcessState.失败;
                    break;
                case OcrProcessState.处理成功:
                    processEntity.State = ProcessState.已完成;
                    break;
                case OcrProcessState.处理超时:
                    processEntity.State = ProcessState.已超时;
                    break;
                default:
                    processEntity.State = ProcessState.正在处理;
                    break;
            }
            
            // 表格和翻译特殊处理
            if (processEntity.State == ProcessState.已完成)
            {
                if (processEntity.IsTextToTable)
                {
                    if (content.result.ToTable())
                        content.result.resultType = ResutypeEnum.表格;
                }
                else if (processEntity.IsTextToTrans)
                {
                    // 翻译处理逻辑
                }
            }
            
            return content;
        }
    }

    public enum EnableState
    {
        启用 = 0,
        禁用 = 1
    }

    [Serializable]
    public class ProcessStateEntity
    {

        public string taskId { get; set; }

        public int percent { get; set; }

        public int privewPercent { get; set; }

        public OcrProcessState state { get; set; }

        public string desc { get; set; }
    }
}
