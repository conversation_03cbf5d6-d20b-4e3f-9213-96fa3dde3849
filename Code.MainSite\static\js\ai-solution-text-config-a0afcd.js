ready(function(){let vm=new Vue({el:"#vue",data:{activeTab:"tab-0",loading:{recog:false},currentSample:"",recogInfo:{text:"",status:0},fieldList:[],viewType:"text",activeScene:"scene-1"},methods:{init:function(){vm.initEvent();vm.initSample()},initEvent:function(){},initSample:function(){let data={code:200,message:"success",data:[{key:"时间",value:"2018年10月22日16时05分",position:"52,137,508,137,508,160,52,160"},{key:"地点",value:"永和路3号中石化加油站",position:"126,170,396,171,396,194,126,194"},{key:"在场人",value:"小王",position:"530,170,689,170,689,193,530,194"},{key:"询问人",value:"小艾",position:"153,203,210,203,210,227,153,226"},{key:"记录人",value:"小峰",position:"57,236,216,236,216,260,57,259"},{key:"被询问人姓名",value:"小郎",position:"52,301,258,301,258,325,52,326"},{key:"被询问人性别",value:"男",position:"275,301,710,301,710,324,275,325"},{key:"被询问人出生日期",value:"1983.10.25",position:"275,301,710,301,710,324,275,325"},{key:"被询问人民族",value:"汉",position:"275,301,710,301,710,324,275,325"}]};let url=$("#static-base").val()+"ai/solution/sample/z2.jpg";vm.currentSample=url;vm.fieldList=data.data;vm.jsonBuild(JSON.stringify(data))},checkScene:function(scene){vm.activeScene=scene},consult:function(){},readFile:function(file,callback){var size=file.size;var reader=new FileReader;vm.filename=file.name;reader.readAsDataURL(file);reader.onload=function(e){var base64WithHead=this.result,base64=DPS.removeBase64Head(base64WithHead);if(size>AIBASE.maxBodySize){DPS.compressDataWithOption(base64WithHead,{max:1600},function(data){vm.base64WithHead=data;callback(data)})}else{vm.base64WithHead=base64WithHead;callback(base64)}}},checkTab:function(tab){if(tab){vm.activeTab=tab}},parseRecog:function(ret){var list=ret.item_list;if(isArray(list)&&list.length){list.forEach(function(item){if(item.value===true)item.value="true";if(item.value===false)item.value="false"})}return ret},recogNotice:function(text,status){var T=2e3;vm.recogInfo.text=text;vm.recogInfo.status=status;setTimeout(function(){vm.recogInfo.text=""},T)},jsonBuild:function(data){iu.jsonview.insert(data,$(".iu-jsonview-container"))},checkViewType:function(id){vm.viewType=id},isImgKey:function(key){if(!key)return false;var array=["id_number_image","crop_image","head_portrait"];return array.indexOf(key)>-1}}});vm.init()});