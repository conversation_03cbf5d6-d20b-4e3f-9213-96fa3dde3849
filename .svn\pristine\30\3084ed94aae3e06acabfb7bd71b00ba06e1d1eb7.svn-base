﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="DonotRunMeDirectly" xml:space="preserve">
    <value>Oops..I'm using to update, don't run me directly.</value>
  </data>
  <data name="Error" xml:space="preserve">
    <value>Error</value>
  </data>
  <data name="Message" xml:space="preserve">
    <value>Message</value>
  </data>
  <data name="MinmumVersionRequired_Desc" xml:space="preserve">
    <value>To perform a update the minimum version requires {0}, and you have version {1} installed. Please update to latest manually.</value>
    <comment>MinmumVersionRequired_Desc description</comment>
  </data>
  <data name="Unknown" xml:space="preserve">
    <value>Unknown</value>
  </data>
  <data name="UpdateFound_EstimateDownloadSize" xml:space="preserve">
    <value>Established Download Size: {0}</value>
    <comment>UpdateFound_DownloadSize description</comment>
  </data>
  <data name="Updater_AssemblyNotMarkedAsUpdateable" xml:space="preserve">
    <value>The main assembly not marked as updatable, unable to determine update url.</value>
  </data>
  <data name="Updater_AutomaticUpgradeTipForce" xml:space="preserve">
    <value>{0} (Version {1}) will upgrade to version {2} automatically, please wait for upgrade complete.\n\nOld version no longer working.</value>
  </data>
  <data name="Updater_AutomaticUpgradeTipNotForce" xml:space="preserve">
    <value> {0} (Version {1}) will upgrade to version {2} automatically, please wait for upgrade complete.</value>
  </data>
  <data name="Updater_ExtractingFile" xml:space="preserve">
    <value>Decompressing {0}</value>
    <comment>Updater_ExtractingFile description</comment>
  </data>
  <data name="Updater_InstallFileError" xml:space="preserve">
    <value>Failed to intstall file '{0}' to '{1}', error: {2}</value>
  </data>
  <data name="Updater_UnableToCheckUpdate" xml:space="preserve">
    <value>Unable to check for update, error: {0}</value>
  </data>
  <data name="Updater_UpdateCanceledByCloseApp" xml:space="preserve">
    <value>Update aborted due to unable to close running applications.</value>
  </data>
  <data name="UpdatesFound_CriticalUpdateWarning" xml:space="preserve">
    <value>This update is critical update, cancel this update will cause application stops working. Sure to coninue?</value>
    <comment>UpdatesFound_CriticalUpdateWarning description</comment>
  </data>
  <data name="UpdatesFound_Tip" xml:space="preserve">
    <value>Your application version of '{1}' can now upgrade to '{0}', upgrade now?</value>
    <comment>UpdatesFound_Top description</comment>
  </data>
  <data name="ClosingApplications" xml:space="preserve">
    <value>Closing applications...</value>
  </data>
  <data name="ComputingFileInfo" xml:space="preserve">
    <value>Estimate downloading information...</value>
  </data>
  <data name="DeleteOriginalFiles" xml:space="preserve">
    <value>Removing old files...</value>
  </data>
  <data name="DownloadPackage" xml:space="preserve">
    <value>Downloading...</value>
  </data>
  <data name="DownloadProgress" xml:space="preserve">
    <value>Downloading...{0}/{1} ({2}/{3})</value>
  </data>
  <data name="ExecuteExternal" xml:space="preserve">
    <value>Executing program...</value>
  </data>
  <data name="ExtractPackage" xml:space="preserve">
    <value>Extract packages...</value>
  </data>
  <data name="InstallNewFiles" xml:space="preserve">
    <value>Install new files...</value>
  </data>
  <data name="InstallPackages" xml:space="preserve">
    <value>Install packages...</value>
  </data>
  <data name="Ex_VersionTooLow" xml:space="preserve">
    <value>Unable to update due to current version '{1}' was too low to perform update (required '{0}').</value>
  </data>
</root>