﻿<%@ Page Language="C#" AutoEventWireup="true" CodeBehind="RCode.aspx.cs" Inherits="Code.Client.Web.RCode" %>

<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head runat="server">
    <title></title>
    <link rel="stylesheet" href="/css/style.css" media="screen" type="text/css" />
</head>
<body>
    <section class='container'>
    <form id="form1" runat="server">
		<fieldset class='alpha'>
			<legend><b>Code System</b></legend>
			<div class='frow'><asp:TextBox ID="txtApp" runat="server" CssClass="item" placeholder='码子' /></div>
			<div class='frow'><asp:TextBox ID="txtMoney" runat="server" CssClass="item" placeholder='金额' /></div>
			<div class='frow'><asp:TextBox ID="txtAmount" runat="server" CssClass="item" placeholder='数量' /></div>
			<div class='frow'><asp:TextBox ID="txtPwd" runat="server" CssClass="item" placeholder='密钥' /></div>
			<div class='frow'>
            <asp:Button ID="btnQuery" runat="server" Text="Query" OnClick="btnQuery_Click" Width="45%" />
            <asp:Button ID="btnOK" runat="server" Text="Create/Change" OnClick="btnOK_Click" Width="45%" style="margin-left:5px;" />
			</div>
			<div class='frow' style="height:10px;"></div>
			<legend><b>Opreate Info</b></legend>
			<div class='frow'><asp:Label ID="lblMsg" runat="server" Text=""></asp:Label></div>
		</fieldset>

	</form>
</section>
</body>
</html>
