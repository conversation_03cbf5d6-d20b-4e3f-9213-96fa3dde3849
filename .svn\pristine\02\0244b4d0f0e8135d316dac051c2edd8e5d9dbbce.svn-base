﻿using CommonLib;
using System;
using System.Collections.Generic;

namespace HanZiOcr
{
    /// <summary>
    /// https://ai.100tal.com/product/ocr-ptr
    /// </summary>
    public class XueErSiDemoRec : BaseOcrRec
    {
        public XueErSiDemoRec()
        {
            OcrGroup = OcrGroupType.学而思;
            OcrType = HanZiOcrType.学而思;
            MaxExecPerTime = 20;

            IsSupportUrlOcr = true;

            LstJsonPreProcessArray = new List<object>() { "data", "recognition", "ocrLinesList" };
            LstJsonNextProcessArray = new List<object>() { "data" };
            IsSupportVertical = true;
            IsDesrializeVerticalByLocation = true;
            DicDeserializeVerticalJson = new Dictionary<string, string> { { "left", "x" }, { "top", "y" }, { "words", "value" } };
        }

        protected override string GetHtml(OcrContent content)
        {
            return processByContent(content);
        }

        public override string GetHtmlByUrl(OcrContent content)
        {
            return processByContent(content);
        }

        private string processByContent(OcrContent content)
        {
            var strPost = "{\"idx\":\"10000\","
                            + "\"sid\":\"" + UnixTime() + "\""
                            + ",\"URL\":" + (string.IsNullOrEmpty(content.url) ? "\"\"" : "\"" + content.url + "\"")
                            + ",\"openMode\":\"0\",\"platform\":\"1\""
                            + ",\"imagedata\":" + (string.IsNullOrEmpty(content.strBase64) ? "null" : "\"" + content.strBase64 + "\"")
                            + ",\"imageLength\":" + (string.IsNullOrEmpty(content.strBase64) ? 0 : Convert.FromBase64String(content.strBase64).Length)
                            + ",\"method\":\"/aiocr/srchsub\""
                            + "}";
            var result = WebClientSyncExt.GetHtml("https://ai.100tal.com/openapi/try/service", strPost, ExecTimeOutSeconds);
            //"{\"code\":0,\"msg\":\"成功\",\"data\":\"[{\\\"confidence\\\":1,\\\"word\\\":[{\\\"content\\\":\\\"请点击下图中所有的靴子\\\"},{\\\"content\\\":\\\"火柴\\\"}]}]\",\"content\":null,\"audioPath\":null}"
            return result;
        }

        private string UnixTime()
        {
            return Convert.ToInt64((DateTime.UtcNow - new DateTime(1970, 1, 1, 0, 0, 0, 0)).TotalSeconds * 1000).ToString();
        }

    }
}