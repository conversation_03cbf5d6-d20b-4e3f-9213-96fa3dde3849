﻿using CommonLib;
using System;
using System.Collections.Generic;
using System.Collections.Specialized;
using System.IO;
using System.Linq;
using System.Net;
using System.Reflection;
using System.Text;
using System.Threading.Tasks;
using System.Web;

namespace Account.Web
{
    public delegate byte[] GetImage(CNNWebClient myClient, string Url, ref string CookieStr, string ipAddress = "");
    public delegate string GetNoSyncHtml(string Url, string CookieStr, string ipAddress = "", string strPost = "", string Referer = "", int timeOut = 2);
    public class WebClientExt
    {
        private static List<string> lstErrorGptMsg = new List<string>() { "盗用接口的网站", "yqcloud.top", "jinshutuan.com", "wuguokai.cn", "binjie.fun", "yscxy.net", "AI教学", "零声教育", "0voice.ke.qq.com" };

        private static string GetGptMsg(string result)
        {
            if (!string.IsNullOrEmpty(result) && lstErrorGptMsg.Any(p => result.Contains(p)))
            {
                result = string.Empty;
            }
            else
            {
                if (result.Contains("ChatGPT账号") && result.Contains("免费"))
                {
                    result = result.Substring(result.LastIndexOf("\n") + 1);
                }
            }
            return result;
        }

        public static async Task<bool> RedirectHtmlAsync(HttpContext context, string Url, string CookieStr, string strPost = "", string Referer = "")
        {
            var result = false;
            try
            {
                var uri = new Uri(Url, true);
                var request = (HttpWebRequest)WebRequest.Create(uri);
                request.Method = "POST";
                //request.Proxy = new WebProxy("195.189.62.7", 80);
                if (!string.IsNullOrEmpty(strPost) && strPost.StartsWith("{"))
                    request.ContentType = "application/json; charset=utf-8";  // 设置请求体编码格式
                else
                    SetHeaderValue(request.Headers, "Content-Type", "application/x-www-form-urlencoded");

                request.Accept = "application/json; charset=utf-8";

                if (CookieStr != "")
                {
                    request.Headers.Add("Cookie: " + CookieStr);
                }
                if (!string.IsNullOrEmpty(Referer))
                {
                    SetHeaderValue(request.Headers, "Referer", Referer);
                    SetHeaderValue(request.Headers, "Origin", Referer);
                }
                SetHeaderValue(request.Headers, "User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/110.0.0.0 Safari/537.36");

                request.Timeout = 15000;

                using (var streamWriter = new StreamWriter(request.GetRequestStream()))
                {
                    streamWriter.Write(strPost);
                    streamWriter.Flush();
                    streamWriter.Close();
                }

                context.Response.ContentType = "application/json";
                context.Response.BufferOutput = true;
                context.Response.Buffer = false;
                context.Response.ContentEncoding = Encoding.UTF8;

                using (var response = (HttpWebResponse)request.GetResponse())
                using (var streamReader = new StreamReader(response.GetResponseStream(), Encoding.UTF8))
                {
                    StringBuilder sb = new StringBuilder();
                    var buffer = new char[1024];
                    int bytesRead;
                    bool isFirst = true;
                    while ((bytesRead = streamReader.Read(buffer, 0, buffer.Length)) > 0)
                    {
                        if (isFirst && sb.Length < 20)
                        {
                            if (Equals(buffer[0], '{') || (buffer.Length > 1 && Equals(buffer[0], '\n') && Equals(buffer[1], '{')))
                            {
                                var strTmp = string.Join("", buffer);
                                strTmp = CommonHelper.SubString(strTmp, "\",\"text\":\"", "\",\"role\":\"");
                                sb = new StringBuilder(strTmp);
                            }
                            else
                            {
                                sb.Append(buffer, 0, bytesRead);
                            }
                        }
                        else
                        {
                            if (sb.Length > 0)
                            {
                                isFirst = false;
                                var strResult = GetGptMsg(sb.ToString());
                                sb.Clear();
                                if (string.IsNullOrEmpty(strResult))
                                {
                                    break;
                                }
                                context.Response.Output.Write(strResult);
                                result = true;
                            }
                            context.Response.Output.Write(buffer, 0, bytesRead);
                            context.Response.Flush();
                        }
                    }
                    if (sb.Length > 0)
                    {
                        var strResult = GetGptMsg(sb.ToString());
                        result = !string.IsNullOrEmpty(strResult);
                        if (result)
                        {
                            context.Response.Output.Write(strResult);
                        }
                        //else
                        //{
                        //    LogHelper.Log.Error(sb.ToString());
                        //}
                        sb.Clear();
                    }
                }
            }
            catch (Exception oe)
            {
            }
            return result;
        }

        private static void SetHeaderValue(WebHeaderCollection header, string name, string value)
        {
            var property = typeof(WebHeaderCollection).GetProperty("InnerCollection",
                BindingFlags.Instance | BindingFlags.NonPublic);
            if (property != null)
            {
                var collection = property.GetValue(header, null) as NameValueCollection;
                collection[name] = value;
            }
        }

        public static string GetHtml(string Url, string CookieStr, string ipAddress = "", string strPost = "", string Referer = "", int timeOut = 2, int nCount = 0)
        {
            string result = "";
            CNNWebClient myClient = new CNNWebClient();
            try
            {
                myClient.Timeout = timeOut;
                if (!string.IsNullOrEmpty(ipAddress))
                    myClient.StrIPAddress = ipAddress;
                if (!string.IsNullOrEmpty(strPost) && strPost.StartsWith("{"))
                    myClient.Headers.Add("Content-Type: application/json");
                else
                    myClient.Headers.Add("Content-Type: application/x-www-form-urlencoded");
                myClient.Headers.Add("User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/110.0.0.0 Safari/537.36");
                myClient.Headers.Add("Cache-Control: no-cache");
                myClient.Headers.Add("Pragma: no-cache");
                if (CookieStr != "")
                {
                    myClient.Headers.Add("Cookie: " + CookieStr);
                }
                myClient.Encoding = Encoding.UTF8;
                if (!string.IsNullOrEmpty(Referer))
                {
                    myClient.SetHeaderValue(myClient.Headers, "Referer", Referer);
                    myClient.SetHeaderValue(myClient.Headers, "Origin", Referer);
                }
                myClient.Headers.Add("sec-ch-ua", "\"Chromium\";v=\"110\", \"Not A(Brand\";v=\"24\", \"Google Chrome\";v=\"110\"");
                myClient.Headers.Add("sec-ch-ua-mobile", "?0");
                myClient.Headers.Add("sec-ch-ua-platform", "\"Windows\"");
                myClient.Headers.Add("Sec-Fetch-Site", "cross-site");
                myClient.Headers.Add("Sec-Fetch-Mode", "cors");
                myClient.Headers.Add("Sec-Fetch-Dest", "empty");
                if (string.IsNullOrEmpty(strPost))
                    result = myClient.DownloadString(new Uri(Url, true));
                else
                    result = myClient.UploadString(new Uri(Url, true), strPost);
                if (!string.IsNullOrEmpty(myClient.ResponseHeaders["Set-Cookie"]))
                {
                    //JSESSIONID=TM566SC1-55FYNTOYH1A6L00BEW1G3-SAYYTW7I-VV85; Path=/; HttpOnly_session0=eNrz4IovSYovyc9OzYvn8k9OM3ROzXFyy%2BcNdg0O9vT3i%2Ff1d3H1MYiqzkyxUgrxNTUzC3Y21DU1dYv0C%2FGP9DB0NPMxMHByDTd0N9YNdoyMDAk399QNC7MwVdJJLrEyNDEyBwIzC3NjYyOdxGQ0gdwKK4PaKACsXCPB; Path=/; HttpOnly
                    CookieStr += myClient.ResponseHeaders["Set-Cookie"].Replace("Path=/;", " ").Replace("HttpOnly", "").Replace(",", "").Trim();
                }
                if (!string.IsNullOrEmpty(myClient.ResponseHeaders["Location"]))
                {
                    nCount++;
                    if (nCount < 3)
                        return GetHtml(myClient.ResponseHeaders["Location"], CookieStr, ipAddress, "", Referer, timeOut, nCount);
                }
                if (Url.Contains("baidu") && !string.IsNullOrEmpty(myClient.ResponseHeaders["Date"]))//header.Url.Contains("12306") ||
                {
                    try
                    {
                        result = myClient.ResponseHeaders["Date"];
                    }
                    catch { }
                }
            }
            catch (Exception oe)
            {
            }
            finally
            {
                try
                {
                    if (myClient.IsBusy)
                        myClient.CancelAsync();
                }
                catch { }
                try
                {
                    myClient.Dispose();
                }
                catch { }
                try
                {
                    myClient = null;
                }
                catch { }
            }
            return result;
        }
    }

    ///// <summary>
    ///// 过期时回调委托
    ///// </summary>
    ///// <param name="userdata"></param>
    //public delegate void TimeoutCaller(object userdata);

    public class CNNWebClient : WebClient
    {
        //private Calculagraph _timer;
        private int _timeOut = 3;
        private string strIPAddress = "";

        public string StrIPAddress
        {
            get { return strIPAddress; }
            set { strIPAddress = value; }
        }

        /// <summary>
        /// 过期时间
        /// </summary>
        public int Timeout
        {
            get
            {
                return _timeOut;
            }
            set
            {
                if (value <= 0)
                    _timeOut = 10;
                _timeOut = value;
            }
        }

        /// <summary>
        /// 重写GetWebRequest,添加WebRequest对象超时时间
        /// </summary>
        /// <param name="address"></param>
        /// <returns></returns>
        protected override WebRequest GetWebRequest(Uri address)
        {
            if (ServerTime.DateTime.Second % 2 == 0)
            {
                //System.Threading.Thread.Sleep(1);
                System.GC.Collect();
            }
            //System.GC.Collect();
            if (!string.IsNullOrEmpty(StrIPAddress))
                address = new Uri(address.AbsoluteUri.Replace(address.Host, StrIPAddress), true);

            HttpWebRequest request = (HttpWebRequest)base.GetWebRequest(address);//address.AbsoluteUri.IndexOf("leftTicket") > 0 ? (HttpWebRequest)HttpWebRequest.Create(address.AbsoluteUri) :
            if (request.Proxy != null)
            {
                request.Proxy = null;
            }
            request.ProtocolVersion = HttpVersion.Version10;
            //是否使用 Nagle 不使用 提高效率 
            request.ServicePoint.UseNagleAlgorithm = false;
            //最大连接数 
            request.ServicePoint.ConnectionLimit = int.MaxValue;
            //数据是否缓冲 false 提高效率  
            request.AllowWriteStreamBuffering = true;
            //request.ServicePoint.Expect100Continue = false;
            request.AutomaticDecompression = DecompressionMethods.Deflate | DecompressionMethods.GZip;
            request.AllowAutoRedirect = false;

            request.KeepAlive = true;
            request.IfModifiedSince = new DateTime(1970, 1, 1);
            request.UserAgent = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/110.0.0.0 Safari/537.36";
            //request.Headers.Add("If-None-Match", DateTime.Now.Ticks.ToString());
            //request.CachePolicy = new HttpRequestCachePolicy(HttpRequestCacheLevel.NoCacheNoStore);

            //HttpHelper.SetHeaderValue(request.Headers, "If-Modified-Since", "Wed, 31 Dec 1969 16:00:00 GMT");
            //request.Headers.Add("If-None-Match", DateTime.Now.Ticks.ToString());
            //}
            request.Proxy = GlobalProxySelection.GetEmptyWebProxy();
            //HttpHelper.SetHeaderValue(request.Headers, "Connection", "Close");
            return request;
        }

        public void SetHeaderValue(WebHeaderCollection header, string name, string value)
        {
            var property = typeof(WebHeaderCollection).GetProperty("InnerCollection",
                BindingFlags.Instance | BindingFlags.NonPublic);
            if (property != null)
            {
                var collection = property.GetValue(header, null) as NameValueCollection;
                collection[name] = value;
            }
        }
    }
}
