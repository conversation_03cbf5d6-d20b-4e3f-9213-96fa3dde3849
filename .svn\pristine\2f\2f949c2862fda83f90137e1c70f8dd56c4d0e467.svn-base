﻿using CommonLib;
using System.ServiceProcess;
using System.Threading.Tasks;

namespace Notice.Process.Console
{
    partial class EngineService : ServiceBase
    {
        public EngineService()
        {
            InitializeComponent();
        }

        protected override void OnStart(string[] args)
        {
            if (ConfigHelper.NMaxCodeProcessThread > 1)
            {
                Parallel.For(0, ConfigHelper.NMaxCodeProcessThread, item =>
                {
                    ProcessNotice.StartProcess();
                });
            }
            else
            {
                ProcessNotice.StartProcess();
            }
        }

        protected override void OnStop()
        {
            //处理服务的关闭
            ProcessNotice.StopProgress();
        }
    }
}
