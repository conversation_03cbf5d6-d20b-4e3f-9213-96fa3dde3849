﻿using CommonLib;
using System;
using System.Collections.Generic;
using System.Collections.Specialized;
using System.IO;
using System.Security.Cryptography;
using System.Text;
using System.Web;

namespace TransOcr
{
    /// <summary>
    /// 有道翻译
    /// https://ai.youdao.com/DOCSIRMA/html/%E6%96%87%E5%AD%97%E8%AF%86%E5%88%ABOCR/API%E6%96%87%E6%A1%A3/%E9%80%9A%E7%94%A8OCR%E6%9C%8D%E5%8A%A1/%E9%80%9A%E7%94%A8OCR%E6%9C%8D%E5%8A%A1-API%E6%96%87%E6%A1%A3.html
    /// </summary>
    public class YouDaoRec : BaseOcrRec
    {
        public YouDaoRec()
        {
            OcrGroup = OcrGroupType.有道;
            OcrType = TransOcrType.有道翻译;
            MaxExecPerTime = 21;

            LstJsonPreProcessArray = new List<object>() { "translateResult" };

            LstJsonNextProcessArray = new List<object>() { "|Merge|" };

            AllowUploadFileTypes = new List<string>() { "txt" };
            StrResultJsonSpilt = "src";
            StrResultTransJsonSpilt = "tgt";
            InitLanguage();
        }

        #region 支持的语言

        private void InitLanguage()
        {
            TransLanguageDic = new Dictionary<TransLanguageTypeEnum, string>();
            TransLanguageDic.Add(TransLanguageTypeEnum.自动, "AUTO");
            TransLanguageDic.Add(TransLanguageTypeEnum.中文, "zh-CHS");
            TransLanguageDic.Add(TransLanguageTypeEnum.英文, "en");
            TransLanguageDic.Add(TransLanguageTypeEnum.日语, "ja");
            TransLanguageDic.Add(TransLanguageTypeEnum.韩语, "ko");
            TransLanguageDic.Add(TransLanguageTypeEnum.西班牙语, "es");
            TransLanguageDic.Add(TransLanguageTypeEnum.法语, "fr");
            TransLanguageDic.Add(TransLanguageTypeEnum.泰语, "th");
            TransLanguageDic.Add(TransLanguageTypeEnum.阿拉伯语, "ar");
            TransLanguageDic.Add(TransLanguageTypeEnum.俄语, "ru");

            TransLanguageDic.Add(TransLanguageTypeEnum.葡萄牙语, "pt");
            TransLanguageDic.Add(TransLanguageTypeEnum.德语, "de");
            TransLanguageDic.Add(TransLanguageTypeEnum.意大利语, "it");
            TransLanguageDic.Add(TransLanguageTypeEnum.希腊语, "el");
            TransLanguageDic.Add(TransLanguageTypeEnum.荷兰语, "nl");
            TransLanguageDic.Add(TransLanguageTypeEnum.波兰语, "pl");
            TransLanguageDic.Add(TransLanguageTypeEnum.保加利亚语, "bg");
            TransLanguageDic.Add(TransLanguageTypeEnum.爱沙尼亚语, "et");
            TransLanguageDic.Add(TransLanguageTypeEnum.丹麦语, "da");
            TransLanguageDic.Add(TransLanguageTypeEnum.芬兰语, "fi");
            TransLanguageDic.Add(TransLanguageTypeEnum.捷克语, "cs");
            TransLanguageDic.Add(TransLanguageTypeEnum.罗马尼亚语, "ro");
            TransLanguageDic.Add(TransLanguageTypeEnum.斯洛文尼亚语, "sl");
            TransLanguageDic.Add(TransLanguageTypeEnum.瑞典语, "sv");
            TransLanguageDic.Add(TransLanguageTypeEnum.匈牙利语, "hu");
            TransLanguageDic.Add(TransLanguageTypeEnum.越南语, "vi");
        }

        #endregion

        //string strAppId = "2423360539ba5632";
        //string secretId = "QQ8gLkYxtchLt6Osj1eXrsSDTus8N2Ru";
        //string strAppId = "712b0ae8fd3d404d";
        //string secretId = "TF7ORXNiC6J3V18WZ4JCVYe2chHPVnRZ";

        protected override string GetHtml(OcrContent content)
        {
            InitTokens();
            string from = "", to = "";
            InitLanguage(content, ref from, ref to);

            string time = UnixTime();
            var salt = time + "9";
            var cookie = "OUTFOX_SEARCH_USER_ID=123456789@123.45.67.89;";
            var strToken = appSign(content.strBase64, time, salt);
            /*{
i	识别文本区
from	auto
to	zh-CHS
smartresult	dict
client	fanyideskweb
salt	15978459050006
sign	5c9d97153afbaf2722994188847284fe
ts	1597845905000
doctype	json
version	2.1
keyfrom	fanyi.web
action	FY_BY_REALTIME
typoResult	false*/
            var strPost = string.Format("i={0}&from={1}&to={2}&smartresult=dict&client=fanyideskweb&salt={3}&sign={4}&ts={5}&doctype=json&version=2.1&keyfrom=fanyi.web&action=FY_BY_REALTIME&typoResult=false"
                , HttpUtility.UrlEncode(content.strBase64)
                , from
                , to
                , salt
                , strToken
                , time
                );
            var result = WebClientSyncExt.GetHtml("http://fanyi.youdao.com/translate_o?smartresult=dict&smartresult=rule"
                , cookie, strPost, "http://fanyi.youdao.com/", ExecTimeOutSeconds);
            //"{\"translateResult\":[[{\"tgt\":\"你好,测试\",\"src\":\"hello test\"}]],\"errorCode\":0,\"type\":\"en2zh-CHS\"}"
            return result;
        }

        private string appSign(string content, string time, string salt)
        {
            //var t = n.md5(navigator.appVersion)
            //, r = "" + (new Date).getTime()
            //, i = r + parseInt(10 * Math.random(), 10);
            //n.md5("fanyideskweb" + e + i + "]BjuETDhU)zqSxf-=B#7m")
            string plainText = strAppId + content + salt + strSecret;

            return ToMd5(plainText);
        }

        private string ToMd5(string strContent)
        {
            string result;
            if (strContent == null)
            {
                result = null;
            }
            else
            {
                MD5 md = MD5.Create();
                byte[] array = md.ComputeHash(Encoding.UTF8.GetBytes(strContent));
                StringBuilder stringBuilder = new StringBuilder();
                for (int i = 0; i < array.Length; i++)
                {
                    stringBuilder.Append(array[i].ToString("x2"));
                }
                result = stringBuilder.ToString();
            }
            return result;
        }

        private string strAppId = "";
        private string strSecret = "";

        private const string strAppJs = "sign:n.md5(";

        private void InitTokens()
        {
            if (string.IsNullOrEmpty(strSecret))
            {
                var jsUrl = GetJsUrl();
                if (!string.IsNullOrEmpty(jsUrl))
                {
                    var html = WebClientSyncExt.GetHtml(jsUrl, "", ExecTimeOutSeconds);
                    if (!string.IsNullOrEmpty(html) && html.Contains(strAppJs))
                    {
                        html = CommonHelper.SubString(html, "sign:n.md5(\"", "\")}");
                        if (!string.IsNullOrEmpty(html))
                        {
                            //"fanyideskweb\"+e+i+\"]BjuETDhU)zqSxf-=B#7m"
                            strAppId = CommonHelper.SubString(html, "", "\"");
                            if (!string.IsNullOrEmpty(strAppId))
                            {
                                html = CommonHelper.SubString(html, strAppId + "\"");
                                strSecret = CommonHelper.SubString(html, "\"");
                            }
                        }
                    }
                }
                if (string.IsNullOrEmpty(strSecret))
                {
                    strAppId = "fanyideskweb";
                    strSecret = "]BjuETDhU)zqSxf-=B#7m";
                }
            }
        }

        private string GetJsUrl()
        {
            var result = "";
            var html = WebClientSyncExt.GetHtml("http://fanyi.youdao.com/", "", ExecTimeOutSeconds);
            if (!string.IsNullOrEmpty(html) && html.Contains("fanyi.min.js"))
            {
                html = html.Substring(0, html.IndexOf("fanyi.min.js"));
                result = html.Substring(html.LastIndexOf("\"") + 1) + "fanyi.min.js";
            }
            return result;
        }

        public override void Reset()
        {
            strSecret = "";
            base.Reset();
        }

        private string UnixTime()
        {
            return Convert.ToInt64((DateTime.UtcNow - new DateTime(1970, 1, 1, 0, 0, 0, 0)).TotalSeconds * 1000).ToString();
        }

    }
}