﻿@inherits ServiceStack.Razor.ViewPage<Rockstars> 

@{
    ViewBag.Title = "Page with typed 'Rockstars' model and no C# controller";
    var rockstars = Model.Age.HasValue 
        ? Db.Select<Rockstar>(q => q.Age == Model.Age.Value)
        : Db.Select<Rockstar>();
    var title = Model.Age.HasValue ? "{0} year old rockstars".Fmt(Model.Age) : "All Rockstars";
}

<div id="content-page">
    
@Html.Partial("RazorPartial")
@Html.Partial("MarkdownPartial")
@Html.Partial("RazorPartialModel", rockstars)

<div>@title</div>
<ul>
    @foreach (var rockstar in rockstars) {
        <li>@rockstar.FirstName - @rockstar.LastName (<a href="?Age=@rockstar.Age">@rockstar.Age</a>)</li>
    }
</ul>

<p><a href="?">Show all Rockstars</a></p>

<h2>Razor View</h2>
<script src="https://gist.github.com/3162493.js"></script>
 
</div>

<!--view:Pages/default.cshtml-->