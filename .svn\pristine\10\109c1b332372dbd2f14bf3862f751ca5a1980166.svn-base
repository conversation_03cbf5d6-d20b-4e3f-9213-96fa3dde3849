﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="12.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v$(VisualStudioVersion)\TypeScript\Microsoft.TypeScript.Default.props" Condition="Exists('$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v$(VisualStudioVersion)\TypeScript\Microsoft.TypeScript.Default.props')" />
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProductVersion>
    </ProductVersion>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectGuid>{50971470-179A-45D1-92C9-748DEFCBD9E5}</ProjectGuid>
    <ProjectTypeGuids>{349c5851-65df-11da-9384-00065b846f21};{fae04ec0-301f-11d3-bf4b-00c04f79efbc}</ProjectTypeGuids>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>CheckMvc</RootNamespace>
    <AssemblyName>CheckMvc</AssemblyName>
    <TargetFrameworkVersion>v4.7.2</TargetFrameworkVersion>
    <MvcBuildViews>false</MvcBuildViews>
    <UseIISExpress>true</UseIISExpress>
    <IISExpressSSLPort />
    <IISExpressAnonymousAuthentication />
    <IISExpressWindowsAuthentication />
    <IISExpressUseClassicPipelineMode />
    <SolutionDir Condition="$(SolutionDir) == '' Or $(SolutionDir) == '*Undefined*'">..\..\src\</SolutionDir>
    <RestorePackages>true</RestorePackages>
    <MvcProjectUpgradeChecked>true</MvcProjectUpgradeChecked>
    <FileUpgradeFlags>
    </FileUpgradeFlags>
    <UpgradeBackupLocation>
    </UpgradeBackupLocation>
    <OldToolsVersion>4.0</OldToolsVersion>
    <UseGlobalApplicationHostFile />
    <NuGetPackageImportStamp>
    </NuGetPackageImportStamp>
    <Use64BitIISExpress />
    <TargetFrameworkProfile />
    <TypeScriptToolsVersion>3.0</TypeScriptToolsVersion>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="mscorlib" />
    <Reference Include="System" />
    <Reference Include="System.Data" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Net" />
    <Reference Include="System.Runtime.Serialization" />
    <Reference Include="System.Threading" />
    <Reference Include="System.Threading.Tasks" />
    <Reference Include="System.Web.DynamicData" />
    <Reference Include="System.Web.Entity" />
    <Reference Include="System.Web.ApplicationServices" />
    <Reference Include="System.ComponentModel.DataAnnotations" />
    <Reference Include="System.Web.Extensions" />
    <Reference Include="System.Web" />
    <Reference Include="System.Web.Abstractions" />
    <Reference Include="System.Web.Routing" />
    <Reference Include="System.Xml" />
    <Reference Include="System.Configuration" />
    <Reference Include="System.Web.Services" />
    <Reference Include="System.EnterpriseServices" />
    <Reference Include="System.Net.Http">
    </Reference>
    <Reference Include="System.Net.Http.WebRequest">
    </Reference>
    <Reference Include="System.Xml.Linq" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="CheckWebGlobalNamespace.dtos.cs" />
    <Compile Include="Controllers\HomeController.cs" />
    <Compile Include="RequestInfoTests.cs" />
    <Compile Include="TestGlobalNamespace.dtos.cs" />
    <Compile Include="Global.asax.cs">
      <DependentUpon>Global.asax</DependentUpon>
    </Compile>
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="StackApis.dtos.cs" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="App_Start\FilterConfig.cs" />
    <Compile Include="App_Start\RouteConfig.cs" />
    <Compile Include="App_Start\WebApiConfig.cs" />
    <Content Include="Global.asax" />
    <Content Include="tsconfig.json" />
    <Content Include="TypeScript.dtos.d.ts" />
    <Content Include="Views\Home\Hello.cshtml" />
    <Content Include="Views\Shared\_Layout.cshtml" />
    <Content Include="Web.config" />
    <Content Include="Web.Debug.config">
      <DependentUpon>Web.config</DependentUpon>
    </Content>
    <Content Include="Web.Release.config">
      <DependentUpon>Web.config</DependentUpon>
    </Content>
    <Content Include="Views\Web.config" />
  </ItemGroup>
  <ItemGroup>
    <Folder Include="App_Data\" />
    <Folder Include="Models\" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\..\src\ServiceStack.Api.OpenApi\ServiceStack.Api.OpenApi.csproj">
      <Project>{5e258282-86a6-4780-ab25-5e458f2e6f70}</Project>
      <Name>ServiceStack.Api.OpenApi</Name>
    </ProjectReference>
    <ProjectReference Include="..\..\src\ServiceStack.Client\ServiceStack.Client.csproj">
      <Project>{c43f583f-abde-4dd4-bbe3-66322817a6ad}</Project>
      <Name>ServiceStack.Client</Name>
    </ProjectReference>
    <ProjectReference Include="..\..\src\ServiceStack.Common\ServiceStack.Common.csproj">
      <Project>{982416db-c143-4028-a0c3-cf41892d18d3}</Project>
      <Name>ServiceStack.Common</Name>
    </ProjectReference>
    <ProjectReference Include="..\..\src\ServiceStack.Interfaces\ServiceStack.Interfaces.csproj">
      <Project>{55942102-033a-4da8-a6af-1db7b2f34a2d}</Project>
      <Name>ServiceStack.Interfaces</Name>
    </ProjectReference>
    <ProjectReference Include="..\..\src\ServiceStack.Mvc\ServiceStack.Mvc.csproj">
      <Project>{672f2dfe-4ee8-498b-b449-23e9e7f6961c}</Project>
      <Name>ServiceStack.Mvc</Name>
    </ProjectReference>
    <ProjectReference Include="..\..\src\ServiceStack.NetFramework\ServiceStack.NetFramework.csproj">
      <Project>{22f8d050-2c96-4993-b221-da16ebc61f31}</Project>
      <Name>ServiceStack.NetFramework</Name>
    </ProjectReference>
    <ProjectReference Include="..\..\src\ServiceStack.Server\ServiceStack.Server.csproj">
      <Project>{5a315f92-80d2-4c60-a5a4-22e027ac7e7e}</Project>
      <Name>ServiceStack.Server</Name>
    </ProjectReference>
    <ProjectReference Include="..\..\src\ServiceStack\ServiceStack.csproj">
      <Project>{680a1709-25eb-4d52-a87f-ee03ffd94baa}</Project>
      <Name>ServiceStack</Name>
    </ProjectReference>
    <ProjectReference Include="..\Check.ServiceInterface\Check.ServiceInterface.csproj">
      <Project>{9982317F-831C-478B-9CC3-57F888BCD97A}</Project>
      <Name>Check.ServiceInterface</Name>
    </ProjectReference>
    <ProjectReference Include="..\CheckHttpListener\CheckHttpListener.csproj">
      <Project>{F709A373-6FD5-4415-B2ED-2520EA7CC568}</Project>
      <Name>CheckHttpListener</Name>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup>
    <Content Include="Views\Home\Index.cshtml" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="Views\Home\About.cshtml" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="Views\Home\Contact.cshtml" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="MRootPage.md" />
  </ItemGroup>
  <ItemGroup>
    <TypeScriptCompile Include="TypeScript.dtos.ts" />
  </ItemGroup>
  <ItemGroup>
    <Service Include="{4A0DDDB5-7A95-4FBF-97CC-616D07737A77}" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\..\..\ServiceStack.Text\src\ServiceStack.Text\ServiceStack.Text.csproj" />
    <PackageReference Include="Microsoft.AspNet.Mvc" Version="5.2.7" />
    <PackageReference Include="Microsoft.AspNet.Mvc.FixedDisplayModes" Version="5.0.0" />
    <PackageReference Include="Microsoft.AspNet.Razor" Version="3.2.7" />
    <PackageReference Include="Microsoft.AspNet.WebApi" Version="5.2.7" />
    <PackageReference Include="Microsoft.AspNet.WebApi.Client" Version="5.2.7" />
    <PackageReference Include="Microsoft.AspNet.WebApi.Core" Version="5.2.7" />
    <PackageReference Include="Microsoft.AspNet.WebApi.WebHost" Version="5.2.7" />
    <PackageReference Include="Microsoft.AspNet.WebPages" Version="3.2.7" />
    <PackageReference Include="Microsoft.Bcl.Build" Version="1.0.21" />
    <PackageReference Include="Microsoft.CSharp" Version="4.7.0" />
    <PackageReference Include="Microsoft.Web.Infrastructure" Version="1.0.0" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.1" />
    <PackageReference Include="NUnit" Version="3.13.2" />
    <PackageReference Include="System.Buffers" Version="4.5.1" />
    <PackageReference Include="System.Memory" Version="4.5.4" />
    <PackageReference Include="System.Runtime.CompilerServices.Unsafe" Version="4.7.1" />
    <PackageReference Include="System.ValueTuple" Version="4.5.0" />
  </ItemGroup>
  <PropertyGroup>
    <VisualStudioVersion Condition="'$(VisualStudioVersion)' == ''">10.0</VisualStudioVersion>
    <VSToolsPath Condition="'$(VSToolsPath)' == ''">$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v$(VisualStudioVersion)</VSToolsPath>
  </PropertyGroup>
  <Import Project="$(MSBuildBinPath)\Microsoft.CSharp.targets" />
  <Import Project="$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v$(VisualStudioVersion)\TypeScript\Microsoft.TypeScript.targets" Condition="Exists('$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v$(VisualStudioVersion)\TypeScript\Microsoft.TypeScript.targets')" />
  <Import Project="$(VSToolsPath)\WebApplications\Microsoft.WebApplication.targets" Condition="'$(VSToolsPath)' != ''" />
  <Import Project="$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v10.0\WebApplications\Microsoft.WebApplication.targets" Condition="false" />
  <Target Name="MvcBuildViews" AfterTargets="AfterBuild" Condition="'$(MvcBuildViews)'=='true'">
    <AspNetCompiler VirtualPath="temp" PhysicalPath="$(WebProjectOutputDir)" />
  </Target>
  <ProjectExtensions>
    <VisualStudio>
      <FlavorProperties GUID="{349c5851-65df-11da-9384-00065b846f21}">
        <WebProjectProperties>
          <UseIIS>True</UseIIS>
          <AutoAssignPort>True</AutoAssignPort>
          <DevelopmentServerPort>49435</DevelopmentServerPort>
          <DevelopmentServerVPath>/</DevelopmentServerVPath>
          <IISUrl>http://localhost:49435/</IISUrl>
          <NTLMAuthentication>False</NTLMAuthentication>
          <UseCustomServer>False</UseCustomServer>
          <CustomServerUrl>
          </CustomServerUrl>
          <SaveServerSettingsInUserFile>False</SaveServerSettingsInUserFile>
        </WebProjectProperties>
      </FlavorProperties>
    </VisualStudio>
  </ProjectExtensions>
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target> -->
</Project>