﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace EvalJs.ServiceMsg
{
    public class LogOutputSocketProtocolMgr
    {
        private List<LogOutputSocketProtocol> m_list;

        public LogOutputSocketProtocolMgr()
        {
            this.m_list = new List<LogOutputSocketProtocol>();
        }

        public int Count()
        {
            return this.m_list.Count;
        }

        public LogOutputSocketProtocol ElementAt(int index)
        {
            return this.m_list.ElementAt(index);
        }

        public void Add(LogOutputSocketProtocol value)
        {
            this.m_list.Add(value);
        }

        public void Remove(LogOutputSocketProtocol value)
        {
            this.m_list.Remove(value);
        }
    }

    public class LogOutputSocketProtocol : BaseSocketProtocol
    {
        private LogFixedBuffer m_logFixedBuffer;

        public LogFixedBuffer LogFixedBuffer
        {
            get
            {
                return this.m_logFixedBuffer;
            }
        }

        public LogOutputSocketProtocol(AsyncSocketServer asyncSocketServer, AsyncSocketUserToken asyncSocketUserToken)
            : base(asyncSocketServer, asyncSocketUserToken)
        {
            this.m_socketFlag = "LogOutput";
            this.m_logFixedBuffer = new LogFixedBuffer();
            LogOutputSocketProtocolMgr logOutputSocketProtocolMgr = Service.AsyncSocketSvr.LogOutputSocketProtocolMgr;
            lock (logOutputSocketProtocolMgr)
            {
                Service.AsyncSocketSvr.LogOutputSocketProtocolMgr.Add(this);
            }
            this.SendResponse();
        }

        public override void Close()
        {
            LogOutputSocketProtocolMgr logOutputSocketProtocolMgr = Service.AsyncSocketSvr.LogOutputSocketProtocolMgr;
            lock (logOutputSocketProtocolMgr)
            {
                Service.AsyncSocketSvr.LogOutputSocketProtocolMgr.Remove(this);
            }
        }

        public override bool ProcessReceive(byte[] buffer, int offset, int count)
        {
            this.m_activeDT = DateTime.UtcNow;
            bool flag = count == 1;
            bool result;
            if (flag)
            {
                bool flag2 = buffer[0] == 27;
                result = (!flag2 && this.SendResponse());
            }
            else
            {
                result = this.SendResponse();
            }
            return result;
        }

        public override bool SendCallback()
        {
            bool result = base.SendCallback();
            bool flag = this.m_logFixedBuffer.DataCount > 0;
            if (flag)
            {
                result = base.DoSendBuffer(this.m_logFixedBuffer.FixedBuffer, 0, this.m_logFixedBuffer.DataCount);
                this.m_logFixedBuffer.Clear();
            }
            return result;
        }

        public bool InitiativeSend()
        {
            bool flag = !this.m_sendAsync;
            return !flag || this.SendCallback();
        }

        public bool SendResponse()
        {
            this.m_logFixedBuffer.WriteString("\r\nNET IOCP Demo Server, SQLDebug_Fan <EMAIL>, http://blog.csdn.net/SQLDebug_Fan\r\n");
            this.m_logFixedBuffer.WriteString("Press ESC to exit\r\n");
            return this.InitiativeSend();
        }
    }
}
