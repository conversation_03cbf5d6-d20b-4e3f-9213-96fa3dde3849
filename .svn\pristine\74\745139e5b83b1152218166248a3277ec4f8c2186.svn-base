﻿using CommonLib;
using log4net.Config;
using System;
using System.Collections.Generic;
using System.IO;
using System.ServiceProcess;
using System.Threading;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace Notice.Process.Console
{
    class Program
    {
        static void Main(string[] args)
        {
            //TestTime();
            //Set error handler
            SetApplicationErrorHandler();
            //Set Log4net基础信息
            SetLoggerContext();

            InitConfig();

            //RdsCacheHelper.NoticeQueue.Push(new NoticeQueueEntity()
            //{
            //    Body = "body",
            //    Subject = "标题",
            //    DtAdd = DateTime.Now,
            //    IsSendBySelf = false,
            //    MacCode = "123456",
            //    To = "<EMAIL>",
            //    NoticeType = NoticeType.邮件,
            //    QQ = "441221482",
            //}, 0, 1, 0);

            //RdsCacheHelper.NoticeQueue.EnqueueCacheMessage(new NoticeQueueEntity()
            //{
            //    Body = "body",
            //    Subject = "标题",
            //    DtAdd = DateTime.Now,
            //    Email = "<EMAIL>",
            //    Password = "Aa@qpzs01",
            //    IsSendBySelf = true,
            //    MacCode = "123456",
            //    MobileNo = "18217521743",
            //    To = "<EMAIL>",
            //    NoticeType = NoticeType.邮件,
            //    QQ = "441221482",
            //});

            //RdsCacheHelper.NoticeQueue.EnqueueCacheMessage(new NoticeQueueEntity()
            //{
            //    Body = "body",
            //    Subject = "标题",
            //    DtAdd = DateTime.Now,
            //    MacCode = "123456",
            //    MobileNo = "18217521743",
            //    NoticeType = NoticeType.电话,
            //    QQ = "441221482",
            //});

            //RdsCacheHelper.NoticeQueue.EnqueueCacheMessage(new NoticeQueueEntity()
            //{
            //    Body = "12345",
            //    Subject = "标题",
            //    DtAdd = DateTime.Now,
            //    MacCode = "123456",
            //    MobileNo = "18217521743",
            //    NoticeType = NoticeType.短信,
            //    QQ = "441221482",
            //});

            //RdsCacheHelper.NoticeQueue.EnqueueCacheMessage(new NoticeQueueEntity()
            //{
            //    Body = "body",
            //    DtAdd = DateTime.Now,
            //    MacCode = "123456",
            //    NoticeType = NoticeType.QQ,
            //    QQ = "441221482",
            //});

            System.Net.ServicePointManager.ServerCertificateValidationCallback =
                (sender, certificate, chain, sslPolicyErrors) => true;

            if (System.Diagnostics.Debugger.IsAttached)
            {
                LogHelper.Log.Info("Debug模式");
                if (ConfigHelper.NMaxCodeProcessThread > 1)
                {
                    Parallel.For(0, ConfigHelper.NMaxCodeProcessThread, item =>
                    {
                        ProcessFrpc.StartProcess();
                    });
                }
                else
                {
                    ProcessFrpc.StartProcess();
                }
                System.Console.ReadLine();
            }
            else
            {
                LogHelper.Log.Info("Service模式");
                ServiceBase[] ServicesToRun;
                ServicesToRun = new ServiceBase[]
                {
                    new EngineService()
                };
                ServiceBase.Run(ServicesToRun);
            }

        }

        static void InitConfig()
        {
            ConfigHelper.InitConfig();

            ConfigHelper.InitThread();
        }

        private static void SetApplicationErrorHandler()
        {
            Application.ThreadException += new ThreadExceptionEventHandler(Application_ThreadException);
            Application.SetUnhandledExceptionMode(UnhandledExceptionMode.CatchException);
            AppDomain.CurrentDomain.UnhandledException += new UnhandledExceptionEventHandler(CurrentDomain_UnhandledException);
        }

        private static void SetLoggerContext()
        {
            string fileInfo = Path.Combine(AppDomain.CurrentDomain.SetupInformation.ApplicationBase, "Log4net.config");
            XmlConfigurator.ConfigureAndWatch(new FileInfo(fileInfo));
            ////添加ServiceStack的日志输出，从而将ServiceStack的错误打印出来
            //ServiceStack.Logging.LogManager.LogFactory = new ServiceStack.Logging.Log4Net.Log4NetFactory();
        }

        private static void Application_ThreadException(object sender, ThreadExceptionEventArgs t)
        {
            //if (ApplicationStartInfo.StartMode != StartModes.Service)
            //    if (MessageBox.Show(string.Format("应用发生了错误，请关闭后重试。\r\n\r\n错误消息为\"{0}\"", t.Exception.Message), "系统错误", MessageBoxButtons.OK, MessageBoxIcon.Error) == DialogResult.OK)
            //    {
            //        Application.UseWaitCursor = false;
            //        Application.Exit();
            //    }
        }

        private static void CurrentDomain_UnhandledException(object sender, UnhandledExceptionEventArgs e)
        {
            //Exception ex = (Exception)e.ExceptionObject;
            //_logger.Error("Get Application Current Domain Unhandle Thread Exception", ex);
            //if (ApplicationStartInfo.StartMode != StartModes.Service)
            //    if (MessageBox.Show(string.Format("应用发生了错误，请关闭后重试。\r\n\r\n错误消息为\"{0}\"", ex.Message), "系统错误", MessageBoxButtons.OK, MessageBoxIcon.Error) == DialogResult.OK)
            //    {
            //        Application.UseWaitCursor = false;
            //        Application.Exit();
            //    }
        }
    }
    public enum StartModes
    {
        Service,
        Form
    }
    public static class ApplicationStartInfo
    {
        static StartModes _startMode = StartModes.Form;
        static IDictionary<string, string> _argument;
        static string _applicationName;
        static ApplicationStartInfo()
        {
            _argument = new Dictionary<string, string>();

            string[] args = Environment.GetCommandLineArgs();
            _applicationName = args[0];

            for (int i = 1; i < args.Length; i++)
            {
                string arg = args[i].Trim().Replace("/", "");
                switch (arg.ToLower())
                {
                    case "form":
                        _startMode = StartModes.Form;
                        break;
                    default:
                        string[] command = arg.Split('=');
                        if (command.Length == 2)
                        {
                            _argument.Add(command[0].ToLower(), command[1]);
                        }
                        break;
                }
            }
        }

        public static StartModes StartMode
        {
            get { return _startMode; }
        }

        public static IDictionary<string, string> Argument
        {
            get { return _argument; }
        }

        public static string ApplicationName
        {
            get { return _applicationName; }
        }
    }
}
