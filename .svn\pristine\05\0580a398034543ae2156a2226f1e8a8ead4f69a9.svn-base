﻿using System.Collections.Generic;
using Enterprise.Framework.Redis;

namespace CommonLib
{
    public class ImageCache : RedisCacheObject<List<ImageEntity>>
    {
        protected override string CurrentObject_KeyPrefix
        {
            get { return "ImageCache"; }
        }

        protected override string Object_DBName
        {
            get { return "OPS_Cache"; }
        }

        public void Add(string strName, List<ImageEntity> lstMsg)
        {
            if (KeyExists(strName))
                Remove(strName);
            Insert(strName, lstMsg);
        }
    }
}