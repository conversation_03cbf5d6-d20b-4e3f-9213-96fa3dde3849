﻿<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <PackageId>ServiceStack.Interfaces.Core</PackageId>
        <AssemblyName>ServiceStack.Interfaces</AssemblyName>
        <RootNamespace>ServiceStack.Interfaces</RootNamespace>
        <TargetFrameworks>netstandard2.0;net6.0</TargetFrameworks>
        <Title>ServiceStack.Interfaces .NET Standard 2.0</Title>
        <PackageDescription>
            Lightweight and implementation-free interfaces for DTO's, providers and adapters.
        </PackageDescription>
        <PackageTags>ServiceStack;Common;Framework;Clients;ServiceClients;Gateway</PackageTags>
    </PropertyGroup>
    <PropertyGroup Condition=" '$(TargetFramework)' == 'netstandard2.0' ">
        <DefineConstants>$(DefineConstants);NETCORE;NETSTANDARD2_0</DefineConstants>
    </PropertyGroup>
    <PropertyGroup Condition=" '$(TargetFramework)' == 'net6.0' ">
        <DefineConstants>$(DefineConstants);NETCORE;NET6_0;NET6_0_OR_GREATER</DefineConstants>
    </PropertyGroup>

    <ItemGroup Condition=" '$(TargetFramework)' == 'netstandard2.0' ">
        <PackageReference Include="System.Threading.Tasks.Extensions" Version="4.5.4" />
        <PackageReference Include="System.Runtime.Serialization.Primitives" Version="4.3.0" />
        <PackageReference Include="Microsoft.Bcl.AsyncInterfaces" Version="5.0.0" />
    </ItemGroup>

    <ItemGroup Condition=" '$(TargetFramework)' == 'net6.0' ">
    </ItemGroup>

</Project>
