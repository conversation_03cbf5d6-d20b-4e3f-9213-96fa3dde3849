﻿using CommonLib;
using System.Collections.Generic;
using System.Security.Cryptography;
using System.Text;
using System.Web;

namespace TransOcr
{
    /// <summary>
    /// 搜狗翻译
    /// view-source:https://fanyi.sogou.com/
    /// ,"secretCode":8511813095152,"uuid":"718492ae-1472-4a31-83aa-9ed8c6147b7f"
    /// ,"approveToken":"742ECEDBB3E539B91117BE7FB411B736125BF4F25F657DE3"
    /// </summary>
    public class SouGouTxtFanYiRec : BaseOcrRec
    {
        public SouGouTxtFanYiRec()
        {
            OcrGroup = OcrGroupType.搜狗;
            OcrType = TransOcrType.搜狗文本翻译;
            MaxExecPerTime = 30;

            LstJsonPreProcessArray = new List<object>() { "data", "translate" };
            StrResultJsonSpilt = "dit";

            AllowUploadFileTypes = new List<string>() { "txt" };
            InitLanguage();

            LstForbidArray = new Dictionary<string, List<string>>
            {
                { "Translate API: unknown error", new List<string>() },
                { ",\"status\":\"-1\"", new List<string>() }
            };
        }

        #region 支持的语言

        private void InitLanguage()
        {
            TransLanguageDic = new Dictionary<TransLanguageTypeEnum, string>();
            TransLanguageDic.Add(TransLanguageTypeEnum.自动, "auto");
            TransLanguageDic.Add(TransLanguageTypeEnum.中文, "zh");
            TransLanguageDic.Add(TransLanguageTypeEnum.英文, "en");
            TransLanguageDic.Add(TransLanguageTypeEnum.日语, "ja");
            TransLanguageDic.Add(TransLanguageTypeEnum.韩语, "ko");
            TransLanguageDic.Add(TransLanguageTypeEnum.西班牙语, "es");
            TransLanguageDic.Add(TransLanguageTypeEnum.法语, "fr");
            TransLanguageDic.Add(TransLanguageTypeEnum.泰语, "th");
            TransLanguageDic.Add(TransLanguageTypeEnum.阿拉伯语, "ar");
            TransLanguageDic.Add(TransLanguageTypeEnum.俄语, "ru");

            TransLanguageDic.Add(TransLanguageTypeEnum.葡萄牙语, "pt");
            TransLanguageDic.Add(TransLanguageTypeEnum.德语, "de");
            TransLanguageDic.Add(TransLanguageTypeEnum.意大利语, "it");
            TransLanguageDic.Add(TransLanguageTypeEnum.希腊语, "el");
            TransLanguageDic.Add(TransLanguageTypeEnum.荷兰语, "nl");
            TransLanguageDic.Add(TransLanguageTypeEnum.波兰语, "pl");
            TransLanguageDic.Add(TransLanguageTypeEnum.保加利亚语, "bg");
            TransLanguageDic.Add(TransLanguageTypeEnum.爱沙尼亚语, "et");
            TransLanguageDic.Add(TransLanguageTypeEnum.丹麦语, "da");
            TransLanguageDic.Add(TransLanguageTypeEnum.芬兰语, "fi");
            TransLanguageDic.Add(TransLanguageTypeEnum.捷克语, "cs");
            TransLanguageDic.Add(TransLanguageTypeEnum.罗马尼亚语, "ro");
            TransLanguageDic.Add(TransLanguageTypeEnum.斯洛文尼亚语, "sl");
            TransLanguageDic.Add(TransLanguageTypeEnum.瑞典语, "sv");
            TransLanguageDic.Add(TransLanguageTypeEnum.匈牙利语, "hu");
            TransLanguageDic.Add(TransLanguageTypeEnum.越南语, "vi");
        }

        #endregion

        private string sougouAppid = null;
        private string sougouKey = null;
        private string strCookie = null;

        protected override string GetHtml(OcrContent content)
        {
            InitTokens();
            if (string.IsNullOrEmpty(sougouKey) || string.IsNullOrEmpty(sougouAppid))
            {
                return null;
            }
            string from = "", to = "";
            InitLanguage(content, ref from, ref to);

            string txtSignKey = string.Concat(new string[] { from, to, content.strBase64.Trim(), sougouKey });
            var txtSign = GetSign(txtSignKey);
            string strPost = string.Concat(new string[]
            {
                "from="+ from,
                "&to="+to,
                "&text="+HttpUtility.UrlEncode(content.strBase64.Trim()),
                "&client=pc",
                "&fr=browser_pc",
                "&pid=sogou-dict-vr",
                "&dict=false",
                "&word_group=false",
                "&second_query=false",
                "&uuid="+sougouAppid,
                "&needQc=1",
                "&s=" + txtSign
            });
            string strTmp = WebClientSyncExt.GetHtml("https://fanyi.sogou.com/api/transpc/text/result"//"https://fanyi.sogou.com/reventondc/translateV3"
                , strCookie
                , strPost, ExecTimeOutSeconds);
            if (!string.IsNullOrEmpty(strTmp)
                && (strTmp.Contains("\"errorCode\": \"10\"") || strTmp.Contains("\"errorCode\":\"10\"") || strTmp.Contains("\"errorCode\": \"20\"") || strTmp.Contains("\"errorCode\":\"20\"")))
            {
                //10:Token失效
                //20:爬虫
                strCookie = null;
                sougouAppid = null;
                sougouKey = null;
                strTmp = null;
            }
            return strTmp;
        }

        private string GetSign(string strSource)
        {
            //autoen测试128511813095152
            //e6833e5b00abc534dbe46a258574bd14
            string result = null;
            if (!string.IsNullOrEmpty(strSource))
            {
                MD5 md = MD5.Create();
                byte[] array = md.ComputeHash(Encoding.UTF8.GetBytes(strSource));
                StringBuilder stringBuilder = new StringBuilder();
                for (int i = 0; i < array.Length; i++)
                {
                    stringBuilder.Append(array[i].ToString("x2"));
                }
                result = stringBuilder.ToString();
            }
            return result;
        }


        private const string strAppSecretKey = "\"secretCode\":";
        private const string strAppIdKey = "\"uuid\":\"";

        private void InitTokens()
        {
            if (string.IsNullOrEmpty(sougouKey))
            {
                strCookie = "";
                var html = WebClientSyncExt.GetHtml("https://fanyi.sogou.com", ref strCookie, "", "https://fanyi.sogou.com", ExecTimeOutSeconds);
                if (!string.IsNullOrEmpty(html) && html.Contains(strAppSecretKey))
                {
                    sougouKey = CommonHelper.SubString(html, strAppSecretKey, ",");
                    sougouAppid = CommonHelper.SubString(html, strAppIdKey, "\"");
                }
            }
        }

        public override void Reset()
        {
            sougouKey = "";
            base.Reset();
        }

    }
}