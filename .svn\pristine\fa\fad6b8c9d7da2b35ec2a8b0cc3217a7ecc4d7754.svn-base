﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Net;
using System.Text;
using System.Web;
using System.Web.Script.Serialization;
using CommonLib;

namespace Account.Web
{
    public partial class Code : System.Web.UI.Page
    {
        protected void Page_Load(object sender, EventArgs e)
        {
            string strOp = BoxUtil.GetStringFromObject(Request.QueryString["op"]);
            if (!string.IsNullOrEmpty(strOp))
            {
                DoOperate(strOp);
            }
            Response.End();
        }

        /// <summary>
        /// 通过字符串获取MD5值，返回32位字符串。
        /// </summary>
        /// <param name="str"></param>
        /// <returns></returns>
        public string GetMD5String(string str)
        {
            using (var md5 = System.Security.Cryptography.MD5.Create())
            {
                byte[] data2 = md5.ComputeHash(System.Text.Encoding.UTF8.GetBytes(str));

                return GetbyteToString(data2);
            }
        }

        private string GetbyteToString(byte[] data)
        {
            var sb = new System.Text.StringBuilder();
            for (int i = 0; i < data.Length; i++)
            {
                sb.Append(data[i].ToString("x2"));
            }
            data = null;
            return sb.ToString();
        }

        private static List<string> lstMsg = new List<string>();
        private static object objLock = "";
        private static JavaScriptSerializer JavaScriptSerializer = new JavaScriptSerializer();

        private void DoOperate(string strOP)
        {
            var code = new CodeEntity
            {
                StrAppCode = BoxUtil.GetStringFromObject(Request.QueryString["app"])
            };
            if (strOP == "pay")
            {
                //http://ocr.oldfish.cn:9090/testOrder?isHtml=1&type=0&price=0.01&return_url=http%3A%2F%2Focr.oldfish.cn%3A2020%2FPay.aspx&notify_url=http%3A%2F%2Focr.oldfish.cn%3A2020%2FPay.ashx&remark=OCR%E5%8A%A9%E6%89%8B-%E4%B8%80%E5%B9%B4%E4%B8%93%E4%B8%9A%E7%89%88&param=***********
                var account = BoxUtil.GetStringFromObject(Request.QueryString["account"]);
                var orderNo = BoxUtil.GetStringFromObject(Request.QueryString["orderNo"]);
                var remark = HttpUtility.UrlDecode(BoxUtil.GetStringFromObject(Request.QueryString["remark"]));
                string price = "";

                if (!string.IsNullOrEmpty(remark))
                {
                    var userType = GetCanRegUserTypes().FirstOrDefault(p => remark.EndsWith(p.Type.ToString()));
                    if (userType != null && userType.UserChargeType.Count > 0)
                    {
                        var priceType = userType.UserChargeType.FirstOrDefault(p => remark.StartsWith(p.Name)) ??
                                        userType.UserChargeType.FirstOrDefault();
                        if (priceType != null)
                        {
                            price = priceType.Price.ToString("F0");
                            remark = string.Format("{0}{1}", priceType.Name, userType.Type);
                        }
                    }
                }
                LogHelper.Log.Info(string.Format("price:{0}, orderNo:{1}, remark:{2}, account:{3}", price, orderNo, Request.QueryString["remark"], account));
                var result = PayUtil.GetPayUrl(price, orderNo, remark, account);
                Response.Write(result);
            }
            else if (strOP == "msg")
            {
                var msg = "";
                lock (objLock)
                {
                    if (lstMsg.Count > 0)
                    {
                        msg = lstMsg[0];
                        lstMsg.Remove(msg);
                    }
                }
                if (!string.IsNullOrEmpty(msg))
                {
                    Response.Write(msg);
                }
                else
                {
                    Response.Write("no");
                }
            }
            else if (strOP == "reg")
            {
                code.StrAppCode = BoxUtil.GetStringFromObject(Request.QueryString["account"]);
                if (CodeHelper.IsExitsCode(code.StrAppCode))
                {
                    Response.Write("当前账号已注册，请返回重新登录！");
                }
                else
                {
                    var isEmail = BoxUtil.IsEmail(code.StrAppCode);
                    var isMobile = BoxUtil.IsMobile(code.StrAppCode);
                    if (isEmail || isMobile)
                    {
                        code.StrPwd = BoxUtil.GetStringFromObject(Request.QueryString["pwd"]);
                        if (string.IsNullOrEmpty(code.StrPwd))
                        {
                            Response.Write("密码不能为空！");
                        }
                        else
                        {
                            var validateCode = BoxUtil.GetStringFromObject(Request.QueryString["code"]);
                            if (string.IsNullOrEmpty(validateCode))
                            {
                                Response.Write("验证码不能为空！");
                            }
                            else
                            {
                                //使用regex进行格式设置 至少有数字、大小写字母，最少8个字符、最长30个字符
                                if (!System.Text.RegularExpressions.Regex.IsMatch(code.StrPwd, @"[0-9A-Za-z].{5,15}"))//判断密码格式是否符合要求
                                {
                                    Response.Write("密码必须为6-15位的数字或大小写字母！");
                                }
                                else
                                {
                                    code.StrNickName = BoxUtil.GetStringFromObject(Request.QueryString["nick"]);
                                    if (code.StrNickName.Length < 2 || code.StrNickName.Length > 20 || !new System.Text.RegularExpressions.Regex(@"^([\u4E00-\uFA29]|[\uE7C7-\uE7F3]|[a-zA-Z0-9_])").IsMatch(code.StrNickName))
                                    {
                                        Response.Write("昵称为2-15位的中英文数字字母或下划线！");
                                    }
                                    else
                                    {
                                        code.DtReg = ServerTime.LocalTime;
                                        if (isEmail && DisposeEmailHelper.IsDisposeEmail(code.StrAppCode))
                                        {
                                            code.StrType = UserTypeEnum.体验版.ToString();
                                            code.DtExpire = code.DtReg;
                                        }
                                        else
                                        {
                                            if (ConfigHelper.IsRegToGeRen)
                                            {
                                                code.StrType = UserTypeEnum.个人版.ToString();
                                                code.DtExpire = code.DtReg.AddDays(ConfigHelper.NRegSendDays);
                                            }
                                            else if (ConfigHelper.IsRegToProfessional)
                                            {
                                                code.StrType = UserTypeEnum.专业版.ToString();
                                                code.DtExpire = code.DtReg.AddDays(ConfigHelper.NRegSendDays);
                                            }
                                            else
                                            {
                                                code.StrType = UserTypeEnum.体验版.ToString();
                                                code.DtExpire = code.DtReg;
                                            }
                                        }

                                        code.NMaxLogin = 1;
                                        code.NMaxWindow = 1;
                                        if (string.IsNullOrEmpty(code.StrNickName))
                                        {
                                            code.StrNickName = code.StrAppCode;
                                        }
                                        var validateMd5 = GetMD5String(code.StrAppCode + ServerTime.LocalTime.Date.ToString("yyyy-MM-dd") + "OCRREG").ToUpper();
                                        if (validateMd5.Contains(validateCode?.ToUpper()))
                                        {
                                            code.StrPwd = GetMD5String(code.StrPwd + "OCRREG").ToUpper();
                                            Response.Write("True");
                                            Add(code);
                                        }
                                        else
                                        {
                                            Response.Write("验证码错误！");
                                        }
                                    }
                                }

                            }
                        }
                    }
                    else
                    {
                        Response.Write("账号格式不正确，必须为手机号或者邮箱！");
                    }
                }
            }
            else if (strOP == "heart")
            {
                var uid = Request.Headers["uid"];
                if (string.IsNullOrEmpty(uid))
                {
                    Response.Write("True");
                }
                else
                {
                    var token = BoxUtil.GetStringFromObject(Request.Headers["token"]);
                    var account = BoxUtil.GetStringFromObject(Request.Headers["app"]);
                    token = string.IsNullOrEmpty(token) ? uid : token;
                    account = string.IsNullOrEmpty(account) ? RdsCacheHelper.LstAccountCache.GetTestUserName(token) : account;

                    var success = RdsCacheHelper.LstAccountCache.HeartBeat(account, token);
                    try
                    {
                        UserData data = new UserData()
                        {
                            Account = account,
                            Token = Request.Headers["uid"],
                            DtLast = ServerTime.DateTime,
                            Ip = GetIPAddress(),
                            Mac = Request.Headers["mac"]
                        };
                        if (!string.IsNullOrEmpty(data.Mac))
                        {
                            data.Mac = HttpUtility.UrlDecode(data.Mac);
                        }
                        CodeHelper.AddUserData(data);
                    }
                    catch (Exception oe)
                    {
                        LogHelper.Log.Error("Heart失败", oe);
                    }
                    Response.Write(success.ToString());
                }
            }
            else if (strOP == "frpc")
            {
                try
                {
                    var strData = Request.Form["frpc"];
                    if (!string.IsNullOrEmpty(strData))
                    {
                        var data = CodeProcessHelper.JavaScriptSerializer.Deserialize<List<FrpcEntity>>(strData);
                        CodeHelper.AddOrUpdateFrpc(data);
                    }
                }
                catch (Exception oe)
                {
                    LogHelper.Log.Error("Frpc保存失败", oe);
                }
                try
                {
                    var dtTmp = new DataTable();
                    var mainSite = CodeHelper.GetFastSite(ref dtTmp);
                    var strTmp = new JavaScriptSerializer().Serialize(mainSite).Replace(",\"IsDefault\":false", "").Replace(",\"Host\":null", "");
                    QiNiuUpload.UploadFile(strTmp, "newSite.json");
                }
                catch (Exception oe)
                {
                    LogHelper.Log.Error("生成Site失败:" + oe.Message);
                }
                Response.Write("True");
            }
            else if (strOP == "count")
            {
                var account = BoxUtil.GetStringFromObject(string.IsNullOrEmpty(Request.Headers["app"]) ? Request.QueryString["account"] : Request.Headers["app"]);
                if (string.IsNullOrEmpty(account))
                {
                    var identity = Request.Headers["uid"];
                    if (!string.IsNullOrEmpty(identity))
                    {
                        account = RdsCacheHelper.LstAccountCache.GetTestUserName(identity);
                    }
                }

                var codeCount = new UserCodeCount { Account = account };
                if (!string.IsNullOrEmpty(account))
                {
                    codeCount.TodayCount = RdsCacheHelper.CodeRecordCache.GetUserCodeInfo(account).TodayCount;

                    var userLoginInfo = RdsCacheHelper.LstAccountCache.GetUserInfo(account);
                    if (userLoginInfo != null)
                    {
                        var userType = UserTypeHelper.GetUserType(userLoginInfo.UserType);
                        codeCount.LimitCount = userType.LimitPerTokenCount > 0
                            ? userType.LimitPerTokenCount
                            : userType.LimitPerDayCount;
                    }
                }
                Response.Write(JavaScriptSerializer.Serialize(codeCount));
            }
            else if (strOP == "regtype")
            {
                var lstUserType = GetCanRegUserTypes();
                Response.Write(JavaScriptSerializer.Serialize(lstUserType));
            }
            else if (strOP == "login")
            {
                code.StrAppCode = BoxUtil.GetStringFromObject(Request.QueryString["account"]);
                code.StrPwd = BoxUtil.GetStringFromObject(Request.QueryString["pwd"]);

                var isEmail = BoxUtil.IsEmail(code.StrAppCode);
                var isMobile = BoxUtil.IsMobile(code.StrAppCode);
                if (isEmail || isMobile)
                {
                    var user = CodeHelper.GetCodeByAccountId(code.StrAppCode);

                    if (user == null)
                    {
                        Response.Write("用户名不存在，请先注册！");
                    }
                    else
                    {
                        var pwd = GetMD5String(code.StrPwd + "OCRREG").ToUpper();
                        if (Equals(pwd, user.StrPwd))
                        {
                            var loginInfo = new UserLoginInfo
                            {
                                Account = user.StrAppCode,
                                NickName = user.StrNickName,
                                UserType = (UserTypeEnum)Enum.Parse(typeof(UserTypeEnum), user.StrType),
                                Remark = user.StrRemark,
                                DtExpired = user.DtExpire,
                                DtReg = user.DtReg,
                                DtLogin = ServerTime.LocalTime,
                                LstToken = RdsCacheHelper.LstAccountCache.GetUserInfo(user.StrAppCode)?.LstToken
                            };
                            if (loginInfo.LstToken == null)
                            {
                                loginInfo.LstToken = new List<TokenEntity>();
                            }
                            if (user.IsExpired)
                            {
                                //其他版本(非个人版)到期，自动降级为个人版
                                if (!string.IsNullOrEmpty(user.StrRemark) && user.StrRemark.Contains("版") &&
                                    !user.StrRemark.Contains(UserTypeEnum.个人版.ToString()))
                                {
                                    code.DtExpire = ServerTime.DateTime.AddYears(100);
                                    code.StrType = UserTypeEnum.个人版.ToString();

                                    Add(code);

                                    loginInfo.DtExpired = code.DtExpire;
                                    loginInfo.UserType = UserTypeEnum.个人版;
                                }
                                else
                                {
                                    //否则，降级为体验版
                                    loginInfo.UserType = UserTypeEnum.体验版;
                                    if (!Equals(code.StrType, loginInfo.UserType.ToString()))
                                    {
                                        code.StrType = loginInfo.UserType.ToString();
                                        Add(code);
                                    }
                                }
                            }

                            //if (isEmail && !user.IsExpired && string.IsNullOrEmpty(user.StrRemark))
                            //{
                            //    user.StrRemark = "Validate:" + ServerTime.DateTime.AddHours(1);
                            //}

                            var identity = Request.Headers["uid"];
                            if (string.IsNullOrEmpty(identity))
                            {
                                identity = Guid.NewGuid().ToString().Replace("-", "").ToLower();
                            }
                            else
                            {
                                loginInfo.LstToken.RemoveAll(p => Equals(p.Token, identity));
                            }
                            var userTypeInfo = UserTypeHelper.GetUserInfo(loginInfo.UserType);
                            while (loginInfo.LstToken.Count >= userTypeInfo.MaxLoginCount)
                            {
                                loginInfo.LstToken = loginInfo.LstToken.OrderBy(p => p.DtExpired).ToList();
                                loginInfo.LstToken.RemoveAt(0);
                            }

                            loginInfo.UserTypeName = loginInfo.UserType.ToString();
                            loginInfo.IsSetOtherResult = userTypeInfo.IsSetOtherResult;
                            loginInfo.IsSupportBatch = userTypeInfo.IsSupportBatch;
                            loginInfo.IsSupportMath = userTypeInfo.IsSupportMath;
                            loginInfo.IsSupportImageFile = userTypeInfo.IsSupportImageFile;
                            loginInfo.IsSupportDocFile = userTypeInfo.IsSupportDocFile;
                            loginInfo.IsSupportTable = userTypeInfo.IsSupportTable;
                            loginInfo.IsSupportTxt = userTypeInfo.IsSupportTxt;
                            loginInfo.IsSupportVertical = userTypeInfo.IsSupportVertical;
                            loginInfo.IsSupportTranslate = userTypeInfo.IsSupportTranslate;
                            loginInfo.MaxUploadSize = userTypeInfo.MaxUploadSize;
                            loginInfo.PerTimeSpanExecCount = userTypeInfo.PerTimeSpanExecCount;
                            loginInfo.PerTimeSpan = userTypeInfo.PerTimeSpan;
                            loginInfo.LimitPerDayCount = userTypeInfo.LimitPerDayCount;
                            loginInfo.IsSupportPassage = userTypeInfo.IsSupportPassage;
                            loginInfo.IsSupportLocalOcr = userTypeInfo.IsSupportLocalOcr;

                            var token = new TokenEntity
                            {
                                Token = identity,
                                DtExpired = ServerTime.LocalTime.AddHours(1)
                            };
                            loginInfo.Token = token.Token;
                            loginInfo.LstToken.Add(token);

                            RdsCacheHelper.LstAccountCache.InsertOrUpdateUser(loginInfo);

                            CodeHelper.UpdateLastLogin(new List<string> { code.StrAppCode });

                            //不需要返回给用户
                            loginInfo.LstToken = null;
                            Response.Write("True|" + JavaScriptSerializer.Serialize(loginInfo));
                        }
                        else
                        {
                            Response.Write("用户名或密码错误！");
                        }
                    }
                }
                else
                {
                    Response.Write("用户名为手机号或邮箱，请重试！");
                }
            }
            else if (strOP == "resetpwd")
            {
                code.StrAppCode = BoxUtil.GetStringFromObject(Request.QueryString["account"]);
                if (!CodeHelper.IsExitsCode(code.StrAppCode))
                {
                    Response.Write("当前账号不存在，请先注册账号！");
                }
                else
                {
                    var isEmail = BoxUtil.IsEmail(code.StrAppCode);
                    var isMobile = BoxUtil.IsMobile(code.StrAppCode);
                    if (isEmail || isMobile)
                    {
                        code.StrPwd = BoxUtil.GetStringFromObject(Request.QueryString["pwd"]);
                        if (string.IsNullOrEmpty(code.StrPwd))
                        {
                            Response.Write("新密码不能为空！");
                        }
                        else
                        {
                            var validateCode = BoxUtil.GetStringFromObject(Request.QueryString["code"]);
                            if (GetMD5String(code.StrAppCode + ServerTime.LocalTime.Date.ToString("yyyy-MM-dd") + "OCRREGForgetPwd").ToUpper().Contains(validateCode?.ToUpper()))
                            {
                                code.StrPwd = GetMD5String(code.StrPwd + "OCRREG").ToUpper();
                                UpdateCodeRemark(code.StrAppCode, code.StrPwd);
                            }
                            else
                            {
                                Response.Write("验证码错误！");
                            }
                        }
                    }
                    else
                    {
                        Response.Write("账号格式不正确，必须为手机号或者邮箱！");
                    }
                }
            }
            else if (strOP == "resetnickname")
            {
                code.StrAppCode = BoxUtil.GetStringFromObject(Request.QueryString["account"]);
                if (!CodeHelper.IsExitsCode(code.StrAppCode))
                {
                    Response.Write("当前账号不存在，请先注册账号！");
                }
                else
                {
                    var nickName = Request.QueryString["nick"];
                    if (string.IsNullOrEmpty(nickName) || nickName.Length < 2 || nickName.Length > 20 || !new System.Text.RegularExpressions.Regex(@"^([\u4E00-\uFA29]|[\uE7C7-\uE7F3]|[a-zA-Z0-9_])").IsMatch(nickName))
                    {
                        Response.Write("昵称为2-15位的中英文数字字母或下划线！");
                    }
                    else
                    {
                        UpdateCodeNickName(code.StrAppCode, nickName);
                    }
                }
            }
            else
            {
                switch (strOP)
                {
                    case "del":
                        Del(code);
                        break;
                    default:
                        break;
                }
            }
        }

        private const string StrLocalIp = "127.0.0.1";
        private const string StrUnknowIp = "unknown";
        /// <summary>
        /// 获取客户端IP地址
        /// </summary>
        /// <returns>若失败则返回回送地址</returns>
        private string GetIPAddress()
        {
            //如果客户端使用了代理服务器，则利用HTTP_X_FORWARDED_FOR找到客户端IP地址
            string userHostAddress = HttpContext.Current.Request.ServerVariables["HTTP_X_FORWARDED_FOR"]?.Replace(" ", "")
                .Replace(StrLocalIp + ",", "").Replace("," + StrLocalIp, "").TrimStart(',').TrimEnd(',').Trim();
            if (!string.IsNullOrEmpty(userHostAddress) && userHostAddress.Contains(","))
            {
                userHostAddress = userHostAddress.Split(',')[0].Trim();
            }
            //否则直接读取REMOTE_ADDR获取客户端IP地址
            if (string.IsNullOrEmpty(userHostAddress) || userHostAddress.ToLower() == StrUnknowIp || userHostAddress.ToLower() == StrLocalIp)
            {
                userHostAddress = HttpContext.Current.Request.ServerVariables["REMOTE_ADDR"];
            }
            //前两者均失败，则利用Request.UserHostAddress属性获取IP地址，但此时无法确定该IP是客户端IP还是代理IP
            if (string.IsNullOrEmpty(userHostAddress) || userHostAddress.ToLower() == StrUnknowIp || userHostAddress.ToLower() == StrLocalIp)
            {
                userHostAddress = HttpContext.Current.Request.UserHostAddress;
            }
            return userHostAddress;
        }

        internal class UserCodeCount
        {
            public string Account { get; set; }

            public long TodayCount { get; set; }

            public long LimitCount { get; set; }
        }

        public static List<UserType> GetCanRegUserTypes()
        {
            var lstUserType = UserTypeHelper.GetCanRegUserTypes();
            lstUserType.ForEach(p =>
            {
                var lstChargeType = new List<ChargeViewToUser>();
                p.ChargeTypes.ForEach(q =>
                {
                    string strDesc = "";
                    var price = q.GetPrice(p.PerPrice, ref strDesc);
                    var charge = new ChargeViewToUser()
                    {
                        Name = q.Name,
                        Desc = strDesc,
                        Price = (double)price,
                        IsDefault = q.IsDefault,
                        Tag = q.Tag,
                    };
                    lstChargeType.Add(charge);
                });
                p.UserChargeType = lstChargeType;
            });
            return lstUserType;
        }

        private void Add(CodeEntity code)
        {
            if (CodeHelper.AddOrUpdateCode(code))
            {
                CommonLib.RdsCacheHelper.LstAccountCache.Remove(code.StrAppCode);
                Response.Write("添加成功！");
            }
            else
            {
                Response.Write("添加失败！");
            }
        }

        private void UpdateCodeRemark(string code, string pwd)
        {
            if (CodeHelper.UpdateCodePwd(code, pwd))
            {
                Response.Write("True");
            }
            else
            {
                Response.Write("更新失败，请稍后重试！");
            }
        }

        private void UpdateCodeNickName(string code, string nickName)
        {
            if (CodeHelper.UpdateCodeNickName(code, nickName))
            {
                Response.Write("True");
            }
            else
            {
                Response.Write("更新失败，请稍后重试！");
            }
        }

        private void Del(CodeEntity code)
        {
            if (CodeHelper.DelByAppCode(code))
            {
                Response.Write("删除成功！");
            }
            else
            {
                Response.Write("删除失败！");
            }
        }
    }

    [Serializable]
    public class UserEntity
    {
        public string Account { get; set; }

        public string UserType { get; set; }

        public string NickName { get; set; }

        public string Remark { get; set; }

        public DateTime DtReg { get; set; }

        public DateTime DtExpired { get; set; }
        public string Token { get; internal set; }
    }
}