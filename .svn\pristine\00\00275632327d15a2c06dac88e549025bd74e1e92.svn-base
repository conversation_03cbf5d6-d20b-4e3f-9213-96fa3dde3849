﻿using CommonLib;
using System.Collections.Generic;

namespace HanZiOcr
{
    public abstract class BaseOcrRec : BaseRec
    {
        private HanZiOcrType _OcrType;

        public new HanZiOcrType OcrType
        {
            get { return _OcrType; }
            set
            {
                _OcrType = value;
                base.OcrType = value.GetHashCode();
            }
        }

        public override string GetOcrTypeName()
        {
            return OcrType.ToString();
        }

        public override int GetOcrType()
        {
            return OcrType.GetHashCode();
        }

        #region Html字符串处理

        /// <summary>
        /// 字符串拆分开始
        /// </summary>
        protected string strSpiltStart;

        /// <summary>
        /// 字符串拆分结尾
        /// </summary>
        protected string strSpiltEnd;

        /// <summary>
        /// 识别错误标识
        /// </summary>
        protected List<string> StrRecError = new List<string>();

        protected Dictionary<string, string> DicReplace = new Dictionary<string, string>();

        #endregion

        protected override ResultEntity GetProcessText(OcrContent content, string html)
        {
            var result = base.GetProcessText(content, html);
            //string result = GetSpiltTextByJson(html);
            //ResultEntity result = null;
            //if (IsJsonResult)
            //{
            //var result = OcrHtmlProcess.GetSpiltTextByJsonNew(html, LstJsonPreProcessArray, IsProcessJsonResultByArray
            //    , LstJsonNextProcessArray, StrResultJsonSpilt, StrResultTransJsonSpilt, IsVerticalOcr, IsSupportVertical, IsFromLeftToRight, IsFromTopToDown
            //    , LstVerticalLocation, DicDeserializeVerticalJson, IsDesrializeVerticalByLocation);
            //result = OcrResultProcess.GetSpiltTextByJson(html, LstJsonPreProcessArray, IsProcessJsonResultByArray, LstJsonNextProcessArray, StrResultJsonSpilt, IsVerticalOcr, IsFromLeftToRight, LstVerticalLocation, DicDeserializeVerticalJson);
            //}
            //else
            //{
            //    result = OcrResultProcess.GetSpiltTextByHtml(html, strSpiltStart, strSpiltEnd, StrRecError, DicReplace);
            //}
            return result;
        }
    }
}
