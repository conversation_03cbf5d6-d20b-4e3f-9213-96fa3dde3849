﻿using CommonLib;
using System;
using System.Collections.Generic;
using System.Collections.Specialized;
using System.Security.Cryptography;
using System.Text;
using System.Web;

namespace TableOcr
{
    /// <summary>
    /// 扫描王全能宝
    /// https://www.camoryapps.com/windows/scanner/
    /// https://cloud.tencent.com/document/product/866/49525
    /// </summary>
    public class TencentAPIRec : BaseTableRec
    {
        public TencentAPIRec()
        {
            OcrType = TableOcrType.腾讯API;
            OcrGroup = OcrGroupType.腾讯;
            MaxExecPerTime = 28;

            LstJsonPreProcessArray = new List<object>() { "Response", "TableDetections" };
            LstJsonNextProcessArray = new List<object>() { "Cells" };
            LstJsonResultProcessArray = new List<object>() { "Text" };
            LstRowIndex = new List<object>() { "RowTl", "RowBr" };
            LstColumnIndex = new List<object>() { "ColTl", "ColBr" };
            RowIndexIsArray = false;
            IsRowIndexAddOne = true;
            IsSupportUrlOcr = true;
        }

        static List<TencentAccount> lstAppAccount = new List<TencentAccount>() {
        new TencentAccount(){
            strSecretId = "AKIDYeVBUcyQAHZeTjJhJhn10kMuEuJWrkM4",
            strSecretKey =  "e7LyL2f7Jn222lEafU7anxrU1HGyOXeI"
        },
        };

        protected override string GetHtml(OcrContent content)
        {
            var account = lstAppAccount.GetRndItem();
            var postStr = string.IsNullOrEmpty(content.url) ? "{\"ImageBase64\":\"" + content.strBase64 + "\"}" : "{\"ImageUrl\":\"" + content.url + "\"}";
            var header = BuildHeaders(account.strSecretId, account.strSecretKey, "ocr", "ocr.tencentcloudapi.com", "ap-shanghai"
                , "RecognizeTableOCR", "2018-11-19", DateTime.UtcNow, postStr);
            var result = WebClientSyncExt.GetHtml("https://ocr.tencentcloudapi.com/", postStr, ExecTimeOutSeconds, header);
            return result;
        }

        public override string GetHtmlByUrl(OcrContent content)
        {
            return GetHtml(content);
        }

        static string SHA256Hex(string s)
        {
            string result;
            using (SHA256 algo = SHA256.Create())
            {
                byte[] hashbytes = algo.ComputeHash(Encoding.UTF8.GetBytes(s));
                StringBuilder builder = new StringBuilder();
                for (int i = 0; i < hashbytes.Length; i++)
                {
                    builder.Append(hashbytes[i].ToString("x2"));
                }
                result = builder.ToString();
            }
            return result;
        }

        static byte[] HmacSHA256(byte[] key, byte[] msg)
        {
            byte[] result;
            using (HMACSHA256 mac = new HMACSHA256(key))
            {
                result = mac.ComputeHash(msg);
            }
            return result;
        }

        static NameValueCollection BuildHeaders(string secretid, string secretkey, string service, string endpoint, string region
            , string action, string version, DateTime date, string requestPayload)
        {
            string datestr = date.ToString("yyyy-MM-dd");
            DateTime startTime = new DateTime(1970, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc);
            long requestTimestamp = (long)Math.Round((date - startTime).TotalMilliseconds, MidpointRounding.AwayFromZero) / 1000L;
            string algorithm = "TC3-HMAC-SHA256";
            string httpRequestMethod = "POST";
            string canonicalUri = "/";
            string canonicalQueryString = "";
            string contentType = "application/json";
            string canonicalHeaders = string.Concat(new string[]
            {
                "content-type:",
                contentType,
                "; charset=utf-8\nhost:",
                endpoint,
                "\n"
            });
            string signedHeaders = "content-type;host";
            string hashedRequestPayload = SHA256Hex(requestPayload);
            string text = string.Concat(new string[]
            {
                httpRequestMethod,
                "\n",
                canonicalUri,
                "\n",
                canonicalQueryString,
                "\n",
                canonicalHeaders,
                "\n",
                signedHeaders,
                "\n",
                hashedRequestPayload
            });
            Console.WriteLine(text);
            string credentialScope = datestr + "/" + service + "/tc3_request";
            string hashedCanonicalRequest = SHA256Hex(text);
            string stringToSign = string.Concat(new string[]
            {
                algorithm,
                "\n",
                requestTimestamp.ToString(),
                "\n",
                credentialScope,
                "\n",
                hashedCanonicalRequest
            });
            Console.WriteLine(stringToSign);
            string signature = BitConverter.ToString(HmacSHA256(HmacSHA256(HmacSHA256(HmacSHA256(Encoding.UTF8.GetBytes("TC3" + secretkey), Encoding.UTF8.GetBytes(datestr)), Encoding.UTF8.GetBytes(service)), Encoding.UTF8.GetBytes("tc3_request")), Encoding.UTF8.GetBytes(stringToSign))).Replace("-", "").ToLower();
            Console.WriteLine(signature);
            string authorization = string.Concat(new string[]
            {
                algorithm,
                " Credential=",
                secretid,
                "/",
                credentialScope,
                ", SignedHeaders=",
                signedHeaders,
                ", Signature=",
                signature
            });
            Console.WriteLine(authorization);
            return new NameValueCollection
            {
                {
                    "Authorization",
                    authorization
                },
                {
                    "Host",
                    endpoint
                },
                {
                    "Content-Type",
                    contentType + "; charset=utf-8"
                },
                {
                    "X-TC-Timestamp",
                    requestTimestamp.ToString()
                },
                {
                    "X-TC-Version",
                    version
                },
                {
                    "X-TC-Action",
                    action
                },
                {
                    "X-TC-Region",
                    region
                }
            };
        }

    }
    class TencentAccount
    {
        public string strSecretId { get; set; }

        public string strSecretKey { get; set; }
    }
}