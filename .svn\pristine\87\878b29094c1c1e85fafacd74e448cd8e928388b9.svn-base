﻿using CommonLib;
using System;
using System.Collections.Generic;
using System.Collections.Specialized;
using System.IO;
using System.Security.Cryptography;
using System.Text;

namespace HanZiOcr
{
    /// <summary>
    /// https://bangong.360.cn/#/tools/img_ocr
    /// </summary>
    public class SuDaOfficeRec : BaseOcrRec
    {
        public SuDaOfficeRec()
        {
            OcrType = HanZiOcrType.SuDa;

            MaxExecPerTime = 20;

            LstJsonPreProcessArray = new List<object>() { "data" };

            StrResultJsonSpilt = "txt";
        }

        protected override string GetHtml(OcrContent content)
        {
            var byt = Convert.FromBase64String(content.strBase64);
            var result = PostFileResult(byt);
            return result;
        }

        private string PostFileResult(byte[] content)
        {
            var result = "";
            try
            {
                var url = "https://sudaself.browser.360.cn/image2txt";
                var file = new UploadFileInfo()
                {
                    Name = "img[]",
                    Filename = "1.jpg",
                    ContentType = "image/jpg",
                    Stream = new MemoryStream(content)
                };
                var header = new NameValueCollection() {
                    { "Referer","https://bangong.360.cn"}
                };
                result = PostFile(url, new[] { file }, null, header);
            }
            catch { }
            return result;
        }
    }
}