using System.Reflection;
using System.Runtime.CompilerServices;
using System.Runtime.InteropServices;

namespace Microsoft.Office.Interop.PowerPoint
{
	[ComImport]
	[TypeIdentifier]
	[CompilerGenerated]
	[DefaultMember("Name")]
	[Guid("91493442-5A91-11CF-8700-00AA0060263B")]
	public interface _Application
	{
		Presentations Presentations
		{
			[DispId(2001)]
			[return: MarshalAs(UnmanagedType.Interface)]
			get;
		}

		PpWindowState WindowState
		{
			[DispId(2029)]
			get;
			[DispId(2029)]
			[param: In]
			set;
		}

		int HWND
		{
			[DispId(2031)]
			get;
		}

		void _VtblGap1_21();

		[DispId(2021)]
		void Quit();

		void _VtblGap2_11();

		void _VtblGap3_2();

		void _VtblGap4_1();

		[DispId(2033)]
		void Activate();
	}
}
