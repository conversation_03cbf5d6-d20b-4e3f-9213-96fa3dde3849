﻿using CommonLib;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System.Collections.Generic;
using System.Collections.Specialized;

namespace DocOcr
{
    /// <summary>
    /// TextIn Tools 文档解析
    /// https://tools.textin.com/transform/pdf2markdown
    /// </summary>
    public class HeHeMDRec : BaseDocOcrRec
    {
        public HeHeMDRec()
        {
            OcrType = DocOcrType.合合MD;
            OcrGroup = OcrGroupType.合合;
            ResultType = ResutypeEnum.网页;
            MaxExecPerTime = 23;
            AllowUploadFileTypes = new List<string>() { "pdf", "bmp", "jpg", "png", "jpeg", "gif" };
        }

        protected override string GetHtml(OcrContent content)
        {
            var url = "https://api.textin.com/home/<USER>";
            var result = WebClientSyncExt.GetHtml(url, "", content.strBase64, "https://tools.textin.com/", ExecTimeOutSeconds
                  , new NameValueCollection() {
                    { "App-Key", "ai_demo_text_recognize_3d1" },
                    { "App-Secret","ai_demo_text_recognize_3d1"}
                  });
            //"{\"msg\":\"reach_limit\",\"code\":441}"
            if (result.EndsWith("\"code\":441}") || result.EndsWith("\"code\":451}"))
            {
                result = string.Empty;
            }
            return result;
        }

        protected override ResultEntity GetProcessText(OcrContent content, string html)
        {
            var strContent = JObject.Parse(html)?.SelectToken("data.result.markdown")?.Value<string>();
            var url = CommonHelper.SaveBase64ToFile(strContent, "md", false);
            var entity = GetFileResult(string.Empty);

            if (!string.IsNullOrEmpty(url))
            {
                var file = new DownLoadInfo()
                {
                    fileType = OcrFileType.MD,
                    desc = "合合-MD",
                    url = url,
                };
                entity.viewUrl = url;
                entity.files.Add(file);
                //entity.downloadHtml = ConstHelper.GetDownLoadHtml(entity, OcrType.GetHashCode(), true);
            }
            return entity;
        }
    }
}