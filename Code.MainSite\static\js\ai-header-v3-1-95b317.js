ready(function(){var $header=$("#header");var $headerContainer=$("#header .header-container");var $navItem=$("#header .nav-list .item");var $dropdownItem=$("#header .dropdown-item");var $headerDropdown=$("#header .header-dropdown");var $navList=$("#header .nav-list");var openDropdown=function(height,callback){$headerDropdown.animate({height:height},200,"swing",function(){callback()})};var closeDropdown=function(callback){$headerDropdown.animate({height:0},200,"swing",function(){callback()})};var showDropdown=function(id){if(id){var $item=$("#header "+id);var height=$item.data("height");if(!$item.hasClass("visible")){$dropdownItem.removeClass("visible");$item.addClass("visible")}}};$headerDropdown.on("mouseleave",function(e){var $toElement=$(e.toElement);if(!$toElement.data("dropdown")){$dropdownItem.removeClass("visible")}if(!$toElement.hasClass("nav-list")){$header.removeClass("hover")}});$navItem.on("mouseenter",function(){$dropdownItem.removeClass("visible");var dropdownId=$(this).data("dropdown");if(dropdownId){var $item=$("#header "+dropdownId);if(!$item.hasClass("visible")){$item.addClass("visible")}}}).on("mousemove",function(){}).on("mouseleave",function(e){if(DPS.isInMobile())return;var $toElement=$(e.toElement);if($toElement.hasClass("dropdown-item")){}else{$dropdownItem.removeClass("visible")}});$headerContainer.on("mousemove",function(){$header.addClass("hover")}).on("mouseleave",function(e){var $toElement=$(e.toElement);if(!$toElement.hasClass("dropdown-item")){$header.removeClass("hover")}});var t=$(window).scrollTop();if(t>10){$("#header").addClass("scrolled")}$(window).scroll(function(){var t=$(this).scrollTop();if(t>10){$("#header").addClass("scrolled")}else{$("#header").removeClass("scrolled")}})});