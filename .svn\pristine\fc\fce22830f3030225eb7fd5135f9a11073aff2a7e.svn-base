<?xml version="1.0" encoding="UTF-8"?>
<svg width="180px" height="180px" viewBox="0 0 180 180" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <!-- Generator: Sketch 54 (76480) - https://sketchapp.com -->
    <title>icon_structured_extraction </title>
    <desc>Created with Sketch.</desc>
    <defs>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="98.3504012%" id="linearGradient-1">
            <stop stop-color="#004FFF" offset="0%"></stop>
            <stop stop-color="#FFFFFF" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="98.3504012%" id="linearGradient-2">
            <stop stop-color="#FFFFFF" offset="0%"></stop>
            <stop stop-color="#193FAD" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="70.7680041%" y1="28.1190969%" x2="44.5590685%" y2="64.760001%" id="linearGradient-3">
            <stop stop-color="#71C2FF" offset="0%"></stop>
            <stop stop-color="#2F60EE" offset="100%"></stop>
        </linearGradient>
        <path d="M57.1480489,78.4027297 L103.224852,104.830495 C103.960158,105.252237 104.864078,105.25193 105.599098,104.82969 L139.458974,85.378507 C140.438905,84.8155746 140.889122,83.6421014 140.537188,82.5681831 L132.839591,59.0791693 C132.533424,58.1449063 131.68526,57.4943105 130.703571,57.4407056 L91.0112072,55.2733115 C90.5511904,55.2481924 90.093746,55.3568659 89.6941773,55.5861939 L57.147458,74.2660189 C56.0052304,74.9215876 55.6107155,76.3789893 56.2662842,77.5212168 C56.4768266,77.8880546 56.7811511,78.1922921 57.1480489,78.4027297 Z" id="path-4"></path>
        <filter x="-5.3%" y="-27.0%" width="121.2%" height="136.0%" filterUnits="objectBoundingBox" id="filter-5">
            <feMorphology radius="1" operator="erode" in="SourceAlpha" result="shadowSpreadOuter1"></feMorphology>
            <feOffset dx="6" dy="-6" in="shadowSpreadOuter1" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="2.5" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feColorMatrix values="0 0 0 0 0.44409173   0 0 0 0 0.629283647   0 0 0 0 0.872848732  0 0 0 0.4 0" type="matrix" in="shadowBlurOuter1"></feColorMatrix>
        </filter>
        <linearGradient x1="69.1357387%" y1="25.3110874%" x2="41.0176473%" y2="61.5442936%" id="linearGradient-6">
            <stop stop-color="#71C2FF" offset="0%"></stop>
            <stop stop-color="#2F60EE" offset="100%"></stop>
        </linearGradient>
        <path d="M36.7970793,91.4870272 L82.8738824,117.914793 C83.6091883,118.336534 84.5131084,118.336227 85.248128,117.913987 L119.108004,98.4628045 C120.087935,97.8998721 120.538152,96.7263989 120.186218,95.6524806 L112.488622,72.1634668 C112.182454,71.2292038 111.33429,70.578608 110.352601,70.525003 L70.6602375,68.357609 C70.2002207,68.3324899 69.7427763,68.4411634 69.3432077,68.6704914 L36.7964883,87.3503163 C35.6542607,88.005885 35.2597458,89.4632868 35.9153145,90.6055143 C36.1258569,90.9723521 36.4301814,91.2765896 36.7970793,91.4870272 Z" id="path-7"></path>
        <filter x="-5.3%" y="-27.0%" width="121.2%" height="136.0%" filterUnits="objectBoundingBox" id="filter-8">
            <feMorphology radius="1" operator="erode" in="SourceAlpha" result="shadowSpreadOuter1"></feMorphology>
            <feOffset dx="6" dy="-6" in="shadowSpreadOuter1" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="2.5" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feColorMatrix values="0 0 0 0 0.44409173   0 0 0 0 0.629283647   0 0 0 0 0.872848732  0 0 0 0.4 0" type="matrix" in="shadowBlurOuter1"></feColorMatrix>
        </filter>
        <polygon id="path-9" points="89.9985736 106.441021 77.2180687 82.5765489 64.4389902 92.2706928 72.0111963 96.4714244 72.0111963 111.23734 82.4249411 116.843801 82.4249411 102.243296"></polygon>
        <filter x="-29.3%" y="-21.9%" width="158.7%" height="143.8%" filterUnits="objectBoundingBox" id="filter-10">
            <feOffset dx="0" dy="0" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="2.5" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feColorMatrix values="0 0 0 0 1   0 0 0 0 1   0 0 0 0 1  0 0 0 0.5 0" type="matrix" in="shadowBlurOuter1"></feColorMatrix>
        </filter>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="98.3504012%" id="linearGradient-11">
            <stop stop-color="#E7E9F0" offset="0%"></stop>
            <stop stop-color="#B3C3EF" offset="99.675359%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-12">
            <stop stop-color="#FFFFFF" stop-opacity="0" offset="0%"></stop>
            <stop stop-color="#FFFFFF" stop-opacity="0.6" offset="100%"></stop>
        </linearGradient>
    </defs>
    <g id="设计规范" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="图标" transform="translate(-961.000000, -518.000000)">
            <g id="icon_structured_extraction-" transform="translate(961.000000, 518.000000)">
                <rect id="矩形复制-2" fill="#FFFFFF" opacity="0.01" x="0" y="0" width="180" height="180"></rect>
                <g id="编组-7" transform="translate(0.000000, 3.000000)">
                    <polygon id="多边形" stroke="#004FFF" stroke-width="0.596153846" opacity="0.3" points="90 16.6028546 148.652222 51.76362 148.652222 122.085151 90 157.245916 31.3477778 122.085151 31.3477778 51.76362"></polygon>
                    <polygon id="路径-2复制" fill-opacity="0.2" fill="url(#linearGradient-1)" points="31.3043478 122.085151 89.95657 157.245916 148.608792 122.085151 90 86.9243855"></polygon>
                    <polygon id="路径-2复制-2" fill-opacity="0.1" fill="url(#linearGradient-2)" transform="translate(60.609056, 69.344183) rotate(-240.000000) translate(-60.609056, -69.344183) " points="0.270826369 70.3179602 60.0470535 103.531892 120.947286 68.3711265 61.2157369 35.156474"></polygon>
                    <g id="路径-2复制-8" opacity="0.3" transform="translate(98.302747, 80.208175) scale(-1, 1) rotate(-240.000000) translate(-98.302747, -80.208175) ">
                        <use fill="black" fill-opacity="1" filter="url(#filter-5)" xlink:href="#path-4"></use>
                        <use fill="url(#linearGradient-3)" fill-rule="evenodd" xlink:href="#path-4"></use>
                    </g>
                    <g id="路径-2复制-5" transform="translate(77.951777, 93.292472) scale(-1, 1) rotate(-240.000000) translate(-77.951777, -93.292472) ">
                        <use fill="black" fill-opacity="1" filter="url(#filter-8)" xlink:href="#path-7"></use>
                        <use fill="url(#linearGradient-6)" fill-rule="evenodd" xlink:href="#path-7"></use>
                    </g>
                    <g id="路径" fill-rule="nonzero">
                        <use fill="black" fill-opacity="1" filter="url(#filter-10)" xlink:href="#path-9"></use>
                        <use fill="#FFFFFF" xlink:href="#path-9"></use>
                    </g>
                    <polygon id="路径-2复制-3" fill-opacity="0.2" fill="url(#linearGradient-11)" transform="translate(119.304084, 69.344183) scale(-1, 1) rotate(-240.000000) translate(-119.304084, -69.344183) " points="58.9658543 70.3179602 118.742081 103.531892 179.642314 68.3711265 119.910765 35.156474"></polygon>
                    <path d="M31.3043478,51.76362 L89.95657,86.9243855 L148.608792,51.76362" id="路径-2" stroke="#004FFF" stroke-width="0.596153846" opacity="0.3" stroke-linejoin="round" stroke-dasharray="3.576923171679179,3.576923171679179"></path>
                    <path d="M31.3043478,51.76362 L89.95657,86.9243855 L148.608792,51.76362 L90,16.6028546 L31.3043478,51.76362 Z" id="路径-2复制-4" fill="url(#linearGradient-12)"></path>
                </g>
            </g>
        </g>
    </g>
</svg>