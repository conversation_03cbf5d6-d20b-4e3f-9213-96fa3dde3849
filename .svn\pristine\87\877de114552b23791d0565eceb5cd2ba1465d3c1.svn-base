﻿using CommonLib;
using System;
using System.Collections.Generic;
using System.Security.Cryptography;
using System.Text;
using System.Web;

namespace TransOcr
{
    /// <summary>
    /// 有道智云
    /// </summary>
    public class YouDaoTxtLiteRec : BaseOcrRec
    {
        public YouDaoTxtLiteRec()
        {
            OcrGroup = OcrGroupType.有道;
            OcrType = TransOcrType.有道Text;
            MaxExecPerTime = 25;

            AllowUploadFileTypes = new List<string>() { "txt" };
            StrResultJsonSpilt = "query";
            StrResultTransJsonSpilt = "translation";

            InitLanguage();
        }

        #region 支持的语言

        private void InitLanguage()
        {
            TransLanguageDic = new Dictionary<TransLanguageTypeEnum, string>();
            TransLanguageDic.Add(TransLanguageTypeEnum.中文, "zh-CHS");
            TransLanguageDic.Add(TransLanguageTypeEnum.英文, "en");
            TransLanguageDic.Add(TransLanguageTypeEnum.日语, "ja");
            TransLanguageDic.Add(TransLanguageTypeEnum.韩语, "ko");
            TransLanguageDic.Add(TransLanguageTypeEnum.西班牙语, "es");
            TransLanguageDic.Add(TransLanguageTypeEnum.法语, "fr");
            TransLanguageDic.Add(TransLanguageTypeEnum.泰语, "th");
            TransLanguageDic.Add(TransLanguageTypeEnum.阿拉伯语, "ar");
            TransLanguageDic.Add(TransLanguageTypeEnum.俄语, "ru");

            TransLanguageDic.Add(TransLanguageTypeEnum.葡萄牙语, "pt");
            TransLanguageDic.Add(TransLanguageTypeEnum.德语, "de");
            TransLanguageDic.Add(TransLanguageTypeEnum.意大利语, "it");
            TransLanguageDic.Add(TransLanguageTypeEnum.希腊语, "el");
            TransLanguageDic.Add(TransLanguageTypeEnum.荷兰语, "nl");
            TransLanguageDic.Add(TransLanguageTypeEnum.波兰语, "pl");
            TransLanguageDic.Add(TransLanguageTypeEnum.保加利亚语, "bg");
            TransLanguageDic.Add(TransLanguageTypeEnum.爱沙尼亚语, "et");
            TransLanguageDic.Add(TransLanguageTypeEnum.丹麦语, "da");
            TransLanguageDic.Add(TransLanguageTypeEnum.芬兰语, "fi");
            TransLanguageDic.Add(TransLanguageTypeEnum.捷克语, "cs");
            TransLanguageDic.Add(TransLanguageTypeEnum.罗马尼亚语, "ro");
            TransLanguageDic.Add(TransLanguageTypeEnum.斯洛文尼亚语, "sl");
            TransLanguageDic.Add(TransLanguageTypeEnum.瑞典语, "sv");
            TransLanguageDic.Add(TransLanguageTypeEnum.匈牙利语, "hu");
            TransLanguageDic.Add(TransLanguageTypeEnum.越南语, "vi");
        }

        #endregion

        private const string Api_Url =
            "https://openapi.youdao.com/api?q={0}&from={1}&to={2}&appKey={3}&salt={4}&sign={5}";

        protected override string GetHtml(OcrContent content)
        {
            string from = "", to = "";
            InitLanguage(content, ref from, ref to);
            var account = YouDaoAccountHelper.LstTransText.GetRndItem();
            string salt = ServerTime.DateTime.Millisecond.ToString();
            string signStr = account?.strAppId + content.strBase64 + salt + account?.strSecretId;
            string sign = ToMd5(signStr);

            var result = WebClientSyncExt.GetHtml(string.Format(Api_Url, HttpUtility.UrlEncode(content.strBase64), from, to, account?.strAppId, salt, sign)
                , "", "", "https://servicewechat.com/wxe4bf7f68ecdfd24b/28/page-frame.html", ExecTimeOutSeconds);
            return result;
        }

        private string ToMd5(string strContent)
        {
            string result;
            if (strContent == null)
            {
                result = null;
            }
            else
            {
                MD5 md = MD5.Create();
                byte[] array = md.ComputeHash(Encoding.UTF8.GetBytes(strContent));
                StringBuilder stringBuilder = new StringBuilder();
                for (int i = 0; i < array.Length; i++)
                {
                    stringBuilder.Append(array[i].ToString("x2"));
                }
                result = stringBuilder.ToString();
            }
            return result;
        }


        protected static string ComputeHash(string input)
        {
            using (HashAlgorithm algorithm = new SHA256CryptoServiceProvider())
            {
                byte[] inputBytes = Encoding.UTF8.GetBytes(input);
                byte[] hashedBytes = algorithm.ComputeHash(inputBytes);
                return BitConverter.ToString(hashedBytes).Replace("-", "");
            }
        }

    }
}