﻿using CommonLib;

namespace MathOcr
{
    public abstract class BaseMathRec : BaseRec
    {
        private MathOcrType _OcrType;

        public new MathOcrType OcrType
        {
            get => _OcrType;
            set
            {
                _OcrType = value;
                base.OcrType = value.GetHashCode();
            }
        }

        public override string GetOcrTypeName()
        {
            return OcrType.ToString();
        }

        public override int GetOcrType()
        {
            return OcrType.GetHashCode();
        }

        protected override ResultEntity GetProcessText(OcrContent content, string html)
        {
            var result = base.GetProcessText(content, html);
            if (result != null && !string.IsNullOrEmpty(result.autoText))
            {
                result.autoText = result?.autoText.Replace("\r", " ").Replace("\n", " \\\\ ").Replace("__", "﹏﹏").Replace("$$", ""); ;
            }
            return result;
        }
    }
}
