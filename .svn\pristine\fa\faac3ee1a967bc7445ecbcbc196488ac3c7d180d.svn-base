﻿using CommonLib;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Security.Cryptography;
using System.Text;

namespace HanZiOcr
{
    /// <summary>
    /// https://ai.qq.com/doc/imagetranslate.shtml
    /// </summary>
    public class QQAIRec : BaseOcrRec
    {
        public QQAIRec()
        {
            OcrGroup = OcrGroupType.腾讯;
            OcrType = HanZiOcrType.QQAI;
            State = EnableState.禁用;

            MaxExecPerTime = 20;
            LstJsonPreProcessArray = new List<object>() { "data", "item_list" };
            LstJsonNextProcessArray = new List<object>() { "itemcoord|R|" };

            IsSupportVertical = true;
            DicDeserializeVerticalJson = new Dictionary<string, string> { { "left", "x" }, { "top", "y" }, { "location", "itemcoord" }, { "words", "itemstring" } };

            StrResultJsonSpilt = "itemstring";
        }

        private const string clientId = "2130782054";
        private const string clientSecret = "RLPOMskDhQj3zYlD";
        //private const string clientId = "2117492408";
        //private const string clientSecret = "nEBuDPFUc5epCfWV";
        //private const string clientId = "2109396127";
        //private const string clientSecret = "zVGYpFmTZVho2M3B";
        //private const string clientId = "2117579988";
        //private const string clientSecret = "fFK6B8taYFYrChjR";
        //private const string clientId = "2119430094";
        //private const string clientSecret = "KT9Zhpr9t7Sh3Egi";

        private string GetRndTick()
        {
            return Convert.ToInt64((DateTime.UtcNow - new DateTime(1970, 1, 1, 0, 0, 0, 0)).TotalSeconds).ToString();
        }

        protected override string GetHtml(OcrContent content)
        {
            var noticeStr = Guid.NewGuid().ToString("N");
            var timeSpan = GetRndTick();
            var dic = new Dictionary<string, string>() {
                { "app_id",clientId},
                { "time_stamp",timeSpan},
                { "nonce_str",noticeStr},
                { "image",content.strBase64},
                { "session_id",noticeStr},
                { "scene","doc"},
                { "source","zh"},
                { "target","en"},
            };
            var sign = Sign(dic);
            var strTmp = WebClientSyncExt.GetHtml("https://api.ai.qq.com/fcgi-bin/nlp/nlp_imagetranslate"
                , string.Format("app_id={0}&time_stamp={1}&nonce_str={2}&sign={3}&image={4}&session_id={2}&scene=doc&source=zh&target=en"
                , clientId
                , timeSpan
                , noticeStr
                , sign
                , System.Web.HttpUtility.UrlEncode(content.strBase64)
                ), ExecTimeOutSeconds);

            //strTmp = strTmp.Replace("\r","").Replace("\n", "").Replace("\t", "");

            return strTmp;
        }

        /// <summary>
        /// 签名
        /// </summary>
        /// <param name="pairs">参数字典</param>
        /// <param name="appkey">APPKEY</param>
        /// <param name="charset">编码格式</param>
        /// <returns></returns>
        string Sign(Dictionary<string, string> pairs, string charset = "utf-8")
        {
            var dic = pairs.OrderBy(x => x.Key);

            var pair = "";
            foreach (var kv in dic)
            {
                if (!string.IsNullOrEmpty(kv.Value))
                {
                    pair += kv.Key + "=" + Encode(kv.Value, charset) + "&";
                }
            }

            pair += "app_key=" + clientSecret;

            var sign = MD5(pair).ToUpper();

            return sign;
        }

        /// <summary>
        /// MD5加密（小写）
        /// </summary>
        /// <param name="s">字符串</param>
        /// <param name="len">长度</param>
        /// <returns></returns>
        string MD5(string s, int len = 32)
        {
            string result = "";

            var md5Hasher = new MD5CryptoServiceProvider();
            byte[] data = md5Hasher.ComputeHash(Encoding.Default.GetBytes(s));
            StringBuilder sb = new StringBuilder();
            for (int i = 0; i < data.Length; i++)
            {
                sb.Append(data[i].ToString("x2"));
            }
            result = sb.ToString();

            return len == 32 ? result : result.Substring(8, 16);
        }

        /// <summary>
        /// 签名编码，编码格式需要转成大写
        /// </summary>
        /// <param name="uri">字符串</param>
        /// <param name="charset">编码格式</param>
        /// <returns></returns>
        string Encode(string uri, string charset = "utf-8")
        {
            string URL_ALLOWED_CHARS = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789-_.~";

            if (string.IsNullOrEmpty(uri))
                return string.Empty;

            const string escapeFlag = "%";
            var encodedUri = new StringBuilder(uri.Length * 2);
            var bytes = Encoding.GetEncoding(charset).GetBytes(uri);
            foreach (var b in bytes)
            {
                char ch = (char)b;
                if (URL_ALLOWED_CHARS.IndexOf(ch) != -1)
                    encodedUri.Append(ch);
                else
                {
                    encodedUri.Append(escapeFlag).Append(string.Format(CultureInfo.InstalledUICulture, "{0:X2}", (int)b).ToUpper());
                }
            }

            return encodedUri.ToString();
        }
    }
}