﻿using System;
using System.Collections.Generic;

namespace FtpLib
{
    public class SyncTask
    {
        public string Name { get; set; }

        public string LocalPath { get; set; }

        private string _serverPath = "/";

        public string ServerPath
        {
            get { return _serverPath; }
            set { _serverPath = value; }
        }

        public string FTPURL { get; set; }

        private string _bakURL;

        public string BakURL
        {
            get { return _bakURL; }
            set { _bakURL = value; }
        }
        private string _UserName;

        public string UserName
        {
            get { return _UserName; }
            set { _UserName = value; }
        }
        private string _Password;

        public string Password
        {
            get { return _Password; }
            set { _Password = value; }
        }
        private int _Port = 21;

        public int Port
        {
            get { return _Port; }
            set { _Port = value; }
        }
        private TaskType _Type = TaskType.每日;

        public TaskType Type
        {
            get { return _Type; }
            set { _Type = value; }
        }
        private List<int> _lstDay = new List<int>();

        public List<int> LstDay
        {
            get { return _lstDay; }
            set { _lstDay = value; }
        }
        private List<int> _lstWeek = new List<int>();

        public List<int> LstWeek
        {
            get { return _lstWeek; }
            set { _lstWeek = value; }
        }
        private DateTime _dtValue = DateTime.MinValue;

        public DateTime DtValue
        {
            get { return _dtValue; }
            set { _dtValue = value; }
        }
        private DayTaskType _dayType = DayTaskType.小时;

        public DayTaskType DayType
        {
            get { return _dayType; }
            set { _dayType = value; }
        }
        private int _dayValue = 1;

        public int DayValue
        {
            get { return _dayValue; }
            set { _dayValue = value; }
        }

    }
    public enum TaskType
    {
        每月 = 2,
        每周 = 1,
        每日 = 0
    }
    public enum DayTaskType
    {
        小时 = 0,
        分钟 = 1,
        秒 = 2,
        定时 = 3
    }
}
