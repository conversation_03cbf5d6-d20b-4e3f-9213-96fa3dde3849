﻿using System;
using System.Collections.Generic;

using System.Text;
using System.Net;
using System.IO;

using System.Runtime.InteropServices;

namespace ToolCommon
{
    public class DownLoadFiles
    {
        // 定义常量

        private const long INTERNET_CONNECTION_MODEM = 1;//Local system uses a modem to connect to the Internet.

        private const long INTERNET_CONNECTION_LAN = 2; //Local system uses a local area network to connect to the Internet.

        private const long INTERNET_CONNECTION_PROXY = 4;//Local system uses a proxy server to connect to the Internet.

        private const long INTERNET_CONNECTION_MODEM_BUSY = 8;   //No longer used.

        private const long INTERNET_RAS_INSTALLED = 16; //Local system has RAS installed.

        private const long INTERNET_CONNECTION_OFFLINE = 32; // Local system is in offline mode.

        private const long INTERNET_CONNECTION_CONFIGURED = 64; //Local system has a valid connection to the Internet, but it might or might not be currently connected.
        //定义（引用）API函数

        [DllImport("wininet.dll")]
        public static extern bool InternetGetConnectedState(out   long lpdwFlags, long dwReserved);

        private string GetNowState()
        {
            long lfag;
            string strConnectionDev = "";
            if (InternetGetConnectedState(out lfag, 0))
                strConnectionDev = "网络连接正常!";
            else
                strConnectionDev = "网络连接不可用!";

            if ((lfag & INTERNET_CONNECTION_OFFLINE) > 0)
                strConnectionDev += "OFFLINE 本地连接处于离线状态";
            else if ((lfag & INTERNET_CONNECTION_MODEM) > 0)
                strConnectionDev += "Modem 本地系统使用调制解调器连接到互联网。";
            else if ((lfag & INTERNET_CONNECTION_LAN) > 0)
                strConnectionDev += "LAN 本地系统使用的局域网连接到互联网。";
            else if ((lfag & INTERNET_CONNECTION_PROXY) > 0)
                strConnectionDev += "Proxy 本地系统通过代理连接到互联网";
            else if ((lfag & INTERNET_CONNECTION_MODEM_BUSY) > 0)
                strConnectionDev += "Modem 调制解调器正忙";
            else if ((lfag & INTERNET_CONNECTION_CONFIGURED) > 0)
                strConnectionDev += "连接已经被设定";
            else if ((lfag & INTERNET_RAS_INSTALLED) > 0)
                strConnectionDev += "本机已经安装了远程访问服务功能";
            return strConnectionDev;
        }

        public static void CreatDoc(string path)
        {
            if (!Directory.Exists(path))
                Directory.CreateDirectory(path);
        }

        /// <summary>
        /// 检测网络是否连接
        /// </summary>
        public static bool IsConnected()
        {
            return true;
            WebRequest request = WebRequest.Create("http://baidu.com");
            request.Timeout = 3000;
            try
            {
                WebResponse response = request.GetResponse();
                return true;
            }
            catch
            {
                return false;
            }
        }

        public static bool DownLoadByTransmitFile(string path, string URL, string FileName)
        {
            CreatDoc(path);

            WebRequest request = (WebRequest)WebRequest.Create(URL);
            //request.Headers.Add("user-agent", "Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 5.2; .NET CLR 1.0.3705;)");
            //request.Method = "get";
            try
            {
                WebResponse response = request.GetResponse();
                Stream stream = response.GetResponseStream();
                Byte[] buffer = new byte[256];
                FileStream filestream = new FileStream(path + FileName, FileMode.Create, FileAccess.Write);
                int sizeCount = stream.Read(buffer, 0, 256);
                while (sizeCount > 0)
                {
                    filestream.Write(buffer, 0, sizeCount);
                    sizeCount = stream.Read(buffer, 0, 256);

                }
                filestream.Write(buffer, 0, buffer.Length);
                stream.Close();
                filestream.Close();
                response.Close();
            }
            catch { }
            return true;
            // return StreamToFile(response.GetResponseStream(), path + FileName);
        }

        private static bool StreamToFile(Stream stream, string fileName)
        {
            try
            {


                //FileStream fs = new FileStream(fileName, FileMode.Open);//指定路径打开需要传送的文件
                //Socket socket = new Socket(AddressFamily.InterNetwork, SocketType.Stream, ProtocolType.Tcp);
                //NetworkStream ns = new NetworkStream(socket);
                //Byte[] outBuffer = new Byte[fs.Length];
                //fs.Read(outBuffer, 0, outBuffer.Length);
                //ns.Write(outBuffer, 0, outBuffer.Length);
                //fs.Close();
                //ns.Flush();
                //ns.Close();


                //// 把 Stream 转换成 byte[]
                //byte[] bytes = new byte[stream.Length];
                //stream.Read(bytes, 0, bytes.Length);
                //// 设置当前流的位置为流的开始
                //stream.Seek(0, SeekOrigin.Begin);
                //// 把 byte[] 写入文件
                //FileStream fs = new FileStream(fileName, FileMode.Create);
                //BinaryWriter bw = new BinaryWriter(fs);
                //bw.Write(bytes);
                //bw.Close();
                //fs.Close();
                return true;
            }
            catch { }
            return false;
        }

        /// <summary>
        /// 下载指定的文件到某一指定的文件夹
        /// </summary>
        /// <param name="path">文件保存路径</param>
        /// <param name="URL">文件URL</param>
        /// <param name="FileName">文件名</param>
        /// <returns></returns>
        public static bool DownLoad(string path, string URL, string FileName, string FromURL)
        {
            path = !path.EndsWith("\\") ? (path + "\\") : path;
            CreatDoc(path);
            if (IsConnected())
            {
                if (URL.IndexOf(@"C:\Users") >= 0)
                    return true;
                using (HttpClient MyWebClient = new HttpClient(cookieCon))
                {
                    MyWebClient.UseDefaultCredentials = true;
                    MyWebClient.Referer = FromURL;
                    MyWebClient.Headers.Set("User-Agent", "Microsoft Internet Explorer");
                    try
                    {
                        string filePath = path + FileName;
                        byte[] bytes = MyWebClient.DownloadData(URL);
                        FileStream FS = new FileStream(filePath, FileMode.Create);
                        //把文件流写到文件中
                        FS.Write(bytes, 0, bytes.Length);
                        FS.Close();
                        FS.Dispose();
                    }
                    catch (Exception oe)
                    {
                        return false;
                    }
                }
                return true;
            }
            else
                return false;
        }

        public static string GetStrings(string url)
        {
            try
            {
                WebRequest request = (WebRequest)WebRequest.Create(url);
                request.Method = "get";

                using (WebResponse response = request.GetResponse())
                {
                    using (Stream responseStream = response.GetResponseStream())
                    {
                        using (StreamReader reader = new StreamReader(responseStream, System.Text.Encoding.GetEncoding("GB2312")))
                        {
                            String html = reader.ReadToEnd();
                            response.Close();
                            request = null;
                            return html;
                        }
                    }
                }
            }
            catch
            {
                return string.Empty;
            }
        }

        public static string GetHTMLContext(string url)
        {
            return GetHTMLContext(url, "Get", "", false);
        }

        public static string GetHTMLContext(string url, bool isGB2312)
        {
            return GetHTMLContext(url, "Get", "", isGB2312);
        }

        public static string GetHTMLContext(string url, string postData)
        {
            return GetHTMLContext(url, "Post", postData, false);
        }

        public static string GetHTMLContext(string url, string postData, bool isGB2312)
        {
            return GetHTMLContext(url, "Post", postData, isGB2312);
        }

        static CookieContainer cookieCon = new CookieContainer();
        public static string GetHTMLContext(string url, string type, string postData, bool isGB2312)
        {
            string html = "";
            if (!IsConnected())
            {
                return html;
            }
            Encoding encoding = isGB2312 ? Encoding.GetEncoding("gb2312") : Encoding.UTF8;
            try
            {
                System.Net.ServicePointManager.Expect100Continue = false;
                var request = (HttpWebRequest)WebRequest.Create(url);
                request.Accept = "*/*";
                //request.Timeout = 15000;
                //request.ContentType = "application/x-www-form-urlencoded";
                request.Method = type.ToUpper();
                //验证在得到结果时是否有传入数据
                if (!string.IsNullOrEmpty(postData) && request.Method.Trim().ToLower().Contains("post"))
                {
                    byte[] buffer = encoding.GetBytes(postData);
                    request.ContentLength = buffer.Length;
                    request.GetRequestStream().Write(buffer, 0, buffer.Length);
                }
                request.CookieContainer = cookieCon; //这个是重点只要你现模拟浏览器去浏览这个网页就可以去读取他的源码了
                request.AllowAutoRedirect = true;
                HttpWebResponse response = null;
                Stream stream = null;
                StreamReader reader = null;
                try
                {
                    response = (HttpWebResponse)request.GetResponse();
                    stream = response.GetResponseStream();
                    reader = new StreamReader(stream, encoding);
                    html = reader.ReadToEnd();
                }
                finally
                {
                    if (reader != null)
                    {
                        reader.Close();
                        reader.Dispose();
                    }
                    if (stream != null)
                    {
                        stream.Close();
                        stream.Dispose();
                    }
                    if (response != null)
                    {
                        response.Close();
                    }
                }
            }
            catch
            {
            }
            return html;
        }

    }
}
