<?xml version="1.0"?>
<configuration>
  <configSections>
    <section name="log4net" type="System.Configuration.IgnoreSectionHandler" />
  </configSections>
  <appSettings>
    <add key="NPort" value="21"/>
    <add key="IsWriteLog" value="true"/>
    <add key="adminMachine" value="F294CF7971894F0B7035D7F910D9ADF9"/>
    <add key="RestartTime" value="6:50|9:50|12:50|13:50|"/>
    <add key="Port" value="5234"/>
    <add key="FileDirectory" value=""/>
    <add key="RedisServer" value="127.0.0.1"/>
    <add key="ParallelNum" value="8000"/>
    <!-- 超时，单位毫秒 -->
    <add key="SocketTimeOutMS" value="300000"/>
  </appSettings>
  <startup useLegacyV2RuntimeActivationPolicy="true">
    <supportedRuntime version="v4.0" sku=".NETFramework,Version=v4.0"/>
    <requiredRuntime version="v4.0.20506" />
  </startup>

  <log4net>
    <!--定义输出到文件中-->
    <appender name="RollingLogFileAppender" type="log4net.Appender.RollingFileAppender">
      <!--定义文件存放位置-->
      <file type="log4net.Util.PatternString" value="Log\%property{LogDir}\Log%property{LogFileName}.log" />
      <!--<file value="logfile.log" />-->
      <appendToFile value="true" />
      <maxSizeRollBackups value="10" />
      <maximumFileSize value="100" />
      <rollingStyle value="Date" />
      <DatePattern value="yyyyMMdd" />
      <layout type="log4net.Layout.PatternLayout">
        <!--输出格式-->
        <!--样例：2008-03-26 13:42:32,111 [10] INFO  Log4NetDemo.MainClass [(null)] - info-->
        <!--conversionPattern value="%date [%thread] %-5level %logger property:[%property{NDC}]：%message%newline" /-->
        <conversionPattern value="%date [%thread] %-5level: %message%newline" />
      </layout>
    </appender>
    <!--定义控制台颜色设置-->
    <appender name="ColoredConsoleAppender" type="log4net.Appender.ColoredConsoleAppender">
      <mapping>
        <level value="INFO" />
        <foreColor value="White" />
      </mapping>
      <mapping>
        <level value="DEBUG" />
        <foreColor value="Blue, HighIntensity" />
      </mapping>
      <mapping>
        <level value="WARN" />
        <foreColor value="Yellow, HighIntensity" />
        <!--backColor value="Red, HighIntensity" /-->
      </mapping>
      <mapping>
        <level value="ERROR" />
        <foreColor value="Purple, HighIntensity" />
      </mapping>
      <mapping>
        <level value="FATAL" />
        <foreColor value="Red, HighIntensity" />
      </mapping>
      <layout type="log4net.Layout.PatternLayout">
        <conversionPattern value="%date [%thread] %-5level: %message%newline" />
      </layout>
    </appender>
    <appender name="LogSocketAppender" type="AsyncSocketServer.LogSocketAppender" >
      <layout type="log4net.Layout.PatternLayout">
        <conversionPattern value="%date [%thread] %-5level: %message%newline" />
      </layout>
    </appender>
    <!--定义日志的输出媒介。可以下面的按照一种类型或其他类型输出。-->
    <root>
      <!--文件形式记录日志-->
      <appender-ref ref="RollingLogFileAppender" />
      <!--控制台-->
      <appender-ref ref="ColoredConsoleAppender" />
      <!--Socket-->
      <appender-ref ref="LogSocketAppender" />
    </root>
  </log4net>
</configuration>
