﻿
using CommonLib;
using System;
using System.Collections.Generic;

namespace MathOcr
{
    /// <summary>
    /// 百度AI 演示Demo
    /// https://ai.baidu.com/tech/ocr/formula
    /// </summary>
    public class BaiDuAIRec : BaseMathRec
    {
        public BaiDuAIRec()
        {
            OcrGroup = OcrGroupType.百度;
            OcrType = MathOcrType.百度AI;
            MaxExecPerTime = 20;

            LstJsonPreProcessArray = new List<object>() { "data", "words_result" };
            StrResultJsonSpilt = "words";
            IsSupportUrlOcr = true;
            IsSupportVertical = true;
        }

        protected override string GetHtml(OcrContent content)
        {
            content.strBase64 = "data%3Aimage%2Fjpeg%3Bbase64%2C" + System.Web.HttpUtility.UrlEncode(content.strBase64);
            return RequestHtmlContent(content.strBase64);
        }

        public override string GetHtmlByUrl(OcrContent content)
        {
            return RequestHtmlContent(null, content.url);
        }

        private string RequestHtmlContent(string strBase64, string imgUrl = null)
        {
            EngineType = ConstHelper.lstBaiDuOcrTypeWithLocations.GetRndItem();
            var op = EngineType.Value;
            //var op = IsVerticalOcr ? ConstHelper.lstBaiDuOcrTypeWithLocations.Values.GetRndItem() : ConstHelper.lstBaiDuOcrTypes.Values.GetRndItem();
            var strPost = "type=" + op + "&image=" + strBase64 + "&image_url=" + System.Web.HttpUtility.UrlEncode(imgUrl) + "&detect_direction=true";

            var cookie = "BDAIUID=" + Guid.NewGuid().ToString().ToUpper().Replace("-", "")
                + ";BDAUIDD=" + Guid.NewGuid().ToString().ToUpper().Replace("-", "")
                + ";BAIDUID=" + Guid.NewGuid().ToString().ToUpper().Replace("-", "") + ":FG=1;max-age=31536000;version=1;";

            WebClientSyncExt.GetHtml("https://ai.baidu.com/tech/ocr/webimage", ref cookie);

            var strTmp = WebClientSyncExt.GetHtml("https://ai.baidu.com/aidemo", ref cookie, strPost, "http://ai.baidu.com/tech/ocr", ExecTimeOutSeconds);

            if (strTmp.StartsWith("<html"))
            {
                strTmp = "";
            }

            return strTmp;
        }
    }
}