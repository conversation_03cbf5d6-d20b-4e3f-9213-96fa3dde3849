﻿using CommonLib;
using System.Web;

namespace Account.Web
{
    /// <summary>
    /// Pay 的摘要说明
    /// </summary>
    public class Pay : IHttpHandler
    {
        public void ProcessRequest(HttpContext context)
        {
            if (context.Request.Params.Count <= 0)
            {
                return;
            }
            context.Response.ContentType = "text/plain";

            var payId = HttpUtility.UrlDecode(context.Request.QueryString["payId"]);
            var param = HttpUtility.UrlDecode(context.Request.QueryString["param"]);
            var type = BoxUtil.GetInt32FromObject(context.Request.QueryString["type"]);
            var price = HttpUtility.UrlDecode(context.Request.QueryString["price"]);
            var reallyPrice = HttpUtility.UrlDecode(context.Request.QueryString["reallyPrice"]);
            var remark = HttpUtility.UrlDecode(context.Request.QueryString["remark"]);
            var fromSign = context.Request.QueryString["sign"];
            if (PayUtil.ValidateMd5(payId, param, price, reallyPrice, type, remark, fromSign))
            {
                context.Response.Write("success");
            }
            else
            {
                LogHelper.Log.InfoFormat("异步：payId:{0},param:{1},type:{2},price:{3},reallyPrice:{4},fromSign:{5}", payId, param, type, price, reallyPrice, fromSign);
                context.Response.Write("error sign");
            }
            context.Response.End();
        }

        public bool IsReusable
        {
            get
            {
                return false;
            }
        }
    }
}