﻿using Code.Process.Common;
using CommonLib;
using ImageLib;
using log4net.Config;
using System;
using System.Collections.Generic;
using System.IO;
using System.Text;
using System.Threading;
using System.Windows.Forms;

namespace Code.Process.Console
{
    class Program
    {

        //static void TestTime()
        //{
        //    while (true)
        //    {
        //        var off1 = NtpClient.Instance.GetNetworkTimeOffset();
        //        var off2 = SNtpClient.Instance.GetNetworkTimeOffset();
        //        System.Console.WriteLine(string.Format("Tick0:{0}   OffSet:{1}ms    time:{2}"
        //            , Math.Abs(off1 - off2), TimeSpan.FromTicks(Math.Abs(off1 - off2)).TotalMilliseconds, DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss fff")));
        //        System.Console.WriteLine(string.Format("Tick1:{0}   OffSet:{1}ms    time:{2}"
        //            , off1, TimeSpan.FromTicks(off1).TotalMilliseconds, DateTime.Now.AddTicks(off1).ToString("yyyy-MM-dd HH:mm:ss fff")));
        //        System.Console.WriteLine(string.Format("Tick2:{0}   OffSet:{1}ms    time:{2}"
        //            , off2, TimeSpan.FromTicks(off2).TotalMilliseconds, DateTime.Now.AddTicks(off2).ToString("yyyy-MM-dd HH:mm:ss fff")));
        //        System.Console.WriteLine();
        //        System.Threading.Thread.Sleep(1000);
        //    }
        //}

        //static string DoProcess(BaseOcrRec ocr, string strTmp, int times = 10)
        //{
        //    var strResult = "";
        //    long minTime = -1;
        //    long maxTime = -1;
        //    int errorTimes = 0;
        //    long dtStart = DateTime.Now.Ticks;

        //    long totalExecTimes = 0;
        //    try
        //    {
        //        ocr.OnStart -= Ocr_OnStart;
        //        ocr.OnStart += Ocr_OnStart;
        //        ocr.OnError -= Ocr_OnError;
        //        ocr.OnError += Ocr_OnError;
        //        ocr.OnCompleted -= Ocr_OnCompleted;
        //        ocr.OnCompleted += Ocr_OnCompleted;
        //        Parallel.For(0, times, p =>
        //        {
        //            var content = new OcrContent() { strBase64 = strTmp };
        //            content = ocr?.GetResult(content)?.Result;
        //            //System.Console.WriteLine(string.Format("{0}第{1}次识别：{2}", ocrType, p + 1, st));
        //            var execTime = content.endTicks - content.startTicks;
        //            if (minTime < 0 || execTime < minTime)
        //            {
        //                minTime = execTime;
        //            }
        //            if (maxTime < 0 || execTime > maxTime)
        //            {
        //                maxTime = execTime;
        //            }
        //            if (string.IsNullOrEmpty(content?.result?.html))
        //            {
        //                errorTimes++;
        //            }
        //            totalExecTimes += execTime;
        //            //System.Console.WriteLine(string.Format("【{0}】识别结果：{1}，耗时：{2}ms", content?.processId, content?.result, new TimeSpan(execTime).TotalMilliseconds.ToString("F0")));
        //            //System.Console.WriteLine(st);
        //        });
        //    }
        //    catch (AggregateException ex)
        //    {
        //        // enumerate the exceptions that have been aggregated
        //        foreach (Exception inner in ex.InnerExceptions)
        //        {
        //            System.Console.WriteLine("Exception type {0} from {1}",
        //            inner.GetType(), inner.Source);
        //        }
        //    }
        //    var ts = new TimeSpan(DateTime.Now.Ticks - dtStart);
        //    var tsMin = new TimeSpan(minTime);
        //    var tsMax = new TimeSpan(maxTime);
        //    System.Console.WriteLine(string.Format("{0}识别共{1}次，总耗时：{2}ms，平均耗时：{3}ms，最低耗时：{4}ms，最多耗时：{5}ms，失败次数：{6}"
        //        , ocr.OcrType, times, ts.TotalMilliseconds.ToString("F0"), Math.Floor(new TimeSpan(totalExecTimes).TotalMilliseconds / times).ToString("F0")
        //        , tsMin.TotalMilliseconds.ToString("F0"), tsMax.TotalMilliseconds.ToString("F0"), errorTimes));

        //    return strResult;
        //}

        //private static void Ocr_OnCompleted(object sender, CommonLib.Events.OnCompletedEventArgs e)
        //{
        //    System.Console.WriteLine("处理完成：【{0}】-{1},State:{2},ExecTime:{3}ms,Result:{4}", e.content.processId, e.content.threadId, e.content.state, new TimeSpan(e.content.endTicks - e.content.startTicks).TotalMilliseconds.ToString("F0"), e.content.result, DateTime.Now.ToString("MM-dd HH:mm:ss fff"));
        //}

        //private static void Ocr_OnError(object sender, CommonLib.Events.OnErrorEventArgs e)
        //{
        //    //System.Console.WriteLine("处理异常：【{0}】ThreadId:{1},State:{2},Message:{3},time:{4}", e.content.processId, e.content.threadId, e.content.state, e.content.message, DateTime.Now.ToString("MM-dd HH:mm:ss fff"));
        //}

        //private static void Ocr_OnStart(object sender, CommonLib.Events.OnStartedEventArgs e)
        //{
        //    System.Console.WriteLine("开始处理：【{0}】-{1},State:{2},WaitTime:{3}ms", e.content.processId, e.content.threadId, e.content.state, new TimeSpan(e.content.startTicks - e.content.receivedTicks).TotalMilliseconds.ToString("F0"), DateTime.Now.ToString("MM-dd HH:mm:ss fff"));
        //}

        static void Main(string[] args)
        {
            //Set error handler
            SetApplicationErrorHandler();
            //Set Log4net基础信息
            SetLoggerContext();
            InitConfig();
            ProcessNew.InitOcrEngine();

            //using (WebClient client = new WebClient())
            //{
            //    byte[] response = client.UploadValues("http://textbelt.com/text", new NameValueCollection() {
            //        { "phone", "+86***********" },
            //        { "message", "【OCR助手】您的验证码是123456" },
            //        { "key", "textbelt" },
            //      });

            //    string result = System.Text.Encoding.UTF8.GetString(response);
            //}

            //var html = "{\"code\":200,\"data\":{\"subImages\":[{\"blockInfo\":{\"blockCount\":11,\"blockDetails\":[{\"blockId\":0,\"charInfos\":[{\"charPoints\":[{\"x\":2,\"y\":6},{\"x\":26,\"y\":6},{\"x\":26,\"y\":28},{\"x\":2,\"y\":28}],\"charConfidence\":99,\"charId\":0,\"charContent\":\"长\"},{\"charPoints\":[{\"x\":26,\"y\":6},{\"x\":50,\"y\":6},{\"x\":50,\"y\":28},{\"x\":26,\"y\":28}],\"charConfidence\":100,\"charId\":1,\"charContent\":\"歌\"},{\"charPoints\":[{\"x\":53,\"y\":6},{\"x\":77,\"y\":6},{\"x\":77,\"y\":27},{\"x\":53,\"y\":27}],\"charConfidence\":99,\"charId\":2,\"charContent\":\"行\"}],\"blockAngle\":0,\"blockPoints\":[{\"x\":548,\"y\":123},{\"x\":574,\"y\":123},{\"x\":574,\"y\":198},{\"x\":548,\"y\":198}],\"blockContent\":\"长歌行\",\"blockConfidence\":99},{\"blockId\":1,\"charInfos\":[{\"charPoints\":[{\"x\":1,\"y\":4},{\"x\":12,\"y\":4},{\"x\":12,\"y\":17},{\"x\":1,\"y\":17}],\"charConfidence\":31,\"charId\":0,\"charContent\":\"_\"},{\"charPoints\":[{\"x\":12,\"y\":4},{\"x\":23,\"y\":4},{\"x\":23,\"y\":17},{\"x\":12,\"y\":17}],\"charConfidence\":91,\"charId\":1,\"charContent\":\"作\"},{\"charPoints\":[{\"x\":25,\"y\":4},{\"x\":36,\"y\":4},{\"x\":36,\"y\":17},{\"x\":25,\"y\":17}],\"charConfidence\":95,\"charId\":2,\"charContent\":\"者\"},{\"charPoints\":[{\"x\":38,\"y\":5},{\"x\":49,\"y\":5},{\"x\":49,\"y\":15},{\"x\":38,\"y\":15}],\"charConfidence\":61,\"charId\":3,\"charContent\":\"‘\"},{\"charPoints\":[{\"x\":51,\"y\":4},{\"x\":62,\"y\":4},{\"x\":62,\"y\":17},{\"x\":51,\"y\":17}],\"charConfidence\":99,\"charId\":4,\"charContent\":\"汉\"},{\"charPoints\":[{\"x\":62,\"y\":4},{\"x\":73,\"y\":4},{\"x\":73,\"y\":17},{\"x\":62,\"y\":17}],\"charConfidence\":100,\"charId\":5,\"charContent\":\"乐\"},{\"charPoints\":[{\"x\":75,\"y\":4},{\"x\":86,\"y\":4},{\"x\":86,\"y\":17},{\"x\":75,\"y\":17}],\"charConfidence\":100,\"charId\":6,\"charContent\":\"府\"}],\"blockAngle\":0,\"blockPoints\":[{\"x\":521,\"y\":117},{\"x\":537,\"y\":117},{\"x\":537,\"y\":204},{\"x\":521,\"y\":204}],\"blockContent\":\"_作者‘汉乐府\",\"blockConfidence\":91},{\"blockId\":2,\"charInfos\":[{\"charPoints\":[{\"x\":1,\"y\":3},{\"x\":22,\"y\":3},{\"x\":22,\"y\":21},{\"x\":1,\"y\":21}],\"charConfidence\":98,\"charId\":0,\"charContent\":\"青\"},{\"charPoints\":[{\"x\":22,\"y\":4},{\"x\":43,\"y\":4},{\"x\":43,\"y\":21},{\"x\":22,\"y\":21}],\"charConfidence\":99,\"charId\":1,\"charContent\":\"青\"},{\"charPoints\":[{\"x\":43,\"y\":5},{\"x\":64,\"y\":5},{\"x\":64,\"y\":20},{\"x\":43,\"y\":20}],\"charConfidence\":100,\"charId\":2,\"charContent\":\"园\"},{\"charPoints\":[{\"x\":64,\"y\":3},{\"x\":85,\"y\":3},{\"x\":85,\"y\":21},{\"x\":64,\"y\":21}],\"charConfidence\":98,\"charId\":3,\"charContent\":\"中\"},{\"charPoints\":[{\"x\":86,\"y\":3},{\"x\":107,\"y\":3},{\"x\":107,\"y\":22},{\"x\":86,\"y\":22}],\"charConfidence\":62,\"charId\":4,\"charContent\":\"‘\"},{\"charPoints\":[{\"x\":107,\"y\":4},{\"x\":128,\"y\":4},{\"x\":128,\"y\":22},{\"x\":107,\"y\":22}],\"charConfidence\":95,\"charId\":5,\"charContent\":\"朝\"},{\"charPoints\":[{\"x\":128,\"y\":3},{\"x\":149,\"y\":3},{\"x\":149,\"y\":21},{\"x\":128,\"y\":21}],\"charConfidence\":96,\"charId\":6,\"charContent\":\"露\"},{\"charPoints\":[{\"x\":150,\"y\":3},{\"x\":171,\"y\":3},{\"x\":171,\"y\":22},{\"x\":150,\"y\":22}],\"charConfidence\":100,\"charId\":7,\"charContent\":\"待\"},{\"charPoints\":[{\"x\":171,\"y\":3},{\"x\":192,\"y\":3},{\"x\":192,\"y\":21},{\"x\":171,\"y\":21}],\"charConfidence\":98,\"charId\":8,\"charContent\":\"日\"},{\"charPoints\":[{\"x\":192,\"y\":3},{\"x\":213,\"y\":3},{\"x\":213,\"y\":21},{\"x\":192,\"y\":21}],\"charConfidence\":90,\"charId\":9,\"charContent\":\"怖\"}],\"blockAngle\":0,\"blockPoints\":[{\"x\":492,\"y\":50},{\"x\":512,\"y\":50},{\"x\":512,\"y\":260},{\"x\":492,\"y\":260}],\"blockContent\":\"青青园中‘朝露待日怖\",\"blockConfidence\":97},{\"blockId\":3,\"charInfos\":[{\"charPoints\":[{\"x\":506,\"y\":256},{\"x\":510,\"y\":256},{\"x\":510,\"y\":268},{\"x\":506,\"y\":268}],\"charConfidence\":78,\"charId\":0,\"charContent\":\"c\"}],\"blockAngle\":0,\"blockPoints\":[{\"x\":500,\"y\":256},{\"x\":509,\"y\":256},{\"x\":509,\"y\":267},{\"x\":500,\"y\":267}],\"blockContent\":\"c\",\"blockConfidence\":78},{\"blockId\":4,\"charInfos\":[{\"charPoints\":[{\"x\":475,\"y\":52},{\"x\":482,\"y\":52},{\"x\":482,\"y\":73},{\"x\":475,\"y\":73}],\"charConfidence\":98,\"charId\":0,\"charContent\":\"日\"},{\"charPoints\":[{\"x\":498,\"y\":52},{\"x\":510,\"y\":52},{\"x\":510,\"y\":73},{\"x\":498,\"y\":73}],\"charConfidence\":99,\"charId\":1,\"charContent\":\"青\"}],\"blockAngle\":0,\"blockPoints\":[{\"x\":475,\"y\":52},{\"x\":503,\"y\":52},{\"x\":503,\"y\":73},{\"x\":475,\"y\":73}],\"blockContent\":\"日青\",\"blockConfidence\":99},{\"blockId\":5,\"charInfos\":[{\"charPoints\":[{\"x\":1,\"y\":4},{\"x\":19,\"y\":4},{\"x\":19,\"y\":22},{\"x\":1,\"y\":22}],\"charConfidence\":100,\"charId\":0,\"charContent\":\"阳\"},{\"charPoints\":[{\"x\":19,\"y\":4},{\"x\":37,\"y\":4},{\"x\":37,\"y\":22},{\"x\":19,\"y\":22}],\"charConfidence\":98,\"charId\":1,\"charContent\":\"春\"},{\"charPoints\":[{\"x\":39,\"y\":4},{\"x\":57,\"y\":4},{\"x\":57,\"y\":22},{\"x\":39,\"y\":22}],\"charConfidence\":98,\"charId\":2,\"charContent\":\"布\"},{\"charPoints\":[{\"x\":57,\"y\":4},{\"x\":75,\"y\":4},{\"x\":75,\"y\":22},{\"x\":57,\"y\":22}],\"charConfidence\":99,\"charId\":3,\"charContent\":\"德\"},{\"charPoints\":[{\"x\":77,\"y\":4},{\"x\":95,\"y\":4},{\"x\":95,\"y\":22},{\"x\":77,\"y\":22}],\"charConfidence\":99,\"charId\":4,\"charContent\":\"泽\"},{\"charPoints\":[{\"x\":95,\"y\":5},{\"x\":113,\"y\":5},{\"x\":113,\"y\":22},{\"x\":95,\"y\":22}],\"charConfidence\":64,\"charId\":5,\"charContent\":\"‘\"},{\"charPoints\":[{\"x\":115,\"y\":5},{\"x\":133,\"y\":5},{\"x\":133,\"y\":22},{\"x\":115,\"y\":22}],\"charConfidence\":100,\"charId\":6,\"charContent\":\"万\"},{\"charPoints\":[{\"x\":133,\"y\":5},{\"x\":151,\"y\":5},{\"x\":151,\"y\":22},{\"x\":133,\"y\":22}],\"charConfidence\":100,\"charId\":7,\"charContent\":\"物\"},{\"charPoints\":[{\"x\":154,\"y\":4},{\"x\":172,\"y\":4},{\"x\":172,\"y\":22},{\"x\":154,\"y\":22}],\"charConfidence\":100,\"charId\":8,\"charContent\":\"生\"},{\"charPoints\":[{\"x\":172,\"y\":4},{\"x\":190,\"y\":4},{\"x\":190,\"y\":22},{\"x\":172,\"y\":22}],\"charConfidence\":100,\"charId\":9,\"charContent\":\"光\"},{\"charPoints\":[{\"x\":192,\"y\":4},{\"x\":210,\"y\":4},{\"x\":210,\"y\":22},{\"x\":192,\"y\":22}],\"charConfidence\":98,\"charId\":10,\"charContent\":\"辉\"}],\"blockAngle\":0,\"blockPoints\":[{\"x\":460,\"y\":50},{\"x\":481,\"y\":50},{\"x\":481,\"y\":261},{\"x\":460,\"y\":261}],\"blockContent\":\"阳春布德泽‘万物生光辉\",\"blockConfidence\":95},{\"blockId\":6,\"charInfos\":[{\"charPoints\":[{\"x\":1,\"y\":4},{\"x\":19,\"y\":4},{\"x\":19,\"y\":22},{\"x\":1,\"y\":22}],\"charConfidence\":90,\"charId\":0,\"charContent\":\"党\"},{\"charPoints\":[{\"x\":19,\"y\":4},{\"x\":37,\"y\":4},{\"x\":37,\"y\":23},{\"x\":19,\"y\":23}],\"charConfidence\":96,\"charId\":1,\"charContent\":\"常\"},{\"charPoints\":[{\"x\":38,\"y\":4},{\"x\":56,\"y\":4},{\"x\":56,\"y\":23},{\"x\":38,\"y\":23}],\"charConfidence\":96,\"charId\":2,\"charContent\":\"恐\"},{\"charPoints\":[{\"x\":57,\"y\":5},{\"x\":75,\"y\":5},{\"x\":75,\"y\":22},{\"x\":57,\"y\":22}],\"charConfidence\":98,\"charId\":3,\"charContent\":\"秋\"},{\"charPoints\":[{\"x\":76,\"y\":5},{\"x\":94,\"y\":5},{\"x\":94,\"y\":22},{\"x\":76,\"y\":22}],\"charConfidence\":94,\"charId\":4,\"charContent\":\"节\"},{\"charPoints\":[{\"x\":95,\"y\":4},{\"x\":113,\"y\":4},{\"x\":113,\"y\":22},{\"x\":95,\"y\":22}],\"charConfidence\":98,\"charId\":5,\"charContent\":\"至\"},{\"charPoints\":[{\"x\":114,\"y\":4},{\"x\":132,\"y\":4},{\"x\":132,\"y\":22},{\"x\":114,\"y\":22}],\"charConfidence\":90,\"charId\":6,\"charContent\":\"焜\"},{\"charPoints\":[{\"x\":132,\"y\":4},{\"x\":150,\"y\":4},{\"x\":150,\"y\":22},{\"x\":132,\"y\":22}],\"charConfidence\":100,\"charId\":7,\"charContent\":\"黄\"},{\"charPoints\":[{\"x\":151,\"y\":4},{\"x\":169,\"y\":4},{\"x\":169,\"y\":22},{\"x\":151,\"y\":22}],\"charConfidence\":100,\"charId\":8,\"charContent\":\"华\"},{\"charPoints\":[{\"x\":170,\"y\":4},{\"x\":188,\"y\":4},{\"x\":188,\"y\":22},{\"x\":170,\"y\":22}],\"charConfidence\":100,\"charId\":9,\"charContent\":\"叶\"},{\"charPoints\":[{\"x\":189,\"y\":4},{\"x\":207,\"y\":4},{\"x\":207,\"y\":22},{\"x\":189,\"y\":22}],\"charConfidence\":100,\"charId\":10,\"charContent\":\"衰\"}],\"blockAngle\":0,\"blockPoints\":[{\"x\":429,\"y\":50},{\"x\":449,\"y\":50},{\"x\":449,\"y\":259},{\"x\":429,\"y\":259}],\"blockContent\":\"党常恐秋节至焜黄华叶衰\",\"blockConfidence\":96},{\"blockId\":7,\"charInfos\":[{\"charPoints\":[{\"x\":444,\"y\":256},{\"x\":448,\"y\":256},{\"x\":448,\"y\":269},{\"x\":444,\"y\":269}],\"charConfidence\":93,\"charId\":0,\"charContent\":\"o\"}],\"blockAngle\":0,\"blockPoints\":[{\"x\":438,\"y\":256},{\"x\":448,\"y\":256},{\"x\":448,\"y\":268},{\"x\":438,\"y\":268}],\"blockContent\":\"o\",\"blockConfidence\":93},{\"blockId\":8,\"charInfos\":[{\"charPoints\":[{\"x\":1,\"y\":6},{\"x\":17,\"y\":6},{\"x\":17,\"y\":22},{\"x\":1,\"y\":22}],\"charConfidence\":96,\"charId\":0,\"charContent\":\"二\"},{\"charPoints\":[{\"x\":17,\"y\":6},{\"x\":33,\"y\":6},{\"x\":33,\"y\":22},{\"x\":17,\"y\":22}],\"charConfidence\":99,\"charId\":1,\"charContent\":\"百\"},{\"charPoints\":[{\"x\":34,\"y\":5},{\"x\":50,\"y\":5},{\"x\":50,\"y\":23},{\"x\":34,\"y\":23}],\"charConfidence\":99,\"charId\":2,\"charContent\":\"川\"},{\"charPoints\":[{\"x\":50,\"y\":5},{\"x\":66,\"y\":5},{\"x\":66,\"y\":23},{\"x\":50,\"y\":23}],\"charConfidence\":99,\"charId\":3,\"charContent\":\"东\"},{\"charPoints\":[{\"x\":69,\"y\":4},{\"x\":85,\"y\":4},{\"x\":85,\"y\":22},{\"x\":69,\"y\":22}],\"charConfidence\":99,\"charId\":4,\"charContent\":\"到\"},{\"charPoints\":[{\"x\":86,\"y\":4},{\"x\":102,\"y\":4},{\"x\":102,\"y\":23},{\"x\":86,\"y\":23}],\"charConfidence\":99,\"charId\":5,\"charContent\":\"海\"},{\"charPoints\":[{\"x\":102,\"y\":5},{\"x\":118,\"y\":5},{\"x\":118,\"y\":22},{\"x\":102,\"y\":22}],\"charConfidence\":74,\"charId\":6,\"charContent\":\"‘\"},{\"charPoints\":[{\"x\":119,\"y\":5},{\"x\":135,\"y\":5},{\"x\":135,\"y\":22},{\"x\":119,\"y\":22}],\"charConfidence\":99,\"charId\":7,\"charContent\":\"何\"},{\"charPoints\":[{\"x\":138,\"y\":5},{\"x\":154,\"y\":5},{\"x\":154,\"y\":22},{\"x\":138,\"y\":22}],\"charConfidence\":98,\"charId\":8,\"charContent\":\"时\"},{\"charPoints\":[{\"x\":154,\"y\":5},{\"x\":170,\"y\":5},{\"x\":170,\"y\":22},{\"x\":154,\"y\":22}],\"charConfidence\":99,\"charId\":9,\"charContent\":\"复\"},{\"charPoints\":[{\"x\":171,\"y\":5},{\"x\":187,\"y\":5},{\"x\":187,\"y\":22},{\"x\":171,\"y\":22}],\"charConfidence\":100,\"charId\":10,\"charContent\":\"西\"},{\"charPoints\":[{\"x\":188,\"y\":5},{\"x\":204,\"y\":5},{\"x\":204,\"y\":21},{\"x\":188,\"y\":21}],\"charConfidence\":100,\"charId\":11,\"charContent\":\"归\"},{\"charPoints\":[{\"x\":207,\"y\":6},{\"x\":223,\"y\":6},{\"x\":223,\"y\":22},{\"x\":207,\"y\":22}],\"charConfidence\":97,\"charId\":12,\"charContent\":\"?\"}],\"blockAngle\":0,\"blockPoints\":[{\"x\":399,\"y\":51},{\"x\":418,\"y\":51},{\"x\":418,\"y\":273},{\"x\":399,\"y\":273}],\"blockContent\":\"二百川东到海‘何时复西归?\",\"blockConfidence\":97},{\"blockId\":9,\"charInfos\":[{\"charPoints\":[{\"x\":1,\"y\":6},{\"x\":18,\"y\":6},{\"x\":18,\"y\":24},{\"x\":1,\"y\":24}],\"charConfidence\":96,\"charId\":0,\"charContent\":\"少\"},{\"charPoints\":[{\"x\":18,\"y\":6},{\"x\":35,\"y\":6},{\"x\":35,\"y\":24},{\"x\":18,\"y\":24}],\"charConfidence\":85,\"charId\":1,\"charContent\":\"壮\"},{\"charPoints\":[{\"x\":38,\"y\":5},{\"x\":55,\"y\":5},{\"x\":55,\"y\":25},{\"x\":38,\"y\":25}],\"charConfidence\":95,\"charId\":2,\"charContent\":\"不\"},{\"charPoints\":[{\"x\":55,\"y\":6},{\"x\":72,\"y\":6},{\"x\":72,\"y\":25},{\"x\":55,\"y\":25}],\"charConfidence\":98,\"charId\":3,\"charContent\":\"努\"},{\"charPoints\":[{\"x\":75,\"y\":7},{\"x\":92,\"y\":7},{\"x\":92,\"y\":23},{\"x\":75,\"y\":23}],\"charConfidence\":99,\"charId\":4,\"charContent\":\"力\"},{\"charPoints\":[{\"x\":92,\"y\":8},{\"x\":109,\"y\":8},{\"x\":109,\"y\":23},{\"x\":92,\"y\":23}],\"charConfidence\":50,\"charId\":5,\"charContent\":\"‘\"},{\"charPoints\":[{\"x\":112,\"y\":6},{\"x\":129,\"y\":6},{\"x\":129,\"y\":24},{\"x\":112,\"y\":24}],\"charConfidence\":99,\"charId\":6,\"charContent\":\"老\"},{\"charPoints\":[{\"x\":130,\"y\":6},{\"x\":147,\"y\":6},{\"x\":147,\"y\":25},{\"x\":130,\"y\":25}],\"charConfidence\":99,\"charId\":7,\"charContent\":\"大\"},{\"charPoints\":[{\"x\":149,\"y\":5},{\"x\":166,\"y\":5},{\"x\":166,\"y\":25},{\"x\":149,\"y\":25}],\"charConfidence\":90,\"charId\":8,\"charContent\":\"徒\"},{\"charPoints\":[{\"x\":167,\"y\":6},{\"x\":184,\"y\":6},{\"x\":184,\"y\":24},{\"x\":167,\"y\":24}],\"charConfidence\":100,\"charId\":9,\"charContent\":\"伤\"},{\"charPoints\":[{\"x\":187,\"y\":6},{\"x\":204,\"y\":6},{\"x\":204,\"y\":24},{\"x\":187,\"y\":24}],\"charConfidence\":100,\"charId\":10,\"charContent\":\"悲\"}],\"blockAngle\":0,\"blockPoints\":[{\"x\":366,\"y\":49},{\"x\":388,\"y\":49},{\"x\":388,\"y\":257},{\"x\":366,\"y\":257}],\"blockContent\":\"少壮不努力‘老大徒伤悲\",\"blockConfidence\":96},{\"blockId\":10,\"charInfos\":[{\"charPoints\":[{\"x\":380,\"y\":257},{\"x\":384,\"y\":257},{\"x\":384,\"y\":269},{\"x\":380,\"y\":269}],\"charConfidence\":94,\"charId\":0,\"charContent\":\"o\"}],\"blockAngle\":0,\"blockPoints\":[{\"x\":375,\"y\":257},{\"x\":385,\"y\":257},{\"x\":385,\"y\":268},{\"x\":375,\"y\":268}],\"blockContent\":\"o\",\"blockConfidence\":94}]},\"subImageId\":0,\"subImagePoints\":[{\"x\":0,\"y\":0},{\"x\":893,\"y\":0},{\"x\":893,\"y\":349},{\"x\":0,\"y\":349}],\"stampInfo\":{\"stampCount\":1,\"stampDetails\":[{\"data\":{\"companyId\":\"\",\"organizationName\":\"\",\"antiFakeCode\":\"\",\"otherText\":\"券木\",\"topText\":\"\",\"organizationNameEng\":\"\",\"taxpayerId\":\"\"},\"stampPoints\":[{\"x\":542,\"y\":125},{\"x\":578,\"y\":124},{\"x\":578,\"y\":202},{\"x\":542,\"y\":203}],\"stampAngle\":2}]},\"figureInfo\":{},\"paragraphInfo\":{\"paragraphDetails\":[{\"paragraphContent\":\"长歌行\",\"blockList\":[0],\"paragraphId\":0},{\"paragraphContent\":\"_作者‘汉乐府\",\"blockList\":[1],\"paragraphId\":1},{\"paragraphContent\":\"青青园中‘朝露待日怖c\",\"blockList\":[2,3],\"paragraphId\":2},{\"paragraphContent\":\"日青\",\"blockList\":[4],\"paragraphId\":3},{\"paragraphContent\":\"阳春布德泽‘万物生光辉\",\"blockList\":[5],\"paragraphId\":4},{\"paragraphContent\":\"党常恐秋节至焜黄华叶衰o\",\"blockList\":[6,7],\"paragraphId\":5},{\"paragraphContent\":\"二百川东到海‘何时复西归?\",\"blockList\":[8],\"paragraphId\":6},{\"paragraphContent\":\"少壮不努力‘老大徒伤悲o\",\"blockList\":[9,10],\"paragraphId\":7}],\"paragraphCount\":8},\"rowInfo\":{\"rowDetails\":[{\"rowContent\":\"长歌行\",\"blockList\":[0],\"rowId\":0},{\"rowContent\":\"_作者‘汉乐府\",\"blockList\":[1],\"rowId\":1},{\"rowContent\":\"青青园中‘朝露待日怖c\",\"blockList\":[2,3],\"rowId\":2},{\"rowContent\":\"日青\",\"blockList\":[4],\"rowId\":3},{\"rowContent\":\"阳春布德泽‘万物生光辉\",\"blockList\":[5],\"rowId\":4},{\"rowContent\":\"党常恐秋节至焜黄华叶衰o\",\"blockList\":[6,7],\"rowId\":5},{\"rowContent\":\"二百川东到海‘何时复西归?\",\"blockList\":[8],\"rowId\":6},{\"rowContent\":\"少壮不努力‘老大徒伤悲o\",\"blockList\":[9,10],\"rowId\":7}],\"rowCount\":8},\"tableInfo\":{\"tableCount\":0,\"tableDetails\":[]},\"angle\":0,\"type\":\"通用文字识别高精版\"}],\"requestId\":\"C8F53BA8-8D93-5289-8C7B-B804A281A00E\",\"width\":893,\"subImageCount\":1,\"content\":\"长歌行 _作者‘汉乐府 青青园中‘朝露待日怖 c 日青 阳春布德泽‘万物生光辉 党常恐秋节至焜黄华叶衰 o 二百川东到海‘何时复西归? 少壮不努力‘老大徒伤悲 o \",\"height\":349},\"requestId\":\"16db605d-0a52-4bbb-a193-3d958caf5fed\"}";

            //var processType = BaseRecHelper.GetInstance(OcrType.文本, HanZiOcrType.读光高精度.GetHashCode());
            //processType.IsFromLeftToRight = false;
            //processType.IsFromTopToDown = true;
            //for (int i = 0; i < 50; i++)
            //{
            //    var startTicks = ServerTime.DateTime.Ticks;
            //    var result = processType.GetProcessTextTest(html);
            //    System.Console.WriteLine(string.Format("解析HTML：{0}，已耗时{1}ms", ServerTime.DateTime.ToString("HH:mm:ss fff"), new TimeSpan(ServerTime.DateTime.Ticks - startTicks).TotalMilliseconds.ToString("F0")));
            //}
            //System.Console.Read();
            //return;

            //var fileName = (@"C:\Users\<USER>\Desktop\1.doc");
            //var fileName = (@"C:\Users\<USER>\Desktop\2.pdf");
            //var fileName = (@"C:\Users\<USER>\Desktop\CAT性能优化的实践和思考-梁锦华.pdf");
            //var fileName = Environment.GetFolderPath(Environment.SpecialFolder.Desktop) + @"\2022-09-06-18-36-24.jpg";
            var fileName = Environment.GetFolderPath(Environment.SpecialFolder.Desktop) + @"\shupai.png";
            //var fileName = Environment.GetFolderPath(Environment.SpecialFolder.Desktop) + @"\qq.png";
            //var fileName = Environment.GetFolderPath(Environment.SpecialFolder.Desktop) + @"\en.png";
            //var fileName = Environment.GetFolderPath(Environment.SpecialFolder.Desktop) + @"\juanzi.png";
            //var fileName = Environment.GetFolderPath(Environment.SpecialFolder.Desktop) + @"\002.png";
            //var fileName = Environment.GetFolderPath(Environment.SpecialFolder.Desktop) + @"\table1.png";
            //var fileName = Environment.GetFolderPath(Environment.SpecialFolder.Desktop) + @"\math.png";
            //var fileName = Environment.GetFolderPath(Environment.SpecialFolder.Desktop) + @"\shupai.png";
            //var fileName = Environment.GetFolderPath(Environment.SpecialFolder.Desktop) + @"\1.txt";
            //var fileName = Environment.GetFolderPath(Environment.SpecialFolder.Desktop) + @"\1.doc";
            //var fileName = @"C:\Users\<USER>\Desktop\math1.png";
            //var fileName = @"C:\Users\<USER>\Desktop\umsg.txt";
            var fileExt = Path.GetExtension(fileName).TrimStart('.');
            var from = TransLanguageTypeEnum.中文;
            var to = TransLanguageTypeEnum.英文;
            var bytes = File.ReadAllBytes(fileName);
            var strTmp = Convert.ToBase64String(bytes);//File.ReadAllText(fileName).Trim();//
            var imgUrl = ImageHelper.GetUploadResult(bytes);//
                                                            //var tImg = YouTuXLab.EnhanceImage(strTmp);
                                                            //GoogleRecTest.GetRecImg();"";//
            var baseOcrType = OcrType.文本;
            //if (baseOcrType == OcrType.翻译)
            //{
            //    strTmp = "测试";
            //    fileExt = "txt";
            //}
            bool? isSupportVertical = null;
            var ocrGroupStr = "腾讯印刷体";//"";
            var lstEnableType = BaseRecHelper.GetEnableRecType(true, baseOcrType, OcrGroupType.不限, null, isSupportVertical, fileExt, from, to);
            for (int i = 0; i < 1; i++)
            {
                StringBuilder sb = new StringBuilder();
                foreach (var c in lstEnableType)
                {
                    sb.Append(c.OcrType);
                }
                lstEnableType.ForEach(customer =>
                {
                    //if (!HanZiOcr.HanZiOcrType.白描_讯飞.GetHashCode().Equals(customer.OcrType))
                    //{
                    //    return;
                    //}
                    //if (!MathOcr.MathOcrType.腾讯优图Lite.GetHashCode().Equals(customer.OcrType))
                    //{
                    //    return;
                    //}
                    //if (!TableOcr.TableOcrType.学而思.GetHashCode().Equals(customer.OcrType))
                    //{
                    //    return;
                    //}
                    //if (!DocOcr.DocOcrType.WPS.GetHashCode().Equals(customer.OcrType))
                    //{
                    //    return;
                    //}
                    //if (!TransOcr.TransOcrType.搜狗译图.GetHashCode().Equals(customer.OcrType))
                    //{
                    //    return;
                    //}
                    if (!string.IsNullOrEmpty(ocrGroupStr)
                    && !((HanZiOcr.HanZiOcrType)customer.OcrType).ToString().Contains(ocrGroupStr)
                    && !((MathOcr.MathOcrType)customer.OcrType).ToString().Contains(ocrGroupStr)
                    && !((TableOcr.TableOcrType)customer.OcrType).ToString().Contains(ocrGroupStr)
                    && !((DocOcr.DocOcrType)customer.OcrType).ToString().Contains(ocrGroupStr)
                    && !((TransOcr.TransOcrType)customer.OcrType).ToString().Contains(ocrGroupStr)
                    )
                    {
                        return;
                    }
                    var baseOcr = BaseRecHelper.GetInstance(baseOcrType, customer.OcrType);
                    //if (baseOcr.LstJsonNextProcessArray == null || baseOcr.LstJsonNextProcessArray?.Count <= 0)
                    //{
                    //    return;
                    //}
                    //baseOcr.IsFromLeftToRight = false;
                    //baseOcr.IsFromTopToDown = true;
                    //baseOcr.IsVerticalOcr = true;
                    //if (!baseOcr.IsSupportTrans)
                    //{
                    //    return;
                    //}
                    var content = baseOcr.GetResult(
                            new OcrContent()
                            {
                                url = imgUrl,
                                strBase64 = strTmp,
                                fileExt = fileExt,
                                from = from,
                                to = to,
                                IsAutoDuplicateSymbol = false,
                                IsAutoSpace = false,
                                IsAutoSymbol = true
                            });
                    if (content?.result?.resultType == ResutypeEnum.网页)
                    {
                        var state = new ProcessStateEntity();
                        while (state.state != OcrProcessState.处理成功 && state.state != OcrProcessState.处理失败)
                        {
                            state = baseOcr.QueryFileStatus(content.result.autoText);
                            System.Console.WriteLine("【" + content.processName + "】" + state + " " + state.desc);
                            Thread.Sleep(300);
                        }
                    }
                    else if (string.IsNullOrEmpty(content?.result?.spiltText))
                    {
                    }
                    else if (content?.result?.spiltText.Contains("  ") == true)
                    {

                    }
                    System.Console.WriteLine("处理完成：{6}【{0}】-{1},State:{2},ExecTime:{3}ms,Result:\n{4}"
                        , content.processName, content.threadId, content.state
                        , new TimeSpan(content.OcrTime.OcrServerEnd - content.OcrTime.UserStartRequest).TotalMilliseconds.ToString("F0")
                        , string.IsNullOrEmpty(content.result?.spiltLocText) ? content.result?.spiltText : content.result?.spiltLocText
                        , ServerTime.DateTime.ToString("MM-dd HH:mm:ss fff")
                        , !string.IsNullOrEmpty(content.result?.verticalText) ? "[竖排]" : "");
                });
            }

            //return;

            System.Console.WriteLine("全部处理完成！！");

            //if (System.Diagnostics.Debugger.IsAttached)
            {
                ServerUuidUtil.Mode = "Console";
                //ServerInfo.SetTestMode(true);
                ProcessNew.StartProcess();

                //var strWaitOcr = "{\"Account\":\"***********\",\"Token\":\"8c1d877a6b910bdbe00e5b7c986d2de6\",\"State\":0,\"ImgUrl\":\"https://download.ydstatic.com/ead/zhiyun/guanwang_cdn_2019/images/p-ocr/cookbook100K.jpg\",\"IsSupportUrl\":false,\"StrIndex\":\"9fb92d741b3946c0ac52437e020e3880\",\"UserType\":0,\"OcrType\":0,\"OcrGroup\":0,\"isLstOcrGroup\":false,\"ProcessId\":null,\"IsFromLeftToRight\":true,\"IsFromTopToDown\":true,\"DtAdd\":638775394106315141,\"DtUser\":638775393510383516,\"DtReceived\":0,\"DtExpired\":638775394206325137,\"DtOcrServerGet\":0,\"IsValidate\":false,\"FileExt\":\"png\",\"FileContentLength\":0,\"FromLanguage\":0,\"ToLanguage\":0,\"IsSupportVertical\":null,\"IsAutoFull2Half\":true,\"IsAutoSpace\":false,\"IsAutoSymbol\":true,\"IsAutoDuplicateSymbol\":true,\"IsTextToTable\":false,\"IsTextToTrans\":false,\"ClientTicks\":638775393440423773}";

                //var url = string.Format("http://localhost:19227/code.ashx?op=ocr&param={0}", HttpUtility.UrlEncode(strWaitOcr));
                ////WebClientSyncExt.GetHtml(url, 10);
                //ProcessNew.ProcessOcrByImg(strWaitOcr, true);

                System.Console.ReadLine();
            }
            //else
            //{
            //    ServerUuidUtil.Mode = "Service";
            //    var ServicesToRun = new System.ServiceProcess.ServiceBase[]
            //    {
            //        new EngineService()
            //    };
            //    System.ServiceProcess.ServiceBase.Run(ServicesToRun);
            //}
        }

        static void InitConfig()
        {
            ConfigHelper.InitConfig();

            //DaMaConfig.InitOtherConfig();

            ConfigHelper.InitThread();
        }

        private static void SetApplicationErrorHandler()
        {
            Application.ThreadException += new ThreadExceptionEventHandler(Application_ThreadException);
            Application.SetUnhandledExceptionMode(UnhandledExceptionMode.CatchException);
            AppDomain.CurrentDomain.UnhandledException += new UnhandledExceptionEventHandler(CurrentDomain_UnhandledException);
        }

        private static void SetLoggerContext()
        {
            string fileInfo = Path.Combine(AppDomain.CurrentDomain.SetupInformation.ApplicationBase, "Log4net.config");
            XmlConfigurator.ConfigureAndWatch(new FileInfo(fileInfo));
            ////添加ServiceStack的日志输出，从而将ServiceStack的错误打印出来
            //ServiceStack.Logging.LogManager.LogFactory = new ServiceStack.Logging.Log4Net.Log4NetFactory();
        }

        private static void Application_ThreadException(object sender, ThreadExceptionEventArgs t)
        {
            //if (ApplicationStartInfo.StartMode != StartModes.Service)
            //    if (MessageBox.Show(string.Format("应用发生了错误，请关闭后重试。\r\n\r\n错误消息为\"{0}\"", t.Exception.Message), "系统错误", MessageBoxButtons.OK, MessageBoxIcon.Error) == DialogResult.OK)
            //    {
            //        Application.UseWaitCursor = false;
            //        Application.Exit();
            //    }
        }

        private static void CurrentDomain_UnhandledException(object sender, UnhandledExceptionEventArgs e)
        {
            //Exception ex = (Exception)e.ExceptionObject;
            //_logger.Error("Get Application Current Domain Unhandle Thread Exception", ex);
            //if (ApplicationStartInfo.StartMode != StartModes.Service)
            //    if (MessageBox.Show(string.Format("应用发生了错误，请关闭后重试。\r\n\r\n错误消息为\"{0}\"", ex.Message), "系统错误", MessageBoxButtons.OK, MessageBoxIcon.Error) == DialogResult.OK)
            //    {
            //        Application.UseWaitCursor = false;
            //        Application.Exit();
            //    }
        }
    }
    public enum StartModes
    {
        Service,
        Form
    }
    public static class ApplicationStartInfo
    {
        static ApplicationStartInfo()
        {
            Argument = new Dictionary<string, string>();

            string[] args = Environment.GetCommandLineArgs();
            ApplicationName = args[0];

            for (int i = 1; i < args.Length; i++)
            {
                string arg = args[i].Trim().Replace("/", "");
                switch (arg.ToLower())
                {
                    case "form":
                        StartMode = StartModes.Form;
                        break;
                    default:
                        string[] command = arg.Split('=');
                        if (command.Length == 2)
                        {
                            Argument.Add(command[0].ToLower(), command[1]);
                        }
                        break;
                }
            }
        }

        public static StartModes StartMode { get; private set; } = StartModes.Form;

        public static IDictionary<string, string> Argument { get; private set; }

        public static string ApplicationName { get; private set; }
    }
}
