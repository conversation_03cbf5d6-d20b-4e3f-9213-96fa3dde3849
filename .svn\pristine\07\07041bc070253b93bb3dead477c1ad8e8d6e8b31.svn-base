﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace CommonLib
{
    public class AccountHelper
    {
        private static Dictionary<int, List<AccountDto>> dicAccountByOrderType = new Dictionary<int, List<AccountDto>>();

        public static void RegAccount(int ocrType, List<AccountDto> lstDto)
        {
            lock (dicAccountByOrderType)
            {
                if (!dicAccountByOrderType.ContainsKey(ocrType))
                {
                    dicAccountByOrderType.Add(ocrType, lstDto);
                }
                else
                {
                    var lst = dicAccountByOrderType[ocrType];
                    lstDto.ForEach(p =>
                    {
                        if (!lst.Exists(q => Equals(q.clientId, p.clientId) && Equals(q.secretKey, p.secretKey)))
                        {
                            lst.Add(p);
                        }
                    });
                    dicAccountByOrderType[ocrType] = lst;
                }
            }
        }

        public static AccountDto GetAccount(int ocrType)
        {
            AccountDto dto = null;
            if (dicAccountByOrderType.ContainsKey(ocrType))
            {
                dto = dicAccountByOrderType[ocrType].FirstOrDefault(p => p.isEnable);
            }
            return dto;
        }

        public static void Reset()
        {
            try
            {
                foreach (var value in dicAccountByOrderType.Values)
                {
                    value?.ForEach(obj =>
                    {
                        obj.isEnable = true;
                    });
                }
            }
            catch { }
        }

        public static void ForbidAccount(int ocrType, string clientId, string secredKey, bool isRemove = false, string strTmp = null)
        {
            try
            {
                if (!string.IsNullOrEmpty(strTmp))
                    LogHelper.Log.Info(string.Format("账号{1}，明细：{0}", strTmp, isRemove ? "移除" : "暂停"));
                if (dicAccountByOrderType.ContainsKey(ocrType))
                {
                    if (isRemove)
                    {
                        dicAccountByOrderType[ocrType].RemoveAll(p =>
                            Equals(clientId, p.clientId) && Equals(secredKey, p.secretKey));
                    }
                    else
                    {
                        dicAccountByOrderType[ocrType].FirstOrDefault(p => p.isEnable && Equals(clientId, p.clientId) && Equals(secredKey, p.secretKey)).isEnable = false;
                    }
                }
            }
            catch { }
        }
    }

    public class AccountDto
    {
        public string clientId { get; set; }

        public string secretKey { get; set; }

        public string remark { get; set; }

        public bool isEnable { get; set; } = true;
    }
}
