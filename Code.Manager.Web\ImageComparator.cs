﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Drawing;
using System.Drawing.Imaging;
using System.Drawing.Drawing2D;
using System.Runtime.InteropServices;
using System.IO;
using log4net;
using System.IO.Compression;
using System.Threading.Tasks;
using System.Threading;

namespace BaiDuAPI
{

    public class ImageComparator
    {
        public static readonly ILog _Log;

        static ImageComparator()
        {
            _Log = LogManager.GetLogger("ImgLog");
        }

        private static int NCodeIndex = 0;

        public static bool IsSave = true;

        private static Dictionary<string, List<ImageEntity>> LstCodes = new Dictionary<string, List<ImageEntity>>();
        public static Dictionary<string, List<ImageEntity>> LstHanZi = new Dictionary<string, List<ImageEntity>>();
        private static Dictionary<string, List<string>> LstTmpImg = new Dictionary<string, List<string>>();

        #region 汉字识别相关

        //加载 识别引擎
        [DllImport("AntiVC.dll")]
        static extern int VcodeInit(string PassWord);
        [DllImport("AntiVC.dll")]
        static extern bool SetCdsOption(int CdsFileIndex, int OptionIndex, int OptionValue);
        [DllImport("AntiVC.dll")]
        static extern bool GetVcodeFromBuffer(int CdsFileIndex, byte[] FileBuffer, int ImgBufLen, StringBuilder Vcode);
        [DllImport("AntiVC.dll")]
        static extern int LoadCdsFromBuffer(byte[] FileBuffer, int FileBufLen, string Password);

        public static void Init()
        {
            InitAntiVC();
            //LoadHanZi();
            LoadCodes();
        }

        public static void InitAntiVC()
        {
            _Log.Info("开始加载AntiVC…");
            try
            {
                using (FileStream fileStream = new FileStream(AppDomain.CurrentDomain.RelativeSearchPath + "\\HanZi.cds"
                    , FileMode.Open, FileAccess.Read, FileShare.Read))
                {
                    // 读取文件的 byte[]
                    byte[] bytes = new byte[fileStream.Length];
                    fileStream.Read(bytes, 0, bytes.Length);
                    fileStream.Close();
                    if (bytes != null && bytes.Length > 0)
                        NCodeIndex = LoadCdsFromBuffer(bytes, bytes.Length, "890128");
                }
            }
            catch (Exception oe)
            {
                _Log.Error(oe);
                Console.WriteLine(oe.Message);
            }
            _Log.Info("结束加载AntiVC！");
        }

        public static void LoadHanZi(string codePath = "")
        {
            if (string.IsNullOrEmpty(codePath))
            {
                codePath = AppDomain.CurrentDomain.RelativeSearchPath + "\\Spe.dll";
            }
            _Log.Info("开始加载汉字…" + codePath);
            if (File.Exists(codePath))
            {
                try
                {
                    var strContext = CommonCompress.DecompressString(File.ReadAllText(codePath));
                    var strTmp = "";
                    ImageComparator.LstHanZi = new System.Collections.Generic.Dictionary<string, System.Collections.Generic.List<ImageEntity>>();
                    int count = 0;
                    while (strContext.Contains("["))
                    {
                        count++;
                        strTmp = SubStringHorspool(strContext, "", "[");
                        strContext = SubStringHorspool(strContext, "[");

                        if (count > 75 && !string.IsNullOrEmpty(strContext) && !strContext.EndsWith("["))
                        {
                            strContext += "[";
                        }
                        var tmp = strTmp.Split(new string[] { "[" }, StringSplitOptions.RemoveEmptyEntries);
                        if (tmp != null && tmp.Length > 0)
                        {

                            var strName = "";
                            var contents = new string[0];
                            foreach (var item in tmp)
                            {
                                if (!item.Contains("【"))
                                    continue;
                                strName = SubStringHorspool(item, "", "]");
                                contents = SubStringHorspool(item, "【", "】").Split(new string[] { "," }, StringSplitOptions.RemoveEmptyEntries);
                                if (contents != null && contents.Length > 0)
                                {
                                    var lst = new System.Collections.Generic.List<ImageEntity>();
                                    string strColor = "";
                                    foreach (var con in contents)
                                    {
                                        strColor = con;
                                        var en = new ImageEntity() { IsHanZi = true };
                                        //0-1-2-3
                                        en.StrId = SubStringHorspool(strColor, "", "-");
                                        strColor = SubStringHorspool(strColor, "-");
                                        en.NAverageGray = GetInt32FromObject(strColor);
                                        //strColor = CommonMethod.SubString(strColor, "-");
                                        //en.NAverageColorR = BoxUtil.GetDoubleFromObject(CommonMethod.SubString(strColor, "", "-"));
                                        //strColor = CommonMethod.SubString(strColor, "-");
                                        //en.NAverageColorG = BoxUtil.GetDoubleFromObject(CommonMethod.SubString(strColor, "", "-"));
                                        //strColor = CommonMethod.SubString(strColor, "-");
                                        //en.NAverageColorB = BoxUtil.GetDoubleFromObject(strColor);
                                        lst.Add(en);
                                    }
                                    //LstCodes.Add(strName, new List<ImageEntity>());
                                    ImageComparator.LstHanZi.Add(strName, lst);
                                }
                                strName = null;
                                contents = null;
                            }
                        }
                        tmp = null;
                    }
                }
                catch (Exception oe)
                {
                    _Log.Error(oe);
                    Console.WriteLine(oe.Message);
                }
            }
            _Log.Info("结束加载汉字！");
        }

        public static void LoadCodes(string codePath = "")
        {
            if (string.IsNullOrEmpty(codePath))
            {
                codePath = AppDomain.CurrentDomain.RelativeSearchPath + "\\Rec.dll";
            }
            _Log.Info("开始加载图片…" + codePath);
            if (File.Exists(codePath))
            {
                try
                {
                    var strContext = CommonCompress.DecompressString(File.ReadAllText(codePath));
                    var tmp = strContext.Split(new string[] { "[" }, StringSplitOptions.RemoveEmptyEntries);
                    if (tmp != null && tmp.Length > 0)
                    {
                        ImageComparator.LstCodes = new System.Collections.Generic.Dictionary<string, System.Collections.Generic.List<ImageEntity>>();

                        //System.Threading.Tasks.Parallel.ForEach(tmp, item =>
                        //{
                        var strName = "";
                        var contents = new string[0];
                        foreach (var item in tmp)
                        {
                            if (!item.Contains("【"))
                                //return;
                                continue;
                            strName = SubStringHorspool(item, "", "]");
                            contents = SubStringHorspool(item, "【", "】").Split(new string[] { "," }, StringSplitOptions.RemoveEmptyEntries);
                            if (contents != null && contents.Length > 0)
                            {
                                string strColor = "";
                                var lst = new System.Collections.Generic.List<ImageEntity>();
                                //System.Threading.Tasks.Parallel.ForEach(contents, con =>
                                foreach (var con in contents)
                                {
                                    try
                                    {
                                        strColor = con;

                                        var en = new ImageEntity();
                                        //0-1-2-3
                                        en.StrId = SubStringHorspool(strColor, "", "-");
                                        strColor = SubStringHorspool(strColor, "-");
                                        en.NAverageGray = GetInt32FromObject(SubStringHorspool(strColor, "", "-"));
                                        strColor = SubStringHorspool(strColor, "-");
                                        en.NAverageColorR = GetInt32FromObject(SubStringHorspool(strColor, "", "-"));
                                        strColor = SubStringHorspool(strColor, "-");
                                        en.NAverageColorG = GetInt32FromObject(SubStringHorspool(strColor, "", "-"));
                                        strColor = SubStringHorspool(strColor, "-");
                                        en.NAverageColorB = GetInt32FromObject(strColor);
                                        lst.Add(en);
                                    }
                                    catch { }
                                }
                                //foreach (var con in contents)
                                //{
                                //    strColor = con;

                                //    var en = new ImageEntity();
                                //    //0-1-2-3
                                //    en.StrId = SubStringHorspool(strColor, "", "-");
                                //    strColor = SubStringHorspool(strColor, "-");
                                //    en.NAverageGray = GetInt32FromObject(SubStringHorspool(strColor, "", "-"));
                                //    strColor = SubStringHorspool(strColor, "-");
                                //    en.NAverageColorR = GetInt32FromObject(SubStringHorspool(strColor, "", "-"));
                                //    strColor = SubStringHorspool(strColor, "-");
                                //    en.NAverageColorG = GetInt32FromObject(SubStringHorspool(strColor, "", "-"));
                                //    strColor = SubStringHorspool(strColor, "-");
                                //    en.NAverageColorB = GetInt32FromObject(strColor);
                                //    lst.Add(en);
                                //}

                                //LstCodes.Add(strName, new List<ImageEntity>());
                                ImageComparator.LstCodes.Add(strName, lst);
                            }
                            strName = null;
                            contents = null;
                        }
                    }
                    tmp = null;
                }
                catch (Exception oe)
                {
                    _Log.Error(oe);
                    Console.WriteLine(oe.Message);
                }
            }
            _Log.Info("加载图片完毕！");
        }

        /// <summary>
        /// 从对象中获取Int32
        /// added by lwy 06-03-30 20:17
        /// </summary>
        /// <param name="o"></param>
        /// <param name="leap"></param>
        /// <returns></returns>
        private static int GetInt32FromObject(object o, int leap = 0)
        {
            int rtn = 0;
            if (o == null)
            {
                return leap;
            }

            try
            {
                rtn = Convert.ToInt32(o);
            }
            catch (Exception)
            {
                rtn = leap;
            }

            return rtn;
        }

        //Horspool匹配算法
        private static string SubStringHorspool(string str, string strStart, string strEnd = "")
        {
            int index = 0;
            if (!string.IsNullOrEmpty(strStart))
            {
                index = str.IndexOf(strStart);
                if (index >= 0)
                {
                    str = str.Substring(index + strStart.Length);
                }
                else
                    str = "";
            }
            if (!string.IsNullOrEmpty(str) && !string.IsNullOrEmpty(strEnd))
            {
                index = str.IndexOf(strEnd);
                if (index >= 0)
                {
                    str = str.Substring(0, index);
                }
                else
                    str = "";
            }
            strStart = null;
            strEnd = null;
            return str;
        }
        #endregion

        #region 图片识别相关

        /// <summary>
        /// 获取图标识别结果（坐标）
        /// </summary>
        /// <param name="codeStr"></param>
        /// <returns></returns>
        public static string GetCode(string codeStr, bool isXiaDan = false)
        {
            string strCode = "";
            if (!string.IsNullOrEmpty(codeStr))
            {
                var bit = GetImageFromBase64(codeStr);
                byte[] byt = null;
                if (bit != null)
                {
                    byt = ImageToByte(bit);
                }
                if (byt == null || byt.Length <= 0)
                {
                    byt = CommonCompress.StringToByte(codeStr);
                }
                if (byt != null && byt.Length > 5000)
                {
                    if (!isXiaDan)
                    {
                        string strName = "";
                        strCode = GetCode(byt, ref strName);
                        strName = null;
                    }
                    if (string.IsNullOrEmpty(strCode) && !string.IsNullOrEmpty(JiSuDaMa.StrChaoRenKey))
                    {
                        if (JiSuDaMa.IsJiaSu)
                        {
                            using (AutoResetEvent myResetEvent = new AutoResetEvent(false))
                            {
                                int count = 0;
                                new System.Threading.Thread(q =>
                                {
                                    Parallel.For(0, 3, p =>
                                    {
                                        try
                                        {
                                            var tmpCode = JiSuDaMa.GetCode(byt);
                                            if (!string.IsNullOrEmpty(tmpCode))
                                            {
                                                strCode = tmpCode;
                                                myResetEvent.Set();
                                            }
                                            count++;
                                            if (count == 3)
                                            {
                                                myResetEvent.Set();
                                            }
                                        }
                                        catch (Exception oe)
                                        {
                                            Console.WriteLine(oe.Message);
                                        }
                                    });
                                }) { Priority = ThreadPriority.Highest }.Start();
                                myResetEvent.WaitOne(5000);
                                myResetEvent.Dispose();
                            }
                        }
                        else
                            strCode = JiSuDaMa.GetCode(byt);
                    }
                }
                byt = null;
            }
            codeStr = null;
            return strCode.TrimStart(',').TrimEnd(',');
        }

        #region NewCode

        public static RectangleF rtgHanZi = new RectangleF(120, 0, 170, 25);//像素矩阵
        
        private static Dictionary<string, double> LstColorDif = new Dictionary<string, double>();

        public static ImageEntity GetEntityByStr(string strCode)
        {
            ImageEntity en = new ImageEntity();
            try
            {
                //0-1-2-3
                en.StrId = SubStringHorspool(strCode, "", "-");
                strCode = SubStringHorspool(strCode, "-");
                en.NAverageGray = GetInt32FromObject(SubStringHorspool(strCode, "", "-"));
                strCode = SubStringHorspool(strCode, "-");
                en.NAverageColorR = GetInt32FromObject(SubStringHorspool(strCode, "", "-"));
                strCode = SubStringHorspool(strCode, "-");
                en.NAverageColorG = GetInt32FromObject(SubStringHorspool(strCode, "", "-"));
                strCode = SubStringHorspool(strCode, "-");
                en.NAverageColorB = GetInt32FromObject(strCode);
            }
            catch (Exception oe)
            {
                Console.WriteLine(oe.Message);
            }
            return en;
        }

        public static Bitmap GetImageFromBase64(string base64string)
        {
            try
            {
                byte[] b = Convert.FromBase64String(base64string.Replace("%2B", "+"));
                MemoryStream ms = new MemoryStream(b);
                Bitmap bitmap = new Bitmap(ms);
                return bitmap;
            }
            catch (Exception oe)
            {
                Console.WriteLine(oe.Message);
            }
            return null;
        }
        public static void SaveImage(string strName, ImageEntity img, string strCodeStr)
        {
            string dir = "";
            if (!string.IsNullOrEmpty(strName))
            {
                if (img != null && !string.IsNullOrEmpty(img.StrId))
                {
                    dir = AppDomain.CurrentDomain.RelativeSearchPath + "\\user\\" + DateTime.Now.ToString("MMdd") + "\\rec\\" + strName;
                    if (!LstCodes.ContainsKey(strName))
                        LstCodes.Add(strName, new List<ImageEntity>());

                    if (!LstCodes[strName].Exists(p => ImageComparator.IsSameByEntity(img, p, GetMaxColorDifByName(strName))))
                    {
                        CreateDire(strName, dir);
                        LstCodes[strName].Add(img);
                        Bitmap bit = GetImageFromBase64(strCodeStr);
                        if (bit != null)
                            bit.Save(dir + "\\" + Guid.NewGuid().ToString() + ".jpg");
                    }
                }
            }
        }

        public static double GetMaxColorDifByName(string strName, bool isHanZi = false)
        {
            if (string.IsNullOrEmpty(strName) || !LstColorDif.ContainsKey(strName))
            {
                return isHanZi ? 0 : NColorDifference;
            }
            else
            {
                return LstColorDif[strName];
            }
        }

        public static void SaveImage(Bitmap source, List<int> lstTmp, string strName, bool isHanZi = false, string strOldRes = "")
        {
            string dir = "";
            if (!string.IsNullOrEmpty(strName))
            {
                if (!isHanZi)
                {
                    if (lstTmp == null || lstTmp.Count <= 0)
                    {
                        dir = "newpic\\error\\" + strName;
                        CreateDire(strName, dir);
                        source.Save(dir + "\\[" + strOldRes + "]" + Guid.NewGuid().ToString() + ".jpg", ImageFormat.Jpeg);
                    }
                    else
                    {
                        dir = "newpic\\rec\\" + strName;
                        if (!LstCodes.ContainsKey(strName))
                            LstCodes.Add(strName, new List<ImageEntity>());

                        ImageType type = GetImgType(source);
                        var lst = SpiltImage(source, type);
                        foreach (var item in lstTmp)
                        {
                            //获取转换后的图片的直方图
                            var entity = GetHash(lst[item]);
                            if (!LstCodes[strName].Exists(p => ImageComparator.IsSameByEntity(entity, p, GetMaxColorDifByName(strName))))
                            {
                                CreateDire(strName, dir);
                                LstCodes[strName].Add(entity);
                                lst[item].Save(dir + "\\" + Guid.NewGuid().ToString() + ".jpg");
                            }
                        }
                        lst.ForEach(p => { p.Dispose(); });
                        lst = null;
                    }
                }
                else
                {
                    if (!LstHanZi.ContainsKey(strName))
                        LstHanZi.Add(strName, new List<ImageEntity>());

                    string strRealName = strName;

                    //获取转换后的图片的直方图
                    var entity = GetHash(source, true);
                    if (strRealName.StartsWith("e"))
                    {
                        strRealName = strRealName.Replace("e", "");

                        if (LstHanZi.ContainsKey(strRealName))
                        {
                            if (LstHanZi[strRealName].Exists(p => ImageComparator.IsSameByEntity(entity, p, GetMaxColorDifByName(strName), false, true)))
                            {
                                return;
                            }
                        }
                        dir = "newpic\\hanzi\\error\\" + strRealName;
                    }
                    else
                        dir = "newpic\\hanzi\\" + strName;

                    if (!LstHanZi[strName].Exists(p => ImageComparator.IsSameByEntity(entity, p, GetMaxColorDifByName(strName), false, true)))
                    {
                        LstHanZi[strName].Add(entity);
                        CreateDire(strRealName, dir, true);
                        source.Save(dir + "\\" + Guid.NewGuid().ToString() + ".jpg");
                    }
                }
            }
            else
            {
                if (!isHanZi)
                {
                    dir = "newpic\\no\\";
                    CreateDire(strName, dir);
                    source.Save(dir + "\\" + Guid.NewGuid().ToString() + ".jpg", ImageFormat.Jpeg);
                }
                else
                {
                    dir = "newpic\\hanzi\\error";
                    CreateDire(strName, dir);
                    source.Save(dir + "\\" + Guid.NewGuid().ToString() + ".jpg");
                }
            }
        }

        public static void SaveImageByte(byte[] buffer, string strName, string strOldRes = "")
        {
            var source = CommonCompress.ByteToString(buffer);
            if (!string.IsNullOrEmpty(source))
            {
                string dir = "";
                if (!string.IsNullOrEmpty(strName))
                {
                    dir = "newpic\\" + strName.TrimStart('e') + ".txt";
                    if (!string.IsNullOrEmpty(strOldRes))
                    {
                        source = "[" + strOldRes + "]" + source;
                    }
                }
                else
                {
                    dir = "newpic\\no.txt";
                }
                if (!LstTmpImg.ContainsKey(dir))
                    LstTmpImg.Add(dir, new List<string>());
                LstTmpImg[dir].Add(source);
            }
        }

        public static void SaveErrorImage(Bitmap source, string strName)
        {
            string dir = "newpic\\name\\" + (!string.IsNullOrEmpty(strName) ? strName : "");
            CreateDire(strName, dir);
            source.Save(dir + "\\" + Guid.NewGuid().ToString() + ".jpg", ImageFormat.Jpeg);
        }

        public static void SaveRecErrorImage(Bitmap source, string strName)
        {
            string dir = "newpic\\recError\\" + (!string.IsNullOrEmpty(strName) ? strName : "");
            CreateDire(strName, dir);
            source.Save(dir + "\\" + Guid.NewGuid().ToString() + ".jpg", ImageFormat.Jpeg);
        }

        private static void CreateDire(string strName, string path, bool isHanZi = false)
        {
            if (!System.IO.Directory.Exists(path))
            {
                System.IO.Directory.CreateDirectory(path);
            }
            if (!string.IsNullOrEmpty(strName))
            {
                if (!isHanZi && !LstCodes.ContainsKey(strName))
                    LstCodes.Add(strName, new List<ImageEntity>());
                else if (isHanZi && !LstHanZi.ContainsKey(strName))
                    LstHanZi.Add(strName, new List<ImageEntity>());
            }
        }

        public static List<ImageEntity> SpiltImageEntity(Bitmap source, ImageType type)
        {
            List<ImageEntity> lstResult = new List<ImageEntity>();
            if (source != null && source.Width > 250)
            {
                List<Bitmap> lstTmp = SpiltImage(source, type);

                for (int i = 0; i < lstTmp.Count; i++)
                {
                    lstResult.Add(null);
                }
                //DateTime dtStart = DateTime.Now;

                System.Threading.Tasks.Parallel.For(0, lstTmp.Count, i =>
                {
                    try
                    {
                        lstResult[i] = GetHash(lstTmp[i]);
                    }
                    catch { }
                });

                lstTmp.ForEach(p => p.Dispose());
                lstTmp = null;
                //Console.WriteLine("图片分拆耗时：" + new TimeSpan(DateTime.Now.Ticks - dtStart.Ticks).TotalMilliseconds.ToString("F0") + "ms");
            }
            return lstResult;
        }

        public static List<Bitmap> SpiltImage(Bitmap source, ImageType type)
        {
            List<Bitmap> lstTmp = new List<Bitmap>();
            if (source != null && source.Width > 250)
            {
                int width, height;
                RectangleF part;
                switch (type)
                {
                    case ImageType.Eight:
                        for (int x = 0; x < 4; x++)
                        {
                            for (int y = 0; y < 2; y++)
                            {
                                width = 6 * (x + 1) + x * 66 - 1;
                                height = 41 + y * 66 + 6 * y;
                                part = new RectangleF(width, height, 66, 66);//像素矩阵
                                Bitmap s = ImageToJPG(source.Clone(part, PixelFormat.DontCare));
                                lstTmp.Add(s);
                            }
                        }
                        break;
                    case ImageType.Eighteen:
                        for (int x = 0; x < 6; x++)
                        {
                            for (int y = 0; y < 3; y++)
                            {
                                width = 4 * (x + 1) + x * 44;
                                height = 40 + y * 44 + 4 * y;
                                part = new RectangleF(width, height, 44, 44);//像素矩阵
                                Bitmap s = ImageToJPG(source.Clone(part, PixelFormat.DontCare));
                                lstTmp.Add(s);
                            }
                        }
                        break;
                    case ImageType.ThirtySix:
                        break;
                    default:
                        break;
                }

                System.Threading.Tasks.Parallel.For(0, lstTmp.Count, i =>
                {
                    try
                    {
                        lstTmp[i] = GetCutWhiteImg(lstTmp[i], false);
                    }
                    catch { }
                });
                //Console.WriteLine("图片分拆耗时：" + new TimeSpan(DateTime.Now.Ticks - dtStart.Ticks).TotalMilliseconds.ToString("F0") + "ms");
            }
            else
            {
                lstTmp.Add(source);
            }
            return lstTmp;
        }

        private static ImageType GetImgType(Bitmap source)
        {
            var result = ImageType.Eight;
            try
            {
                //ImageComparator.ClearNoise(ref bit, 255, 1);//清除黑色噪点
                int count = 0;
                Color color;

                PointBitmap bit = new PointBitmap(source);
                try
                {
                    bit.LockBits();
                    for (int x = 1; x < 4; x++)
                    {
                        for (int y = 0; y < 2; y++)
                        {

                            int width = 6 * (x + 1) + x * 66 - 1 - 3;
                            int height = 41 + y * 66 + 6 * y;
                            for (int i = 1; i < 6; i++)
                            {
                                color = bit.GetPixel(width, height + (i + 1) * 10);
                                if ((color.R + color.G + color.B) / 3 < 240)
                                {
                                    count++;
                                }
                            }
                        }
                    }
                }
                catch (Exception oe)
                {
                    Console.WriteLine(oe.Message);
                }
                finally
                {
                    bit.UnlockBits();
                    bit = null;
                }
                if (count < 8)
                //if ((count * 1.0 / 36) * 100 <= 6)
                {
                    result = ImageType.Eight;
                }
                else
                {
                    result = ImageType.Eighteen;
                    //count = 0;
                    //for (int x = 1; x < 6; x++)
                    //{
                    //    for (int y = 0; y < 3; y++)
                    //    {
                    //        int width = 4 * (x + 1) + x * 44 - 2;
                    //        int height = 41 + y * 44 + 4 * y;
                    //        for (int i = 0; i < 4; i++)
                    //        {
                    //            color = bit.GetPixel(width, height + (i + 1) * 10);
                    //            if ((color.R + color.G + color.B) / 3 < 240)
                    //            {
                    //                count++;
                    //            }
                    //        }
                    //    }
                    //}
                    //if ((count * 1.0 / 60) * 100 <= 5)
                    //{
                    //    result = ImageType.Eighteen;
                    //}
                }
            }
            catch (Exception oe)
            {
                Console.WriteLine(oe.Message);
            }
            return result;
        }

        public static ImageType GetImgType(byte[] byt)
        {
            var result = ImageType.Eight;
            using (Bitmap source = new Bitmap(Bitmap.FromStream(new MemoryStream(byt))))
            {
                result = GetImgType(source);
                source.Dispose();
            }
            return result;
        }

        public static ImageEntity GetHash(Bitmap SourceImg, bool isHanZi = false)
        {
            var reslut = new ImageEntity();
            using (var bit = ReduceSize(SourceImg))
            {
                Byte[] colorRValues = null;
                Byte[] colorGValues = null;
                Byte[] colorBValues = null;
                Byte[] grayValues = ReduceColor(bit, isHanZi, ref colorRValues, ref colorGValues, ref colorBValues);
                if (grayValues != null)
                    reslut.NAverageGray = CalcAverage(grayValues);
                if (colorRValues != null)
                    reslut.NAverageColorR = CalcAverage(colorRValues);
                if (colorGValues != null)
                    reslut.NAverageColorG = CalcAverage(colorGValues);
                if (colorBValues != null)
                    reslut.NAverageColorB = CalcAverage(colorBValues);
                reslut.StrId = ComputeBits(grayValues, Convert.ToByte(reslut.NAverageGray));
                grayValues = null;
                colorRValues = null;
                colorRValues = null;
                colorGValues = null;
                colorBValues = null;
                bit.Dispose();
            }
            return reslut;
        }

        // Step 1 : Reduce size to 8*8
        private static Bitmap ReduceSize(Bitmap SourceImg, int width = 16, int height = 16)
        {
            return new Bitmap(SourceImg.GetThumbnailImage(width, height, () => { return false; }, IntPtr.Zero));
        }

        public static void ErZhiHua(ref Bitmap image, double dgGrayValue)
        {
            if (image != null)
            {
                PointBitmap bit = new PointBitmap(image);
                try
                {
                    bit.LockBits();
                    Color color; double grayValue;
                    for (int x = 0; x < bit.Width; x++)
                    {
                        for (int y = 0; y < bit.Height; y++)
                        {
                            color = bit.GetPixel(x, y);
                            grayValue = (color.R * 30 + color.G * 59 + color.B * 11) / 100;
                            if (grayValue < dgGrayValue)
                            {
                                bit.SetPixel(x, y, Color.FromArgb(0, 0, 0));
                            }
                            else
                            {
                                bit.SetPixel(x, y, Color.FromArgb(255, 255, 255));
                            }
                        }
                    }
                }
                catch (Exception oe)
                {
                    Console.WriteLine(oe.Message);
                }
                finally
                {
                    bit.UnlockBits();
                    bit = null;
                }
            }
        }

        /// <summary>
        ///  去掉杂点（适合杂点/杂线粗为1）
        /// </summary>
        /// <param name="dgGrayValue">背前景灰色界限</param>
        /// <returns></returns>
        public static void ClearNoise(ref Bitmap bmpobj, double dgGrayValue, int MaxNearPoints = 2)
        {
            PointBitmap bit = new PointBitmap(bmpobj);
            try
            {
                bit.LockBits();
                Color piexl;
                int nearDots = 0;
                int xStart = bit.Width / 3;
                int xEnd = xStart * 2;
                int yStart = bit.Height / 3;
                int yEnd = yStart * 2;
                //逐点判断
                for (int i = 0; i < bit.Width; i++)
                    for (int j = 0; j < bit.Height; j++)
                    {
                        if (i > xStart && i < xEnd && j > yStart && j < yEnd)
                            continue;
                        piexl = bit.GetPixel(i, j);
                        if (piexl.R < dgGrayValue)
                        {
                            nearDots = 0;
                            //判断周围8个点是否全为空
                            if (i == 0 || i == bit.Width - 1 || j == 0 || j == bit.Height - 1)  //边框全去掉
                            {
                                bit.SetPixel(i, j, Color.FromArgb(255, 255, 255));
                            }
                            else
                            {
                                if (bit.GetPixel(i - 1, j - 1).R < dgGrayValue) nearDots++;
                                if (bit.GetPixel(i, j - 1).R < dgGrayValue) nearDots++;
                                if (bit.GetPixel(i + 1, j - 1).R < dgGrayValue) nearDots++;
                                if (bit.GetPixel(i - 1, j).R < dgGrayValue) nearDots++;
                                if (bit.GetPixel(i + 1, j).R < dgGrayValue) nearDots++;
                                if (bit.GetPixel(i - 1, j + 1).R < dgGrayValue) nearDots++;
                                if (bit.GetPixel(i, j + 1).R < dgGrayValue) nearDots++;
                                if (bit.GetPixel(i + 1, j + 1).R < dgGrayValue) nearDots++;
                            }

                            if (nearDots <= MaxNearPoints)
                                bit.SetPixel(i, j, Color.FromArgb(255, 255, 255));   //去掉单点 && 粗细小3邻边点
                        }
                        //else  //背景
                        //    bmpobj.SetPixel(i, j, Color.FromArgb(255, 255, 255));
                    }
            }
            catch (Exception oe)
            {
                Console.WriteLine(oe.Message);
            }
            finally
            {
                bit.UnlockBits();
                bit = null;
            }
        }

        // Step 2 : Reduce Color
        private static Byte[] ReduceColor(Bitmap image, bool isHanZi, ref byte[] colorRValues, ref byte[] colorGValues, ref byte[] colorBValues)
        {
            Byte[] grayValues = null;
            PointBitmap bit = new PointBitmap(image);
            try
            {
                bit.LockBits();
                grayValues = new Byte[bit.Width * bit.Height];
                if (!isHanZi)
                {
                    colorRValues = new Byte[(bit.Width - 5) * (bit.Height - 5)];
                    colorGValues = new Byte[(bit.Width - 5) * (bit.Height - 5)];
                    colorBValues = new Byte[(bit.Width - 5) * (bit.Height - 5)];
                }
                Color color; byte grayValue;
                for (int x = 0; x < bit.Width; x++)
                    for (int y = 0; y < bit.Height; y++)
                    {
                        color = bit.GetPixel(x, y);
                        if (!isHanZi && x >= 5 && y >= 5)
                        {
                            colorRValues[(x - 5) * (bit.Width - 5) + (y - 5)] = (byte)(color.R);
                            colorGValues[(x - 5) * (bit.Width - 5) + (y - 5)] = (byte)(color.G);
                            colorBValues[(x - 5) * (bit.Width - 5) + (y - 5)] = (byte)(color.B);
                        }
                        grayValue = (byte)((color.R * 30 + color.G * 59 + color.B * 11) / 100);

                        grayValues[x * bit.Width + y] = grayValue;
                    }
            }
            catch (Exception oe)
            {
                Console.WriteLine(oe.Message);
            }
            finally
            {
                bit.UnlockBits();
                bit = null;
            }
            return grayValues;
        }

        /// <summary>
        /// 中值滤波算法处理
        /// </summary>
        /// <param name="bmp">原始图片</param>
        /// <param name="bmp">是否是彩色位图</param>
        /// <param name="windowRadius">过滤半径</param>
        public static Bitmap ColorfulBitmapMedianFilterFunction(Bitmap bitSource, int windowRadius, bool IsColorfulBitmap)
        {
            if (windowRadius < 1)
            {
                throw new Exception("过滤半径小于1没有意义");
            }
            //创建一个新的位图对象
            Bitmap bmp = new Bitmap(bitSource.Width, bitSource.Height);

            //PointBitmap bitSource = new PointBitmap(srcBmp);
            //bitSource.LockBits();
            //PointBitmap bitNew = new PointBitmap(bmp);
            //bitNew.LockBits();

            try
            {
                //存储该图片所有点的RGB值
                byte[,] mR, mG, mB;
                mR = new byte[bitSource.Width, bitSource.Height];
                if (IsColorfulBitmap)
                {
                    mG = new byte[bitSource.Width, bitSource.Height];
                    mB = new byte[bitSource.Width, bitSource.Height];
                }
                else
                {
                    mG = mR;
                    mB = mR;
                }

                for (int i = 0; i <= bitSource.Width - 1; i++)
                {
                    for (int j = 0; j <= bitSource.Height - 1; j++)
                    {
                        mR[i, j] = bitSource.GetPixel(i, j).R;
                        if (IsColorfulBitmap)
                        {
                            mG[i, j] = bitSource.GetPixel(i, j).G;
                            mB[i, j] = bitSource.GetPixel(i, j).B;
                        }
                    }
                }

                mR = MedianFilterFunction(mR, windowRadius);
                if (IsColorfulBitmap)
                {
                    mG = MedianFilterFunction(mG, windowRadius);
                    mB = MedianFilterFunction(mB, windowRadius);
                }
                else
                {
                    mG = mR;
                    mB = mR;
                }
                for (int i = 0; i <= bmp.Width - 1; i++)
                {
                    for (int j = 0; j <= bmp.Height - 1; j++)
                    {
                        bmp.SetPixel(i, j, Color.FromArgb(mR[i, j], mG[i, j], mB[i, j]));
                    }
                }
            }
            catch (Exception oe)
            {
                Console.WriteLine(oe.Message);
            }
            finally
            {
                //bitSource.UnlockBits();
                //bitNew.UnlockBits();
                //bitSource = null;
                //bitNew = null;
            }
            return bmp;
        }

        /// <summary>
        /// 对矩阵M进行中值滤波
        /// </summary>
        /// <param name="m">矩阵M</param>
        /// <param name="windowRadius">过滤半径</param>
        /// <returns>结果矩阵</returns>
        private static byte[,] MedianFilterFunction(byte[,] m, int windowRadius)
        {
            int width = m.GetLength(0);
            int height = m.GetLength(1);

            byte[,] lightArray = new byte[width, height];

            //开始滤波
            for (int i = 0; i <= width - 1; i++)
            {
                for (int j = 0; j <= height - 1; j++)
                {
                    //得到过滤窗口矩形
                    Rectangle rectWindow = new Rectangle(i - windowRadius, j - windowRadius, 2 * windowRadius + 1, 2 * windowRadius + 1);
                    if (rectWindow.Left < 0) rectWindow.X = 0;
                    if (rectWindow.Top < 0) rectWindow.Y = 0;
                    if (rectWindow.Right > width - 1) rectWindow.Width = width - 1 - rectWindow.Left;
                    if (rectWindow.Bottom > height - 1) rectWindow.Height = height - 1 - rectWindow.Top;
                    //将窗口中的颜色取到列表中
                    List<byte> windowPixelColorList = new List<byte>();
                    for (int oi = rectWindow.Left; oi <= rectWindow.Right - 1; oi++)
                    {
                        for (int oj = rectWindow.Top; oj <= rectWindow.Bottom - 1; oj++)
                        {
                            windowPixelColorList.Add(m[oi, oj]);
                        }
                    }
                    //排序
                    windowPixelColorList.Sort();
                    //取中值
                    byte middleValue = 0;
                    if ((windowRadius * windowRadius) % 2 == 0)
                    {
                        //如果是偶数
                        middleValue = Convert.ToByte((windowPixelColorList[windowPixelColorList.Count / 2] + windowPixelColorList[windowPixelColorList.Count / 2 - 1]) / 2);
                    }
                    else
                    {
                        //如果是奇数
                        middleValue = windowPixelColorList[(windowPixelColorList.Count - 1) / 2];
                    }
                    //设置为中值
                    lightArray[i, j] = middleValue;
                }
            }
            return lightArray;
        }

        // Step 3 : Average the colors
        private static double CalcAverage(byte[] values)
        {
            int sum = 0;
            for (int i = 0; i < values.Length; i++)
                sum += (int)values[i];
            return sum * 1.0 / values.Length;
        }

        // Step 4 : Compute the bits
        private static String ComputeBits(byte[] values, byte averageValue)
        {
            char[] result = new char[values.Length];
            for (int i = 0; i < values.Length; i++)
            {
                if (values[i] < averageValue)
                    result[i] = '0';
                else
                    result[i] = '1';
            }
            return new String(result);
        }

        // Compare hash
        public static bool IsSame(ImageEntity a, List<ImageEntity> lst, double nmaxColorDif, bool isOpenFile = false, bool isHanZi = false)
        {
            bool result = false;
            foreach (var b in lst)
            {
                result = IsSameByEntity(a, b, nmaxColorDif, isOpenFile, isHanZi);
                if (result)
                    break;
                //int count = 0;
                //if (a.NAverageColor <= b.NAverageColor + 2 && a.NAverageColor >= b.NAverageColor - 2)
                //{
                //    count = CalcSimilarDegree(a.StrId, b.StrId);
                //    if ((count * 1.0 / a.StrId.Length) * 100 <= 3)
                //    {
                //        result = true;
                //        break;
                //    }
                //}
            }
            return result;
        }

        // Compare hash
        public static bool IsSameByEntity(ImageEntity a, ImageEntity b, double maxColorDif, bool isOpenFile = false, bool isHanZi = false)
        {
            bool result = false;
            if (!isHanZi)
            {
                if (a.NAverageGray <= b.NAverageGray + NGrayDifference && a.NAverageGray >= b.NAverageGray - NGrayDifference)
                {
                    if ((a.NAverageColorR <= b.NAverageColorR + maxColorDif && a.NAverageColorR >= b.NAverageColorR - maxColorDif)
                        && (a.NAverageColorG <= b.NAverageColorG + maxColorDif && a.NAverageColorG >= b.NAverageColorG - maxColorDif)
                        && (a.NAverageColorB <= b.NAverageColorB + maxColorDif + 3 && a.NAverageColorB >= b.NAverageColorB - maxColorDif - 3))
                    {
                        int count = CalcSimilarDegree(a.StrId, b.StrId);
                        if (Math.Floor((count * 1.0 / a.StrId.Length) * 100) <= NMaxDifPercent)
                        {
                            result = true;
                        }
                    }
                }
            }
            else
            {
                if (a.NAverageGray <= b.NAverageGray + NHanZiGrayDifference && a.NAverageGray >= b.NAverageGray - NHanZiGrayDifference)
                {
                    //if ((a.NAverageColorR <= b.NAverageColorR + NHanZiColorDifference && a.NAverageColorR >= b.NAverageColorR - NHanZiColorDifference)
                    //    && (a.NAverageColorG <= b.NAverageColorG + NHanZiColorDifference && a.NAverageColorG >= b.NAverageColorG - NHanZiColorDifference))
                    {
                        int count = CalcSimilarDegree(a.StrId, b.StrId);
                        if (Math.Floor((count * 1.0 / a.StrId.Length) * 100) <= NHanZiMaxDifPercent)
                        {
                            result = true;
                        }
                    }
                }
            }
            return result;
        }

        public static double NGrayDifference = 1.5;
        public static double NColorDifference = 5;
        public static double NMaxDifPercent = 10;

        public static double NHanZiGrayDifference = 1.5;
        public static double NHanZiMaxDifPercent = 5;

        // Compare hash
        public static int CalcSimilarDegree(string a, string b)
        {
            if (a.Length != b.Length)
                throw new ArgumentException();
            int count = 0;
            for (int i = 0; i < a.Length; i++)
            {
                if (a[i] != b[i])
                    count++;
            }
            return count;
        }

        /// <summary>
        /// 2014.6.12 剪去图片空余白边
        /// </summary>
        /// <param name="FilePath">源文件</param>
        /// <param name="WhiteBarRate">保留空白边比例</param>
        public static Bitmap CutImageWhitePart(Bitmap bmp)
        {
            RectangleF rtg = CutImageWhitePartSize(bmp);
            return Cut(bmp, rtg);
        }

        /// <summary>
        /// 得到灰度图像前景背景的临界值 最大类间方差法
        /// </summary>
        /// <returns>前景背景的临界值</returns>
        private static double GetDgGrayValue(PointBitmap bmpobj)
        {
            int[] pixelNum = new int[256];           //图象直方图，共256个点
            int n, n1, n2;
            int total;                              //total为总和，累计值
            double m1, m2, sum, csum, fmax, sb;     //sb为类间方差，fmax存储最大方差值
            int k, t, q;
            double threshValue = 1;                      // 阈值
            //生成直方图
            for (int i = 0; i < bmpobj.Width; i++)
            {
                for (int j = 0; j < bmpobj.Height; j++)
                {
                    //返回各个点的颜色，以RGB表示
                    pixelNum[bmpobj.GetPixel(i, j).R]++;            //相应的直方图加1
                }
            }
            //直方图平滑化
            for (k = 0; k <= 255; k++)
            {
                total = 0;
                for (t = -2; t <= 2; t++)              //与附近2个灰度做平滑化，t值应取较小的值
                {
                    q = k + t;
                    if (q < 0)                     //越界处理
                        q = 0;
                    if (q > 255)
                        q = 255;
                    total = total + pixelNum[q];    //total为总和，累计值
                }
                pixelNum[k] = (int)((float)total / 5.0 + 0.5);    //平滑化，左边2个+中间1个+右边2个灰度，共5个，所以总和除以5，后面加0.5是用修正值
            }
            //求阈值
            sum = csum = 0.0;
            n = 0;
            //计算总的图象的点数和质量矩，为后面的计算做准备
            for (k = 0; k <= 255; k++)
            {
                sum += (double)k * (double)pixelNum[k];     //x*f(x)质量矩，也就是每个灰度的值乘以其点数（归一化后为概率），sum为其总和
                n += pixelNum[k];                       //n为图象总的点数，归一化后就是累积概率
            }

            fmax = -1.0;                          //类间方差sb不可能为负，所以fmax初始值为-1不影响计算的进行
            n1 = 0;
            for (k = 0; k < 256; k++)                  //对每个灰度（从0到255）计算一次分割后的类间方差sb
            {
                n1 += pixelNum[k];                //n1为在当前阈值遍前景图象的点数
                if (n1 == 0) { continue; }            //没有分出前景后景
                n2 = n - n1;                        //n2为背景图象的点数
                if (n2 == 0) { break; }               //n2为0表示全部都是后景图象，与n1=0情况类似，之后的遍历不可能使前景点数增加，所以此时可以退出循环
                csum += (double)k * pixelNum[k];    //前景的“灰度的值*其点数”的总和
                m1 = csum / n1;                     //m1为前景的平均灰度
                m2 = (sum - csum) / n2;               //m2为背景的平均灰度
                sb = (double)n1 * (double)n2 * (m1 - m2) * (m1 - m2);   //sb为类间方差
                if (sb > fmax)                  //如果算出的类间方差大于前一次算出的类间方差
                {
                    fmax = sb;                    //fmax始终为最大类间方差（otsu）
                    threshValue = k;              //取最大类间方差时对应的灰度的k就是最佳阈值
                }
            }
            return threshValue;
        }

        public static RectangleF CutImageWhitePartSize(Bitmap bmp)
        {
            //PointBitmap bit = new PointBitmap(new Bitmap(bmp));
            RectangleF rtg = new RectangleF(0, 0, bmp.Width, bmp.Height);
            int top = 0, left = 0;
            int right = bmp.Width, bottom = bmp.Height;


            PointBitmap bit = new PointBitmap(bmp);
            try
            {
                bit.LockBits();

                //寻找最上面的标线,从左(0)到右，从上(0)到下
                for (int i = 0; i < bit.Height; i++)//行
                {
                    bool find = false;
                    for (int j = 0; j < bit.Width; j++)//列
                    {
                        Color c = bit.GetPixel(j, i);
                        if (IsWhite(c))
                        {
                            top = i;
                            find = true;
                            break;
                        }
                    }
                    if (find) break;
                }
                //寻找最左边的标线，从上（top位）到下，从左到右
                for (int i = 0; i < bit.Width; i++)//列
                {
                    bool find = false;
                    for (int j = top; j < bit.Height; j++)//行
                    {
                        Color c = bit.GetPixel(i, j);
                        if (IsWhite(c))
                        {
                            left = i;
                            find = true;
                            break;
                        }
                    }
                    if (find) break; ;
                }
                ////寻找最下边标线，从下到上，从左到右
                for (int i = bit.Height - 1; i >= 0; i--)//行
                {
                    bool find = false;
                    for (int j = left; j < bit.Width; j++)//列
                    {
                        Color c = bit.GetPixel(j, i);
                        if (IsWhite(c))
                        {
                            bottom = i;
                            find = true;
                            break;
                        }
                    }
                    if (find) break;
                }
                //寻找最右边的标线，从上到下，从右往左
                for (int i = bit.Width - 1; i >= 0; i--)//列
                {
                    bool find = false;
                    for (int j = 0; j < bottom; j++)//行
                    {
                        Color c = bit.GetPixel(i, j);
                        if (IsWhite(c))
                        {
                            right = i;
                            find = true;
                            break;
                        }
                    }
                    if (find) break;
                }
            }
            catch (Exception oe)
            {
                Console.WriteLine(oe.Message);
            }
            finally
            {
                bit.UnlockBits();
                bit = null;
            }
            //int iWidth = right - left;
            //int iHeight = bottom - left;
            //int blockWidth = Convert.ToInt32(iWidth * WhiteBarRate / 100);

            rtg = new RectangleF(left, top, right - left, bottom - top);
            if (bmp.Width >= 16 && rtg.Width < 16)
            {
                rtg.X -= 16 - rtg.Width;
                rtg.Width = 16;
            }
            if (bmp.Height >= 16 && rtg.Height < 16)
            {
                rtg.Y -= 16 - rtg.Height;
                rtg.Height = 16;
            }
            return rtg;
        }


        /// <summary>
        /// 2014.6.13 来源于网络的一个函数
        /// </summary>
        /// <param name="b"></param>
        /// <param name="StartX"></param>
        /// <param name="StartY"></param>
        /// <param name="iWidth"></param>
        /// <param name="iHeight"></param>
        /// <returns></returns>
        public static Bitmap Cut(Bitmap b, RectangleF rtg)
        {
            if (b == null)
            {
                return null;
            }
            int StartX = (int)rtg.X;
            int StartY = (int)rtg.Y;
            int w = b.Width;
            int h = b.Height;
            if (StartX >= w || StartY >= h)
            {
                return null;
            }

            int iWidth = (int)rtg.Width;
            int iHeight = (int)rtg.Height;
            if (StartX + iWidth > w)
            {
                iWidth = w - StartX;
            }
            if (StartY + iHeight > h)
            {
                iHeight = h - StartY;
            }
            try
            {
                Bitmap bmpOut = new Bitmap(iWidth, iHeight, b.PixelFormat);
                Graphics g = Graphics.FromImage(bmpOut);
                g.DrawImage(b, new Rectangle(0, 0, iWidth, iHeight), new Rectangle(StartX, StartY, iWidth, iHeight), GraphicsUnit.Pixel);
                g.Dispose();
                return bmpOut;
            }
            catch
            {
                return null;
            }
        }

        /// <summary>
        /// 2014.6.12 判断白色与否，非纯白色
        /// </summary>
        /// <param name="c"></param>
        /// <returns></returns>
        public static bool IsWhite(Color c)
        {
            if (c.R < 245 || c.G < 245 || c.B < 245)
                return true;
            else return false;
        }
        #endregion
        ///// <summary>
        ///// 获取图片识别结果（序号）
        ///// </summary>
        ///// <param name="buffer"></param>
        ///// <param name="strName"></param>
        ///// <param name="type"></param>
        ///// <returns></returns>
        //private static List<int> GetCodes(byte[] buffer, ref string strName, out ImageType type)
        //{
        //    var codes = new List<int>();
        //    var bit = new Bitmap(Bitmap.FromStream(new MemoryStream(buffer)));
        //    type = GetImgType(bit);
        //    if (type == ImageType.Eight)
        //    {
        //        if (string.IsNullOrEmpty(strName))
        //        {
        //            StringBuilder codeBuilder = new StringBuilder();
        //            if (GetVcodeFromBuffer(1, buffer, buffer.Length, codeBuilder))
        //                strName = codeBuilder.ToString();
        //        }
        //        var names = strName.Split(new string[] { "," }, StringSplitOptions.RemoveEmptyEntries);
        //        if (names != null && names.Length > 0)
        //        {
        //            var lstImg = SpiltImage(bit, type);
        //            if (lstImg != null && lstImg.Count > 0)
        //            {
        //                foreach (var name in names)
        //                {
        //                    if (LstCodes.ContainsKey(name))
        //                    {
        //                        System.Threading.Tasks.Parallel.For(0, lstImg.Count, i =>
        //                        {
        //                            try
        //                            {
        //                                //获取转换后的图片的直方图
        //                                var actualHisogram = GetHash(lstImg[i]);

        //                                bool res = IsSame(actualHisogram, LstCodes[name]);
        //                                if (res && !codes.Contains(i))
        //                                    codes.Add(i);
        //                            }
        //                            catch { }
        //                        });
        //                    }
        //                }
        //            }
        //            lstImg.ForEach(p =>
        //            {
        //                p.Dispose();
        //                p = null;
        //            });
        //            lstImg = null;
        //        }
        //        names = null;
        //    }
        //    bit.Dispose();
        //    bit = null;
        //    buffer = null;
        //    //如果相似度为1则表示两张图片是一样的
        //    return codes;
        //}

        ///// <summary>
        ///// 获取图片类型
        ///// </summary>
        ///// <param name="source"></param>
        ///// <returns></returns>
        //private static ImageType GetImgType(Bitmap source)
        //{
        //    var result = ImageType.Eight;
        //    try
        //    {
        //        var bit = new Bitmap(source);
        //        //ImageComparator.ClearNoise(ref bit, 255, 1);//清除黑色噪点
        //        int count = 0;
        //        Color color;
        //        for (int x = 1; x < 4; x++)
        //        {
        //            for (int y = 0; y < 2; y++)
        //            {

        //                int width = 6 * (x + 1) + x * 66 - 1 - 3;
        //                int height = 41 + y * 66 + 6 * y;
        //                for (int i = 0; i < 3; i++)
        //                {
        //                    color = bit.GetPixel(width, height + (i + 1) * 10);
        //                    if ((color.R + color.G + color.B) / 3 < 240)
        //                    {
        //                        count++;
        //                    }
        //                }
        //            }
        //        }
        //        if ((count * 1.0 / 36) * 100 <= 5)
        //        {
        //            result = ImageType.Eight;
        //        }
        //        else
        //        {
        //            result = ImageType.Eighteen;
        //            //count = 0;
        //            //for (int x = 1; x < 6; x++)
        //            //{
        //            //    for (int y = 0; y < 3; y++)
        //            //    {
        //            //        int width = 4 * (x + 1) + x * 44 - 2;
        //            //        int height = 41 + y * 44 + 4 * y;
        //            //        for (int i = 0; i < 4; i++)
        //            //        {
        //            //            color = bit.GetPixel(width, height + (i + 1) * 10);
        //            //            if ((color.R + color.G + color.B) / 3 < 240)
        //            //            {
        //            //                count++;
        //            //            }
        //            //        }
        //            //    }
        //            //}
        //            //if ((count * 1.0 / 60) * 100 <= 5)
        //            //{
        //            //    result = ImageType.Eighteen;
        //            //}
        //        }
        //        bit.Dispose();
        //        bit = null;

        //    }
        //    catch (Exception oe)
        //    {
        //        Console.WriteLine(oe.Message);
        //    }
        //    return result;
        //}

        ///// <summary>
        ///// 格式转换相关
        ///// </summary>
        ///// <param name="img"></param>
        //private static void ImageToJPG(ref Bitmap img)
        //{
        //    if (img.RawFormat != ImageFormat.Jpeg)
        //    {
        //        using (MemoryStream ms = new MemoryStream())
        //        {
        //            img.Save(ms, ImageFormat.Jpeg);
        //            img = new Bitmap(Image.FromStream(ms));
        //        }
        //    }
        //}

        ///// <summary>
        ///// 图片分割相关
        ///// </summary>
        ///// <param name="source"></param>
        ///// <param name="type"></param>
        ///// <returns></returns>
        //private static List<Bitmap> SpiltImage(Bitmap source, ImageType type)
        //{
        //    List<Bitmap> lstTmp = new List<Bitmap>();
        //    if (source != null && source.Width > 250)
        //    {
        //        int width, height;
        //        RectangleF part;
        //        switch (type)
        //        {
        //            case ImageType.Eight:
        //                for (int x = 0; x < 4; x++)
        //                {
        //                    for (int y = 0; y < 2; y++)
        //                    {
        //                        width = 6 * (x + 1) + x * 66 - 1;
        //                        height = 41 + y * 66 + 6 * y;
        //                        part = new RectangleF(width, height, 66, 66);//像素矩阵
        //                        Bitmap s = source.Clone(part, PixelFormat.DontCare);
        //                        ImageToJPG(ref s);
        //                        lstTmp.Add(s);
        //                    }
        //                }
        //                break;
        //            case ImageType.Eighteen:
        //                for (int x = 0; x < 6; x++)
        //                {
        //                    for (int y = 0; y < 3; y++)
        //                    {
        //                        width = 4 * (x + 1) + x * 44;
        //                        height = 40 + y * 44 + 4 * y;
        //                        part = new RectangleF(width, height, 44, 44);//像素矩阵
        //                        Bitmap s = source.Clone(part, PixelFormat.DontCare);
        //                        ImageToJPG(ref s);
        //                        lstTmp.Add(s);
        //                    }
        //                }
        //                break;
        //            case ImageType.ThirtySix:
        //                break;
        //            default:
        //                break;
        //        }
        //    }
        //    else
        //    {
        //        lstTmp.Add(source);
        //    }
        //    return lstTmp;
        //}

        #endregion

    }
    public class CommonCompress
    {        /// <summary>
        /// 字符串压缩
        /// </summary>
        /// <param name="strSource"></param>
        /// <returns></returns>
        private static byte[] Compress(byte[] data)
        {
            try
            {
                MemoryStream ms = new MemoryStream();
                GZipStream zip = new GZipStream(ms, CompressionMode.Compress, true);
                zip.Write(data, 0, data.Length);
                zip.Close();
                byte[] buffer = new byte[ms.Length];
                ms.Position = 0;
                ms.Read(buffer, 0, buffer.Length);
                ms.Close();
                return buffer;

            }
            catch (Exception e)
            {
                throw new Exception(e.Message);
            }
        }

        /// <summary>
        /// 字符串解压缩
        /// </summary>
        /// <param name="strSource"></param>
        /// <returns></returns>
        private static byte[] Decompress(byte[] data)
        {
            try
            {
                MemoryStream ms = new MemoryStream(data);
                GZipStream zip = new GZipStream(ms, CompressionMode.Decompress, true);
                MemoryStream msreader = new MemoryStream();
                byte[] buffer = new byte[0x1000];
                while (true)
                {
                    int reader = zip.Read(buffer, 0, buffer.Length);
                    if (reader <= 0)
                    {
                        break;
                    }
                    msreader.Write(buffer, 0, reader);
                }
                zip.Close();
                ms.Close();
                msreader.Position = 0;
                buffer = msreader.ToArray();
                msreader.Close();
                return buffer;
            }
            catch (Exception e)
            {
                throw new Exception(e.Message);
            }
        }

        public static string CompressString(string str)
        {
            string compressString = "";
            byte[] compressBeforeByte = Encoding.GetEncoding("UTF-8").GetBytes(str);
            byte[] compressAfterByte = Compress(compressBeforeByte);
            //compressString = Encoding.GetEncoding("UTF-8").GetString(compressAfterByte);  
            compressString = Convert.ToBase64String(compressAfterByte);
            return compressString;
        }

        public static string DecompressString(string str)
        {
            string compressString = "";
            //byte[] compressBeforeByte = Encoding.GetEncoding("UTF-8").GetBytes(str);  
            byte[] compressBeforeByte = Convert.FromBase64String(str);
            byte[] compressAfterByte = Decompress(compressBeforeByte);
            compressString = Encoding.GetEncoding("UTF-8").GetString(compressAfterByte);
            return compressString;
        }

        public static string ByteToString(byte[] bytes)
        {
            StringBuilder strBuilder = new StringBuilder();
            foreach (byte bt in bytes)
            {
                strBuilder.AppendFormat("{0:X2}", bt);
            }
            return strBuilder.ToString();
        }

        public static byte[] StringToByte(string str)
        {
            byte[] bytes = new byte[str.Length / 2];
            for (int i = 0; i < str.Length / 2; i++)
            {
                int btvalue = Convert.ToInt32(str.Substring(i * 2, 2), 16);
                bytes[i] = (byte)btvalue;
            }
            return bytes;
        }
    }
}