﻿using Account.Web.Common;
using CommonLib;
using Org.BouncyCastle.Asn1.Ocsp;
using System;

namespace Account.Web
{
    public partial class UserLogin : System.Web.UI.Page
    {
        public string strErrorMsg = string.Empty;

        protected void Page_Load(object sender, EventArgs e)
        {
            if (Request.RequestType.Equals("POST"))
            {
                string username = Request.Form["username"];
                string password = Request.Form["password"];

                if (string.IsNullOrEmpty(username) || string.IsNullOrEmpty(password))
                {
                    Response.Write("<script>alert('用户名或密码不能为空！');</script>");
                }
                else if (!BoxUtil.IsEmail(username) && !BoxUtil.IsMobile(username))
                {
                    Response.Write("<script>alert('用户名格式不正确，请检查后重试！');</script>");
                }
                else
                {
                    var user = CodeHelper.GetCodeByAccountId(username);

                    if (user == null)
                    {
                        // 用户不存在，显示错误提示
                        Response.Write("<script>alert('用户不存在，请先注册！');</script>");
                    }
                    else
                    {
                        var pwd = CommonValidateCode.GetMD5String(password + "OCRREG").ToUpper();
                        if (Equals(pwd, user.StrPwd))
                        {
                            // 登录成功，设置用户信息并跳转到首页
                            Session["user"] = user;
                            Response.Redirect("UserIndex.aspx");
                        }
                        else
                        {
                            // 登录失败，显示错误提示
                            Response.Write("<script>alert('用户名或密码错误！');</script>");
                        }
                    }
                }
            }
        }
    }
}