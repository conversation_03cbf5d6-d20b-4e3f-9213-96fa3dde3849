﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="12.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{00A0C26F-AE34-4B16-802C-D271FDC5A8DA}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>TransOcr</RootNamespace>
    <AssemblyName>TransOcr</AssemblyName>
    <TargetFrameworkVersion>v4.5</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <TargetFrameworkProfile />
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <Prefer32Bit>false</Prefer32Bit>
    <PlatformTarget>AnyCPU</PlatformTarget>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="log4net, Version=1.2.15.0, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a, processorArchitecture=x86">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\DLL\log4net.dll</HintPath>
    </Reference>
    <Reference Include="Newtonsoft.Json, Version=8.0.0.0, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed, processorArchitecture=MSIL">
      <HintPath>..\packages\Newtonsoft.Json.8.0.3\lib\net45\Newtonsoft.Json.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Core" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Net" />
    <Reference Include="System.Net.Http" />
    <Reference Include="System.Web" />
    <Reference Include="System.Web.Extensions" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="BingRec.cs" />
    <Compile Include="QQTransPicRec.cs" />
    <Compile Include="QuNaTransRec.cs" />
    <Compile Include="DeepLTransRec.cs" />
    <Compile Include="NiuTransRec.cs" />
    <Compile Include="SouGouBroswerTxtRec.cs" />
    <Compile Include="SouGouBroswerRec.cs" />
    <Compile Include="SouGouPinYinRec.cs" />
    <Compile Include="SouFanYiRec.cs" />
    <Compile Include="XueErSiDemoRec.cs" />
    <Compile Include="XunFeiWenDaRec.cs" />
    <Compile Include="YeekitTransRec.cs" />
    <Compile Include="FuXinTransRec.cs" />
    <Compile Include="CaiYunTransRec.cs" />
    <Compile Include="ALiTransRec.cs" />
    <Compile Include="MicrosoftTransRec.cs" />
    <Compile Include="HaiCiTransRec.cs" />
    <Compile Include="ICiBaRec.cs" />
    <Compile Include="QQApiTransRec.cs" />
    <Compile Include="AICloudRec.cs" />
    <Compile Include="HuJiangRec.cs" />
    <Compile Include="QQTransRec.cs" />
    <Compile Include="PapaGoRec.cs" />
    <Compile Include="XunFeiTransRec.cs" />
    <Compile Include="XunJieV4Rec.cs" />
    <Compile Include="ConstHelper.cs" />
    <Compile Include="WangYiSightRec.cs" />
    <Compile Include="SouGouDeepFanYiRec.cs" />
    <Compile Include="BaseOcrRec.cs" />
    <Compile Include="TransOcrType.cs" />
    <Compile Include="SouGouDeepTransRec.cs" />
    <Compile Include="XunFeiLiteAppRec.cs" />
    <Compile Include="XunFeiXiaoYaRec.cs" />
    <Compile Include="YouDaoAPIRec.cs" />
    <Compile Include="QQFanYiJunRec.cs" />
    <Compile Include="YouDaoSnapRec.cs" />
    <Compile Include="YouDaoDictRec.cs" />
    <Compile Include="YouDaoRec.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="YXWeiLaiRec.cs" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\CommonLib\CommonLib.csproj">
      <Project>{fc03a7d4-8ef2-4dea-a15a-c099eb77b0eb}</Project>
      <Name>CommonLib</Name>
    </ProjectReference>
    <ProjectReference Include="..\ImageLib\ImageLib.csproj">
      <Project>{b7e169a2-3104-40fb-9d1e-2ff911fa45e5}</Project>
      <Name>ImageLib</Name>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup>
    <None Include="packages.config" />
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>