﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <PackageId>ServiceStack.HttpClient</PackageId>
    <AssemblyName>ServiceStack.HttpClient</AssemblyName>
    <TargetFrameworks>net472;netstandard2.0;net6.0</TargetFrameworks>
    <Title>Common libraries for ServiceStack projects</Title>
    <PackageDescription>Typed .NET Core and .NET Framework ServiceClients based on .NET's HttpClient</PackageDescription>
    <PackageTags>ServiceStack;Common;Framework;Clients;ServiceClients;Gateway</PackageTags>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(TargetFramework)' == 'net6.0' ">
    <DefineConstants>$(DefineConstants);NET6_0;NET6_0_OR_GREATER</DefineConstants>
  </PropertyGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\..\ServiceStack.Text\src\ServiceStack.Text\ServiceStack.Text.csproj" />
    <ProjectReference Include="..\ServiceStack.Interfaces\ServiceStack.Interfaces.csproj" />
    <ProjectReference Include="..\ServiceStack.Client\ServiceStack.Client.csproj" />
  </ItemGroup>

  <ItemGroup Condition=" '$(TargetFramework)' == 'net472' ">
    <Reference Include="System" />
    <Reference Include="System.Net.Http" />
    <Reference Include="Microsoft.CSharp" />
  </ItemGroup>

  <ItemGroup Condition=" '$(TargetFramework)' == 'netstandard2.0' ">
    <PackageReference Include="System.Net.Requests" Version="4.3.0" />
    <PackageReference Include="System.Collections.Specialized" Version="4.3.0" />
    <PackageReference Include="System.ServiceModel.Primitives" Version="4.8.1" />
    <PackageReference Include="System.Xml.XmlSerializer" Version="4.3.0" />
  </ItemGroup>

  <ItemGroup Condition=" '$(TargetFramework)' == 'net6.0' ">
  </ItemGroup>

</Project>
