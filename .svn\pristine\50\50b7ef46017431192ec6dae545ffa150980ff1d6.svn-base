﻿using CommonLib;
using System;
using System.Collections.Generic;
using System.Collections.Specialized;
using System.IO;
using System.Security.Cryptography;
using System.Text;

namespace HanZiOcr
{
    /// <summary>
    /// 有道API-本地计算Sign版-小程序
    /// https://ai.youdao.com/DOCSIRMA/html/%E6%96%87%E5%AD%97%E8%AF%86%E5%88%ABOCR/API%E6%96%87%E6%A1%A3/%E9%80%9A%E7%94%A8%E6%96%87%E5%AD%97%E8%AF%86%E5%88%AB%E6%9C%8D%E5%8A%A1/%E9%80%9A%E7%94%A8%E6%96%87%E5%AD%97%E8%AF%86%E5%88%AB%E6%9C%8D%E5%8A%A1-API%E6%96%87%E6%A1%A3.html
    /// </summary>
    public class YouDaoAPIRec : BaseOcrRec
    {
        public YouDaoAPIRec()
        {
            OcrGroup = OcrGroupType.有道;
            OcrType = HanZiOcrType.有道API;
            MaxExecPerTime = 18;

            LstJsonPreProcessArray = new List<object>() { "Result", "regions" };
            LstJsonNextProcessArray = new List<object>() { "lines", "words" };

            StrResultJsonSpilt = "word";
            IsSupportVertical = true;
            LstVerticalLocation = new List<object>() { "boundingBox" };
        }

        static List<YouDaoAccount> lstAppAccount = new List<YouDaoAccount>() {
        new YouDaoAccount(){
         strAppId = "2423360539ba5632",
          strSecretId =  "********************************"
        },
        new YouDaoAccount(){
         strAppId = "712b0ae8fd3d404d",
          strSecretId =  "********************************"
        }
        };
        //string strAppId = "2423360539ba5632";
        //string secretId = "********************************";
        //string strAppId = "712b0ae8fd3d404d";
        //string secretId = "********************************";

        const double EXPIRED_SECONDS = 2592000;

        protected override string GetHtml(OcrContent content)
        {
            string time = UnixTime();
            YouDaoAccount account = lstAppAccount.GetRndItem();
            //var a = o.appKeyOcr, t = o.appSecretOcr, i = o.apiUrlOcr, c = Date.parse(new Date())
            var strToken = appSign(account, time);
            /*{
  "orientation": "UP",
  "errorCode": "0",
  "lines": [
    {
      "boundingBox": "122,0,384,0,384,75,122,75",
      "words": "Flovor makers work in a lab."
    },
    {
      "boundingBox": "87,20,431,20,431,114,87,114",
      "words": "They use chemicals to make flavors."
    }
  ]
}*/
            var result = PostFileResult(account, Convert.FromBase64String(content.strBase64), time, strToken);
            return result;
        }

        private string PostFileResult(YouDaoAccount account, byte[] content, string rndTick, string sign)
        {
            var result = "";
            try
            {
                var url = "https://openapi.youdao.com/ocrapi";
                var file = new UploadFileInfo()
                {
                    Name = "file",
                    Filename = "test.png",
                    ContentType = "image/png",
                    Stream = new MemoryStream(content)
                };

                var values = new NameValueCollection {
                    { "langType", "auto" },
                    { "appKey",account?.strAppId },
                    { "sign", sign },
                    { "docType", "json" },
                    { "detectType", "10012" },//按行识别：10012
                    { "imageType", "2" },
                    { "salt", rndTick },
                    { "angle", "1" },
                    { "column", "columns" },
                };
                result = PostFile(url, new[] { file }, values);
            }
            catch (Exception)
            {

            }
            return result;
        }

        private string appSign(YouDaoAccount account, string time)
        {
            string plainText = GetSignKey(account?.strAppId, account?.strSecretId, time);

            return ToMd5(plainText);
        }

        private string ToMd5(string strContent)
        {
            string result;
            if (strContent == null)
            {
                result = null;
            }
            else
            {
                MD5 md = MD5.Create();
                byte[] array = md.ComputeHash(Encoding.UTF8.GetBytes(strContent));
                StringBuilder stringBuilder = new StringBuilder();
                for (int i = 0; i < array.Length; i++)
                {
                    stringBuilder.Append(array[i].ToString("x2"));
                }
                result = stringBuilder.ToString();
            }
            return result;
        }

        private string GetSignKey(string appid, string secretid, string stime)
        {
            return string.Format("{0}{2}{1}", appid, secretid, stime);
        }

        private string UnixTime()
        {
            return Convert.ToInt64((DateTime.UtcNow - new DateTime(1970, 1, 1, 0, 0, 0, 0)).TotalSeconds * 1000).ToString();
        }

    }

    class YouDaoAccount
    {
        public string strAppId { get; set; }

        public string strSecretId { get; set; }
    }
}