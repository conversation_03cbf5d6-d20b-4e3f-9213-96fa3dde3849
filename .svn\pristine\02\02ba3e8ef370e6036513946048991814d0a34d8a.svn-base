<Project Sdk="Microsoft.NET.Sdk.Web">
  
  <PropertyGroup>
    <TargetFramework>net6.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <RootNamespace>MyApp</RootNamespace>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Bogus" Version="34.0.1" />
    <ProjectReference Include="..\..\..\ServiceStack.Text\src\ServiceStack.Text\ServiceStack.Text.csproj" />
    <ProjectReference Include="..\..\..\ServiceStack.Redis\src\ServiceStack.Redis\ServiceStack.Redis.csproj" />
    <ProjectReference Include="..\..\..\ServiceStack.OrmLite\src\ServiceStack.OrmLite\ServiceStack.OrmLite.csproj" />
    <ProjectReference Include="..\..\..\ServiceStack.OrmLite\src\ServiceStack.OrmLite.Sqlite\ServiceStack.OrmLite.Sqlite.csproj" />
    <ProjectReference Include="..\..\src\ServiceStack.Api.OpenApi\ServiceStack.Api.OpenApi.csproj" />

    <ProjectReference Include="..\..\src\ServiceStack.Interfaces\ServiceStack.Interfaces.csproj" />
    <ProjectReference Include="..\..\src\ServiceStack.Client\ServiceStack.Client.csproj" />
    <ProjectReference Include="..\..\src\ServiceStack.Common\ServiceStack.Common.csproj" />
    <ProjectReference Include="..\..\src\ServiceStack\ServiceStack.csproj" />
    <ProjectReference Include="..\..\src\ServiceStack.Server\ServiceStack.Server.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="admin-ui\assets" />
  </ItemGroup>

</Project>