﻿
using CommonLib;
using System.Collections.Generic;

namespace MathOcr
{
    /// <summary>
    /// 有道AI体验中心-上传图片识别
    /// https://ai.youdao.com/product-ocr.s#question
    /// </summary>
    public class YouDaoRec : BaseMathRec
    {
        public YouDaoRec()
        {
            OcrGroup = OcrGroupType.有道;
            OcrType = MathOcrType.有道AI;
            MaxExecPerTime = 28;
            IsSupportUrlOcr = true;

            LstJsonPreProcessArray = new List<object>() { "lines" };
            LstJsonNextProcessArray = new List<object>() { "|Merge|" };
            StrResultJsonSpilt = "text";
            //LstJsonResultProcessArray = new List<object>() { "|N|text|C|type=formula" };

            IsSupportUrlOcr = true;
        }

        protected override string GetHtml(OcrContent content)
        {
            var strPost = "lang=&imgBase=data%3Aimage%2Fpng%3Bbase64%2C" + System.Web.HttpUtility.UrlEncode(content.strBase64) + "&company=&angle=1";
            var strTmp = WebClientSyncExt.GetHtml("https://aidemo.youdao.com/ocrquestionformulaapi1", strPost, ExecTimeOutSeconds);

            return strTmp;
        }

        public override string GetHtmlByUrl(OcrContent content)
        {
            var strPost = "lang=&imgUrl=" + System.Web.HttpUtility.UrlEncode(content.url) + "&company=&angle=1";
            var strTmp = WebClientSyncExt.GetHtml("https://aidemo.youdao.com/ocrquestionformulaapi", strPost, ExecTimeOutSeconds);

            return strTmp;
        }

    }
}