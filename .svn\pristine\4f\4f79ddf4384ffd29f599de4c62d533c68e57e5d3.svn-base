﻿using System;
using monocaptcha;

namespace NewTicket.JiSu
{
    public class JiSuDaMa
    {

        public static System.Collections.Concurrent.ConcurrentBag<monocaptcha.ICaptchaDecoder> LstCache = new System.Collections.Concurrent.ConcurrentBag<monocaptcha.ICaptchaDecoder>();

        public static monocaptcha.ICaptchaDecoder GetOneClient()
        {
            try
            {
                monocaptcha.ICaptchaDecoder myClient = monocaptcha.CaptchaDecoderFactory.getSimpleDecoder();
                //monocaptcha.ICaptchaDecoder myClient = null;
                //lock (lstCache)
                //{
                //    if (lstCache.Count > 0)
                //    {
                //        myClient = lstCache.FirstOrDefault(p => !p.IsUsed);
                //    }
                //    if (myClient == null)
                //    {
                //        myClient = monocaptcha.CaptchaDecoderFactory.getSimpleDecoder();
                //        lstCache.Add(myClient);
                //    }
                //    myClient.IsUsed = true;
                //}
                return myClient;
            }
            catch (Exception oe)
            {
                Console.WriteLine(oe.Message);
            }
            return GetOneClient();
        }

        public static string GetCode(byte[] array)
        {
            int yue = 0;
            var code = GetCode(array, ref yue);
            if (yue > 0)
            {
                CommonMSG.AddMSG("【超人打码】实时余额：" + yue);
            }
            return code;
        }

        public static void ClearCache()
        {
            lock (LstCache)
            {
                try
                {
                    foreach (var item in LstCache)
                    {
                        try
                        {
                            item.Dispose();
                        }
                        catch (Exception oe)
                        {
                            Console.WriteLine(oe.Message);
                        }
                    }
                }
                catch (Exception oe)
                {
                    Console.WriteLine(oe.Message);
                }
                LstCache = new System.Collections.Concurrent.ConcurrentBag<ICaptchaDecoder>();
            }
        }

        public static string GetCode(byte[] array, ref int yue)
        {
            var result = "";
            //应该为每个线程申请一个 decoder
            //多个线程并发调用 decoder 可能会有意外的情况发生
            var decoder = GetOneClient();
            try
            {
                AnswerResult[] ar = new AnswerResult[1];
                ar[0] = new AnswerResult();
                ar[0].setPictureData(array);

                decoder.decode(Settings.Default.ChaoRenKey, ar);

                if (ar != null && ar.Length > 0)
                {
                    result = ar[0].Result;
                    yue = ar[0].NYue;
                }
            }
            catch (Exception oe)
            {
                decoder = monocaptcha.CaptchaDecoderFactory.getSimpleDecoder();
                Console.WriteLine(oe.Message);
            }
            if (decoder != null)
                decoder.IsUsed = false;
            return result;
        }

        public static int GetYuE()
        {
            int result = 0;
            string html = WebClientExt.GetHtml("http://www.monocaptcha.com:8080/info", "", "", string.Format("tokens={0}", Settings.Default.ChaoRenKey), 1, 3);

            if (!string.IsNullOrEmpty(html) && html.Contains("remaining sum\":"))
            {
                result = BoxUtil.GetInt32FromObject(CommonMethod.SubString(html, "remaining sum\":", ","));
            }
            if (result <= 0)
            {
                GetCode(new byte[1000], ref result);
            }
            return result;
        }
    }
    class AnswerResult : monocaptcha.AbstractAnswerResult
    {
        public override void handle(monocaptcha.ResponseEnum status, string answer, int yue)
        {
            switch (status)
            {
                case ResponseEnum.OK:
                    break;
                case ResponseEnum.FORMAT_ERROR:
                case ResponseEnum.JPEG_DECODE_ERROR:
                    break;
                case ResponseEnum.DATA_OVERFLOW:
                    break;
                case ResponseEnum.UNKNOWN_TOKEN:
                case ResponseEnum.BALANCE_INSUFFICIENT:
                case ResponseEnum.ILLEGAL_ACCOUNT:
                    CommonMSG.AddMSG("【超人】打码失败，请检查余额或联系客服！");
                    Settings.Default.IsChaoRenEnable = false;
                    break;
                case ResponseEnum.SERVER_ERROR:
                    Util.IsServerOK = !Util.IsServerOK;
                    JiSuDaMa.ClearCache();
                    break;
                default:
                    break;
            }
            NYue = yue;
            if (status == monocaptcha.ResponseEnum.OK)
            {
                Result = answer;
            }
        }

        public string Result { get; set; }

        public int NYue { get; set; }
    }
}
