using System.Xml;

namespace Enterprise.Framework.Redis
{
	public static class XmlNodeExtend
	{
		public static string GetAttributeValue(this XmlNode node, string attributeName, string defaultValue)
		{
			XmlAttribute xmlAttribute = node.Attributes[attributeName];
			if (xmlAttribute == null)
			{
				return defaultValue;
			}
			if (string.IsNullOrEmpty(xmlAttribute.Value))
			{
				return defaultValue;
			}
			return xmlAttribute.Value.Trim();
		}
	}
}
