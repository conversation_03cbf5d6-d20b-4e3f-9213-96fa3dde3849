﻿using CommonLib;
using System;
using System.Collections.Generic;
using System.Web;

namespace TransOcr
{
    /// <summary>
    /// https://fanyi.qq.com/
    /// </summary>
    public class QQFanYiJunRec : BaseOcrRec
    {
        public QQFanYiJunRec()
        {
            OcrGroup = OcrGroupType.腾讯;
            OcrType = TransOcrType.腾讯翻译君;
            MaxExecPerTime = 30;

            LstJsonPreProcessArray = new List<object>() { "translate", "records" };

            StrResultJsonSpilt = "sourceText";
            StrResultTransJsonSpilt = "targetText";

            AllowUploadFileTypes = new List<string>() { "txt" };
            InitLanguage();
        }

        #region 支持的语言

        private void InitLanguage()
        {
            TransLanguageDic = new Dictionary<TransLanguageTypeEnum, string>();
            TransLanguageDic.Add(TransLanguageTypeEnum.自动, "auto");
            TransLanguageDic.Add(TransLanguageTypeEnum.中文, "zh");
            TransLanguageDic.Add(TransLanguageTypeEnum.英文, "en");
            TransLanguageDic.Add(TransLanguageTypeEnum.日语, "ja");
            TransLanguageDic.Add(TransLanguageTypeEnum.韩语, "ko");
            TransLanguageDic.Add(TransLanguageTypeEnum.西班牙语, "es");
            TransLanguageDic.Add(TransLanguageTypeEnum.法语, "fr");
            TransLanguageDic.Add(TransLanguageTypeEnum.泰语, "th");
            TransLanguageDic.Add(TransLanguageTypeEnum.阿拉伯语, "ar");
            TransLanguageDic.Add(TransLanguageTypeEnum.俄语, "ru");

            TransLanguageDic.Add(TransLanguageTypeEnum.葡萄牙语, "pt");
            TransLanguageDic.Add(TransLanguageTypeEnum.德语, "de");
            TransLanguageDic.Add(TransLanguageTypeEnum.意大利语, "it");
            TransLanguageDic.Add(TransLanguageTypeEnum.希腊语, "el");
            TransLanguageDic.Add(TransLanguageTypeEnum.荷兰语, "nl");
            TransLanguageDic.Add(TransLanguageTypeEnum.波兰语, "pl");
            TransLanguageDic.Add(TransLanguageTypeEnum.保加利亚语, "bg");
            TransLanguageDic.Add(TransLanguageTypeEnum.爱沙尼亚语, "et");
            TransLanguageDic.Add(TransLanguageTypeEnum.丹麦语, "da");
            TransLanguageDic.Add(TransLanguageTypeEnum.芬兰语, "fi");
            TransLanguageDic.Add(TransLanguageTypeEnum.捷克语, "cs");
            TransLanguageDic.Add(TransLanguageTypeEnum.罗马尼亚语, "ro");
            TransLanguageDic.Add(TransLanguageTypeEnum.斯洛文尼亚语, "sl");
            TransLanguageDic.Add(TransLanguageTypeEnum.瑞典语, "sv");
            TransLanguageDic.Add(TransLanguageTypeEnum.匈牙利语, "hu");
            TransLanguageDic.Add(TransLanguageTypeEnum.越南语, "vi");
        }

        #endregion

        protected override string GetHtml(OcrContent content)
        {
            InitTokens();
            if (string.IsNullOrEmpty(strQtk))
            {
                return null;
            }
            string from = "", to = "";
            InitLanguage(content, ref from, ref to);

            string time = UnixTime();
            var strPost = string.Format("source={1}&target={2}&sourceText={0}&qtv={3}&qtk={4}&sessionUuid=translate_uuid{5}"
                , HttpUtility.UrlEncode(content.strBase64)
                , from
                , to
                , strQtv
                , HttpUtility.UrlEncode(strQtk)
                , time
                );
            var result = WebClientSyncExt.GetHtml("https://fanyi.qq.com/api/translate"
                , "", strPost, "https://fanyi.qq.com/", ExecTimeOutSeconds);
            return result;
        }

        private string strQtv = "";
        private string strQtk = "";

        private const string strQtkSpilt = "\"qtk\":\"";
        private const string strQtvSpilt = "\"qtv\":\"";

        private void InitTokens()
        {
            if (string.IsNullOrEmpty(strQtk))
            {
                var reauthuri = "reauth12f";
                //网页源码里边
                var strHtml = WebClientSyncExt.GetHtml("https://fanyi.qq.com/");
                if (!string.IsNullOrEmpty(strHtml) && strHtml.Contains("var reauthuri = \""))
                {
                    reauthuri = CommonHelper.SubString(strHtml, "var reauthuri = \"", "\"");
                }
                var html = WebClientSyncExt.GetHtml("https://fanyi.qq.com/api/" + reauthuri, "", "qtv=&qtk=", "https://fanyi.qq.com/", ExecTimeOutSeconds);
                if (!string.IsNullOrEmpty(html))
                {
                    strQtk = CommonHelper.SubString(html, strQtkSpilt, "\"");
                    strQtv = CommonHelper.SubString(html, strQtvSpilt, "\"");
                }
            }
        }

        public override void Reset()
        {
            strQtv = "";
            strQtk = "";
            base.Reset();
        }

        private string UnixTime()
        {
            return Convert.ToInt64((DateTime.UtcNow - new DateTime(1970, 1, 1, 0, 0, 0, 0)).TotalSeconds * 1000).ToString();
        }

    }
}