<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net6.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
        <Title>ServiceStack.Skia</Title>
        <PackageDescription>ServiceStack Imagigng features implemented in SkiaSharp</PackageDescription>
        <PackageTags>ServiceStack;Imaging;Skia;SkiaSharp</PackageTags>
    </PropertyGroup>
    
    <PropertyGroup Condition=" '$(TargetFramework)' == 'net6.0' ">
        <DefineConstants>$(DefineConstants);NETCORE;NET6_0;NET6_0_OR_GREATER</DefineConstants>
    </PropertyGroup>
    <ItemGroup>
        <ProjectReference Include="..\..\..\ServiceStack.Text\src\ServiceStack.Text\ServiceStack.Text.csproj" />
        <ProjectReference Include="..\ServiceStack.Interfaces\ServiceStack.Interfaces.csproj" />
        <ProjectReference Include="..\ServiceStack.Common\ServiceStack.Common.csproj" />
        <ProjectReference Include="..\ServiceStack.Client\ServiceStack.Client.csproj" />
        <ProjectReference Include="..\ServiceStack\ServiceStack.csproj" />
        <PackageReference Include="System.Memory" Version="4.5.4" />
        <PackageReference Include="SkiaSharp" Version="2.*" />
        <PackageReference Include="SkiaSharp.NativeAssets.Linux" Version="2.*" />
    </ItemGroup>

</Project>
