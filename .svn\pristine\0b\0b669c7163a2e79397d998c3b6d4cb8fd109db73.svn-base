﻿using System;
using System.IO;
namespace NewTicket
{
    public class FileLogger
    {
        private bool enabled = true;
        private StreamWriter streamWriter_0;

        public FileLogger(string filePath)
        {
            if (!File.Exists(filePath))
            {
                File.Create(filePath).Close();
            }
            this.streamWriter_0 = new StreamWriter(File.Open(filePath, FileMode.Append, FileAccess.Write, FileShare.Read));
        }

        ~FileLogger()
        {
            this.Close();
        }

        public void Log(string string_0)
        {
            if (this.enabled)
            {
                lock (this.streamWriter_0)
                {
                    this.streamWriter_0.WriteLine(string_0 + "\n");
                    this.streamWriter_0.Flush();
                }
            }
        }

        public void LogWithTime(string string_0)
        {
            string str = string.Format("{0}:{1}", DateTime.Now.ToString(), string_0);
            this.Log(str);
        }

        private void Close()
        {
            if (this.streamWriter_0 != null)
            {
                try
                {
                    this.streamWriter_0.Close();
                    this.streamWriter_0 = null;
                }
                catch
                {
                }
            }
        }

        public bool Enabled
        {
            get
            {
                return this.enabled;
            }
            set
            {
                this.enabled = value;
            }
        }

        public void Dispose()
        {
            this.Close();
            GC.SuppressFinalize(this);
        }
    }

}