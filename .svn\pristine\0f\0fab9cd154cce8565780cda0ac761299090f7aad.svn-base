using System;
using System.Web;

namespace Account.Web
{
    /// <summary>
    /// 处理多语言URL路径的HttpModule
    /// 支持 /zh-CN/page.aspx 格式的URL，提取语言信息并重写为标准路径
    /// </summary>
    public class LanguagePathModule : IHttpModule
    {
        public void Init(HttpApplication application)
        {
            application.BeginRequest += Application_BeginRequest;
        }

        void Application_BeginRequest(object sender, EventArgs e)
        {
            HttpContext context = ((HttpApplication)sender).Context;

            // 排除SignalR请求
            if (context.Request.Path.StartsWith("/signalr", StringComparison.OrdinalIgnoreCase) ||
                context.Request.Path.StartsWith("/static/", StringComparison.OrdinalIgnoreCase) ||
                context.Request.Path.StartsWith("/site/", StringComparison.OrdinalIgnoreCase))
            {
                return;
            }

            // 调试模式初始化
            if (context.Request.QueryString["debug_lang"] == "true")
            {
                context.Items["LangDebugInfo"] = new System.Text.StringBuilder("<!-- 语言处理调试信息 --><br/>");
            }

            var debugInfo = context.Items["LangDebugInfo"] as System.Text.StringBuilder;
            debugInfo?.AppendLine("语言路径模块(LanguagePathModule)开始执行。<br/>");
            debugInfo?.AppendLine($"&nbsp;&nbsp;- 原始请求Url: {context.Request.Url}<br/>");
            debugInfo?.AppendLine($"&nbsp;&nbsp;- 原始请求路径: {context.Request.Path}<br/>");

            string path = context.Request.Path;

            // 尝试提取语言代码
            string lang = null;
            string actualPath = null;
            string oriLanuagePath = null;

            if (UrlService.TryExtractLanguage(path, out lang, out oriLanuagePath, out actualPath))
            {
                debugInfo?.AppendLine("UrlService.TryExtractLanguage 调用成功。<br/>");
                debugInfo?.AppendLine($"&nbsp;&nbsp;- 提取出的语言(lang): {(lang ?? "null")}<br/>");
                debugInfo?.AppendLine($"&nbsp;&nbsp;- 重写后的路径(actualPath): {(actualPath ?? "null")}<br/>");
                // 确保目录URL以斜杠结尾
                if (string.IsNullOrEmpty(actualPath) || UrlService.IsDirectory("~/" + actualPath))
                {
                    if (!path.EndsWith("/"))
                    {
                        // 重定向到带斜杠的URL
                        context.Response.RedirectPermanent(path + "/");
                        context.Response.End();
                        return;
                    }
                }
                // 保存提取的语言信息到上下文
                context.Items["lang"] = lang;
                context.Items["originalPath"] = path;
                context.Items["oriLanuagePath"] = oriLanuagePath;
                context.Items["actualPath"] = actualPath;

                debugInfo?.AppendLine($"&nbsp;&nbsp;- 已将 context.Items[\"lang\"] 设置为: '{lang}'<br/>");

                // 重写路径
                context.RewritePath("~/" + actualPath,
                    context.Request.PathInfo,
                    context.Request.QueryString.ToString(),
                    false);
            }
            else
            {
                debugInfo?.AppendLine("UrlService.TryExtractLanguage 调用失败，未从URL中提取出语言。<br/>");
            }

            debugInfo?.AppendLine("语言路径模块(LanguagePathModule)执行完毕。<br/><br/>");
        }

        public void Dispose()
        {
            // 无需释放资源
        }
    }
}
