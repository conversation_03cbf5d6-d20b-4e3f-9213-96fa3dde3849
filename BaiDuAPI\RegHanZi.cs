﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Diagnostics;

namespace BaiDuAPI
{
    public class FixHanZiEntity
    {
        public string StrOld { get; set; }
        public string StrRec { get; set; }
        public string StrFixOld { get; set; }
        public int MatchCount { get; set; }
        public bool IsPadLeft { get; set; }
    }
    public class RegHanZi
    {
        public static void TestRegNew(string strOld, string strHanZi, bool isLogin = true)
        {
            if (false)
            {
                var lst = RegHanZi.GetHanZiDicByPath(ref strOld, isLogin);
                if (lst != null && lst.Count > 0)
                {
                    string strKeys = string.Join("、", lst.Select(p => p.Key));
                    var count = lst.Sum(p => p.Value.Count);
                    var oldStrs = strHanZi.Split(new string[] { "、" }, StringSplitOptions.RemoveEmptyEntries);
                    if (!strKeys.Length.Equals(strHanZi.Length) || count != oldStrs.Length)
                    {
                        var lstTmp = new List<string>();
                        var strTmp = "";
                        foreach (var item in lst)
                        {
                            lstTmp.AddRange(item.Value);
                            strTmp += string.Format("{0}-》{1}  ", item.Key, string.Join("、", item.Value));
                        }
                        //if (!lstTmp.Exists(q => !strHanZi.Contains(q)))
                        BaiDuCode._Log.InfoFormat("【FixNew】{2}=>{0}{3}{1}", strHanZi, strTmp, strOld, Environment.NewLine);
                    }
                }
            }
        }

        public static List<string> GetNewWords(string strOld, bool isLogin = true)
        {
            List<string> lstResult = new List<string>();
            //var strTmp = "";
            var lst = RegHanZi.GetHanZiDicByPath(ref strOld, isLogin);
            if (lst != null && lst.Count > 0)
            {
                foreach (var item in lst)
                {
                    lstResult.AddRange(item.Value);
                    //strTmp += string.Format("{0}-》{1}{2}", item.Key, string.Join("、", item.Value), Environment.NewLine);
                }
                lstResult = lstResult.Distinct().ToList();
            }
            return lstResult;
        }

        public static Dictionary<string, List<string>> GetHanZiDicByPath(ref string strOld, bool isLogin = true)
        {
            var dicRes = new Dictionary<string, List<string>>();

            string result = strOld;

            if (!string.IsNullOrEmpty(result))
            {
                try
                {
                    result = GetFixName(result, ref strOld, isLogin);

                    dicRes = FixWords(result);

                    dicRes = RemoveOneHanZi(dicRes);

                    //int count = dicRes.Sum(p => p.Value.Count);
                    //if (!isLogin && count < 2)
                    //{
                    //    BaiDuCode._Log.InfoFormat("【第1次】Old:{0}=>{1} Result:{2}", strOld, result, string.Join("、", dicRes.Select(p => p.Key)));
                    //    result = deleteChar(result, ref strOld, false);

                    //    dicRes = FixNameNew(result);

                    //    BaiDuCode._Log.InfoFormat("【第2次】Old:{0}=>{1} Result:{2}", strOld, result, string.Join("、", dicRes.Select(p => p.Key)));
                    //}
                }
                catch (Exception oe)
                {
                    BaiDuCode._Log.Error(oe);
                }

            }
            return dicRes;
        }

        private static void InitReplace()
        {
            if (DicReplace.Count <= 0)
            {
                //玻璃瓶塑料瓶瓶子
                //办公椅餐椅椅子
                DicReplace.Add("白酒", "酒");
                DicReplace.Add("玻璃瓶", "瓶子");
                DicReplace.Add("塑料瓶", "瓶子");
                DicReplace.Add("办公椅", "椅子");
                DicReplace.Add("餐椅", "椅子");
                DicReplace.Add("躺椅", "椅子");

                DicReplace.Add("黑板", "黑板擦");
                DicReplace.Add("乒乓球拍", "球拍");
                DicReplace.Add("墨镜", "眼镜");
                DicReplace.Add("羽毛球拍", "球拍");
                DicReplace.Add("乒乓球", "球拍");
                DicReplace.Add("羽毛球", "球拍");
                DicReplace.Add("手机壳", "手机");
                DicReplace.Add("鸡蛋", "鸭蛋");
                DicReplace.Add("救生圈", "游泳圈");
                DicReplace.Add("舞龙", "舞狮");
                DicReplace.Add("挂钟", "钟表");
                DicReplace.Add("图书馆", "书柜");
                DicReplace.Add("小提琴", "吉他");
            }
        }

        public static List<string> HanZiReplace(List<string> lst)
        {
            InitReplace();
            foreach (var item in DicReplace)
            {
                if (lst.Contains(item.Key))
                {
                    if (!lst.Contains(item.Value))
                    {
                        lst.Add(item.Value);
                    }
                    lst.RemoveAll(p => p.Equals(item.Key));
                    lst = lst.Distinct().ToList();
                }
            }
            return lst;
        }

        private static Dictionary<string, List<string>> HanZiReplace(Dictionary<string, List<string>> dic)
        {
            InitReplace();
            foreach (var item in DicReplace)
            {
                if (dic.ContainsKey(item.Key))
                {
                    if (!dic.ContainsKey(item.Value))
                    {
                        dic.Add(item.Value, new List<string>() { item.Value });
                    }
                    dic[item.Value].AddRange(dic[item.Key]);
                    dic[item.Value].RemoveAll(p => p.Equals(item.Key));
                    dic[item.Value] = dic[item.Value].Distinct().ToList();
                    dic.Remove(item.Key);
                }
            }
            return dic;
        }

        public static Dictionary<string, string> DicReplace = new Dictionary<string, string>();

        private static Dictionary<string, List<string>> RemoveOneHanZi(Dictionary<string, List<string>> dic)
        {
            if (dic.Count > 0)
            {
                var keys = dic.Select(p => p.Key).ToList();

                dic = HanZiReplace(dic);

                if (keys.Exists(p => p.Length == 1))
                {
                    var lstMao = new List<string> { "鹰", "猫" };
                    if (keys.Exists(p => lstMao.Contains(p)))
                    {
                        var main = "猫头鹰";
                        if (keys.Contains(main))
                        {
                            keys.RemoveAll(p => lstMao.Contains(p));
                            dic.Remove(lstMao[0]);
                            dic.Remove(lstMao[1]);
                        }
                        else
                        {
                            if (keys.Contains(lstMao[0]) && keys.Contains(lstMao[1]))
                            {
                                dic.Add(main, new List<string>());
                                dic[main].AddRange(dic[lstMao[0]]);
                                dic[main].AddRange(dic[lstMao[1]]);
                                dic[main] = dic[main].Distinct().ToList();
                                keys.RemoveAll(p => lstMao.Contains(p));
                                dic.Remove(lstMao[0]);
                                dic.Remove(lstMao[1]);
                            }
                        }
                    }
                    //lstMao = new List<string> { "洋葱", "葱" };
                    //if (keys.Contains(lstMao[0]) && keys.Contains(lstMao[1]))
                    //{
                    //    dic.Remove(lstMao[1]);
                    //}
                }
            }
            return dic;
        }

        public static Dictionary<string, List<string>> FixWords(string name)
        {
            var dicRes = new Dictionary<string, List<string>>();
            List<string> dic_find = new List<string>();

            //完整匹配
            dicRes = ProcessContainStr(ref name);

            if (!string.IsNullOrEmpty(name))
            {
                var oldLen = name.Length;
                #region 模式匹配

                var tmp = GetStepNames(name);

                if (tmp.Count > 0)
                {
                    foreach (var item in tmp)
                    {
                        if (!dicRes.ContainsKey(item.Key))
                        {
                            dicRes.Add(item.Key, item.Value);
                        }
                        else
                        {
                            var ss = dicRes[item.Key];
                            foreach (var vv in item.Value)
                            {
                                if (!ss.Contains(vv))
                                {
                                    ss.Add(vv);
                                }
                            }
                            dicRes[item.Key] = ss;
                        }
                    }
                }
                //if (res.Count > 0)
                //{
                //    if (res.Count > 1 && res.Exists(p => p.Length == oldLen))
                //    {
                //        res.RemoveAll(p => p.Length != oldLen);
                //    }
                //    if (!string.IsNullOrEmpty(name))
                //    {
                //        if (res.Exists(p => p.StartsWith(name[0].ToString())))
                //        {
                //            res.RemoveAll(p => !p.StartsWith(name[0].ToString()));
                //        }
                //        if (res.Exists(p => p.EndsWith(name[name.Length - 1].ToString())))
                //        {
                //            res.RemoveAll(p => !p.EndsWith(name[name.Length - 1].ToString()));
                //        }
                //    }
                //    foreach (var item in res)
                //    {
                //        if (!dic_find.Contains(item))
                //        {
                //            dic_find.Add(item);
                //        }
                //    }
                //}
                #endregion
            }

            #region 结果处理
            //if (dic_find.Count >= 2)
            //{
            //    //if (old.Length > 2)
            //    //{
            //    //    foreach (var item in dic_find)
            //    //    {
            //    //        if (item.Contains(old[0]))
            //    //        {
            //    //        }
            //    //    }
            //    //}
            //}

            #endregion

            return dicRes;
        }

        private static Dictionary<string, List<string>> GroupDic(Dictionary<string, List<string>> dicRes)
        {
            var result = new Dictionary<string, List<string>>();

            var lstGroup = dicRes.Select(p => p.Key).Distinct().ToList();

            lstGroup = ProcessSingleHanZi(lstGroup);

            foreach (var item in lstGroup)
            {
                if (!result.ContainsKey(item))
                {
                    result.Add(item, dicRes[item]);
                }
            }

            return result;
        }

        private static Dictionary<string, List<string>> GetStepNames(string name)
        {
            var dicRes = new Dictionary<string, List<string>>();
            int nowIndex = 0;
            int stepLen = 0;
            string strName = "";
            while (nowIndex < name.Length)
            {
                if (name.Length - nowIndex > 3)
                {
                    stepLen = 4;
                }
                else if (name.Length - nowIndex > 2)
                {
                    stepLen = 3;
                }
                else if (name.Length - nowIndex > 1)
                {
                    stepLen = 2;
                }
                else
                {
                    stepLen = 1;
                }
                strName = name.Substring(nowIndex, stepLen);
                nowIndex += stepLen;

                var tmp = GetStepNamesWithLength(ref strName);
                if (tmp != null && tmp.Count > 0)
                {
                    List<FixHanZiEntity> lstFix = new List<FixHanZiEntity>();
                    for (int i = 0; i < tmp.Count; i++)
                    {
                        lstFix.Add(GetMatchCount(strName, tmp[i]));
                    }
                    if (lstFix.Exists(p => p.MatchCount > 0))
                    {
                        lstFix.RemoveAll(p => p.MatchCount <= 0);
                    }

                    var lstGroup = lstFix.Select(p => p.StrFixOld).Distinct().ToList();

                    //if (lstGroup.Count > 1) 
                    //    lstGroup.RemoveAll(p => p.Equals(strName));
                    lstGroup = ProcessSingleHanZi(lstGroup);

                    var maxIndex = -1;
                    string strLast = "";
                    foreach (var group in lstGroup)
                    {
                        if (strName.IndexOf(group) > maxIndex)
                        {
                            strLast = group;
                            maxIndex = strName.IndexOf(group);
                        }
                        var lstTmp = new List<FixHanZiEntity>();
                        int maxCount = lstFix.Where(p => p.StrFixOld.Equals(group)).Max(p => p.MatchCount);
                        lstTmp = lstFix.FindAll(p => p.StrFixOld.Equals(group) && p.MatchCount == maxCount).ToList();
                        if (lstTmp != null && lstTmp.Count > 0)
                        {
                            foreach (var fix in lstTmp)
                            {
                                if (dicRes.ContainsKey(group))
                                {
                                    var ss = dicRes[group];
                                    if (!ss.Contains(fix.StrRec))
                                    {
                                        ss.Add(fix.StrRec);
                                        dicRes[group] = ss;
                                    }
                                }
                                else
                                {
                                    dicRes.Add(group, new List<string>() { fix.StrRec });
                                }
                            }
                        }
                    }
                    if (dicRes[strLast].Count > 1)
                    {
                        if (nowIndex < name.Length)
                        {
                            //电话重退业
                            //电话重
                            if (strName.StartsWith(strLast))
                            {
                                strName = strName.Replace(strLast, "");
                            }
                            else
                            {
                                dicRes.Remove(strLast);
                                strName = strName.Substring(strName.IndexOf(strLast));
                            }
                        }
                        else
                        {
                            var ss = dicRes[strLast];
                            if (dicRes[strLast].Exists(p => p.Length >= strLast.Length))
                            {
                                ss.RemoveAll(p => p.Length < strLast.Length);
                            }
                            //ss = ProcessSingleHanZi(ss);
                            dicRes[strLast] = ss;
                        }
                    }
                    else
                    {
                        if (strName.IndexOf(strLast) >= 0)
                        {
                            strName = strName.Substring(strName.IndexOf(strLast)).Replace(strLast, "");
                            if (strLast.Contains(strName))
                            {
                                strName = "";
                            }
                        }
                        else
                        {
                            strName = "";
                        }
                    }
                    //strName = strName.Replace(group, "");
                }
                else
                {
                    //dicRes[strName] = new List<string>() { strName };
                }
                if (!string.IsNullOrEmpty(strName) && strName.Length != stepLen)
                {
                    nowIndex -= strName.Length;
                }
            }

            return GroupDic(dicRes);
        }

        private static int GetStartIndex(ref string strOld, string strRec)
        {
            int index = -1;
            if (strOld.Length > 0 && strRec.Length > 0)
            {
                index = strOld.IndexOf(strRec);
                if (index < 0)
                {
                    int nFirst = -1;
                    while (nFirst < strRec.Length && index < 0)
                    {
                        nFirst++;
                        index = strOld.IndexOf(strRec[nFirst]);
                    }
                    //本来口蝇拍
                    //苍蝇拍
                    //index:3
                    //nFirst:1
                    if (index > nFirst)
                    {
                        index -= nFirst;
                    }
                    else
                    {
                        //蝇拍
                        //苍蝇拍
                        //index:0
                        //nFirst:1
                        strOld = strOld.PadLeft(strOld.Length + nFirst - index, ' ');
                        index = 0;
                        // 蝇拍
                    }
                }
            }
            return index;
        }

        private static FixHanZiEntity GetMatchCount(string strOld, string strRec)
        {
            var entity = new FixHanZiEntity()
            {
                StrOld = strOld,
                StrRec = strRec
            };
            string strFixOld = "";
            int count = 0;
            if (strOld.Length > 0 && strRec.Length > 0)
            {
                int index = GetStartIndex(ref strOld, strRec);
                if (index < 0)
                {
                    BaiDuCode._Log.InfoFormat("【Index异常】，Old：{0} Rec：{1}", strOld, strRec);
                }
                else
                {
                    for (int i = index, nIndex = 0; i < strOld.Length; i++, nIndex++)
                    {
                        if (nIndex < strRec.Length)
                        {
                            if (strRec[nIndex].Equals(strOld[i]))
                            {
                                count++;
                            }
                        }
                        else
                        {
                            break;
                        }
                    }
                    entity.IsPadLeft = strOld.StartsWith(" ");
                    strFixOld = strOld.Substring(index, strOld.Length > index + strRec.Length ? strRec.Length : strOld.Length - index).Trim();
                }
            }
            if (string.IsNullOrEmpty(strFixOld))
                strFixOld = strOld;
            entity.StrFixOld = strFixOld;
            entity.MatchCount = count;

            return entity;
        }

        private static List<string> GetStepNamesWithLength(ref string strOldName)
        {
            //var dicRes = new Dictionary<string, List<string>>();
            List<string> res = new List<string>();
            string name = strOldName;

            #region 匹配New

            switch (name.Length)
            {
                case 1:
                    res = BaiDuCode.lstHanZi.FindAll(p => p.StartsWith(name) || p.EndsWith(name));
                    if (res == null || res.Count <= 0)
                    {
                        res = BaiDuCode.lstHanZi.FindAll(p => p.Contains(name));
                    }
                    break;
                case 2:
                    res = BaiDuCode.lstHanZi.FindAll(p => (p.Contains(name[0]) && p.Contains(name[1])));
                    if (res == null || res.Count <= 0)
                    {
                        res = BaiDuCode.lstHanZi.FindAll(p => (p.StartsWith(name[0].ToString()) && p.EndsWith(name[1].ToString())));
                        if (res == null || res.Count <= 0)
                        {
                            res = BaiDuCode.lstHanZi.FindAll(p => p.StartsWith(name[1].ToString()) && p.EndsWith(name[0].ToString()));
                            if (res == null || res.Count <= 0)
                            {
                                res = BaiDuCode.lstHanZi.FindAll(p => p.Contains(name[0].ToString()) || p.Contains(name[1].ToString()));
                            }
                        }
                    }
                    break;
                case 3:
                    res = BaiDuCode.lstHanZi.FindAll(p => p.Contains(name[0]) && p.Contains(name[1]) && p.Contains(name[2]));
                    if (res == null || res.Count <= 0)
                    {
                        res = BaiDuCode.lstHanZi.FindAll(p =>
                            p.StartsWith(name[0].ToString() + name[1].ToString()) ||
                            p.StartsWith(name[1].ToString() + name[2].ToString()) ||
                            p.EndsWith(name[0].ToString() + name[1].ToString()) ||
                            p.EndsWith(name[1].ToString() + name[2].ToString())
                            );
                        if (res == null || res.Count <= 0)
                        {
                            res = BaiDuCode.lstHanZi.FindAll(p =>
                                (p.Contains(name[0]) && p.Contains(name[1])) ||
                                (p.Contains(name[1]) && p.Contains(name[2])) ||
                                (p.Contains(name[0]) && p.Contains(name[2]))
                                );
                            if (res == null || res.Count <= 0)
                            {
                                res = BaiDuCode.lstHanZi.FindAll(p =>
                                    p.StartsWith(name[0].ToString())
                                    || p.StartsWith(name[2].ToString())
                                    || p.EndsWith(name[0].ToString())
                                    || p.EndsWith(name[2].ToString())
                                    || (p.IndexOf(name[1]) > 0 && !p.EndsWith(name[1].ToString()))
                                    );
                                if (res == null || res.Count <= 0)
                                {
                                    res = BaiDuCode.lstHanZi.FindAll(p => p.Contains(name[0]) || p.Contains(name[1]) || p.Contains(name[2]));
                                }
                            }
                        }
                    }
                    break;
                case 4:
                    res = BaiDuCode.lstHanZi.FindAll(p =>
                        p.Contains(name[0].ToString() + name[1].ToString() + name[2].ToString()) ||
                        p.Contains(name[0].ToString() + name[1].ToString() + name[3].ToString()) ||
                        p.Contains(name[0].ToString() + name[2].ToString() + name[3].ToString()) ||
                        p.Contains(name[1].ToString() + name[2].ToString() + name[3].ToString())
                        );
                    if (res == null || res.Count <= 0)
                    {
                        res = BaiDuCode.lstHanZi.FindAll(p =>
                            p.StartsWith(name[0].ToString() + name[1].ToString()) ||
                            p.StartsWith(name[1].ToString() + name[2].ToString()) ||
                            p.StartsWith(name[2].ToString() + name[3].ToString()) ||
                            p.EndsWith(name[0].ToString() + name[1].ToString()) ||
                            p.EndsWith(name[1].ToString() + name[2].ToString()) ||
                            p.EndsWith(name[2].ToString() + name[3].ToString())
                            );
                        if (res == null || res.Count <= 0)
                        {
                            res = BaiDuCode.lstHanZi.FindAll(p =>
                            (p.Contains(name[0]) && p.Contains(name[1])) ||
                            (p.Contains(name[1]) && p.Contains(name[2])) ||
                            (p.Contains(name[2]) && p.Contains(name[3])) ||
                            (p.Contains(name[0]) && p.Contains(name[2])) ||
                            (p.Contains(name[0]) && p.Contains(name[3]))
                                );
                            if (res == null || res.Count <= 0)
                            {
                                res = BaiDuCode.lstHanZi.FindAll(p => p.Contains(name[0]) || p.Contains(name[1]) || p.Contains(name[2]) || p.Contains(name[3]));
                            }
                        }
                    }
                    break;
            }
            #endregion

            //if (res != null && res.Count > 1)
            //{
            //    if (res.Exists(p => p.Length == name.Length))
            //    {
            //        res.RemoveAll(p => p.Length != name.Length);
            //    }
            //}

            return res;
        }

        private static Dictionary<string, List<string>> ProcessContainStr(ref string strOldName)
        {
            var result = new Dictionary<string, List<string>>();
            string name = strOldName;
            try
            {
                var lstTmp = BaiDuCode.lstHanZi.FindAll(p => name.Contains(p));
                //部分匹配
                if (lstTmp.Count <= 0)
                {
                    lstTmp = BaiDuCode.lstHanZi.FindAll(p => p.Contains(name));
                }
                lstTmp = ProcessSingleHanZi(lstTmp);
                if (lstTmp != null && lstTmp.Count > 0)
                {
                    #region 汉字入库

                    var lstExp = new List<string>();
                    foreach (var item in lstTmp)
                    {
                        if (name.Contains(item))
                        {
                            result.Add(item, new List<string>() { item });
                            if (name.Equals(item))
                            {
                                name = "";
                            }
                            else
                            {
                                try
                                {
                                    int start = name.IndexOf(item);
                                    if (name.StartsWith(item))
                                    {
                                        name = name.Substring(start + item.Length);
                                    }
                                    else
                                    {
                                        if (name.EndsWith(item))
                                        {
                                            name = name.Substring(0, start);
                                        }
                                        else
                                        {
                                            var startStr = name.Substring(0, start);
                                            int end = name.Length - item.Length - start;
                                            var endStr = name.Substring(item.Length + start);
                                            if (start > end)
                                            {
                                                name = startStr;
                                            }
                                            else if (start < end)
                                            {
                                                name = endStr;
                                            }
                                            else
                                            {
                                                if (BaiDuCode.lstHanZi.Exists(p => p.Contains(startStr) || startStr.Contains(p)))
                                                {
                                                    name = startStr;
                                                }
                                                else if (BaiDuCode.lstHanZi.Exists(p => p.Contains(endStr) || endStr.Contains(p)))
                                                {
                                                    name = endStr;
                                                }
                                                else
                                                {
                                                    name = endStr;
                                                }
                                                //name = name.Substring(end);
                                            }
                                        }
                                    }
                                }
                                catch (Exception oe)
                                {
                                    BaiDuCode._Log.Error(string.Format("【汉字处理异常】name:{0} item:{1}", name, item), oe);
                                    name = "";
                                }
                            }
                        }
                        else
                        {
                            if (!lstExp.Contains(item))
                            {
                                lstExp.Add(item);
                            }
                        }
                    }
                    if (!string.IsNullOrEmpty(name) && lstExp.Count > 0)
                    {
                        lstTmp = new List<string>();
                        if (lstExp.Count > 1)
                        {
                            //处理字符集中包含更长汉字的情况
                            foreach (var item in lstExp)
                            {
                                if (item.Length < name.Length && BaiDuCode.lstHanZi.Exists(q => q.Length > item.Length && q.Contains(item)))
                                {
                                    lstTmp.AddRange(BaiDuCode.lstHanZi.FindAll(q => q.Length > item.Length && q.Contains(item)));
                                }
                            }
                            foreach (var item in lstTmp)
                            {
                                if (!lstExp.Contains(item))
                                {
                                    lstExp.Add(item);
                                }
                            }
                        }
                        result.Add(name, lstExp);
                    }
                    #endregion
                }
                lstTmp = null;
            }
            catch (Exception oe)
            {
                BaiDuCode._Log.Error("处理汉字【第一步】出错", oe);
            }
            strOldName = name;
            return result;
        }

        private static List<string> ProcessSingleHanZi(List<string> lstTmp)
        {
            if (lstTmp.Count > 1)
            {
                lstTmp.RemoveAll(p => p.Length < 4 && lstTmp.Exists(q => q.Length > p.Length && q.Contains(p)));
                lstTmp = lstTmp.OrderByDescending(p => p.Length).ToList();
            }
            return lstTmp;
        }

        public static string RemoveBadChar(string name)
        {
            string result = "";
            foreach (var item in name)
            {
                if (BaiDuCode.lstDanHanZi.Contains(item))
                {
                    result += item;
                }
            }
            return result;
        }

        private static string RegexString(string input, string pattern)
        {
            string value = "";
            foreach (System.Text.RegularExpressions.Match match in System.Text.RegularExpressions.Regex.Matches(input, pattern))
            {
                value = match.Value.ToString() == "" ? "" : match.Value.ToString();
            }
            return value;
        }

        private static string GetFirstRelpaceStr(string result)
        {
            if (result.Contains("所有的"))
            {
                return result.Substring(result.IndexOf("所有的") + "所有的".Length);
            }
            else if (result.Contains("有的"))
            {
                return result.Substring(result.IndexOf("所有的") + "所有的".Length);
            }
            return result.TrimStart('请').Replace("点击", "").Replace("击图中", "").Replace("下图中所的", "").Replace("下图中", "")
                .Replace("图中", "").Replace("下图", "").Replace("所有的", "").Replace("所有", "").Replace("所郁", "").Replace("棚有的", "");
        }

        public static string GetFixName(string inputstr, ref string strOld, bool isLogin = true)
        {
            string text = "";
            if (!string.IsNullOrEmpty(inputstr))
            {
                inputstr = GetFirstRelpaceStr(inputstr);
                for (int i = 0; i < inputstr.Length; i++)
                {
                    int num = (int)inputstr[i];
                    if (num > 19968 && num < 40869)
                    {
                        text += inputstr[i].ToString();
                    }
                }
                text = text.Replace("！", "");
                strOld = text;
                if (!string.IsNullOrEmpty(text))
                {
                    try
                    {
                        foreach (var item in BaiDuCode.lstHanZiReplace)
                        {
                            if (text.Contains(item.Key) && !text.Contains(item.Value))
                            {
                                if (item.Value.Contains(item.Key))
                                {
                                    Console.WriteLine(item.Key);
                                }
                                else
                                {
                                    if (!isLogin && item.Key.Length == 1 && item.Value.Length > 1)
                                        continue;
                                    text = text.Replace(item.Key, item.Value);
                                }
                            }
                        }
                    }
                    catch (Exception oe)
                    {
                        Console.WriteLine(oe.Message);
                    }
                }
            }
            return text.Replace("恤", "T恤");
        }
    }
}