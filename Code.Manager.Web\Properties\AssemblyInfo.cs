﻿using System.Reflection;
using System.Runtime.InteropServices;

// 有关程序集的常规信息通过以下特性集 
// 控制。更改这些特性值可修改
// 与程序集关联的信息。

[assembly: AssemblyTitle("Code.Manager.Web")]
[assembly: AssemblyDescription("")]
[assembly: AssemblyConfiguration("")]
[assembly: AssemblyCompany("Microsoft")]
[assembly: AssemblyProduct("Code.Manager.Web")]
[assembly: AssemblyCopyright("Copyright © OldFish 2013-2016")]
[assembly: AssemblyTrademark("")]
[assembly: AssemblyCulture("")]

// 将 ComVisible 设置为 false 会使此程序集中的类型 
// 对 COM 组件不可见。如果需要从 COM 访问此程序集中的类型， 
// 则将该类型上的 ComVisible 特性设置为 true。

[assembly: ComVisible(false)]

// 如果此项目向 COM 公开，则下列 GUID 用于类型库的 ID

[assembly: Guid("a817884b-8b69-4a8b-b22a-d8fc2dd28f88")]

// 程序集的版本信息由下列四个值组成:
//
//      主版本
//      次版本 
//      内部版本号
//      修订号
//
// 您可以指定所有这些值，也可以使用“修订号”和“内部版本号”的默认值， 
// 方法是按如下所示使用“*”:

[assembly: AssemblyVersion("*******")]
[assembly: AssemblyFileVersion("*******")]