﻿using CommonLib;
using System;
using System.Collections.Generic;
using System.Collections.Specialized;
using System.Diagnostics;
using System.IO;
using System.Net;
using System.Text;

namespace HanZiOcr
{
    /// <summary>
    /// http://developer.hanvon.com/generaltext/toZXGeneralText.do
    /// </summary>
    public class HanWangYunRec : BaseOcrRec
    {
        public HanWangYunRec()
        {
            OcrGroup = OcrGroupType.汉王;
            OcrType = HanZiOcrType.汉王云文本;
            MaxExecPerTime = 23;

            LstJsonPreProcessArray = new List<object>() { "doc", "lines" };
            IsSupportVertical = true;
            StrResultJsonSpilt = "str";
            LstVerticalLocation = new List<object>() { "coords" };
        }

        protected override string GetHtml(OcrContent content)
        {
            var result = "";
            try
            {
                var byts = Convert.FromBase64String(content.strBase64);
                var file = new UploadFileInfo()
                {
                    Name = "accessFile",
                    Filename = Guid.NewGuid().ToString() + "." + content.fileExt,
                    ContentType = ApplicationTypeHelper.GetApplicationType(content.fileExt),
                    Stream = new MemoryStream(byts)
                };
                result = PostFile("http://developer.hanvon.com/generaltext/zxreceive.do", new[] { file }, null);
            }
            catch (Exception)
            {

            }
            return result;
        }

    }
}