﻿using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;

namespace CommonLib
{
    public class OcrContent
    {
        public OcrType ocrType { get; set; }

        public OcrContent()
        {
            id = Guid.NewGuid().ToString();
            state = OcrProcessState.待处理;
            receivedTicks = ServerTime.DateTime.Ticks;
        }

        public string id { get; set; }

        [System.Xml.Serialization.XmlIgnore]
        [System.Web.Script.Serialization.ScriptIgnore]
        public string strBase64 { get; set; }

        public string url { get; set; }

        public int processId { get; set; }

        public string processName { get; set; }

        public int threadId { get; set; }

        public ResultEntity result { get; set; }

        public string message { get; set; }

        public OcrProcessState state { get; set; }

        public Exception exception { get; set; }

        public long requestTicks { get; set; }

        public long receivedTicks { get; set; }

        public long startTicks { get; set; }

        public long endTicks { get; set; }

        //public OcrFileType fileType { get; set; }

        public string fileExt { get; set; }

        public TransLanguageTypeEnum from { get; set; }

        public TransLanguageTypeEnum to { get; set; }

        /// <summary>
        /// 有限度的全角转半角（英文、数字、空格以及某些特殊字符等使用半角字符）
        /// </summary>
        public bool IsAutoFull2Half { get; set; } = true;

        /// <summary>
        /// 在中文与英文字母/用于数学、科学和工程的希腊字母/数字之间添加空格
        /// </summary>
        public bool IsAutoSpace { get; set; } = true;

        /// <summary>
        /// 根据语言类型，把文字中的标点符号转换为中/英文标点
        /// </summary>
        public bool IsAutoSymbol { get; set; } = true;

        /// <summary>
        /// 重复标点校正
        /// </summary>
        public bool IsAutoDuplicateSymbol { get; set; } = true;
    }


    public class ResultEntity
    {
        [System.Xml.Serialization.XmlIgnore]
        [System.Web.Script.Serialization.ScriptIgnore]
        public string html { get; set; }

        public string spiltText { get; set; }

        public string transText { get; set; }

        public string spiltLocText { get; set; }

        public string transLocText { get; set; }

        public string verticalText { get; set; }

        public string autoText { get; set; }

        [System.Xml.Serialization.XmlIgnore]
        [System.Web.Script.Serialization.ScriptIgnore]
        public string resultHtml { get; set; }

        public string downloadHtml { get; set; }

        public List<DownLoadInfo> files { get; set; }

        /// <summary>
        /// 文件预览页URL
        /// </summary>
        public string viewUrl { get; set; }

        public ResutypeEnum resultType { get; set; }

        public bool ToTable()
        {
            var result = false;
            TableContentInfo tableInfo = OcrHtmlProcess.DetectTableFromOcrResult(this);
            if (tableInfo?.rows?.Count > 0)
            {
                result = true;
                var resultStr = CodeProcessHelper.JavaScriptSerializer.Serialize(tableInfo);
                autoText = spiltText = resultStr;
            }
            return result;
        }
    }

    public enum ResutypeEnum
    {
        文本 = 0,
        网页 = 1,
        表格 = 2,
    }

    public class DownLoadInfo
    {
        public string url { get; set; }

        public string param { get; set; }

        public OcrFileType fileType { get; set; }

        public string desc { get; set; }
    }

    public enum OcrFileType
    {
        PDF = 1,
        Doc = 2,
        PPT = 3,
        Xls = 4,
        Txt = 5
    }

    public enum OcrProcessState
    {
        待处理 = 0,
        处理中 = 1,
        处理成功 = 2,
        处理失败 = 3,
        处理超时 = 4,
        未知状态 = 5,
        并发限制 = 6,
        类型不支持 = 7,
        可预览 = 8
    }
    [Serializable]
    public class TableContentInfo
    {
        public List<TableRow> rows { get; set; }
    }

    [Serializable]
    public class TableRow
    {
        public int index { get; set; }

        public List<TableCell> cells { get; set; }
    }

    [Serializable]
    public class TableCell
    {
        public int index { get; set; }

        public string content { get; set; }
    }
}
