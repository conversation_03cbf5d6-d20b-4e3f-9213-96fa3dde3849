﻿using CommonLib;
using System;
using System.Collections.Specialized;
using System.IO;

namespace ImageLib
{
    public class SouGouImageUpload : BaseImageUpload
    {
        public SouGouImageUpload()
        {
            ImageType = ImageUploadTypeEnum.搜狗;
        }

        public override string GetResult(byte[] content)
        {
            var result = GetFromSouGou(content);
            if (!string.IsNullOrEmpty(result))
            {
                result += "?1.png";
            }
            return result;
        }

        private string GetFromSouGou(byte[] content)
        {
            var result = "";
            try
            {
                var url = "http://pic.sogou.com/pic/upload_pic.jsp";
                var file = new UploadFileInfo()
                {
                    Name = "pic_path",
                    Filename = "test.png",
                    ContentType = "image/png",
                    Stream = new MemoryStream(content)
                };
                var header = new NameValueCollection()
                {
                    { "Cookie","FUV="+Guid.NewGuid().ToString().Replace("-","")}
                };
                result = PostFile(url, new[] { file }, null, header);
            }
            catch (Exception oe)
            {

            }
            return result;
        }
    }
}
