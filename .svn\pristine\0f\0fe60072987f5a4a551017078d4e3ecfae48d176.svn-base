using Microsoft.Office.Interop.Excel;
using System;
using System.Reflection;
using System.Runtime.InteropServices;

namespace OfficeCreator
{
	internal class ExcelCreator : OfficeCreator
	{
		private Application excelApp;

		public override CreatorKind Kind => CreatorKind.Excel;

		public ExcelCreator()
		{
			base.SupportFormat = new string[2]
			{
				".xls",
				".xlsx"
			};
		}

		public override bool ToPdf(string path, string outputPath)
		{
			if (excelApp == null)
			{
				excelApp = (Application)Activator.CreateInstance(Type.GetTypeFromCLSID(new Guid("00024500-0000-0000-C000-000000000046")));
				excelApp.Visible = false;
				excelApp.ShowWindowsInTaskbar = false;
				excelApp.WindowState = XlWindowState.xlMinimized;
			}
			object missing = Type.Missing;
			object readOnly = true;
			object obj = false;
			Workbook workbook = excelApp.Workbooks.Open(path, missing, readOnly, Missing.Value, Missing.Value, Missing.Value, Missing.Value, Missing.Value, Missing.Value, Missing.Value, Missing.Value, Missing.Value, Missing.Value, Missing.Value, Missing.Value);
			if (workbook != null)
			{
				workbook.ExportAsFixedFormat(XlFixedFormatType.xlTypePDF, outputPath, XlFixedFormatQuality.xlQualityStandard, true, false, 1, missing, false, Missing.Value);
				workbook.Close(false, Missing.Value, Missing.Value);
				return true;
			}
			Log("ToPdf Error: excelApp.Workbooks.Open failed.");
			return false;
		}

		protected override IntPtr QuitAndReleaseComObject()
		{
			IntPtr result = IntPtr.Zero;
			if (excelApp != null)
			{
				result = new IntPtr(excelApp.Hwnd);
				excelApp.Quit();
				Marshal.ReleaseComObject(excelApp);
				excelApp = null;
			}
			return result;
		}
	}
}
