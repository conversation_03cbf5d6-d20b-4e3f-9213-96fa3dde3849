﻿using CommonLib;
using System;
using System.Collections.Generic;
using System.Web;

namespace TransOcr
{
    /// <summary>
    /// 去哪网AI体验中心
    /// 机器翻译
    /// </summary>
    public class QuNaTransRec : BaseOcrRec
    {
        public QuNaTransRec()
        {
            OcrType = TransOcrType.去哪网;
            LstJsonPreProcessArray = new List<object>() { "data" };
            IsProcessJsonResultByArray = false;
            MaxExecPerTime = 18;

            AllowUploadFileTypes = new List<string>() { "txt" };
            InitLanguage();
        }

        #region 支持的语言

        //中文	zh
        //英文 en
        //日文 jp
        //韩文 kr
        //自动识别（中英互译）	auto

        private void InitLanguage()
        {
            TransLanguageDic = new Dictionary<TransLanguageTypeEnum, string>();
            TransLanguageDic.Add(TransLanguageTypeEnum.中文, "zh");
            TransLanguageDic.Add(TransLanguageTypeEnum.英文, "en");
            TransLanguageDic.Add(TransLanguageTypeEnum.日语, "ja");
            TransLanguageDic.Add(TransLanguageTypeEnum.韩语, "ko");
            TransLanguageDic.Add(TransLanguageTypeEnum.西班牙语, "spa");
            TransLanguageDic.Add(TransLanguageTypeEnum.俄语, "ru");
            TransLanguageDic.Add(TransLanguageTypeEnum.法语, "fra");
            TransLanguageDic.Add(TransLanguageTypeEnum.德语, "de");


            TransLanguageDic.Add(TransLanguageTypeEnum.阿拉伯语, "ara");
            TransLanguageDic.Add(TransLanguageTypeEnum.葡萄牙语, "pt");
            TransLanguageDic.Add(TransLanguageTypeEnum.泰语, "th");
            TransLanguageDic.Add(TransLanguageTypeEnum.意大利语, "it");
        }

        #endregion

        protected override string GetHtml(OcrContent content)
        {
            string from = "", to = "";
            InitLanguage(content, ref from, ref to);
            var url = "https://aiex.qunar.com/ai/translate";
            var strPost = "{"
                + string.Format("\"text\":\"{2}\",\"from\":\"{0}\",\"to\":\"{1}\""
                , from
                , to
                , content.strBase64)
                + "}";
            var result = WebClientSyncExt.GetHtml(url, "", strPost, "https://servicewechat.com/wx20dbcf835e124a3b/5/page-frame.html", ExecTimeOutSeconds);
            return result;
        }
    }
}