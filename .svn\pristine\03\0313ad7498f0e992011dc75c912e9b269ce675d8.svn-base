﻿using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Threading.Tasks;

namespace CommonLib.OcrProcessor
{
    public class OcrLineProcessor
    {
        private static readonly double floatPercentDown = 1.25;
        private static readonly double floatPercentUp = 0.75;

        public List<LineInfo> GetVerticalOcrResult(List<TextCellInfo> lstCells, bool isFromLeftToRight, bool isFromTopToDown, string StrContactCell)
        {
            if (lstCells == null || lstCells.Count == 0)
                return new List<LineInfo>();

            List<LineInfo> lstLine = new List<LineInfo>();
            try
            {
                if (lstCells?.Count > 0)
                {
                    double? left = null;
                    double? top = null;
                    double? height = null;
                    double? width = null;
                    double? lastTop = null;
                    double? lastLeft = null;
                    double? lastTopTmp = null;
                    double? lastLeftTmp = null;
                    int? lastPageTmp = null;
                    TextCellInfo cell;
                    LineInfo line = null;

                    while ((cell =
                        isFromLeftToRight ?
                        GetNextProcessFromLeftToRight(lstCells, isFromTopToDown, ref lastTop) :
                         //GetNextProcessFromRightToLeft(lstCells, isFromTopToDown, ref lastTop)
                         GetNextProcessFromRightToLeft(lstCells, isFromTopToDown, ref lastLeft)
                        ) != null)
                    {
                        bool isNextLine = cell.location == null;
                        if (!isNextLine & left.HasValue)
                        {
                            if (cell.PageIndex != lastPageTmp || lastPageTmp == null)
                            {
                                isNextLine = true;
                            }
                            else
                            {
                                if (isFromLeftToRight)
                                {
                                    if (lastTopTmp != lastTop || !lastTop.HasValue)
                                    {
                                        //从上到下
                                        if (isFromTopToDown)
                                        {
                                            //如果高度偏离1/10以上
                                            if (cell.location.top > top && cell.location.top + cell.location.height > top + height * floatPercentDown)
                                                isNextLine = true;
                                        }
                                        //从下到上
                                        else
                                        {
                                            //如果高度偏离1/3以上
                                            if (cell.location.top < top && !OcrUtils.CheckCross(new Rectangle { X = (int)cell.location.left, Y = (int)cell.location.top, Width = (int)cell.location.width, Height = (int)(cell.location.height * floatPercentDown) }, new Rectangle { X = (int)cell.location.left, Y = (int)top.Value, Width = (int)width.Value, Height = (int)height.Value }))
                                                //cell.rectangle.top + cell.rectangle.height < top + height * floatPercentUp
                                                isNextLine = true;

                                        }
                                        if (!isNextLine)
                                        {
                                            //从左向右
                                            //if (cell.rectangle.left > left && !CheckCross(new rectangle() { X = cell.rectangle.left, Y = cell.rectangle.top, Width = (int)(cell.rectangle.width * floatPercentDown), Height = cell.rectangle.height }, new rectangle() { X = left.Value, Y = cell.rectangle.top, Width = width.Value, Height = height.Value }))
                                            if (cell.location.left > left && cell.location.left + cell.location.width < left + width * floatPercentDown)
                                                isNextLine = true;
                                        }
                                    }
                                }
                                else
                                {
                                    if (lastLeftTmp != lastLeft || !lastLeft.HasValue)
                                    {
                                        //如果左右偏离1/4以上
                                        if (cell.location.left < left && cell.location.left + cell.location.width < left + width * floatPercentUp)
                                            isNextLine = true;
                                    }
                                }
                            }
                        }

                        if (line != null && isNextLine)
                        {
                            ProcessTextByLine(isFromLeftToRight, isFromTopToDown, line, lstLine, StrContactCell);
                            line = null;
                        }
                        if (line == null)
                        {
                            line = new LineInfo { lstCell = new List<TextCellInfo>(), words = string.Empty, trans = string.Empty };
                        }
                        if (isNextLine)
                        {
                            top = -1;
                            left = -1;
                            height = -1;
                            width = -1;
                        }
                        line.lstCell.Add(cell);
                        //if (!string.IsNullOrEmpty(cell.words))
                        //{
                        //    line.words += cell.words;
                        //}
                        //if (!string.IsNullOrEmpty(cell.trans))
                        //{
                        //    line.trans += cell.trans;
                        //}
                        if (cell.location != null)
                        {
                            top = cell.location.top;
                            left = cell.location.left;
                            height = cell.location.height;
                            width = cell.location.width;
                        }
                        if (lastTop.HasValue)
                        {
                            lastTopTmp = lastTop.Value;
                        }
                        if (lastLeft.HasValue)
                        {
                            lastLeftTmp = lastLeft.Value;
                        }
                        lastPageTmp = cell.PageIndex;
                        cell.IsProcessed = true;
                    }
                    if (line != null)
                    {
                        ProcessTextByLine(isFromLeftToRight, isFromTopToDown, line, lstLine, StrContactCell);
                    }
                }
            }
            catch (Exception oe)
            {
                Console.WriteLine(oe.Message);
            }

            foreach (var line in lstLine)
            {
                line.Init();
            }
            return lstLine;
        }

        private void ProcessTextByLine(bool isFromLeftToRight, bool isFromTopToDown, LineInfo line, List<LineInfo> lstLine, string StrContactCell)
        {
            if (!line.lstCell.Any(p => p.location == null))
            {
                if (isFromLeftToRight)
                {
                    if (isFromTopToDown)
                    {
                        line.lstCell = line.lstCell.OrderBy(p => p.location.left).ToList();
                    }
                    else
                    {
                        line.lstCell = line.lstCell.OrderByDescending(p => p.location.top).ToList();
                    }
                }
                else
                {
                    if (isFromTopToDown)
                    {
                        line.lstCell = line.lstCell.OrderBy(p => p.location.top).ToList();
                    }
                    else
                    {
                        line.lstCell = line.lstCell.OrderByDescending(p => p.location.left).ToList();
                    }
                }
            }

            MergeLine(line, StrContactCell, isFromLeftToRight, isFromTopToDown);
            lstLine.Add(OcrUtils.JsonConvertClone<LineInfo>(line));
        }

        private void MergeLine(LineInfo line, string StrContactCell, bool isFromLeftToRight, bool isFromTopToDown)
        {
            if (line.lstCell.Count > 1)
            {
                for (int i = 1; i < line.lstCell.Count; i++)
                {
                    var last = line.lstCell[i - 1];
                    var now = line.lstCell[i];
                    var distance = 0d;
                    var averageWidth = 0d;

                    if (isFromLeftToRight)
                    {
                        if (isFromTopToDown)
                        {
                            //白日依山尽 黄河入海流
                            //欲穷千里目 更上一层楼
                            distance = now.location.left - last.location.left - last.location.width;
                            averageWidth = (last.location.width / last.words.Length + now.location.width / now.words.Length) / 2;
                        }
                        else
                        {
                            //道
                            //车
                            //交
                            //公
                            distance = now.location.top + last.location.top - now.location.top;
                            averageWidth = (last.location.height / last.words.Length + now.location.height / now.words.Length) / 2;
                        }
                    }
                    else
                    {
                        if (isFromTopToDown)
                        {
                            //更欲
                            //上穷
                            //一千
                            //层里
                            //楼目
                            distance = now.location.top - last.location.top - last.location.height;
                            averageWidth = (last.location.height / last.words.Length + now.location.height / now.words.Length) / 2;
                        }
                        else
                        {
                            distance = last.location.left - now.location.left - now.location.width;
                            averageWidth = (last.location.width / last.words.Length + now.location.width / now.words.Length) / 2;
                        }
                    }

                    // 间距大于平均宽度的80%时，添加空格
                    var isAddSpace = distance > averageWidth * 0.8;
                    if (isAddSpace)
                    {
                        now.words = " " + now.words;
                    }
                }

                var wordsResult = new string[line.lstCell.Count];
                var transResult = new string[line.lstCell.Count];

                Parallel.For(0, line.lstCell.Count, i =>
                {
                    wordsResult[i] = $"{line.lstCell[i].words?.Replace("\n", " ")}";
                    transResult[i] = $"{line.lstCell[i].trans?.Replace("\n", " ")}";
                });

                //result.spiltText = (strStart + string.Join("\n" + strStart, lstLines.Select(p => p.words?.TrimEnd()))).TrimEnd();
                //result.transText = (strStart + string.Join("\n" + strStart, lstLines.Select(p => p.trans?.TrimEnd()))).TrimEnd();

                line.words = CommonPool.GetStringJoinResult(StrContactCell, wordsResult);
                line.trans = CommonPool.GetStringJoinResult(StrContactCell, transResult);
            }
            else
            {
                line.words = line.lstCell.FirstOrDefault()?.words;
                line.trans = line.lstCell.FirstOrDefault()?.trans;
            }
        }

        private const double NMinWidth = 5;

        private TextCellInfo GetNextProcessFromLeftToRight(List<TextCellInfo> lstCells
            , bool isFromTopToDown, ref double? lastTop)
        {
            TextCellInfo cell = null;

            var minPage = lstCells.Any(p => !p.IsProcessed) ? lstCells.Where(p => !p.IsProcessed).Min(p => p.PageIndex) : 0;
            var lstNoProcessed = lstCells.Where(p => !p.IsProcessed && p.PageIndex == minPage);
            if (lstNoProcessed?.Count() > 0)
            {
                double? minTop = lastTop;
                List<TextCellInfo> lstTmp = null;
                if (!minTop.HasValue)
                {
                    if (isFromTopToDown)
                    {
                        minTop = lstNoProcessed.Min(p => p.location?.top);
                    }
                    else
                    {
                        minTop = lstNoProcessed.Max(p => p.location?.top);
                    }
                }
                else
                {
                    //Console.WriteLine("上次高度：" + lastTop);
                }
                lstTmp = GetFitResultByTop(isFromTopToDown, minTop, lstNoProcessed);
                lastTop = null;
                if (lstTmp?.Count > 0)
                {
                    cell = lstTmp.OrderBy(p => p.location?.left).FirstOrDefault();
                    if (lstTmp.Count > 1)
                    {
                        lastTop = minTop;
                    }
                }
                else
                {
                    cell = lstNoProcessed.FirstOrDefault();
                }
            }
            return cell;
        }

        private TextCellInfo GetNextProcessFromRightToLeft(List<TextCellInfo> lstCells
            , bool isFromTopToDown, ref double? lastLeft)
        {
            TextCellInfo cell = null;

            //var lstNoProcessed = lstCells.Where(p => !p.IsProcessed);
            //var minPage = lstNoProcessed.Min(p => p.PageIndex);
            //lstNoProcessed = lstNoProcessed.Where(p => p.PageIndex == minPage);

            var minPage = lstCells.Any(p => !p.IsProcessed) ? lstCells.Where(p => !p.IsProcessed).Min(p => p.PageIndex) : 0;
            var lstNoProcessed = lstCells.Where(p => !p.IsProcessed && p.PageIndex == minPage);
            if (lstNoProcessed?.Count() > 0)
            {
                double? minLeft = lastLeft;
                if (!minLeft.HasValue)
                {
                    minLeft = lstNoProcessed.Max(p => p.location?.left);
                    //minWidth = lstNoProcessed.Where(p => p.rectangle?.left + p.rectangle?.width >= minLeft).Select(p => p.rectangle?.width).Average();
                }
                else
                {
                    if (!lstNoProcessed.Any(p => p.location?.left <= minLeft && p.location?.left + p.location?.width >= minLeft))
                    {
                        minLeft = lstNoProcessed.Select(p => p.location.left).Max();
                    }
                    //Console.WriteLine("上次高度：" + lastTop);
                }
                var lstTmp = GetFitResultByLeft(minLeft, lstNoProcessed);
                lastLeft = null;
                if (lstTmp?.Count > 0)
                {
                    if (isFromTopToDown)
                    {
                        cell = lstTmp.OrderBy(p => p.location?.top).ThenByDescending(p => p.location?.left).FirstOrDefault();
                    }
                    else
                    {
                        cell = lstTmp.OrderByDescending(p => p.location?.top).ThenByDescending(p => p.location?.left).FirstOrDefault();
                    }
                    if (lstTmp.Count > 1)
                    {
                        lastLeft = minLeft;
                    }
                }
                else
                {
                    cell = lstNoProcessed.FirstOrDefault();
                }
            }
            return cell;
        }

        private List<TextCellInfo> GetFitResultByLeft(double? left, IEnumerable<TextCellInfo> lstNoProcessed)
        {
            double? minLeft;
            double? maxLeft = left;
            //minLeft = maxLeft - width * 1.5;
            minLeft = lstNoProcessed.Where(p => p.location?.left <= left && p.location?.left + p.location?.width >= left)
                .Select(p => p.location.left).Min();
            minLeft = Math.Max(minLeft.Value, NMinWidth);

            var lstTmp = lstNoProcessed
                 .Where(p => p.location?.left >= minLeft && p.location?.left <= maxLeft).ToList();
            return lstTmp;
        }

        private List<TextCellInfo> GetFitResultByTop(bool isFromTopToDown, double? top, IEnumerable<TextCellInfo> lstNoProcessed)
        {
            double? minTop;
            double? maxTop;

            List<TextCellInfo> lstTmp;
            if (isFromTopToDown)
            {
                double? height = lstNoProcessed.Where(p => p.location?.top <= top).Select(p => p.location?.height).Max();
                if (height == null)
                {
                    top = lstNoProcessed.Where(p => p.IsProcessed == false).Select(p => p.location?.top).Min();
                    height = lstNoProcessed.Where(p => p.location?.top <= top).Select(p => p.location?.height).Max();
                }
                minTop = top;
                //maxTop = minTop + height * 1.25;
                maxTop = lstNoProcessed.Where(p => p.location?.top >= top && p.location?.top <= top + height * floatPercentUp)
                    .Select(p => p.location?.top + p.location?.height * floatPercentUp).Max();
                var tmpMaxHeight = lstNoProcessed.Where(p => p.location?.top <= maxTop).Select(p => p.location?.height).Max();
                if (tmpMaxHeight > height)
                {
                    maxTop = lstNoProcessed.Where(p => p.location?.top >= top && p.location?.top <= top + tmpMaxHeight * floatPercentUp)
                        .Select(p => p.location?.top + p.location?.height * floatPercentUp).Max();
                }
                //var tmpMaxTop = lstNoProcessed.Where(p => p.rectangle?.top >= minTop && p.rectangle?.top + p.rectangle?.height <= maxTop)
                //    .Select(p => p.rectangle.top + p.rectangle.height).Max();
            }
            else
            {
                maxTop = top;
                //minTop = maxTop - height * 1.25;
                //minTop = Math.Max(0, minTop.Value);
                minTop = lstNoProcessed.Where(p => p.location?.top <= top && p.location?.top + p.location?.height >= top)
                    .Select(p => p.location?.top).Min();
                minTop = lstNoProcessed.Where(p => p.location?.top <= minTop && p.location?.top + p.location?.height >= minTop)
                    .Select(p => p.location?.top).Min();
            }
            minTop = Math.Max(0, minTop ?? 0d);

            lstTmp = lstNoProcessed.Where(p => p.location?.top >= minTop && p.location?.top <= maxTop).ToList();
            return lstTmp;
        }

    }
}
