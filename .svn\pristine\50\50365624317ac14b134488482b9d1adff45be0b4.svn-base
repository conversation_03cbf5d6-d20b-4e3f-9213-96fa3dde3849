﻿using System;
using System.Collections.Generic;
using System.Configuration;
using System.Xml;
using System.Xml.Linq;
using System.Xml.Serialization;

namespace CommonLib.UserConfig
{
    public class ChargeTypeConfigurationSectionHandler : IConfigurationSectionHandler
    {
        public object Create(object parent, object configContext, XmlNode section)
        {
            var apiSettings = default(UserChargeTypeSetting);
            var serialize = new XmlSerializer(typeof(UserChargeTypeSetting));
            var xDocument = XDocument.Parse(section.OuterXml, LoadOptions.SetBaseUri | LoadOptions.SetLineInfo);

            using (var memoryStream = new System.IO.MemoryStream())
            {
                xDocument.Save(memoryStream);
                memoryStream.Seek(0, System.IO.SeekOrigin.Begin);
                apiSettings = (UserChargeTypeSetting)serialize.Deserialize(memoryStream);
            }

            return apiSettings;
        }

        static UserChargeTypeSetting chargeTypeSetting;

        public static UserChargeTypeSetting ChargeTypes
        {
            get
            {
                if (chargeTypeSetting == null)
                {
                    chargeTypeSetting = ConfigurationManager.GetSection("UserChargeTypeSetting") as UserChargeTypeSetting;
                }
                return chargeTypeSetting;
            }
        }
    }

    [Serializable]
    public class UserChargeTypeSetting
    {
        public List<UserTypeSetting> UserTypeSettings { get; set; }
    }

    [Serializable]
    public class UserTypeSetting
    {
        [XmlAttribute]
        public UserTypeEnum UserType { get; set; }

        [XmlAttribute]
        public PriceType PriceType { get; set; }

        /// <summary>
        /// 单位价格
        /// </summary>
        [XmlAttribute]
        public double PerPrice { get; set; }

        /// <summary>
        /// 收费类型
        /// </summary>
        public List<ChargeType> ChargeTypes { get; set; }

        [XmlAttribute]
        public bool Enable { get; set; }
    }

    public enum PriceType
    {
        按天 = -1,
        按月 = 0,
        按年 = 1,
        终身 = 2,
        按量 = 3
    }

    [Serializable]
    public class ChargeType
    {
        [XmlAttribute]
        public string Name { get; set; }

        /// <summary>
        /// 递增数量
        /// </summary>
        [XmlAttribute]
        public double IncreaseStep { get; set; } = 1;

        /// <summary>
        /// 优惠
        /// </summary>
        [XmlAttribute]
        public double Discount { get; set; } = 1;

        [XmlAttribute]
        public string Desc { get; set; }

        [XmlAttribute]
        public bool LimitActivity { get; set; }

        [XmlAttribute]
        public double LimitDiscount { get; set; } = 1;

        [XmlAttribute]
        public string LimitDesc { get; set; }

        [XmlAttribute]
        public DateTime DtStart { get; set; }

        [XmlAttribute]
        public DateTime DtEnd { get; set; }

        [XmlAttribute]
        public bool Enable { get; set; }

        [XmlAttribute]
        public bool IsDefault { get; set; }

        [XmlAttribute]
        public string Tag { get; set; }

        [XmlAttribute]
        public decimal OriPrice { get; set; }

        public decimal GetPrice(double price, ref string strDesc)
        {
            var result = OriPrice = (decimal)price * (decimal)IncreaseStep * (decimal)Discount;
            if (LimitActivity)
            {
                if (ServerTime.DateTime >= DtStart && ServerTime.DateTime <= DtEnd)
                {
                    result = (decimal)price * (decimal)IncreaseStep * (decimal)LimitDiscount;
                    strDesc = string.Format("{0}{1}元{2}{3}", Name, result.ToString("F0"), string.IsNullOrEmpty(LimitDesc) ? "" : "-" + LimitDesc
                        , new TimeSpan(DtEnd.Ticks - ServerTime.DateTime.Ticks).TotalDays > 30 ? "" : "[截止" + DtEnd.ToString("M月d日") + "]");
                }
                else if (ServerTime.DateTime < DtStart)
                {
                    strDesc = string.Format("{0}{1}元[{2},{3}开始]", Name, result.ToString("F0"), LimitDesc, DtStart.ToString("M月d日"));
                }
            }
            if (string.IsNullOrEmpty(strDesc))
            {
                strDesc = string.Format("{0}{1}元{2}", Name, result.ToString("F0"), string.IsNullOrEmpty(Desc) ? "" : string.Format("[{0}]", Desc));
            }
            return result;
        }
    }
}
