﻿using CommonLib;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace HanZiOcr
{
    public class CommonHanZi
    {
        public static List<string> lstHanZi = new List<string>();
        public static Dictionary<string, string> lstHanZiReplace = new Dictionary<string, string>();
        public static List<char> lstDanHanZi = new List<char>();

        private static DateTime dtLastAccess = DateTime.MinValue;

        private static DateTime dtLastLoad = DateTime.MinValue;
        public static List<char> lstReFirst { get; set; }

        private static string LoadFileContext(string fileName, bool isReplace = false)
        {
            var result = "";
            var file = GetFilePath(fileName);
            if (!string.IsNullOrEmpty(file))
            {
                result = File.ReadAllText(file, Encoding.UTF8);
            }
            return result;
        }

        private static string GetFilePath(string fileName)
        {
            var result = "";
            if (File.Exists(fileName))
            {
                result = fileName;
            }
            else if (File.Exists(ConfigHelper.StrCodeBasePath + fileName))
            {
                result = ConfigHelper.StrCodeBasePath + fileName;
            }
            return result;
        }

        public static Dictionary<string, string> GetListByNameLength(Dictionary<string, string> dicTmp)
        {
            var tmpReplace = new Dictionary<string, string>();
            try
            {
                foreach (var item in dicTmp.OrderByDescending(p => p.Key.Length))
                {
                    tmpReplace.Add(item.Key, item.Value);
                }
            }
            catch (Exception oe)
            {
                tmpReplace = new Dictionary<string, string>();
                ConfigHelper._Log.Error("加载规则出错！", oe);
            }
            return tmpReplace;
        }

        private static void LoadWords()
        {
            var str = LoadFileContext("Words.txt"); //ConfigurationManager.AppSettings["StrAllWord"];
            lstHanZi = new List<string>();
            lstHanZi.AddRange(str.Split(new[] { "、" }, StringSplitOptions.RemoveEmptyEntries));
            lstHanZi = lstHanZi.OrderByDescending(p => p.Length).Distinct().ToList();

            var lstFirst = new List<char>();
            lstReFirst = new List<char>();

            foreach (var item in lstHanZi)
            {
                if (!lstFirst.Contains(item[0]))
                {
                    lstFirst.Add(item[0]);
                }
                else
                {
                    if (!lstReFirst.Contains(item[0]))
                    {
                        lstReFirst.Add(item[0]);
                    }
                }
                foreach (var car in item)
                {
                    if (!lstDanHanZi.Contains(car))
                    {
                        lstDanHanZi.Add(car);
                    }
                }
            }
            lstFirst = null;
            str = null;
            lstDanHanZi.Add('T');
            lstDanHanZi = lstDanHanZi.Distinct().ToList();
        }

        private static void LoadUserWords(bool isFirst = true)
        {
            //var replace = LoadFileContext("ExpWords.txt").Replace("，", ",").Replace("。", ",");// ConfigurationManager.AppSettings["StrExWord"];
            var tmpReplace = new Dictionary<string, string>();
            //foreach (var item in replace.Split(new string[] { "|", "\n", "\r" }, StringSplitOptions.RemoveEmptyEntries))
            //{
            //    if (!tmpReplace.ContainsKey(item.Substring(0, item.IndexOf(",")).Trim()))
            //        tmpReplace.Add(item.Substring(0, item.IndexOf(",")).Trim(), item.Substring(item.IndexOf(",") + 1).Trim());
            //}

            try
            {
                //replace = LoadFileContext("UserFix.txt").Replace("，", ",").Replace("。", ",");// ConfigurationManager.AppSettings["StrExWord"];
                //foreach (var item in replace.Split(new string[] { "|", "\n", "\r" }, StringSplitOptions.RemoveEmptyEntries))
                //{
                //    if (!tmpReplace.ContainsKey(item.Substring(0, item.IndexOf(",")).Trim()))
                //        tmpReplace.Add(item.Substring(0, item.IndexOf(",")).Trim(), item.Substring(item.IndexOf(",") + 1).Trim());
                //}
                if (Directory.Exists(ConfigHelper.StrCodeBasePath + "User\\"))
                {
                    var dirUser = Directory.GetDirectories(ConfigHelper.StrCodeBasePath + "User\\");
                    if (dirUser.Length > 0)
                    {
                        var lstTmp = new List<string>();
                        lstTmp.AddRange(dirUser);
                        dirUser = null;
                        lstTmp.RemoveAll(p => p.Contains("\\bak"));

                        if (lstTmp.Exists(p => p.Contains(ServerTime.DateTime.ToString("MMdd"))))
                        {
                            lstTmp.RemoveAll(p => !p.Contains(ServerTime.DateTime.ToString("MMdd")));
                        }
                        else if (lstTmp.Exists(p => p.Contains(ServerTime.DateTime.AddDays(-1).ToString("MMdd"))))
                        {
                            lstTmp.RemoveAll(p => !p.Contains(ServerTime.DateTime.AddDays(-1).ToString("MMdd")));
                        }
                        else if (lstTmp.Exists(p => p.Contains("\\" + ServerTime.DateTime.ToString("MM"))))
                        {
                            lstTmp.RemoveAll(p => !p.Contains("\\" + ServerTime.DateTime.ToString("MM")));
                        }
                        else if (lstTmp.Exists(p => p.Contains("\\" + ServerTime.DateTime.AddMonths(-1).ToString("MM"))))
                        {
                            lstTmp.RemoveAll(p => !p.Contains("\\" + ServerTime.DateTime.AddMonths(-1).ToString("MM")));
                        }
                        lstTmp.Reverse();
                        //foreach (var dir in lstTmp)
                        if (lstTmp != null && lstTmp.Count > 0)
                        {
                            var files = Directory.GetFiles(lstTmp[0]);
                            if (files.Length > 0)
                            {
                                var filePath = GetFilePath(files[0]);
                                if (!string.IsNullOrEmpty(filePath))
                                {
                                    var info = new FileInfo(filePath);
                                    if (info.LastAccessTime <= dtLastAccess)
                                        return;
                                    dtLastAccess = info.LastAccessTime;
                                }
                                //foreach (var file in files)
                                {
                                    var replace = LoadFileContext(filePath);
                                    //// ConfigurationManager.AppSettings["StrExWord"];.Replace("，", ",").Replace("。", ",")
                                    var lstReplaceWords = replace.Split(new[] { "|" },//, "\n", "\r"
                                        StringSplitOptions.RemoveEmptyEntries);

                                    object obj = "";

                                    replace = null;

                                    Parallel.ForEach(lstReplaceWords, item =>
                                    {
                                        try
                                        {
                                            if (!string.IsNullOrEmpty(item))
                                            {
                                                var key = CommonHelper.SubString(item, ",");
                                                var isChongFu = lstHanZi.Exists(p => p.Contains(key) || key.Contains(p));
                                                //if (isChongFu)
                                                //{
                                                //    BaiDuCode._Log.InfoFormat("添加{0}时碰到包含字段：{1}", key, item);
                                                //}
                                                if (!isChongFu)
                                                {
                                                    isChongFu = key.Length == 2 &&
                                                                lstHanZi.Exists(p => p.StartsWith(key[1].ToString()))
                                                                && lstHanZi.Exists(p => p.EndsWith(key[0].ToString()));
                                                    //if (isChongFu)
                                                    //{
                                                    //    BaiDuCode._Log.InfoFormat("添加{0}时碰到开头和结尾重复字段：{1}", key, item);
                                                    //}
                                                }
                                                if (!isChongFu)
                                                {
                                                    if (!tmpReplace.ContainsKey(key))
                                                    {
                                                        lock (tmpReplace)
                                                        {
                                                            if (!tmpReplace.ContainsKey(key))
                                                            {
                                                                tmpReplace.Add(key, CommonHelper.SubString(item, "", ","));
                                                            }
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                        catch (Exception oe)
                                        {
                                            ConfigHelper._Log.Error("加载字典出错！当前项：" + item, oe);
                                        }
                                    });
                                }
                            }
                        }
                    }
                }
                tmpReplace = GetListByNameLength(tmpReplace);
                if (tmpReplace != null && tmpReplace.Count > 0)
                {
                    lstHanZiReplace = tmpReplace;
                }
            }
            catch (Exception oe)
            {
                ConfigHelper._Log.Error("加载字典出错！", oe);
            }
            //finally
            //{
            //    replace = null;
            //}
        }

        public static void LoadWordInfo(bool isLoadWord = true)
        {
            if (isLoadWord)
            {
                LoadWords();
                lstHanZiReplace = new Dictionary<string, string>();
            }
            new Thread(delegate () { LoadUserWords(isLoadWord); }).Start();
        }
    }
}