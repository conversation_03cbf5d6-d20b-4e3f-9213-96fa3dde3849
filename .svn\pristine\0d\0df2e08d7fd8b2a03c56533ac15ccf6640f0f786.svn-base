<script minify>
App.component('Image', ({ svg, uri, alt, cls }, opt) => {
    let f = attr => !opt || !opt[attr] ? '' : resolve(opt[attr])  
    return {
        $template: '#image-template',
        uri,
        get svg() { 
            let val = resolve(svg)
            if (!val || !val.startsWith('<svg ')) return val 
            return `<svg class='${this.cls}' ${val.substring(4)}`
        },
        get alt() { return alt || f('alt') },
        get cls() { return cls || f('cls') },
    }
})
</script>
<template id="image-template">
<span v-if="svg" v-html="svg" :aria-label="alt"></span>
<img v-else-if="uri" :src="uri" :class="cls" :alt="alt">
</template>
