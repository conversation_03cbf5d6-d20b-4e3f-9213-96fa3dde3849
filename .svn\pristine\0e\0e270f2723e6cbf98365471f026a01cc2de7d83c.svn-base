﻿using CommonLib;
using System;
using System.Collections.Generic;
using System.Collections.Specialized;
using System.Diagnostics;
using System.IO;
using System.Net;
using System.Security.Cryptography;
using System.Text;

namespace HanZiOcr
{
    /// <summary>
    /// https://cloud.tencent.com/document/product/866/33526
    /// </summary>
    public class TencentAPIBasicRec : BaseOcrRec
    {
        public TencentAPIBasicRec()
        {
            OcrGroup = OcrGroupType.腾讯;
            OcrType = HanZiOcrType.腾讯印刷体;
            MaxExecPerTime = 18;

            LstJsonPreProcessArray = new List<object>() { "Response", "TextDetections" };
            StrResultJsonSpilt = "DetectedText";
            LstVerticalLocation = new List<object>() { "Polygon" };
            IsSupportVertical = true;
            IsSupportUrlOcr = true;
        }

        class TencentAccount
        {
            public string strSecretId { get; set; }

            public string strSecretKey { get; set; }
        }

        static List<TencentAccount> lstAppAccount = new List<TencentAccount>() {
            new TencentAccount(){
                strSecretId = "AKIDYeVBUcyQAHZeTjJhJhn10kMuEuJWrkM4",
                strSecretKey =  "e7LyL2f7Jn222lEafU7anxrU1HGyOXeI"
            }
        };

        protected override string GetHtml(OcrContent content)
        {
            var account = lstAppAccount.GetRndItem();
            var postStr = string.IsNullOrEmpty(content.url) ? "{\"ImageBase64\":\"" + content.strBase64 + "\"}" : "{\"ImageUrl\":\"" + content.url + "\"}";
            var header = BuildHeaders(account.strSecretId, account.strSecretKey, "ocr", "ocr.tencentcloudapi.com", "ap-shanghai"
                , "GeneralBasicOCR", "2018-11-19", DateTime.UtcNow, postStr);
            var result = WebClientSyncExt.GetHtml("https://ocr.tencentcloudapi.com/", postStr, ExecTimeOutSeconds, header);
            return result;
        }

        public override string GetHtmlByUrl(OcrContent content)
        {
            return GetHtml(content);
        }

        static string SHA256Hex(string s)
        {
            string result;
            using (SHA256 algo = SHA256.Create())
            {
                byte[] hashbytes = algo.ComputeHash(Encoding.UTF8.GetBytes(s));
                StringBuilder builder = new StringBuilder();
                for (int i = 0; i < hashbytes.Length; i++)
                {
                    builder.Append(hashbytes[i].ToString("x2"));
                }
                result = builder.ToString();
            }
            return result;
        }

        static byte[] HmacSHA256(byte[] key, byte[] msg)
        {
            byte[] result;
            using (HMACSHA256 mac = new HMACSHA256(key))
            {
                result = mac.ComputeHash(msg);
            }
            return result;
        }

        static NameValueCollection BuildHeaders(string secretid, string secretkey, string service, string endpoint, string region
            , string action, string version, DateTime date, string requestPayload)
        {
            string datestr = date.ToString("yyyy-MM-dd");
            DateTime startTime = new DateTime(1970, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc);
            long requestTimestamp = (long)Math.Round((date - startTime).TotalMilliseconds, MidpointRounding.AwayFromZero) / 1000L;
            string algorithm = "TC3-HMAC-SHA256";
            string httpRequestMethod = "POST";
            string canonicalUri = "/";
            string canonicalQueryString = "";
            string contentType = "application/json";
            string canonicalHeaders = string.Concat(new string[]
            {
                "content-type:",
                contentType,
                "; charset=utf-8\nhost:",
                endpoint,
                "\n"
            });
            string signedHeaders = "content-type;host";
            string hashedRequestPayload = SHA256Hex(requestPayload);
            string text = string.Concat(new string[]
            {
                httpRequestMethod,
                "\n",
                canonicalUri,
                "\n",
                canonicalQueryString,
                "\n",
                canonicalHeaders,
                "\n",
                signedHeaders,
                "\n",
                hashedRequestPayload
            });
            string credentialScope = datestr + "/" + service + "/tc3_request";
            string hashedCanonicalRequest = SHA256Hex(text);
            string stringToSign = string.Concat(new string[]
            {
                algorithm,
                "\n",
                requestTimestamp.ToString(),
                "\n",
                credentialScope,
                "\n",
                hashedCanonicalRequest
            });
            string signature = BitConverter.ToString(HmacSHA256(HmacSHA256(HmacSHA256(HmacSHA256(Encoding.UTF8.GetBytes("TC3" + secretkey), Encoding.UTF8.GetBytes(datestr)), Encoding.UTF8.GetBytes(service)), Encoding.UTF8.GetBytes("tc3_request")), Encoding.UTF8.GetBytes(stringToSign))).Replace("-", "").ToLower();
            string authorization = string.Concat(new string[]
            {
                algorithm,
                " Credential=",
                secretid,
                "/",
                credentialScope,
                ", SignedHeaders=",
                signedHeaders,
                ", Signature=",
                signature
            });
            return new NameValueCollection
            {
                {
                    "Authorization",
                    authorization
                },
                {
                    "Host",
                    endpoint
                },
                {
                    "Content-Type",
                    contentType + "; charset=utf-8"
                },
                {
                    "X-TC-Timestamp",
                    requestTimestamp.ToString()
                },
                {
                    "X-TC-Version",
                    version
                },
                {
                    "X-TC-Action",
                    action
                },
                {
                    "X-TC-Region",
                    region
                }
            };
        }

    }
}