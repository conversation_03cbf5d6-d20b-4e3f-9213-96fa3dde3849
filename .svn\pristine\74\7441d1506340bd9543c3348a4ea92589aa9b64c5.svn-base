﻿namespace NewTicket
{
    
    using Tcp.Passive;
    using Tcp.Server;
    using Udp;
    using System;
    using NewTicket.Core;

    /// <summary>
    /// 通信引擎工厂。
    /// </summary>
    public static class NetworkEngineFactory
    {
        /// <summary>
        /// 创建使用二进制协议的TCP客户端引擎。对于返回的引擎实例，可以设置其更多属性，然后调用其Initialize方法启动引擎。
        /// </summary>        
        /// <param name="serverIP">要连接的服务器的IP</param> 
        /// <param name="serverPort">要连接的服务器的端口</param> 
        /// <param name="helper">二进制协议助手接口</param> 
        public static ITcpPassiveEngine CreateStreamTcpPassivEngine(string serverIP, int serverPort, IStreamContractHelper helper)
        {
            return new PassiveStreamTcpEngine(new AgileTcpClient(serverIP, serverPort), helper);
        }
        /// <summary>
        /// 创建使用二进制协议的TCP服务端引擎。对于返回的引擎实例，可以设置其更多属性，然后调用其Initialize方法启动引擎。
        /// </summary>
        /// <param name="port">服务端引擎监听的端口号</param>
        /// <param name="helper">二进制协议助手接口</param>
        public static ITcpServerEngine CreateStreamTcpServerEngine(int port, IStreamContractHelper helper)
        {
            return new StreamTcpEngine(port, helper);
        }

        /// <summary>
        /// 创建使用文本协议的TCP客户端引擎。对于返回的引擎实例，可以设置其更多属性，然后调用其Initialize方法启动引擎。
        /// 注意：返回的引擎实例，可以强转为ITextEngine接口。
        /// </summary>
        /// <param name="serverIP">要连接的服务器的IP</param> 
        /// <param name="serverPort">要连接的服务器的端口</param> 
        /// <param name="helper">文本协议助手接口</param> 
        public static ITcpPassiveEngine CreateTextTcpPassiveEngine(string serverIP, int serverPort, ITextContractHelper helper)
        {
            return new PassiveTextTcpEngine(new AgileTcpClient(serverIP, serverPort), helper);
        }
        /// <summary>
        /// 创建使用文本协议的TCP服务端引擎。对于返回的引擎实例，可以设置其更多属性，然后调用其Initialize方法启动引擎。
        /// 注意：返回的引擎实例，可以强转为ITextEngine接口。
        /// </summary>
        /// <param name="port">服务端引擎监听的端口号</param>
        /// <param name="helper">文本协议助手接口</param>
        public static ITcpServerEngine CreateTextTcpServerEngine(int port, ITextContractHelper helper)
        {
            return new TextTcpEngine(port, helper);
        }
        /// <summary>
        /// 创建UDP引擎。对于返回的引擎实例，可以设置其更多属性，然后调用其Initialize方法启动引擎。
        /// </summary> 
        public static IUdpEngine CreateUdpEngine()
        {
            return new UdpEngine();
        }
    }
}

