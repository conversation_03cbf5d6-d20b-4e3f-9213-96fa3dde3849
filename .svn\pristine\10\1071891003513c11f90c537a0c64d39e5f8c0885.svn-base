﻿using System;
using System.Collections.Generic;
using System.Data;

namespace TicketBLL
{
    public class CodeHelper
    {
        private static List<string> _lstTmpCache;

        static CodeHelper()
        {
            _lstTmpCache = GetAllCodeEntity();
        }

        public static bool SetAllForbid()
        {
            string strSQL = string.Format(@"update reg set IsForbid = 1 where dtExpired <= '{0}' ", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"));
            return CommonHelper.DBHelper.ExecuteCommand(strSQL) > 0;
        }

        public static void Vacuum()
        {
            string strSQL = @"VACUUM";
            CommonHelper.DBHelper.ExecuteCommand(strSQL);
        }

        public static bool IsExitsCode(string strAppCode)
        {
            bool result = true;
            try
            {
                if (_lstTmpCache == null)
                {
                    _lstTmpCache = GetAllCodeEntity();
                }
                if (_lstTmpCache != null && _lstTmpCache.Count > 0)
                {
                    result = _lstTmpCache.Exists(p => p.Equals(strAppCode));
                    if (!result)
                    {
                        if (IsExitsCodeFromDB(strAppCode))
                        {
                            result = true;
                            _lstTmpCache = GetAllCodeEntity();
                        }
                    }
                }
            }
            catch (Exception oe)
            {
                Console.WriteLine(oe.Message);
            }
            return result;
        }

        public static bool IsExitsCodeFromDB(string code)
        {
            bool result = false;
            string strSQL = string.Format("select appCode from reg where appCode='{0}' and IsForbid = 0", code);
            object obj = CommonHelper.DBHelper.ExecuScalar(strSQL);
            if (obj != null && !string.IsNullOrEmpty(obj.ToString()))
            {
                result = true;
            }
            return result;
        }

        public static List<string> GetListCodeByDataSet(DataTable result)
        {
            List<string> lstCodeEntity = new List<string>();
            if (!(result == null || result.Rows.Count <= 0))
            {
                foreach (DataRow row in result.Rows)
                {
                    lstCodeEntity.Add(GetCodeEntityByDataRow(row));
                }
            }
            return lstCodeEntity;
        }

        public static List<string> GetListCodeByDataSet(DataSet result)
        {
            if (!(result == null || result.Tables.Count < 1 || result.Tables[0].Rows.Count <= 0))
            {
                return GetListCodeByDataSet(result.Tables[0]);
            }
            return null;
        }

        public static List<string> GetAllCodeEntity()
        {
            SetAllForbid();
            return GetAllCodeEntityByPageIndex();
        }

        public static List<string> GetAllCodeEntityByPageIndex(int pageIndex = 0, int pageSize = 0)
        {
            string strSQL = @"select distinct appCode from reg where isForbid = 0  order by dtReg desc ";
            if (pageSize > 0)
                strSQL += @" Limit " + pageSize;
            if (pageIndex > 1)
            {
                strSQL += " Offset " + pageSize * (pageIndex - 1);
            }
            DataSet dsMain = CommonHelper.DBHelper.GetDataSet(strSQL);
            if ((dsMain == null || dsMain.Tables.Count < 1 || dsMain.Tables[0].Rows.Count <= 0))
            {
                return new List<string>();
            }
            return GetListCodeByDataSet(dsMain);
        }

        public static string GetCodeEntityByDataRow(DataRow row)
        {
            //appCode ,machine ,NType ,dtReg ,dtExpired ,MaxWindow ,MaxLogin ,IsForbid
            //string CodeEntity = row["appCode"].ToString();
            return row["appCode"].ToString();
        }

    }
}