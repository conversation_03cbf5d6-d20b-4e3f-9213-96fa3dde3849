﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Drawing;
using System.IO;
using System.Drawing.Imaging;
using System.Drawing.Drawing2D;
using System.Runtime.InteropServices;
using BaiDuAPI;

namespace NewTicket
{
    public class NewLocalDaMa
    {
        [DllImport("MTVC.dll", CharSet = CharSet.Ansi)]
        private static extern IntPtr ep_nimage(IntPtr str1, int ntype);

        [DllImport("MTVC.dll", CharSet = CharSet.Ansi)]
        private static extern int ep_loadlib(string str1, string str2, string strpwd);

        [DllImport("MTVC.dll", CharSet = CharSet.Ansi)]
        private static extern string ep_getversion();

        public static DateTime GetVerSionDate()
        {
            DateTime dtTmp = DateTime.MinValue;
            try
            {
                dtTmp = BoxUtil.GetDateTimeFromObject(ep_getversion());
            }
            catch (Exception oe)
            {
                BaiDuCode._Log.Error("获取识别库版本错误！", oe);
            }
            return dtTmp;
        }

        public static bool IsLocalCodeEnable;
        public static bool IsInit;

        public static void Init()
        {
            IsInit = false;
            var str1 = ConfigHelper.LoadFileBase64Context("one.lib");
            var str2 = ConfigHelper.LoadFileBase64Context("two.lib");
            if (!string.IsNullOrEmpty(str1) && !string.IsNullOrEmpty(str2))
            {
                try
                {
                    var res = ep_loadlib(str1, str2, "epic123");
                    if (res == 1)
                    {
                        IsLocalCodeEnable = true;
                    }
                }
                catch (Exception oe)
                {
                    BaiDuCode._Log.Error("加载识别库错误！", oe);
                }
            }
            IsInit = true;
        }

        public static string GetCode(byte[] buffer, bool isXiaDan = false)
        {
            string result = "";
            if (!IsInit || !IsLocalCodeEnable)
            {
                return result;
            }
            try
            {
                IntPtr ptrIn = Marshal.StringToHGlobalAnsi(Convert.ToBase64String(buffer));
                IntPtr ptrRet = ep_nimage(ptrIn, isXiaDan ? 1 : 0);
                string ret2 = Marshal.PtrToStringAnsi(ptrRet);

                if (!string.IsNullOrEmpty(ret2))
                {
                    var lstCodes = new List<int>();
                    for (int i = 0; i < ret2.Length; i++)
                    {
                        var index = getIndex(int.Parse(ret2[i].ToString()));
                        if (!lstCodes.Contains(index))
                        {
                            lstCodes.Add(index);
                        }
                    }
                    result = ImageHelper.GetCodePointByIndex(lstCodes);
                }
            }
            catch (Exception oe)
            {
                Console.WriteLine(oe.Message);
            }
            return result.TrimEnd(',').TrimStart(',');
        }

        private static int getIndex(int index)
        {
            //1234
            //5678

            //0246
            //1357
            int result = 8;
            switch (index)
            {
                case 1:
                    result = 0;
                    break;
                case 2:
                    result = 2;
                    break;
                case 3:
                    result = 4;
                    break;
                case 4:
                    result = 6;
                    break;
                case 5:
                    result = 1;
                    break;
                case 6:
                    result = 3;
                    break;
                case 7:
                    result = 5;
                    break;
                case 8:
                    result = 7;
                    break;
                default:
                    break;
            }
            return result;
        }


    }
}
