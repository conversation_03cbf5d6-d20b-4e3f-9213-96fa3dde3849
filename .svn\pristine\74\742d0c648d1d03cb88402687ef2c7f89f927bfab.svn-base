﻿using System;
using System.Globalization;
using System.IO;
using System.Web;
using CommonLib;

namespace HanZiOcr
{
    public class MicroSoftRec
    {
        private static string strCookie = "";
        private static string strToken = "";

        private static readonly string strSpilt = "\\\"text\\\":\\\"";

        public static string GetRecImg()
        {
            var result = "";
            if (string.IsNullOrEmpty(strToken))
            {
                GetCookie();
            }
            if (!string.IsNullOrEmpty(strToken))
            {
                var base64 = ""; // GetBase64("http://img.oldfish.cn/11.jpg");
                for (var i = 11; i < 15; i++)
                {
                    var byt = File.ReadAllBytes(@"D:\助手\Image\0119\Old\" + i + ".jpg"); //10584306645.jpg");
                    if (byt.Length > 0)
                        base64 = Convert.ToBase64String(byt);
                    //base64 = GetBase64("http://img.oldfish.cn/11.jpg");
                    //string strPost = "imageData=" + System.Web.HttpUtility.UrlEncode(base64)
                    //    + "&dataType=data&Time=" + ToGMTFormat(DateTime.Now)
                    //    + "&isUrl=false&languageCode=zh-Hans&__RequestVerificationToken=";
                    //var strPost = string.Format("imageData={0}&isUrl=true&dataType=imageUrl&languageCode=zh-Hans&__RequestVerificationToken=", "http://img.oldfish.cn/11.jpg");

                    result += GetContext(base64);
                }
            }
            return result;
        }

        public static string ToGMTFormat(DateTime dt)
        {
            return dt.ToString("ddd dd MMM yyyy ", CultureInfo.CreateSpecificCulture("en-US")).Replace(",", "") +
                   dt.ToString("HH:mm:ss") + " GMT+0800 (中国标准时间)";
        }

        public static string GetContext(string strbase64)
        {
            var result = "";
            if (string.IsNullOrEmpty(strToken))
            {
                GetCookie();
            }
            if (!string.IsNullOrEmpty(strToken))
            {
                var strPost =
                    string.Format(
                        "imageData={0}&isUrl=false&dataType=data&Time={1}&languageCode=zh-Hans&__RequestVerificationToken={2}"
                        , HttpUtility.UrlEncode(strbase64), ToGMTFormat(ServerTime.DateTime), strToken);
                //var strPost = string.Format("imageData=http%3A%2F%2Fimg.oldfish.cn%2F{0}&isUrl=true&dataType=imageUrl&languageCode=zh-Hans&__RequestVerificationToken={1}"
                //    , fileName.Replace("\\", "%2F"), strToken);

                var strTmp = WebClientSyncExt.GetHtml("https://www.azure.cn/cognitive-services/Demo/VisionDemo/Ocr"
                    , ref strCookie, "", strPost, 1, 5);

                if (!string.IsNullOrEmpty(strTmp))
                {
                    if (strTmp.IndexOf("}]},{\\\"boundingBox\\\":") > 0)
                    {
                        strTmp = strTmp.Substring(0, strTmp.IndexOf("}]},{\\\"boundingBox\\\":"));
                    }
                    while (strTmp.Contains(strSpilt))
                    {
                        strTmp = strTmp.Substring(strTmp.IndexOf(strSpilt) + strSpilt.Length);
                        result += strTmp.Substring(0, strTmp.IndexOf("\\\""));
                    }
                }
            }
            return result.Replace("\\n", "").TrimStart('，').Trim();
        }

        private static void GetCookie()
        {
            strCookie = "";
            var html = WebClientSyncExt.GetHtml("https://www.azure.cn/cognitive-services/zh-cn/computer-vision-api"
                , ref strCookie, "", "", 1, 30);
            if (!string.IsNullOrEmpty(html) && html.Contains("antiForgeryToken"))
            {
                html = html.Substring(html.LastIndexOf("antiForgeryToken"));
                html = html.Substring(html.IndexOf("= '") + "= '".Length);
                strToken = html.Substring(0, html.IndexOf("';"));
            }
        }

        //}
        //    return result;
        //        , ref strCookie, "", strPost, 1, 10);
        //    string result = WebClientExt.GetHtml("https://www.azure.cn/cognitive-services/Demo/VisionDemo/Ocr"
        //    var strPost = string.Format("imageData={0}&__RequestVerificationToken={1}&isUrl=true&dataType=imageUrl&languageCode=zh-Hans", strUrl, strToken);
        //{

        //public static string GetBase64(string strUrl)
    }
}