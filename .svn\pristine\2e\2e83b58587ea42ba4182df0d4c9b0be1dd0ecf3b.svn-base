﻿using EvalJs.ServiceMsg;
using log4net;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.IO;
using System.Net;
using System.ServiceProcess;
using System.Threading;

namespace EvalJs
{
    static class Program
    {
        /// <summary>
        /// 应用程序的主入口点。
        /// </summary>
        static void Main()
        {
            //new Service().CheckState();
            //System.Threading.Thread.Sleep(1000000);
            //Console.Read();
            //return;
            //TicketBLL.TicketService service = null;
            ////Log.WriteLog(string.Format("服务重启：{0}", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")));
            //service = new TicketBLL.TicketService();
            //service.Connect(21);
            //Console.ReadLine();

            new Thread(delegate()
            {
                try
                {
                    DateTime now = DateTime.Now;
                    GlobalContext.Properties["LogDir"] = now.ToString("yyyyMM");
                    GlobalContext.Properties["LogFileName"] = "_SocketAsyncServer" + now.ToString("yyyyMMdd");
                    Configuration configuration = ConfigurationManager.OpenExeConfiguration(ConfigurationUserLevel.None);
                    Program.FileDirectory = configuration.AppSettings.Settings["FileDirectory"].Value;
                    bool flag = Program.FileDirectory == "";
                    if (flag)
                    {
                        Program.FileDirectory = Path.Combine(Directory.GetCurrentDirectory(), "Files");
                    }
                    bool flag2 = !Directory.Exists(Program.FileDirectory);
                    if (flag2)
                    {
                        Directory.CreateDirectory(Program.FileDirectory);
                    }
                    int num = 0;
                    bool flag3 = !int.TryParse(configuration.AppSettings.Settings["Port"].Value, out num);
                    if (flag3)
                    {
                        num = 9420;
                    }
                    num = 9420;
                    int numConnections = 0;
                    bool flag4 = !int.TryParse(configuration.AppSettings.Settings["ParallelNum"].Value, out numConnections);
                    if (flag4)
                    {
                        numConnections = 8000;
                    }
                    int socketTimeOutMS = 0;
                    bool flag5 = !int.TryParse(configuration.AppSettings.Settings["SocketTimeOutMS"].Value, out socketTimeOutMS);
                    if (flag5)
                    {
                        socketTimeOutMS = 300000;
                    }
                    ServicePointManager.DefaultConnectionLimit = 65536;
                    Program.AsyncSocketSvr = new AsyncSocketServer(numConnections);
                    Program.AsyncSocketSvr.SocketTimeOutMS = socketTimeOutMS;
                    Program.AsyncSocketSvr.Init();
                    IPEndPoint localEndPoint = new IPEndPoint(IPAddress.Parse("0.0.0.0"), num);
                    Program.AsyncSocketSvr.Start(localEndPoint);
                    //Console.WriteLine("开启服务器" + num);
                    ClsGloab.alldammPort = new List<int>();
                    ClsGloab.alldammPort.Add(24100);
                    ClsGloab.alldammPort.Add(24100);
                    ClsGloab.alldammPort.Add(24100);
                }
                catch (Exception ex)
                {
                }
            }).Start();

            //Console.ReadKey();

            ServiceBase[] ServicesToRun;
            ServicesToRun = new ServiceBase[] 
            { 
                new Service() 
            };
            ServiceBase.Run(ServicesToRun);
        }

        public static AsyncSocketServer AsyncSocketSvr;

        public static string FileDirectory;
    }
}
