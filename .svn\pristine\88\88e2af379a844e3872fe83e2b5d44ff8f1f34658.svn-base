﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using ToolCommon;

namespace AutoProxy
{
    public class IPHelper
    {
        #region 获取本机IP

        public static string GetNowIP(int maxCount = 0)
        {
            var count = 1;
            var strIP = "";
            strIP = GetIPFrom163();
            count++;
            if (string.IsNullOrEmpty(strIP) && maxCount > 0 && maxCount > count)
            {
                strIP = GetIPFromSoHu();
                count++;
            }
            count++;
            if (string.IsNullOrEmpty(strIP) && maxCount > 0 && maxCount > count)
            {
                strIP = GetIPFromIPCn();
                count++;
            }
            //if (string.IsNullOrEmpty(strIP) && maxCount > 0 && maxCount > count)
            //{
            //    strIP = GetIPFromSina();
            //    count++;
            //}
            //if (string.IsNullOrEmpty(strIP) && maxCount > 0 && maxCount > count)
            //{
            //    strIP = GetIPFromCz88();
            //    count++;
            //}
            if (string.IsNullOrEmpty(strIP) && maxCount > 0 && maxCount > count)
            {
                strIP = GetIPFromOray();
                count++;
            }
            return strIP;
        }

        //private static string GetIPFromSina()
        //{
        //    //http://counter.sina.com.cn/ip
        //    //var ILData = new Array("**************","中国", "上海市", "", "电信"); if (typeof(ILData_callback) != "undefined") 		{ ILData_callback(); }
        //    var result = "";
        //    try
        //    {
        //        var html = WebClientExt.GetHtml("http://counter.sina.com.cn/ip", 3);
        //        if (!string.IsNullOrEmpty(html) && html.Contains("new Array(\""))
        //        {
        //            result = SubString(html, "new Array(\"", "\"").Trim();
        //        }
        //    }
        //    catch (Exception oe)
        //    {
        //        Log.WriteError(oe);
        //    }
        //    return IsIPAddress(result) ? result : "";
        //}

        private static string GetIPFromSoHu()
        {
            //http://pv.sohu.com/cityjson?ie=utf-8
            //var returnCitySN = {"cip": "**************", "cid": "310110", "cname": "上海市杨浦区"};
            var result = "";
            try
            {
                var html = WebClientExt.GetHtml("http://pv.sohu.com/cityjson?ie=utf-8", 3);
                if (!string.IsNullOrEmpty(html) && html.Contains("\"cip\": \""))
                {
                    result = SubString(html, "\"cip\": \"", "\"").Trim();
                }
            }
            catch (Exception oe)
            {
                Log.WriteError(oe);
            }
            return IsIPAddress(result) ? result : "";
        }

        private static string GetIPFromOray()
        {
            //http://ddns.oray.com/checkip
            //<html><head><title>Current IP Check</title></head><body>Current IP Address: **************</body></html>
            var result = "";
            try
            {
                var html = WebClientExt.GetHtml("http://ddns.oray.com/checkip", 3);
                if (!string.IsNullOrEmpty(html) && html.Contains("IP Address:"))
                {
                    result = SubString(html, "IP Address:", "<").Trim();
                }
            }
            catch (Exception oe)
            {
                Log.WriteError(oe);
            }
            return IsIPAddress(result) ? result : "";
        }

        private static string GetIPFrom163()
        {
            //http://ddns.oray.com/checkip
            //<html><head><title>Current IP Check</title></head><body>Current IP Address: **************</body></html>
            var result = "";
            try
            {
                var html = WebClientExt.GetHtml("http://mt.analytics.163.com/ip", 3);
                if (!string.IsNullOrEmpty(html))
                {
                    result = html.Trim();
                }
            }
            catch (Exception oe)
            {
                Log.WriteError(oe);
            }
            return IsIPAddress(result) ? result : "";
        }

        //private static string GetIPFromCz88()
        //{
        //    //http://www.cz88.net
        //    //你的IP：**************&nbsp;&nbsp;&nbsp;&nbsp;<a href
        //    var result = "";
        //    try
        //    {
        //        var html = WebClientExt.GetHtml("http://www.cz88.net", 3);
        //        if (!string.IsNullOrEmpty(html) && html.Contains("你的IP："))
        //        {
        //            result = SubString(html, "你的IP：", "<").Replace("&nbsp;", "").Trim();
        //        }
        //    }
        //    catch (Exception oe)
        //    {
        //        Log.WriteError(oe);
        //    }
        //    return IsIPAddress(result) ? result : "";
        //}

        private static string GetIPFromIPCn()
        {
            //http://ip.cn/
            //你的IP：**************&nbsp;&nbsp;&nbsp;&nbsp;<a href
            var result = "";
            try
            {
                var html = WebClientExt.GetHtml("http://ip.cn/", 3);
                if (!string.IsNullOrEmpty(html) && html.Contains(" IP："))
                {
                    result =
                        SubString(html, " IP：", "</p>")
                            .Replace("&nbsp;", "")
                            .Replace("<code>", "")
                            .Replace("</code>", "")
                            .Trim();
                }
            }
            catch (Exception oe)
            {
                Log.WriteError(oe);
            }
            return IsIPAddress(result) ? result : "";
        }

        public static string SubString(string strSource, string strSpilt, string strEnd = "")
        {
            return strSource.SubStringHorspool(strSpilt, strEnd).Trim();
        }

        private static bool IsIPAddressString(string ip)
        {
            if (string.IsNullOrEmpty(ip) || ip.Length < 7 || ip.Length > 15)
                return false;
            return true;
        }

        private static readonly string regformat =
            @"^(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])$";

        public static bool IsIPAddress(string ip)
        {
            if (string.IsNullOrEmpty(ip) || ip.Length < 7 || ip.Length > 15)
                return false;
            var regex = new Regex(regformat, RegexOptions.IgnoreCase);
            return regex.IsMatch(ip);
        }

        public static bool IsIntranetIPAddress(string ip)
        {
            return ip.StartsWith("10.") || ip.StartsWith("127.")
                   || ip.StartsWith("192.") || ip.StartsWith("169.") || ip.StartsWith("172.");
        }

        #endregion
    }
    public static class StringExtension
    {
        //Horspool匹配算法
        public static string SubStringHorspool(this string str, string strStart, string strEnd = "")
        {
            var index = 0;
            if (!string.IsNullOrEmpty(strStart))
            {
                index = str.HorspoolIndex(strStart);
                str = index >= 0 ? str.Substring(index + strStart.Length) : "";
            }
            if (!string.IsNullOrEmpty(str) && !string.IsNullOrEmpty(strEnd))
            {
                index = str.HorspoolIndex(strEnd);
                str = index >= 0 ? str.Substring(0, index) : "";
            }
            strStart = null;
            strEnd = null;
            return str;
        }

        #region Horspool算法

        //Horspool匹配算法，平均时间复杂度O(n)
        public static int HorspoolIndex(this string source, string target, int pos = 0)
        {
            var sLen = source.Length;
            var tLen = target.Length;
            var Ti = tLen - 1; //目标字符串匹配位置
            var Si = pos + Ti; //源字符串匹配位置

            if (sLen - pos < tLen)
                return -1;

            while ((Ti > -1) && (Si < sLen))
            {
                if (source[Si] == target[Ti])
                {
                    Ti--;
                    Si--;
                }
                else
                {
                    while ((Ti > -1) && (source[Si] != target[Ti]))
                    {
                        Ti--;
                    }
                    Si += tLen - 1 - Ti;
                    Ti = tLen - 1;
                }
            }

            if (Si < sLen)
                return Si + 1;
            return -1;
        }

        #endregion
    }
}
