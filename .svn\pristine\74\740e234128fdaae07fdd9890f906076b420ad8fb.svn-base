﻿using CommonLib;
using System;
using System.Collections.Generic;
using System.Collections.Specialized;
using System.IO;

namespace HanZiOcr
{
    /// <summary>
    /// 讯飞Lite-AI体验
    /// </summary>
    public class XunFeiLiteAppRec : BaseOcrRec
    {
        public XunFeiLiteAppRec()
        {
            OcrGroup = OcrGroupType.讯飞;
            OcrType = HanZiOcrType.讯飞Lite;
            MaxExecPerTime = 16;

            LstJsonPreProcessArray = new List<object>() { "data" };
            LstJsonNextProcessArray = new List<object>() { "boxes", "words" };
            StrResultJsonSpilt = "text";
        }

        protected override void SetProcessArray(string html)
        {
            var value = OcrHtmlProcess.ProcessJsonResult(html, new List<object>() { "data" });
            var isEnglish = !string.IsNullOrEmpty(value) && Equals(CommonLib.OcrProcessor.OcrUtils.GetLang(value), "en");
            StrContactCell = isEnglish ? " " : "";
        }

        protected override string GetHtml(OcrContent content)
        {
            var byt = Convert.FromBase64String(content.strBase64);
            var result = PostFileResult(byt);//.Replace("\\\"", "\"").Replace("\\\\", "\\").Replace("\\r", "\r").Replace("\\n", "\n")
            //"{\"code\":0,\"msg\":\"成功\",\"data\":\"[{\\\"confidence\\\":1,\\\"word\\\":[{\\\"content\\\":\\\"请点击下图中所有的靴子\\\"},{\\\"content\\\":\\\"火柴\\\"}]}]\",\"content\":null,\"audioPath\":null}"
            return result;
        }

        private List<string> lstOcrType = new List<string>() { "generalocr", "handwritingocr", "ocr_tts" };

        private string PostFileResult(byte[] content)
        {
            var result = "";
            try
            {
                string apiType = lstOcrType.GetRndItem();
                if (apiType.Equals("ocr_tts"))
                {
                    StrResultJsonSpilt = "text";
                    LstJsonNextProcessArray = null;
                }
                else
                {
                    StrResultJsonSpilt = "content";
                    LstJsonNextProcessArray = new List<object>() { "word" };
                }
                var url = "https://aistack.openspeech.cn/aistack/vision/" + apiType;
                var file = new UploadFileInfo()
                {
                    Name = "file",
                    Filename = "test.png",
                    ContentType = "image/png",
                    Stream = new MemoryStream(content)
                };
                var values = new NameValueCollection { { "language", "cn" } };
                var headers = new NameValueCollection
                {
                    {"referer", "https://servicewechat.com/wxa7f07807b0f7f668/25/page-frame.html"}
                };
                result = PostFile(url, new[] { file }, values, headers);
            }
            catch (Exception)
            {

            }
            return result;
        }

    }
}