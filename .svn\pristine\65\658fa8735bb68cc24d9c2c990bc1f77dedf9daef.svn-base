﻿using CommonLib;
using System;
using System.Collections.Generic;
using System.Collections.Specialized;
using System.Diagnostics;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Net;
using System.Security.Cryptography;
using System.Text;
using System.Threading.Tasks;

namespace DocOcr
{
    public class XunJieV4Rec : BaseDocOcrRec
    {
        private string StrMacId = "";
        public XunJieV4Rec()
        {
            OcrGroup = OcrGroupType.迅捷;
            OcrType = DocOcrType.迅捷V4;
            MaxExecPerTime = 10;
            StrMacId = Guid.NewGuid().ToString().Replace("-", "").ToLower();

            LstJsonPreProcessArray = new List<object>() { "txtcontent" };
            IsProcessJsonResultByArray = false;

            //文件上限2M
            FileSizeLimit = 1024 * 1024 * 2;
            AllowUploadFileTypes = new List<string>() { "pdf", "doc", "docx" };
        }

        private long GetRndTick()
        {
            return Convert.ToInt64((DateTime.UtcNow - new DateTime(1970, 1, 1, 0, 0, 0, 0)).TotalSeconds);
        }

        private string GetNewId(string fileName, OcrContent content, ref string token, ref string strTimeSpan)
        {
            var result = "";
            var timeSpan = GetRndTick();
            var dic = new Dictionary<string, string>() {
                { "tasktype", "pdf2word"},
                { "limitsize", "10240"},
                { "filename",fileName},
                { "filecount","1"},
                { "isshare","0"},
                { "timestamp",timeSpan.ToString()},
                { "softname","wxpdf2word"},
                { "softversion","1.0.0"},
                { "machineid",StrMacId},
                { "outputfileextension","doc"},//doc/docx
                //{ "fanyi_from",from},
                //{ "fanyi_to",to},
            };
            dic.Add("datasign", Sign(dic));
            var html = WebClientSyncExt.GetHtml("https://app.xunjiepdf.com/api/v4/uploadpar", ConstHelper.JavaScriptSerializer.Serialize(dic), ExecTimeOutSeconds);
            if (html.Contains("通过校验"))
            {
                result = CommonHelper.SubString(html, "\"tasktag\":\"", "\"");
                token = CommonHelper.SubString(html, "\"tasktoken\":\"", "\"");
                strTimeSpan = CommonHelper.SubString(html, "\"timestamp\":\"", "\"");
            }
            return result;
        }

        protected override string GetHtml(OcrContent content)
        {
            var result = "";
            var tasktoken = "";
            var fileName = Guid.NewGuid().ToString().Replace("-", "") + "." + content.fileExt;
            var strTimeSpan = "";

            var tasktag = GetNewId(fileName, content, ref tasktoken, ref strTimeSpan);
            if (string.IsNullOrEmpty(tasktag))
            {
                return result;
            }
            var tranFileType = ResultFileType == OcrFileType.PDF ? "pdf" : (ResultFileType == OcrFileType.Txt ? "txt" : "docx");

            var byt = Convert.FromBase64String(content.strBase64);
            if (UploadPic(string.Format("http://app.xunjiepdf.com/api/v4/uploadfile?tasktag={0}&timestamp={1}&tasktoken={2}&fileindex=0&chunks=1&chunk=0"
                , tasktag
                , strTimeSpan
                , tasktoken), byt))
            {
                result = "{" +
                    string.Format("\"fileId\":\"{0}\",\"fileType\":\"{1}\",\"url\":\"{2}\""
                    , tasktag
                    , tranFileType
                    , string.Format("https://app.xunjiepdf.com/download/fileid/{0}", tasktag)) + "}";
            }
            return result;
        }

        private bool UploadPic(string url, byte[] b)
        {
            var result = false;
            var httpWebRequest = (HttpWebRequest)WebRequest.Create(new Uri(url));
            httpWebRequest.Timeout = ExecTimeOutSeconds * 1000;
            httpWebRequest.ReadWriteTimeout = 2000;
            httpWebRequest.Method = "POST";
            try
            {
                using (Stream requestStream = httpWebRequest.GetRequestStream())
                {
                    requestStream.Write(b, 0, b.Length);
                    //获取服务器端的响应
                    using (var response = (HttpWebResponse)httpWebRequest.GetResponse())
                    {
                        var receiveStream = response.GetResponseStream();
                        var readStream = new StreamReader(receiveStream, Encoding.UTF8);
                        var strTmp = readStream.ReadToEnd();
                        if (!string.IsNullOrEmpty(strTmp))
                        {
                            //{"result":true,"fileIndex":2,"fileSize":"0KB","message":"成功","keytag":"2c23c238e60143b9897ede099e5e68db","uploadtime":"2018/8/2 11:34:21","filename":"captcha-image.jpg","filepages":""}
                            if (strTmp.Contains("完成"))
                            {
                                result = true;
                            }
                        }
                        response.Close();
                        readStream.Close();
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine("文件传输异常： " + ex.Message);
            }
            finally
            {
                httpWebRequest = null;
            }
            return result;
        }

        private string PostFileResult(string tasktag)
        {
            var result = "";
            try
            {
                var url = "http://app.xunjiepdf.com/api/progress";
                var values = new NameValueCollection {
                    { "tasktag", tasktag },
                    { "limituse", "-1" } };
                var html = PostFile(url, null, values);

                if (!string.IsNullOrEmpty(html))
                {
                    result = CommonHelper.SubString(html, "\"message\":\"", "\"");
                    if (string.IsNullOrEmpty(result) && html.Contains(",\"downurl\":\"http"))
                    {
                        result = "成功";
                    }
                }
            }
            catch (Exception)
            {

            }
            return result;
        }

        protected override ResultEntity GetProcessText(OcrContent content, string html)
        {
            //{"status":2
            //"is_preview_docx_ready":true,"is_preview_pdf_ready":true,"is_preview_uncomparison_docx_ready":true,"is_preview_uncomparison_pdf_ready":true
            //"is_full_docx_ready":true,"is_full_pdf_ready":true,"is_full_uncomparison_docx_ready":true,"is_full_uncomparison_pdf_ready":true
            //,"preview_status":2}
            //result = "1";
            var entity = new ResultEntity()
            {
                files = new List<DownLoadInfo>(),
                autoText = CommonHelper.SubString(html, "\"fileId\":\"", "\""),
                resultType = ResutypeEnum.网页
            };

            if (!string.IsNullOrEmpty(entity.autoText))
            {
                var fileType = CommonHelper.GetFileType(CommonHelper.SubString(html, "\"fileType\":\"", "\""));
                var file = new DownLoadInfo()
                {
                    fileType = fileType,
                    desc = "迅捷V4-" + fileType.ToString(),
                    url = CommonHelper.SubString(html, "\"url\":\"", "\""),
                };
                entity.viewUrl = OnLineViewHelper.GetViewUrl(file.url);
                var viewFile = new DownLoadInfo()
                {
                    fileType = fileType,
                    desc = "在线预览",
                    url = entity.viewUrl,
                };
                entity.files.Add(viewFile);
                entity.files.Add(file);
                //entity.downloadHtml = ConstHelper.GetDownLoadHtml(entity, OcrType.GetHashCode(), true);
            }
            return entity;
        }

        public override ProcessStateEntity QueryFileStatuMethod(string taskId)
        {
            var html = PostFileResult(taskId);
            var processStatus = new ProcessStateEntity()
            {
                state = OcrProcessState.未知状态,
                taskId = taskId
            };
            if (html.Contains("成功"))
            {
                processStatus.state = OcrProcessState.处理成功;
                processStatus.desc = "处理完毕，可以下载了！";
            }
            else if (html.Contains("正在处理"))
            {
                processStatus.state = OcrProcessState.处理中;
                processStatus.desc = string.Format("处理中，请稍后…", processStatus.privewPercent, processStatus.percent);
            }
            else if (html.Contains("未处理"))
            {
                processStatus.state = OcrProcessState.待处理;
                processStatus.desc = "排队中，请稍后…";
            }
            else if (html.Contains("失败"))
            {
                processStatus.state = OcrProcessState.处理失败;
                processStatus.desc = "处理失败，详细：" + html;
            }
            else if (!string.IsNullOrEmpty(html))
            {
                processStatus.desc = "处理结果：" + html;
            }
            else
            {
                Console.WriteLine("迅捷V4查询状态异常：" + html);
            }
            return processStatus;
        }

        /// <summary>
        /// 签名
        /// </summary>
        /// <param name="pairs">参数字典</param>
        /// <param name="appkey">APPKEY</param>
        /// <param name="charset">编码格式</param>
        /// <returns></returns>
        string Sign(Dictionary<string, string> pairs, string charset = "utf-8")
        {
            var dic = pairs.OrderBy(x => x.Key);

            var pair = "";
            foreach (var kv in dic)
            {
                if (!string.IsNullOrEmpty(kv.Value))
                {
                    pair += kv.Key + "=" + kv.Value + "&";
                }
            }

            pair = pair.TrimEnd('&') + "hUuPd20171206LuOnD";

            var sign = MD5(pair).ToLower();

            return sign;
        }

        /// <summary>
        /// MD5加密（小写）
        /// </summary>
        /// <param name="s">字符串</param>
        /// <param name="len">长度</param>
        /// <returns></returns>
        string MD5(string s, int len = 32)
        {
            var md5Hasher = new MD5CryptoServiceProvider();
            byte[] data = md5Hasher.ComputeHash(Encoding.UTF8.GetBytes(s));
            StringBuilder sb = new StringBuilder();
            for (int i = 0; i < data.Length; i++)
            {
                sb.Append(data[i].ToString("x2"));
            }
            string result = sb.ToString();
            return len == 32 ? result : result.Substring(8, 16);
        }

    }
}
