﻿using NewTicket.MyControl;

namespace NewTicket
{
    partial class NewFormMain
    {
        /// <summary>
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows 窗体设计器生成的代码

        /// <summary>
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(NewFormMain));
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle19 = new System.Windows.Forms.DataGridViewCellStyle();
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle25 = new System.Windows.Forms.DataGridViewCellStyle();
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle26 = new System.Windows.Forms.DataGridViewCellStyle();
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle20 = new System.Windows.Forms.DataGridViewCellStyle();
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle21 = new System.Windows.Forms.DataGridViewCellStyle();
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle22 = new System.Windows.Forms.DataGridViewCellStyle();
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle23 = new System.Windows.Forms.DataGridViewCellStyle();
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle24 = new System.Windows.Forms.DataGridViewCellStyle();
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle1 = new System.Windows.Forms.DataGridViewCellStyle();
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle7 = new System.Windows.Forms.DataGridViewCellStyle();
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle8 = new System.Windows.Forms.DataGridViewCellStyle();
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle27 = new System.Windows.Forms.DataGridViewCellStyle();
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle9 = new System.Windows.Forms.DataGridViewCellStyle();
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle31 = new System.Windows.Forms.DataGridViewCellStyle();
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle32 = new System.Windows.Forms.DataGridViewCellStyle();
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle28 = new System.Windows.Forms.DataGridViewCellStyle();
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle29 = new System.Windows.Forms.DataGridViewCellStyle();
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle30 = new System.Windows.Forms.DataGridViewCellStyle();
            this.menuStrip1 = new System.Windows.Forms.MenuStrip();
            this.cmsUserManager = new System.Windows.Forms.ToolStripMenuItem();
            this.订票提醒ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.打码平台ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripMenuItem1 = new System.Windows.Forms.ToolStripMenuItem();
            this.其他ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.关联任务文件打开方式ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.网络加速QToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.封IPToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.iPToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.本地IP解封ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.机器码ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.联系客服ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.意见反馈ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.pnlSet = new System.Windows.Forms.Panel();
            this.grpTask = new System.Windows.Forms.GroupBox();
            this.label6 = new System.Windows.Forms.Label();
            this.chkTaskSele = new System.Windows.Forms.CheckBox();
            this.nMaxOrderTime = new System.Windows.Forms.NumericUpDown();
            this.btnRefreshTask = new System.Windows.Forms.Button();
            this.btnExportTask = new System.Windows.Forms.Button();
            this.btnImpotTask = new System.Windows.Forms.Button();
            this.btnStopTask = new System.Windows.Forms.Button();
            this.btnClearAllTask = new System.Windows.Forms.Button();
            this.cmsOpType = new System.Windows.Forms.ComboBox();
            this.linkLabel2 = new System.Windows.Forms.LinkLabel();
            this.btnStartTask = new System.Windows.Forms.Button();
            this.btnEditTask = new System.Windows.Forms.Button();
            this.btnDeleTask = new System.Windows.Forms.Button();
            this.label20 = new System.Windows.Forms.Label();
            this.dvTasks = new System.Windows.Forms.DataGridView();
            this.chkTask = new System.Windows.Forms.DataGridViewCheckBoxColumn();
            this.账号 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.TrainNo = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.起始站点 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.FromStationDescription = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.ToStationDescription = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column9 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.status = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.LeftTicketDescription = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Remark = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.cmsTask = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.tsmStart = new System.Windows.Forms.ToolStripMenuItem();
            this.tsmStop = new System.Windows.Forms.ToolStripMenuItem();
            this.启动选择任务ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.停止选择任务ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.选择任务ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.全选ToolStripMenuItem1 = new System.Windows.Forms.ToolStripMenuItem();
            this.反选ToolStripMenuItem1 = new System.Windows.Forms.ToolStripMenuItem();
            this.选择所有已停止ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.所有正在运行ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.所有已完成ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripSeparator3 = new System.Windows.Forms.ToolStripSeparator();
            this.tsmDeleTask = new System.Windows.Forms.ToolStripMenuItem();
            this.删除任务及账号ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripMenuItem2 = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripSeparator4 = new System.Windows.Forms.ToolStripSeparator();
            this.tsmCopyAccount = new System.Windows.Forms.ToolStripMenuItem();
            this.tsmCopyPassenger = new System.Windows.Forms.ToolStripMenuItem();
            this.tsmPasterPassenger = new System.Windows.Forms.ToolStripMenuItem();
            this.tsmSaveToWeb = new System.Windows.Forms.ToolStripMenuItem();
            this.tsmCheckPassenger = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripSeparator5 = new System.Windows.Forms.ToolStripSeparator();
            this.tsmCancelQueue = new System.Windows.Forms.ToolStripMenuItem();
            this.tsmCancelAllQueue = new System.Windows.Forms.ToolStripMenuItem();
            this.tsmQueryOrder = new System.Windows.Forms.ToolStripMenuItem();
            this.tsmQueryCompleteOrder = new System.Windows.Forms.ToolStripMenuItem();
            this.tsmCancelOrder = new System.Windows.Forms.ToolStripMenuItem();
            this.取消所有订单ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.取消选择订单ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.tsmCopyThisOrder = new System.Windows.Forms.ToolStripMenuItem();
            this.tsmOpenIE = new System.Windows.Forms.ToolStripMenuItem();
            this.btnNewTask = new System.Windows.Forms.Button();
            this.btnStopAll = new System.Windows.Forms.Button();
            this.btnStartAll = new System.Windows.Forms.Button();
            this.btnCopyTask = new System.Windows.Forms.Button();
            this.grpUser = new System.Windows.Forms.GroupBox();
            this.pnlAdd = new System.Windows.Forms.Panel();
            this.txtPWD = new System.Windows.Forms.TextBox();
            this.txtUserName = new System.Windows.Forms.TextBox();
            this.lblStatus = new System.Windows.Forms.Label();
            this.btnCancel = new System.Windows.Forms.Button();
            this.btnOK = new System.Windows.Forms.Button();
            this.label2 = new System.Windows.Forms.Label();
            this.label3 = new System.Windows.Forms.Label();
            this.dvUsers = new System.Windows.Forms.DataGridView();
            this.imgAccount = new System.Windows.Forms.DataGridViewImageColumn();
            this.imgMobile = new System.Windows.Forms.DataGridViewImageColumn();
            this.colPassCount = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.dataGridViewTextBoxColumn1 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.dataGridViewTextBoxColumn2 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.实名制 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.imgIndex = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.taskMobile = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.cmsUser = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.cmsAddUser = new System.Windows.Forms.ToolStripMenuItem();
            this.cmsPasteUser = new System.Windows.Forms.ToolStripMenuItem();
            this.cmsImportUser = new System.Windows.Forms.ToolStripMenuItem();
            this.cmsExportUser = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripSeparator1 = new System.Windows.Forms.ToolStripSeparator();
            this.cmsEditUser = new System.Windows.Forms.ToolStripMenuItem();
            this.tsmCopy = new System.Windows.Forms.ToolStripMenuItem();
            this.删除所有乘客ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.实名制校验ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.显示隐藏账号ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.cmsDelteUser = new System.Windows.Forms.ToolStripMenuItem();
            this.删除账号及任务ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripSeparator6 = new System.Windows.Forms.ToolStripSeparator();
            this.打开12306网站ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.cmsRelogin = new System.Windows.Forms.ToolStripMenuItem();
            this.全部重新登陆ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.重新登陆未登录账号ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripSeparator2 = new System.Windows.Forms.ToolStripSeparator();
            this.cmsDeleteAllUser = new System.Windows.Forms.ToolStripMenuItem();
            this.tsmRemoveBadUser = new System.Windows.Forms.ToolStripMenuItem();
            this.btnPasteUser = new System.Windows.Forms.Button();
            this.btnDeleUser = new System.Windows.Forms.Button();
            this.btnClearAllUser = new System.Windows.Forms.Button();
            this.btnExportUser = new System.Windows.Forms.Button();
            this.btnAddUser = new System.Windows.Forms.Button();
            this.btnImportUser = new System.Windows.Forms.Button();
            this.cmbCodeType = new System.Windows.Forms.ComboBox();
            this.toolStrip1 = new System.Windows.Forms.ToolStrip();
            this.lblCDN = new System.Windows.Forms.ToolStripButton();
            this.lblDateTime = new System.Windows.Forms.ToolStripDropDownButton();
            this.lblAuto = new System.Windows.Forms.ToolStripLabel();
            this.statusBarPanelCPU = new System.Windows.Forms.ToolStripLabel();
            this.pnlInfo = new System.Windows.Forms.Panel();
            this.spMain = new System.Windows.Forms.SplitContainer();
            this.chkRepeatLog = new System.Windows.Forms.CheckBox();
            this.pnlTongDao = new System.Windows.Forms.Panel();
            this.label7 = new System.Windows.Forms.Label();
            this.label4 = new System.Windows.Forms.Label();
            this.nMaxCodeWait = new System.Windows.Forms.NumericUpDown();
            this.nOrderSubTimeOut = new System.Windows.Forms.NumericUpDown();
            this.label1 = new System.Windows.Forms.Label();
            this.nBlackHouseTimeOut = new System.Windows.Forms.NumericUpDown();
            this.chkBlackHouse = new System.Windows.Forms.CheckBox();
            this.cmbQueryThread = new System.Windows.Forms.ComboBox();
            this.linkLabel1 = new System.Windows.Forms.LinkLabel();
            this.label5 = new System.Windows.Forms.Label();
            this.lnkDaMa = new System.Windows.Forms.LinkLabel();
            this.linkLabel3 = new System.Windows.Forms.LinkLabel();
            this.groupBox8 = new System.Windows.Forms.GroupBox();
            this.rtbNewLog = new System.Windows.Forms.RichTextBox();
            this.cmsLog = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.清空日志ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.复制日志ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.保存日志ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.dvLog = new System.Windows.Forms.DataGridView();
            this.dataGridViewTextBoxColumn4 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.dataGridViewTextBoxColumn6 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.dataGridViewTextBoxColumn7 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.chkNoLog = new System.Windows.Forms.CheckBox();
            this.tipTypeInfo = new System.Windows.Forms.ToolTip(this.components);
            this.statusStrip2 = new System.Windows.Forms.StatusStrip();
            this.lblVersion = new System.Windows.Forms.ToolStripDropDownButton();
            this.lblUpdate = new System.Windows.Forms.ToolStripDropDownButton();
            this.imgIcon = new System.Windows.Forms.ImageList(this.components);
            this.open = new System.Windows.Forms.OpenFileDialog();
            this.save = new System.Windows.Forms.SaveFileDialog();
            this.bgMSG = new System.ComponentModel.BackgroundWorker();
            this.bgSaveInfo = new System.ComponentModel.BackgroundWorker();
            this.bgPublic = new System.ComponentModel.BackgroundWorker();
            this.cmsTicket = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.cmsPassenger = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.bgDeleAllPass = new System.ComponentModel.BackgroundWorker();
            this.mobileImg = new System.Windows.Forms.ImageList(this.components);
            this.dataGridViewImageColumn1 = new System.Windows.Forms.DataGridViewImageColumn();
            this.dataGridViewImageColumn2 = new System.Windows.Forms.DataGridViewImageColumn();
            this.toolStripMenuItem3 = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripMenuItem4 = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripMenuItem5 = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripMenuItem6 = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripMenuItem7 = new System.Windows.Forms.ToolStripMenuItem();
            this.srcTxt = new NewTicket.MyControl.ScrollingText();
            this.cmbDDL = new NewTicket.MyControl.CheckCombo();
            this.btnSave = new System.Windows.Forms.Button();
            this.menuStrip1.SuspendLayout();
            this.pnlSet.SuspendLayout();
            this.grpTask.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.nMaxOrderTime)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.dvTasks)).BeginInit();
            this.cmsTask.SuspendLayout();
            this.grpUser.SuspendLayout();
            this.pnlAdd.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.dvUsers)).BeginInit();
            this.cmsUser.SuspendLayout();
            this.toolStrip1.SuspendLayout();
            this.pnlInfo.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.spMain)).BeginInit();
            this.spMain.Panel1.SuspendLayout();
            this.spMain.Panel2.SuspendLayout();
            this.spMain.SuspendLayout();
            this.pnlTongDao.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.nMaxCodeWait)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.nOrderSubTimeOut)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.nBlackHouseTimeOut)).BeginInit();
            this.groupBox8.SuspendLayout();
            this.cmsLog.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.dvLog)).BeginInit();
            this.statusStrip2.SuspendLayout();
            this.SuspendLayout();
            // 
            // menuStrip1
            // 
            this.menuStrip1.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(240)))), ((int)(((byte)(240)))));
            this.menuStrip1.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.cmsUserManager,
            this.订票提醒ToolStripMenuItem,
            this.打码平台ToolStripMenuItem,
            this.toolStripMenuItem1,
            this.其他ToolStripMenuItem});
            this.menuStrip1.Location = new System.Drawing.Point(0, 0);
            this.menuStrip1.Name = "menuStrip1";
            this.menuStrip1.Size = new System.Drawing.Size(974, 27);
            this.menuStrip1.TabIndex = 0;
            // 
            // cmsUserManager
            // 
            this.cmsUserManager.Image = ((System.Drawing.Image)(resources.GetObject("cmsUserManager.Image")));
            this.cmsUserManager.Name = "cmsUserManager";
            this.cmsUserManager.Size = new System.Drawing.Size(84, 23);
            this.cmsUserManager.Text = "账号列表";
            this.cmsUserManager.Click += new System.EventHandler(this.cmsUserManager_Click);
            // 
            // 订票提醒ToolStripMenuItem
            // 
            this.订票提醒ToolStripMenuItem.Image = ((System.Drawing.Image)(resources.GetObject("订票提醒ToolStripMenuItem.Image")));
            this.订票提醒ToolStripMenuItem.Name = "订票提醒ToolStripMenuItem";
            this.订票提醒ToolStripMenuItem.Size = new System.Drawing.Size(84, 23);
            this.订票提醒ToolStripMenuItem.Text = "订票提醒";
            this.订票提醒ToolStripMenuItem.Click += new System.EventHandler(this.订票提醒ToolStripMenuItem_Click);
            // 
            // 打码平台ToolStripMenuItem
            // 
            this.打码平台ToolStripMenuItem.Image = ((System.Drawing.Image)(resources.GetObject("打码平台ToolStripMenuItem.Image")));
            this.打码平台ToolStripMenuItem.Name = "打码平台ToolStripMenuItem";
            this.打码平台ToolStripMenuItem.Size = new System.Drawing.Size(84, 23);
            this.打码平台ToolStripMenuItem.Text = "打码平台";
            this.打码平台ToolStripMenuItem.Click += new System.EventHandler(this.打码平台ToolStripMenuItem_Click);
            // 
            // toolStripMenuItem1
            // 
            this.toolStripMenuItem1.Image = ((System.Drawing.Image)(resources.GetObject("toolStripMenuItem1.Image")));
            this.toolStripMenuItem1.Name = "toolStripMenuItem1";
            this.toolStripMenuItem1.Size = new System.Drawing.Size(113, 23);
            this.toolStripMenuItem1.Text = "路由/拨号设置";
            this.toolStripMenuItem1.Click += new System.EventHandler(this.tsmNetWork_Click);
            // 
            // 其他ToolStripMenuItem
            // 
            this.其他ToolStripMenuItem.DropDownItems.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.关联任务文件打开方式ToolStripMenuItem,
            this.网络加速QToolStripMenuItem,
            this.封IPToolStripMenuItem,
            this.机器码ToolStripMenuItem,
            this.联系客服ToolStripMenuItem,
            this.意见反馈ToolStripMenuItem});
            this.其他ToolStripMenuItem.Font = new System.Drawing.Font("微软雅黑", 9.5F, System.Drawing.FontStyle.Bold);
            this.其他ToolStripMenuItem.Image = ((System.Drawing.Image)(resources.GetObject("其他ToolStripMenuItem.Image")));
            this.其他ToolStripMenuItem.Name = "其他ToolStripMenuItem";
            this.其他ToolStripMenuItem.Size = new System.Drawing.Size(63, 23);
            this.其他ToolStripMenuItem.Text = "其他";
            // 
            // 关联任务文件打开方式ToolStripMenuItem
            // 
            this.关联任务文件打开方式ToolStripMenuItem.Image = global::NewTicket.Properties.Resources.Set_Security_Question_24px_530425_easyicon_net;
            this.关联任务文件打开方式ToolStripMenuItem.Name = "关联任务文件打开方式ToolStripMenuItem";
            this.关联任务文件打开方式ToolStripMenuItem.Size = new System.Drawing.Size(156, 24);
            this.关联任务文件打开方式ToolStripMenuItem.Text = "关联任务文件";
            this.关联任务文件打开方式ToolStripMenuItem.ToolTipText = "关联任务文件打开方式。\r\n\r\n成功关联到任务文件后，\r\n直接双击导出的任务文件，\r\n即可打开助手，并自动加载对应的任务文件。";
            this.关联任务文件打开方式ToolStripMenuItem.Click += new System.EventHandler(this.关联任务文件打开方式ToolStripMenuItem_Click);
            // 
            // 网络加速QToolStripMenuItem
            // 
            this.网络加速QToolStripMenuItem.Image = ((System.Drawing.Image)(resources.GetObject("网络加速QToolStripMenuItem.Image")));
            this.网络加速QToolStripMenuItem.Name = "网络加速QToolStripMenuItem";
            this.网络加速QToolStripMenuItem.Size = new System.Drawing.Size(156, 24);
            this.网络加速QToolStripMenuItem.Text = "网络加速";
            this.网络加速QToolStripMenuItem.Click += new System.EventHandler(this.网络加速QToolStripMenuItem_Click);
            // 
            // 封IPToolStripMenuItem
            // 
            this.封IPToolStripMenuItem.DropDownItems.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.iPToolStripMenuItem,
            this.本地IP解封ToolStripMenuItem});
            this.封IPToolStripMenuItem.Image = ((System.Drawing.Image)(resources.GetObject("封IPToolStripMenuItem.Image")));
            this.封IPToolStripMenuItem.Name = "封IPToolStripMenuItem";
            this.封IPToolStripMenuItem.Size = new System.Drawing.Size(156, 24);
            this.封IPToolStripMenuItem.Text = "IP被封检测";
            // 
            // iPToolStripMenuItem
            // 
            this.iPToolStripMenuItem.Name = "iPToolStripMenuItem";
            this.iPToolStripMenuItem.Size = new System.Drawing.Size(143, 24);
            this.iPToolStripMenuItem.Text = "本地IP检测";
            this.iPToolStripMenuItem.Click += new System.EventHandler(this.iPToolStripMenuItem_Click);
            // 
            // 本地IP解封ToolStripMenuItem
            // 
            this.本地IP解封ToolStripMenuItem.Name = "本地IP解封ToolStripMenuItem";
            this.本地IP解封ToolStripMenuItem.Size = new System.Drawing.Size(143, 24);
            this.本地IP解封ToolStripMenuItem.Text = "本地IP解封";
            this.本地IP解封ToolStripMenuItem.Click += new System.EventHandler(this.本地IP解封ToolStripMenuItem_Click);
            // 
            // 机器码ToolStripMenuItem
            // 
            this.机器码ToolStripMenuItem.Image = ((System.Drawing.Image)(resources.GetObject("机器码ToolStripMenuItem.Image")));
            this.机器码ToolStripMenuItem.Name = "机器码ToolStripMenuItem";
            this.机器码ToolStripMenuItem.Size = new System.Drawing.Size(156, 24);
            this.机器码ToolStripMenuItem.Text = "机器码";
            this.机器码ToolStripMenuItem.Click += new System.EventHandler(this.机器码ToolStripMenuItem_Click);
            // 
            // 联系客服ToolStripMenuItem
            // 
            this.联系客服ToolStripMenuItem.Image = ((System.Drawing.Image)(resources.GetObject("联系客服ToolStripMenuItem.Image")));
            this.联系客服ToolStripMenuItem.Name = "联系客服ToolStripMenuItem";
            this.联系客服ToolStripMenuItem.Size = new System.Drawing.Size(156, 24);
            this.联系客服ToolStripMenuItem.Text = "联系客服";
            this.联系客服ToolStripMenuItem.Click += new System.EventHandler(this.联系客服ToolStripMenuItem_Click);
            // 
            // 意见反馈ToolStripMenuItem
            // 
            this.意见反馈ToolStripMenuItem.Image = ((System.Drawing.Image)(resources.GetObject("意见反馈ToolStripMenuItem.Image")));
            this.意见反馈ToolStripMenuItem.Name = "意见反馈ToolStripMenuItem";
            this.意见反馈ToolStripMenuItem.Size = new System.Drawing.Size(156, 24);
            this.意见反馈ToolStripMenuItem.Text = "意见反馈";
            this.意见反馈ToolStripMenuItem.Click += new System.EventHandler(this.btnQuestion_Click);
            // 
            // pnlSet
            // 
            this.pnlSet.Controls.Add(this.grpTask);
            this.pnlSet.Controls.Add(this.grpUser);
            this.pnlSet.Dock = System.Windows.Forms.DockStyle.Fill;
            this.pnlSet.Location = new System.Drawing.Point(0, 0);
            this.pnlSet.Name = "pnlSet";
            this.pnlSet.Size = new System.Drawing.Size(974, 355);
            this.pnlSet.TabIndex = 1;
            // 
            // grpTask
            // 
            this.grpTask.Controls.Add(this.btnSave);
            this.grpTask.Controls.Add(this.label6);
            this.grpTask.Controls.Add(this.chkTaskSele);
            this.grpTask.Controls.Add(this.nMaxOrderTime);
            this.grpTask.Controls.Add(this.cmbDDL);
            this.grpTask.Controls.Add(this.btnRefreshTask);
            this.grpTask.Controls.Add(this.btnExportTask);
            this.grpTask.Controls.Add(this.btnImpotTask);
            this.grpTask.Controls.Add(this.btnStopTask);
            this.grpTask.Controls.Add(this.btnClearAllTask);
            this.grpTask.Controls.Add(this.cmsOpType);
            this.grpTask.Controls.Add(this.linkLabel2);
            this.grpTask.Controls.Add(this.btnStartTask);
            this.grpTask.Controls.Add(this.btnEditTask);
            this.grpTask.Controls.Add(this.btnDeleTask);
            this.grpTask.Controls.Add(this.label20);
            this.grpTask.Controls.Add(this.dvTasks);
            this.grpTask.Controls.Add(this.btnNewTask);
            this.grpTask.Controls.Add(this.btnStopAll);
            this.grpTask.Controls.Add(this.btnStartAll);
            this.grpTask.Controls.Add(this.btnCopyTask);
            this.grpTask.Dock = System.Windows.Forms.DockStyle.Fill;
            this.grpTask.Location = new System.Drawing.Point(235, 0);
            this.grpTask.Name = "grpTask";
            this.grpTask.Size = new System.Drawing.Size(739, 355);
            this.grpTask.TabIndex = 118;
            this.grpTask.TabStop = false;
            this.grpTask.Text = "任务列表";
            // 
            // label6
            // 
            this.label6.AutoSize = true;
            this.label6.Location = new System.Drawing.Point(325, 228);
            this.label6.Name = "label6";
            this.label6.Size = new System.Drawing.Size(29, 12);
            this.label6.TabIndex = 125;
            this.label6.Text = "毫秒";
            this.label6.Visible = false;
            // 
            // chkTaskSele
            // 
            this.chkTaskSele.AutoSize = true;
            this.chkTaskSele.Location = new System.Drawing.Point(9, 50);
            this.chkTaskSele.Name = "chkTaskSele";
            this.chkTaskSele.Size = new System.Drawing.Size(15, 14);
            this.chkTaskSele.TabIndex = 138;
            this.chkTaskSele.UseVisualStyleBackColor = true;
            this.chkTaskSele.CheckedChanged += new System.EventHandler(this.chkTaskSele_CheckedChanged);
            // 
            // nMaxOrderTime
            // 
            this.nMaxOrderTime.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle;
            this.nMaxOrderTime.Increment = new decimal(new int[] {
            100,
            0,
            0,
            0});
            this.nMaxOrderTime.Location = new System.Drawing.Point(275, 224);
            this.nMaxOrderTime.Maximum = new decimal(new int[] {
            8000,
            0,
            0,
            0});
            this.nMaxOrderTime.Name = "nMaxOrderTime";
            this.nMaxOrderTime.Size = new System.Drawing.Size(48, 21);
            this.nMaxOrderTime.TabIndex = 126;
            this.nMaxOrderTime.TabStop = false;
            this.tipTypeInfo.SetToolTip(this.nMaxOrderTime, "控制下单过程的总时间，\r\n避免下单过快导致排队或者莫名其妙的不出票。\r\n\r\n默认值为5200\r\n从开始确认订单计时，\r\n到开始提交订单结束。\r\n如果提交速度过快，" +
        "\r\n动态调整该部分总时间为设置的时间。\r\n如果提交过慢，以实际提交时间为准。\r\n\r\n");
            this.nMaxOrderTime.Value = new decimal(new int[] {
            5200,
            0,
            0,
            0});
            this.nMaxOrderTime.Visible = false;
            this.nMaxOrderTime.ValueChanged += new System.EventHandler(this.nMaxOrderTime_ValueChanged);
            // 
            // btnRefreshTask
            // 
            this.btnRefreshTask.Location = new System.Drawing.Point(597, 10);
            this.btnRefreshTask.Name = "btnRefreshTask";
            this.btnRefreshTask.Size = new System.Drawing.Size(64, 32);
            this.btnRefreshTask.TabIndex = 124;
            this.btnRefreshTask.TabStop = false;
            this.btnRefreshTask.Text = "刷新任务";
            this.btnRefreshTask.UseVisualStyleBackColor = true;
            this.btnRefreshTask.Click += new System.EventHandler(this.btnRefreshTask_Click);
            // 
            // btnExportTask
            // 
            this.btnExportTask.Font = new System.Drawing.Font("微软雅黑", 10F, System.Drawing.FontStyle.Bold);
            this.btnExportTask.Location = new System.Drawing.Point(323, 9);
            this.btnExportTask.Name = "btnExportTask";
            this.btnExportTask.Size = new System.Drawing.Size(47, 34);
            this.btnExportTask.TabIndex = 120;
            this.btnExportTask.TabStop = false;
            this.btnExportTask.Text = "导出";
            this.btnExportTask.UseVisualStyleBackColor = true;
            this.btnExportTask.Click += new System.EventHandler(this.btnExportTask_Click);
            // 
            // btnImpotTask
            // 
            this.btnImpotTask.Font = new System.Drawing.Font("微软雅黑", 10F, System.Drawing.FontStyle.Bold);
            this.btnImpotTask.Location = new System.Drawing.Point(275, 9);
            this.btnImpotTask.Name = "btnImpotTask";
            this.btnImpotTask.Size = new System.Drawing.Size(47, 34);
            this.btnImpotTask.TabIndex = 121;
            this.btnImpotTask.TabStop = false;
            this.btnImpotTask.Text = "导入";
            this.btnImpotTask.UseVisualStyleBackColor = true;
            this.btnImpotTask.Click += new System.EventHandler(this.btnImpotTask_Click);
            // 
            // btnStopTask
            // 
            this.btnStopTask.Location = new System.Drawing.Point(192, 17);
            this.btnStopTask.Name = "btnStopTask";
            this.btnStopTask.Size = new System.Drawing.Size(37, 23);
            this.btnStopTask.TabIndex = 124;
            this.btnStopTask.TabStop = false;
            this.btnStopTask.Text = "停止";
            this.btnStopTask.UseVisualStyleBackColor = true;
            this.btnStopTask.Click += new System.EventHandler(this.btnStopTask_Click);
            // 
            // btnClearAllTask
            // 
            this.btnClearAllTask.Location = new System.Drawing.Point(662, 10);
            this.btnClearAllTask.Name = "btnClearAllTask";
            this.btnClearAllTask.Size = new System.Drawing.Size(64, 32);
            this.btnClearAllTask.TabIndex = 127;
            this.btnClearAllTask.TabStop = false;
            this.btnClearAllTask.Text = "清空任务";
            this.btnClearAllTask.UseVisualStyleBackColor = true;
            this.btnClearAllTask.Click += new System.EventHandler(this.btnClearAllTask_Click);
            // 
            // cmsOpType
            // 
            this.cmsOpType.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.cmsOpType.Enabled = false;
            this.cmsOpType.Font = new System.Drawing.Font("宋体", 10F, System.Drawing.FontStyle.Bold);
            this.cmsOpType.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(0)))), ((int)(((byte)(0)))), ((int)(((byte)(192)))));
            this.cmsOpType.FormattingEnabled = true;
            this.cmsOpType.Items.AddRange(new object[] {
            "双核",
            "电脑",
            "手机"});
            this.cmsOpType.Location = new System.Drawing.Point(434, 224);
            this.cmsOpType.Name = "cmsOpType";
            this.cmsOpType.Size = new System.Drawing.Size(53, 21);
            this.cmsOpType.TabIndex = 121;
            this.cmsOpType.TabStop = false;
            this.tipTypeInfo.SetToolTip(this.cmsOpType, "点击选择要使用的订票通道！\r\n双核=电脑+手机！\r\n电脑版主要用于预售或刷票，\r\n手机版主要用于预售。");
            this.cmsOpType.Visible = false;
            this.cmsOpType.SelectedIndexChanged += new System.EventHandler(this.cmsType_SelectedIndexChanged);
            // 
            // linkLabel2
            // 
            this.linkLabel2.AutoSize = true;
            this.linkLabel2.Font = new System.Drawing.Font("宋体", 10F, System.Drawing.FontStyle.Bold);
            this.linkLabel2.LinkBehavior = System.Windows.Forms.LinkBehavior.HoverUnderline;
            this.linkLabel2.LinkColor = System.Drawing.Color.Red;
            this.linkLabel2.Location = new System.Drawing.Point(218, 226);
            this.linkLabel2.Name = "linkLabel2";
            this.linkLabel2.Size = new System.Drawing.Size(60, 14);
            this.linkLabel2.TabIndex = 11;
            this.linkLabel2.TabStop = true;
            this.linkLabel2.Tag = "2";
            this.linkLabel2.Text = "总提交:";
            this.tipTypeInfo.SetToolTip(this.linkLabel2, "控制下单过程的总时间，\r\n避免下单过快导致排队或者莫名其妙的不出票。\r\n\r\n默认值为5200\r\n从开始确认订单计时，\r\n到开始提交订单结束。\r\n如果提交速度过快，" +
        "\r\n动态调整该部分总时间为设置的时间。\r\n如果提交过慢，以实际提交时间为准。\r\n");
            this.linkLabel2.Visible = false;
            // 
            // btnStartTask
            // 
            this.btnStartTask.Location = new System.Drawing.Point(155, 17);
            this.btnStartTask.Name = "btnStartTask";
            this.btnStartTask.Size = new System.Drawing.Size(37, 23);
            this.btnStartTask.TabIndex = 123;
            this.btnStartTask.TabStop = false;
            this.btnStartTask.Text = "开始";
            this.btnStartTask.UseVisualStyleBackColor = true;
            this.btnStartTask.Click += new System.EventHandler(this.btnStartTask_Click);
            // 
            // btnEditTask
            // 
            this.btnEditTask.Location = new System.Drawing.Point(44, 17);
            this.btnEditTask.Name = "btnEditTask";
            this.btnEditTask.Size = new System.Drawing.Size(37, 23);
            this.btnEditTask.TabIndex = 126;
            this.btnEditTask.TabStop = false;
            this.btnEditTask.Text = "修改";
            this.btnEditTask.UseVisualStyleBackColor = true;
            this.btnEditTask.Click += new System.EventHandler(this.btnEditTask_Click);
            // 
            // btnDeleTask
            // 
            this.btnDeleTask.Location = new System.Drawing.Point(118, 17);
            this.btnDeleTask.Name = "btnDeleTask";
            this.btnDeleTask.Size = new System.Drawing.Size(37, 23);
            this.btnDeleTask.TabIndex = 119;
            this.btnDeleTask.TabStop = false;
            this.btnDeleTask.Text = "删除";
            this.btnDeleTask.UseVisualStyleBackColor = true;
            this.btnDeleTask.Click += new System.EventHandler(this.btnDeleTask_Click);
            // 
            // label20
            // 
            this.label20.AutoSize = true;
            this.label20.Font = new System.Drawing.Font("宋体", 10F, System.Drawing.FontStyle.Bold);
            this.label20.ForeColor = System.Drawing.Color.Red;
            this.label20.Location = new System.Drawing.Point(394, 228);
            this.label20.Name = "label20";
            this.label20.Size = new System.Drawing.Size(45, 14);
            this.label20.TabIndex = 122;
            this.label20.Text = "通道:";
            this.label20.Visible = false;
            // 
            // dvTasks
            // 
            this.dvTasks.AllowUserToAddRows = false;
            this.dvTasks.AllowUserToDeleteRows = false;
            this.dvTasks.AllowUserToResizeRows = false;
            this.dvTasks.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.dvTasks.AutoSizeColumnsMode = System.Windows.Forms.DataGridViewAutoSizeColumnsMode.Fill;
            this.dvTasks.BackgroundColor = System.Drawing.SystemColors.ControlDark;
            this.dvTasks.BorderStyle = System.Windows.Forms.BorderStyle.None;
            this.dvTasks.CausesValidation = false;
            dataGridViewCellStyle19.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleCenter;
            dataGridViewCellStyle19.BackColor = System.Drawing.SystemColors.Control;
            dataGridViewCellStyle19.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            dataGridViewCellStyle19.ForeColor = System.Drawing.SystemColors.WindowText;
            dataGridViewCellStyle19.SelectionBackColor = System.Drawing.SystemColors.Highlight;
            dataGridViewCellStyle19.SelectionForeColor = System.Drawing.SystemColors.HighlightText;
            dataGridViewCellStyle19.WrapMode = System.Windows.Forms.DataGridViewTriState.True;
            this.dvTasks.ColumnHeadersDefaultCellStyle = dataGridViewCellStyle19;
            this.dvTasks.ColumnHeadersHeight = 26;
            this.dvTasks.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.DisableResizing;
            this.dvTasks.Columns.AddRange(new System.Windows.Forms.DataGridViewColumn[] {
            this.chkTask,
            this.账号,
            this.TrainNo,
            this.起始站点,
            this.FromStationDescription,
            this.ToStationDescription,
            this.Column9,
            this.status,
            this.LeftTicketDescription,
            this.Remark});
            this.dvTasks.ContextMenuStrip = this.cmsTask;
            dataGridViewCellStyle25.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleLeft;
            dataGridViewCellStyle25.BackColor = System.Drawing.SystemColors.Window;
            dataGridViewCellStyle25.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            dataGridViewCellStyle25.ForeColor = System.Drawing.SystemColors.ControlText;
            dataGridViewCellStyle25.SelectionBackColor = System.Drawing.SystemColors.Highlight;
            dataGridViewCellStyle25.SelectionForeColor = System.Drawing.SystemColors.HighlightText;
            dataGridViewCellStyle25.WrapMode = System.Windows.Forms.DataGridViewTriState.False;
            this.dvTasks.DefaultCellStyle = dataGridViewCellStyle25;
            this.dvTasks.Location = new System.Drawing.Point(6, 44);
            this.dvTasks.MultiSelect = false;
            this.dvTasks.Name = "dvTasks";
            this.dvTasks.ReadOnly = true;
            dataGridViewCellStyle26.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleLeft;
            dataGridViewCellStyle26.BackColor = System.Drawing.SystemColors.Control;
            dataGridViewCellStyle26.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            dataGridViewCellStyle26.ForeColor = System.Drawing.SystemColors.WindowText;
            dataGridViewCellStyle26.SelectionBackColor = System.Drawing.SystemColors.Highlight;
            dataGridViewCellStyle26.SelectionForeColor = System.Drawing.SystemColors.HighlightText;
            dataGridViewCellStyle26.WrapMode = System.Windows.Forms.DataGridViewTriState.True;
            this.dvTasks.RowHeadersDefaultCellStyle = dataGridViewCellStyle26;
            this.dvTasks.RowHeadersVisible = false;
            this.dvTasks.RowTemplate.Height = 25;
            this.dvTasks.SelectionMode = System.Windows.Forms.DataGridViewSelectionMode.FullRowSelect;
            this.dvTasks.ShowEditingIcon = false;
            this.dvTasks.Size = new System.Drawing.Size(730, 308);
            this.dvTasks.TabIndex = 26;
            this.dvTasks.TabStop = false;
            this.dvTasks.CellContentClick += new System.Windows.Forms.DataGridViewCellEventHandler(this.dvTasks_CellContentClick);
            this.dvTasks.CellMouseDoubleClick += new System.Windows.Forms.DataGridViewCellMouseEventHandler(this.dvTasks_CellMouseDoubleClick);
            this.dvTasks.CellMouseDown += new System.Windows.Forms.DataGridViewCellMouseEventHandler(this.dvTasks_CellMouseDown);
            this.dvTasks.KeyDown += new System.Windows.Forms.KeyEventHandler(this.dvTasks_KeyDown);
            // 
            // chkTask
            // 
            this.chkTask.AutoSizeMode = System.Windows.Forms.DataGridViewAutoSizeColumnMode.None;
            this.chkTask.HeaderText = "";
            this.chkTask.Name = "chkTask";
            this.chkTask.ReadOnly = true;
            this.chkTask.Width = 20;
            // 
            // 账号
            // 
            this.账号.AutoSizeMode = System.Windows.Forms.DataGridViewAutoSizeColumnMode.None;
            this.账号.DataPropertyName = "StrNowUserName";
            this.账号.HeaderText = "账号";
            this.账号.Name = "账号";
            this.账号.ReadOnly = true;
            this.账号.Width = 70;
            // 
            // TrainNo
            // 
            this.TrainNo.AutoSizeMode = System.Windows.Forms.DataGridViewAutoSizeColumnMode.None;
            this.TrainNo.DataPropertyName = "TaskTrainNo";
            dataGridViewCellStyle20.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleLeft;
            this.TrainNo.DefaultCellStyle = dataGridViewCellStyle20;
            this.TrainNo.HeaderText = "车次";
            this.TrainNo.Name = "TrainNo";
            this.TrainNo.ReadOnly = true;
            this.TrainNo.Width = 65;
            // 
            // 起始站点
            // 
            this.起始站点.AutoSizeMode = System.Windows.Forms.DataGridViewAutoSizeColumnMode.None;
            this.起始站点.DataPropertyName = "TaskFromTo";
            this.起始站点.HeaderText = "车站";
            this.起始站点.Name = "起始站点";
            this.起始站点.ReadOnly = true;
            this.起始站点.Width = 90;
            // 
            // FromStationDescription
            // 
            this.FromStationDescription.AutoSizeMode = System.Windows.Forms.DataGridViewAutoSizeColumnMode.None;
            this.FromStationDescription.DataPropertyName = "TaskDate";
            dataGridViewCellStyle21.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleLeft;
            this.FromStationDescription.DefaultCellStyle = dataGridViewCellStyle21;
            this.FromStationDescription.HeaderText = "日期(*)";
            this.FromStationDescription.Name = "FromStationDescription";
            this.FromStationDescription.ReadOnly = true;
            this.FromStationDescription.Width = 80;
            // 
            // ToStationDescription
            // 
            this.ToStationDescription.AutoSizeMode = System.Windows.Forms.DataGridViewAutoSizeColumnMode.None;
            this.ToStationDescription.DataPropertyName = "TaskSeat";
            dataGridViewCellStyle22.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleLeft;
            this.ToStationDescription.DefaultCellStyle = dataGridViewCellStyle22;
            this.ToStationDescription.HeaderText = "席别(*)";
            this.ToStationDescription.Name = "ToStationDescription";
            this.ToStationDescription.ReadOnly = true;
            this.ToStationDescription.Width = 65;
            // 
            // Column9
            // 
            this.Column9.AutoSizeMode = System.Windows.Forms.DataGridViewAutoSizeColumnMode.None;
            this.Column9.DataPropertyName = "TaskPassenger";
            dataGridViewCellStyle23.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleLeft;
            this.Column9.DefaultCellStyle = dataGridViewCellStyle23;
            this.Column9.HeaderText = "乘客(*)";
            this.Column9.Name = "Column9";
            this.Column9.ReadOnly = true;
            this.Column9.Width = 90;
            // 
            // status
            // 
            this.status.AutoSizeMode = System.Windows.Forms.DataGridViewAutoSizeColumnMode.None;
            this.status.DataPropertyName = "StrNowUserStatus";
            this.status.HeaderText = "操作(查订单)";
            this.status.Name = "status";
            this.status.ReadOnly = true;
            this.status.Width = 130;
            // 
            // LeftTicketDescription
            // 
            this.LeftTicketDescription.AutoSizeMode = System.Windows.Forms.DataGridViewAutoSizeColumnMode.None;
            this.LeftTicketDescription.DataPropertyName = "StrStatus";
            dataGridViewCellStyle24.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleLeft;
            dataGridViewCellStyle24.Font = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.LeftTicketDescription.DefaultCellStyle = dataGridViewCellStyle24;
            this.LeftTicketDescription.HeaderText = "状态";
            this.LeftTicketDescription.Name = "LeftTicketDescription";
            this.LeftTicketDescription.ReadOnly = true;
            this.LeftTicketDescription.Width = 65;
            // 
            // Remark
            // 
            this.Remark.AutoSizeMode = System.Windows.Forms.DataGridViewAutoSizeColumnMode.None;
            this.Remark.DataPropertyName = "Remark";
            this.Remark.HeaderText = "备注";
            this.Remark.Name = "Remark";
            this.Remark.ReadOnly = true;
            this.Remark.Width = 60;
            // 
            // cmsTask
            // 
            this.cmsTask.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.tsmStart,
            this.tsmStop,
            this.启动选择任务ToolStripMenuItem,
            this.停止选择任务ToolStripMenuItem,
            this.选择任务ToolStripMenuItem,
            this.toolStripSeparator3,
            this.tsmDeleTask,
            this.删除任务及账号ToolStripMenuItem,
            this.toolStripMenuItem2,
            this.toolStripSeparator4,
            this.tsmCopyAccount,
            this.tsmCopyPassenger,
            this.tsmPasterPassenger,
            this.toolStripMenuItem3,
            this.tsmSaveToWeb,
            this.toolStripMenuItem4,
            this.tsmCheckPassenger,
            this.toolStripMenuItem5,
            this.toolStripSeparator5,
            this.tsmCancelOrder,
            this.取消选择订单ToolStripMenuItem,
            this.取消所有订单ToolStripMenuItem,
            this.tsmCancelQueue,
            this.toolStripMenuItem6,
            this.tsmCancelAllQueue,
            this.tsmQueryOrder,
            this.tsmQueryCompleteOrder,
            this.tsmCopyThisOrder,
            this.tsmOpenIE,
            this.toolStripMenuItem7});
            this.cmsTask.Name = "cmsPassenger";
            this.cmsTask.Size = new System.Drawing.Size(209, 616);
            this.cmsTask.Opening += new System.ComponentModel.CancelEventHandler(this.cmsTask_Opening);
            // 
            // tsmStart
            // 
            this.tsmStart.Name = "tsmStart";
            this.tsmStart.Size = new System.Drawing.Size(208, 22);
            this.tsmStart.Text = "启动当前任务";
            this.tsmStart.Click += new System.EventHandler(this.tsmStart_Click);
            // 
            // tsmStop
            // 
            this.tsmStop.Name = "tsmStop";
            this.tsmStop.Size = new System.Drawing.Size(208, 22);
            this.tsmStop.Text = "停止当前任务";
            this.tsmStop.Click += new System.EventHandler(this.tsmStop_Click);
            // 
            // 启动选择任务ToolStripMenuItem
            // 
            this.启动选择任务ToolStripMenuItem.Name = "启动选择任务ToolStripMenuItem";
            this.启动选择任务ToolStripMenuItem.Size = new System.Drawing.Size(208, 22);
            this.启动选择任务ToolStripMenuItem.Text = "启动已勾选任务";
            this.启动选择任务ToolStripMenuItem.Visible = false;
            this.启动选择任务ToolStripMenuItem.Click += new System.EventHandler(this.btnStartTask_Click);
            // 
            // 停止选择任务ToolStripMenuItem
            // 
            this.停止选择任务ToolStripMenuItem.Name = "停止选择任务ToolStripMenuItem";
            this.停止选择任务ToolStripMenuItem.Size = new System.Drawing.Size(208, 22);
            this.停止选择任务ToolStripMenuItem.Text = "停止已勾选任务";
            this.停止选择任务ToolStripMenuItem.Visible = false;
            this.停止选择任务ToolStripMenuItem.Click += new System.EventHandler(this.btnStopTask_Click);
            // 
            // 选择任务ToolStripMenuItem
            // 
            this.选择任务ToolStripMenuItem.DropDownItems.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.全选ToolStripMenuItem1,
            this.反选ToolStripMenuItem1,
            this.选择所有已停止ToolStripMenuItem,
            this.所有正在运行ToolStripMenuItem,
            this.所有已完成ToolStripMenuItem});
            this.选择任务ToolStripMenuItem.Name = "选择任务ToolStripMenuItem";
            this.选择任务ToolStripMenuItem.Size = new System.Drawing.Size(208, 22);
            this.选择任务ToolStripMenuItem.Text = "选择任务";
            // 
            // 全选ToolStripMenuItem1
            // 
            this.全选ToolStripMenuItem1.Name = "全选ToolStripMenuItem1";
            this.全选ToolStripMenuItem1.Size = new System.Drawing.Size(148, 22);
            this.全选ToolStripMenuItem1.Text = "全选";
            this.全选ToolStripMenuItem1.Click += new System.EventHandler(this.全选ToolStripMenuItem_Click);
            // 
            // 反选ToolStripMenuItem1
            // 
            this.反选ToolStripMenuItem1.Name = "反选ToolStripMenuItem1";
            this.反选ToolStripMenuItem1.Size = new System.Drawing.Size(148, 22);
            this.反选ToolStripMenuItem1.Text = "反选";
            this.反选ToolStripMenuItem1.Click += new System.EventHandler(this.反选ToolStripMenuItem_Click);
            // 
            // 选择所有已停止ToolStripMenuItem
            // 
            this.选择所有已停止ToolStripMenuItem.Name = "选择所有已停止ToolStripMenuItem";
            this.选择所有已停止ToolStripMenuItem.Size = new System.Drawing.Size(148, 22);
            this.选择所有已停止ToolStripMenuItem.Text = "所有未启动";
            this.选择所有已停止ToolStripMenuItem.Click += new System.EventHandler(this.选择所有已停止ToolStripMenuItem_Click);
            // 
            // 所有正在运行ToolStripMenuItem
            // 
            this.所有正在运行ToolStripMenuItem.Name = "所有正在运行ToolStripMenuItem";
            this.所有正在运行ToolStripMenuItem.Size = new System.Drawing.Size(148, 22);
            this.所有正在运行ToolStripMenuItem.Text = "所有正在运行";
            this.所有正在运行ToolStripMenuItem.Click += new System.EventHandler(this.所有正在运行ToolStripMenuItem_Click);
            // 
            // 所有已完成ToolStripMenuItem
            // 
            this.所有已完成ToolStripMenuItem.Name = "所有已完成ToolStripMenuItem";
            this.所有已完成ToolStripMenuItem.Size = new System.Drawing.Size(148, 22);
            this.所有已完成ToolStripMenuItem.Text = "所有已完成";
            this.所有已完成ToolStripMenuItem.Click += new System.EventHandler(this.所有已完成ToolStripMenuItem_Click);
            // 
            // toolStripSeparator3
            // 
            this.toolStripSeparator3.Name = "toolStripSeparator3";
            this.toolStripSeparator3.Size = new System.Drawing.Size(205, 6);
            // 
            // tsmDeleTask
            // 
            this.tsmDeleTask.Name = "tsmDeleTask";
            this.tsmDeleTask.Size = new System.Drawing.Size(208, 22);
            this.tsmDeleTask.Text = "删除任务";
            this.tsmDeleTask.Click += new System.EventHandler(this.tsmDeleTask_Click);
            // 
            // 删除任务及账号ToolStripMenuItem
            // 
            this.删除任务及账号ToolStripMenuItem.Name = "删除任务及账号ToolStripMenuItem";
            this.删除任务及账号ToolStripMenuItem.Size = new System.Drawing.Size(208, 22);
            this.删除任务及账号ToolStripMenuItem.Text = "删除任务及账号";
            this.删除任务及账号ToolStripMenuItem.Click += new System.EventHandler(this.删除任务及账号ToolStripMenuItem_Click);
            // 
            // toolStripMenuItem2
            // 
            this.toolStripMenuItem2.Name = "toolStripMenuItem2";
            this.toolStripMenuItem2.Size = new System.Drawing.Size(208, 22);
            this.toolStripMenuItem2.Text = "删除勾选任务及账号";
            this.toolStripMenuItem2.Click += new System.EventHandler(this.toolStripMenuItem2_Click);
            // 
            // toolStripSeparator4
            // 
            this.toolStripSeparator4.Name = "toolStripSeparator4";
            this.toolStripSeparator4.Size = new System.Drawing.Size(205, 6);
            // 
            // tsmCopyAccount
            // 
            this.tsmCopyAccount.Name = "tsmCopyAccount";
            this.tsmCopyAccount.Size = new System.Drawing.Size(208, 22);
            this.tsmCopyAccount.Text = "复制账号密码";
            this.tsmCopyAccount.Click += new System.EventHandler(this.tsmCopyAccount_Click);
            // 
            // tsmCopyPassenger
            // 
            this.tsmCopyPassenger.Name = "tsmCopyPassenger";
            this.tsmCopyPassenger.Size = new System.Drawing.Size(208, 22);
            this.tsmCopyPassenger.Text = "复制乘客";
            this.tsmCopyPassenger.Click += new System.EventHandler(this.tsmCopyPassenger_Click);
            // 
            // tsmPasterPassenger
            // 
            this.tsmPasterPassenger.Name = "tsmPasterPassenger";
            this.tsmPasterPassenger.Size = new System.Drawing.Size(208, 22);
            this.tsmPasterPassenger.Text = "粘贴乘客到任务";
            this.tsmPasterPassenger.Click += new System.EventHandler(this.tsmPasterPassenger_Click);
            // 
            // tsmSaveToWeb
            // 
            this.tsmSaveToWeb.Name = "tsmSaveToWeb";
            this.tsmSaveToWeb.Size = new System.Drawing.Size(208, 22);
            this.tsmSaveToWeb.Text = "保存乘客到官网";
            this.tsmSaveToWeb.Click += new System.EventHandler(this.tsmSaveToWeb_Click);
            // 
            // tsmCheckPassenger
            // 
            this.tsmCheckPassenger.Font = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.tsmCheckPassenger.Name = "tsmCheckPassenger";
            this.tsmCheckPassenger.Size = new System.Drawing.Size(208, 22);
            this.tsmCheckPassenger.Text = "校验订票乘客";
            this.tsmCheckPassenger.Click += new System.EventHandler(this.tsmCheckPassenger_Click);
            // 
            // toolStripSeparator5
            // 
            this.toolStripSeparator5.Name = "toolStripSeparator5";
            this.toolStripSeparator5.Size = new System.Drawing.Size(205, 6);
            // 
            // tsmCancelQueue
            // 
            this.tsmCancelQueue.Font = new System.Drawing.Font("Microsoft YaHei UI", 9F);
            this.tsmCancelQueue.Name = "tsmCancelQueue";
            this.tsmCancelQueue.Size = new System.Drawing.Size(208, 22);
            this.tsmCancelQueue.Text = "取消任务排队";
            this.tsmCancelQueue.Click += new System.EventHandler(this.tsmCancelQueue_Click);
            // 
            // tsmCancelAllQueue
            // 
            this.tsmCancelAllQueue.Font = new System.Drawing.Font("Microsoft YaHei UI", 9F);
            this.tsmCancelAllQueue.Name = "tsmCancelAllQueue";
            this.tsmCancelAllQueue.Size = new System.Drawing.Size(208, 22);
            this.tsmCancelAllQueue.Text = "取消所有任务排队";
            this.tsmCancelAllQueue.Click += new System.EventHandler(this.tsmCancelAllQueue_Click);
            // 
            // tsmQueryOrder
            // 
            this.tsmQueryOrder.Font = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.tsmQueryOrder.Name = "tsmQueryOrder";
            this.tsmQueryOrder.Size = new System.Drawing.Size(208, 22);
            this.tsmQueryOrder.Text = "查询未完成订单";
            this.tsmQueryOrder.Click += new System.EventHandler(this.tsmQueryOrder_Click);
            // 
            // tsmQueryCompleteOrder
            // 
            this.tsmQueryCompleteOrder.Font = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.tsmQueryCompleteOrder.Name = "tsmQueryCompleteOrder";
            this.tsmQueryCompleteOrder.Size = new System.Drawing.Size(208, 22);
            this.tsmQueryCompleteOrder.Text = "查询已完成订单";
            this.tsmQueryCompleteOrder.Click += new System.EventHandler(this.tsmQueryCompleteOrder_Click);
            // 
            // tsmCancelOrder
            // 
            this.tsmCancelOrder.Font = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.tsmCancelOrder.Name = "tsmCancelOrder";
            this.tsmCancelOrder.Size = new System.Drawing.Size(208, 22);
            this.tsmCancelOrder.Text = "取消订单";
            this.tsmCancelOrder.Click += new System.EventHandler(this.tsmCancelOrder_Click);
            // 
            // 取消所有订单ToolStripMenuItem
            // 
            this.取消所有订单ToolStripMenuItem.Font = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Bold);
            this.取消所有订单ToolStripMenuItem.Name = "取消所有订单ToolStripMenuItem";
            this.取消所有订单ToolStripMenuItem.Size = new System.Drawing.Size(208, 22);
            this.取消所有订单ToolStripMenuItem.Text = "取消所有订单";
            this.取消所有订单ToolStripMenuItem.Click += new System.EventHandler(this.取消所有订单ToolStripMenuItem_Click);
            // 
            // 取消选择订单ToolStripMenuItem
            // 
            this.取消选择订单ToolStripMenuItem.Font = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Bold);
            this.取消选择订单ToolStripMenuItem.Name = "取消选择订单ToolStripMenuItem";
            this.取消选择订单ToolStripMenuItem.Size = new System.Drawing.Size(208, 22);
            this.取消选择订单ToolStripMenuItem.Text = "取消选择订单";
            this.取消选择订单ToolStripMenuItem.Click += new System.EventHandler(this.取消选择订单ToolStripMenuItem_Click);
            // 
            // tsmCopyThisOrder
            // 
            this.tsmCopyThisOrder.Font = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.tsmCopyThisOrder.Name = "tsmCopyThisOrder";
            this.tsmCopyThisOrder.Size = new System.Drawing.Size(208, 22);
            this.tsmCopyThisOrder.Text = "复制当前订单";
            this.tsmCopyThisOrder.Click += new System.EventHandler(this.复制成功订单ToolStripMenuItem_Click);
            // 
            // tsmOpenIE
            // 
            this.tsmOpenIE.Name = "tsmOpenIE";
            this.tsmOpenIE.Size = new System.Drawing.Size(208, 22);
            this.tsmOpenIE.Text = "打开12306网页";
            this.tsmOpenIE.Click += new System.EventHandler(this.tsmOpenIE_Click);
            // 
            // btnNewTask
            // 
            this.btnNewTask.Location = new System.Drawing.Point(7, 17);
            this.btnNewTask.Name = "btnNewTask";
            this.btnNewTask.Size = new System.Drawing.Size(37, 23);
            this.btnNewTask.TabIndex = 122;
            this.btnNewTask.TabStop = false;
            this.btnNewTask.Text = "新建";
            this.btnNewTask.UseVisualStyleBackColor = true;
            this.btnNewTask.Click += new System.EventHandler(this.btnNewTask_Click);
            // 
            // btnStopAll
            // 
            this.btnStopAll.Location = new System.Drawing.Point(532, 10);
            this.btnStopAll.Name = "btnStopAll";
            this.btnStopAll.Size = new System.Drawing.Size(64, 32);
            this.btnStopAll.TabIndex = 120;
            this.btnStopAll.TabStop = false;
            this.btnStopAll.Text = "全部停止";
            this.btnStopAll.UseVisualStyleBackColor = true;
            this.btnStopAll.Click += new System.EventHandler(this.btnStopAll_Click);
            // 
            // btnStartAll
            // 
            this.btnStartAll.Location = new System.Drawing.Point(467, 10);
            this.btnStartAll.Name = "btnStartAll";
            this.btnStartAll.Size = new System.Drawing.Size(64, 32);
            this.btnStartAll.TabIndex = 121;
            this.btnStartAll.TabStop = false;
            this.btnStartAll.Text = "全部开始";
            this.btnStartAll.UseVisualStyleBackColor = true;
            this.btnStartAll.Click += new System.EventHandler(this.btnStartAll_Click);
            // 
            // btnCopyTask
            // 
            this.btnCopyTask.Location = new System.Drawing.Point(81, 17);
            this.btnCopyTask.Name = "btnCopyTask";
            this.btnCopyTask.Size = new System.Drawing.Size(37, 23);
            this.btnCopyTask.TabIndex = 118;
            this.btnCopyTask.TabStop = false;
            this.btnCopyTask.Text = "复制";
            this.btnCopyTask.UseVisualStyleBackColor = true;
            this.btnCopyTask.Click += new System.EventHandler(this.btnCopyTask_Click);
            // 
            // grpUser
            // 
            this.grpUser.Controls.Add(this.pnlAdd);
            this.grpUser.Controls.Add(this.dvUsers);
            this.grpUser.Controls.Add(this.btnPasteUser);
            this.grpUser.Controls.Add(this.btnDeleUser);
            this.grpUser.Controls.Add(this.btnClearAllUser);
            this.grpUser.Controls.Add(this.btnExportUser);
            this.grpUser.Controls.Add(this.btnAddUser);
            this.grpUser.Controls.Add(this.btnImportUser);
            this.grpUser.Dock = System.Windows.Forms.DockStyle.Left;
            this.grpUser.Location = new System.Drawing.Point(0, 0);
            this.grpUser.Name = "grpUser";
            this.grpUser.Size = new System.Drawing.Size(235, 355);
            this.grpUser.TabIndex = 98;
            this.grpUser.TabStop = false;
            this.grpUser.Text = "账号列表";
            // 
            // pnlAdd
            // 
            this.pnlAdd.Controls.Add(this.txtPWD);
            this.pnlAdd.Controls.Add(this.txtUserName);
            this.pnlAdd.Controls.Add(this.lblStatus);
            this.pnlAdd.Controls.Add(this.btnCancel);
            this.pnlAdd.Controls.Add(this.btnOK);
            this.pnlAdd.Controls.Add(this.label2);
            this.pnlAdd.Controls.Add(this.label3);
            this.pnlAdd.Location = new System.Drawing.Point(9, 123);
            this.pnlAdd.Name = "pnlAdd";
            this.pnlAdd.Size = new System.Drawing.Size(221, 109);
            this.pnlAdd.TabIndex = 120;
            this.pnlAdd.Visible = false;
            // 
            // txtPWD
            // 
            this.txtPWD.ImeMode = System.Windows.Forms.ImeMode.Off;
            this.txtPWD.Location = new System.Drawing.Point(74, 51);
            this.txtPWD.Name = "txtPWD";
            this.txtPWD.Size = new System.Drawing.Size(109, 21);
            this.txtPWD.TabIndex = 121;
            // 
            // txtUserName
            // 
            this.txtUserName.ImeMode = System.Windows.Forms.ImeMode.Off;
            this.txtUserName.Location = new System.Drawing.Point(74, 25);
            this.txtUserName.Name = "txtUserName";
            this.txtUserName.Size = new System.Drawing.Size(109, 21);
            this.txtUserName.TabIndex = 0;
            // 
            // lblStatus
            // 
            this.lblStatus.Font = new System.Drawing.Font("宋体", 11F, System.Drawing.FontStyle.Bold);
            this.lblStatus.Location = new System.Drawing.Point(3, 5);
            this.lblStatus.Name = "lblStatus";
            this.lblStatus.Size = new System.Drawing.Size(215, 20);
            this.lblStatus.TabIndex = 124;
            this.lblStatus.Text = "添加用户信息";
            this.lblStatus.TextAlign = System.Drawing.ContentAlignment.TopCenter;
            // 
            // btnCancel
            // 
            this.btnCancel.BackgroundImageLayout = System.Windows.Forms.ImageLayout.Center;
            this.btnCancel.FlatStyle = System.Windows.Forms.FlatStyle.Popup;
            this.btnCancel.Location = new System.Drawing.Point(113, 78);
            this.btnCancel.Name = "btnCancel";
            this.btnCancel.Size = new System.Drawing.Size(62, 21);
            this.btnCancel.TabIndex = 123;
            this.btnCancel.Text = "取消";
            this.btnCancel.UseVisualStyleBackColor = true;
            this.btnCancel.Click += new System.EventHandler(this.btnCancel_Click);
            // 
            // btnOK
            // 
            this.btnOK.BackgroundImageLayout = System.Windows.Forms.ImageLayout.Center;
            this.btnOK.FlatStyle = System.Windows.Forms.FlatStyle.Popup;
            this.btnOK.Location = new System.Drawing.Point(45, 78);
            this.btnOK.Name = "btnOK";
            this.btnOK.Size = new System.Drawing.Size(62, 21);
            this.btnOK.TabIndex = 122;
            this.btnOK.Text = "确定";
            this.btnOK.UseVisualStyleBackColor = true;
            this.btnOK.Click += new System.EventHandler(this.btnOK_Click);
            // 
            // label2
            // 
            this.label2.AutoSize = true;
            this.label2.Location = new System.Drawing.Point(27, 55);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(47, 12);
            this.label2.TabIndex = 121;
            this.label2.Text = "密  码:";
            // 
            // label3
            // 
            this.label3.AutoSize = true;
            this.label3.Location = new System.Drawing.Point(27, 29);
            this.label3.Name = "label3";
            this.label3.Size = new System.Drawing.Size(47, 12);
            this.label3.TabIndex = 0;
            this.label3.Text = "用户名:";
            // 
            // dvUsers
            // 
            this.dvUsers.AllowUserToAddRows = false;
            this.dvUsers.AllowUserToDeleteRows = false;
            this.dvUsers.AllowUserToResizeColumns = false;
            this.dvUsers.AllowUserToResizeRows = false;
            this.dvUsers.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.dvUsers.BackgroundColor = System.Drawing.Color.WhiteSmoke;
            this.dvUsers.BorderStyle = System.Windows.Forms.BorderStyle.None;
            this.dvUsers.CausesValidation = false;
            dataGridViewCellStyle1.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleCenter;
            dataGridViewCellStyle1.BackColor = System.Drawing.SystemColors.Control;
            dataGridViewCellStyle1.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            dataGridViewCellStyle1.ForeColor = System.Drawing.SystemColors.WindowText;
            dataGridViewCellStyle1.SelectionBackColor = System.Drawing.SystemColors.Highlight;
            dataGridViewCellStyle1.SelectionForeColor = System.Drawing.SystemColors.HighlightText;
            dataGridViewCellStyle1.WrapMode = System.Windows.Forms.DataGridViewTriState.True;
            this.dvUsers.ColumnHeadersDefaultCellStyle = dataGridViewCellStyle1;
            this.dvUsers.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.AutoSize;
            this.dvUsers.Columns.AddRange(new System.Windows.Forms.DataGridViewColumn[] {
            this.imgAccount,
            this.imgMobile,
            this.colPassCount,
            this.dataGridViewTextBoxColumn1,
            this.dataGridViewTextBoxColumn2,
            this.实名制,
            this.imgIndex,
            this.taskMobile});
            this.dvUsers.ContextMenuStrip = this.cmsUser;
            this.dvUsers.Location = new System.Drawing.Point(6, 42);
            this.dvUsers.Name = "dvUsers";
            this.dvUsers.ReadOnly = true;
            this.dvUsers.RowHeadersVisible = false;
            this.dvUsers.RowTemplate.Height = 25;
            this.dvUsers.SelectionMode = System.Windows.Forms.DataGridViewSelectionMode.FullRowSelect;
            this.dvUsers.Size = new System.Drawing.Size(226, 313);
            this.dvUsers.TabIndex = 118;
            this.dvUsers.TabStop = false;
            this.dvUsers.DataBindingComplete += new System.Windows.Forms.DataGridViewBindingCompleteEventHandler(this.dvUsers_DataBindingComplete);
            this.dvUsers.KeyDown += new System.Windows.Forms.KeyEventHandler(this.dvUsers_KeyDown);
            this.dvUsers.MouseDoubleClick += new System.Windows.Forms.MouseEventHandler(this.gvUsers_MouseDoubleClick);
            // 
            // imgAccount
            // 
            this.imgAccount.HeaderText = "";
            this.imgAccount.Image = ((System.Drawing.Image)(resources.GetObject("imgAccount.Image")));
            this.imgAccount.Name = "imgAccount";
            this.imgAccount.ReadOnly = true;
            this.imgAccount.Width = 20;
            // 
            // imgMobile
            // 
            this.imgMobile.HeaderText = "";
            this.imgMobile.Image = ((System.Drawing.Image)(resources.GetObject("imgMobile.Image")));
            this.imgMobile.Name = "imgMobile";
            this.imgMobile.ReadOnly = true;
            this.imgMobile.Width = 20;
            // 
            // colPassCount
            // 
            this.colPassCount.DataPropertyName = "NPassengerCount";
            this.colPassCount.FillWeight = 20F;
            this.colPassCount.HeaderText = "";
            this.colPassCount.Name = "colPassCount";
            this.colPassCount.ReadOnly = true;
            this.colPassCount.Width = 20;
            // 
            // dataGridViewTextBoxColumn1
            // 
            this.dataGridViewTextBoxColumn1.DataPropertyName = "StrNowUserNameStr";
            dataGridViewCellStyle7.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleLeft;
            this.dataGridViewTextBoxColumn1.DefaultCellStyle = dataGridViewCellStyle7;
            this.dataGridViewTextBoxColumn1.HeaderText = "用户名";
            this.dataGridViewTextBoxColumn1.Name = "dataGridViewTextBoxColumn1";
            this.dataGridViewTextBoxColumn1.ReadOnly = true;
            // 
            // dataGridViewTextBoxColumn2
            // 
            this.dataGridViewTextBoxColumn2.DataPropertyName = "StrNowUserPWD";
            dataGridViewCellStyle8.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleCenter;
            dataGridViewCellStyle8.Format = "MM月dd日";
            dataGridViewCellStyle8.NullValue = null;
            this.dataGridViewTextBoxColumn2.DefaultCellStyle = dataGridViewCellStyle8;
            this.dataGridViewTextBoxColumn2.HeaderText = "密码";
            this.dataGridViewTextBoxColumn2.Name = "dataGridViewTextBoxColumn2";
            this.dataGridViewTextBoxColumn2.ReadOnly = true;
            this.dataGridViewTextBoxColumn2.Visible = false;
            // 
            // 实名制
            // 
            this.实名制.DataPropertyName = "NowAccountStatus";
            dataGridViewCellStyle27.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleCenter;
            this.实名制.DefaultCellStyle = dataGridViewCellStyle27;
            this.实名制.HeaderText = "实名";
            this.实名制.Name = "实名制";
            this.实名制.ReadOnly = true;
            this.实名制.Width = 60;
            // 
            // imgIndex
            // 
            this.imgIndex.DataPropertyName = "TaskImg";
            this.imgIndex.HeaderText = "imgIndex";
            this.imgIndex.Name = "imgIndex";
            this.imgIndex.ReadOnly = true;
            this.imgIndex.Visible = false;
            this.imgIndex.Width = 80;
            // 
            // taskMobile
            // 
            this.taskMobile.DataPropertyName = "TaskMobile";
            this.taskMobile.HeaderText = "taskMobile";
            this.taskMobile.Name = "taskMobile";
            this.taskMobile.ReadOnly = true;
            this.taskMobile.Visible = false;
            this.taskMobile.Width = 80;
            // 
            // cmsUser
            // 
            this.cmsUser.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.cmsAddUser,
            this.cmsPasteUser,
            this.cmsImportUser,
            this.cmsExportUser,
            this.toolStripSeparator1,
            this.cmsEditUser,
            this.tsmCopy,
            this.删除所有乘客ToolStripMenuItem,
            this.实名制校验ToolStripMenuItem,
            this.显示隐藏账号ToolStripMenuItem,
            this.cmsDelteUser,
            this.删除账号及任务ToolStripMenuItem,
            this.toolStripSeparator6,
            this.打开12306网站ToolStripMenuItem,
            this.cmsRelogin,
            this.全部重新登陆ToolStripMenuItem,
            this.重新登陆未登录账号ToolStripMenuItem,
            this.toolStripSeparator2,
            this.cmsDeleteAllUser,
            this.tsmRemoveBadUser});
            this.cmsUser.Name = "cmsPassenger";
            this.cmsUser.Size = new System.Drawing.Size(185, 430);
            // 
            // cmsAddUser
            // 
            this.cmsAddUser.Name = "cmsAddUser";
            this.cmsAddUser.Size = new System.Drawing.Size(184, 24);
            this.cmsAddUser.Text = "添加账号";
            this.cmsAddUser.Click += new System.EventHandler(this.btnAddUser_Click);
            // 
            // cmsPasteUser
            // 
            this.cmsPasteUser.Name = "cmsPasteUser";
            this.cmsPasteUser.Size = new System.Drawing.Size(184, 24);
            this.cmsPasteUser.Text = "粘贴导入";
            this.cmsPasteUser.Click += new System.EventHandler(this.btnPasteUser_Click);
            // 
            // cmsImportUser
            // 
            this.cmsImportUser.Name = "cmsImportUser";
            this.cmsImportUser.Size = new System.Drawing.Size(184, 24);
            this.cmsImportUser.Text = "文本导入";
            this.cmsImportUser.Click += new System.EventHandler(this.btnImportUser_Click);
            // 
            // cmsExportUser
            // 
            this.cmsExportUser.Name = "cmsExportUser";
            this.cmsExportUser.Size = new System.Drawing.Size(184, 24);
            this.cmsExportUser.Text = "导出账号";
            this.cmsExportUser.Click += new System.EventHandler(this.btnExportUser_Click);
            // 
            // toolStripSeparator1
            // 
            this.toolStripSeparator1.Name = "toolStripSeparator1";
            this.toolStripSeparator1.Size = new System.Drawing.Size(181, 6);
            // 
            // cmsEditUser
            // 
            this.cmsEditUser.Name = "cmsEditUser";
            this.cmsEditUser.Size = new System.Drawing.Size(184, 24);
            this.cmsEditUser.Text = "修改账号";
            this.cmsEditUser.Click += new System.EventHandler(this.修改账号ToolStripMenuItem_Click);
            // 
            // tsmCopy
            // 
            this.tsmCopy.Name = "tsmCopy";
            this.tsmCopy.Size = new System.Drawing.Size(184, 24);
            this.tsmCopy.Text = "复制账号";
            this.tsmCopy.Click += new System.EventHandler(this.tsmCopy_Click);
            // 
            // 删除所有乘客ToolStripMenuItem
            // 
            this.删除所有乘客ToolStripMenuItem.Font = new System.Drawing.Font("微软雅黑", 10.5F, System.Drawing.FontStyle.Bold);
            this.删除所有乘客ToolStripMenuItem.ForeColor = System.Drawing.Color.Red;
            this.删除所有乘客ToolStripMenuItem.Name = "删除所有乘客ToolStripMenuItem";
            this.删除所有乘客ToolStripMenuItem.Size = new System.Drawing.Size(184, 24);
            this.删除所有乘客ToolStripMenuItem.Text = "删除所有乘客";
            this.删除所有乘客ToolStripMenuItem.Click += new System.EventHandler(this.删除所有乘客ToolStripMenuItem_Click);
            // 
            // 实名制校验ToolStripMenuItem
            // 
            this.实名制校验ToolStripMenuItem.Font = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Bold);
            this.实名制校验ToolStripMenuItem.Name = "实名制校验ToolStripMenuItem";
            this.实名制校验ToolStripMenuItem.Size = new System.Drawing.Size(184, 24);
            this.实名制校验ToolStripMenuItem.Text = "实名制校验";
            this.实名制校验ToolStripMenuItem.Click += new System.EventHandler(this.实名制校验ToolStripMenuItem_Click);
            // 
            // 显示隐藏账号ToolStripMenuItem
            // 
            this.显示隐藏账号ToolStripMenuItem.Font = new System.Drawing.Font("微软雅黑", 10.5F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.显示隐藏账号ToolStripMenuItem.ForeColor = System.Drawing.Color.Red;
            this.显示隐藏账号ToolStripMenuItem.Name = "显示隐藏账号ToolStripMenuItem";
            this.显示隐藏账号ToolStripMenuItem.Size = new System.Drawing.Size(184, 24);
            this.显示隐藏账号ToolStripMenuItem.Text = "显示所有账号";
            this.显示隐藏账号ToolStripMenuItem.ToolTipText = "显示已删除但是有任务的账号";
            // 
            // cmsDelteUser
            // 
            this.cmsDelteUser.Name = "cmsDelteUser";
            this.cmsDelteUser.Size = new System.Drawing.Size(184, 24);
            this.cmsDelteUser.Text = "删除账号";
            this.cmsDelteUser.Click += new System.EventHandler(this.btnDeleUser_Click);
            // 
            // 删除账号及任务ToolStripMenuItem
            // 
            this.删除账号及任务ToolStripMenuItem.Name = "删除账号及任务ToolStripMenuItem";
            this.删除账号及任务ToolStripMenuItem.Size = new System.Drawing.Size(184, 24);
            this.删除账号及任务ToolStripMenuItem.Text = "删除账号及任务";
            this.删除账号及任务ToolStripMenuItem.Click += new System.EventHandler(this.删除账号及任务ToolStripMenuItem_Click);
            // 
            // toolStripSeparator6
            // 
            this.toolStripSeparator6.Name = "toolStripSeparator6";
            this.toolStripSeparator6.Size = new System.Drawing.Size(181, 6);
            // 
            // 打开12306网站ToolStripMenuItem
            // 
            this.打开12306网站ToolStripMenuItem.Name = "打开12306网站ToolStripMenuItem";
            this.打开12306网站ToolStripMenuItem.Size = new System.Drawing.Size(184, 24);
            this.打开12306网站ToolStripMenuItem.Text = "打开12306网站";
            this.打开12306网站ToolStripMenuItem.Click += new System.EventHandler(this.打开12306网站ToolStripMenuItem_Click);
            // 
            // cmsRelogin
            // 
            this.cmsRelogin.Font = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.cmsRelogin.Name = "cmsRelogin";
            this.cmsRelogin.Size = new System.Drawing.Size(184, 24);
            this.cmsRelogin.Text = "重新登陆";
            this.cmsRelogin.Click += new System.EventHandler(this.cmsRelogin_Click);
            // 
            // 全部重新登陆ToolStripMenuItem
            // 
            this.全部重新登陆ToolStripMenuItem.Font = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Bold);
            this.全部重新登陆ToolStripMenuItem.Name = "全部重新登陆ToolStripMenuItem";
            this.全部重新登陆ToolStripMenuItem.Size = new System.Drawing.Size(184, 24);
            this.全部重新登陆ToolStripMenuItem.Text = "全部重新登陆";
            this.全部重新登陆ToolStripMenuItem.Click += new System.EventHandler(this.全部重新登陆ToolStripMenuItem_Click);
            // 
            // 重新登陆未登录账号ToolStripMenuItem
            // 
            this.重新登陆未登录账号ToolStripMenuItem.Font = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Bold);
            this.重新登陆未登录账号ToolStripMenuItem.Name = "重新登陆未登录账号ToolStripMenuItem";
            this.重新登陆未登录账号ToolStripMenuItem.Size = new System.Drawing.Size(184, 24);
            this.重新登陆未登录账号ToolStripMenuItem.Text = "重新登陆未登录账号";
            this.重新登陆未登录账号ToolStripMenuItem.Click += new System.EventHandler(this.重新登陆未登录账号ToolStripMenuItem_Click);
            // 
            // toolStripSeparator2
            // 
            this.toolStripSeparator2.Name = "toolStripSeparator2";
            this.toolStripSeparator2.Size = new System.Drawing.Size(181, 6);
            // 
            // cmsDeleteAllUser
            // 
            this.cmsDeleteAllUser.Name = "cmsDeleteAllUser";
            this.cmsDeleteAllUser.Size = new System.Drawing.Size(184, 24);
            this.cmsDeleteAllUser.Text = "清空所有账号";
            this.cmsDeleteAllUser.Click += new System.EventHandler(this.btnDeleUser_Click);
            // 
            // tsmRemoveBadUser
            // 
            this.tsmRemoveBadUser.Font = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Bold);
            this.tsmRemoveBadUser.Name = "tsmRemoveBadUser";
            this.tsmRemoveBadUser.Size = new System.Drawing.Size(184, 24);
            this.tsmRemoveBadUser.Text = "移除不能订票账号";
            this.tsmRemoveBadUser.Click += new System.EventHandler(this.tsmRemoveBadUser_Click);
            // 
            // btnPasteUser
            // 
            this.btnPasteUser.Location = new System.Drawing.Point(44, 17);
            this.btnPasteUser.Name = "btnPasteUser";
            this.btnPasteUser.Size = new System.Drawing.Size(37, 23);
            this.btnPasteUser.TabIndex = 116;
            this.btnPasteUser.TabStop = false;
            this.btnPasteUser.Text = "粘贴";
            this.tipTypeInfo.SetToolTip(this.btnPasteUser, "使用方法：\r\n复制文本或其他地方的账号，\r\n点粘贴按钮即可导入。\r\n\r\n粘贴内容格式：\r\n用户名，密码\r\n用户名，密码\r\n用户名，密码\r\n用户名，密码\r\n用户名，" +
        "密码\r\n\r\n");
            this.btnPasteUser.UseVisualStyleBackColor = true;
            this.btnPasteUser.Click += new System.EventHandler(this.btnPasteUser_Click);
            // 
            // btnDeleUser
            // 
            this.btnDeleUser.Location = new System.Drawing.Point(155, 17);
            this.btnDeleUser.Name = "btnDeleUser";
            this.btnDeleUser.Size = new System.Drawing.Size(37, 23);
            this.btnDeleUser.TabIndex = 117;
            this.btnDeleUser.TabStop = false;
            this.btnDeleUser.Text = "删除";
            this.btnDeleUser.UseVisualStyleBackColor = true;
            this.btnDeleUser.Click += new System.EventHandler(this.btnDeleUser_Click);
            // 
            // btnClearAllUser
            // 
            this.btnClearAllUser.Location = new System.Drawing.Point(192, 17);
            this.btnClearAllUser.Name = "btnClearAllUser";
            this.btnClearAllUser.Size = new System.Drawing.Size(37, 23);
            this.btnClearAllUser.TabIndex = 117;
            this.btnClearAllUser.TabStop = false;
            this.btnClearAllUser.Text = "清空";
            this.tipTypeInfo.SetToolTip(this.btnClearAllUser, "删除所有账号并停止对应的任务!");
            this.btnClearAllUser.UseVisualStyleBackColor = true;
            this.btnClearAllUser.Click += new System.EventHandler(this.btnClearAllUser_Click);
            // 
            // btnExportUser
            // 
            this.btnExportUser.Location = new System.Drawing.Point(118, 17);
            this.btnExportUser.Name = "btnExportUser";
            this.btnExportUser.Size = new System.Drawing.Size(37, 23);
            this.btnExportUser.TabIndex = 117;
            this.btnExportUser.TabStop = false;
            this.btnExportUser.Text = "导出";
            this.tipTypeInfo.SetToolTip(this.btnExportUser, "保存账号信息到文本文件，下次可以直接导入。");
            this.btnExportUser.UseVisualStyleBackColor = true;
            this.btnExportUser.Click += new System.EventHandler(this.btnExportUser_Click);
            // 
            // btnAddUser
            // 
            this.btnAddUser.Location = new System.Drawing.Point(7, 17);
            this.btnAddUser.Name = "btnAddUser";
            this.btnAddUser.Size = new System.Drawing.Size(37, 23);
            this.btnAddUser.TabIndex = 117;
            this.btnAddUser.TabStop = false;
            this.btnAddUser.Text = "手工";
            this.tipTypeInfo.SetToolTip(this.btnAddUser, "手工添加一个账号");
            this.btnAddUser.UseVisualStyleBackColor = true;
            this.btnAddUser.Click += new System.EventHandler(this.btnAddUser_Click);
            // 
            // btnImportUser
            // 
            this.btnImportUser.Location = new System.Drawing.Point(81, 17);
            this.btnImportUser.Name = "btnImportUser";
            this.btnImportUser.Size = new System.Drawing.Size(37, 23);
            this.btnImportUser.TabIndex = 115;
            this.btnImportUser.TabStop = false;
            this.btnImportUser.Text = "文本";
            this.tipTypeInfo.SetToolTip(this.btnImportUser, "批量导入文本格式：\r\n用户名，密码\r\n用户名，密码\r\n用户名，密码\r\n用户名，密码\r\n用户名，密码\r\n");
            this.btnImportUser.UseVisualStyleBackColor = true;
            this.btnImportUser.Click += new System.EventHandler(this.btnImportUser_Click);
            // 
            // cmbCodeType
            // 
            this.cmbCodeType.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.cmbCodeType.Font = new System.Drawing.Font("宋体", 10F, System.Drawing.FontStyle.Bold);
            this.cmbCodeType.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(0)))), ((int)(((byte)(0)))), ((int)(((byte)(192)))));
            this.cmbCodeType.FormattingEnabled = true;
            this.cmbCodeType.Items.AddRange(new object[] {
            "自动",
            "手动"});
            this.cmbCodeType.Location = new System.Drawing.Point(465, 2);
            this.cmbCodeType.Name = "cmbCodeType";
            this.cmbCodeType.Size = new System.Drawing.Size(53, 21);
            this.cmbCodeType.TabIndex = 102;
            this.cmbCodeType.TabStop = false;
            this.tipTypeInfo.SetToolTip(this.cmbCodeType, "点击选择验证码输入方式\r\n如果验证码突然升级，请临时切换到“手动”模式！");
            this.cmbCodeType.SelectedIndexChanged += new System.EventHandler(this.cmbCodeType_SelectedIndexChanged);
            // 
            // toolStrip1
            // 
            this.toolStrip1.Dock = System.Windows.Forms.DockStyle.Bottom;
            this.toolStrip1.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.lblCDN,
            this.lblDateTime,
            this.lblAuto});
            this.toolStrip1.Location = new System.Drawing.Point(0, 607);
            this.toolStrip1.Name = "toolStrip1";
            this.toolStrip1.Size = new System.Drawing.Size(974, 25);
            this.toolStrip1.TabIndex = 5;
            this.toolStrip1.Text = "toolStrip1";
            // 
            // lblCDN
            // 
            this.lblCDN.Image = ((System.Drawing.Image)(resources.GetObject("lblCDN.Image")));
            this.lblCDN.ImageTransparentColor = System.Drawing.Color.Magenta;
            this.lblCDN.Name = "lblCDN";
            this.lblCDN.Size = new System.Drawing.Size(23, 22);
            this.lblCDN.ToolTipText = "当前可用CDN数量。\r\n如果CDN数量过少，点击该按钮开始服务器测速！";
            this.lblCDN.Click += new System.EventHandler(this.网络加速QToolStripMenuItem_Click);
            // 
            // lblDateTime
            // 
            this.lblDateTime.AutoToolTip = false;
            this.lblDateTime.Font = new System.Drawing.Font("微软雅黑", 10F, System.Drawing.FontStyle.Bold);
            this.lblDateTime.Image = global::NewTicket.Properties.Resources.timeicon;
            this.lblDateTime.ImageTransparentColor = System.Drawing.Color.Magenta;
            this.lblDateTime.Name = "lblDateTime";
            this.lblDateTime.ShowDropDownArrow = false;
            this.lblDateTime.Size = new System.Drawing.Size(20, 22);
            this.lblDateTime.ToolTipText = "此时间为当前服务器时间。\r\n点击手动同步服务器时间。";
            this.lblDateTime.Click += new System.EventHandler(this.lbDateTime_Click);
            // 
            // lblAuto
            // 
            this.lblAuto.Name = "lblAuto";
            this.lblAuto.Size = new System.Drawing.Size(0, 22);
            // 
            // statusBarPanelCPU
            // 
            this.statusBarPanelCPU.Name = "statusBarPanelCPU";
            this.statusBarPanelCPU.Size = new System.Drawing.Size(0, 22);
            this.statusBarPanelCPU.Visible = false;
            // 
            // pnlInfo
            // 
            this.pnlInfo.Controls.Add(this.spMain);
            this.pnlInfo.Dock = System.Windows.Forms.DockStyle.Fill;
            this.pnlInfo.Location = new System.Drawing.Point(0, 27);
            this.pnlInfo.Name = "pnlInfo";
            this.pnlInfo.Size = new System.Drawing.Size(974, 580);
            this.pnlInfo.TabIndex = 6;
            // 
            // spMain
            // 
            this.spMain.Dock = System.Windows.Forms.DockStyle.Fill;
            this.spMain.ImeMode = System.Windows.Forms.ImeMode.Off;
            this.spMain.Location = new System.Drawing.Point(0, 0);
            this.spMain.Name = "spMain";
            this.spMain.Orientation = System.Windows.Forms.Orientation.Horizontal;
            // 
            // spMain.Panel1
            // 
            this.spMain.Panel1.Controls.Add(this.pnlSet);
            this.spMain.Panel1.RightToLeft = System.Windows.Forms.RightToLeft.No;
            // 
            // spMain.Panel2
            // 
            this.spMain.Panel2.Controls.Add(this.chkRepeatLog);
            this.spMain.Panel2.Controls.Add(this.pnlTongDao);
            this.spMain.Panel2.Controls.Add(this.groupBox8);
            this.spMain.Panel2.RightToLeft = System.Windows.Forms.RightToLeft.No;
            this.spMain.Size = new System.Drawing.Size(974, 580);
            this.spMain.SplitterDistance = 355;
            this.spMain.TabIndex = 139;
            this.spMain.TabStop = false;
            // 
            // chkRepeatLog
            // 
            this.chkRepeatLog.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.chkRepeatLog.AutoSize = true;
            this.chkRepeatLog.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.chkRepeatLog.Location = new System.Drawing.Point(833, 1);
            this.chkRepeatLog.Name = "chkRepeatLog";
            this.chkRepeatLog.Size = new System.Drawing.Size(135, 16);
            this.chkRepeatLog.TabIndex = 108;
            this.chkRepeatLog.TabStop = false;
            this.chkRepeatLog.Text = "屏蔽无效/重复日志";
            this.tipTypeInfo.SetToolTip(this.chkRepeatLog, "\r\n屏蔽查票过程中无效的查票日志，只有查到可以定的票时才会显示。\r\n屏蔽订票过程中的日志，以优化日志显示。\r\n\r\n");
            this.chkRepeatLog.UseVisualStyleBackColor = true;
            this.chkRepeatLog.CheckedChanged += new System.EventHandler(this.chkRepeatLog_CheckedChanged);
            // 
            // pnlTongDao
            // 
            this.pnlTongDao.Anchor = System.Windows.Forms.AnchorStyles.Top;
            this.pnlTongDao.Controls.Add(this.label7);
            this.pnlTongDao.Controls.Add(this.label4);
            this.pnlTongDao.Controls.Add(this.nMaxCodeWait);
            this.pnlTongDao.Controls.Add(this.nOrderSubTimeOut);
            this.pnlTongDao.Controls.Add(this.label1);
            this.pnlTongDao.Controls.Add(this.nBlackHouseTimeOut);
            this.pnlTongDao.Controls.Add(this.chkBlackHouse);
            this.pnlTongDao.Controls.Add(this.cmbQueryThread);
            this.pnlTongDao.Controls.Add(this.cmbCodeType);
            this.pnlTongDao.Controls.Add(this.linkLabel1);
            this.pnlTongDao.Controls.Add(this.label5);
            this.pnlTongDao.Controls.Add(this.lnkDaMa);
            this.pnlTongDao.Controls.Add(this.linkLabel3);
            this.pnlTongDao.Location = new System.Drawing.Point(97, -2);
            this.pnlTongDao.Name = "pnlTongDao";
            this.pnlTongDao.Size = new System.Drawing.Size(625, 24);
            this.pnlTongDao.TabIndex = 129;
            // 
            // label7
            // 
            this.label7.AutoSize = true;
            this.label7.Location = new System.Drawing.Point(389, 6);
            this.label7.Name = "label7";
            this.label7.Size = new System.Drawing.Size(17, 12);
            this.label7.TabIndex = 125;
            this.label7.Text = "秒";
            this.tipTypeInfo.SetToolTip(this.label7, "12306服务器近期压力很大，动不动就崩溃或卡顿。\r\n因此会出现很多在下单时提示未响应，尤其是整点。\r\n如果下单时一直出现未响应，请调整此项。\r\n偶尔一次半次，可" +
        "以忽略。\r\n\r\n默认下单时单步超时为5秒钟，\r\n如果频繁出现未响应，请调大该项。\r\n该项设置【并不是越小越好】！！！\r\n设置的小，在下单时可能会出现未响应。\r\n" +
        "设置的大并不影响其他网络正常账号的出票速度。\r\n");
            // 
            // label4
            // 
            this.label4.AutoSize = true;
            this.label4.Location = new System.Drawing.Point(230, 6);
            this.label4.Name = "label4";
            this.label4.Size = new System.Drawing.Size(29, 12);
            this.label4.TabIndex = 125;
            this.label4.Text = "毫秒";
            // 
            // nMaxCodeWait
            // 
            this.nMaxCodeWait.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle;
            this.nMaxCodeWait.Increment = new decimal(new int[] {
            100,
            0,
            0,
            0});
            this.nMaxCodeWait.Location = new System.Drawing.Point(180, 2);
            this.nMaxCodeWait.Maximum = new decimal(new int[] {
            6000,
            0,
            0,
            0});
            this.nMaxCodeWait.Name = "nMaxCodeWait";
            this.nMaxCodeWait.Size = new System.Drawing.Size(48, 21);
            this.nMaxCodeWait.TabIndex = 126;
            this.nMaxCodeWait.TabStop = false;
            this.tipTypeInfo.SetToolTip(this.nMaxCodeWait, resources.GetString("nMaxCodeWait.ToolTip"));
            this.nMaxCodeWait.Value = new decimal(new int[] {
            4000,
            0,
            0,
            0});
            this.nMaxCodeWait.ValueChanged += new System.EventHandler(this.nMaxCodeWait_ValueChanged);
            // 
            // nOrderSubTimeOut
            // 
            this.nOrderSubTimeOut.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle;
            this.nOrderSubTimeOut.Location = new System.Drawing.Point(347, 2);
            this.nOrderSubTimeOut.Maximum = new decimal(new int[] {
            10,
            0,
            0,
            0});
            this.nOrderSubTimeOut.Minimum = new decimal(new int[] {
            3,
            0,
            0,
            0});
            this.nOrderSubTimeOut.Name = "nOrderSubTimeOut";
            this.nOrderSubTimeOut.Size = new System.Drawing.Size(36, 21);
            this.nOrderSubTimeOut.TabIndex = 126;
            this.nOrderSubTimeOut.TabStop = false;
            this.tipTypeInfo.SetToolTip(this.nOrderSubTimeOut, "12306服务器近期压力很大，动不动就崩溃或卡顿。\r\n因此会出现很多在下单时提示未响应，尤其是整点。\r\n如果下单时一直出现未响应，请调整此项。\r\n偶尔一次半次，可" +
        "以忽略。\r\n\r\n默认下单时单步超时为5秒钟，\r\n如果频繁出现未响应，请调大该项。\r\n该项设置【并不是越小越好】！！！\r\n设置的小，在下单时可能会出现未响应。\r\n" +
        "设置的大并不影响其他网络正常账号的出票速度。");
            this.nOrderSubTimeOut.Value = new decimal(new int[] {
            5,
            0,
            0,
            0});
            this.nOrderSubTimeOut.ValueChanged += new System.EventHandler(this.nOrderSubTimeOut_ValueChanged);
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Location = new System.Drawing.Point(109, 6);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(17, 12);
            this.label1.TabIndex = 122;
            this.label1.Text = "秒";
            // 
            // nBlackHouseTimeOut
            // 
            this.nBlackHouseTimeOut.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle;
            this.nBlackHouseTimeOut.Increment = new decimal(new int[] {
            10,
            0,
            0,
            0});
            this.nBlackHouseTimeOut.Location = new System.Drawing.Point(63, 2);
            this.nBlackHouseTimeOut.Maximum = new decimal(new int[] {
            300,
            0,
            0,
            0});
            this.nBlackHouseTimeOut.Minimum = new decimal(new int[] {
            5,
            0,
            0,
            0});
            this.nBlackHouseTimeOut.Name = "nBlackHouseTimeOut";
            this.nBlackHouseTimeOut.Size = new System.Drawing.Size(44, 21);
            this.nBlackHouseTimeOut.TabIndex = 123;
            this.nBlackHouseTimeOut.TabStop = false;
            this.tipTypeInfo.SetToolTip(this.nBlackHouseTimeOut, "默认值30，最大值300。");
            this.nBlackHouseTimeOut.Value = new decimal(new int[] {
            30,
            0,
            0,
            0});
            this.nBlackHouseTimeOut.ValueChanged += new System.EventHandler(this.nBlackHouseTimeOut_ValueChanged);
            // 
            // chkBlackHouse
            // 
            this.chkBlackHouse.AutoSize = true;
            this.chkBlackHouse.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.chkBlackHouse.ForeColor = System.Drawing.Color.Red;
            this.chkBlackHouse.Location = new System.Drawing.Point(3, 4);
            this.chkBlackHouse.Name = "chkBlackHouse";
            this.chkBlackHouse.Size = new System.Drawing.Size(63, 16);
            this.chkBlackHouse.TabIndex = 124;
            this.chkBlackHouse.TabStop = false;
            this.chkBlackHouse.Text = "小黑屋";
            this.tipTypeInfo.SetToolTip(this.chkBlackHouse, "查票小黑屋\r\n\r\n启用该功能后：\r\n查到无效票或者订票时余票不足，\r\n自动关进小黑屋，避免重复提交！");
            this.chkBlackHouse.UseVisualStyleBackColor = true;
            this.chkBlackHouse.CheckedChanged += new System.EventHandler(this.chkBlackHouse_CheckedChanged);
            // 
            // cmbQueryThread
            // 
            this.cmbQueryThread.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.cmbQueryThread.Font = new System.Drawing.Font("宋体", 10F, System.Drawing.FontStyle.Bold);
            this.cmbQueryThread.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(0)))), ((int)(((byte)(0)))), ((int)(((byte)(192)))));
            this.cmbQueryThread.FormattingEnabled = true;
            this.cmbQueryThread.Items.AddRange(new object[] {
            "极速",
            "高速",
            "稳定"});
            this.cmbQueryThread.Location = new System.Drawing.Point(563, 2);
            this.cmbQueryThread.Name = "cmbQueryThread";
            this.cmbQueryThread.Size = new System.Drawing.Size(53, 21);
            this.cmbQueryThread.TabIndex = 121;
            this.cmbQueryThread.TabStop = false;
            this.tipTypeInfo.SetToolTip(this.cmbQueryThread, "【助手运行性能参数】\r\n如果助手运行比较卡顿，请调整此参数！\r\n\r\n极速：火力全开，要求电脑性能很强或者窗口账号很少。\r\n高速：速度中等，电脑性能稍强，适合开多账" +
        "号刷票。\r\n稳定：速度正常，电脑性能一般");
            this.cmbQueryThread.SelectedIndexChanged += new System.EventHandler(this.cmbQueryThread_SelectedIndexChanged);
            // 
            // linkLabel1
            // 
            this.linkLabel1.AutoSize = true;
            this.linkLabel1.Font = new System.Drawing.Font("宋体", 10F, System.Drawing.FontStyle.Bold);
            this.linkLabel1.LinkBehavior = System.Windows.Forms.LinkBehavior.HoverUnderline;
            this.linkLabel1.LinkColor = System.Drawing.Color.Red;
            this.linkLabel1.Location = new System.Drawing.Point(136, 4);
            this.linkLabel1.Name = "linkLabel1";
            this.linkLabel1.Size = new System.Drawing.Size(45, 14);
            this.linkLabel1.TabIndex = 11;
            this.linkLabel1.TabStop = true;
            this.linkLabel1.Tag = "2";
            this.linkLabel1.Text = "延时:";
            this.tipTypeInfo.SetToolTip(this.linkLabel1, resources.GetString("linkLabel1.ToolTip"));
            // 
            // label5
            // 
            this.label5.AutoSize = true;
            this.label5.Font = new System.Drawing.Font("宋体", 10F, System.Drawing.FontStyle.Bold);
            this.label5.ForeColor = System.Drawing.Color.Blue;
            this.label5.Location = new System.Drawing.Point(523, 5);
            this.label5.Name = "label5";
            this.label5.Size = new System.Drawing.Size(45, 14);
            this.label5.TabIndex = 122;
            this.label5.Text = "性能:";
            this.tipTypeInfo.SetToolTip(this.label5, "【助手运行性能参数】\r\n如果助手运行比较卡顿，请调整此参数！\r\n\r\n极速：火力全开，要求电脑性能很强或者窗口账号很少。\r\n高速：速度中等，电脑性能稍强，适合开多账" +
        "号刷票。\r\n稳定：速度正常，电脑性能一般\r\n");
            // 
            // lnkDaMa
            // 
            this.lnkDaMa.AutoSize = true;
            this.lnkDaMa.Font = new System.Drawing.Font("宋体", 10F, System.Drawing.FontStyle.Bold);
            this.lnkDaMa.LinkBehavior = System.Windows.Forms.LinkBehavior.HoverUnderline;
            this.lnkDaMa.LinkColor = System.Drawing.Color.Red;
            this.lnkDaMa.Location = new System.Drawing.Point(409, 6);
            this.lnkDaMa.Name = "lnkDaMa";
            this.lnkDaMa.Size = new System.Drawing.Size(60, 14);
            this.lnkDaMa.TabIndex = 11;
            this.lnkDaMa.TabStop = true;
            this.lnkDaMa.Tag = "2";
            this.lnkDaMa.Text = "验证码:";
            this.tipTypeInfo.SetToolTip(this.lnkDaMa, "点此修改第三方打码设置！");
            this.lnkDaMa.Click += new System.EventHandler(this.打码平台ToolStripMenuItem_Click);
            // 
            // linkLabel3
            // 
            this.linkLabel3.AutoSize = true;
            this.linkLabel3.Font = new System.Drawing.Font("宋体", 10F, System.Drawing.FontStyle.Bold);
            this.linkLabel3.LinkBehavior = System.Windows.Forms.LinkBehavior.HoverUnderline;
            this.linkLabel3.LinkColor = System.Drawing.Color.Red;
            this.linkLabel3.Location = new System.Drawing.Point(259, 4);
            this.linkLabel3.Name = "linkLabel3";
            this.linkLabel3.Size = new System.Drawing.Size(90, 14);
            this.linkLabel3.TabIndex = 11;
            this.linkLabel3.TabStop = true;
            this.linkLabel3.Tag = "2";
            this.linkLabel3.Text = "下单未响应:";
            this.tipTypeInfo.SetToolTip(this.linkLabel3, "12306服务器近期压力很大，动不动就崩溃或卡顿。\r\n因此会出现很多在下单时提示未响应，尤其是整点。\r\n如果下单时一直出现未响应，请调整此项。\r\n偶尔一次半次，可" +
        "以忽略。\r\n\r\n默认下单时单步超时为5秒钟，\r\n如果频繁出现未响应，请调大该项。\r\n该项设置【并不是越小越好】！！！\r\n设置的小，在下单时可能会出现未响应。\r\n" +
        "设置的大并不影响其他网络正常账号的出票速度。\r\n");
            // 
            // groupBox8
            // 
            this.groupBox8.Controls.Add(this.rtbNewLog);
            this.groupBox8.Controls.Add(this.dvLog);
            this.groupBox8.Controls.Add(this.chkNoLog);
            this.groupBox8.Dock = System.Windows.Forms.DockStyle.Fill;
            this.groupBox8.Location = new System.Drawing.Point(0, 0);
            this.groupBox8.Name = "groupBox8";
            this.groupBox8.Size = new System.Drawing.Size(974, 221);
            this.groupBox8.TabIndex = 28;
            this.groupBox8.TabStop = false;
            this.groupBox8.Text = "日志显示";
            // 
            // rtbNewLog
            // 
            this.rtbNewLog.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.rtbNewLog.BackColor = System.Drawing.Color.Linen;
            this.rtbNewLog.BorderStyle = System.Windows.Forms.BorderStyle.None;
            this.rtbNewLog.ContextMenuStrip = this.cmsLog;
            this.rtbNewLog.Cursor = System.Windows.Forms.Cursors.IBeam;
            this.rtbNewLog.Font = new System.Drawing.Font("宋体", 9.5F);
            this.rtbNewLog.Location = new System.Drawing.Point(3, 43);
            this.rtbNewLog.Name = "rtbNewLog";
            this.rtbNewLog.ReadOnly = true;
            this.rtbNewLog.ShortcutsEnabled = false;
            this.rtbNewLog.Size = new System.Drawing.Size(967, 174);
            this.rtbNewLog.TabIndex = 109;
            this.rtbNewLog.TabStop = false;
            this.rtbNewLog.Text = "";
            this.rtbNewLog.WordWrap = false;
            // 
            // cmsLog
            // 
            this.cmsLog.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.清空日志ToolStripMenuItem,
            this.复制日志ToolStripMenuItem,
            this.保存日志ToolStripMenuItem});
            this.cmsLog.Name = "cmsLog";
            this.cmsLog.Size = new System.Drawing.Size(125, 70);
            // 
            // 清空日志ToolStripMenuItem
            // 
            this.清空日志ToolStripMenuItem.Name = "清空日志ToolStripMenuItem";
            this.清空日志ToolStripMenuItem.Size = new System.Drawing.Size(124, 22);
            this.清空日志ToolStripMenuItem.Text = "清空日志";
            this.清空日志ToolStripMenuItem.Click += new System.EventHandler(this.btnClearLog_Click);
            // 
            // 复制日志ToolStripMenuItem
            // 
            this.复制日志ToolStripMenuItem.Name = "复制日志ToolStripMenuItem";
            this.复制日志ToolStripMenuItem.Size = new System.Drawing.Size(124, 22);
            this.复制日志ToolStripMenuItem.Text = "复制日志";
            this.复制日志ToolStripMenuItem.Click += new System.EventHandler(this.btnCopy_Click);
            // 
            // 保存日志ToolStripMenuItem
            // 
            this.保存日志ToolStripMenuItem.Name = "保存日志ToolStripMenuItem";
            this.保存日志ToolStripMenuItem.Size = new System.Drawing.Size(124, 22);
            this.保存日志ToolStripMenuItem.Text = "导出日志";
            this.保存日志ToolStripMenuItem.Click += new System.EventHandler(this.btnSaveLog_Click);
            // 
            // dvLog
            // 
            this.dvLog.AllowUserToAddRows = false;
            this.dvLog.AllowUserToDeleteRows = false;
            this.dvLog.AllowUserToResizeColumns = false;
            this.dvLog.AllowUserToResizeRows = false;
            this.dvLog.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.dvLog.BackgroundColor = System.Drawing.Color.White;
            this.dvLog.ClipboardCopyMode = System.Windows.Forms.DataGridViewClipboardCopyMode.EnableAlwaysIncludeHeaderText;
            dataGridViewCellStyle9.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleLeft;
            dataGridViewCellStyle9.BackColor = System.Drawing.SystemColors.Control;
            dataGridViewCellStyle9.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            dataGridViewCellStyle9.ForeColor = System.Drawing.SystemColors.WindowText;
            dataGridViewCellStyle9.SelectionBackColor = System.Drawing.SystemColors.Highlight;
            dataGridViewCellStyle9.SelectionForeColor = System.Drawing.SystemColors.HighlightText;
            dataGridViewCellStyle9.WrapMode = System.Windows.Forms.DataGridViewTriState.True;
            this.dvLog.ColumnHeadersDefaultCellStyle = dataGridViewCellStyle9;
            this.dvLog.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.AutoSize;
            this.dvLog.Columns.AddRange(new System.Windows.Forms.DataGridViewColumn[] {
            this.dataGridViewTextBoxColumn4,
            this.dataGridViewTextBoxColumn6,
            this.dataGridViewTextBoxColumn7});
            dataGridViewCellStyle31.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleLeft;
            dataGridViewCellStyle31.BackColor = System.Drawing.SystemColors.Window;
            dataGridViewCellStyle31.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            dataGridViewCellStyle31.ForeColor = System.Drawing.SystemColors.ControlText;
            dataGridViewCellStyle31.SelectionBackColor = System.Drawing.SystemColors.Highlight;
            dataGridViewCellStyle31.SelectionForeColor = System.Drawing.SystemColors.HighlightText;
            dataGridViewCellStyle31.WrapMode = System.Windows.Forms.DataGridViewTriState.False;
            this.dvLog.DefaultCellStyle = dataGridViewCellStyle31;
            this.dvLog.Location = new System.Drawing.Point(2, 23);
            this.dvLog.Name = "dvLog";
            this.dvLog.ReadOnly = true;
            dataGridViewCellStyle32.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleLeft;
            dataGridViewCellStyle32.BackColor = System.Drawing.SystemColors.Control;
            dataGridViewCellStyle32.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            dataGridViewCellStyle32.ForeColor = System.Drawing.SystemColors.WindowText;
            dataGridViewCellStyle32.SelectionBackColor = System.Drawing.SystemColors.Highlight;
            dataGridViewCellStyle32.SelectionForeColor = System.Drawing.SystemColors.HighlightText;
            dataGridViewCellStyle32.WrapMode = System.Windows.Forms.DataGridViewTriState.True;
            this.dvLog.RowHeadersDefaultCellStyle = dataGridViewCellStyle32;
            this.dvLog.RowHeadersVisible = false;
            this.dvLog.RowTemplate.Height = 25;
            this.dvLog.SelectionMode = System.Windows.Forms.DataGridViewSelectionMode.FullRowSelect;
            this.dvLog.ShowEditingIcon = false;
            this.dvLog.Size = new System.Drawing.Size(971, 195);
            this.dvLog.TabIndex = 28;
            this.dvLog.TabStop = false;
            // 
            // dataGridViewTextBoxColumn4
            // 
            this.dataGridViewTextBoxColumn4.DataPropertyName = "Date";
            dataGridViewCellStyle28.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleLeft;
            this.dataGridViewTextBoxColumn4.DefaultCellStyle = dataGridViewCellStyle28;
            this.dataGridViewTextBoxColumn4.HeaderText = "时间";
            this.dataGridViewTextBoxColumn4.Name = "dataGridViewTextBoxColumn4";
            this.dataGridViewTextBoxColumn4.ReadOnly = true;
            this.dataGridViewTextBoxColumn4.Width = 84;
            // 
            // dataGridViewTextBoxColumn6
            // 
            this.dataGridViewTextBoxColumn6.DataPropertyName = "NType";
            dataGridViewCellStyle29.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleLeft;
            dataGridViewCellStyle29.Format = "MM月dd日";
            dataGridViewCellStyle29.NullValue = null;
            this.dataGridViewTextBoxColumn6.DefaultCellStyle = dataGridViewCellStyle29;
            this.dataGridViewTextBoxColumn6.HeaderText = "来源";
            this.dataGridViewTextBoxColumn6.Name = "dataGridViewTextBoxColumn6";
            this.dataGridViewTextBoxColumn6.ReadOnly = true;
            this.dataGridViewTextBoxColumn6.Width = 64;
            // 
            // dataGridViewTextBoxColumn7
            // 
            this.dataGridViewTextBoxColumn7.AutoSizeMode = System.Windows.Forms.DataGridViewAutoSizeColumnMode.Fill;
            this.dataGridViewTextBoxColumn7.DataPropertyName = "NCardType";
            dataGridViewCellStyle30.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleLeft;
            this.dataGridViewTextBoxColumn7.DefaultCellStyle = dataGridViewCellStyle30;
            this.dataGridViewTextBoxColumn7.HeaderText = "日志内容";
            this.dataGridViewTextBoxColumn7.Name = "dataGridViewTextBoxColumn7";
            this.dataGridViewTextBoxColumn7.ReadOnly = true;
            this.dataGridViewTextBoxColumn7.SortMode = System.Windows.Forms.DataGridViewColumnSortMode.NotSortable;
            // 
            // chkNoLog
            // 
            this.chkNoLog.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.chkNoLog.AutoSize = true;
            this.chkNoLog.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.chkNoLog.Location = new System.Drawing.Point(725, 1);
            this.chkNoLog.Name = "chkNoLog";
            this.chkNoLog.Size = new System.Drawing.Size(102, 16);
            this.chkNoLog.TabIndex = 108;
            this.chkNoLog.TabStop = false;
            this.chkNoLog.Text = "屏蔽所有日志";
            this.chkNoLog.UseVisualStyleBackColor = true;
            this.chkNoLog.CheckedChanged += new System.EventHandler(this.chkNoLog_CheckedChanged);
            // 
            // tipTypeInfo
            // 
            this.tipTypeInfo.AutoPopDelay = 30000;
            this.tipTypeInfo.BackColor = System.Drawing.SystemColors.HighlightText;
            this.tipTypeInfo.InitialDelay = 500;
            this.tipTypeInfo.IsBalloon = true;
            this.tipTypeInfo.ReshowDelay = 100;
            this.tipTypeInfo.ToolTipIcon = System.Windows.Forms.ToolTipIcon.Info;
            this.tipTypeInfo.ToolTipTitle = "订票帮助";
            // 
            // statusStrip2
            // 
            this.statusStrip2.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.statusStrip2.Dock = System.Windows.Forms.DockStyle.None;
            this.statusStrip2.GripStyle = System.Windows.Forms.ToolStripGripStyle.Visible;
            this.statusStrip2.ImageScalingSize = new System.Drawing.Size(20, 20);
            this.statusStrip2.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.lblVersion,
            this.lblUpdate});
            this.statusStrip2.Location = new System.Drawing.Point(803, 606);
            this.statusStrip2.Name = "statusStrip2";
            this.statusStrip2.ShowItemToolTips = true;
            this.statusStrip2.Size = new System.Drawing.Size(170, 26);
            this.statusStrip2.TabIndex = 125;
            this.statusStrip2.Text = "statusStrip2";
            // 
            // lblVersion
            // 
            this.lblVersion.Image = ((System.Drawing.Image)(resources.GetObject("lblVersion.Image")));
            this.lblVersion.ImageTransparentColor = System.Drawing.Color.Magenta;
            this.lblVersion.Name = "lblVersion";
            this.lblVersion.ShowDropDownArrow = false;
            this.lblVersion.Size = new System.Drawing.Size(64, 24);
            this.lblVersion.Text = "类型:-";
            this.lblVersion.Click += new System.EventHandler(this.lblNowCount_Click);
            // 
            // lblUpdate
            // 
            this.lblUpdate.Image = ((System.Drawing.Image)(resources.GetObject("lblUpdate.Image")));
            this.lblUpdate.ImageTransparentColor = System.Drawing.Color.Magenta;
            this.lblUpdate.Name = "lblUpdate";
            this.lblUpdate.ShowDropDownArrow = false;
            this.lblUpdate.Size = new System.Drawing.Size(80, 24);
            this.lblUpdate.Text = "检查更新";
            this.lblUpdate.ToolTipText = "点击检查服务端是否有最新版本。";
            this.lblUpdate.Click += new System.EventHandler(this.lblUpdate_Click);
            // 
            // imgIcon
            // 
            this.imgIcon.ImageStream = ((System.Windows.Forms.ImageListStreamer)(resources.GetObject("imgIcon.ImageStream")));
            this.imgIcon.TransparentColor = System.Drawing.Color.Transparent;
            this.imgIcon.Images.SetKeyName(0, "0.png");
            this.imgIcon.Images.SetKeyName(1, "1.png");
            this.imgIcon.Images.SetKeyName(2, "2.png");
            this.imgIcon.Images.SetKeyName(3, "3.png");
            // 
            // open
            // 
            this.open.Filter = "TXT文件|*.txt";
            this.open.Title = "打开文件";
            // 
            // save
            // 
            this.save.Filter = "TXT文件|*.txt";
            this.save.Title = "选择保存位置";
            // 
            // bgMSG
            // 
            this.bgMSG.WorkerSupportsCancellation = true;
            this.bgMSG.DoWork += new System.ComponentModel.DoWorkEventHandler(this.bgMSG_DoWork);
            // 
            // bgSaveInfo
            // 
            this.bgSaveInfo.WorkerSupportsCancellation = true;
            this.bgSaveInfo.DoWork += new System.ComponentModel.DoWorkEventHandler(this.bgSaveInfo_DoWork);
            // 
            // bgPublic
            // 
            this.bgPublic.WorkerSupportsCancellation = true;
            this.bgPublic.DoWork += new System.ComponentModel.DoWorkEventHandler(this.bgPublic_DoWork);
            // 
            // cmsTicket
            // 
            this.cmsTicket.Name = "cmsDate";
            this.cmsTicket.Size = new System.Drawing.Size(61, 4);
            // 
            // cmsPassenger
            // 
            this.cmsPassenger.Name = "cmsDate";
            this.cmsPassenger.Size = new System.Drawing.Size(61, 4);
            // 
            // bgDeleAllPass
            // 
            this.bgDeleAllPass.WorkerSupportsCancellation = true;
            this.bgDeleAllPass.DoWork += new System.ComponentModel.DoWorkEventHandler(this.bgDeleAllPass_DoWork);
            this.bgDeleAllPass.RunWorkerCompleted += new System.ComponentModel.RunWorkerCompletedEventHandler(this.bgDeleAllPass_RunWorkerCompleted);
            // 
            // mobileImg
            // 
            this.mobileImg.ImageStream = ((System.Windows.Forms.ImageListStreamer)(resources.GetObject("mobileImg.ImageStream")));
            this.mobileImg.TransparentColor = System.Drawing.Color.Transparent;
            this.mobileImg.Images.SetKeyName(0, "0002.png");
            this.mobileImg.Images.SetKeyName(1, "002.png");
            this.mobileImg.Images.SetKeyName(2, "0001.png");
            // 
            // dataGridViewImageColumn1
            // 
            this.dataGridViewImageColumn1.HeaderText = "";
            this.dataGridViewImageColumn1.Image = ((System.Drawing.Image)(resources.GetObject("dataGridViewImageColumn1.Image")));
            this.dataGridViewImageColumn1.Name = "dataGridViewImageColumn1";
            this.dataGridViewImageColumn1.ReadOnly = true;
            this.dataGridViewImageColumn1.Width = 20;
            // 
            // dataGridViewImageColumn2
            // 
            this.dataGridViewImageColumn2.HeaderText = "";
            this.dataGridViewImageColumn2.Image = ((System.Drawing.Image)(resources.GetObject("dataGridViewImageColumn2.Image")));
            this.dataGridViewImageColumn2.Name = "dataGridViewImageColumn2";
            this.dataGridViewImageColumn2.ReadOnly = true;
            this.dataGridViewImageColumn2.Width = 20;
            // 
            // toolStripMenuItem3
            // 
            this.toolStripMenuItem3.Name = "toolStripMenuItem3";
            this.toolStripMenuItem3.Size = new System.Drawing.Size(208, 22);
            this.toolStripMenuItem3.Text = "粘贴乘客到勾选任务";
            this.toolStripMenuItem3.Click += new System.EventHandler(this.toolStripMenuItem3_Click);
            // 
            // toolStripMenuItem4
            // 
            this.toolStripMenuItem4.Name = "toolStripMenuItem4";
            this.toolStripMenuItem4.Size = new System.Drawing.Size(208, 22);
            this.toolStripMenuItem4.Text = "保存勾选任务乘客到官网";
            this.toolStripMenuItem4.Click += new System.EventHandler(this.toolStripMenuItem4_Click);
            // 
            // toolStripMenuItem5
            // 
            this.toolStripMenuItem5.Font = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.toolStripMenuItem5.Name = "toolStripMenuItem5";
            this.toolStripMenuItem5.Size = new System.Drawing.Size(208, 22);
            this.toolStripMenuItem5.Text = "校验勾选任务订票乘客";
            this.toolStripMenuItem5.Click += new System.EventHandler(this.toolStripMenuItem5_Click);
            // 
            // toolStripMenuItem6
            // 
            this.toolStripMenuItem6.Font = new System.Drawing.Font("Microsoft YaHei UI", 9F);
            this.toolStripMenuItem6.Name = "toolStripMenuItem6";
            this.toolStripMenuItem6.Size = new System.Drawing.Size(208, 22);
            this.toolStripMenuItem6.Text = "取消勾选任务排队";
            this.toolStripMenuItem6.Click += new System.EventHandler(this.toolStripMenuItem6_Click);
            // 
            // toolStripMenuItem7
            // 
            this.toolStripMenuItem7.Name = "toolStripMenuItem7";
            this.toolStripMenuItem7.Size = new System.Drawing.Size(208, 22);
            this.toolStripMenuItem7.Text = "立即支付";
            this.toolStripMenuItem7.Visible = false;
            this.toolStripMenuItem7.Click += new System.EventHandler(this.toolStripMenuItem7_Click);
            // 
            // srcTxt
            // 
            this.srcTxt.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.srcTxt.BorderColor = System.Drawing.Color.Black;
            this.srcTxt.Cursor = System.Windows.Forms.Cursors.Default;
            this.srcTxt.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.srcTxt.ForegroundBrush = null;
            this.srcTxt.Location = new System.Drawing.Point(458, 0);
            this.srcTxt.Name = "srcTxt";
            this.srcTxt.ScrollDirection = NewTicket.MyControl.ScrollDirection.RightToLeft;
            this.srcTxt.ScrollText = "欢迎使用助手抢票软件!";
            this.srcTxt.ShowBorder = false;
            this.srcTxt.Size = new System.Drawing.Size(515, 27);
            this.srcTxt.StopScrollOnMouseOver = true;
            this.srcTxt.StrLink = "";
            this.srcTxt.TabIndex = 127;
            this.srcTxt.TextScrollDistance = 3;
            this.srcTxt.TextScrollSpeed = 40;
            this.srcTxt.VerticleTextPosition = NewTicket.MyControl.VerticleTextPosition.Botom;
            // 
            // cmbDDL
            // 
            this.cmbDDL.DropDownHeigth = 155;
            this.cmbDDL.ImeMode = System.Windows.Forms.ImeMode.Disable;
            this.cmbDDL.Location = new System.Drawing.Point(6, 167);
            this.cmbDDL.Name = "cmbDDL";
            this.cmbDDL.ReadOnly = true;
            this.cmbDDL.Size = new System.Drawing.Size(91, 23);
            this.cmbDDL.TabIndex = 137;
            this.cmbDDL.TabStop = false;
            this.cmbDDL.TreeImageList = null;
            this.cmbDDL.Visible = false;
            this.cmbDDL.DropDownHided += new System.EventHandler(this.cmbDDL_DropDownHided);
            // 
            // btnSave
            // 
            this.btnSave.Font = new System.Drawing.Font("微软雅黑", 10F, System.Drawing.FontStyle.Bold);
            this.btnSave.Location = new System.Drawing.Point(370, 9);
            this.btnSave.Name = "btnSave";
            this.btnSave.Size = new System.Drawing.Size(47, 34);
            this.btnSave.TabIndex = 139;
            this.btnSave.TabStop = false;
            this.btnSave.Text = "保存";
            this.tipTypeInfo.SetToolTip(this.btnSave, "保存当前任务");
            this.btnSave.UseVisualStyleBackColor = true;
            this.btnSave.Click += new System.EventHandler(this.btnSave_Click);
            // 
            // NewFormMain
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(974, 632);
            this.Controls.Add(this.statusStrip2);
            this.Controls.Add(this.srcTxt);
            this.Controls.Add(this.pnlInfo);
            this.Controls.Add(this.menuStrip1);
            this.Controls.Add(this.toolStrip1);
            this.Icon = ((System.Drawing.Icon)(resources.GetObject("$this.Icon")));
            this.MainMenuStrip = this.menuStrip1;
            this.Name = "NewFormMain";
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen;
            this.FormClosing += new System.Windows.Forms.FormClosingEventHandler(this.FormMain_FormClosing);
            this.Shown += new System.EventHandler(this.FormMain_Shown);
            this.menuStrip1.ResumeLayout(false);
            this.menuStrip1.PerformLayout();
            this.pnlSet.ResumeLayout(false);
            this.grpTask.ResumeLayout(false);
            this.grpTask.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.nMaxOrderTime)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.dvTasks)).EndInit();
            this.cmsTask.ResumeLayout(false);
            this.grpUser.ResumeLayout(false);
            this.pnlAdd.ResumeLayout(false);
            this.pnlAdd.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.dvUsers)).EndInit();
            this.cmsUser.ResumeLayout(false);
            this.toolStrip1.ResumeLayout(false);
            this.toolStrip1.PerformLayout();
            this.pnlInfo.ResumeLayout(false);
            this.spMain.Panel1.ResumeLayout(false);
            this.spMain.Panel2.ResumeLayout(false);
            this.spMain.Panel2.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.spMain)).EndInit();
            this.spMain.ResumeLayout(false);
            this.pnlTongDao.ResumeLayout(false);
            this.pnlTongDao.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.nMaxCodeWait)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.nOrderSubTimeOut)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.nBlackHouseTimeOut)).EndInit();
            this.groupBox8.ResumeLayout(false);
            this.groupBox8.PerformLayout();
            this.cmsLog.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.dvLog)).EndInit();
            this.statusStrip2.ResumeLayout(false);
            this.statusStrip2.PerformLayout();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private System.Windows.Forms.MenuStrip menuStrip1;
        private System.Windows.Forms.Panel pnlSet;
        private System.Windows.Forms.ToolStrip toolStrip1;
        private System.Windows.Forms.Panel pnlInfo;
        private System.Windows.Forms.ToolTip tipTypeInfo;
        private System.Windows.Forms.DataGridView dvTasks;
        private System.Windows.Forms.GroupBox grpUser;
        private System.Windows.Forms.DataGridView dvLog;
        private System.Windows.Forms.ToolStripDropDownButton lblDateTime;
        private System.Windows.Forms.StatusStrip statusStrip2;
        private System.Windows.Forms.ToolStripDropDownButton lblUpdate;
        private System.Windows.Forms.ToolStripDropDownButton lblVersion;
        private System.Windows.Forms.GroupBox groupBox8;
        private System.Windows.Forms.ToolStripMenuItem cmsUserManager;
        private System.Windows.Forms.ImageList imgIcon;
        private System.Windows.Forms.OpenFileDialog open;
        private System.Windows.Forms.SaveFileDialog save;
        private System.Windows.Forms.ComboBox cmbCodeType;
        private System.Windows.Forms.ToolStripMenuItem 订票提醒ToolStripMenuItem;
        private ScrollingText srcTxt;
        private System.ComponentModel.BackgroundWorker bgMSG;
        private System.Windows.Forms.ToolStripButton lblCDN;
        private System.ComponentModel.BackgroundWorker bgSaveInfo;
        private System.Windows.Forms.ToolStripLabel statusBarPanelCPU;
        private System.Windows.Forms.DataGridViewTextBoxColumn dataGridViewTextBoxColumn4;
        private System.Windows.Forms.DataGridViewTextBoxColumn dataGridViewTextBoxColumn6;
        private System.Windows.Forms.DataGridViewTextBoxColumn dataGridViewTextBoxColumn7;
        private System.Windows.Forms.ToolStripLabel lblAuto;
        private System.Windows.Forms.CheckBox chkRepeatLog;
        private System.Windows.Forms.ToolStripMenuItem 其他ToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem 机器码ToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem 联系客服ToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem 意见反馈ToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem 封IPToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem iPToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem 本地IP解封ToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem 网络加速QToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem 打码平台ToolStripMenuItem;
        private System.Windows.Forms.ComboBox cmsOpType;
        private System.Windows.Forms.Label label20;
        private System.Windows.Forms.Panel pnlTongDao;
        private System.Windows.Forms.LinkLabel lnkDaMa;
        private System.Windows.Forms.Button btnPasteUser;
        private System.Windows.Forms.Button btnAddUser;
        private System.Windows.Forms.Button btnImportUser;
        private System.Windows.Forms.Button btnClearAllUser;
        private System.Windows.Forms.Button btnExportUser;
        private System.Windows.Forms.GroupBox grpTask;
        private System.Windows.Forms.Button btnDeleTask;
        private System.Windows.Forms.Button btnNewTask;
        private System.Windows.Forms.Button btnExportTask;
        private System.Windows.Forms.Button btnCopyTask;
        private System.Windows.Forms.Button btnImpotTask;
        private System.Windows.Forms.Button btnStopAll;
        private System.Windows.Forms.Button btnStartAll;
        private System.Windows.Forms.Button btnStopTask;
        private System.Windows.Forms.Button btnClearAllTask;
        private System.Windows.Forms.Button btnStartTask;
        private System.Windows.Forms.Button btnEditTask;
        private System.Windows.Forms.ToolStripMenuItem toolStripMenuItem1;
        private System.Windows.Forms.DataGridView dvUsers;
        private System.Windows.Forms.Panel pnlAdd;
        private System.Windows.Forms.TextBox txtPWD;
        private System.Windows.Forms.TextBox txtUserName;
        private System.Windows.Forms.Label lblStatus;
        private System.Windows.Forms.Button btnCancel;
        private System.Windows.Forms.Button btnOK;
        private System.Windows.Forms.Label label2;
        private System.Windows.Forms.Label label3;
        private System.Windows.Forms.Button btnDeleUser;
        private System.ComponentModel.BackgroundWorker bgPublic;
        private System.Windows.Forms.ContextMenuStrip cmsUser;
        private System.Windows.Forms.ToolStripMenuItem cmsAddUser;
        private System.Windows.Forms.ToolStripMenuItem cmsPasteUser;
        private System.Windows.Forms.ToolStripMenuItem cmsEditUser;
        private System.Windows.Forms.ToolStripSeparator toolStripSeparator1;
        private System.Windows.Forms.ToolStripMenuItem cmsDelteUser;
        private System.Windows.Forms.ToolStripMenuItem cmsDeleteAllUser;
        private System.Windows.Forms.ToolStripMenuItem cmsRelogin;
        private System.Windows.Forms.ToolStripSeparator toolStripSeparator2;
        private System.Windows.Forms.ContextMenuStrip cmsTask;
        private System.Windows.Forms.ToolStripMenuItem tsmStart;
        private System.Windows.Forms.ToolStripMenuItem tsmStop;
        private System.Windows.Forms.ToolStripSeparator toolStripSeparator3;
        private System.Windows.Forms.ToolStripSeparator toolStripSeparator5;
        private System.Windows.Forms.ToolStripMenuItem tsmQueryOrder;
        private System.Windows.Forms.ToolStripMenuItem tsmOpenIE;
        private System.Windows.Forms.ToolStripMenuItem tsmCancelOrder;
        private System.Windows.Forms.ToolStripMenuItem cmsImportUser;
        private System.Windows.Forms.Button btnRefreshTask;
        private System.Windows.Forms.ContextMenuStrip cmsTicket;
        private System.Windows.Forms.ContextMenuStrip cmsPassenger;
        private CheckCombo cmbDDL;
        private System.Windows.Forms.CheckBox chkTaskSele;
        private System.Windows.Forms.ToolStripMenuItem cmsExportUser;
        private System.Windows.Forms.ToolStripMenuItem tsmCheckPassenger;
        private System.Windows.Forms.ToolStripMenuItem tsmCopyThisOrder;
        private System.Windows.Forms.ToolStripMenuItem tsmRemoveBadUser;
        private System.Windows.Forms.ToolStripMenuItem tsmCopy;
        private System.Windows.Forms.SplitContainer spMain;
        private System.Windows.Forms.CheckBox chkNoLog;
        private System.Windows.Forms.ContextMenuStrip cmsLog;
        private System.Windows.Forms.ToolStripMenuItem 清空日志ToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem 复制日志ToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem 保存日志ToolStripMenuItem;
        private System.Windows.Forms.DataGridViewCheckBoxColumn chkTask;
        private System.Windows.Forms.DataGridViewTextBoxColumn 账号;
        private System.Windows.Forms.DataGridViewTextBoxColumn TrainNo;
        private System.Windows.Forms.DataGridViewTextBoxColumn 起始站点;
        private System.Windows.Forms.DataGridViewTextBoxColumn FromStationDescription;
        private System.Windows.Forms.DataGridViewTextBoxColumn ToStationDescription;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column9;
        private System.Windows.Forms.DataGridViewTextBoxColumn status;
        private System.Windows.Forms.DataGridViewTextBoxColumn LeftTicketDescription;
        private System.Windows.Forms.DataGridViewTextBoxColumn Remark;
        private System.Windows.Forms.ToolStripMenuItem 实名制校验ToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem 全部重新登陆ToolStripMenuItem;
        private System.Windows.Forms.ToolStripSeparator toolStripSeparator6;
        private System.Windows.Forms.ToolStripMenuItem 重新登陆未登录账号ToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem 取消所有订单ToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem 选择任务ToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem 全选ToolStripMenuItem1;
        private System.Windows.Forms.ToolStripMenuItem 反选ToolStripMenuItem1;
        private System.Windows.Forms.ToolStripMenuItem 选择所有已停止ToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem 所有已完成ToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem 所有正在运行ToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem 启动选择任务ToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem 停止选择任务ToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem 取消选择订单ToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem tsmSaveToWeb;
        private System.Windows.Forms.ToolStripMenuItem tsmPasterPassenger;
        private System.Windows.Forms.ToolStripMenuItem tsmCopyPassenger;
        private System.Windows.Forms.ToolStripMenuItem tsmCopyAccount;
        private System.Windows.Forms.ToolStripSeparator toolStripSeparator4;
        private System.Windows.Forms.ToolStripMenuItem 删除账号及任务ToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem 显示隐藏账号ToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem 删除所有乘客ToolStripMenuItem;
        private System.ComponentModel.BackgroundWorker bgDeleAllPass;
        private System.Windows.Forms.ImageList mobileImg;
        private System.Windows.Forms.CheckBox chkBlackHouse;
        private System.Windows.Forms.NumericUpDown nBlackHouseTimeOut;
        private System.Windows.Forms.Label label1;
        private System.Windows.Forms.Label label4;
        private System.Windows.Forms.NumericUpDown nMaxCodeWait;
        private System.Windows.Forms.LinkLabel linkLabel1;
        private System.Windows.Forms.ComboBox cmbQueryThread;
        private System.Windows.Forms.Label label5;
        private System.Windows.Forms.ToolStripMenuItem 打开12306网站ToolStripMenuItem;
        private System.Windows.Forms.DataGridViewImageColumn dataGridViewImageColumn1;
        private System.Windows.Forms.DataGridViewImageColumn dataGridViewImageColumn2;
        private System.Windows.Forms.ToolStripMenuItem tsmQueryCompleteOrder;
        private System.Windows.Forms.RichTextBox rtbNewLog;
        private System.Windows.Forms.Label label6;
        private System.Windows.Forms.NumericUpDown nMaxOrderTime;
        private System.Windows.Forms.LinkLabel linkLabel2;
        private System.Windows.Forms.ToolStripMenuItem tsmCancelQueue;
        private System.Windows.Forms.ToolStripMenuItem tsmCancelAllQueue;
        private System.Windows.Forms.ToolStripMenuItem tsmDeleTask;
        private System.Windows.Forms.ToolStripMenuItem 删除任务及账号ToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem toolStripMenuItem2;
        private System.Windows.Forms.ToolStripMenuItem 关联任务文件打开方式ToolStripMenuItem;
        private System.Windows.Forms.DataGridViewImageColumn imgAccount;
        private System.Windows.Forms.DataGridViewImageColumn imgMobile;
        private System.Windows.Forms.DataGridViewTextBoxColumn colPassCount;
        private System.Windows.Forms.DataGridViewTextBoxColumn dataGridViewTextBoxColumn1;
        private System.Windows.Forms.DataGridViewTextBoxColumn dataGridViewTextBoxColumn2;
        private System.Windows.Forms.DataGridViewTextBoxColumn 实名制;
        private System.Windows.Forms.DataGridViewTextBoxColumn imgIndex;
        private System.Windows.Forms.DataGridViewTextBoxColumn taskMobile;
        private System.Windows.Forms.Label label7;
        private System.Windows.Forms.NumericUpDown nOrderSubTimeOut;
        private System.Windows.Forms.LinkLabel linkLabel3;
        private System.Windows.Forms.ToolStripMenuItem toolStripMenuItem3;
        private System.Windows.Forms.ToolStripMenuItem toolStripMenuItem4;
        private System.Windows.Forms.ToolStripMenuItem toolStripMenuItem5;
        private System.Windows.Forms.ToolStripMenuItem toolStripMenuItem6;
        private System.Windows.Forms.ToolStripMenuItem toolStripMenuItem7;
        private System.Windows.Forms.Button btnSave;
    }
}

