﻿using CommonLib;
using System;
using System.Collections.Generic;
using System.Collections.Specialized;
using System.IO;
using System.Security.Cryptography;
using System.Text;

namespace DocOcr
{
    /// <summary>
    /// WPS PDF
    /// </summary>
    public class WPSRec : BaseDocOcrRec
    {
        public WPSRec()
        {
            OcrGroup = OcrGroupType.金山;
            OcrType = DocOcrType.WPS;
            MaxExecPerTime = 22;

            IsMultiPage = true;
            IsSupportVertical = true;
            StrResultJsonSpilt = "text";
            LstVerticalLocation = new List<object>() { "position" };
            LstJsonLastProcessArray = new List<object>() { "lines" };

            AllowUploadFileTypes = new List<string>() { "pdf" };
        }

        protected override string GetHtml(OcrContent content)
        {
            var result = "";
            var fileId = Upload(content.strBase64, content.fileExt);
            if (!string.IsNullOrEmpty(fileId))
            {
                var query = "TaskRunning";
                while (query.Equals("TaskRunning"))
                {
                    System.Threading.Thread.Sleep(500);
                    query = Query(fileId);
                }
                if (query?.ToLower().Contains("success") == true)
                {
                    var url = Download(fileId);
                    if (!string.IsNullOrEmpty(url))
                    {
                        result = WebClientSyncExt.GetHtml(url, ExecTimeOutSeconds);
                    }
                }
            }
            return result;
        }

        private const string strFileIdSpilt = "\"taskid\":\"";

        private string Upload(string strBase64, string fileType = "pdf")
        {
            string url = "https://ai.wps.cn/task/create/ks_scan_ocr_pdf2json";
            var strPost = "{" +
                "   \"check_md5\" : false," +
                "   \"file_name\" : \"" + Guid.NewGuid().ToString().Replace("-", "") + "." + fileType + "\"," +
                "   \"post_file\" : \"" + strBase64 + "\"," +
                "   \"project_name\" : \"ks_scan_ocr_pdf2json\"," +
                "   \"request_parm\" : \"{\\\"font_size\\\" : \\\"1\\\",  \\\"font_style\\\" : \\\"0\\\",  \\\"position\\\" : \\\"1\\\",  \\\"txt\\\" : \\\"1\\\"}\"," +
                "   \"request_type\" : 11}";
            var heads = new NameValueCollection() {
                { "Client-Type","wps-pc-pdf"},
                //{ "Client-UF",ServerTime.DateTime.Ticks.ToString()},
                //{ "Content-Md5","fa7274269c0d95848c474ccd3da24955"},
                //{ "Client-Flag","87fb869eddfd3750cc86799e397221bd"},
            };
            var result = WebClientSyncExt.GetHtml(url, strPost, ExecTimeOutSeconds, heads);
            if (!string.IsNullOrEmpty(result) && result.Contains(strFileIdSpilt))
            {
                result = CommonHelper.SubString(result, strFileIdSpilt, "\"");
            }
            else
            {
                result = "";
            }
            return result;
        }

        private string Query(string fileId)
        {
            string url = "https://ai.wps.cn/task/query/ks_scan_ocr_pdf2json?taskid=" + fileId;
            var heads = new NameValueCollection() {
                { "Client-Type","wps-pc-pdf"},
                //{ "Client-UF",ServerTime.DateTime.Ticks.ToString()},
                //{ "Content-Md5","fa7274269c0d95848c474ccd3da24955"},
                //{ "Client-Flag","87fb869eddfd3750cc86799e397221bd"},
            };
            var result = WebClientSyncExt.GetHtml(url, "", ExecTimeOutSeconds, heads);
            //{"code":201,"msg":"TaskRunning"
            //{"code":203,"msg":"TaskSuccess"
            if (!string.IsNullOrEmpty(result))
            {
                result = CommonHelper.SubString(result, "\"msg\":\"", "\"");
            }
            return result;
        }

        private string Download(string fileId)
        {
            string url = "https://ai.wps.cn/task/result/ks_scan_ocr_pdf2json?taskid=" + fileId;
            var heads = new NameValueCollection() {
                { "Client-Type","wps-pc-pdf"},
                //{ "Client-UF",ServerTime.DateTime.Ticks.ToString()},
                //{ "Content-Md5","fa7274269c0d95848c474ccd3da24955"},
                //{ "Client-Flag","87fb869eddfd3750cc86799e397221bd"},
            };
            var result = WebClientSyncExt.GetHtml(url, "", ExecTimeOutSeconds, heads);
            //{"code":201,"msg":"TaskRunning"
            //{"code":203,"msg":"TaskSuccess"
            if (!string.IsNullOrEmpty(result))
            {
                result = CommonHelper.SubString(result, "\"response_file_path\": \"", "\"");
                //result = CommonHelper.SubString(result, "\"response_file_path\":\"", "\"");
            }
            //var result = WebClientSyncExt.GetHtml(url, strPost, ExecTimeOutSeconds);
            //File.WriteAllText("1234.doc", result);
            return result;
        }

        protected override LocationInfo GetLocationByStr(string locationInfoStr)
        {
            LocationInfo location = null;
            var spilt = Newtonsoft.Json.Linq.JArray.Parse(locationInfoStr);
            if (spilt?.Count == 4)
            {
                location = new LocationInfo()
                {
                    left = BoxUtil.GetInt32FromObject(spilt[0]),
                    top = BoxUtil.GetInt32FromObject(spilt[1]),
                    width = BoxUtil.GetInt32FromObject(spilt[2]) - BoxUtil.GetInt32FromObject(spilt[0]),
                    height = BoxUtil.GetInt32FromObject(spilt[3]) - BoxUtil.GetInt32FromObject(spilt[1]),
                };
            }
            return location;
        }


    }
}