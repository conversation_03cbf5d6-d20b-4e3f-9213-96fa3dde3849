﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Sockets;
using System.Text;
using System.Threading;

namespace EvalJs.ServiceMsg
{
    public class AsyncSocketServer
    {
        private Socket listenSocket;

        private int m_numConnections;

        private int m_receiveBufferSize;

        private Semaphore m_maxNumberAcceptedClients;

        private int m_socketTimeOutMS;

        private AsyncSocketUserTokenPool m_asyncSocketUserTokenPool;

        private AsyncSocketUserTokenList m_asyncSocketUserTokenList;

        private LogOutputSocketProtocolMgr m_logOutputSocketProtocolMgr;

        private UploadSocketProtocolMgr m_uploadSocketProtocolMgr;

        private DownloadSocketProtocolMgr m_downloadSocketProtocolMgr;

        private DaemonThread m_daemonThread;

        public int SocketTimeOutMS
        {
            get
            {
                return this.m_socketTimeOutMS;
            }
            set
            {
                this.m_socketTimeOutMS = value;
            }
        }

        public AsyncSocketUserTokenList AsyncSocketUserTokenList
        {
            get
            {
                return this.m_asyncSocketUserTokenList;
            }
        }

        public LogOutputSocketProtocolMgr LogOutputSocketProtocolMgr
        {
            get
            {
                return this.m_logOutputSocketProtocolMgr;
            }
        }

        public UploadSocketProtocolMgr UploadSocketProtocolMgr
        {
            get
            {
                return this.m_uploadSocketProtocolMgr;
            }
        }

        public DownloadSocketProtocolMgr DownloadSocketProtocolMgr
        {
            get
            {
                return this.m_downloadSocketProtocolMgr;
            }
        }

        public AsyncSocketServer(int numConnections)
        {
            this.m_numConnections = numConnections;
            this.m_receiveBufferSize = ProtocolConst.ReceiveBufferSize;
            this.m_asyncSocketUserTokenPool = new AsyncSocketUserTokenPool(numConnections);
            this.m_asyncSocketUserTokenList = new AsyncSocketUserTokenList();
            this.m_maxNumberAcceptedClients = new Semaphore(numConnections, numConnections);
            this.m_logOutputSocketProtocolMgr = new LogOutputSocketProtocolMgr();
            this.m_uploadSocketProtocolMgr = new UploadSocketProtocolMgr();
            this.m_downloadSocketProtocolMgr = new DownloadSocketProtocolMgr();
        }

        public void Init()
        {
            for (int i = 0; i < this.m_numConnections; i++)
            {
                AsyncSocketUserToken asyncSocketUserToken = new AsyncSocketUserToken(this.m_receiveBufferSize);
                asyncSocketUserToken.ReceiveEventArgs.Completed += new EventHandler<SocketAsyncEventArgs>(this.IO_Completed);
                asyncSocketUserToken.SendEventArgs.Completed += new EventHandler<SocketAsyncEventArgs>(this.IO_Completed);
                this.m_asyncSocketUserTokenPool.Push(asyncSocketUserToken);
            }
        }

        public void Start(IPEndPoint localEndPoint)
        {
            this.listenSocket = new Socket(localEndPoint.AddressFamily, SocketType.Stream, ProtocolType.Tcp);
            this.listenSocket.Bind(localEndPoint);
            this.listenSocket.Listen(this.m_numConnections);
            this.StartAccept(null);
            this.m_daemonThread = new DaemonThread(this);
        }

        public void StartAccept(SocketAsyncEventArgs acceptEventArgs)
        {
            bool flag = acceptEventArgs == null;
            if (flag)
            {
                acceptEventArgs = new SocketAsyncEventArgs();
                acceptEventArgs.Completed += new EventHandler<SocketAsyncEventArgs>(this.AcceptEventArg_Completed);
            }
            else
            {
                acceptEventArgs.AcceptSocket = null;
            }
            this.m_maxNumberAcceptedClients.WaitOne();
            bool flag2 = this.listenSocket.AcceptAsync(acceptEventArgs);
            bool flag3 = !flag2;
            if (flag3)
            {
                this.ProcessAccept(acceptEventArgs);
            }
        }

        private void AcceptEventArg_Completed(object sender, SocketAsyncEventArgs acceptEventArgs)
        {
            try
            {
                this.ProcessAccept(acceptEventArgs);
            }
            catch (Exception ex)
            {
                Console.WriteLine(ex.StackTrace);
            }
        }

        private void ProcessAccept(SocketAsyncEventArgs acceptEventArgs)
        {
            AsyncSocketUserToken asyncSocketUserToken = this.m_asyncSocketUserTokenPool.Pop();
            this.m_asyncSocketUserTokenList.Add(asyncSocketUserToken);
            asyncSocketUserToken.ConnectSocket = acceptEventArgs.AcceptSocket;
            asyncSocketUserToken.ConnectDateTime = DateTime.Now;
            try
            {
                bool flag = asyncSocketUserToken.ConnectSocket.ReceiveAsync(asyncSocketUserToken.ReceiveEventArgs);
                bool flag2 = !flag;
                if (flag2)
                {
                    AsyncSocketUserToken obj = asyncSocketUserToken;
                    lock (obj)
                    {
                        this.ProcessReceive(asyncSocketUserToken.ReceiveEventArgs);
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine("Accept client {0} error, message: {1}", asyncSocketUserToken.ConnectSocket, ex.Message);
                Console.WriteLine(ex.StackTrace);
            }
            this.StartAccept(acceptEventArgs);
        }

        private void IO_Completed(object sender, SocketAsyncEventArgs asyncEventArgs)
        {
            AsyncSocketUserToken asyncSocketUserToken = asyncEventArgs.UserToken as AsyncSocketUserToken;
            asyncSocketUserToken.ActiveDateTime = DateTime.Now;
            try
            {
                AsyncSocketUserToken obj = asyncSocketUserToken;
                lock (obj)
                {
                    bool flag2 = asyncEventArgs.LastOperation == SocketAsyncOperation.Receive;
                    if (flag2)
                    {
                        this.ProcessReceive(asyncEventArgs);
                    }
                    else
                    {
                        bool flag3 = asyncEventArgs.LastOperation == SocketAsyncOperation.Send;
                        if (!flag3)
                        {
                            throw new ArgumentException("The last operation completed on the socket was not a receive or send");
                        }
                        this.ProcessSend(asyncEventArgs);
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine("IO_Completed {0} error, message: {1}", asyncSocketUserToken.ConnectSocket, ex.Message);
                Console.WriteLine(ex.StackTrace);
            }
        }

        private void ProcessReceive(SocketAsyncEventArgs receiveEventArgs)
        {
            AsyncSocketUserToken asyncSocketUserToken = receiveEventArgs.UserToken as AsyncSocketUserToken;
            bool flag = asyncSocketUserToken.ConnectSocket == null;
            if (!flag)
            {
                asyncSocketUserToken.ActiveDateTime = DateTime.Now;
                bool flag2 = asyncSocketUserToken.ReceiveEventArgs.BytesTransferred > 0 && asyncSocketUserToken.ReceiveEventArgs.SocketError == SocketError.Success;
                if (flag2)
                {
                    int num = asyncSocketUserToken.ReceiveEventArgs.Offset;
                    int num2 = asyncSocketUserToken.ReceiveEventArgs.BytesTransferred;
                    bool flag3 = asyncSocketUserToken.AsyncSocketInvokeElement == null & asyncSocketUserToken.ConnectSocket != null;
                    if (flag3)
                    {
                        this.BuildingSocketInvokeElement(asyncSocketUserToken);
                        num++;
                        num2--;
                    }
                    bool flag4 = asyncSocketUserToken.AsyncSocketInvokeElement == null;
                    if (flag4)
                    {
                        Console.WriteLine("Illegal client connection. Local Address: {0}, Remote Address: {1}", asyncSocketUserToken.ConnectSocket.LocalEndPoint, asyncSocketUserToken.ConnectSocket.RemoteEndPoint);
                        this.CloseClientSocket(asyncSocketUserToken);
                    }
                    else
                    {
                        bool flag5 = num2 > 0;
                        if (flag5)
                        {
                            bool flag6 = !asyncSocketUserToken.AsyncSocketInvokeElement.ProcessReceive(asyncSocketUserToken.ReceiveEventArgs.Buffer, num, num2);
                            if (flag6)
                            {
                                this.CloseClientSocket(asyncSocketUserToken);
                            }
                            else
                            {
                                bool flag7 = asyncSocketUserToken.ConnectSocket.ReceiveAsync(asyncSocketUserToken.ReceiveEventArgs);
                                bool flag8 = !flag7;
                                if (flag8)
                                {
                                    this.ProcessReceive(asyncSocketUserToken.ReceiveEventArgs);
                                }
                            }
                        }
                        else
                        {
                            bool flag9 = asyncSocketUserToken.ConnectSocket.ReceiveAsync(asyncSocketUserToken.ReceiveEventArgs);
                            bool flag10 = !flag9;
                            if (flag10)
                            {
                                this.ProcessReceive(asyncSocketUserToken.ReceiveEventArgs);
                            }
                        }
                    }
                }
                else
                {
                    this.CloseClientSocket(asyncSocketUserToken);
                }
            }
        }

        private void BuildingSocketInvokeElement(AsyncSocketUserToken userToken)
        {
            byte b = userToken.ReceiveEventArgs.Buffer[userToken.ReceiveEventArgs.Offset];
            bool flag = b == 2;
            if (flag)
            {
                userToken.AsyncSocketInvokeElement = new UploadSocketProtocol(this, userToken);
            }
            else
            {
                bool flag2 = b == 3;
                if (flag2)
                {
                    userToken.AsyncSocketInvokeElement = new DownloadSocketProtocol(this, userToken);
                }
                else
                {
                    bool flag3 = b == 4;
                    if (flag3)
                    {
                        userToken.AsyncSocketInvokeElement = new RemoteStreamSocketProtocol(this, userToken);
                    }
                    else
                    {
                        bool flag4 = b == 5;
                        if (flag4)
                        {
                            userToken.AsyncSocketInvokeElement = new ThroughputSocketProtocol(this, userToken);
                        }
                        else
                        {
                            bool flag5 = b == 8;
                            if (flag5)
                            {
                                userToken.AsyncSocketInvokeElement = new ControlSocketProtocol(this, userToken);
                            }
                            else
                            {
                                bool flag6 = b == 9;
                                if (flag6)
                                {
                                    userToken.AsyncSocketInvokeElement = new LogOutputSocketProtocol(this, userToken);
                                }
                            }
                        }
                    }
                }
            }
            bool flag7 = userToken.AsyncSocketInvokeElement != null;
            if (flag7)
            {
            }
        }

        private bool ProcessSend(SocketAsyncEventArgs sendEventArgs)
        {
            AsyncSocketUserToken asyncSocketUserToken = sendEventArgs.UserToken as AsyncSocketUserToken;
            bool flag = asyncSocketUserToken.AsyncSocketInvokeElement == null;
            bool result;
            if (flag)
            {
                result = false;
            }
            else
            {
                asyncSocketUserToken.ActiveDateTime = DateTime.Now;
                bool flag2 = sendEventArgs.SocketError == SocketError.Success;
                if (flag2)
                {
                    result = asyncSocketUserToken.AsyncSocketInvokeElement.SendCompleted();
                }
                else
                {
                    this.CloseClientSocket(asyncSocketUserToken);
                    result = false;
                }
            }
            return result;
        }

        public bool SendAsyncEvent(Socket connectSocket, SocketAsyncEventArgs sendEventArgs, byte[] buffer, int offset, int count)
        {
            bool flag = connectSocket == null;
            bool result;
            if (flag)
            {
                result = false;
            }
            else
            {
                sendEventArgs.SetBuffer(buffer, offset, count);
                bool flag2 = connectSocket.SendAsync(sendEventArgs);
                bool flag3 = !flag2;
                result = (!flag3 || this.ProcessSend(sendEventArgs));
            }
            return result;
        }

        public void CloseClientSocket(AsyncSocketUserToken userToken)
        {
            bool flag = userToken.ConnectSocket == null;
            if (!flag)
            {
                try
                {
                    userToken.ConnectSocket.Shutdown(SocketShutdown.Both);
                }
                catch (Exception var_1_22)
                {
                }
                userToken.ConnectSocket.Close();
                userToken.ConnectSocket = null;
                this.m_maxNumberAcceptedClients.Release();
                this.m_asyncSocketUserTokenPool.Push(userToken);
                this.m_asyncSocketUserTokenList.Remove(userToken);
            }
        }
    }
}
