using Enterprise.Framework.Redis.Utility;
using ServiceStack.Redis;
using System;
using System.Collections.Generic;

namespace Enterprise.Framework.Redis
{
    public abstract class RedisSortedSetObject<TObject> : RedisObject where TObject : class
    {
        private string _listId;

        protected override string FormatKey(string key)
        {
            return key;
        }

        public RedisSortedSetObject(string listId)
        {
            _listId = listId;
        }

        public void Insert(TObject value, long sequence)
        {
            string text = InstanceCreator.JavaScriptSerializer.Serialize(value);
            ExecuteRedisAction(delegate (IRedisClient redisClient)
            {
                redisClient.AddItemToSortedSet(_listId, text, sequence);
            });
        }

        public TObject Pop()
        {
            return ExecuteRedisFunc(delegate (IRedisClient redisClient)
            {
                string text = redisClient.PopItemWithLowestScoreFromSortedSet(_listId);
                return (!string.IsNullOrWhiteSpace(text)) ? InstanceCreator.JavaScriptSerializer.Deserialize<TObject>(text) : null;
            });
        }

        public void Remove(TObject value)
        {
            string text = InstanceCreator.JavaScriptSerializer.Serialize(value);
            ExecuteRedisAction(delegate (IRedisClient redisClient)
            {
                redisClient.RemoveItemFromSortedSet(_listId, text);
            });
        }

        public int GetCount()
        {
            return ExecuteRedisFunc(delegate (IRedisClient redisClient)
            {
                long listCount = redisClient.GetListCount(_listId);
                return Convert.ToInt32(listCount);
            });
        }

        public List<TObject> GetAllObject()
        {
            return ExecuteRedisFunc(delegate (IRedisClient redisClient)
            {
                List<string> allItemsFromSortedSet = redisClient.GetAllItemsFromSortedSet(_listId);
                List<TObject> list = new List<TObject>();
                foreach (string item2 in allItemsFromSortedSet)
                {
                    TObject item = InstanceCreator.JavaScriptSerializer.Deserialize<TObject>(item2);
                    list.Add(item);
                }
                return list;
            });
        }
    }
}
