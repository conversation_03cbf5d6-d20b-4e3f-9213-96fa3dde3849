﻿using CommonLib;
using MySqlX.XDevAPI;
using System;

namespace Account.Web
{
    public partial class UserMac : System.Web.UI.Page
    {
        public string strErrorMsg = string.Empty;

        protected void Page_Load(object sender, EventArgs e)
        {
            var session = Session["user"] as CodeEntity;
            if (session == null || string.IsNullOrEmpty(session.StrNickName))
            {
                Response.Redirect("UserLogin.aspx");
                return;
            }
            string strOp = Request.QueryString["op"];
            string uid = Request.QueryString["id"];
            if (Request.RequestType.Equals("GET") && !string.IsNullOrEmpty(strOp) && BoxUtil.IsAlphaNumeric(uid))
            {
                var result = true;
                var strMsg = "操作";
                try
                {
                    switch (strOp)
                    {
                        case "offline":
                            strMsg = "下线";
                            OffLineByToken(session.StrAppCode, uid);
                            break;
                        case "enable":
                            strMsg = "启用";
                            result = CodeHelper.UpdateUserDataState(session.StrAppCode, uid, true);
                            break;
                        case "disable":
                            strMsg = "禁用";
                            result = CodeHelper.UpdateUserDataState(session.StrAppCode, uid, false);
                            OffLineByToken(session.StrAppCode, uid);
                            break;
                    }
                }
                catch { }
                Response.Write("<script>alert('" + strMsg + (result ? "成功" : "失败") + "！');window.location='UserMac.aspx'</script>");
            }
        }

        private void OffLineByToken(string account, string uid)
        {
            var token = CodeHelper.GetUserToken(account, uid);
            if (!string.IsNullOrEmpty(token))
                RdsCacheHelper.LstAccountCache.ClearToken(account, token);
        }
    }
}