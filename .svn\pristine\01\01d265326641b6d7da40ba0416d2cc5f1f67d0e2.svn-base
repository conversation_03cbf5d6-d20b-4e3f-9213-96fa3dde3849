<script minify>
App.components({
    /** @param {{input:InputInfo,model:*,api:ApiResult}} args */
    Input({ input, prop, field, model, api }) {
        if (!input && field)
            input = field.input
        if (!prop && field)
            prop = field.prop
        return {
            $template: '#input-template',
            get input() { return resolve(input) },
            get prop() { return resolve(prop) },
            get model() { return  resolve(model) || {} },
            get error() { return resolve(api, x => x && x.error) },
            /** @param {InputInfo} input */
            kvpValues(input) {
                return resolve(input.allowableEntries) || (resolve(input.allowableValues)||[]).map(x => ({ key:x, value:x }))
            },
            /** @param {InputInfo} input */
            useLabel(input) {
                return input.label != null ? input.label : humanify(input.id)
            },
            /** @param {InputInfo} input */
            usePlaceholder(input) {
                return input.placeholder || '' 
            },
            /** @param {string} id */
            fieldError(id) {
                let error = this.error
                let fieldError = error && error.errors && error.errors.find(x => x.fieldName.toLowerCase() === id.toLowerCase());
                return fieldError && fieldError.message
            },
            /** @param {string} id */
            hasError(id) { return !!this.fieldError(id) },
            /** @param {InputInfo} input */
            labelClass(input) { return input.css && input.css.label || '' },
            /** @param {InputInfo} input */
            inputClass(input) { return inputClass(this.fieldError(input.id), input.css && input.css.input) },
            /** @param {InputInfo} input */
            ariaDescribedby(input) {
                return this.fieldError(input.id) ? `${input.id}-error` : input.help ? `${input.id}-description` : null
            },
            isMultiple(input) {
                return input['data-type'] === 'List`1'
            },
            fileChange(input,e) {
                let files = e.target.files
                this.model[input.id] = Array.from(files)
                    .map(f => ({ fileName:f.name, contentLength:f.size, filePath:Files.fileImageUri(f) }))
            },
            hasValidFile(input) { 
                return map(this.model[input.id], x => typeof x != 'string' || !x.startsWith('C:\\fakepath\\')) 
            },
            fileList(input) {
                let files = this.model[input.id] || []
                return files.filter(f => typeof f == 'object')
            },
            imgSrc(input) {
                let val = this.model[input.id]
                if (!val) return null
                let src = typeof val == 'string'
                    ? val
                    : val[0].filePath
                return Files.filePathUri(src)
            },
            filePathUri(path) { return Files.filePathUri(path) },
            imgCls(src) {
                return !src || src.startsWith('data:') || src.endsWith('.svg') 
                    ?  ''
                    : 'rounded-full'
            },
            updateLookup(input,value) {
                this.model[input.id] = value
                Forms.fetchLookupValues([this.model],[prop], () => {
                    this.model[input.id] = null
                    App.nextTick(() => this.model[input.id] = value)
                })
            },
            formatBytes:Files.formatBytes,
            toAppUrl,
        }
    }
})
</script>
<!--minify-->
<template id="input-template">
<div v-if="input.type=='divider'" class="border-t"></div>
<input v-else-if="input.type=='hidden'" type="hidden" :name="input.id" v-model="model[input.id]" />
<div v-else-if="input.type=='file'" :class="`flex ${!isMultiple(input) ? 'justify-between' : 'flex-col'}`">
    <div>
        <label v-if="useLabel(input)" :for="input.id" :class="['block text-sm font-medium text-gray-700',labelClass(input)]"
               v-html="useLabel(input)"></label>
        <div class="block mt-2">
            <span class="sr-only">{{input.help || useLabel(input)}}</span>
            <input ref="inputFile" type="file" :name="input.id" :multiple="isMultiple(input)" v-bind="input" class="block w-full text-sm text-slate-500
              file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold 
              file:bg-violet-50 file:text-violet-700 hover:file:bg-violet-100" 
              @change="fileChange(input,event)" />
            <p v-if="fieldError(input.id)" class="mt-2 text-sm text-red-500" :id="`${input.id}-error`" v-html="fieldError(input.id)"></p>
            <p v-else-if="input.help" :id="`${input.id}-description`" class="text-gray-500" v-html="input.help"></p>
        </div>
    </div>
    <div v-if="!isMultiple(input) && hasValidFile(input)" class="shrink-0 cursor-pointer" @click="$1('#'+input.id).click()">
        <img :class="`h-16 w-16 object-cover ${imgCls(imgSrc(input))}`" :src="imgSrc(input)" :alt="`Current ${useLabel(input)}`" onerror="iconOnError(this)" />
    </div>
    <div v-else-if="isMultiple(input) && fileList(input).length" class="mt-3">
        <table>
            <tr v-for="file in fileList(input)">
                <td class="pr-6 align-bottom pb-2">
                    <div class="flex w-full">
                        <img :src="filePathUri(file.filePath)" :class="`mr-2 h-8 w-8 object-cover ${imgCls(file.filePath)}`" onerror="iconOnError(this)">
                        <a v-if="file.filePath && !file.filePath.startsWith('data:') && !file.filePath.startsWith('blob:')" :href="toAppUrl(file.filePath)" :title="file.filePath" target="_blank">
                            {{file.fileName}}
                        </a>
                        <span v-else>{{file.fileName}}</span>
                    </div>
                </td>
                <td class="align-top pb-2 whitespace-nowrap">
                    <span class="text-gray-500 text-sm bg-white">{{formatBytes(file.contentLength)}}</span>
                </td>
            </tr>
        </table>
    </div>
</div>
<div v-else-if="!input.disabled && prop && prop.refInfo" class="lookup-field">
    <input type="hidden" :name="input.id" v-model="model[input.id]" />
    <div v-if="useLabel(input)" class="flex justify-between">
        <label :for="input.id" :class="['block text-sm font-medium text-gray-700',labelClass(input)]"
               v-html="useLabel(input)"></label>
        <span class="text-sm text-gray-500 pr-3">{{model[input.id]}}</span>
    </div>
    <div v-if="prop.refInfo(model)" class="mt-1 relative">
        <button type="button" class="lookup flex relative w-full bg-white border border-gray-300 rounded-md shadow-sm pl-3 pr-10 py-2 text-left focus:outline-none focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                @click="prop.refLookup(value => updateLookup(input,value))"
                aria-haspopup="listbox" aria-expanded="true" aria-labelledby="listbox-label">
          <span class="w-full inline-flex truncate">
            <span class="text-blue-700 flex cursor-pointer">
                <span class="mr-1" v-scope="Image(prop.refInfo(model).icon, { cls:'w-5 h-5' })"></span>
                <span v-html="map(prop.refInfo(model),x => x.html || input.placeholder || '')"></span>
            </span>
          </span>
            <span class="absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none">
                <svg class="h-5 w-5 text-gray-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                    <path fill-rule="evenodd" d="M10 3a1 1 0 01.707.293l3 3a1 1 0 01-1.414 1.414L10 5.414 7.707 7.707a1 1 0 01-1.414-1.414l3-3A1 1 0 0110 3zm-3.707 9.293a1 1 0 011.414 0L10 14.586l2.293-2.293a1 1 0 011.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z" clip-rule="evenodd" />
                </svg>
            </span>
        </button>
    </div>
    <p v-if="fieldError(input.id)" class="mt-2 text-sm text-red-500" :id="`${input.id}-error`" v-html="fieldError(input.id)"></p>
</div>
<div v-else-if="input.type=='checkbox'" class="relative flex flex-col">
    <span>&nbsp;</span>
    <div class="flex items-start">
        <div class="flex items-center h-5 flex-col">
            <input :name="input.id" v-bind="input" type="checkbox" v-model="model[input.id]"
                   :aria-invalid="hasError(input.id)" :aria-describedby="ariaDescribedby(input)"
                   class="focus:ring-indigo-500 h-4 w-4 text-indigo-600 border-gray-300 rounded" />
        </div>
        <div class="ml-3 text-sm">
            <label v-if="useLabel(input)" :for="input.id" :class="['font-medium text-gray-700 select-none',labelClass(input)]"
                   v-html="useLabel(input)"></label>
            <p v-if="fieldError(input.id)" class="text-red-500" v-html="fieldError(input.id)"></p>
            <p v-else-if="input.help" class="text-gray-500" v-html="input.help"></p>
        </div>
    </div>
</div>
<div v-else>
    <label v-if="useLabel(input)" :for="input.id" :class="['block text-sm font-medium text-gray-700',labelClass(input)]"
           v-html="useLabel(input)"></label>
    <div class="mt-1 relative rounded-md shadow-sm">
        <select v-if="input.type=='select'" :name="input.id" v-bind="input"
                :aria-invalid="hasError(input.id)" :aria-describedby="ariaDescribedby(input)"
                :class="inputClass(input)" v-model="model[input.id]">
            <option v-if="input['data-type']=='Nullable`1'"></option>
            <option v-for="entry in kvpValues(input)" :value="entry.key">{{entry.value}}</option>
        </select>
        <textarea v-else-if="input.type=='textarea'" :name="input.id" v-bind="input" :placeholder="usePlaceholder(input)"
                  :aria-invalid="hasError(input.id)" :aria-describedby="ariaDescribedby(input)"
                  :class="inputClass(input)" v-model="model[input.id]"></textarea>
        <input v-else :name="input.id" v-bind="input" :type="input.type||'text'" :placeholder="usePlaceholder(input)" autocomplete="new-password"
               :aria-invalid="hasError(input.id)" :aria-describedby="ariaDescribedby(input)"
               :class="inputClass(input)" v-model="model[input.id]" />
        <div v-if="fieldError(input.id)" class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
            <svg class="h-5 w-5 text-red-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
            </svg>
        </div>
    </div>
    <p v-if="fieldError(input.id)" class="mt-2 text-sm text-red-500" :id="`${input.id}-error`" v-html="fieldError(input.id)"></p>
    <p v-else-if="input.help" :id="`${input.id}-description`" class="text-gray-500" v-html="input.help"></p>
</div>
</template>
<!--/minify-->
