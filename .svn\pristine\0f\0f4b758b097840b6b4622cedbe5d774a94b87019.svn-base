﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="12.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{68876E32-6A4C-4271-A45A-6F6ACC9030B0}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>TableOcr</RootNamespace>
    <AssemblyName>TableOcr</AssemblyName>
    <TargetFrameworkVersion>v4.7.2</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <TargetFrameworkProfile />
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <Prefer32Bit>false</Prefer32Bit>
    <PlatformTarget>AnyCPU</PlatformTarget>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="Newtonsoft.Json, Version=13.0.0.0, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed, processorArchitecture=MSIL">
      <HintPath>..\packages\Newtonsoft.Json.13.0.1\lib\net45\Newtonsoft.Json.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Web" />
    <Reference Include="System.Web.Extensions" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="BaiDuAILiteRec.cs" />
    <Compile Include="BaiMiaoYouDaoRec.cs" />
    <Compile Include="ConstHelper.cs" />
    <Compile Include="DaGuanYinHangRec.cs" />
    <Compile Include="DaGuanAIRec.cs" />
    <Compile Include="HanWangZhiXueCaiWuRec.cs" />
    <Compile Include="HanWangZhiXueNoBordRec.cs" />
    <Compile Include="HanWangZhiXueRec.cs" />
    <Compile Include="HanWangRec.cs" />
    <Compile Include="HuoShanRec.cs" />
    <Compile Include="HeHeLiteRec.cs" />
    <Compile Include="HeHeAPIRec.cs" />
    <Compile Include="HeHeRec.cs" />
    <Compile Include="QQBroswerRec.cs" />
    <Compile Include="TencentAPIV3Rec.cs" />
    <Compile Include="XinDongFangRec.cs" />
    <Compile Include="YiDaoRec.cs" />
    <Compile Include="YiTuRec.cs" />
    <Compile Include="TencentAPIV2Rec.cs" />
    <Compile Include="TuDangRec.cs" />
    <Compile Include="YouDaoLiteRec.cs" />
    <Compile Include="ZaiXianAIRec.cs" />
    <Compile Include="DuGuangRec.cs" />
    <Compile Include="OcrResultProcess.cs" />
    <Compile Include="TableOcrType.cs" />
    <Compile Include="BaseTableRec.cs" />
    <Compile Include="XueErSiDemoRec.cs" />
    <Compile Include="BaiMiaoTengXunRec.cs" />
    <Compile Include="YouDaoRec.cs" />
    <Compile Include="YouDaoAPIRec.cs" />
    <Compile Include="XinHuoRec.cs" />
    <Compile Include="VivoRec.cs" />
    <Compile Include="BaiDuAPIRec.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\CommonLib\CommonLib.csproj">
      <Project>{fc03a7d4-8ef2-4dea-a15a-c099eb77b0eb}</Project>
      <Name>CommonLib</Name>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup>
    <None Include="app.config" />
    <None Include="packages.config" />
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>