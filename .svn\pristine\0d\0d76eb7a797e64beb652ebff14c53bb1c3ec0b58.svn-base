﻿using CommonLib.UserConfig;
using System;
using System.Collections.Generic;
using System.Linq;

namespace CommonLib
{
    public class UserType
    {
        public UserTypeEnum Type { get; set; }

        public string Name
        {
            get { return Type.ToString(); }
        }

        /// <summary>
        /// 指定时间段内（毫秒）
        /// </summary>
        public int PerTimeSpan { get; set; }

        /// <summary>
        /// 指定时间段内执行次数
        /// </summary>
        public int PerTimeSpanExecCount { get; set; }

        /// <summary>
        /// 是否显示其他处理结果
        /// </summary>
        public bool IsSetOtherResult { get; set; }

        /// <summary>
        /// 是否支持文本识别
        /// </summary>
        public bool IsSupportTxt { get; set; } = true;

        /// <summary>
        /// 是否支持翻译
        /// </summary>
        public bool IsSupportTranslate { get; set; }

        /// <summary>
        /// 是否支持图片文件识别
        /// </summary>
        public bool IsSupportImageFile { get; set; }

        /// <summary>
        /// 是否支持文档翻译
        /// </summary>
        public bool IsSupportDocFile { get; set; }

        /// <summary>
        /// 能否批量处理文件
        /// </summary>
        public bool IsSupportBatch { get; set; }

        /// <summary>
        /// 是否支持竖排识别
        /// </summary>
        public bool IsSupportVertical { get; set; }

        /// <summary>
        /// 是否支持数学公式识别
        /// </summary>
        public bool IsSupportMath { get; set; }

        /// <summary>
        /// 是否支持表格识别
        /// </summary>
        public bool IsSupportTable { get; set; }

        /// <summary>
        /// 是否支持通道切换
        /// </summary>
        public bool IsSupportPassage { get; set; }

        /// <summary>
        /// 是否支持本地OCR识别
        /// </summary>
        public bool IsSupportLocalOcr { get; set; }

        /// <summary>
        /// 能否多点登录
        /// </summary>
        public int MaxLoginCount { get; set; } = 1;

        /// <summary>
        /// 后台单次并行执行次数
        /// </summary>
        [System.Xml.Serialization.XmlIgnore]
        [System.Web.Script.Serialization.ScriptIgnore]
        public int ProcessPerTime { get; set; } = 1;

        /// <summary>
        /// 是否开放申请
        /// </summary>

        [System.Xml.Serialization.XmlIgnore]
        [System.Web.Script.Serialization.ScriptIgnore]
        public bool IsOpen { get; set; } = true;

        /// <summary>
        /// 单月价格
        /// </summary>
        public double PerPriceMonth { get; set; }

        /// <summary>
        /// 季度优惠
        /// </summary>
        public double QuarDiscount { get; set; } = 1;

        /// <summary>
        /// 年费优惠
        /// </summary>
        public double YearDiscount { get; set; } = 1;

        [System.Xml.Serialization.XmlIgnore]
        [System.Web.Script.Serialization.ScriptIgnore]
        public PriceType PriceType { get; set; }

        /// <summary>
        /// 单位价格
        /// </summary>

        [System.Xml.Serialization.XmlIgnore]
        [System.Web.Script.Serialization.ScriptIgnore]
        public double PerPrice { get; set; }

        /// <summary>
        /// 收费类型
        /// </summary>

        [System.Xml.Serialization.XmlIgnore]
        [System.Web.Script.Serialization.ScriptIgnore]
        public List<ChargeType> ChargeTypes { get; set; }

        public List<ChargeViewToUser> UserChargeType { get; set; }

        /// <summary>
        /// 最大可上传的识别文件
        /// </summary>
        public int MaxUploadSize { get; set; } = 300;

        public string LimitDesc(long todayCount, bool isLimitInfo, long todayTokenCount)
        {
            string desc;
            switch (Type)
            {
                case UserTypeEnum.体验版:
                    if (ConfigHelper.IsRegToGeRen)
                    {
                        desc = "现在注册会员，即送[个人版]体验资格！\n支持文本，竖排等！\n速度更快，功能更强，识别精度更高！";
                    }
                    else if (ConfigHelper.IsRegToProfessional)
                    {
                        desc = "现在注册会员，即送[专业版]体验资格！\n支持文本，竖排，公式等！\n速度更快，功能更强，识别精度更高！";
                    }
                    else
                    {
                        desc = "请考虑升级为[专业版]！\n支持文本，竖排，公式等！\n速度更快，功能更强，识别精度更高！";
                    }
                    break;
                case UserTypeEnum.专业版:
                    desc = "请考虑升级为[企业版]或[旗舰版]！\n支持表格，文档翻译，文档转换，文档导出等！\n速度更快，功能更强，识别精度更高！";
                    break;
                case UserTypeEnum.企业版:
                    desc = "请考虑升级为[旗舰版]或[宇宙版]！\n支持文档翻译，文档转换，文档导出等！\n速度更快，功能更强，识别精度更高！";
                    break;
                case UserTypeEnum.旗舰版:
                    desc = "请考虑升级为[宇宙版]！\n速度更快，功能更强，识别精度更高！";
                    break;
                case UserTypeEnum.宇宙版:
                    desc = "更多功能，请联系我们定制！";
                    break;
                default:
                    desc = "请稍后重试！";
                    break;
            }
            if (!string.IsNullOrEmpty(ConfigHelper.TipMsg))
            {
                desc += "\n\n" + ConfigHelper.TipMsg;
            }

            if (!isLimitInfo)
            {
                return desc;
            }

            var limitTotal = Equals(Type, UserTypeEnum.体验版) ? LimitPerTokenCount : LimitPerDayCount;
            var userdCount = Equals(Type, UserTypeEnum.体验版) ? todayTokenCount : todayCount;
            return string.Format("{0}识别限制:\n频率:{1}秒/{2}次{3}\n{4}"
                , Type
                , Math.Floor(PerTimeSpan * 1d / 1000).ToString("F0")
                , Equals(PerTimeSpanExecCount, 1) ? "" : PerTimeSpanExecCount.ToString()
                , limitTotal > 0 ? ",总量:" + limitTotal + "次/天" + (userdCount > 0 ? "(已用" + userdCount + "次)" : "") : ""
                , desc);
        }

        /// <summary>
        /// 单账户每天最大执行次数
        /// </summary>
        public long LimitPerDayCount { get; set; }

        /// <summary>
        /// 单Token每天最大执行次数
        /// </summary>
        public long LimitPerTokenCount { get; set; }
    }

    public class ChargeViewToUser
    {
        public string Name { get; set; }

        public string Desc { get; set; }

        public double Price { get; set; }

        public bool IsDefault { get; set; }

        public string Tag { get; set; }
    }

    public enum UserTypeEnum
    {
        个人版 = -1,
        体验版 = 0,
        专业版 = 1,
        企业版 = 2,
        旗舰版 = 3,
        宇宙版 = 4,
        定制版 = 9
    }

    public class UserTypeHelper
    {
        static UserTypeHelper()
        {
            lstUserType.Add(new UserType
            {
                Type = UserTypeEnum.体验版,
                PerTimeSpanExecCount = 1,
                PerTimeSpan = 15 * 1000,
                IsOpen = false,
                LimitPerDayCount = 5000,
                LimitPerTokenCount = 50
            });
            var geRen = new UserType
            {
                Type = UserTypeEnum.个人版,
                PerTimeSpanExecCount = 1,
                PerTimeSpan = 5 * 1000,
                ProcessPerTime = 2,
                IsSetOtherResult = true,
                IsSupportTxt = true,
                IsSupportImageFile = true,
                IsSupportVertical = true,
                IsSupportMath = false,
                IsSupportBatch = false,
                IsSupportTable = false,
                IsSupportDocFile = false,
                IsSupportTranslate = false,
                PriceType = PriceType.按年,
                PerPrice = 100,
                PerPriceMonth = 2,
                QuarDiscount = 0.9d,
                YearDiscount = 0.8d,
                MaxLoginCount = 2,
                LimitPerDayCount = 500
            };
            lstUserType.Add(geRen);
            var zhuanYe = new UserType
            {
                Type = UserTypeEnum.专业版,
                PerTimeSpanExecCount = 1,
                PerTimeSpan = 3 * 1000,
                ProcessPerTime = 2,
                IsSetOtherResult = true,
                IsSupportTxt = true,
                IsSupportImageFile = true,
                IsSupportVertical = true,
                IsSupportMath = true,
                IsSupportBatch = false,
                IsSupportTable = true,
                IsSupportDocFile = false,
                IsSupportTranslate = false,
                IsSupportLocalOcr = true,
                PriceType = PriceType.按年,
                PerPrice = 100,
                PerPriceMonth = 5,
                QuarDiscount = 0.9d,
                YearDiscount = 0.8d,
                MaxLoginCount = 3,
                LimitPerDayCount = 1000
            };
            lstUserType.Add(zhuanYe);
            var qiYe = new UserType
            {
                Type = UserTypeEnum.企业版,
                PerTimeSpanExecCount = 1,
                PerTimeSpan = 2 * 1000,
                ProcessPerTime = 3,
                IsSetOtherResult = true,
                IsSupportTxt = true,
                IsSupportImageFile = true,
                IsSupportVertical = true,
                IsSupportMath = true,
                IsSupportBatch = true,
                IsSupportTable = true,
                IsSupportDocFile = false,
                IsSupportTranslate = true,
                IsSupportPassage = true,
                IsSupportLocalOcr = true,
                PriceType = PriceType.按年,
                PerPrice = 100,
                PerPriceMonth = 10,
                QuarDiscount = 0.85d,
                YearDiscount = 0.8d,
                MaxLoginCount = 5,
                LimitPerDayCount = 2000
            };
            lstUserType.Add(qiYe);
            var qiJian = new UserType
            {
                Type = UserTypeEnum.旗舰版,
                PerTimeSpanExecCount = 1,
                PerTimeSpan = 1 * 1000,
                ProcessPerTime = 3,
                IsSetOtherResult = true,
                IsSupportTxt = true,
                IsSupportImageFile = true,
                IsSupportVertical = true,
                IsSupportMath = true,
                IsSupportBatch = true,
                IsSupportTable = true,
                IsSupportDocFile = true,
                IsSupportTranslate = true,
                IsSupportPassage = true,
                IsSupportLocalOcr = true,
                PriceType = PriceType.按年,
                PerPrice = 100,
                PerPriceMonth = 15,
                QuarDiscount = 0.85d,
                YearDiscount = 0.8d,
                MaxLoginCount = 8,
                LimitPerDayCount = 3000
            };
            lstUserType.Add(qiJian);
            var yuZhou = new UserType
            {
                Type = UserTypeEnum.宇宙版,
                //IsOpen = false,
                PerTimeSpanExecCount = 2,
                PerTimeSpan = 1 * 1000,
                ProcessPerTime = 3,
                IsSetOtherResult = true,
                IsSupportTxt = true,
                IsSupportImageFile = true,
                IsSupportVertical = true,
                IsSupportMath = true,
                IsSupportBatch = true,
                IsSupportTable = true,
                IsSupportDocFile = true,
                IsSupportTranslate = true,
                IsSupportPassage = true,
                IsSupportLocalOcr = true,
                PriceType = PriceType.按年,
                PerPrice = 100,
                PerPriceMonth = 20,
                QuarDiscount = 0.85d,
                YearDiscount = 0.8d,
                MaxLoginCount = 10,
                LimitPerDayCount = 5000
            };
            lstUserType.Add(yuZhou);
            try
            {
                ChargeTypeConfigurationSectionHandler.ChargeTypes?.UserTypeSettings.ForEach(p =>
                {
                    lstUserType.Find(q => Equals(q.Type, p.UserType)).IsOpen = p.Enable;
                    lstUserType.Find(q => Equals(q.Type, p.UserType)).PriceType = p.PriceType;
                    lstUserType.Find(q => Equals(q.Type, p.UserType)).PerPrice = p.PerPrice;
                    lstUserType.Find(q => Equals(q.Type, p.UserType)).ChargeTypes = p.ChargeTypes;
                });
            }
            catch { }
        }

        private static List<UserType> lstUserType = new List<UserType>();

        /// <summary>
        /// 获取可以注册的用户类别
        /// </summary>
        /// <returns></returns>
        public static List<UserType> GetCanRegUserTypes()
        {
            return lstUserType.Where(p => p.IsOpen).OrderBy(p => p.PerPriceMonth).ToList();
        }

        /// <summary>
        /// 获取可以注册的用户类别
        /// </summary>
        /// <returns></returns>
        public static UserType GetUserType(UserTypeEnum userType)
        {
            return lstUserType.FirstOrDefault(p => Equals(p.Type, userType));
        }

        public static bool ValidateUserAndToken(string account, string token, ref UserType userType)
        {
            var result = false;
            if (!string.IsNullOrEmpty(account) && !string.IsNullOrEmpty(token))
            {
                UserTypeEnum type = UserTypeEnum.体验版;
                result = RdsCacheHelper.LstAccountCache.ValidateUserAndToken(account, token, ref type);
                if (result)
                {
                    userType = GetUserType(type);
                }
            }
            return result;
        }

        public static UserType GetUserInfo(UserTypeEnum userType)
        {
            var result = lstUserType.FirstOrDefault(p => Equals(p.Type, userType));
            return result;
        }

        public static string GetLimitInfo(UserTypeEnum userType, bool isLimitInfo, long todayCount, long todayTokenCount)
        {
            var result = lstUserType.Find(p => Equals(p.Type, userType));
            return result?.LimitDesc(todayCount, isLimitInfo, todayTokenCount);
        }
    }
}
