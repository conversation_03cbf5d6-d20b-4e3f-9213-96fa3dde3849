<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" lang="zh-CN">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=5.0">
    <title>PDF转Excel | 高精度表格识别转换器 | 保留原始格式</title>
    <meta name="description" content="免费高精度PDF转Excel工具，使用OCR智能识别技术将PDF文件中的表格数据转换为Excel工作表。精准保留原始格式和数据结构，适用于财务报表、统计数据及更多表格类文件。">
    <meta name="keywords" content="PDF转Excel,表格识别,PDF表格提取,数据转换工具,Excel数据导出,PDF数据提取,在线表格识别,OCR表格转换,表格结构分析,数据整理工具">
    <meta name="next-head-count" content="13">
    <link rel="preload" href="static/css/bf406b4dfe4a88f7.css" as="style">
    <link rel="stylesheet" href="static/css/bf406b4dfe4a88f7.css" data-n-g="">
    <link rel="preload" href="static/css/97e00c3eababac2c.css" as="style">
    <link rel="stylesheet" href="static/css/97e00c3eababac2c.css" data-n-p="">
    <link rel="preload" href="static/css/09a1ad1c616d180a.css" as="style">
    <link rel="stylesheet" href="static/css/09a1ad1c616d180a.css" data-n-p="">
    <link rel="preload" href="static/css/ccb4941a4753d970.css" as="style">
    <link rel="stylesheet" href="static/css/ccb4941a4753d970.css" data-n-p="">
    <noscript data-n-css=""></noscript>
    <script defer="" nomodule="" src="static/js/polyfills-c67a75d1b6f99dc8.js"></script>
    <script src="static/js/leads.js" defer="" data-nscript="beforeInteractive"></script>
    <script src="static/js/webpack-9e2dac2c71e11463.js" defer=""></script>
    <script src="static/js/framework-bb5c596eafb42b22.js" defer=""></script>
    <script src="static/js/main-ab39c4ec2bce69ad.js" defer=""></script>
    <script src="static/js/_app-d736fe5cc417ac5c.js" defer=""></script>
    <script src="static/js/75fc9c18-2e9ae03a475db518.js" defer=""></script>
    <script src="static/js/676-58ab10dc70b27a01.js" defer=""></script>
    <script src="static/js/937-f867bcf63c8b973c.js" defer=""></script>
    <script src="static/js/188-3e55a14c02731598.js" defer=""></script>
    <script src="static/js/387-5665b26f1097c66c.js" defer=""></script>
    <script src="static/js/[service]-238939d66f4ab4d3.js" defer=""></script>
</head>
<body>
    <div id="__next" data-reactroot="">
        <main class="y1fU5arT">
            <div>
                <main>
                    <div class="caFrXjpw">
                        <div class="_BEBSCQe">
                            <div class="_2iFAvTh"></div>
                            <div class="C_f2FiQb">OCR助手 <!-- -->PDF转Excel</div>
                        </div><div class="__9ksxAtFs">
                            <div class="ant-steps ant-steps-horizontal ant-steps-label-horizontal"><div class="ant-steps-item ant-steps-item-process ant-steps-item-active"><div class="ant-steps-item-container"><div class="ant-steps-item-tail"></div><div class="ant-steps-item-icon"><span class="ant-steps-icon">1</span></div><div class="ant-steps-item-content"><div class="ant-steps-item-title">上传文件</div></div></div></div><div class="ant-steps-item ant-steps-item-wait"><div class="ant-steps-item-container"><div class="ant-steps-item-tail"></div><div class="ant-steps-item-icon"><span class="ant-steps-icon">2</span></div><div class="ant-steps-item-content"><div class="ant-steps-item-title">在线转换</div></div></div></div><div class="ant-steps-item ant-steps-item-wait"><div class="ant-steps-item-container"><div class="ant-steps-item-tail"></div><div class="ant-steps-item-icon"><span class="ant-steps-icon">3</span></div><div class="ant-steps-item-content"><div class="ant-steps-item-title">导出结果</div></div></div></div></div><div class="axgiCdp5">
                                <span>
                                    <div class="ant-upload ant-upload-drag">
                                        <span tabindex="0" class="ant-upload ant-upload-btn" role="button">
                                            <input type="file" style="display:none" accept="application/pdf" multiple=""><div class="ant-upload-drag-container">
                                                <div>
                                                    <div class="R1Gkp8h8">
                                                        <span style="box-sizing:border-box;display:inline-block;overflow:hidden;width:initial;height:initial;background:none;opacity:1;border:0;margin:0;padding:0;position:relative;max-width:100%">
                                                            <span style="box-sizing:border-box;display:block;width:initial;height:initial;background:none;opacity:1;border:0;margin:0;padding:0;max-width:100%"><img style="display:block;max-width:100%;width:initial;height:initial;background:none;opacity:1;border:0;margin:0;padding:0" alt="" aria-hidden="true" src="data:image/svg+xml,%3csvg%20xmlns=%27http://www.w3.org/2000/svg%27%20version=%271.1%27%20width=%2772%27%20height=%2772%27/%3e"></span>
                                                            <img alt="pdf" src="static/image/pic-pdf.svg" decoding="async" data-nimg="intrinsic" style="position:absolute;top:0;left:0;bottom:0;right:0;box-sizing:border-box;padding:0;border:none;margin:auto;display:block;width:0;height:0;min-width:100%;max-width:100%;min-height:100%;max-height:100%">
                                                        </span><span class="alqJFG8O"><span style="box-sizing:border-box;display:inline-block;overflow:hidden;width:initial;height:initial;background:none;opacity:1;border:0;margin:0;padding:0;position:relative;max-width:100%"><span style="box-sizing:border-box;display:block;width:initial;height:initial;background:none;opacity:1;border:0;margin:0;padding:0;max-width:100%"><img style="display:block;max-width:100%;width:initial;height:initial;background:none;opacity:1;border:0;margin:0;padding:0" alt="" aria-hidden="true" src="data:image/svg+xml,%3csvg%20xmlns=%27http://www.w3.org/2000/svg%27%20version=%271.1%27%20width=%2726%27%20height=%2720%27/%3e"></span><img alt="to" src="data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7" decoding="async" data-nimg="intrinsic" style="position:absolute;top:0;left:0;bottom:0;right:0;box-sizing:border-box;padding:0;border:none;margin:auto;display:block;width:0;height:0;min-width:100%;max-width:100%;min-height:100%;max-height:100%"><noscript><img alt="to" srcset="static/image/icon-exchange.svg 1x, static/image/icon-exchange.svg 2x" src="static/image/icon-exchange.svg" decoding="async" data-nimg="intrinsic" style="position:absolute;top:0;left:0;bottom:0;right:0;box-sizing:border-box;padding:0;border:none;margin:auto;display:block;width:0;height:0;min-width:100%;max-width:100%;min-height:100%;max-height:100%" loading="lazy"></noscript></span></span><span style="box-sizing:border-box;display:inline-block;overflow:hidden;width:initial;height:initial;background:none;opacity:1;border:0;margin:0;padding:0;position:relative;max-width:100%"><span style="box-sizing:border-box;display:block;width:initial;height:initial;background:none;opacity:1;border:0;margin:0;padding:0;max-width:100%"><img style="display:block;max-width:100%;width:initial;height:initial;background:none;opacity:1;border:0;margin:0;padding:0" alt="" aria-hidden="true" src="data:image/svg+xml,%3csvg%20xmlns=%27http://www.w3.org/2000/svg%27%20version=%271.1%27%20width=%2772%27%20height=%2772%27/%3e"></span><img alt="excel" src="data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7" decoding="async" data-nimg="intrinsic" style="position:absolute;top:0;left:0;bottom:0;right:0;box-sizing:border-box;padding:0;border:none;margin:auto;display:block;width:0;height:0;min-width:100%;max-width:100%;min-height:100%;max-height:100%"><noscript><img alt="excel" srcset="static/image/pic-xls.svg 1x, static/image/pic-xls.svg 2x" src="static/image/pic-xls.svg" decoding="async" data-nimg="intrinsic" style="position:absolute;top:0;left:0;bottom:0;right:0;box-sizing:border-box;padding:0;border:none;margin:auto;display:block;width:0;height:0;min-width:100%;max-width:100%;min-height:100%;max-height:100%" loading="lazy"></noscript></span>
                                                    </div><div class="KCHi93Zn"><div class="__7C7cP_KT">点击上传文件 / 拖拽文件到此处</div><div class="bjbH0eff">可支持<!-- -->pdf<!-- -->格式文件</div><div class="bjbH0eff">上传单个文件大小不超过<!-- -->20<!-- -->M</div></div>
                                                </div>
                                            </div>
                                        </span>
                                    </div>
                                </span>
                            </div>
                        </div>
                    </div>
                </main>
            </div>
        </main>
    </div>
    <script id="__NEXT_DATA__" type="application/json">{"props":{"pageProps":{"pageInfo":{"from":"static/image/pic-pdf.svg","to":"static/image/pic-xls.svg","accept":[".pdf"],"target":".xls","title":"PDF转Excel","marketService":"pdf-to-excel","acceptMIME":["application/pdf"],"service":"pdf2excel"}},"__N_SSG":true},"page":"/transform/[service]","query":{"service":"pdf2excel"},"buildId":"fmSVWroe3Il4vJrWJlNDc","isFallback":false,"gsp":true,"scriptLoader":[]}</script>
</body>
</html>