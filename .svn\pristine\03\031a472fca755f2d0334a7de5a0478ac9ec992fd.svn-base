﻿using System.Net;

namespace NewTicket.Mobile
{
    public class ClsuserInfo
    {
        private bool isMobile = true;

        public bool IsMobile
        {
            get { return isMobile; }
            set { isMobile = value; }
        }

        public bool IsQuery { get; set; }

        public IPEndPoint NowIPPoint
        {
            get
            {
                return new IPEndPoint(IPAddress.Parse(StrNowIPPoint), 443);
            }
        }

        private string strNowIPPoint;

        public string StrNowIPPoint
        {
            get
            {
                if (string.IsNullOrEmpty(strNowIPPoint))
                {
                    strNowIPPoint = IPHelper.AutoChangeIP(IsMobile, "", false, true, IsQuery);
                }
                if (string.IsNullOrEmpty(strNowIPPoint))
                {
                    strNowIPPoint = IsMobile ? CommonString.StrLocalMobileIP : CommonString.StrLocalComIP;
                }
                if (string.IsNullOrEmpty(strNowIPPoint))
                {
                    strNowIPPoint = IPHelper.GetIP("mobile.12306.cn");
                }
                return strNowIPPoint;
            }
            set { strNowIPPoint = value; }
        }

        public string WL_InstanceId
        {
            get;
            set;
        }
        public string deviced_id
        {
            get;
            set;
        }
        private string deviced_No;

        public string Deviced_No
        {
            get
            {
                return CommonMethod.GetDeviceNo();
            }
            set { deviced_No = value; }
        }

        public string strinSession
        {
            get;
            set;
        }
        public string strCookie
        {
            get;
            set;
        }

        public string UserName { get; set; }

        public string PassWord { get; set; }

        public bool IsLogined { get; set; }
        public string ime
        {
            get;
            set;
        }
        public string mac
        {
            get;
            set;
        }
    }
}
