﻿using CommonLib;
using System;
using System.Collections.Generic;
using System.Collections.Specialized;
using System.Diagnostics;
using System.IO;
using System.Net;
using System.Text;
using System.Web;

namespace DocOcr
{
    public class SouGouRec : BaseDocOcrRec
    {
        public SouGouRec()
        {
            OcrGroup = OcrGroupType.搜狗;
            OcrType = DocOcrType.搜狗翻译;
            ResultType = ResutypeEnum.网页;
            MaxExecPerTime = 23;
            IsDownLoadFile = true;
            //文件上限2M
            FileSizeLimit = 1024 * 1024 * 10;
            IsSupportTrans = true;
            AllowUploadFileTypes = new List<string>() { "pdf", "doc" };//, "docx"
            //State = EnableState.禁用;
            InitLanguage();
        }

        #region 支持的语言

        Dictionary<string, string> LanguageTypeTransfer = new Dictionary<string, string>();

        //zh:简体中文
        //en:英文
        //ja:日语
        //ko:韩语

        private void InitLanguage()
        {
            TransLanguageDic = new Dictionary<TransLanguageTypeEnum, string>();
            //TransLanguageDic.Add(TransLanguageTypeEnum.自动, "auto");
            TransLanguageDic.Add(TransLanguageTypeEnum.中文, "zh");
            TransLanguageDic.Add(TransLanguageTypeEnum.英文, "en");
            TransLanguageDic.Add(TransLanguageTypeEnum.日语, "ja");
            TransLanguageDic.Add(TransLanguageTypeEnum.韩语, "ko");

            LanguageTypeTransfer = new Dictionary<string, string>();
            LanguageTypeTransfer.Add("中文->英文", "zh2en");
            LanguageTypeTransfer.Add("中文->日语", "zh2ja");
            LanguageTypeTransfer.Add("中文->韩语", "zh2ko");

            LanguageTypeTransfer.Add("英文->中文", "en2zh");
            LanguageTypeTransfer.Add("日语->中文", "ja2zh");
            LanguageTypeTransfer.Add("韩语->中文", "ko2zh");
        }

        #endregion

        protected override string GetHtml(OcrContent content)
        {
            var result = "";

            var transType = string.Format("{0}->{1}", content.from.ToString(), content.to.ToString());
            if (!LanguageTypeTransfer.ContainsKey(transType))
            {
                if (content.to.Equals(TransLanguageTypeEnum.自动))
                {
                    content.to = TransLanguageTypeEnum.英文;
                }
                if (content.from.Equals(TransLanguageTypeEnum.自动))
                {
                    content.from = content.to.Equals(TransLanguageTypeEnum.中文) ? TransLanguageTypeEnum.英文 : TransLanguageTypeEnum.中文;
                }
                if (!content.to.Equals(TransLanguageTypeEnum.中文))
                {
                    content.from = TransLanguageTypeEnum.中文;
                }
                else if (!content.from.Equals(TransLanguageTypeEnum.中文))
                {
                    content.to = TransLanguageTypeEnum.中文;
                }
                transType = string.Format("{0}->{1}", content.from.ToString(), content.to.ToString());
            }
            if (LanguageTypeTransfer.ContainsKey(transType))
            {
                var guid = Guid.NewGuid().ToString().Replace("-", "").ToLower();

                var byt = Convert.FromBase64String(content.strBase64);
                var tranFileType = ResultFileType == OcrFileType.PDF ? "pdf" : (ResultFileType == OcrFileType.Txt ? "txt" : "docx");

                if (PostFileResult(byt, LanguageTypeTransfer[transType], content.fileExt, guid))
                {
                    result = "{" +
                        string.Format("\"fileId\":\"{0}\",\"fileType\":\"{1}\",\"url\":\"{2}\""
                        , guid
                        , tranFileType
                        , string.Format("https://fanyi.sogou.com/user/document/preview?fileId={0}", guid)) + "}";
                }
            }
            return result;
        }

        private bool PostFileResult(byte[] content, string transType, string fileExt, string fileId)
        {
            try
            {
                var url = "http://fanyi.sogou.com/doc_trans/upload";
                var file = new UploadFileInfo()
                {
                    Name = "file_metas",
                    Filename = "1." + fileExt,
                    ContentType = ApplicationTypeHelper.GetApplicationType(fileExt),
                    Stream = new MemoryStream(content)
                };
                var values = new NameValueCollection() {
                { "file_id",fileId},
                { "trans_type",transType},
                //{ "file_metas","1."+fileExt}
            };
                var headers = new NameValueCollection()
                {
                    //{ "Referer","https://fanyi.sogou.com/document"},
                };
                var result = PostFile(url, new[] { file
    }, values, headers);
                if (!string.IsNullOrEmpty(result) && result.Contains("\"code\":\"0\""))
                {
                    return true;
                }
            }
            catch (Exception)
            {

            }
            return false;
        }

        private string GetFileResult(string taskId)
        {
            var result = "";
            try
            {
                var url = string.Format("https://fanyi.sogou.com/doc_trans/info?fid={0}&t={1}", taskId, DateTime.Now.Ticks);
                var html = WebClientSyncExt.GetHtml(url, "", "", "https://fanyi.sogou.com/document", ExecTimeOutSeconds);
                if (!string.IsNullOrEmpty(html))
                {
                    result = CommonHelper.SubString(html, "\"status_code\": \"", "\"");
                    if (string.IsNullOrEmpty(result))
                    {
                        result = CommonHelper.SubString(html, "\"status_code\":\"", "\"");
                    }
                }
            }
            catch (Exception)
            {

            }
            return result;
        }

        protected override ResultEntity GetProcessText(OcrContent content, string html)
        {
            //{"status":2
            //"is_preview_docx_ready":true,"is_preview_pdf_ready":true,"is_preview_uncomparison_docx_ready":true,"is_preview_uncomparison_pdf_ready":true
            //"is_full_docx_ready":true,"is_full_pdf_ready":true,"is_full_uncomparison_docx_ready":true,"is_full_uncomparison_pdf_ready":true
            //,"preview_status":2}
            //result = "1";
            var entity = base.GetFileResult(CommonHelper.SubString(html, "\"fileId\":\"", "\""));

            if (!string.IsNullOrEmpty(entity.autoText))
            {
                var oriDocFile = new DownLoadInfo()
                {
                    fileType = OcrFileType.Doc,
                    desc = "译文-Doc",
                    url = string.Format("https://fanyi.sogou.com/doc_trans/download?fid={0}", entity.autoText),
                };
                var oriPDFFile = new DownLoadInfo()
                {
                    fileType = OcrFileType.PDF,
                    desc = "译文-PDF",
                    url = string.Format("https://fanyi.sogou.com/doc_trans/download?fid={0}&pdf", entity.autoText),
                };
                var twoDocFile = new DownLoadInfo()
                {
                    fileType = OcrFileType.Doc,
                    desc = "双语-Doc",
                    url = string.Format("https://fanyi.sogou.com/doc_trans/download?fid={0}&dbl", entity.autoText),
                };
                var twoPDFFile = new DownLoadInfo()
                {
                    fileType = OcrFileType.PDF,
                    desc = "双语-PDF",
                    url = string.Format("https://fanyi.sogou.com/doc_trans/download?fid={0}&dbl&pdf", entity.autoText),
                };

                entity.viewUrl = twoDocFile.url;
                entity.files.Add(oriDocFile);
                entity.files.Add(oriPDFFile);
                entity.files.Add(twoDocFile);
                entity.files.Add(twoPDFFile);
            }
            return entity;
        }

        public override ProcessStateEntity QueryFileStatuMethod(string taskId)
        {
            var html = GetFileResult(taskId);
            var processStatus = new ProcessStateEntity()
            {
                state = OcrProcessState.未知状态,
                taskId = taskId
            };
            if (html.Equals("0"))
            {
                processStatus.state = OcrProcessState.处理中;
                processStatus.desc = string.Format("处理中(40%)，请稍后…");
            }
            else if (html.Equals("1"))
            {
                processStatus.state = OcrProcessState.处理中;
                processStatus.desc = string.Format("处理中(70%)，请稍后…");
            }
            else if (html.Equals("2"))
            {
                processStatus.state = OcrProcessState.处理中;
                processStatus.desc = string.Format("处理中(90%)，请稍后…");
            }
            else if (html.Equals("3"))
            {
                processStatus.state = OcrProcessState.处理成功;
                processStatus.desc = "处理完毕，可以下载了！";
            }
            else if (html.Equals("-1"))
            {
                processStatus.state = OcrProcessState.处理失败;
                processStatus.desc = "文档体积过大！翻译失败了...";
            }
            else if (html.Equals("-2") || html.Equals("-3"))
            {
                processStatus.state = OcrProcessState.处理失败;
                processStatus.desc = "非常抱歉！翻译失败了...";
            }
            else if (!string.IsNullOrEmpty(html))
            {
                processStatus.desc = "处理结果：" + html;
            }
            return processStatus;
        }
    }
}