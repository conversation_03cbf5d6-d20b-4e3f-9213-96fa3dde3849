﻿using CommonLib;
using System;
using System.Collections.Generic;
using System.Collections.Specialized;
using System.IO;
using System.Security.Cryptography;
using System.Text;
using System.Web;

namespace HanZiOcr
{
    /// <summary>
    /// 有道文档核对
    /// https://ai.youdao.com/saas/eBook#/upload-image
    /// https://ai.youdao.com/saas/eBook/static/js/4.js
    /// </summary>
    public class YouDaoDocRec : BaseOcrRec
    {
        public YouDaoDocRec()
        {
            OcrGroup = OcrGroupType.有道;
            OcrType = HanZiOcrType.有道文档;
            MaxExecPerTime = 26;

            LstJsonPreProcessArray = new List<object>() { "resRegions" };
            LstJsonNextProcessArray = new List<object>() { "lines", "words" };

            StrResultJsonSpilt = "word";
            IsSupportVertical = true;
            LstVerticalLocation = new List<object>() { "boundingBox" };
        }

        protected override void SetProcessArray(string html)
        {
            var isEnglish = !string.IsNullOrEmpty(html) && Equals(OcrHtmlProcess.GetLang(html), "en");
            StrContactCell = isEnglish ? " " : "";
        }

        protected override string GetHtml(OcrContent content)
        {
            string time = UnixTime();
            var sign = generateSign(content.strBase64, time);
            var url = "https://aidemo.youdao.com/lineocr?clientele=mdict&salt=" + time + "&sign=" + sign + "&keyfrom=fanyi.2.7.1.desk&option=donot_rotate_box&imei=imei&option=detect360";
            var result = WebClientSyncExt.GetHtml(url, "", "imgBase=" + HttpUtility.UrlEncode(content.strBase64), "https://ai.youdao.com", ExecTimeOutSeconds);
            return result;
        }

        readonly string secretKey = "1cZjSkSvQf]02b8R$L%Q";
        readonly string clientele = "mdict";

        //生成sign
        //sing规则：clientele的取值 + input信息 + salt(随机数) + secretKey(私钥) ==> 取md5的值
        //input信息为 input前10个字符 + input长度 + input后十个字符（当input长度大于20）或 input字符串（当input长度小于等于20）
        private string generateSign(string input, string salt)
        {
            var inputLength = input.Length;
            var headerInput10 = input.Substring(0, 10);
            var tailInput10 = input;
            if (inputLength > 20)
            {
                tailInput10 = input.Substring(input.Length - 10, 10);
            }
            var toMd5Input = string.Format("${0}${1}${2}${3}${4}${5}"
                , clientele, headerInput10, inputLength, tailInput10, salt, secretKey);
            //"mdict/9j/4AAQSk69388xjCL//2Q==17071998758741cZjSkSvQf]02b8R$L%Q"
            return ToMd5(toMd5Input);
        }

        private string appSign(YouDaoAccount account, string time)
        {
            string plainText = GetSignKey(account?.strAppId, account?.strSecretId, time);

            return ToMd5(plainText);
        }

        private string ToMd5(string strContent)
        {
            string result;
            if (strContent == null)
            {
                result = null;
            }
            else
            {
                MD5 md = MD5.Create();
                byte[] array = md.ComputeHash(Encoding.UTF8.GetBytes(strContent));
                StringBuilder stringBuilder = new StringBuilder();
                for (int i = 0; i < array.Length; i++)
                {
                    stringBuilder.Append(array[i].ToString("x2"));
                }
                result = stringBuilder.ToString();
            }
            return result;
        }

        private string GetSignKey(string appid, string secretid, string stime)
        {
            return string.Format("{0}{2}{1}", appid, secretid, stime);
        }

        private string UnixTime()
        {
            return Convert.ToInt64((DateTime.UtcNow - new DateTime(1970, 1, 1, 0, 0, 0, 0)).TotalSeconds * 1000).ToString();
        }

    }
}