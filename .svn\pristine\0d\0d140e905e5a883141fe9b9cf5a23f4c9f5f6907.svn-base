﻿using CommonLib;
using System;
using System.Data;
using System.IO;
using System.IO.Compression;
using System.Net;
using System.Threading;
using System.Xml;

namespace Code.Process.Web
{
    public class CommonUpdate
    {
        private static Thread _updateMainThread;

        private static bool _isOnUpdate;

        public static void InitUpdate()
        {
            if (_updateMainThread != null)
                try
                {
                    _updateMainThread.Abort();
                    _updateMainThread = null;
                }
                catch
                {
                }

            try
            {
                InitUpdateService();
            }
            catch
            {
            }
        }

        private static void InitUpdateService()
        {
            var timerInfo = new TimerInfo
            {
                TimerType = "LoopMinutes",
                DateValue = 10,
                IsExecFirst = true,
                Param = ServerInfo.HostAccount?.FullUrl + "update/Code.Process.Web.xml",
                DtNow = ServerInfo.DtNowVersion,
                TaskName = "UpdateTask"
            };
            _updateMainThread = TimerTaskService.CreateTimerTaskService(timerInfo, UpdateMethod);
            _updateMainThread.Start();
        }

        private static bool UpdateMethod(DateTime dtDate, string url)
        {
            bool result = false;
            try
            {
                if (_isOnUpdate)
                {
                    return result;
                }

                _isOnUpdate = true;
                UpdateEntity updateEntity = null;
                result = IsHasNew(url, dtDate, null, ref updateEntity);
                if (result)
                {
                    LogHelper.Log.Error("发现有更新，开始下载！");
                    var _strTmpFile = Path.Combine(BasePath, "Code.Process.Web.zip");
                    if (DownLoadCache(updateEntity.strURL, _strTmpFile) && File.Exists(_strTmpFile))
                    {
                        try
                        {
                            LogHelper.Log.Error("下载完毕，开始更新！");
                            if (!Directory.Exists(UpdatePath))
                            {
                                Directory.CreateDirectory(UpdatePath);
                            }
                            try
                            {
                                LogHelper.Log.Error("开始解压缩！");
                                ZipFile.ExtractToDirectory(_strTmpFile, UpdatePath);
                                try
                                {
                                    LogHelper.Log.Error("开始更新！");
                                    RunUpdate("runas /trustlevel:0x20000 \"UpdateSite.bat\"");
                                }
                                catch { }
                                try
                                {
                                    Thread.Sleep(2000);
                                    RunUpdate("explorer \"UpdateSite.bat\"");
                                }
                                catch { }
                                try
                                {
                                    Thread.Sleep(2000);
                                    RunUpdate("\"UpdateSite.bat\"");
                                }
                                catch (Exception oe)
                                {
                                    LogHelper.Log.Error("执行更新出错！", oe);
                                }
                                LogHelper.Log.Error("更新完成");
                                result = true;
                            }
                            catch (Exception oe)
                            {
                                LogHelper.Log.Error("解压缩更新文件出错！", oe);
                            }
                        }
                        catch (Exception oe)
                        {
                            LogHelper.Log.Error("更新失败！", oe);
                        }
                    }
                    else
                    {
                        LogHelper.Log.Error("下载文件失败！");
                    }
                }
            }
            catch (Exception oe)
            {
                LogHelper.Log.Error("检查更新错误", oe);
            }
            finally
            {
                _isOnUpdate = false;
            }
            return result;
        }

        static string BasePath = AppDomain.CurrentDomain.BaseDirectory;
        static string UpdateDirectoryName = "Code.Process.Web";
        static string UpdatePath = Path.Combine(BasePath, UpdateDirectoryName);

        static void RunUpdate(string str)
        {
            //process用于调用外部程序
            var p = new System.Diagnostics.Process();
            ////调用cmd.exe
            p.StartInfo.FileName = "cmd.exe";
            //p.StartInfo.Verb = "runas";

            //不显示程序窗口
            p.StartInfo.CreateNoWindow = true;
            //是否指定操作系统外壳进程启动程序
            p.StartInfo.UseShellExecute = false;
            //可能接受来自调用程序的输入信息
            //重定向标准输入
            p.StartInfo.RedirectStandardInput = true;
            //重定向标准输出
            p.StartInfo.RedirectStandardOutput = true;
            //重定向错误输出
            p.StartInfo.RedirectStandardError = true;

            p.StartInfo.WorkingDirectory = BasePath;

            p.Start();
            //输入命令
            p.StandardInput.WriteLine(str);
            //一定要关闭。
            p.StandardInput.WriteLine("exit");
            //xcopy /s /y Code.Process.Web\* . && rmdir /s /q Code.Process.Web
        }

        private static bool DownLoadCache(string _strDownUrl, string _strTmpFile)
        {
            if (string.IsNullOrEmpty(_strDownUrl))
            {
                return true;
            }
            try
            {
                if (File.Exists(_strTmpFile))
                    File.Delete(_strTmpFile);
                using (Stream so = new FileStream(_strTmpFile, FileMode.Create))
                {
                }
            }
            catch (Exception oe)
            {
                LogHelper.Log.Error("获取文件权限失败！", oe);
                return false;
            }

            return DownloadFile(_strDownUrl, _strTmpFile);
        }

        /// <summary>
        /// c#,.net 下载文件
        /// </summary>
        /// <param name="url">下载文件地址</param>
        /// <param name="Filename">下载后的存放地址</param>
        /// <param name="Prog">用于显示的进度条</param>
        static bool DownloadFile(string url, string filename)
        {
            var result = false;
            try
            {
                var client = new WebClient();
                client.DownloadFileAsync(new Uri(url), filename);
                while (client.IsBusy)
                {
                    Thread.Sleep(1000);
                }
                result = true;
            }
            catch (Exception e)
            {
                LogHelper.Log.Error("下载文件失败！", e);
                Console.WriteLine(e);
            }
            return result;
        }


        public static bool IsHasNew(string url, DateTime dtNowDate, string desc, ref UpdateEntity updateNew)
        {
            var result = false;
            try
            {
                if (!string.IsNullOrEmpty(url))
                {
                    updateNew = LoadVersionInfo(url, desc);
                    if (updateNew != null)
                    {
                        result = dtNowDate < updateNew?.dtNewDate;
                    }
                }
            }
            catch
            {
            }

            return result;
        }

        /// <summary>
        /// 将Xml内容字符串转换成DataSet对象
        /// </summary>
        /// <param name="xmlStr"></param>
        /// <returns></returns>
        private static DataSet CXmlToDataSet(string xmlStr)
        {
            if (string.IsNullOrEmpty(xmlStr)) return null;
            StringReader strStream = null;
            XmlTextReader xmlrdr = null;
            try
            {
                var ds = new DataSet();
                //读取字符串中的信息
                strStream = new StringReader(xmlStr);
                //获取StrStream中的数据
                xmlrdr = new XmlTextReader(strStream);
                //ds获取Xmlrdr中的数据
                ds.ReadXml(xmlrdr);
                return ds;
            }
            finally
            {
                //释放资源
                if (xmlrdr != null)
                {
                    xmlrdr.Close();
                    strStream.Close();
                    strStream.Dispose();
                }
            }
        }

        private static UpdateEntity LoadVersionInfo(string url, string desc)
        {
            UpdateEntity updateNew = null;

            if (url.Contains(".zip") || url.Contains(".exe") || url.Contains(".lang"))
            {
                try
                {
                    DateTime newDate;
                    using (var client = new WebClient() { Proxy = null })
                    {
                        client.OpenRead(url);
                        newDate = BoxUtil.GetDateTimeFromObject(client.ResponseHeaders["Last-Modified"]);
                    }

                    updateNew = new UpdateEntity
                    {
                        strNewVersion = "*******",
                        dtNewDate = newDate.Date,
                        strContext = desc,
                        strURL = url,
                        strFullURL = url,
                        IsForceUpdate = false
                    };
                }
                catch (Exception e)
                {
                    Console.WriteLine(e);
                }
            }
            else
            {
                var strUpdate = WebClientSyncExt.GetHtml(url + "?t=" + DateTime.Now.Ticks, 30);
                try
                {
                    var dsTemp = CXmlToDataSet(strUpdate);
                    if (dsTemp != null)
                    {
                        var row = dsTemp.Tables.Count > 0 && dsTemp.Tables[0].Rows.Count > 0 ? dsTemp.Tables[0].Rows[0] : null;
                        if (row != null)
                            updateNew = new UpdateEntity
                            {
                                strNewVersion = row["strNowVersion"].ToString(),
                                dtNewDate = BoxUtil.GetDateTimeFromObject(row["dtNewDate"]?.ToString()),
                                strMinVersion = row["strMinVersion"]?.ToString(),
                                dtMinDate = BoxUtil.GetDateTimeFromObject(row["dtMinDate"]?.ToString()),
                                strContext = row["strContext"].ToString()
                                    .Replace("|", Environment.NewLine),
                                strURL = row["strURL"].ToString(),
                                strFullURL = row["strFullURL"].ToString(),
                                IsForceUpdate = BoxUtil.GetBooleanFromObject(row["IsNowForce"].ToString(), false)
                            };
                        dsTemp.Dispose();
                    }
                }
                catch (Exception oe)
                {
                    Console.WriteLine(oe.Message);
                }
            }

            return updateNew;
        }
    }

    public class UpdateEntity
    {
        public string strNewVersion { get; internal set; }
        public DateTime dtNewDate { get; internal set; }
        public string strMinVersion { get; internal set; }
        public DateTime dtMinDate { get; internal set; }
        public string strContext { get; internal set; }
        public string strURL { get; internal set; }
        public string strFullURL { get; internal set; }
        public bool IsForceUpdate { get; set; }
    }
}