﻿using CommonLib;
using log4net;
using Newtonsoft.Json;
using System;
using System.Collections.Concurrent;
using System.Collections.Specialized;
using System.Diagnostics;
using System.Threading;
using System.Threading.Tasks;
using System.Web;

namespace Code.Process.Common
{
    public class ProcessNew
    {
        #region 构造函数

        static ProcessNew()
        {
            _Log = LogManager.GetLogger("ServicesManager");
        }

        #endregion

        protected static ILog _Log;
        public static bool IsExit = false;

        private static ConcurrentDictionary<string, OcrTimeEntity> OcrTimeCache = new ConcurrentDictionary<string, OcrTimeEntity>();

        public static void InitOcrEngine()
        {
            HanZiOcr.ConstHelper.Init();
            MathOcr.ConstHelper.Init();
            TableOcr.ConstHelper.Init();
            DocOcr.ConstHelper.Init();
            TransOcr.ConstHelper.Init();
            Task.Factory.StartNew(() =>
            {
                BaseRecHelper.ReportToServer();
            });
        }

        /// <summary>
        /// 启动进程
        /// </summary>
        public static void StartProcess(bool isSync = true)
        {
            //var strInfo = "";
            //strInfo +="=============\n"+ HanZiOcr.BaiDuAIRec.GetRecImg();
            //strInfo += "=============\n" + HanZiOcr.BaiDuAPIRec.GetRecImg();
            //strInfo += "=============\n" + HanZiOcr.BaiDuRec.GetRecImg();
            //strInfo += "=============\n" + HanZiOcr.BaiDuTuShuRec.GetRecImg();
            //strInfo += "=============\n" + HanZiOcr.BingImageRec.GetRecImg();
            //strInfo += "=============\n" + HanZiOcr.MicroSoftRec.GetRecImg();
            _Log.Info("消息接收主线程开启成功");
            Task.Factory.StartNew(() =>
            {
                BeginReceiveMessage();
            });
            Task.Factory.StartNew(() =>
            {
                BeginReceiveFileStatusMessage();
            });
            OcrProcessThread();

            CommonClientRequest.InitSignalRClient();
            CommonClientRequest.OnWaitOcrHasResult += (content, e) =>
            {
                ProcessOcrByImg(content as string, false);
            };

            CommonClientRequest.OnWaitFileHasResult += (content, e) =>
            {
                ProcessFileResultByStr(content as string);
            };
        }

        public static void SendOcrResult(OcrContent content)
        {
            content.processVersion = ServerInfo.DtNowVersion.Ticks;
            OcrRsultPool.Add(content);
        }

        public static void ProcessFileResult(ProcessStateEntity content)
        {
            FileResultPool.Add(content);
        }

        /// <summary>
        /// 停止进程
        /// </summary>
        public static void StopProgress()
        {
            try
            {
                IsExit = true;
                _Log.Info("消息接收主线程停止");
            }
            catch (Exception ex)
            {
                _Log.Error(ex.Message);
            }
        }

        /// <summary>
        /// 汇报开始处理池
        /// </summary>
        private static readonly BlockingCollection<CusImageEntity> OcReportPool = new BlockingCollection<CusImageEntity>();

        /// <summary>
        /// OCR处理池
        /// </summary>
        private static readonly BlockingCollection<CusImageEntity> OcrProcessPool = new BlockingCollection<CusImageEntity>();

        /// <summary>
        /// OCR结果池
        /// </summary>
        private static readonly BlockingCollection<OcrContent> OcrRsultPool = new BlockingCollection<OcrContent>();

        /// <summary>
        /// OCR处理池
        /// </summary>
        private static readonly BlockingCollection<ProcessStateEntity> FileResultPool = new BlockingCollection<ProcessStateEntity>();

        private static void OcrProcessThread()
        {
            new Thread(p =>
            {
                try
                {
                    foreach (var processEntity in OcrProcessPool.GetConsumingEnumerable())
                    {
                        CommonProcess.AddToProcess(processEntity);
                    }
                }
                catch { }
            })
            { IsBackground = true, Priority = ThreadPriority.Highest }.Start();
            new Thread(async p =>
            {
                try
                {
                    foreach (var processEntity in OcReportPool.GetConsumingEnumerable())
                    {
                        var currentTicks = ServerTime.DateTime.Ticks;
                        // 处理时间记录
                        try
                        {
                            OcrTimeCache.TryGetValue(processEntity.StrIndex, out var timeEntity);
                            timeEntity = timeEntity ?? new OcrTimeEntity();
                            timeEntity.OcrServerReported = currentTicks;
                            OcrTimeCache[processEntity.StrIndex] = timeEntity;
                        }
                        catch { }

                        //var strTmp = await WebClientSyncExt.GetHtmlAsync(ServerInfo.HostAccount?.FullUrl + "code.ashx?op=reportbeginprocess&server=" + HttpUtility.UrlEncode(ConfigHelper.OcrServer) + "&id=" + processEntity.StrIndex + "&start=" + processEntity.DtAdd + "&push=" + processEntity.DtOcrServerGet + "&receive=" + processEntity.DtReceived + "&current=" + currentTicks + "&version=" + ServerInfo.DtNowVersion.Ticks, "", 15);
                        var extQuery = "&id=" + processEntity.StrIndex
                            + "&start=" + processEntity.OcrTime.ServerReceivedUserRequest
                            + "&push=" + processEntity.OcrTime.ServerAlloted
                            + "&receive=" + processEntity.OcrTime.OcrServerAccepted
                            + "&current=" + ServerTime.DateTime.Ticks;
                        
                        var strTmp = await CommonClientRequest.WaitResult("reportbeginprocess", 15, string.Empty, extQuery, MessageSendMode.Parallel);
                        _Log.InfoFormat("反馈Ocr Server Id:{0},Result:{1}", processEntity.StrIndex, strTmp);
                    }
                }
                catch { }
            })
            { IsBackground = true, Priority = ThreadPriority.AboveNormal }.Start();
            new Thread(async p =>
            {
                try
                {
                    foreach (var content in OcrRsultPool.GetConsumingEnumerable())
                    {
                        // OCR结果汇报给Server
                        try
                        {
                            if (!string.IsNullOrEmpty(content?.result?.autoText) || content.result?.files?.Count > 0)
                            {
                                try
                                {
                                    OcrTimeCache.TryGetValue(content.id, out var timeEntity);
                                    timeEntity = timeEntity ?? new OcrTimeEntity();
									if (content.OcrTime == null)
									{
										content.OcrTime = timeEntity;
									}
									else if (timeEntity.OcrServerReported > 0)
									{
										// 如果缓存中有"客户端向服务端报告已接收"的时间，则保留它
										content.OcrTime.OcrServerReported = timeEntity.OcrServerReported;
									}

									// 设置客户端开始报告结果的时间
									content.OcrTime.OcrServerReportedResult = ServerTime.DateTime.Ticks;
                                }
                                catch { }
                                content.Server = ConfigHelper.OcrServer;
                                Stopwatch stopwatch = Stopwatch.StartNew();
                                var strPost = JsonConvert.SerializeObject(content, CommonHelper.NullJsonValueIngoreSetting);
                                var headers = new NameValueCollection
                                {
                                    { "Content-Encoding", "gzip" },
                                    { "Content-Type", "application/octet-stream" }
                                };
                                var result = CommonClientRequest.WaitResult("ocrresult", 15, strPost, string.Empty, MessageSendMode.Parallel);
                                //var result = await WebClientSyncExt.GetHtmlAsync(ServerInfo.HostAccount?.FullUrl + "code.ashx?op=ocrresult" + "&version=" + ServerInfo.DtNowVersion.Ticks, strPost, 15, headers);
                                _Log.Info($"添加OCR结果-{content.processName}:{result},耗时:{stopwatch.ElapsedMilliseconds.ToString("F2")}ms");
                            }
                        }
                        catch (Exception oe)
                        {
                            _Log.Error("添加OCR结果异常！" + oe.Message, oe);
                        }
                    }
                }
                catch { }
            })
            { IsBackground = true, Priority = ThreadPriority.Highest }.Start();
            new Thread(async p =>
            {
                try
                {
                    foreach (var content in FileResultPool.GetConsumingEnumerable())
                    {
                        // 文件状态查询结果给Server
                        try
                        {
                            Stopwatch stopwatch = Stopwatch.StartNew();
                            var strPost = JsonConvert.SerializeObject(content, CommonHelper.NullJsonValueIngoreSetting);
                            var headers = new NameValueCollection
                        {
                            { "Content-Encoding", "gzip" },
                            { "Content-Type", "application/octet-stream" }
                        };
                            var result = CommonClientRequest.WaitResult("filestateresult", 15, strPost, "", MessageSendMode.Parallel);
                            //var result = await WebClientSyncExt.GetHtmlAsync(ServerInfo.HostAccount?.FullUrl + "code.ashx?op=filestateresult" + "&version=" + ServerInfo.DtNowVersion.Ticks, strPost, 15, headers);
                            _Log.Info($"添加文件状态结果:{result},耗时:{stopwatch.ElapsedMilliseconds.ToString("F2")}ms");
                        }
                        catch (Exception oe)
                        {
                            _Log.Error("添加文件结果异常！" + oe.Message, oe);
                        }
                    }
                }
                catch { }
            })
            { IsBackground = true, Priority = ThreadPriority.AboveNormal }.Start();
        }

        private static Random random = new Random();
		
        /// <summary>
        /// 接收消息
        /// </summary>
        public async static void BeginReceiveMessage()
        {
            //System.Threading.Thread.Sleep(30 * 1000);
            //var bytes = System.IO.File.ReadAllBytes(@"D:\助手\Image\0108\Old\11.jpg");//***********.jpg");
            //var tmp = DaMaLib.ZhuShouDaMaHelper.GetCodeByBytes(bytes, false, SiteFlag.助手);

            Console.WriteLine($"处理线程ID：{Thread.CurrentThread.ManagedThreadId}\n");
            while (!IsExit)
            {
                try
                {
                    _Log.InfoFormat("{0} 开始接收OCR消息", ServerTime.DateTime.ToString("HH:mm:ss"));
                    try
                    {
                        var timeOutSec = random.Next(30, 60);
                        var strWaitOcr = await CommonClientRequest.WaitResult("waitocr", timeOutSec, string.Empty, string.Empty, MessageSendMode.Parallel);
                        if (!string.IsNullOrEmpty(strWaitOcr) && strWaitOcr.Length > 50)
                        {
                            ProcessOcrByImg(strWaitOcr, false);
                        }
                        else
                        {
                            GC.Collect();
                        }
						await Task.Delay(random.Next(3000, 5000));
                    }
                    catch (Exception ex)
                    {
                        _Log.Error("接收消息消息并分配线程时出错,错误原因如下:", ex);
                        Thread.Sleep(1000);
                    }
                }
                catch (Exception ex)
                {
                    _Log.Error("轮询消息接收消息时出错,错误原因如下:", ex);
                }
            }
        }

        public static void ProcessOcrByImg(string strWaitOcr, bool isDebug = false)
        {
            if (string.IsNullOrEmpty(strWaitOcr)) return;

            CusImageEntity img = null;
            var dtReceived = ServerTime.DateTime.Ticks;
            try
            {
                img = JsonConvert.DeserializeObject<CusImageEntity>(strWaitOcr);
                // 将接收时间记录到OcrTime中
                img.OcrTime.OcrServerAccepted = dtReceived;
				
                if (isDebug)
                {
                    img.OcrTime.UserStartRequest = dtReceived;
                    img.OcrTime.ServerReceivedUserRequest = dtReceived;
                    img.OcrTime.ServerAlloted = dtReceived;
                    img.OcrTime.OcrServerAccepted = dtReceived;
                    img.DtExpired = ServerTime.DateTime.AddMinutes(10).Ticks;
                }
            }
            catch (Exception oe)
            {
                _Log.Error("反序列化失败！", oe);
            }

            //    Console.WriteLine(string.Format("Now:{0},DtAdd:{1},Expire:{2}"
            //, ServerTime.DateTime.ToString("HH:mm:ss fff"), new DateTime(img.DtAdd).ToString("HH:mm:ss fff"), new DateTime(img.DtExpired).ToString("HH:mm:ss fff")));
            if (img != null && img.IsValidate)
            {
                OcrTimeCache.TryAdd(img.StrIndex, new OcrTimeEntity()
                {
                    UserStartRequest = img.OcrTime.UserStartRequest,
                    ServerReceivedUserRequest = img.OcrTime.ServerReceivedUserRequest,
                    ServerAlloted = img.OcrTime.ServerAlloted,
                    OcrServerAccepted = img.OcrTime.OcrServerAccepted
                });
                // 收到消息开始处理
                OcrProcessPool.Add(img);
                OcReportPool.Add(img);
            }
        }

        /// <summary>
        /// 接收消息
        /// </summary>
        public async static void BeginReceiveFileStatusMessage()
        {
            Console.WriteLine(string.Format("处理线程ID：{0}\n", Thread.CurrentThread.ManagedThreadId));
            while (!IsExit)
            {
                try
                {
                    _Log.InfoFormat("{0} 开始接收文件状态消息", ServerTime.DateTime.ToString("HH:mm:ss"));

                    #region 单线程Block

                    try
                    {
                        var timeOutSec = random.Next(30, 60);
                        var strWaitFileState = await CommonClientRequest.WaitResult("waitfilestate", timeOutSec, string.Empty, string.Empty, MessageSendMode.Parallel);
                        ProcessFileResultByStr(strWaitFileState);
                        await Task.Delay(random.Next(3000, 5000));
                    }
                    catch (Exception ex)
                    {
                        _Log.Error("接收消息消息并分配线程时出错,错误原因如下:", ex);
                        System.Threading.Thread.Sleep(1000);
                    }

                    #endregion
                }
                catch (Exception ex)
                {
                    _Log.Error("轮询消息接收消息时出错,错误原因如下:", ex);
                }
            }
        }

        private static void ProcessFileResultByStr(string strWaitFileState)
        {
            if (!string.IsNullOrEmpty(strWaitFileState) && strWaitFileState.Length > 10)
            {
                try
                {
                    var img = JsonConvert.DeserializeObject<CusFileStatusEntity>(strWaitFileState);
                    CommonProcess.AddToFileStatusProcess(img);
                }
                catch (Exception oe)
                {
                    _Log.Error("反序列化失败！", oe);
                }
            }
        }
    }
}
