$(document).ready((function(){var t=function(){new Swiper(".brand-year .swiper-container",{loop:!1,autoplay:0,slidesPerView:4,spaceBetween:0,prevButton:".swiper-prev",nextButton:".swiper-next",simulateTouch:!1,breakpoints:{1199:{slidesPerView:3},991:{slidesPerView:2}}})};({activeIndex:-1,list:[{title:"效率",text:"一站式云通信平台，采用模块化的界面设计，简洁、直观、易用。不需进行繁琐的配置与开发，十分钟可快速接入各类编程语言的 API 接口。"},{title:"产品",text:"赛邮云以“用户至上”为理念打造数十个产品服务，并在不断地开发新产品。产品之间保持独立又进行互通整合， 一个账户即可轻松应用所有产品服务。"},{title:"发送",text:"赛邮云作为企业级 PaaS 平台，为用户提供多种不同的发送模式和使用方式。从而不受设备/网络/位置等因素限制，也能完成高性能、高速率、高质量的发送需求。"},{title:"成本",text:"用户可按需采购，按量计费，购买的额度没有时间限制。量大价优，无需押金；透明定价，合理计费，并可随时查看发送报告和计费日志，了解费用明细。"},{title:"安全",text:"赛邮云经 ISO9001 和 ISO27001 权威认证，具备云通信平台所需的三级等保和其他相关资质证件，并与全球大多数运营商保持实时互联，正规安全无风险。"},{title:"服务",text:"赛邮云建立了多对一服务体系和 SUBMAIL OMC 保障机制，实行 7X24 小时全天候待命机制，提供高效、贴心的服务与技术支持，努力让每一个用户满意。"}],init:function(){this.initDots()},initDots:function(){var t=this,e="";$.each(this.list,(function(t,i){e+=`\n          <div class="dots-item">\n              <div class="text">${i.title}</div>\n          </div>\n        `})),$(".why-container .dots").empty().append(e),$(".why-container .dots-item").on("click",(function(){t.setDot($(this).index())})),this.setDot(0)},setDot:function(t){if(t!=this.activeIndex){this.activeIndex=t,$(".why-container .dots-item").removeClass("active"),$(".why-container .dots-item").eq(t).addClass("active");var e=`\n        <h5 class="mt-4 fn24">${this.list[t].title}</h5>\n        <p class="color-gray mt-3 text">${this.list[t].text}</p>\n        <a id="webchat" class="my-button mt-4 disa" type="primary" size="large" style="width:120px;transform: translateY(-20px);">立即咨询</a>\n      `;$(".why-container .why-content").empty().append(e)}}}).init(),t()}));