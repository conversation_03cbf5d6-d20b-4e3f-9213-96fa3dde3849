﻿using Code.Process.Common;
using CommonLib;
using log4net.Config;
using System;
using System.Collections.Generic;
using System.IO;
using System.Threading;
using System.Threading.Tasks;
using System.Windows.Forms;
using HanZiOcr;

namespace Code.Process.Console
{
    public delegate string GetHanZiDele(string strTmp);
    class Program
    {

        //static void TestTime()
        //{
        //    while (true)
        //    {
        //        var off1 = NtpClient.Instance.GetNetworkTimeOffset();
        //        var off2 = SNtpClient.Instance.GetNetworkTimeOffset();
        //        System.Console.WriteLine(string.Format("Tick0:{0}   OffSet:{1}ms    time:{2}"
        //            , Math.Abs(off1 - off2), TimeSpan.FromTicks(Math.Abs(off1 - off2)).TotalMilliseconds, DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss fff")));
        //        System.Console.WriteLine(string.Format("Tick1:{0}   OffSet:{1}ms    time:{2}"
        //            , off1, TimeSpan.FromTicks(off1).TotalMilliseconds, DateTime.Now.AddTicks(off1).ToString("yyyy-MM-dd HH:mm:ss fff")));
        //        System.Console.WriteLine(string.Format("Tick2:{0}   OffSet:{1}ms    time:{2}"
        //            , off2, TimeSpan.FromTicks(off2).TotalMilliseconds, DateTime.Now.AddTicks(off2).ToString("yyyy-MM-dd HH:mm:ss fff")));
        //        System.Console.WriteLine();
        //        System.Threading.Thread.Sleep(1000);
        //    }
        //}

        //static string DoProcess(BaseOcrRec ocr, string strTmp, int times = 10)
        //{
        //    var strResult = "";
        //    long minTime = -1;
        //    long maxTime = -1;
        //    int errorTimes = 0;
        //    long dtStart = DateTime.Now.Ticks;

        //    long totalExecTimes = 0;
        //    try
        //    {
        //        ocr.OnStart -= Ocr_OnStart;
        //        ocr.OnStart += Ocr_OnStart;
        //        ocr.OnError -= Ocr_OnError;
        //        ocr.OnError += Ocr_OnError;
        //        ocr.OnCompleted -= Ocr_OnCompleted;
        //        ocr.OnCompleted += Ocr_OnCompleted;
        //        Parallel.For(0, times, p =>
        //        {
        //            var content = new OcrContent() { strBase64 = strTmp };
        //            content = ocr?.GetResult(content)?.Result;
        //            //System.Console.WriteLine(string.Format("{0}第{1}次识别：{2}", ocrType, p + 1, st));
        //            var execTime = content.endTicks - content.startTicks;
        //            if (minTime < 0 || execTime < minTime)
        //            {
        //                minTime = execTime;
        //            }
        //            if (maxTime < 0 || execTime > maxTime)
        //            {
        //                maxTime = execTime;
        //            }
        //            if (string.IsNullOrEmpty(content?.result?.html))
        //            {
        //                errorTimes++;
        //            }
        //            totalExecTimes += execTime;
        //            //System.Console.WriteLine(string.Format("【{0}】识别结果：{1}，耗时：{2}ms", content?.processId, content?.result, new TimeSpan(execTime).TotalMilliseconds.ToString("F0")));
        //            //System.Console.WriteLine(st);
        //        });
        //    }
        //    catch (AggregateException ex)
        //    {
        //        // enumerate the exceptions that have been aggregated
        //        foreach (Exception inner in ex.InnerExceptions)
        //        {
        //            System.Console.WriteLine("Exception type {0} from {1}",
        //            inner.GetType(), inner.Source);
        //        }
        //    }
        //    var ts = new TimeSpan(DateTime.Now.Ticks - dtStart);
        //    var tsMin = new TimeSpan(minTime);
        //    var tsMax = new TimeSpan(maxTime);
        //    System.Console.WriteLine(string.Format("{0}识别共{1}次，总耗时：{2}ms，平均耗时：{3}ms，最低耗时：{4}ms，最多耗时：{5}ms，失败次数：{6}"
        //        , ocr.OcrType, times, ts.TotalMilliseconds.ToString("F0"), Math.Floor(new TimeSpan(totalExecTimes).TotalMilliseconds / times).ToString("F0")
        //        , tsMin.TotalMilliseconds.ToString("F0"), tsMax.TotalMilliseconds.ToString("F0"), errorTimes));

        //    return strResult;
        //}

        //private static void Ocr_OnCompleted(object sender, CommonLib.Events.OnCompletedEventArgs e)
        //{
        //    System.Console.WriteLine("处理完成：【{0}】-{1},State:{2},ExecTime:{3}ms,Result:{4}", e.content.processId, e.content.threadId, e.content.state, new TimeSpan(e.content.endTicks - e.content.startTicks).TotalMilliseconds.ToString("F0"), e.content.result, DateTime.Now.ToString("MM-dd HH:mm:ss fff"));
        //}

        //private static void Ocr_OnError(object sender, CommonLib.Events.OnErrorEventArgs e)
        //{
        //    //System.Console.WriteLine("处理异常：【{0}】ThreadId:{1},State:{2},Message:{3},time:{4}", e.content.processId, e.content.threadId, e.content.state, e.content.message, DateTime.Now.ToString("MM-dd HH:mm:ss fff"));
        //}

        //private static void Ocr_OnStart(object sender, CommonLib.Events.OnStartedEventArgs e)
        //{
        //    System.Console.WriteLine("开始处理：【{0}】-{1},State:{2},WaitTime:{3}ms", e.content.processId, e.content.threadId, e.content.state, new TimeSpan(e.content.startTicks - e.content.receivedTicks).TotalMilliseconds.ToString("F0"), DateTime.Now.ToString("MM-dd HH:mm:ss fff"));
        //}

        static void Main(string[] args)
        {
            //Set error handler
            SetApplicationErrorHandler();
            //Set Log4net基础信息
            SetLoggerContext();
            InitConfig();
            ProcessNew.InitOcrEngine();

            //var fileName = (@"C:\Users\<USER>\Desktop\docEn.docx");
            //var fileName = (@"C:\Users\<USER>\Desktop\CAT性能优化的实践和思考-梁锦华.pdf");
            //var fileName = @"C:\Users\<USER>\Desktop\table.jpg";
            var fileName = @"C:\Users\<USER>\Desktop\qq.png";
            //var fileName = @"C:\Users\<USER>\Desktop\2.pdf";
            //var fileName = @"C:\Users\<USER>\Desktop\math.png";
            //var fileName = @"C:\Users\<USER>\Desktop\内容配置 - 副本.txt";
            var fileExt = Path.GetExtension(fileName).TrimStart('.');
            var from = TransLanguageTypeEnum.自动;
            var to = TransLanguageTypeEnum.自动;
            var bytes = File.ReadAllBytes(fileName);
            var strTmp = Convert.ToBase64String(bytes);//File.ReadAllText(fileName).Trim();//
            var imgUrl = "";//ImageLib.ImageHelper.GetResult(ImageLib.ImageTypeEnum.UCloud, bytes); //
            //var tImg = YouTuXLab.EnhanceImage(strTmp);
            //GoogleRecTest.GetRecImg();
            var baseOcrType = OcrType.文本;
            var ocrGroupStr = "萤石";
            var lstEnableType = BaseRecHelper.GetEnableRecType(true, baseOcrType, OcrGroupType.不限, null, null, fileExt, from, to);
            for (int i = 0; i < 1; i++)
            {
                lstEnableType.ForEach(ocrType =>
                {
                    //if (!HanZiOcr.HanZiOcrType.白描_讯飞.GetHashCode().Equals(ocrType))
                    //{
                    //    return;
                    //}
                    //if (!MathOcr.MathOcrType.腾讯优图Lite.GetHashCode().Equals(ocrType))
                    //{
                    //    return;
                    //}
                    //if (!TableOcr.TableOcrType.学而思.GetHashCode().Equals(ocrType))
                    //{
                    //    return;
                    //}
                    //if (!DocOcr.DocOcrType.WPS.GetHashCode().Equals(ocrType))
                    //{
                    //    return;
                    //}
                    //if (!TransOcr.TransOcrType.搜狗译图.GetHashCode().Equals(ocrType))
                    //{
                    //    return;
                    //}
                    if (!string.IsNullOrEmpty(ocrGroupStr)
                    && !((HanZiOcr.HanZiOcrType)ocrType).ToString().Contains(ocrGroupStr)
                    && !((MathOcr.MathOcrType)ocrType).ToString().Contains(ocrGroupStr)
                    && !((TableOcr.TableOcrType)ocrType).ToString().Contains(ocrGroupStr)
                    && !((DocOcr.DocOcrType)ocrType).ToString().Contains(ocrGroupStr)
                    && !((TransOcr.TransOcrType)ocrType).ToString().Contains(ocrGroupStr)
                    )
                    {
                        return;
                    }
                    var baseOcr = BaseRecHelper.GetInstance(baseOcrType, ocrType);
                    //if (baseOcr.LstJsonNextProcessArray == null || baseOcr.LstJsonNextProcessArray?.Count <= 0)
                    //{
                    //    return;
                    //}
                    //baseOcr.IsFromLeftToRight = false;
                    //baseOcr.IsFromTopToDown = true;
                    //baseOcr.IsVerticalOcr = true;
                    //if (!baseOcr.IsSupportTrans)
                    //{
                    //    return;
                    //}
                    var content = baseOcr.GetResult(
                            new OcrContent()
                            {
                                url = imgUrl,
                                strBase64 = strTmp,
                                receivedTicks = ServerTime.DateTime.Ticks,
                                fileExt = fileExt,
                                from = from,
                                to = to,
                            })?.Result;
                    if (content?.result?.resultType == ResutypeEnum.网页)
                    {
                        var state = new ProcessStateEntity();
                        while (state.state != OcrProcessState.处理成功)
                        {
                            state = baseOcr.QueryFileStatus(content.result.autoText).Result;
                            System.Console.WriteLine("【" + content.processName + "】" + state + " " + state.desc);
                            Thread.Sleep(300);
                        }
                    }
                    else if (string.IsNullOrEmpty(content?.result?.spiltText))
                    {
                    }
                    System.Console.WriteLine("处理完成：{6}【{0}】-{1},State:{2},ExecTime:{3}ms,Result:{4}"
                        , content.processName, content.threadId, content.state
                        , new TimeSpan(content.endTicks - content.startTicks).TotalMilliseconds.ToString("F0")
                        , content.result?.spiltText
                        , DateTime.Now.ToString("MM-dd HH:mm:ss fff")
                        , !string.IsNullOrEmpty(content.result?.verticalText) ? "[竖排]" : "");
                });
            }
            System.Console.WriteLine("全部处理完成！！");
            ////OcrRecHelper.Report();

            //HanZiOcr.BaiDuAPIRec.InitToken();
            //str1 = HanZiOcr.BaiDuRec.GetContextByFile(strTmp);
            ////str1 = HanZiOcr.BaiDuTuShuRec.GetContext(strTmp);
            //str1 = HanZiOcr.BingImageRec.GetContext(bytes);
            //str1 = HanZiOcr.MicroSoftRec.GetContext(strTmp);

            //var bytes = File.ReadAllBytes(@"D:\助手\Image\0108\Old\11.jpg");//10584306645.jpg");
            //var code = _360Code.GetCode(bytes, true);
            //var strTmp = Convert.ToBase64String(bytes);
            //List<string> lstRndTmp = new List<string>();
            ////Random rnd = new Random();
            ////for (int i = 11; i < 16; i++)
            ////{
            ////    var bytes = File.ReadAllBytes(@"D:\助手\Image\0108\Old\" + i + ".jpg");//10584306645.jpg");
            ////    lstRndTmp.Add(Convert.ToBase64String(bytes));
            ////}

            //RdsCacheHelper.ImageQueue = new CusImageQueue();

            //RdsCacheHelper.ImageQueue.EnqueueCacheMessage(new CusImageEntity()
            //    {
            //        DtAdd = DateTime.Now,
            //        DtExpired = DateTime.Now.AddSeconds(20),
            //        IsLogin = false,
            //        SiteFlag = "助手",
            //        StrIndex = Guid.NewGuid().ToString(),
            //        StrImg = strTmp
            //    });

            //int totalCount = 500;
            //int count = 0;
            //System.Diagnostics.Stopwatch stop = System.Diagnostics.Stopwatch.StartNew();

            //for (int i = 0; i < totalCount; i++)
            //{
            //    var cus = new CusImageEntity()
            //    {
            //        DtAdd = DateTime.Now,
            //        DtExpired = DateTime.Now.AddSeconds(60),
            //        IsLogin = false,
            //        SiteFlag = "助手",
            //        StrIndex = Guid.NewGuid().ToString(),
            //        StrImg = strTmp
            //    };
            //    RdsCacheHelper.ImageQueue.EnqueueCacheMessage(cus);
            //    count++;
            //}
            ////Parallel.For(0, 500, i =>
            ////{
            ////    //ThreadPool.QueueUserWorkItem(q =>
            ////    //{
            ////    RdsCacheHelper.ImageQueue.Push(cus);
            ////    count++;
            ////    //});
            ////});
            //Console.WriteLine("============发送{0}条完毕！==============", count);

            //Console.WriteLine("线程ID:{3} 插入{0}条数据，共耗时：{1}ms，平均耗时：{2}ms", totalCount
            //    , stop.ElapsedMilliseconds, stop.ElapsedMilliseconds / totalCount, System.Diagnostics.Process.GetCurrentProcess().Id);
            //return;

            //var date = ServerTime.DateTime;
            //System.Console.WriteLine(date.ToString());
            //SouGouCode.IsEnable = true;
            //SouGouCode.StartAutoProxy();

            //HanZiOcr.BaiDuAIRec.GetRecImg();

            //DaShiCode.GetCode(null, true);

            //ConfigHelper.StrCodeBasePath = "C:\\Config\\";

            if (System.Diagnostics.Debugger.IsAttached)
            {
                LogHelper.Log.Info("Debug模式");
                if (ConfigHelper.IsEnableCode)
                {
                    if (ConfigHelper.NMaxCodeProcessThread > 1)
                    {
                        Parallel.For(0, ConfigHelper.NMaxCodeProcessThread, item =>
                        {
                            ProcessNew.StartProcess();
                        });
                    }
                    else
                    {
                        ProcessNew.StartProcess();
                    }
                }
                System.Console.ReadLine();
            }
            else
            {
                LogHelper.Log.Info("Service模式");
                var ServicesToRun = new System.ServiceProcess.ServiceBase[]
                {
                    new EngineService()
                };
                System.ServiceProcess.ServiceBase.Run(ServicesToRun);
            }
            //if (System.Diagnostics.Debugger.IsAttached)
            //{
            //    LogHelper.Log.Info("Debug模式");
            //    if (ConfigHelper.IsEnableCode)
            //    {
            //        if (ConfigHelper.NMaxCodeProcessThread > 1)
            //        {
            //            Parallel.For(0, ConfigHelper.NMaxCodeProcessThread, item =>
            //            {
            //                ProcessNew.StartProcess();
            //            });
            //        }
            //        else
            //        {
            //            ProcessNew.StartProcess(false);
            //        }
            //    }
            //    System.Console.ReadLine();
            //}
        }

        static void InitConfig()
        {
            ConfigHelper.InitConfig();

            //DaMaConfig.InitOtherConfig();

            ConfigHelper.InitThread();
        }

        private static void SetApplicationErrorHandler()
        {
            Application.ThreadException += new ThreadExceptionEventHandler(Application_ThreadException);
            Application.SetUnhandledExceptionMode(UnhandledExceptionMode.CatchException);
            AppDomain.CurrentDomain.UnhandledException += new UnhandledExceptionEventHandler(CurrentDomain_UnhandledException);
        }

        private static void SetLoggerContext()
        {
            string fileInfo = Path.Combine(AppDomain.CurrentDomain.SetupInformation.ApplicationBase, "Log4net.config");
            XmlConfigurator.ConfigureAndWatch(new FileInfo(fileInfo));
            ////添加ServiceStack的日志输出，从而将ServiceStack的错误打印出来
            //ServiceStack.Logging.LogManager.LogFactory = new ServiceStack.Logging.Log4Net.Log4NetFactory();
        }

        private static void Application_ThreadException(object sender, ThreadExceptionEventArgs t)
        {
            //if (ApplicationStartInfo.StartMode != StartModes.Service)
            //    if (MessageBox.Show(string.Format("应用发生了错误，请关闭后重试。\r\n\r\n错误消息为\"{0}\"", t.Exception.Message), "系统错误", MessageBoxButtons.OK, MessageBoxIcon.Error) == DialogResult.OK)
            //    {
            //        Application.UseWaitCursor = false;
            //        Application.Exit();
            //    }
        }

        private static void CurrentDomain_UnhandledException(object sender, UnhandledExceptionEventArgs e)
        {
            //Exception ex = (Exception)e.ExceptionObject;
            //_logger.Error("Get Application Current Domain Unhandle Thread Exception", ex);
            //if (ApplicationStartInfo.StartMode != StartModes.Service)
            //    if (MessageBox.Show(string.Format("应用发生了错误，请关闭后重试。\r\n\r\n错误消息为\"{0}\"", ex.Message), "系统错误", MessageBoxButtons.OK, MessageBoxIcon.Error) == DialogResult.OK)
            //    {
            //        Application.UseWaitCursor = false;
            //        Application.Exit();
            //    }
        }
    }
    public enum StartModes
    {
        Service,
        Form
    }
    public static class ApplicationStartInfo
    {
        static ApplicationStartInfo()
        {
            Argument = new Dictionary<string, string>();

            string[] args = Environment.GetCommandLineArgs();
            ApplicationName = args[0];

            for (int i = 1; i < args.Length; i++)
            {
                string arg = args[i].Trim().Replace("/", "");
                switch (arg.ToLower())
                {
                    case "form":
                        StartMode = StartModes.Form;
                        break;
                    default:
                        string[] command = arg.Split('=');
                        if (command.Length == 2)
                        {
                            Argument.Add(command[0].ToLower(), command[1]);
                        }
                        break;
                }
            }
        }

        public static StartModes StartMode { get; private set; } = StartModes.Form;

        public static IDictionary<string, string> Argument { get; private set; }

        public static string ApplicationName { get; private set; }
    }
}
