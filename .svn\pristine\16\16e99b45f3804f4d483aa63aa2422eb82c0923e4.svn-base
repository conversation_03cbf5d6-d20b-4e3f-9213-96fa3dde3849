﻿using Emgu.CV;
using Emgu.CV.CvEnum;
using System;
using System.Collections.Generic;
using System.Drawing;
using System.Text;
using System.Threading.Tasks;

namespace OcrLiteLib
{
    public class OcrLite
    {
        public bool isPartImg { get; set; }
        public bool isDebugImg { get; set; }
        private string dbNetPath, angleNetPath, crnnNetPath, keysPath;
        private DbNet dbNet;
        private AngleNet angleNet;
        private CrnnNet crnnNet;

        public OcrLite(string models, int numThread)
        {
            dbNetPath = models + "\\dbnet.onnx";
            angleNetPath = models + "\\angle_net.onnx";
            crnnNetPath = models + "\\crnn_lite_lstm.onnx";
            keysPath = models + "\\keys.txt";
            try
            {
                dbNet = new DbNet(dbNetPath, numThread);
                angleNet = new AngleNet(angleNetPath, numThread);
                crnnNet = new CrnnNet(crnnNetPath, keysPath, numThread);
            }
            catch (Exception ex)
            {
                Console.WriteLine(ex.Message + ex.StackTrace);
                throw ex;
            }
        }

        public OcrResult Detect(byte[] img, int padding, int imgResize, float boxScoreThresh, float boxThresh, float minArea,
                              float unClipRatio, bool doAngle, bool mostAngle)
        {
            Mat brgSrc = new Mat();
            CvInvoke.Imdecode(img, ImreadModes.Color, brgSrc);
            return DetectByMat(padding, imgResize, boxScoreThresh, boxThresh, minArea, unClipRatio, doAngle, mostAngle, brgSrc);
        }

        public OcrResult Detect(string img, int padding, int imgResize, float boxScoreThresh, float boxThresh, float minArea,
                          float unClipRatio, bool doAngle, bool mostAngle)
        {
            Mat brgSrc = CvInvoke.Imread(img, ImreadModes.Color);//default : BGR
            return DetectByMat(padding, imgResize, boxScoreThresh, boxThresh, minArea, unClipRatio, doAngle, mostAngle, brgSrc);
        }

        private OcrResult DetectByMat(int padding, int imgResize, float boxScoreThresh, float boxThresh, float minArea, float unClipRatio, bool doAngle, bool mostAngle, Mat brgSrc)
        {
            Mat originSrc = new Mat();
            CvInvoke.CvtColor(brgSrc, originSrc, ColorConversion.Bgr2Rgb);// convert to RGB
            Rectangle originRect = new Rectangle(padding, padding, originSrc.Cols, originSrc.Rows);
            Mat paddingSrc = OcrUtils.MakePadding(originSrc, padding);

            int resize;
            if (imgResize <= 0)
            {
                resize = Math.Max(paddingSrc.Cols, paddingSrc.Rows);
            }
            else
            {
                resize = imgResize;
            }
            ScaleParam scale = ScaleParam.GetScaleParam(paddingSrc, resize);

            return DetectOnce(paddingSrc, originRect, scale, boxScoreThresh, boxThresh, minArea, unClipRatio, doAngle, mostAngle);
        }

        public OcrLineResult DetectLine(byte[] img, int padding, int imgResize, float boxScoreThresh, float boxThresh, float minArea,
                              float unClipRatio, bool doAngle, bool mostAngle)
        {
            Mat brgSrc = new Mat();
            CvInvoke.Imdecode(img, ImreadModes.Color, brgSrc);
            return DetectLineByMat(padding, imgResize, boxScoreThresh, boxThresh, minArea, unClipRatio, doAngle, mostAngle, brgSrc);
        }

        private OcrLineResult DetectLineByMat(int padding, int imgResize, float boxScoreThresh, float boxThresh, float minArea, float unClipRatio, bool doAngle, bool mostAngle, Mat brgSrc)
        {
            Mat originSrc = new Mat();
            CvInvoke.CvtColor(brgSrc, originSrc, ColorConversion.Bgr2Rgb);// convert to RGB
            Rectangle originRect = new Rectangle(padding, padding, originSrc.Cols, originSrc.Rows);
            Mat paddingSrc = OcrUtils.MakePadding(originSrc, padding);

            int resize;
            if (imgResize <= 0)
            {
                resize = Math.Max(paddingSrc.Cols, paddingSrc.Rows);
            }
            else
            {
                resize = imgResize;
            }
            ScaleParam scale = ScaleParam.GetScaleParam(paddingSrc, resize);

            return DetectLine(paddingSrc, originRect, scale, boxScoreThresh, boxThresh, minArea, unClipRatio, doAngle, mostAngle);
        }

        private OcrLineResult DetectLine(Mat src, Rectangle originRect, ScaleParam scale, float boxScoreThresh, float boxThresh, float minArea,
                              float unClipRatio, bool doAngle, bool mostAngle)
        {
            var startTicks = DateTime.Now.Ticks;

            var textBoxes = dbNet.GetTextBoxes(src, scale, boxScoreThresh, boxThresh, minArea, unClipRatio);
            var dbNetTime = (DateTime.Now.Ticks - startTicks) / 10000F;

            var ocrResult = new OcrLineResult
            {
                DbNetTime = dbNetTime,
                TextBoxs = textBoxes
            };

            return ocrResult;
        }

        private OcrResult DetectOnce(Mat src, Rectangle originRect, ScaleParam scale, float boxScoreThresh, float boxThresh, float minArea,
                              float unClipRatio, bool doAngle, bool mostAngle)
        {
            Mat textBoxPaddingImg = src.Clone();
            int thickness = OcrUtils.GetThickness(src);
            Console.WriteLine("=====Start detect=====");
            var startTicks = DateTime.Now.Ticks;

            Console.WriteLine("---------- step: dbNet getTextBoxes ----------");
            var textBoxes = dbNet.GetTextBoxes(src, scale, boxScoreThresh, boxThresh, minArea, unClipRatio);
            var dbNetTime = (DateTime.Now.Ticks - startTicks) / 10000F;

            Console.WriteLine($"TextBoxesSize({textBoxes.Count})");
            //textBoxes.ForEach(x => Console.WriteLine(x));
            Console.WriteLine($"dbNetTime({dbNetTime}ms)");

            Console.WriteLine("---------- step: drawTextBoxes ----------");
            OcrUtils.DrawTextBoxes(textBoxPaddingImg, textBoxes, thickness);
            //CvInvoke.Imshow("ResultPadding", textBoxPaddingImg);

            //---------- getPartImages ----------
            List<Mat> partImages = OcrUtils.GetPartImages(src, textBoxes);
            //if (isPartImg)
            //{
            //    for (int i = 0; i < partImages.Count; i++)
            //    {
            //        CvInvoke.Imshow($"PartImg({i})", partImages[i]);
            //    }
            //}

            Console.WriteLine("---------- step: angleNet getAngles ----------");
            List<Angle> angles = angleNet.GetAngles(partImages, doAngle, mostAngle);
            //angles.ForEach(x => Console.WriteLine(x));

            //Rotate partImgs
            Parallel.For(0, partImages.Count, new ParallelOptions() { MaxDegreeOfParallelism = OcrUtils.MaxDegreeOfParallelism }, i =>
            {
                if (angles[i].Index == 0)
                {
                    partImages[i] = OcrUtils.MatRotateClockWise180(partImages[i]);
                }
                //if (isDebugImg)
                //{
                //    CvInvoke.Imshow($"DebugImg({i})", partImages[i]);
                //}
            });

            Console.WriteLine("---------- step: crnnNet getTextLines ----------");
            List<TextLine> textLines = crnnNet.GetTextLines(partImages);
            //textLines.ForEach(x => Console.WriteLine(x));

            List<TextBlock> textBlocks = new List<TextBlock>();

            Parallel.For(0, textLines.Count, new ParallelOptions() { MaxDegreeOfParallelism = OcrUtils.MaxDegreeOfParallelism }, i =>
            {
                TextBlock textBlock = new TextBlock
                {
                    BoxPoints = textBoxes[i].Points,
                    BoxScore = textBoxes[i].Score,
                    AngleIndex = angles[i].Index,
                    AngleScore = angles[i].Score,
                    AngleTime = angles[i].Time,
                    Text = textLines[i].Text,
                    CharScores = textLines[i].CharScores,
                    CrnnTime = textLines[i].Time,
                    BlockTime = angles[i].Time + textLines[i].Time
                };
                lock (textBlocks)
                {
                    textBlocks.Add(textBlock);
                }
            });
            //textBlocks.ForEach(x => Console.WriteLine(x));

            var fullDetectTime = (DateTime.Now.Ticks - startTicks) / 10000F;
            //Console.WriteLine($"fullDetectTime({fullDetectTime}ms)");

            //cropped to original size
            Mat rgbBoxImg = new Mat(textBoxPaddingImg, originRect);
            Mat boxImg = new Mat();
            CvInvoke.CvtColor(rgbBoxImg, boxImg, ColorConversion.Rgb2Bgr);//convert to BGR for Output Result Img
            //CvInvoke.Imshow("Result", boxImg);

            StringBuilder strRes = new StringBuilder();
            textBlocks.ForEach(x => strRes.AppendLine(x.Text));

            OcrResult ocrResult = new OcrResult
            {
                TextBlocks = textBlocks,
                DbNetTime = dbNetTime,
                BoxImg = boxImg,
                DetectTime = fullDetectTime,
                StrRes = strRes.ToString()
            };

            return ocrResult;
        }

    }
}
