﻿using CommonLib;
using System;
using System.Collections.Generic;
using System.IO;
using System.Net;
using System.Security.Cryptography;
using System.Text;

namespace DocOcr
{
    /// <summary>
    /// 有道文档翻译
    /// https://ai.youdao.com/DOCSIRMA/html/%E6%96%87%E6%A1%A3%E7%BF%BB%E8%AF%91/API%E6%96%87%E6%A1%A3/%E6%96%87%E6%A1%A3%E7%BF%BB%E8%AF%91%E6%9C%8D%E5%8A%A1/%E6%96%87%E6%A1%A3%E7%BF%BB%E8%AF%91%E6%9C%8D%E5%8A%A1-API%E6%96%87%E6%A1%A3.html
    /// </summary>
    public class YouDaoAPIRec : BaseDocOcrRec
    {
        public YouDaoAPIRec()
        {
            OcrGroup = OcrGroupType.有道;
            OcrType = DocOcrType.有道文档API;
            ResultType = ResutypeEnum.网页;
            MaxExecPerTime = 21;

            AllowUploadFileTypes = new List<string>() { "pdf", "doc", "docx", "ppt", "pptx", "jpg", "png", "bmp" };//, "txt" //ppt/pptx
            IsSupportTrans = true;

            InitLanguage();
        }

        #region 支持的语言

        private void InitLanguage()
        {
            TransLanguageDic = new Dictionary<TransLanguageTypeEnum, string>();
            //TransLanguageDic.Add(TransLanguageTypeEnum.自动, "AUTO");
            TransLanguageDic.Add(TransLanguageTypeEnum.中文, "zh-CHS");
            TransLanguageDic.Add(TransLanguageTypeEnum.英文, "en");
        }

        #endregion

        class YouDaoAccount
        {
            public string strAppId { get; set; }

            public string strSecretId { get; set; }
        }

        static List<YouDaoAccount> lstAppAccount = new List<YouDaoAccount>
        {
            new YouDaoAccount
            {
                strAppId = "zhudytest123",
                strSecretId =  "youdaoapiv120171"
            }
        };

        protected override string GetHtml(OcrContent content)
        {
            var result = "";
            string from = "", to = "";
            InitLanguage(content, ref from, ref to);

            var fileId = Upload(content.strBase64, content.fileExt, from, to);
            if (!string.IsNullOrEmpty(fileId))
            {
                result = GetStatus(fileId);
            }
            return result;
        }

        protected string Upload(string strBase64, string fileExt, string from, string to)
        {
            Dictionary<string, string> dic = new Dictionary<string, string>();
            var account = lstAppAccount.GetRndItem();
            string appKey = account.strAppId;
            string appSecret = account.strSecretId;
            string url = "https://openapi.youdao.com/file_trans/upload";
            string salt = ServerTime.DateTime.Millisecond.ToString();
            TimeSpan ts = (DateTime.UtcNow - new DateTime(1970, 1, 1, 0, 0, 0, DateTimeKind.Utc));
            long millis = (long)ts.TotalMilliseconds;
            string curtime = Convert.ToString(millis / 1000);
            string signStr = appKey + Truncate(strBase64) + salt + curtime + appSecret; ;
            string sign = ComputeHash(signStr, new SHA256CryptoServiceProvider());
            dic.Add("q", System.Web.HttpUtility.UrlEncode(strBase64));
            dic.Add("fileName", "1." + fileExt);
            dic.Add("fileType", fileExt);
            dic.Add("langFrom", from);
            dic.Add("langTo", to);
            dic.Add("appKey", appKey);
            dic.Add("salt", salt);
            dic.Add("curtime", curtime);
            dic.Add("sign", sign);
            dic.Add("docType", "json");
            dic.Add("signType", "v3");
            string result = Post(url, dic);
            result = CommonHelper.SubString(result, "\"flownumber\":\"", "\"");
            return result;
        }

        protected string GetDownloadStr(string flownumber)
        {
            var account = lstAppAccount.GetRndItem();
            string salt = ServerTime.DateTime.Millisecond.ToString();
            string curtime = Convert.ToString((long)(DateTime.UtcNow - new DateTime(1970, 1, 1, 0, 0, 0, DateTimeKind.Utc)).TotalMilliseconds / 1000);
            string signStr = account.strAppId + Truncate(flownumber) + salt + curtime + account.strSecretId; ;
            string sign = ComputeHash(signStr, new SHA256CryptoServiceProvider());
            return string.Format("flownumber={0}&downloadFileType={1}&appKey={2}&salt={3}&curtime={4}&sign={5}&docType=json&signType=v3"
                , flownumber, "word", account.strAppId, salt, curtime, sign);
        }

        private string GetStatus(string taskId)
        {
            var result = "";
            try
            {
                Dictionary<string, string> dic = new Dictionary<string, string>();
                var account = lstAppAccount.GetRndItem();
                string appKey = account.strAppId;
                string appSecret = account.strSecretId;
                string url = "https://openapi.youdao.com/file_trans/query";
                string salt = ServerTime.DateTime.Millisecond.ToString();
                TimeSpan ts = (DateTime.UtcNow - new DateTime(1970, 1, 1, 0, 0, 0, DateTimeKind.Utc));
                long millis = (long)ts.TotalMilliseconds;
                string curtime = Convert.ToString(millis / 1000);
                string signStr = appKey + Truncate(taskId) + salt + curtime + appSecret; ;
                string sign = ComputeHash(signStr, new SHA256CryptoServiceProvider());
                dic.Add("flownumber", taskId);
                dic.Add("appKey", appKey);
                dic.Add("salt", salt);
                dic.Add("curtime", curtime);
                dic.Add("sign", sign);
                dic.Add("docType", "json");
                dic.Add("signType", "v3");
                result = Post(url, dic);
                if (!string.IsNullOrEmpty(result))
                {
                    result = result.Trim().TrimEnd('}') + ",\"taskId\":\"" + taskId + "\"}";
                }
            }
            catch (Exception)
            {

            }
            return result;
        }

        protected override ResultEntity GetProcessText(OcrContent content, string html)
        {
            //{"status":2
            //"is_preview_docx_ready":true,"is_preview_pdf_ready":true,"is_preview_uncomparison_docx_ready":true,"is_preview_uncomparison_pdf_ready":true
            //"is_full_docx_ready":true,"is_full_pdf_ready":true,"is_full_uncomparison_docx_ready":true,"is_full_uncomparison_pdf_ready":true
            //,"preview_status":2}
            //result = "1";
            var entity = GetFileResult(CommonHelper.SubString(html, "\"taskId\":\"", "\""));

            if (!string.IsNullOrEmpty(entity.autoText))
            {
                var fileType = CommonHelper.GetFileType(CommonHelper.SubString(html, "\"fileType\":\"", "\""));
                var viewFileDown = new DownLoadInfo()
                {
                    fileType = fileType,
                    desc = fileType.ToString(),
                    url = "https://openapi.youdao.com/file_trans/download",
                    param = GetDownloadStr(entity.autoText)
                };
                entity.files.Add(viewFileDown);
            }
            return entity;
        }

        public override ProcessStateEntity QueryFileStatuMethod(string taskId)
        {
            var html = GetStatus(taskId);
            //{"statusString":"已完成","errorCode":"0","status":4}
            var processStatus = new ProcessStateEntity()
            {
                state = OcrProcessState.未知状态,
                taskId = taskId
            };
            if (html.Contains("已完成"))
            {
                processStatus.state = OcrProcessState.处理成功;
                processStatus.desc = "处理完毕，可以下载了！";
            }
            else if (html.Contains("生成中") || html.Contains("转换中") || html.Contains("翻译中"))
            {
                processStatus.state = OcrProcessState.处理中;
                processStatus.desc = "处理中，请耐心等待…";
            }
            else if (html.Contains("上传中"))
            {
                processStatus.state = OcrProcessState.待处理;
                processStatus.desc = "排队中，请耐心等待…";
            }
            else if (html.Contains("失败") || html.Contains("取消") || html.Contains("删除"))
            {
                processStatus.state = OcrProcessState.处理失败;
                processStatus.desc = "翻译失败，请稍后重试…";
            }
            else
            {
                Console.WriteLine("有道文档API查询状态异常：" + html);
            }
            return processStatus;
        }


        protected string ComputeHash(string input, HashAlgorithm algorithm)
        {
            byte[] inputBytes = Encoding.UTF8.GetBytes(input);
            byte[] hashedBytes = algorithm.ComputeHash(inputBytes);
            return BitConverter.ToString(hashedBytes).Replace("-", "");
        }

        protected string Truncate(string q)
        {
            if (q == null)
            {
                return null;
            }
            int len = q.Length;
            return len <= 20 ? q : (q.Substring(0, 10) + len + q.Substring(len - 10, 10));
        }

        protected string Post(string url, Dictionary<string, string> dic)
        {
            string result = "";
            HttpWebRequest req = (HttpWebRequest)WebRequest.Create(url);
            req.Method = "POST";
            req.ContentType = "application/x-www-form-urlencoded";
            StringBuilder builder = new StringBuilder();
            int i = 0;
            foreach (var item in dic)
            {
                if (i > 0)
                    builder.Append("&");
                builder.AppendFormat("{0}={1}", item.Key, item.Value);
                i++;
            }
            byte[] data = Encoding.UTF8.GetBytes(builder.ToString());
            req.ContentLength = data.Length;
            using (Stream reqStream = req.GetRequestStream())
            {
                reqStream.Write(data, 0, data.Length);
                reqStream.Close();
            }
            HttpWebResponse resp = (HttpWebResponse)req.GetResponse();
            Stream stream = resp.GetResponseStream();
            using (StreamReader reader = new StreamReader(stream, Encoding.UTF8))
            {
                result = reader.ReadToEnd();
            }
            return result;
        }

    }
}