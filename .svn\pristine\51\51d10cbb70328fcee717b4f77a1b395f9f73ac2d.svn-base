﻿using CommonLib;
using System;
using System.Security.Cryptography;
using System.Text;
using System.Linq;

namespace Account.Web
{
    public partial class Mail : System.Web.UI.Page
    {
        protected void Page_Load(object sender, EventArgs e)
        {
            if (!IsPostBack)
            {
                string strOp = ToolCommon.BoxUtil.GetStringFromObject(Request.QueryString["op"]);
                if (!string.IsNullOrEmpty(strOp))
                {
                    DoOperate(strOp);
                }
            }
            Response.End();
        }

        /// <summary>
        /// 通过字符串获取MD5值，返回32位字符串。
        /// </summary>
        /// <param name="str"></param>
        /// <returns></returns>
        public static string GetMD5String(string str)
        {
            using (MD5 md5 = MD5.Create())
            {
                byte[] data2 = md5.ComputeHash(Encoding.UTF8.GetBytes(str));

                return GetbyteToString(data2);
            }
        }

        private static string GetbyteToString(byte[] data)
        {
            StringBuilder sb = new StringBuilder();
            for (int i = 0; i < data.Length; i++)
            {
                sb.Append(data[i].ToString("x2"));
            }
            data = null;
            return sb.ToString();
        }

        private void DoOperate(string strOP)
        {
            try
            {
                string strEncrypt = "";
                //OPEntity opp = new OPEntity();
                var notice = new NoticeQueueEntity();
                if (CommonHelper.IsEncrypt)
                {
                    strEncrypt = ToolCommon.BoxUtil.GetStringFromObject(Request.QueryString["con"]);
                    strEncrypt = CommonEncryptHelper.DESDecrypt(strEncrypt, CommonHelper.StrEncrypt);
                    if (!string.IsNullOrEmpty(strEncrypt))
                    {
                        //opp.StrApp = CommonHelper.SubString(strEncrypt, "app=", "&");
                        notice.MacCode = CommonHelper.SubString(strEncrypt, "app=", "&");
                    }
                }
                else
                {
                    //opp.StrApp = BoxUtil.GetStringFromObject(Request.QueryString["app"]);
                    notice.MacCode = ToolCommon.BoxUtil.GetStringFromObject(Request.QueryString["app"]);
                }
                var uid = Request.Headers["uid"];
                var mac = Request.Headers["mac"];
                if (!string.IsNullOrEmpty(uid))
                {
                    switch (strOP)
                    {
                        case "forgetpwd":
                            if (CommonHelper.IsEncrypt)
                            {
                                notice.To = CommonHelper.SubString(strEncrypt, "email=", "&");
                                notice.MobileNo = CommonHelper.SubString(strEncrypt, "mobile=", "&");
                            }
                            else
                            {
                                notice.To = ToolCommon.BoxUtil.GetStringFromObject(Request.QueryString["email"]);
                                notice.MobileNo = ToolCommon.BoxUtil.GetStringFromObject(Request.QueryString["mobile"]);
                            }
                            bool isMobile = !string.IsNullOrEmpty(notice.MobileNo) && System.Text.RegularExpressions.Regex.IsMatch(notice.MobileNo, @"^[1]\d{10}");
                            bool isEmail = !string.IsNullOrEmpty(notice.To) && System.Text.RegularExpressions.Regex.IsMatch(notice.To, "^\\s*([A-Za-z0-9_-]+(\\.\\w+)*@(\\w+\\.)+\\w{2,5})\\s*$");
                            if (isMobile || isEmail)
                            {
                                notice.Subject = "密码找回服务";
                                var validateCode = GetMD5String((isMobile ? notice.MobileNo : notice.To)
                                    + ServerTime.LocalTime.Date.ToString("yyyy-MM-dd") + "OCRREGForgetPwd").Substring(10, 4).ToUpper();
                                if (isEmail)
                                {
                                    notice.NoticeType = NoticeType.邮件;
                                    notice.Body = string.Format("您的验证码为：" + validateCode + ",有效期1小时!");
                                }
                                else if (isMobile)
                                {
                                    notice.NoticeType = NoticeType.短信;
                                    notice.Body = validateCode;
                                }
                            }
                            if (!string.IsNullOrEmpty(notice.Body))
                            {
                                //SendMail.Send(notice.To, notice.Body, notice.Subject);
                                Response.Write("True");
                            }
                            else
                            {
                                Response.Write("邮箱/手机号格式错误！");
                            }
                            break;
                        case "regaccount":
                            //mail.aspx?email=<EMAIL>&op=regaccount
                            if (CommonHelper.IsEncrypt)
                            {
                                notice.To = CommonHelper.SubString(strEncrypt, "email=", "&");
                                notice.MobileNo = CommonHelper.SubString(strEncrypt, "mobile=", "&");
                            }
                            else
                            {
                                notice.To = ToolCommon.BoxUtil.GetStringFromObject(Request.QueryString["email"]);
                                notice.MobileNo = ToolCommon.BoxUtil.GetStringFromObject(Request.QueryString["mobile"]);
                            }
                            if (CodeHelper.IsCanReg(uid, "", 3))
                            {
                                isMobile = !string.IsNullOrEmpty(notice.MobileNo) && System.Text.RegularExpressions.Regex.IsMatch(notice.MobileNo, @"^[1]\d{10}");
                                isEmail = !string.IsNullOrEmpty(notice.To) && System.Text.RegularExpressions.Regex.IsMatch(notice.To, "^\\s*([A-Za-z0-9_-]+(\\.\\w+)*@(\\w+\\.)+\\w{2,5})\\s*$");
                                if (isMobile || isEmail)
                                {
                                    notice.Subject = "欢迎注册OCR助手";
                                    var validateCode = GetMD5String((isMobile ? notice.MobileNo : notice.To)
                                        + ServerTime.LocalTime.Date.ToString("yyyy-MM-dd") + "OCRREG").Substring(10, 4).ToUpper();
                                    if (isEmail)
                                    {
                                        notice.NoticeType = NoticeType.邮件;
                                        if (CodeHelper.IsExitsCode(notice.To))
                                        {
                                            Response.Write("用户已注册，请返回登录！");
                                        }
                                        else
                                            notice.Body = string.Format("您的验证码为：" + validateCode + ",有效期1小时!");
                                    }
                                    else if (isMobile)
                                    {
                                        notice.NoticeType = NoticeType.短信;
                                        if (CodeHelper.IsExitsCode(notice.MobileNo))
                                        {
                                            Response.Write("用户已注册，请返回登录！");
                                        }
                                        else
                                            notice.Body = validateCode;
                                    }
                                }
                                if (!string.IsNullOrEmpty(notice.Body))
                                {
                                    //SendMail.Send(notice.To, notice.Body, notice.Subject);
                                    Response.Write("True");
                                }
                                else
                                {
                                    Response.Write("邮箱/手机号格式错误！");
                                }
                            }
                            else
                            {
                                CommonHelper._Log.Debug("注册次数超限！token：" + uid + "，账号：" + notice.To + notice.MobileNo);
                                Response.Write("True");
                            }
                            break;
                        case "send":
                        case "mail":
                            if (!CodeHelper.IsExitsCode(notice.MacCode))
                            {
                                Response.Write("True");
                            }
                            else
                            {
                                notice.NoticeType = NoticeType.邮件;
                                if (CommonHelper.IsEncrypt)
                                {
                                    notice.To = CommonHelper.SubString(strEncrypt, "email=", "&");
                                    notice.Body = CommonHelper.SubString(strEncrypt, "body=", "&");
                                    notice.Subject = CommonHelper.SubString(strEncrypt, "title=", "&");
                                    notice.IsSendBySelf = CommonHelper.SubString(strEncrypt, "self=", "&").Equals("1");
                                    notice.Email = CommonHelper.SubString(strEncrypt, "acc=", "&");
                                    notice.Password = CommonHelper.SubString(strEncrypt, "pwd=", "&");
                                    //strSmtp = CommonHelper.SubString(strEncrypt, "smtp=", "&");
                                }
                                else
                                {
                                    notice.To = ToolCommon.BoxUtil.GetStringFromObject(Request.QueryString["email"]);
                                    notice.Body = ToolCommon.BoxUtil.GetStringFromObject(Request.QueryString["body"]);
                                    notice.Subject = ToolCommon.BoxUtil.GetStringFromObject(Request.QueryString["title"]);
                                    notice.IsSendBySelf = ToolCommon.BoxUtil.GetStringFromObject(Request.QueryString["self"]).Equals("1");
                                    notice.Email = ToolCommon.BoxUtil.GetStringFromObject(Request.QueryString["acc"]);
                                    notice.Password = ToolCommon.BoxUtil.GetStringFromObject(Request.QueryString["pwd"]);
                                    //strSmtp = BoxUtil.GetStringFromObject(Request.QueryString["smtp"]);
                                }
                                if (strOP.Equals("reg"))
                                {
                                    notice.Subject = "欢迎注册OCR助手";
                                    notice.Body = string.Format("您的注册验证码为："
                                        + GetMD5String(notice.To + ServerTime.LocalTime.Date.ToString("yyyy-MM-dd") + "OCRREG").Substring(5, 10).ToUpper()
                                        + ",有效期为2小时。");
                                }
                                notice.Body = CommonHelper.GetReplaceStr(notice.Body);
                                notice.Subject = notice.Subject.Replace("订票提醒", "助手提醒");
                                //SendMail.Send(notice.To, notice.Body, notice.Subject);
                                //SendMail.Send(opp.StrMachine, opp.StrVersion, opp.StrUser, isSelf, strAccount, strPwd, strSmtp);
                                //if (isSelf)
                                //{
                                //    isSelf = false;
                                //    SendMail.Send(opp.StrMachine, opp.StrVersion, opp.StrUser, isSelf, strAccount, strPwd, strSmtp);
                                //}
                                Response.Write("True");
                            }
                            break;
                        case "sms":
                            if (!CodeHelper.IsExitsCode(notice.MacCode))
                            {
                                Response.Write("True");
                            }
                            else
                            {
                                notice.NoticeType = NoticeType.短信;
                                if (CommonHelper.IsEncrypt)
                                {
                                    notice.MobileNo = CommonHelper.SubString(strEncrypt, "phone=", "&");
                                    notice.Password = CommonHelper.SubString(strEncrypt, "pwd=", "&");
                                    notice.MobileNo = CommonHelper.SubString(strEncrypt, "to=", "&");
                                    notice.Body = CommonHelper.SubString(strEncrypt, "body=", "&");
                                }
                                else
                                {
                                    notice.MobileNo = ToolCommon.BoxUtil.GetStringFromObject(Request.QueryString["phone"]);
                                    notice.Password = ToolCommon.BoxUtil.GetStringFromObject(Request.QueryString["pwd"]);
                                    notice.MobileNo = ToolCommon.BoxUtil.GetStringFromObject(Request.QueryString["to"]);
                                    notice.Body = ToolCommon.BoxUtil.GetStringFromObject(Request.QueryString["body"]);
                                }

                                ////phone,sign,msg
                                //string[] tmps = FetionService.GetSouGouSign(opp.StrMachine);
                                //if (tmps == null || tmps.Length <= 0)
                                //{
                                //    tmps = CodeHelper.GetSmsCodeEntity(opp.StrMachine);
                                //}
                                //if (tmps == null || tmps.Length <= 0)
                                //{
                                //    CommonHelper._Log.Debug(string.Format("[NoMobile] Phone:{0}", opp.StrMachine));
                                //}
                                //bool result = false;
                                ////bool result = FetionService.SendMSG(opp.StrMachine, opp.StrVersion, opp.StrOp, opp.StrUser, tmps);
                                //if (SendMail.isChinaMobile(opp.StrOp))
                                //{
                                //    opp.StrUser = CommonHelper.GetReplaceStr(opp.StrUser);
                                //    //opp.StrUser = opp.StrUser.Replace("【抢票助手】", "").Replace("的车票", "").Replace("，请及时登陆网站付款或取消订单", "");
                                //    SendMail.Send(opp.StrOp + "@139.com", opp.StrUser, "助手提醒");
                                //    SendMail.Send(opp.StrOp + "@189.cn", opp.StrUser, "助手提醒");
                                //}
                                Response.Write("True");
                            }
                            break;
                        case "voice":
                            if (!CodeHelper.IsExitsCode(notice.MacCode))
                            {
                                Response.Write("True");
                            }
                            else
                            {
                                notice.NoticeType = NoticeType.电话;
                                if (CommonHelper.IsEncrypt)
                                {
                                    notice.MobileNo = CommonHelper.SubString(strEncrypt, "phone=", "&");
                                    notice.Password = CommonHelper.SubString(strEncrypt, "pwd=", "&");
                                    notice.MobileNo = CommonHelper.SubString(strEncrypt, "to=", "&");
                                    notice.Body = CommonHelper.SubString(strEncrypt, "body=", "&");
                                }
                                else
                                {
                                    notice.MobileNo = ToolCommon.BoxUtil.GetStringFromObject(Request.QueryString["phone"]);
                                    notice.Password = ToolCommon.BoxUtil.GetStringFromObject(Request.QueryString["pwd"]);
                                    notice.MobileNo = ToolCommon.BoxUtil.GetStringFromObject(Request.QueryString["to"]);
                                    notice.Body = ToolCommon.BoxUtil.GetStringFromObject(Request.QueryString["body"]);
                                }
                                //bool result1 = FetionService.SendVoice(opp.StrMachine);
                                //if (!result1 && SendMail.isChinaMobile(opp.StrOp))
                                //{
                                //    opp.StrUser = CommonHelper.GetReplaceStr(opp.StrUser);
                                //    //opp.StrUser = opp.StrUser.Replace("【抢票助手】", "").Replace("的车票", "").Replace("，请及时登陆网站付款或取消订单", "");
                                //    SendMail.Send(opp.StrOp + "@139.com", opp.StrUser, "助手提醒");
                                //    SendMail.Send(opp.StrOp + "@189.cn", opp.StrUser, "助手提醒");
                                //}
                                Response.Write("True");
                            }
                            break;
                        case "qq":
                            if (!CodeHelper.IsExitsCode(notice.MacCode))
                            {
                                Response.Write("True");
                            }
                            else
                            {
                                notice.NoticeType = NoticeType.QQ;
                                if (CommonHelper.IsEncrypt)
                                {
                                    notice.QQ = CommonHelper.SubString(strEncrypt, "to=", "&");
                                    notice.Body = CommonHelper.SubString(strEncrypt, "body=", "&");
                                }
                                else
                                {
                                    notice.QQ = ToolCommon.BoxUtil.GetStringFromObject(Request.QueryString["to"]);
                                    notice.Body = ToolCommon.BoxUtil.GetStringFromObject(Request.QueryString["body"]);
                                }
                                //QQHelper.SendQQMsg(opp.StrMachine, opp.StrVersion);
                                Response.Write("True");
                            }
                            break;
                        default:
                            break;
                    }
                    if (notice != null && notice.NoticeType.GetHashCode() > 0)
                    {
                        CommonHelper._Log.Debug(notice.ToString());
                        try
                        {
                            RdsCacheHelper.NoticeQueue.EnqueueCacheMessage(notice);
                        }
                        catch (Exception oe)
                        {
                            CommonHelper._Log.Error("AddNoticeInfo出错:" + notice.ToString(), oe);
                        }
                    }
                }
                else
                {
                    Response.Write("True");
                    try
                    {
                        LogHelper.Log.Info(string.Format("无效请求！QueryString：{0}\nBody:{1}\nHeaders:{2}", Request.Url
                            , string.Join("|", Request.QueryString.AllKeys.Select(p => string.Format("{0}->{1}", p, Request.QueryString[p])))
                            , string.Join("|", Request.Form.AllKeys.Select(p => string.Format("{0}->{1}", p, Request.Form[p])))
                            , string.Join("|", Request.Headers.AllKeys.Select(p => string.Format("{0}->{1}", p, Request.Headers[p]))))
                            );
                    }
                    catch { }
                }
            }
            catch (Exception oe)
            {
                Response.Write("服务器维护中，请稍后重试！");
                CommonHelper._Log.Error(oe);
            }
        }
    }
}