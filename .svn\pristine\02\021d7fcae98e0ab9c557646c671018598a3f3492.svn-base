﻿using Enterprise.Framework.Redis;
using Newtonsoft.Json;
using Newtonsoft.Json.Converters;
using System;
using System.Collections.Generic;
using System.Linq;

namespace CommonLib
{
    public class UserCodeRecordCache : RedisCacheObject<UserCodeInfo>
    {
        protected override string CurrentObject_KeyPrefix
        {
            get { return "CodeCache:"; }
        }

        protected override string Object_DBName
        {
            get { return "OPS_Cache"; }
        }

        public UserCodeInfo GetUserCodeInfo(string account)
        {
            var user = Get(account) ?? new UserCodeInfo
            {
                Key = account,
                Records = new List<CodeRecordLog>(),
                LastTime = ServerTime.DateTime
            };

            if (!Equals(user.LastTime.Date, ServerTime.DateTime.Date))
            {
                user.TodayCount = 0;
            }

            return user;
        }

        public bool Validate(string account, string token, int timeSpan, int limitCount, int blockSecond, long limitPerDayCount, long limitPerTokenCount, ref long todayTotalCount, ref long todayTokenCount, ref string strMsg)
        {
            var result = false;
            var accessTime = ServerTime.DateTime;
            var user = GetUserCodeInfo(account);

            if (user.Tokens == null)
            {
                user.Tokens = new List<CodeTokenInfo>();
            }
            if (!user.Tokens.Exists(p => Equals(p.Token, token)))
            {
                user.Tokens.Add(new CodeTokenInfo { Token = token });
            }
            var userToken = user.Tokens.FirstOrDefault(p => Equals(p.Token, token));

            if (!Equals(user.LastTime.Date, accessTime.Date))
            {
                userToken.TodayCount = 0;
            }

            todayTotalCount = user.TodayCount;
            todayTokenCount = userToken.TodayCount;

            //总账户每日限额
            if (limitPerDayCount > 0 && user.TodayCount >= limitPerDayCount)
            {
                strMsg = "OCR次数超出账户每日限额:" + limitPerDayCount;
                LogHelper.Log.Info(string.Format("{0} 超过账户每日限额:{1}！"
                    , account
                    , user.TodayCount
                ));
                return result;
            }

            //单Token每日限额
            if (limitPerTokenCount > 0 && userToken.TodayCount >= limitPerTokenCount)
            {
                strMsg = "OCR次数超出账户每日限额:" + limitPerTokenCount;
                LogHelper.Log.Info(string.Format("{0}-{1} 超过单Token每日限额:{2}！"
                    , account
                    , token
                    , userToken.TodayCount
                ));
                return result;
            }

            if (userToken.BlackTime.HasValue)
            {
                result = userToken.BlackTime < accessTime;
                if (!result)
                {
                    LogHelper.Log.Info(string.Format("{3} {0}-{1} 黑名单:{2}！"
                        , account
                        , token
                        , userToken.BlackTime?.ToString("yyyy-MM-dd HH:mm:ss fff")
                        , accessTime.ToString("yyyy-MM-dd HH:mm:ss fff")));
                    return result;
                }
                userToken.BlackTime = null;
            }

            //排除过期及无效数据
            if (user.Records.Count > 0)
            {
                user.Records.RemoveAll(p => p.AddTime < accessTime.AddMilliseconds(-timeSpan));
                if (user.Records.Exists(p => p.AddTime > accessTime))
                {
                    LogHelper.Log.Info(string.Format("{0} 时间抖动！当前:{1},最大：{2}"
                        , account
                        , accessTime.ToString("yyyy-MM-dd HH:mm:ss fff")
                        , user.Records.Max(p => p.AddTime).ToString("yyyy-MM-dd HH:mm:ss fff")));
                    user.Records.RemoveAll(p => p.AddTime > accessTime);
                }
            }

            result = user.Records.Count(p => Equals(p.Token, token) && p.Success) < limitCount;
            user.Records.Add(new CodeRecordLog { Success = result, AddTime = accessTime, Token = token });

            if (!result && !userToken.BlackTime.HasValue)
            {
                var lastSuccessTime = user.Records.Where(p => Equals(p.Token, token) && p.Success).Min(p => p.AddTime);
                if (lastSuccessTime.Year < accessTime.Year)
                {
                    lastSuccessTime = accessTime;
                }
                userToken.BlackTime = lastSuccessTime.AddSeconds(timeSpan * 1.0d / 1000 / limitCount);
            }

            if (result)
            {
                userToken.TodayCount = userToken.TodayCount + 1;
                user.TodayCount = user.TodayCount + 1;
            }

            user.LastTime = accessTime;

            todayTotalCount = user.TodayCount;
            todayTokenCount = userToken.TodayCount;

            Insert(account, user, accessTime.AddDays(1));

            return result;
        }
    }

    public class UserCodeInfo
    {
        public string Key { get; set; }

        public List<CodeRecordLog> Records { get; set; }

        public List<CodeTokenInfo> Tokens { get; set; }

        public long TodayCount { get; internal set; }

        public DateTime LastTime { get; internal set; }
    }

    public class CodeTokenInfo
    {
        public string Token { get; set; }

        public long TodayCount { get; internal set; }

        public DateTime? BlackTime { get; set; }
    }

    public class CodeRecordLog
    {
        public bool Success { get; set; }

        public string Token { get; set; }

        public DateTime AddTime { get; set; }
    }
}