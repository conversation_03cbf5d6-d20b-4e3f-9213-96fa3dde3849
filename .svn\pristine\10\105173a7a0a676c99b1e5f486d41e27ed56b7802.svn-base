﻿using System;
using System.Text;
using System.Net.Mail;
using System.Windows.Forms;
using System.Threading.Tasks;
using System.Diagnostics;
using System.ComponentModel;
using System.Collections.Generic;
using System.Threading;
using System.Collections.Concurrent;
using System.IO;
using CommonLib;
using Notice.Process.Common;

namespace Notice.Process.Console
{

    public class MailService
    {
        private static SendMail SendClient = new SendMail();
        public static bool SendMail(NoticeQueueEntity queue)
        {
            var result = false;
            if (queue.NoticeType != NoticeType.邮件)
            {
                queue.To = queue.MobileNo + "@189.cn";
            }
            result = SendClient.Send(queue);
            if (queue.NoticeType != NoticeType.邮件)
            {
                queue.To = queue.MobileNo + "@139.com";
                result = SendClient.Send(queue);
            }
            return result;
        }
    }
    public class SendMail
    {

        public bool Send(NoticeQueueEntity queue)
        {
            var result = false;
            try
            {

                var lstTmpAccount = new List<MailAccount>();
                var account = new MailAccount();
                bool isSelf = queue.IsSendBySelf;
                while (!result && account != null)
                {
                    if (isSelf)
                    {
                        account = new MailAccount()
                        {
                            Account = queue.Email,
                            Password = queue.Password,
                        };
                        isSelf = false;
                    }
                    else
                    {
                        account = Config.GetMailAccount(lstTmpAccount);
                    }
                    if (account != null)
                    {
                        lstTmpAccount.Add(account);
                        result = SendMessage(queue, account);
                        if (!result)
                        {
                            account.ReportError();
                        }
                        else
                        {
                            queue.SendBy = account.Account;
                        }
                    }
                }
            }
            catch (Exception oe)
            {
                ConfigHelper._Log.Error("Send出错", oe);
            }
            //LogHelper.Log.Info(string.Format("[{1}]{0}", queue.ToString(), result ? "成功" : "失败"));
            return result;
        }

        /// <summary>
        /// 同步发送邮件
        /// </summary>
        /// <param name="isSimple">是否只发送一条</param>
        /// <param name="autoReleaseSmtp">是否自动释放SmtpClient</param>
        /// <param name="isReuse">是否重用SmtpClient</param>
        private bool SendMessage(NoticeQueueEntity mailEntity, MailAccount account)
        {
            var result = false;
            MailHelper mail = new MailHelper(false);
            try
            {
                mail.SetSmtpClient(new SmtpHelper(account.Account, account.Password, false).SmtpClient, true);
                mail.From = account.Account;// Config.TestFromAddress;
                mail.FromDisplayName = Config.TestFromNickName;// Config.GetAddressName(Config.TestFromAddress);

                if (mailEntity.To.Contains(";"))
                {
                    var mails = mailEntity.To.Split(new string[] { ";" }, StringSplitOptions.RemoveEmptyEntries);
                    mail.AddReceive(EmailAddrType.To, mails[0], Config.TestToNickName);
                    for (int i = 1; i < mails.Length; i++)
                    {
                        mail.AddReceive(EmailAddrType.CC, mails[i], Config.TestToNickName);
                    }
                }
                else
                {
                    mail.AddReceive(EmailAddrType.To, mailEntity.To, Config.TestToNickName);
                }

                mail.Subject = mailEntity.Subject ?? "助手提醒";
                // Guid.NewGuid() 防止重复内容，被SMTP服务器拒绝接收邮件
                mail.Body = mailEntity.Body;
                mail.IsBodyHtml = true;

                Dictionary<MailInfoType, string> dic = mail.CheckSendMail();
                if (dic.Count > 0 && MailInfoHelper.ExistsError(dic))
                {
                    // 反馈“错误+提示”信息
                    ConfigHelper._Log.Info(MailInfoHelper.GetMailInfoStr(dic));
                }
                else
                {
                    string msg = String.Empty;
                    if (dic.Count > 0)
                    {
                        // 反馈“提示”信息
                        msg = MailInfoHelper.GetMailInfoStr(dic);
                    }

                    try
                    {
                        mail.SendOneMail();
                        result = true;
                    }
                    catch (Exception ex)
                    {
                        // 反馈异常信息
                        msg = msg + (ex.InnerException == null ? ex.Message : ex.Message + ex.InnerException.Message) + Environment.NewLine;
                        ConfigHelper._Log.Error(msg, ex);
                    }
                    finally
                    {
                        // 输出到界面
                        if (msg.Length > 0)
                            ConfigHelper._Log.Info(msg);
                    }
                }
            }
            catch (Exception oe)
            {
                ConfigHelper._Log.Error("SendMessage出错", oe);
            }
            finally
            {
                mail.Reset();
            }

            return result;
        }

        ///// <summary>
        ///// 异步发送邮件
        ///// </summary>
        ///// <param name="isSimple">是否只发送一条</param>
        ///// <param name="autoReleaseSmtp">是否自动释放SmtpClient</param>
        ///// <param name="isReuse">是否重用SmtpClient</param>
        //private bool SendMessageAsync(MailHelper mail, EmailQueueEntity mailEntity, MailAccount account)
        //{
        //    if (!mail.ExistsSmtpClient())
        //    {
        //        SmtpClient client = new SmtpHelper(Config.TestEmailType, false, Config.TestUserName, Config.TestPassword).SmtpClient;
        //        client.SendCompleted += (send, args) =>
        //        {
        //            AsyncCompletedEventArgs arg = args;

        //            if (arg.Error == null)
        //            {
        //                // 需要注意的事使用 MailHelper 发送异步邮件，其UserState是 MailUserState 类型
        //                ConfigHelper._Log.Info(((MailUserState)args.UserState).UserState.ToString() + "已发送完成.");
        //            }
        //            else
        //            {
        //                ConfigHelper._Log.Error(String.Format("{0} 异常：{1}{2}"
        //                    , ((MailUserState)args.UserState).UserState.ToString() + "发送失败."
        //                    , (arg.Error.InnerException == null ? arg.Error.Message : arg.Error.Message + arg.Error.InnerException.Message)
        //                    , Environment.NewLine), arg.Error);
        //                // 标识异常已处理，否则若有异常，会抛出异常
        //                ((MailUserState)args.UserState).IsErrorHandle = true;
        //            }
        //        };
        //        mail.SetSmtpClient(client, true);
        //    }

        //    mail.From = Config.TestUserName;// Config.TestFromAddress;
        //    mail.FromDisplayName = Config.TestFromNickName;// Config.GetAddressName(Config.TestFromAddress);

        //    mail.AddReceive(EmailAddrType.To, mailEntity.To, Config.TestToNickName);

        //    mail.Subject = mailEntity.Subject;
        //    // Guid.NewGuid() 防止重复内容，被SMTP服务器拒绝接收邮件
        //    mail.Body = mailEntity.Body;
        //    mail.IsBodyHtml = true;

        //    Dictionary<MailInfoType, string> dic = mail.CheckSendMail();
        //    if (dic.Count > 0 && MailInfoHelper.ExistsError(dic))
        //    {
        //        // 反馈“错误+提示”信息
        //        ConfigHelper._Log.Info(MailInfoHelper.GetMailInfoStr(dic));
        //    }
        //    else
        //    {
        //        string msg = String.Empty;
        //        if (dic.Count > 0)
        //        {
        //            // 反馈“提示”信息
        //            msg = MailInfoHelper.GetMailInfoStr(dic);
        //        }

        //        try
        //        {
        //            mail.SendOneMail();
        //        }
        //        catch (Exception ex)
        //        {
        //            // 反馈异常信息
        //            ConfigHelper._Log.Error(String.Format("\"异步\"异常：{0}", ex.Message), ex);

        //        }
        //        finally
        //        {
        //            // 输出到界面
        //            if (msg.Length > 0)
        //                ConfigHelper._Log.Info(msg + Environment.NewLine);
        //        }
        //    }

        //    mail.Reset();
        //}
    }

}
