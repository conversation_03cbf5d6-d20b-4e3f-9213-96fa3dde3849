﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace BaiDuAPI
{
    [Serializable]
    public class ImageEntity
    {
        public bool IsHanZi { get; set; }

        public string StrPath { get; set; }

        public string StrId { get; set; }

        public double NAverageGray { get; set; }

        //public double NAverageColor { get; set; }

        public double NAverageColorR { get; set; }

        public double NAverageColorG { get; set; }

        public double NAverageColorB { get; set; }

        public override string ToString()
        {
            if (!string.IsNullOrEmpty(StrId))
                if (IsHanZi)
                    return string.Format("{0}-{1}", NAverageGray.ToString("F0"), StrPath);
                else
                    return string.Format("{0}-{1}-{2}-{3}-{4}", NAverageGray.ToString("F0"), NAverageColorR.ToString("F0"), NAverageColorG.ToString("F0"), NAverageColorB.ToString("F0"), StrPath);
            return base.ToString();
        }

        public string ToFmtString()
        {
            if (!string.IsNullOrEmpty(StrId))
                if (IsHanZi)
                    return string.Format("{0}-{1}", StrId, NAverageGray.ToString("F0"));
                else
                    return string.Format("{0}-{1}-{2}-{3}-{4}", StrId, NAverageGray.ToString("F0"), NAverageColorR.ToString("F0"), NAverageColorG.ToString("F0"), NAverageColorB.ToString("F0"));
            return base.ToString();
        }
    }
}