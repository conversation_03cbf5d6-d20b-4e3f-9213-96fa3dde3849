﻿using System;
using System.Linq;
using System.Text;
using System.Net;
using System.Collections.Specialized;
using System.Net.Cache;
using System.IO;
using System.Web;
using System.Drawing;

namespace Speed
{
    public delegate byte[] GetImage(CNNWebClient myClient, string Url, ref  string CookieStr, string ipAddress = "", string strPost = "", bool isMobile = false, NameValueCollection collect = null);
    public delegate string GetNoSyncHtml(string Url, ref string CookieStr, out bool isCache, string ipAddress = "", string strPost = ""
    , string Referer = "", NameValueCollection collect = null, int timeOut = 2, bool isMobile = false);

    public class WebClientExt
    {
        private static string smethod_0(string string_5)
        {
            string text = string_5;
            Uri uri = new Uri(text, true);
            if (!string.IsNullOrEmpty(uri.Query))
            {
                string[] array = uri.Query.Split(new char[]
				{
					'&'
				});
                string text2 = string.Empty;
                string[] array2 = array;
                foreach (string text3 in array2)
                {
                    if (!string.IsNullOrEmpty(text3))
                    {
                        string[] array3 = text3.Split(new char[]
                        {
                            '='
                        });
                        if (array3.Length >= 2)
                        {
                            string text4 = text2;
                            text2 = string.Concat(new string[]
                            {
                                text4,
                                string.IsNullOrEmpty(text2) ? "" : "&",
                                array3[0],
                                "=",
                                HttpUtility.UrlEncode(array3[1], Encoding.GetEncoding("gb2312"))
                            });
                        }
                    }
                }
                text = text.Replace(uri.Query, text2);
            }
            return text;
        }
        public static bool DCRouter(string sUser, string sPwd, string sUrl, string sPostData, ref string routeType)
        {
            bool result;
            routeType = "";
            WebResponse res = null;
            try
            {
                sUrl = smethod_0(sUrl);
                string domain = "";
                HttpWebRequest httpWebRequest = (HttpWebRequest)WebRequest.Create(sUrl);
                httpWebRequest.ProtocolVersion = HttpVersion.Version11;
                httpWebRequest.Method = "GET";
                httpWebRequest.KeepAlive = true;
                httpWebRequest.PreAuthenticate = true;
                httpWebRequest.AllowAutoRedirect = true;
                httpWebRequest.Headers.Add(HttpRequestHeader.AcceptEncoding, "gzip, deflate");
                httpWebRequest.Accept = "image/gif, image/jpeg, image/pjpeg, image/pjpeg, application/x-shockwave-flash, application/vnd.ms-powerpoint, application/vnd.ms-excel, application/msword, */*";
                httpWebRequest.ContentType = "application/x-www-form-urlencoded";
                httpWebRequest.UserAgent = "Mozilla/5.0 (Windows NT 5.1) AppleWebKit/534.30 (KHTML, like Gecko) Chrome/12.0.742.12 Safari/534.30";
                httpWebRequest.Referer = sUrl;
                httpWebRequest.Timeout = 5000;
                if (sUser != string.Empty)
                {
                    NetworkCredential networkCredential = new NetworkCredential(sUser, sPwd, domain);
                    httpWebRequest.Credentials = networkCredential.GetCredential(new Uri(sUrl), string.Empty);
                }
                else
                {
                    httpWebRequest.Credentials = CredentialCache.DefaultCredentials;
                }
                if (!string.IsNullOrEmpty(sPostData))
                {
                    httpWebRequest.Method = "POST";
                    httpWebRequest.ContentType = "application/x-www-form-urlencoded";
                    byte[] bytes = Encoding.Default.GetBytes(sPostData);
                    httpWebRequest.ContentLength = (long)bytes.Length;
                    using (Stream requestStream = httpWebRequest.GetRequestStream())
                    {
                        requestStream.Write(bytes, 0, bytes.Length);
                    }
                }
                res = httpWebRequest.GetResponse();
                using (StreamReader streamReader = new StreamReader(res.GetResponseStream(), Encoding.Default))
                {
                    streamReader.ReadToEnd();
                    streamReader.Close();
                }
                result = true;
            }
            catch (Exception oe)
            {
                result = false;
                if (oe as WebException != null)
                {
                    res = (oe as WebException).Response as HttpWebResponse;
                }
            }
            try
            {
                if (res != null && !string.IsNullOrEmpty(res.Headers["WWW-Authenticate"]))
                {
                    routeType = res.Headers["WWW-Authenticate"].Trim().Replace("Basic realm=\"", "").Replace("\"", "");
                }
            }
            catch { }
            try
            {
                if (res != null)
                {
                    res.Close();
                    res = null;
                }
            }
            catch { }
            return result;
        }
        public static System.Collections.Concurrent.ConcurrentBag<CNNWebClient> lstCache = new System.Collections.Concurrent.ConcurrentBag<CNNWebClient>();

        public static CNNWebClient GetOneClient()
        {
            try
            {
                CNNWebClient myClient = lstCache.FirstOrDefault(p => !p.IsUsed);
                if (myClient == null)
                {
                    myClient = new CNNWebClient();
                    lstCache.Add(myClient);
                }
                myClient.IsUsed = true;
                return myClient;
            }
            catch (Exception oe)
            {
                Console.WriteLine(oe.Message);
            }
            return GetOneClient();
        }

        public static byte[] RequestSyncImage(string Url, ref  string CookieStr, string ipAddress = ""
            , string strPost = "", bool isMobile = false, NameValueCollection collect = null)
        {
            //DateTime dtTmp = DateTime.Now;
            byte[] result = null;
            //CNNWebClient myClient = new CNNWebClient() { Timeout = 3 };
            CNNWebClient myClient = GetOneClient();
            if (isMobile)
            {
                myClient.isMobile = isMobile;
                //myClient.Credentials = CredentialCache.DefaultCredentials;
            }
            try
            {
                myClient.Headers.Clear();
                myClient.Timeout = 3;
                GetImage handler = new GetImage(GetNoSyncImage);
                System.IAsyncResult async = handler.BeginInvoke(myClient, Url, ref CookieStr, ipAddress, strPost, isMobile, collect, null, null);
                async.AsyncWaitHandle.WaitOne(myClient.Timeout * 1000, true);  //采用异步等待的方式。直到取到合适的值
                result = handler.EndInvoke(ref CookieStr, async);
                handler = null;
                async.AsyncWaitHandle.Close();
                async.AsyncWaitHandle.Dispose();
                //async.AsyncWaitHandle.SafeWaitHandle.Close();
                //async.AsyncWaitHandle.SafeWaitHandle.Dispose();
                async = null;
            }
            catch (OutOfMemoryException oe)
            {
                MemoryManager.ClearMemory();
                Console.WriteLine(oe.Message);
            }
            catch (ObjectDisposedException oe)
            {
                Console.WriteLine(oe.Message);
            }
            catch (Exception oe)
            {
                Console.WriteLine(oe.Message);
            }
            if (result != null && result.Length > 10 && result.Length < 500)
            {
                result = null;
            }
            try
            {
                Url = null;
                ipAddress = null;
                strPost = null;
                collect = null;
            }
            catch { }
            try
            {
                if (myClient != null)
                {
                    try
                    {
                        if (myClient.IsBusy)
                            myClient.CancelAsync();
                    }
                    catch { }
                    myClient.IsUsed = false;
                    //try
                    //{
                    //    myClient.Dispose();
                    //}
                    //catch { }
                    //try
                    //{
                    //    myClient = null;
                    //}
                    //catch { }
                }
            }
            catch (Exception oe)
            {
                Console.WriteLine(oe.Message);
            }
            //Console.WriteLine("IMG：" + new TimeSpan(DateTime.Now.Ticks - dtTmp.Ticks).TotalMilliseconds);
            return result;
        }

        public static Image GetImage(string Url, ref  string CookieStr, string ipAddress = "", string strPost = "", bool isMobile = false, NameValueCollection collect = null)
        {
            byte[] byteArrayIn = GetNoSyncImage(Url, ref CookieStr, ipAddress, strPost, isMobile, collect);
            return byteArrayIn != null && byteArrayIn.Length > 0 ? Image.FromStream(new MemoryStream(byteArrayIn)) : null;
        }

        public static byte[] GetNoSyncImage(string Url, ref  string CookieStr, string ipAddress = "", string strPost = "", bool isMobile = false, NameValueCollection collect = null)
        {
            CNNWebClient myClient = GetOneClient();
            myClient.Headers.Clear();
            myClient.isMobile = isMobile;
            return GetNoSyncImage(myClient, Url, ref CookieStr, ipAddress, strPost, isMobile, collect);
        }

        private static byte[] GetNoSyncImage(CNNWebClient myClient, string Url, ref  string CookieStr, string ipAddress = ""
             , string strPost = "", bool isMobile = false, NameValueCollection collect = null)
        {
            byte[] result = null;
            DateTime dtStart = DateTime.Now;
            myClient.Timeout = 5;
            if (!string.IsNullOrEmpty(ipAddress))
                myClient.StrIPAddress = ipAddress;
            try
            {
                //GET /otn/passcodeNew/getPassCodeNew?module=login&rand=sjrand&0.2009083000011742 HTTP/1.1
                //Host: kyfw.12306.cn
                //Connection: keep-alive
                //Accept: image/webp,*/*;q=0.8
                //User-Agent: 
                //Referer: https://kyfw.12306.cn/otn/login/init
                //Accept-Encoding: gzip, deflate, sdch
                //Accept-Language: zh-CN,zh;q=0.8
                //Cookie: __NRF=D9E363943C2C55BF932AB280127F7D88; JSESSIONID=0A01D952FCEDEF08C98F1D2DF87DEA8F1D8682E25B; _jc_save_showZtkyts=true; _jc_save_toDate=2015-08-30; BIGipServerotn=1389953290.64545.0000; BIGipServerportal=2949906698.16671.0000; _jc_save_fromStation=%u6210%u90FD%2CCDW; _jc_save_toStation=%u4E4C%u9C81%u6728%u9F50%u5357%2CWMR; _jc_save_fromDate=2015-09-12; _jc_save_wfdc_flag=dc

                myClient.Headers.Add("Content-Type: application/x-www-form-urlencoded");
                myClient.Headers.Add("Accept: image/png, image/svg+xml, image/*;q=0.8, */*;q=0.5");
                myClient.Headers.Add("Accept-Language: zh-CN,zh;q=0.8");
                myClient.Headers.Add("Accept-Encoding: gzip, deflate, sdch");
                //myClient.Headers.Add("User-Agent: Mozilla/4.0 (compatible; MSIE 8.0; Windows NT 5.1; Trident/4.0; .NET CLR 2.0.50727; .NET CLR 3.0.4506.2152; .NET4.0C; .NET4.0E; .NET CLR 3.5.30729; InfoPath.3)");

                myClient.Headers.Add("Pragma: no-cache");
                if (CookieStr != "")
                {
                    myClient.Headers.Add("Cookie: " + CookieStr);
                }
                if (isMobile)
                {
                    myClient.Headers.Add("User-Agent: Mozilla/5.0 (iPhone; CPU iPhone OS 7_1_2 like Mac OS X) AppleWebKit/537.51.2 (KHTML, like Gecko) Mobile/11D257 (367745632)/Worklight/6.0.0");
                }
                else
                {
                    myClient.Headers.Add("User-Agent: Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/43.0.2357.81 Safari/537.36");
                    myClient.Headers.Add("Referer: https://kyfw.12306.cn/otn/login/init");
                }
                if (collect != null && collect.Count > 0)
                {
                    foreach (string key in collect)
                    {
                        try
                        {
                            myClient.Headers.Add(string.Format("{0}: {1}", key.Trim(), collect[key] == null ? "" : collect[key].Trim()));
                        }
                        catch (Exception oe)
                        {
                            Console.WriteLine(oe.Message);
                        }
                    }
                }
                result = myClient.DownloadData(Url);
                //string sContentEncoding = myClient.ResponseHeaders["Content-Encoding"];
            }
            catch (Exception oe)
            {
                if (oe as WebException != null)
                {
                    HttpWebResponse response = (oe as WebException).Response as HttpWebResponse;
                    try
                    {
                        if (response != null)
                        {
                            switch (response.StatusCode)
                            {
                                case HttpStatusCode.Forbidden://403
                                    //if (!isMobile && Url.Contains("12306."))
                                    //    IPHelper.AutoChangeIP(isMobile, ipAddress);
                                    break;
                                case HttpStatusCode.InternalServerError://500
                                    break;
                                case HttpStatusCode.NotFound://404
                                    break;
                                case HttpStatusCode.RequestTimeout://超时
                                    //IPHelper.RemoveUnIP(isMobile, ipAddress, IPHelper.AutoChangeIP(isMobile, ipAddress, false), false);
                                    break;
                                case HttpStatusCode.ServiceUnavailable://503
                                    break;
                                case HttpStatusCode.Unauthorized://401
                                    result = new byte[1];
                                    break;
                            }
                        }
                        else
                        {
                            switch ((oe as WebException).Status)
                            {
                                case WebExceptionStatus.ConnectFailure:
                                    //IPHelper.AutoChangeIP(isMobile, ipAddress, false);
                                    break;
                                case WebExceptionStatus.ConnectionClosed:
                                    break;
                                case WebExceptionStatus.Timeout:
                                    //if (Url.Contains("login"))
                                    //    IPHelper.RemoveUnIP(isMobile, ipAddress, IPHelper.AutoChangeIP(isMobile, ipAddress, false), false);
                                    break;
                            }
                        }
                    }
                    catch { }
                    finally
                    {
                        try
                        {
                            if (response != null)
                            {
                                response.Close();
                                response = null;
                            }
                        }
                        catch { }
                    }
                }
            }
            finally
            {
                try
                {
                    if (myClient.IsBusy)
                        myClient.CancelAsync();
                }
                catch { }
                myClient.IsUsed = false;
                //try
                //{
                //    myClient.Dispose();
                //}
                //catch { }
                //try
                //{
                //    myClient = null;
                //}
                //catch { }
            }
            try
            {
                Url = null;
                ipAddress = null;
                strPost = null;
                collect = null;
            }
            catch { }
            return result;
        }

        #region Web

        public static string GetHtml(string url, double timeOut)
        {
            return GetHtml(url, "", "", "", 1, timeOut > 0 ? (int)timeOut : 2);
        }

        public static string GetHtml(string url, int retryCount = 1)
        {
            return GetHtml(url, "", "", "", retryCount);
        }

        public static string GetHtml(string url, int retryCount, ref long nMsec)
        {
            string strTmp = string.Empty;
            if (retryCount <= 0)
                retryCount = 1;
            for (int i = 0; i < retryCount; i++)
            {
                strTmp = GetHtml(url, "", "", "", 1);
                if (!string.IsNullOrEmpty(strTmp))
                    break;
            }
            return strTmp;
        }

        public static string GetHtml(string url, string cookie, string ipAddress, string post = "", int retryCount = 1, int timeOut = 2)
        {
            string strTmp = string.Empty;
            if (retryCount <= 0)
                retryCount = 1;
            for (int i = 0; i < retryCount; i++)
            {
                strTmp = GetHtml(url, ref cookie, ipAddress, post, "", timeOut);
                if (!string.IsNullOrEmpty(strTmp))
                    break;
            }
            if (strTmp.Equals(" "))
                strTmp = "";
            return strTmp;
        }

        public static string GetHtml(string url, ref string cookie, string ipAddress, string post = ""
            , int retryCount = 1, int timeOut = 2, bool isMobile = false)
        {
            string strTmp = string.Empty;
            retryCount = retryCount <= 0 ? 1 : retryCount;
            for (int i = 0; i < retryCount; i++)
            {
                strTmp = GetHtml(url, ref cookie, ipAddress, post, "", timeOut, isMobile);
                if (!string.IsNullOrEmpty(strTmp))
                    break;
            }
            return strTmp;
        }

        #endregion

        #region Mobile

        public static string GetMobileHtml(string url, ref string cookie, string ipAddress = "", string post = ""
            , int timeOut = 2, NameValueCollection collect = null)
        {
            return GetHtml(url, ref cookie, ipAddress, post, "", timeOut, true, collect);
        }

        public static string GetMobileHtml(string url, ref string cookie, out bool isCache, string ipAddress = "", string post = ""
            , int timeOut = 2, NameValueCollection collect = null)
        {
            return GetHtml(url, ref cookie, out isCache, ipAddress, post, "", timeOut, true, collect);
        }

        public static string GetMobileHtml(string url, ref string cookie, string post, NameValueCollection collect = null, int timeOut = 1)
        {
            return GetHtml(url, ref cookie, "", post, "", timeOut, true, collect);
        }

        public static string GetMobileHtml(string url, string cookie, string post, NameValueCollection collect = null, int retryCount = 1)
        {
            return GetMobileHtml(url, ref cookie, post, collect, retryCount);
        }

        public static string GetMobileHtml(string url, int retryCount = 1)
        {
            return GetMobileHtml(url, "", "", null, retryCount);
        }

        public static string GetMobileHtml(string url, double timeOut)
        {
            return GetMobileHtml(url, "", "", "", 1, timeOut > 0 ? (int)timeOut : 2);
        }

        public static string GetMobileHtml(string url, string cookie, string ipAddress, string post = "", int retryCount = 1, int timeOut = 2, NameValueCollection pubCollect = null)
        {
            string strTmp = string.Empty;
            retryCount = retryCount <= 0 ? 1 : retryCount;
            for (int i = 0; i < retryCount; i++)
            {
                strTmp = GetHtml(url, ref cookie, ipAddress, post, "", timeOut, true, pubCollect);
                if (!string.IsNullOrEmpty(strTmp))
                    break;
            }
            return strTmp;
        }

        #endregion

        public static string GetHtml(string Url, ref string CookieStr, string ipAddress = ""
            , string strPost = "", string Referer = "", int timeOut = 2, bool isMobile = false, NameValueCollection collect = null)
        {
            bool isCache = false;
            return GetHtml(Url, ref CookieStr, out isCache, ipAddress, strPost, Referer, timeOut, isMobile, collect);
        }

        public static string GetHtml(string Url, ref string CookieStr, out bool isCache, string ipAddress = ""
            , string strPost = "", string Referer = "", int timeOut = 2, bool isMobile = false, NameValueCollection collect = null)
        {
            isCache = false;
            DateTime dtStart = DateTime.Now;
            string result = "";
            bool is12306 = Url.Contains("12306.cn");
            CNNWebClient myClient = GetOneClient();
            try
            {
                myClient.Headers.Clear();
                //if (isMobile)
                //    myClient.Credentials = CredentialCache.DefaultCredentials;
                myClient.Timeout = timeOut;
                myClient.isMobile = isMobile;
                myClient.StrIPAddress = ipAddress;

                if (!Url.Contains("leftTicket/query"))
                    myClient.Headers.Add("Content-Type: application/x-www-form-urlencoded; charset=UTF-8");
                if (Url.Contains("confirmSingleForQueueAsys"))
                    myClient.Headers.Add("Accept: application/json, text/javascript, */*; q=0.01");
                else
                    myClient.Headers.Add("Accept: text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8");
                myClient.Headers.Add("Accept-Language: zh-CN,zh;q=0.8");
                myClient.Headers.Add("Cache-Control: no-cache");

                if (is12306)
                {
                    if (!Url.Contains("confirmSingleForQueueAsys"))
                        myClient.Headers.Add("X-Requested-With: XMLHttpRequest");
                }
                else
                {
                    if (!string.IsNullOrEmpty(Referer))
                        myClient.Headers.Add("Referer: " + Referer);
                }
                if (!string.IsNullOrEmpty(CookieStr))
                {
                    myClient.Headers.Add("Cookie: " + CookieStr);
                }
                if (is12306 || Url.Contains("oldfish") || Url.Contains("train")
                    || Url.Contains("?op=") || Url.Contains("ip.cn") || Url.Contains(".ashx"))
                    myClient.Encoding = Encoding.UTF8;
                if (collect != null && collect.Count > 0)
                {
                    foreach (string key in collect)
                    {
                        try
                        {
                            myClient.Headers.Add(string.Format("{0}: {1}", key.Trim(), collect[key] == null ? "" : collect[key].Trim()));
                        }
                        catch (Exception oe)
                        {
                            Console.WriteLine(oe.Message);
                        }
                    }
                }
                if (isMobile)
                {
                    myClient.Headers.Add("User-Agent: Mozilla/5.0 (iPhone; CPU iPhone OS 7_1_2 like Mac OS X) AppleWebKit/537.51.2 (KHTML, like Gecko) Mobile/11D257 (367745632)/Worklight/6.0.0");
                }
                //if (Url.Contains("/query?"))
                //{
                //    result = _12306Helper.lstQuery[(int)(DateTime.Now.Ticks % 4)];
                //}
                //else
                {
                    if (string.IsNullOrEmpty(strPost))
                        result = myClient.DownloadString(new Uri(Url, is12306));
                    else
                        result = myClient.UploadString(new Uri(Url, is12306), strPost);
                }
                var loc = myClient.ResponseHeaders["Location"];
                if (!string.IsNullOrEmpty(loc) && !Url.Equals(loc) && !loc.Contains("error.html"))
                {
                    if (is12306 && !loc.Contains("12306.cn"))
                    {
                        //if (!myClient.ResponseHeaders["Location"].Contains("train"))
                        //    IPHelper.AutoChangeIP(isMobile, ipAddress);
                    }
                    else
                    {
                        return GetHtml(loc, ref CookieStr, ipAddress, strPost, Referer, timeOut);
                    }
                }
                loc = null;
                //if (Url.Contains("baidu") && !string.IsNullOrEmpty(myClient.ResponseHeaders["Date"]))//header.Url.Contains("12306") ||
                //{
                //    try
                //    {
                //        result = myClient.ResponseHeaders["Date"];
                //    }
                //    catch { }
                //}
                if (string.IsNullOrEmpty(result))
                {
                    result = " ";
                }
            }
            catch (OutOfMemoryException oe)
            {
                MemoryManager.ClearMemory();
                Console.WriteLine(oe.Message);
            }
            catch (Exception oe)
            {
                if (oe.Message.Contains("套接字"))
                {
                    Console.WriteLine(oe.Message);
                }
                if (oe as WebException != null)
                {
                    HttpWebResponse response = (oe as WebException).Response as HttpWebResponse;
                    try
                    {
                        if (response != null)
                        {
                            switch (response.StatusCode)
                            {
                                case HttpStatusCode.Forbidden://403
                                    if (is12306)
                                    {
                                        if (Url.Contains("leftTicket/query"))
                                        {
                                            result = "403";
                                        }
                                        else
                                        {
                                            if (response != null)
                                            {
                                                result = new StreamReader(response.GetResponseStream()).ReadToEnd().Trim();
                                            }
                                        }
                                        //if (!result.StartsWith("bad"))
                                        //    IPHelper.AutoChangeIP(isMobile, ipAddress);
                                    }
                                    break;
                                case HttpStatusCode.MethodNotAllowed://405
                                    //if (Url.Contains("12306."))
                                    //{
                                    //    if (response != null)
                                    //    {
                                    //        result = new StreamReader(response.GetResponseStream()).ReadToEnd().Trim();
                                    //    }
                                    //    if (!result.StartsWith("bad"))
                                    //        IPHelper.AutoChangeIP(isMobile, ipAddress);
                                    //}
                                    break;
                                case HttpStatusCode.Unauthorized:
                                    break;
                                case HttpStatusCode.InternalServerError://500
                                    break;
                                case HttpStatusCode.NotFound://404
                                    break;
                                case HttpStatusCode.RequestTimeout://超时
                                    //if (new TimeSpan(DateTime.Now.Ticks - dtStart.Ticks).TotalSeconds > CommonString.NMaxRequestTimeOut)
                                    //{
                                    //    IPHelper.RemoveUnIP(isMobile, ipAddress, IPHelper.AutoChangeIP(isMobile, ipAddress, false), false);
                                    //}
                                    break;
                                case HttpStatusCode.ServiceUnavailable://503
                                    break;
                            }
                        }
                    }
                    catch { }
                    finally
                    {
                        try
                        {
                            if (response != null)
                            {
                                response.Close();
                                response = null;
                            }
                        }
                        catch { }
                    }
                }
            }
            finally
            {
                try
                {
                    Url = null;
                    strPost = null;
                    ipAddress = null;
                    strPost = null;
                    Referer = null;
                    collect = null;
                }
                catch { }
                try
                {
                    if (myClient != null && myClient.ResponseHeaders != null && !string.IsNullOrEmpty(myClient.ResponseHeaders["Age"]))
                        isCache = true;
                }
                catch { }
                try
                {
                    if (myClient.IsBusy)
                        myClient.CancelAsync();
                }
                catch { }
                myClient.IsUsed = false;
                //try
                //{
                //    myClient.Dispose();
                //}
                //catch { }
                //try
                //{
                //    myClient = null;
                //}
                //catch { }
            }
            return result;
        }

        private static string RequestNoSyncHtml(string Url, ref string CookieStr, out bool isCache, string ipAddress = ""
            , string strPost = "", string Referer = "", NameValueCollection collect = null, int timeOut = 2, bool isMobile = false)
        {
            isCache = false;
            return GetHtml(Url, ref CookieStr, out isCache, ipAddress, strPost, "", timeOut, isMobile, collect);
        }

        #region WebSync

        //public static string GetSyncHtml(string url, int retryCount = 1)
        //{
        //    return GetSyncHtml(url, "", "");
        //}

        //public static string GetSyncHtml(string url, double timeOut)
        //{
        //    return GetSyncHtml(url, "", "", "", 1, timeOut > 0 ? (int)timeOut : 2);
        //}

        public static string GetSyncHtml(string url, string cookie, string ipAddress = "", string post = "", int retryCount = 1, int timeout = 2)
        {
            return GetSyncHtml(url, ref cookie, ipAddress, post, null, retryCount, timeout);
        }

        public static string GetSyncHtml(string url, ref string cookie, string ipAddress = ""
            , string post = "", NameValueCollection collect = null, int retryCount = 1, int timeout = 2)
        {
            bool isCache = false;

            return GetSyncHtml(url, ref cookie, out isCache, ipAddress, post, collect, retryCount, timeout);
        }

        public static string GetSyncHtml(string url, ref string cookie, out bool isCache, string ipAddress = "", string post = ""
            , NameValueCollection collect = null, int retryCount = 1, int timeout = 2, bool isMobile = false)
        {
            isCache = false;
            string strTmp = string.Empty;
            retryCount = retryCount <= 0 ? 1 : retryCount;
            timeout = timeout < 0 ? 2 : timeout;
            for (int i = 0; i < retryCount; i++)
            {
                GetNoSyncHtml handler = null;
                try
                {
                    handler = new GetNoSyncHtml(RequestNoSyncHtml);
                    System.IAsyncResult async = handler.BeginInvoke(url, ref cookie, out isCache, ipAddress, post, "", collect, timeout, isMobile, null, null);
                    async.AsyncWaitHandle.WaitOne(timeout * 1000, true);//采用异步等待的方式。直到取到合适的值
                    //if (async.IsCompleted)
                    //{
                    //    strTmp = handler.EndInvoke(async);
                    //}
                    strTmp = handler.EndInvoke(ref cookie, out isCache, async);
                    handler = null;
                    async.AsyncWaitHandle.Close();
                    async.AsyncWaitHandle.Dispose();
                    //async.AsyncWaitHandle.SafeWaitHandle.Close();
                    //async.AsyncWaitHandle.SafeWaitHandle.Dispose();
                    async = null;
                }
                catch (OutOfMemoryException oe)
                {
                    MemoryManager.ClearMemory();
                    Console.WriteLine(oe.Message);
                }
                catch (ObjectDisposedException oe)
                {
                    Console.WriteLine(oe.Message);
                }
                catch (Exception oe)
                {
                    Console.WriteLine(oe.Message);
                }
                finally
                {
                    if (handler != null)
                        handler = null;
                }
                if (!string.IsNullOrEmpty(strTmp))
                    break;
            }
            return strTmp;
        }

        #endregion

        private static string GetCookie(string CookieStr)
        {
            string result = "";
            string[] myArray = CookieStr.Split(',');
            if (myArray.Count() > 0)
            {
                //result = "Cookie: ";
                foreach (var str in myArray)
                {
                    string[] CookieArray = str.Split(';');
                    result += CookieArray[0].Trim();
                    result += "; ";
                }
                result = result.Substring(0, result.Length - 2);
            }
            return result;
        }

    }
    ///// <summary>
    ///// 过期时回调委托
    ///// </summary>
    ///// <param name="userdata"></param>
    //public delegate void TimeoutCaller(object userdata);

    public class CNNWebClient : WebClient
    {
        ~CNNWebClient()
        {
            this.Dispose(false);
        }

        public new void Dispose()
        {
            this.Dispose(true);
            GC.SuppressFinalize(this);
        }

        //private Calculagraph _timer;
        private int _timeOut = 3;
        private string strIPAddress = "";
        public bool isMobile = false;
        public bool IsUsed = false;

        public string StrIPAddress
        {
            get { return strIPAddress; }
            set { strIPAddress = value; }
        }

        /// <summary>
        /// 过期时间
        /// </summary>
        public int Timeout
        {
            get
            {
                if (_timeOut <= 0)
                    _timeOut = 3;
                return _timeOut;
            }
            set
            {
                if (value <= 0)
                    _timeOut = 3;
                _timeOut = value;
            }
        }

        //public bool RemoteCertificateValidationCallback(object sender, X509Certificate certificate, X509Chain chain, SslPolicyErrors errors)
        //{
        //    return true;
        //}

        /// <summary>
        /// 重写GetWebRequest,添加WebRequest对象超时时间
        /// </summary>
        /// <param name="address"></param>
        /// <returns></returns>
        protected override WebRequest GetWebRequest(Uri address)
        {
            //if (DateTime.Now.Second % 2 == 0)
            //{
            //    //System.Threading.Thread.Sleep(1);
            //    System.GC.Collect();
            //}
            //System.GC.Collect();
            if (!string.IsNullOrEmpty(StrIPAddress))
                address = new Uri(address.Scheme + "://" + StrIPAddress + address.PathAndQuery, true);
            //address = new Uri(address.AbsoluteUri.Replace(address.Host, StrIPAddress), true);
            //if (isMobile)
            //    ServicePointManager.ServerCertificateValidationCallback = new RemoteCertificateValidationCallback(this.RemoteCertificateValidationCallback);
            HttpWebRequest request = null;
            try
            {
                request = (HttpWebRequest)base.GetWebRequest(address);
            }
            catch (Exception oe)
            {
                request = (HttpWebRequest)WebRequest.Create(address);
                Console.WriteLine(oe.Message);
            }
            //address.AbsoluteUri.HorspoolIndex("leftTicket") > 0 ? (HttpWebRequest)HttpWebRequest.Create(address.AbsoluteUri) :
            if (request.Proxy != null)
            {
                request.Proxy = null;
            }
            request.ProtocolVersion = HttpVersion.Version11;
            //是否使用 Nagle 不使用 提高效率 
            request.ServicePoint.UseNagleAlgorithm = false;
            //最大连接数 
            request.ServicePoint.ConnectionLimit = 1000;
            //数据是否缓冲 false 提高效率  
            request.AllowWriteStreamBuffering = false;
            request.ServicePoint.Expect100Continue = false;
            request.Headers.Add("Accept-Encoding: gzip, deflate");
            request.AutomaticDecompression = DecompressionMethods.Deflate | DecompressionMethods.GZip;
            if (!string.IsNullOrEmpty(strIPAddress) || address.AbsoluteUri.ToString().Contains("12306."))
            {
                request.AllowAutoRedirect = false;
                request.KeepAlive = false;
                request.Host = isMobile ? "mobile.12306.cn" : "kyfw.12306.cn";
                request.UserAgent = "Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/43.0.2357.81 Safari/537.36";
                if (!isMobile)
                {
                    if (address.AbsoluteUri.ToString().Contains("passcode"))
                    {
                        //request.UserAgent = CommonString.StrCommonAgent;
                        request.AllowAutoRedirect = false;
                    }
                    //else if (address.AbsoluteUri.ToString().EndsWith("message.wav"))
                    //{
                    //    request.UserAgent = "NSPlayer/12.00.7601.17514 WMFSDK/12.00.7601.17514";
                    //    request.AllowAutoRedirect = false;
                    //}
                    else
                    {
                        request.Referer = "https://kyfw.12306.cn/otn/leftTicket/init";
                        //request.Headers.Add("Origin", "https://kyfw.12306.cn");
                        //request.UserAgent = CommonMethod.GetRandomAgent();// +CommonString.serverTime.Ticks;
                    }
                }
                else
                {
                    request.Credentials = CredentialCache.DefaultCredentials;
                }
                if (address.AbsoluteUri.ToString().Contains("/query"))
                {
                    request.Headers.Add("If-None-Match", DateTime.Now.Ticks.ToString());
                    request.CachePolicy = new HttpRequestCachePolicy(HttpRequestCacheLevel.NoCacheNoStore);
                    //SetHeaderValue(request.Headers, "If-Modified-Since", "0");
                    //strIPAddress = string.Format("{0}.{1}.{2}.{3}", new Random().Next(10, 120), new Random().Next(120, 250), new Random().Next(130, 250), new Random().Next(1, 250));
                    //CommonMethod.SetHeaderValue(request.Headers, "X-Forwarded-For", strIPAddress);
                    //CommonMethod.SetHeaderValue(request.Headers, "Proxy-Client-IP", strIPAddress);
                    //CommonMethod.SetHeaderValue(request.Headers, "WL-Proxy-Client-IP", strIPAddress);
                }

                //else if (address.AbsoluteUri.ToString().Contains("passcode"))
                //{
                //    request.KeepAlive = true;
                //}
                //CommonMethod.SetHeaderValue(request.Headers, "If-Modified-Since", "Wed, 31 Dec 1969 16:00:00 GMT");
            }
            else
            {
                request.AllowAutoRedirect = false;
                request.KeepAlive = true;
                request.UserAgent = "Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/43.0.2357.81 Safari/537.36";
            }
            //request.IfModifiedSince = new DateTime(1970, 1, 1);
            //if (!isImg)
            //{

            //request.Headers.Add("If-None-Match", DateTime.Now.Ticks.ToString());
            //}
            try
            {
                request.Timeout = 1000 * Timeout;
                //if (request.Host.Contains("12306.cn"))
                //    request.ReadWriteTimeout = 100;// *Timeout;
                //else
                request.ReadWriteTimeout = 1000;// *Timeout;
            }
            catch { }
            if (request.Proxy != null)
                request.Proxy = GlobalProxySelection.GetEmptyWebProxy();
            //HttpHelper.SetHeaderValue(request.Headers, "Connection", "Close");
            return request;
        }

        //protected override WebResponse GetWebResponse(WebRequest request)
        //{
        //    WebResponse result = null;
        //    try
        //    {
        //        result = base.GetWebResponse(request);
        //    }
        //    catch (Exception oe)
        //    {
        //        Console.WriteLine(oe.Message);
        //    }
        //    finally
        //    {
        //        //request.Abort();
        //        request = null;
        //    }
        //    return result;
        //}

        void SetHeaderValue(WebHeaderCollection header, string name, string value)
        {
            var property = typeof(WebHeaderCollection).GetProperty("InnerCollection",
                System.Reflection.BindingFlags.Instance | System.Reflection.BindingFlags.NonPublic);
            if (property != null)
            {
                var collection = property.GetValue(header, null) as NameValueCollection;
                collection[name] = value;
            }
        }
    }
}
