﻿using CommonLib;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;

namespace Account.Web
{
    public partial class AllUser : System.Web.UI.Page
    {
        protected void Page_Load(object sender, EventArgs e)
        {
            if (CommonLib.BoxUtil.GetStringFromObject(Request.QueryString["pwd"]) != CommonHelper.StrPWD)
            {
                Response.End();
                return;
            }
        }

        protected void btnAllUser_Click(object sender, EventArgs e)
        {
            if (CommonLib.BoxUtil.GetStringFromObject(Request.QueryString["pwd"]) != CommonHelper.StrPWD)
                return;
            hidType.Value = "1";
            DataTable dtTmp = CodeHelper.GetAllCode(txtApp.Text.Trim(), txtType.Text.Trim());
            BindDataTable(dtTmp);
            gvDataSource.DataSource = dtTmp;
            gvDataSource.DataBind();
        }

        protected void btnbtnOnLineUser_Click(object sender, EventArgs e)
        {
            if (CommonLib.BoxUtil.GetStringFromObject(Request.QueryString["pwd"]) != CommonHelper.StrPWD)
                return;
            hidType.Value = "1";
            DataTable dtTmp = CodeHelper.GetOnLineCode(txtApp.Text.Trim(), txtType.Text.Trim());
            BindDataTable(dtTmp);
            gvDataSource.DataSource = dtTmp;
            gvDataSource.DataBind();
        }

        private void BindDataTable(DataTable dtTmp)
        {
            var strTmp= "详细：";
            if (dtTmp != null && dtTmp.Rows.Count > 0)
            {
                var lstUserType = dtTmp.AsEnumerable().Select(p => p["类别"]?.ToString()).Distinct().ToList();
                Dictionary<string, int> dicCount = new Dictionary<string, int>();
                Dictionary<string, int> dicOnLineCount = new Dictionary<string, int>();
                lstUserType.ForEach(userType =>
                {
                    dicCount.Add(userType, dtTmp.AsEnumerable().Where(p => Equals(p["类别"]?.ToString(), userType)).Select(p => p["账号"]?.ToString()).Distinct().Count());
                    dicOnLineCount.Add(userType, dtTmp.AsEnumerable()
                        .Where(p => Equals(p["类别"]?.ToString(), userType) && DateTime.TryParse(p["活动时间"]?.ToString(), out DateTime dtLast) && dtLast > ServerTime.LocalTime.AddDays(-1))
                        .Select(p => p["账号"]?.ToString()).Distinct().Count());
                });
                if (dicCount.Count > 0)
                {
                    int allCount = 0;
                    int allOnLineCount = 0;
                    foreach (var item in dicCount)
                    {
                        allCount += item.Value;
                        if (dicOnLineCount.ContainsKey(item.Key))
                        {
                            allOnLineCount += dicOnLineCount[item.Key];
                        }
                        strTmp += string.Format("{0}:{1}{2} "
                            , item.Key, item.Value
                            , dicOnLineCount.ContainsKey(item.Key) ? string.Format("(在线{0})", dicOnLineCount[item.Key]) : ""
                            );
                    }
                    int allUserCount = RdsCacheHelper.LstAccountCache.GetLoginUserCouunt();
                    strTmp += string.Format("<br />共计：{0}个，总在线：{1}，会员{2}", allCount, allUserCount, allOnLineCount);
                }
            }
            lblCount.Text = strTmp;
        }

        protected void btnVacuum_Click(object sender, EventArgs e)
        {
            if (BoxUtil.GetStringFromObject(Request.QueryString["pwd"]) != CommonHelper.StrPWD)
                return;
            CodeHelper.CleanExpiredAccount();
            CodeHelper.Vacuum();
        }
    }
}