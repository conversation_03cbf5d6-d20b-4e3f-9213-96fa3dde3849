﻿using CommonLib;
using System;
using System.Collections.Generic;
using System.Collections.Specialized;
using System.IO;
using System.Web;

namespace TableOcr
{
    /// <summary>
    /// http://ai.exocr.com/v1_table_api.html?temp_code=306
    /// </summary>
    public class YiDaoRec : BaseTableRec
    {
        public YiDaoRec()
        {
            OcrType = TableOcrType.易道博识;
            MaxExecPerTime = 23;

            LstJsonPreProcessArray = new List<object>() { "tables", 0, "cells" };
            LstRowIndex = new List<object>() { "row_index" };
            LstColumnIndex = new List<object>() { "col_index" };
            LstJsonResultProcessArray = new List<object>() { "words" };
            RowIndexIsArray = false;
            IsRowIndexAddOne = true;
        }

        protected override string GetHtml(OcrContent content)
        {
            var byt = Convert.FromBase64String(content.strBase64);
            var result = GetOcrResult(byt);
            return result;
        }

        private string GetOcrResult(byte[] content)
        {
            var result = "";
            try
            {
                var url = "http://ai.exocr.com/apqi/api/reco/all";
                var file = new UploadFileInfo()
                {
                    Name = "image_binary",
                    Filename = "1.jpg",
                    ContentType = "image/jpeg",
                    Stream = new MemoryStream(content)
                };
                var values = new NameValueCollection() {
                    { "reco_id","306"}
                };
                var header = new NameValueCollection() {
                    { "Refer","http://ai.exocr.com/ocr/"}
                };
                result = PostFile(url, new[] { file }, values, header);
            }
            catch (Exception)
            {

            }
            return result;
        }

    }
}