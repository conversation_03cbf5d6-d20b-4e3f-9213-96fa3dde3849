﻿<?xml version="1.0"?>
<configuration>
  <configSections>
    <section name="Enterprise.Framework.Redis" type="Enterprise.Framework.Redis.Config.RedisConfigReader,Enterprise.Framework.Redis"/>
    <section name="ObjectItemTypeSetting" type="CommonLib.UserConfig.ObjectItemConfigurationSectionHandler, CommonLib" />
  </configSections>
  <!--
    有关 web.config 更改的说明，请参见 http://go.microsoft.com/fwlink/?LinkId=235367。

    可在 <httpRuntime> 标记上设置以下特性。
      <system.Web>
        <httpRuntime targetFramework="4.5.2" />
      </system.Web>
  -->
  <system.web>
    <compilation debug="true" targetFramework="4.5" >
      <assemblies>
        <add assembly="System.Runtime, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
      </assemblies>
    </compilation>
    <customErrors mode="Off"/>
    <pages controlRenderingCompatibilityVersion="4.0"/>
    <httpRuntime enableVersionHeader="false"/>
  </system.web>
  <appSettings>
    <add key="FileHostUrl" value="http://**************:7070/"/>
    <!--打码限制频率（毫秒）-->
    <add key="NBlackMSencond" value="1000"/>
    <!--单位时间内最多打多少码-->
    <add key="NMaxExecPerSecond" value="200"/>
    <!--打码超过频率黑名单秒数-->
    <add key="NMaxExecBlackSecond" value="3"/>
    <!--是否开启打码余额限制-->
    <add key="IsValidateCodeAmount" value="False"/>
    <!--打码超时时间（秒）-->
    <add key="NGetCodeTimeOut" value="10"/>
    <add key="DBFilePath" value=""/>
    <!--http://g.histsci.org/,https://www.sanzhima.com/-->
    <add key="OcrGroupConfig" value="0,不限|1,百度|2,腾讯|4,有道|5,搜狗|6,讯飞|7,迅捷|8,VIVO|10,学而思|11,汉王"/>
    <add key="TipMsg" value="1月28日0点起，限量开放《终身版》！\n更有【一元抢】活动，专业版/企业版，通通一元，先到先得，敬请期待！"/>
    <add key="IsRegToProfessional" value="True"/>
  </appSettings>
  <ObjectItemTypeSetting>
    <LstSettings>
      <ObjectTypeSetting Name="ServerGroup">
        <LstItem>
          <ObjectTypeItem Name="不限" Code="0" Desc="" Remark="" DescUrl="" UpdateUrl="" Enable="true"/>
          <ObjectTypeItem Name="百度" Code="1" Desc="" Remark="" DescUrl="" UpdateUrl="" Enable="true" />
          <ObjectTypeItem Name="腾讯" Code="2" Desc="" Remark="" DescUrl="" UpdateUrl="" Enable="true" />
          <ObjectTypeItem Name="阿里" Code="3" Desc="" Remark="" DescUrl="" UpdateUrl="" Enable="false" />
          <ObjectTypeItem Name="有道" Code="4" Desc="" Remark="" DescUrl="" UpdateUrl="" Enable="true" />
          <ObjectTypeItem Name="搜狗" Code="5" Desc="" Remark="" DescUrl="" UpdateUrl="" Enable="true" />
          <ObjectTypeItem Name="讯飞" Code="6" Desc="" Remark="" DescUrl="" UpdateUrl="" Enable="true" />
          <ObjectTypeItem Name="迅捷" Code="7" Desc="" Remark="" DescUrl="" UpdateUrl="" Enable="true" />
          <ObjectTypeItem Name="VIVO" Code="8" Desc="" Remark="" DescUrl="" UpdateUrl="" Enable="true" />
          <ObjectTypeItem Name="金山" Code="9" Desc="" Remark="" DescUrl="" UpdateUrl="" Enable="false" />
          <ObjectTypeItem Name="学而思" Code="10" Desc="" Remark="" DescUrl="" UpdateUrl="" Enable="true" />
          <ObjectTypeItem Name="汉王" Code="11" Desc="" Remark="" DescUrl="" UpdateUrl="" Enable="true" />
        </LstItem>
      </ObjectTypeSetting>
      <ObjectTypeSetting Name="LocalGroup">
        <LstItem>
          <ObjectTypeItem Name="飞浆Mobile" Code="90001" Desc="百度旗下-飞桨开源的移动版文字识别模型套件。" Remark="" DescUrl="https://www.paddlepaddle.org.cn/hub/scene/ocr" UpdateUrl="" />
          <ObjectTypeItem Name="飞浆Server" Code="90002" Desc="百度旗下-飞桨开源的服务器版文字识别模型套件" Remark="" DescUrl="https://www.paddlepaddle.org.cn/hub/scene/ocr" UpdateUrl="" />
          <ObjectTypeItem Name="中文识别Lite" Code="90003" Desc="超轻量级中文文字识别套件，支持ncnn、mnn、tnn推理(DbNet + Crnn + AngleNet)" Remark="" DescUrl="https://github.com/DayBreak-u/chineseocr_lite" UpdateUrl="" />
        </LstItem>
      </ObjectTypeSetting>
    </LstSettings>
  </ObjectItemTypeSetting>
  <Enterprise.Framework.Redis>
    <RedisDB Name="OPS_Cache" WritePoolSize="1000" ReadPoolSize="1000">
      <RedisServer Host="ocr.oldfish.cn" Port="3600" IsReadOnly="False"/>
    </RedisDB>
  </Enterprise.Framework.Redis>
  <runtime>
    <gcServer enabled="true"/>
    <assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
      <dependentAssembly>
        <assemblyIdentity name="System.Runtime" publicKeyToken="b03f5f7f11d50a3a" culture="neutral"/>
        <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******"/>
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Threading.Tasks" publicKeyToken="b03f5f7f11d50a3a" culture="neutral"/>
        <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******"/>
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Newtonsoft.Json" culture="neutral" publicKeyToken="30ad4fe6b2a6aeed"/>
        <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******"/>
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="log4net" publicKeyToken="669e0ddf0bb1aa2a" culture="neutral"/>
        <bindingRedirect oldVersion="0.0.0.0-1.2.15.0" newVersion="1.2.15.0"/>
      </dependentAssembly>
    </assemblyBinding>
  </runtime>
</configuration>