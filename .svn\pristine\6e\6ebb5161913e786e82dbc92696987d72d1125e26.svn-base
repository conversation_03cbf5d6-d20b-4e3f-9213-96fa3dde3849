﻿using System;

namespace CommonLib
{
    public class CusImageEntity : IDisposable
    {
        public ProcessState State { get; set; }

        public string ResultFrom { get; set; }

        public string Error { get; set; }

        public string StrImg { get; set; }

        public string ImgUrl { get; set; }

        public string StrCode { get; set; }

        public bool IsLogin { get; set; }

        public string StrIndex { get; set; }

        //public bool IsResult { get; set; }

        public string StrHanZi { get; set; }

        public UserTypeEnum UserType { get; set; }

        public OcrType OcrType { get; set; }

        public OcrGroupType OcrGroup { get; set; }

        public int? ProcessId { get; set; }

        /// <summary>
        /// 是否从左到右
        /// </summary>
        public bool IsFromLeftToRight { get; set; }

        /// <summary>
        /// 是否从上到下
        /// </summary>
        public bool IsFromTopToDown { get; set; }

        public void DisposeStrs()
        {
            StrImg = null;
            StrCode = null;
            StrIndex = null;
            StrHanZi = null;
            Error = null;
            ResultFrom = null;
        }

        public long DtAdd { get; set; }

        public long DtUser { get; set; }

        public long DtReceived { get; set; }

        public long DtExpired { get; set; }

        public bool IsValidate
        {
            get { return DtExpired >= ServerTime.DateTime.Ticks; }
        }

        private string fileExt;
        public string FileExt
        {
            get
            {
                if (string.IsNullOrEmpty(fileExt))
                {
                    fileExt = "png";
                }
                return fileExt;
            }
            set
            {
                fileExt = value;
            }
        }

        public int FileContentLength { get; set; }
        public TransLanguageTypeEnum FromLanguage { get; set; }
        public TransLanguageTypeEnum ToLanguage { get; set; }

        protected virtual void Dispose(bool disposing)
        {
            if (disposing)
            {
                DisposeStrs();
            }
        }
        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        public string ToStr()
        {
            return string.Format("【{5}】_{0}_总：{1}ms，消息：{2}ms，处理：{3}ms，[{6}_{7}]：{4}  {8}"
                    , State.ToString()
                    , new TimeSpan(ServerTime.DateTime.Ticks - DtAdd).TotalMilliseconds.ToString("F0")
                    , new TimeSpan(DtReceived - DtAdd).TotalMilliseconds.ToString("F0")
                    //, (processEntity.DtStartProcess - processEntity.DtReceived).TotalMilliseconds.ToString("F0")
                    , new TimeSpan(ServerTime.DateTime.Ticks - DtReceived).TotalMilliseconds.ToString("F0")
                    , ""//StrCode
                    , UserType
                    , ResultFrom
                    , IsLogin ? "0" : "1"
                    , Error);
        }
    }
}