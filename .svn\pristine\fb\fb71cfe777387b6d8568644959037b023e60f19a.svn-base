﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Windows.Forms;
using System.Threading.Tasks;
using System.IO;
using NewTicket;
using System.Text.RegularExpressions;

namespace IPScan
{
    public partial class frmMain : Form
    {
        public frmMain()
        {
            InitializeComponent();
            CheckForIllegalCrossThreadCalls = false;
        }


        private List<string> lstSearchIPS = new List<string>();

        private void btnStart_Click(object sender, EventArgs e)
        {
            //if (string.IsNullOrEmpty(txtIPS.Text.Trim()))
            //    return;
            bgLoad.RunWorkerAsync();
        }

        private int maxThread = 30;
        private void bgLoad_DoWork(object sender, DoWorkEventArgs e)
        {
            if (chkIP.Checked)
            {
                PingItemsByIndex();
                return;
            }
            string[] strIP = txtIPS.Text.Replace("\r", "").Replace("\t", "").Trim().Split(new[] { "\n" }, StringSplitOptions.RemoveEmptyEntries);
            if (strIP.Length <= 0)
                return;
            string strContext = "";
            int index = 0;
            List<string> lstAllTmp = new List<string>();
            lstAllTmp.AddRange(strIP);
            maxThread = 50;
            while (lstAllTmp.Count > 0)
            {
                try
                {
                    List<string> lstTmp = lstAllTmp.GetRange(0, lstAllTmp.Count > maxThread ? maxThread : lstAllTmp.Count);
                    //Parallel.ForEach<string>(lstTmp, str =>
                    foreach (var str in lstTmp)
                    {
                        index++;
                        lblInfo.Text = string.Format("开始处理第{2}/{1}个IP：{0}", str, strIP.Count(), index);
                        if (str.IndexOf("-") < 0)
                        {
                            for (int i = 0; i < 50; i++)
                            {
                                strContext = HttpHelper.GetHtml("http://int.dpool.sina.com.cn/iplookup/iplookup.php?format=json&ip=" + str, 5, 30000);
                                if (!string.IsNullOrEmpty(strContext) && strContext.Contains("start\":\""))
                                {
                                    break;
                                }
                                else
                                {
                                    strContext = "";
                                }
                            }
                            if (string.IsNullOrEmpty(strContext))
                                strContext = HttpHelper.GetHtml("http://www.ipv6home.cn/ip/?ip=" + str, 5, 30000);
                            if (string.IsNullOrEmpty(strContext))
                                strContext = HttpHelper.GetHtml("http://ipwhois.911cha.com/?action=do", "", "", string.Format("q={0}", str), "", 3, 5000);
                            if (!string.IsNullOrEmpty(strContext))
                            {
                                if (strContext.IndexOf("start\"") >= 0)
                                {
                                    strContext = strContext.Substring(strContext.IndexOf("start\":\"") + "start\":\"".Length).Replace("\",\"end\":\"", "-").Trim();
                                    strContext = strContext.Substring(0, strContext.IndexOf("\"")).Replace(" ", "").Trim();
                                    if (!lstSearchIPS.Contains(strContext))
                                    {
                                        lstSearchIPS.Add(strContext);
                                        txtResult.AppendText("\r\n" + strContext);
                                    }
                                }
                                else if (strContext.IndexOf("inetnum:") > 0)
                                {
                                    strContext = strContext.Substring(strContext.IndexOf("inetnum:") + "inetnum:".Length).Trim();
                                    strContext = strContext.Substring(0, strContext.IndexOf("<br />")).Replace(" ", "").Trim();
                                    if (!lstSearchIPS.Contains(strContext))
                                    {
                                        lstSearchIPS.Add(strContext);
                                        txtResult.AppendText("\r\n" + strContext);
                                    }
                                }
                                else if (strContext.IndexOf("InputIPAddrMessage\">") >= 0)
                                {
                                    strContext = strContext.Substring(strContext.IndexOf("InputIPAddrMessage\">") + "InputIPAddrMessage\">".Length).Trim();
                                    strContext = strContext.Substring(0, strContext.IndexOf("<")).Replace(" ", "").Trim();
                                    if (!lstSearchIPS.Contains(strContext))
                                    {
                                        lstSearchIPS.Add(strContext);
                                        txtResult.AppendText("\r\n" + strContext);
                                    }
                                }
                            }
                        }
                        else
                        {
                            if (!lstSearchIPS.Contains(str))
                            {
                                lstSearchIPS.Add(str);
                                txtResult.AppendText("\r\n" + str);
                            }
                        }
                    }
                    //);
                    lstAllTmp.RemoveRange(0, lstAllTmp.Count > maxThread ? maxThread : lstAllTmp.Count);
                }
                catch { }
            }
            maxThread = 10;
            txtIPS.Text = string.Join("\r\n", lstSearchIPS);
            if (!string.IsNullOrEmpty(txtIPS.Text.Trim()))
            {
                txtResult.Text = "";
                List<string> lstAll = new List<string>();
                lstAll.AddRange(txtIPS.Text.Replace("\r", "").Trim().Split(new string[] { "\n" }, StringSplitOptions.RemoveEmptyEntries));
                index = 0;
                isCanSave = true;
                bgSave.RunWorkerAsync();
                while (lstAll.Count > 0)
                {
                    try
                    {
                        List<string> lstTmp = lstAll.GetRange(0, lstAll.Count > maxThread ? maxThread : lstAll.Count);
                        Parallel.ForEach<string>(lstTmp, str =>
                        {
                            index++;
                            lblInfo.Text = string.Format("开始处理IP段：{0}", str);
                            try
                            {
                                PingItemsByIndex(str);
                            }
                            catch (Exception oe)
                            {
                                Console.WriteLine(oe.Message);
                            }
                        });
                        lstAll.RemoveRange(0, lstAll.Count > maxThread ? maxThread : lstAll.Count);
                    }
                    catch { }
                    lblInfo.Text = string.Format("当前进度:({0}/{1}) 共{2}可用", index, lstAll.Count, lstEnableIP.Count);
                }
                lblInfo.Text = string.Format("全部搞定！");
                //foreach (string str in strListIPS)
                //{
                //    PingItemsByIndex(str);
                //}
            }
        }

        private List<string> lstEnableIP = new List<string>();

        public static long IpToInt(string ip)
        {
            char[] separator = new char[] { '.' };
            string[] items = ip.Split(separator);
            return long.Parse(items[0]) << 24
                    | long.Parse(items[1]) << 16
                    | long.Parse(items[2]) << 8
                    | long.Parse(items[3]);
        }

        public static string IntToIp(long ipInt)
        {
            StringBuilder sb = new StringBuilder();
            sb.Append((ipInt >> 24) & 0xFF).Append(".");
            sb.Append((ipInt >> 16) & 0xFF).Append(".");
            sb.Append((ipInt >> 8) & 0xFF).Append(".");
            sb.Append(ipInt & 0xFF);
            return sb.ToString();
        }

        /// <summary>
        /// 获取指定目录及子目录中所有文件列表
        /// </summary>
        /// <param name="directoryPath">指定目录的绝对路径</param>
        /// <param name="searchPattern">模式字符串，"*"代表0或N个字符，"?"代表1个字符。
        /// 范例："Log*.xml"表示搜索所有以Log开头的Xml文件。</param>
        /// <param name="isSearchChild">是否搜索子目录</param>
        public string[] GetFileNames(string directoryPath, string searchPattern, bool isSearchChild)
        {
            try
            {
                if (isSearchChild)
                {
                    return Directory.GetFiles(directoryPath, searchPattern, SearchOption.AllDirectories);
                }
                else
                {
                    return Directory.GetFiles(directoryPath, searchPattern, SearchOption.TopDirectoryOnly);
                }
            }
            catch (IOException ex)
            {
                throw ex;
            }
        }

        private int maxPoolCount = 50;
        private void PingItemsByIndex(string strIPList = "")
        {
            List<string> lstIPS = new List<string>();
            if (string.IsNullOrEmpty(strIPList))
            {
                maxPoolCount = 100;
                if (!string.IsNullOrEmpty(txtIPS.Text.Trim()))
                {
                    lstIPS.AddRange(txtIPS.Text.Replace("\r", "").Replace("\t", "").Trim().Split(new string[] { "\n" }, StringSplitOptions.RemoveEmptyEntries));
                }
                else
                {
                    string[] strTmp = GetFileNames(Application.StartupPath, "Result.txt", true);
                    if (strTmp != null && strTmp.Length > 0)
                    {
                        foreach (string str in strTmp)
                        {
                            string[] ssAll = File.ReadAllLines(str, Encoding.Default);
                            if (ssAll != null && ssAll.Length > 0)
                            {
                                foreach (string item in ssAll)
                                {
                                    if (!string.IsNullOrEmpty(item) && !string.IsNullOrEmpty(item.Trim()) && item.IndexOf("-") < 0 && item.IndexOf("IPs") < 0)
                                    {
                                        if (!lstIPS.Contains(item.Replace("443   Open", "").Trim()))
                                            lstIPS.Add(item.Replace("443   Open", "").Trim());
                                    }
                                }
                            }
                        }
                    }
                    txtIPS.Text = string.Join("\r\n", lstIPS);
                }
            }
            else
            {
                string strStart = strIPList.Substring(0, strIPList.IndexOf("-"));
                string strEnd = strIPList.Substring(strIPList.IndexOf("-") + 1);
                long NNed = IpToInt(strEnd);
                for (long i = IpToInt(strStart); i <= NNed; i++)
                {
                    lstIPS.Add(IntToIp(i));
                }
            }
            int allCount = lstIPS.Count;
            lstEnableIP = new List<string>();
            if (lstIPS != null && lstIPS.Count > 0)
            {
                int index = 0;
                while (lstIPS.Count > 0)
                {
                    try
                    {
                        lblInfo.Text = string.Format("当前IP段：{3} 进度:({0}/{1})剩余{4} 共{2}可用", index, allCount, lstEnableIP.Count, strIPList, lstIPS.Count);
                        List<string> lstTmp = lstIPS.GetRange(0, lstIPS.Count > maxPoolCount ? maxPoolCount : lstIPS.Count);
                        Parallel.ForEach<string>(lstTmp, str =>
                        {
                            index++;
                            CheckByIP(str.Trim());
                        });
                        lstIPS.RemoveRange(0, lstIPS.Count > maxPoolCount ? maxPoolCount : lstIPS.Count);
                    }
                    catch { }
                }
            }
            txtResult.Text = string.Join("\r\n", lstEnableIP);
            lblInfo.Text = string.Format("IP段：{0}处理完毕！ 共{1}/{2}可用", strIPList, lstEnableIP.Count, allCount);
        }

        public void ReportHost(string strIP = "", bool isMobile = false)
        {
            try
            {
                string url = string.Format("ticket.aspx?op=ip&info={0}&type={1}", strIP, isMobile ? "1" : "0");
                url = GetServerHtml(url, txtServerUrl.Text.Trim());
                url = null;
                strIP = null;
            }
            catch { }
        }

        public string GetServerHtml(string url, string strNowSite, bool isDecodeURL = true, bool isPost = false, string strPost = "")
        {
            string html = "";
            try
            {
                if (isDecodeURL)
                {
                    if (isPost)
                        html = WebClientExt.GetHtml(strNowSite + GetSubStrByURL(url), "", "", strPost, 3, 10);
                    else
                        html = WebClientExt.GetHtml(strNowSite + GetSubStrByURL(url), 3);
                }
                else
                {
                    if (isPost)
                        html = WebClientExt.GetHtml(strNowSite + url, "", "", strPost, 3, 10);
                    else
                        html = WebClientExt.GetHtml(strNowSite + url, 3);
                }
            }
            catch { }
            return html;
        }
        public string StrCommonEncryptKey
        {
            get
            {
                return "!(*_^%$#";
            }
        }

        public string GetSubStrByURL(string strURL)
        {
            string strTmp = "";
            if (!string.IsNullOrEmpty(strURL))
            {
                try
                {
                    if (strURL.Contains("&"))
                    {
                        strTmp = SubString(strURL, "", "&");
                        string strEncrpt = SubString(strURL, "&");
                        strEncrpt = CommonEncryptHelper.DESEncrypt(strEncrpt, StrCommonEncryptKey);
                        //strURL = CommonEncryptHelper.DESDecrypt(strTmp, CommonString.StrCommonEncryptKey);
                        strTmp += "&con=" + System.Web.HttpUtility.UrlEncode(strEncrpt);
                    }
                    else
                    {
                        strTmp = strURL;
                    }
                }
                catch (Exception oe)
                {
                    strTmp = strURL;
                }
            }
            return strTmp;
        }

        public string SubString(string strSource, string strSpilt, string strEnd = "")
        {
            return strSource.SubStringHorspool(strSpilt, strEnd).Trim();
        }

        private void CheckByIP(string str)
        {
            try
            {
                DateTime dtStart = DateTime.Now;
                CheckIP check = new CheckIP();
                check.Ip = str;
                check.Host = "kyfw.12306.cn";
                //check.IsMobile = true;
                //check.Host = "mobile.12306.cn";
                check.Ping(0);
                Console.WriteLine("连接耗时：" + (new TimeSpan(DateTime.Now.Ticks - dtStart.Ticks).TotalMilliseconds) + "ms");
                //lblInfo.Text = string.Format("正在验证CDN:\n[{0}] {1}", str
                //    , (check.IsOutOfTime || check.IsForbidden || check.IsNotValid || check.IsErrorIp) ? check.IsOutOfTime ? "超时" : (check.IsForbidden ? "已封" : (check.IsErrorIp ? "无效" : "不可用"))
                //    : check.Speed.Value.TotalMilliseconds.ToString("F0") + "毫秒");
                if (check.IsOutOfTime || check.IsForbidden || check.IsNotValid || check.IsErrorIp)
                {
                }
                else
                {
                    lstEnableIP.Add(str);
                    if (chkAutoSend.Checked && !string.IsNullOrEmpty(txtServerUrl.Text.Trim()))
                    {
                        ReportHost(str, false);
                    }
                    txtResult.AppendText("\r\n" + str);
                }
            }
            catch (Exception oe)
            {
                Console.WriteLine(oe.Message);
            }
        }

        private bool isStop = false;
        private bool isCanSave = false;
        private string StrFileName = DateTime.Now.ToString("yyyyMMddHHmmss");
        private void bgSave_DoWork(object sender, DoWorkEventArgs e)
        {
            while (!isStop)
            {
                if (isCanSave && lstEnableIP != null && lstEnableIP.Count > 0)
                {
                    File.WriteAllText(Application.StartupPath + "\\" + StrFileName + ".txt", string.Join(",", lstEnableIP), Encoding.Default);
                }
                System.Threading.Thread.Sleep(5000);
            }
        }

        private void btnClear_Click(object sender, EventArgs e)
        {
            txtResult.Clear();
            txtIPS.Clear();
        }

        private bool isShow = true;
        private bool isExit = false;
        private void frmMain_FormClosing(object sender, FormClosingEventArgs e)
        {
            if (!isExit)
            {
                isShow = false;
                this.Hide();
                e.Cancel = true;
            }
        }

        private void notifyIcon1_MouseDoubleClick(object sender, MouseEventArgs e)
        {
            if (isShow)
            {
                this.Hide();
                isShow = false;
            }
            else
            {
                isShow = true;
                this.Show();
            }
        }

        private void 退出ToolStripMenuItem_Click(object sender, EventArgs e)
        {
            isExit = true;
            Application.Exit();
        }

        private void frmMain_Load(object sender, EventArgs e)
        {
            notif.Icon = this.Icon;
        }

        private void btnSubmit_Click(object sender, EventArgs e)
        {
            if (!string.IsNullOrEmpty(txtResult.Text))
            {
                var lstTmp = new List<string>();
                lstTmp.AddRange(txtResult.Text.Split(new string[] { "\r", "\n" }, StringSplitOptions.RemoveEmptyEntries));
                try
                {
                    StringBuilder sb = new StringBuilder();
                    foreach (var item in lstTmp)
                    {
                        if (string.IsNullOrEmpty(item))
                            continue;
                        sb.AppendFormat("{0}|", item);
                        if (sb.Length >= 200)
                        {
                            ReportHost(sb.ToString());
                            sb = new StringBuilder();
                        }
                    }
                    if (!string.IsNullOrEmpty(sb.ToString()))
                    {
                        ReportHost(sb.ToString());
                        sb = new StringBuilder();
                    }
                    sb = null;
                }
                catch { }
            }
        }

        private void btnIPPorts_Click(object sender, EventArgs e)
        {
            btnIPPorts.Enabled = false;
            bgLoadIPInfo.RunWorkerAsync();
        }

        private bool IsIPv4(string input)
        {
            string[] array = input.Split(new char[]
			{
				'.'
			});
            for (int i = 0; i < array.Length; i++)
            {
                if (!IsMatch("^\\d+$", array[i]))
                {
                    return false;
                }
                if (Convert.ToUInt16(array[i]) > 255)
                {
                    return false;
                }
            }
            return true;
        }

        private bool IsMatch(string pattern, string input)
        {
            if (input == null || input == "")
            {
                return false;
            }
            Regex regex = new Regex(pattern);
            return regex.IsMatch(input);
        }

        private bool IsIPAddressString(string ip)
        {
            if (string.IsNullOrEmpty(ip) || ip.Length < 7 || ip.Length > 15)
                return false;
            return true;
        }

        private string regformat = @"^(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])$";

        private bool IsIPAddress(string ip)
        {
            if (string.IsNullOrEmpty(ip) || ip.Length < 7 || ip.Length > 15)
                return false;
            Regex regex = new Regex(regformat, RegexOptions.IgnoreCase);
            return regex.IsMatch(ip);
        }

        private bool IsIntranetIPAddress(string ip)
        {
            return ip.StartsWith("10.") || ip.StartsWith("127.")
                    || ip.StartsWith("192.") || ip.StartsWith("169.") || ip.StartsWith("172.");
        }

        void GetAllComputerIPs()
        {
            var lstDetail = new List<IPDetail>();
            
            string url = string.Format("ticket.aspx?op=speed&type=0&app={0}&mac={1}&reg={2}&ver={3}&user={4}&info={5}"
                , ("speed"), (Environment.UserDomainName)
                , (DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")), (DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"))
                , (Environment.UserName), (""));
            var strIPs = GetServerHtml(url, txtServerUrl.Text.Trim());
            if (!string.IsNullOrEmpty(strIPs))
            {
                strIPs = CommonEncryptHelper.DESDecrypt(strIPs, StrCommonEncryptKey);
                string[] strList = strIPs.Split(new string[] { "|" }, StringSplitOptions.RemoveEmptyEntries);
                if (strList != null && strList.Length > 0)
                {
                    foreach (string str in strList)
                    {
                        if (IsIntranetIPAddress(str))
                            continue;
                        if (!lstDetail.Exists(p => p.StrIp.Equals(str)))
                            lstDetail.Add(new IPDetail() { StrIp = str });
                    }
                }
                strList = null;
            }

            strIPs = WebClientExt.GetHtml("http://12306.ie.sogou.com/12306/2014/background2.js?r=" + DateTime.Now.Ticks, (double)10);//CDN.txt
            if (!string.IsNullOrEmpty(strIPs))
            {
                strIPs = SubString(strIPs, "[\"kyfw.12306.cn\"", "],").Replace("\"", "").Trim();
                string[] strList = strIPs.Split(new string[] { "," }, StringSplitOptions.RemoveEmptyEntries);
                if (strList != null && strList.Length > 0)
                {
                    foreach (string str in strList)
                    {
                        if (!IsIPAddress(str) || IsIntranetIPAddress(str) || lstDetail.Exists(p => p.StrIp.Equals(str)))
                            continue;
                        lstDetail.Add(new IPDetail() { StrIp = str });
                    }
                }
                strList = null;
            }

            if (lstDetail != null && lstDetail.Count > 0)
            {
                lstDetail.RemoveAll((IPDetail ip) => IsIntranetIPAddress(ip.StrIp));//|| ip.nAverageSpeed > 300
            }
            List<string> lstTT = new List<string>();
            string strTmp = "";
            foreach (var item in lstDetail)
            {
                strTmp = item.StrIp.Substring(0, item.StrIp.LastIndexOf(".") + 1);
                strTmp = string.Format("{0}0 {0}255", strTmp);
                if (!lstTT.Contains(strTmp))
                {
                    lstTT.Add(strTmp);
                }
            }
            try
            {
                File.Delete(System.Windows.Forms.Application.StartupPath + "\\ip.txt");
            }
            catch { }
            try
            {
                strTmp = string.Join(Environment.NewLine, lstTT.ToArray());
                //Console.WriteLine(strTmp);
                File.WriteAllText(System.Windows.Forms.Application.StartupPath + "\\ip.txt", strTmp);
            }
            catch { }
            lstDetail.Clear();
            lstDetail = null;
            strIPs = "";
        }

        private void bgLoadIPInfo_DoWork(object sender, DoWorkEventArgs e)
        {
            GetAllComputerIPs();
        }

        private void bgLoadIPInfo_RunWorkerCompleted(object sender, RunWorkerCompletedEventArgs e)
        {
            btnIPPorts.Enabled = true;
            if (File.Exists(System.Windows.Forms.Application.StartupPath + "\\ip.txt"))
            {
                MessageBox.Show(this, "IP段更新成功！", "获取信息完毕！");
            }
            else
            {
                MessageBox.Show(this, "IP段更新失败！", "获取信息完毕！");
            }
        }
    }
}
