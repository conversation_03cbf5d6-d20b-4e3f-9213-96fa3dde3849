﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using monocaptcha;

namespace NewTicket.JiSu
{
    public class JiSuDaMa
    {
        public static bool IsJiSu { get; set; }
        public static System.Collections.Concurrent.ConcurrentBag<monocaptcha.ICaptchaDecoder> lstCache = new System.Collections.Concurrent.ConcurrentBag<monocaptcha.ICaptchaDecoder>();

        public static monocaptcha.ICaptchaDecoder GetOneClient()
        {
            try
            {
                monocaptcha.ICaptchaDecoder myClient = null;
                lock (lstCache)
                {
                    if (lstCache.Count > 0)
                    {
                        myClient = lstCache.FirstOrDefault(p => !p.IsUsed);
                    }
                    if (myClient == null)
                    {
                        myClient = monocaptcha.CaptchaDecoderFactory.getSimpleDecoder();
                        lstCache.Add(myClient);
                    }
                    myClient.IsUsed = true;
                }
                return myClient;
            }
            catch (Exception oe)
            {
                Console.WriteLine(oe.Message);
            }
            return GetOneClient();
        }

        public static List<string> LstChaoRenKey = new List<string>();

        public static string StrChaoRenKey = "";

        public static string GetCode(byte[] array)
        {
            var result = "";
            try
            {
                //应该为每个线程申请一个 decoder
                //多个线程并发调用 decoder 可能会有意外的情况发生
                using (var decoder = monocaptcha.CaptchaDecoderFactory.getSimpleDecoder())
                {
                    try
                    {
                        AnswerResult[] ar = new AnswerResult[1];
                        ar[0] = new AnswerResult();
                        ar[0].setPictureData(array);

                        decoder.decode(StrChaoRenKey, ar);

                        if (ar != null && ar.Length > 0)
                        {
                            result = ar[0].Result;
                        }
                    }
                    catch (Exception oe)
                    {
                        Console.WriteLine(oe.Message);
                    }
                    finally
                    {
                        if (decoder != null)
                            decoder.Dispose();
                    }
                }
            }
            catch (Exception oe)
            {
                Console.WriteLine(oe.Message);
            }
            return result;
        }

        //public static void GetChaoRenKey()
        //{
        //    string strUrl = "http://121.42.195.240:4455/version.txt";
        //    try
        //    {
        //        strUrl = AccountTool.WebClientExt.GetHtml(strUrl, 3);
        //        if (!string.IsNullOrEmpty(strUrl))
        //        {
        //            strUrl = strUrl.Substring(strUrl.LastIndexOf("#") + 1);
        //            if (!string.IsNullOrEmpty(strUrl) && strUrl.Length >= 32)
        //            {
        //                StrChaoRenKey = strUrl;
        //            }
        //        }
        //    }
        //    catch (Exception oe)
        //    {
        //        Console.WriteLine(oe.Message);
        //    }
        //}


    }
    class AnswerResult : monocaptcha.AbstractAnswerResult
    {
        public override void handle(monocaptcha.ResponseEnum status, string answer)
        {
            switch (status)
            {
                case ResponseEnum.OK:
                    Result = answer;
                    break;
                case ResponseEnum.FORMAT_ERROR:
                case ResponseEnum.JPEG_DECODE_ERROR:
                    break;
                case ResponseEnum.DATA_OVERFLOW:
                    break;
                case ResponseEnum.UNKNOWN_TOKEN:
                case ResponseEnum.BALANCE_INSUFFICIENT:
                case ResponseEnum.ILLEGAL_ACCOUNT:
                    if (!string.IsNullOrEmpty(JiSuDaMa.StrChaoRenKey) && JiSuDaMa.LstChaoRenKey.Count > 0)
                    {
                        try
                        {
                            var index = JiSuDaMa.LstChaoRenKey.IndexOf(JiSuDaMa.StrChaoRenKey);
                            index = index >= JiSuDaMa.LstChaoRenKey.Count - 1 ? 0 : index + 1;
                            index = index < 0 ? 0 : index;
                            JiSuDaMa.StrChaoRenKey = JiSuDaMa.LstChaoRenKey[index];
                        }
                        catch (Exception oe)
                        {
                            JiSuDaMa.StrChaoRenKey = JiSuDaMa.LstChaoRenKey[0];
                        }
                    }
                    break;
                case ResponseEnum.SERVER_ERROR:
                    break;
                default:
                    break;
            }
            if (status == monocaptcha.ResponseEnum.OK)
            {
                Result = answer;
                //Console.WriteLine("ok" + "\t answer:" + answer);
            }
            else
            {
            }
        }

        public string Result { get; set; }
    }
}
