﻿using System;
using System.Collections.Generic;
using System.Web;

namespace Account.Web
{
    public class TicketEntity
    {
        private string strNo = "";

        public string StrNo
        {
            get { return strNo; }
            set { strNo = value; }
        }
        private string strDate = "";

        public string StrDate
        {
            get { return strDate; }
            set { strDate = value; }
        }
        private DateTime dtTicket = DateTime.MinValue;

        public DateTime DtTicket
        {
            get { return dtTicket; }
            set { dtTicket = value; }
        }
        private string strFrom = "";

        public string StrFrom
        {
            get { return strFrom; }
            set { strFrom = value; }
        }
        private string strTo = "";

        public string StrTo
        {
            get { return strTo; }
            set { strTo = value; }
        }
        private string strType = "";

        public string StrType
        {
            get { return strType; }
            set { strType = value; }
        }
        private OPEntity nowOpEntity = new OPEntity();

        public OPEntity NowOpEntity
        {
            get { return nowOpEntity; }
            set { nowOpEntity = value; }
        }

        private int count = 0;

        public int Count
        {
            get { return count; }
            set { count = value; }
        }

        private string strRemark = "";

        public string StrRemark
        {
            get { return strRemark; }
            set { strRemark = value; }
        }

        private DateTime dtAdd = DateTime.Now;

        public DateTime DtAdd
        {
            get { return dtAdd; }
            set { dtAdd = value; }
        }
    }
}