﻿<Project Sdk="Microsoft.NET.Sdk.Web">
  <PropertyGroup>
    <TargetFramework>net6.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
	<TypeScriptToolsVersion>latest</TypeScriptToolsVersion>
	<TypeScriptCompileBlocked>true</TypeScriptCompileBlocked>
  </PropertyGroup>
  <ItemGroup>
    <PackageReference Include="System.Runtime" Version="4.3.1" />
    <PackageReference Include="System.Reflection.Emit" Version="4.7.0" />
    <PackageReference Include="System.Reflection.Emit.LightWeight" Version="4.7.0" />
    <PackageReference Include="Microsoft.CSharp" Version="4.7.0" />
    <PackageReference Include="System.Memory" Version="4.5.4" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\..\..\ServiceStack.OrmLite\src\ServiceStack.OrmLite.PostgreSQL\ServiceStack.OrmLite.PostgreSQL.csproj" />
    <ProjectReference Include="..\..\..\ServiceStack.OrmLite\src\ServiceStack.OrmLite.Sqlite\ServiceStack.OrmLite.Sqlite.csproj" />
    <ProjectReference Include="..\..\..\ServiceStack.OrmLite\src\ServiceStack.OrmLite\ServiceStack.OrmLite.csproj" />
    <ProjectReference Include="..\..\..\ServiceStack.Text\src\ServiceStack.Text\ServiceStack.Text.csproj" />
    <ProjectReference Include="..\..\src\ServiceStack.Api.OpenApi\ServiceStack.Api.OpenApi.csproj" />
    <ProjectReference Include="..\..\src\ServiceStack.Authentication.MongoDb\ServiceStack.Authentication.MongoDb.csproj" />
    <ProjectReference Include="..\..\src\ServiceStack.Client\ServiceStack.Client.csproj" />
    <ProjectReference Include="..\..\src\ServiceStack.Common\ServiceStack.Common.csproj" />
    <ProjectReference Include="..\..\src\ServiceStack.Interfaces\ServiceStack.Interfaces.csproj" />
    <ProjectReference Include="..\..\src\ServiceStack.Mvc\ServiceStack.Mvc.csproj" />
    <ProjectReference Include="..\..\src\ServiceStack.Server\ServiceStack.Server.csproj" />
    <ProjectReference Include="..\..\src\ServiceStack\ServiceStack.csproj" />
    <ProjectReference Include="..\..\src\ServiceStack.Extensions\ServiceStack.Extensions.csproj" />
  </ItemGroup>
  <ItemGroup>
    <Folder Include="wwwroot\assets\svg\app" />
    <Folder Include="wwwroot\css" />
    <Folder Include="wwwroot\modules\admin-ui\components" />
  </ItemGroup>
</Project>