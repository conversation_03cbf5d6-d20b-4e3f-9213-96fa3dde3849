<?xml version="1.0" encoding="UTF-8"?>
<svg width="180px" height="180px" viewBox="0 0 180 180" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <!-- Generator: Sketch 54 (76480) - https://sketchapp.com -->
    <title>icon_high_tolerance </title>
    <desc>Created with Sketch.</desc>
    <defs>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="98.3504012%" id="linearGradient-1">
            <stop stop-color="#004FFF" offset="0%"></stop>
            <stop stop-color="#FFFFFF" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="98.3504012%" id="linearGradient-2">
            <stop stop-color="#FFFFFF" offset="0%"></stop>
            <stop stop-color="#193FAD" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="25.153334%" x2="32.8737715%" y2="90.953555%" id="linearGradient-3">
            <stop stop-color="#2F60EE" offset="0%"></stop>
            <stop stop-color="#71C2FF" offset="100%"></stop>
        </linearGradient>
        <path d="M75.326087,43.9724353 C75.354305,43.1116368 75.8741428,42.5952461 76.5891981,42.7178748 C76.5891981,42.7178748 98.6831755,46.0396853 102.221282,47.5740572 C105.759389,49.1084291 127.898727,64.7097387 127.898727,64.7097387 C128.601356,65.1993285 129.105807,66.1128686 129.130435,66.9405064 L129.130435,92.8426899 C129.130435,102.352537 123.474349,112.308817 116.398135,114.98045 C116.398135,114.98045 105.930363,119.970425 102.343406,118.663326 C98.7564498,117.356227 88.1072365,104.351306 88.1072365,104.351306 C81.0449797,96.1744554 75.326087,81.3205847 75.326087,71.290796 L75.326087,43.9724353 Z" id="path-4"></path>
        <filter x="-15.8%" y="-5.9%" width="127.9%" height="123.7%" filterUnits="objectBoundingBox" id="filter-5">
            <feMorphology radius="1" operator="erode" in="SourceAlpha" result="shadowSpreadOuter1"></feMorphology>
            <feOffset dx="-1" dy="6" in="shadowSpreadOuter1" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="2.5" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feColorMatrix values="0 0 0 0 0.44409173   0 0 0 0 0.629283647   0 0 0 0 0.872848732  0 0 0 0.4 0" type="matrix" in="shadowBlurOuter1"></feColorMatrix>
        </filter>
        <path d="M54.7826087,56.1615006 C54.8108268,55.3007021 55.3306645,54.7843114 56.0457199,54.9069402 C56.0457199,54.9069402 78.1396972,58.2287507 81.6778041,59.7631225 C85.2159109,61.2974944 107.355249,76.898804 107.355249,76.898804 C108.057878,77.3883938 108.562328,78.301934 108.586957,79.1295717 L108.586957,105.031755 C108.586957,114.541602 102.93087,124.497882 95.8546566,127.169516 C95.8546566,127.169516 85.3868846,132.15949 81.7999281,130.852391 C78.2129716,129.545292 67.5637583,116.540372 67.5637583,116.540372 C60.5015014,108.363521 54.7826087,93.50965 54.7826087,83.4798613 L54.7826087,56.1615006 Z" id="path-6"></path>
        <filter x="-15.8%" y="-5.9%" width="127.9%" height="123.7%" filterUnits="objectBoundingBox" id="filter-7">
            <feMorphology radius="1" operator="erode" in="SourceAlpha" result="shadowSpreadOuter1"></feMorphology>
            <feOffset dx="-1" dy="6" in="shadowSpreadOuter1" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="2.5" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feColorMatrix values="0 0 0 0 0.44409173   0 0 0 0 0.629283647   0 0 0 0 0.872848732  0 0 0 0.4 0" type="matrix" in="shadowBlurOuter1"></feColorMatrix>
        </filter>
        <path d="M72.0850661,94.270051 C71.9502153,94.515023 71.9700615,94.8800715 72.1367007,95.2196961 C72.30334,95.5592457 72.5897077,95.8181575 72.8817534,95.8933908 L79.4649072,98.4290987 C79.7759903,98.5307281 80.0756925,98.7989074 80.2695734,99.1490521 C80.4634543,99.499109 80.5276373,99.8880542 80.4418974,100.193434 L78.3076139,111.066879 C77.7667051,113.936092 78.5969372,114.652892 80.1903119,112.719481 L93.3188815,97.0956762 C93.7381907,96.6504033 93.4656398,96.0188835 92.7779727,95.712032 L84.5762857,92.2539276 C84.2860714,92.1752078 84.0027856,91.9137492 83.8421069,91.5762446 C83.6814282,91.2386672 83.6702524,90.8816107 83.813143,90.6509923 L88.4255438,80.6981975 C88.5668257,80.4749322 88.5543763,80.1240889 88.3934162,79.7926858 C88.2324561,79.4612116 87.950812,79.2064149 87.6665941,79.135222 L80.6599382,76.3062097 C79.9212864,76.0723043 79.2713996,76.3216717 78.9617361,76.9580755 L72.0850661,94.270051 Z" id="path-8"></path>
        <filter x="-34.8%" y="-20.0%" width="169.7%" height="139.9%" filterUnits="objectBoundingBox" id="filter-9">
            <feOffset dx="0" dy="0" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="2.5" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feColorMatrix values="0 0 0 0 1   0 0 0 0 1   0 0 0 0 1  0 0 0 0.5 0" type="matrix" in="shadowBlurOuter1"></feColorMatrix>
        </filter>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="98.3504012%" id="linearGradient-10">
            <stop stop-color="#E7E9F0" offset="0%"></stop>
            <stop stop-color="#B3C3EF" offset="99.675359%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-11">
            <stop stop-color="#FFFFFF" stop-opacity="0" offset="0%"></stop>
            <stop stop-color="#FFFFFF" stop-opacity="0.6" offset="100%"></stop>
        </linearGradient>
    </defs>
    <g id="设计规范" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="图标" transform="translate(-751.000000, -518.000000)">
            <g id="icon_high_tolerance-" transform="translate(751.000000, 518.000000)">
                <rect id="矩形复制" fill="#FFFFFF" opacity="0.01" x="0" y="0" width="180" height="180"></rect>
                <g id="编组-7" transform="translate(0.000000, 3.000000)">
                    <polygon id="多边形" stroke="#004FFF" stroke-width="0.596153846" opacity="0.3" points="90 16.6028546 148.652222 51.76362 148.652222 122.085151 90 157.245916 31.3477778 122.085151 31.3477778 51.76362"></polygon>
                    <polygon id="路径-2复制" fill-opacity="0.2" fill="url(#linearGradient-1)" points="31.3043478 122.085151 89.95657 157.245916 148.608792 122.085151 90 86.9243855"></polygon>
                    <polygon id="路径-2复制-2" fill-opacity="0.1" fill="url(#linearGradient-2)" transform="translate(60.609056, 69.344183) rotate(-240.000000) translate(-60.609056, -69.344183) " points="0.270826369 70.3179602 60.0470535 103.531892 120.947286 68.3711265 61.2157369 35.156474"></polygon>
                    <g id="形状" opacity="0.3">
                        <use fill="black" fill-opacity="1" filter="url(#filter-5)" xlink:href="#path-4"></use>
                        <use fill="url(#linearGradient-3)" fill-rule="evenodd" xlink:href="#path-4"></use>
                    </g>
                    <g id="形状备份">
                        <use fill="black" fill-opacity="1" filter="url(#filter-7)" xlink:href="#path-6"></use>
                        <use fill="url(#linearGradient-3)" fill-rule="evenodd" xlink:href="#path-6"></use>
                    </g>
                    <g id="路径" fill-rule="nonzero">
                        <use fill="black" fill-opacity="1" filter="url(#filter-9)" xlink:href="#path-8"></use>
                        <use fill="#FFFFFF" xlink:href="#path-8"></use>
                    </g>
                    <polygon id="路径-2复制-3" fill-opacity="0.2" fill="url(#linearGradient-10)" transform="translate(119.304084, 69.344183) scale(-1, 1) rotate(-240.000000) translate(-119.304084, -69.344183) " points="58.9658543 70.3179602 118.742081 103.531892 179.642314 68.3711265 119.910765 35.156474"></polygon>
                    <path d="M31.3043478,51.76362 L89.95657,86.9243855 L148.608792,51.76362" id="路径-2" stroke="#004FFF" stroke-width="0.596153846" opacity="0.3" stroke-linejoin="round" stroke-dasharray="3.576923171679179,3.576923171679179"></path>
                    <path d="M31.3043478,51.76362 L89.95657,86.9243855 L148.608792,51.76362 L90,16.6028546 L31.3043478,51.76362 Z" id="路径-2复制-4" fill="url(#linearGradient-11)"></path>
                </g>
            </g>
        </g>
    </g>
</svg>