﻿using CommonLib;
using log4net;
using log4net.Config;
using System;
using System.IO;
using System.Web;

namespace Code.Client.Web
{
    public abstract class Global : HttpApplication
    {

        protected void Application_Start(object sender, EventArgs e)
        {
            lock (ConfigHelper.LockObj)
            {
                if (!ConfigHelper.IsOnInit)
                {
                    ConfigHelper.IsOnInit = true;
                    ConfigHelper.InitThread();
                    try
                    {
                        var file = new FileInfo(AppDomain.CurrentDomain.BaseDirectory + "Log4Net.config");
                        XmlConfigurator.Configure(file);
                    }
                    catch
                    {
                        // ignored
                    }

                    ConfigHelper.InitConfig();

                    RdsCacheHelper.InitLimiter();
                }
            }
        }

        protected void Application_BeginRequest(object sender, EventArgs e)
        {
            HttpContext.Current.Items.Add("ReqTime", ServerTime.LocalTime.Ticks);
        }

        protected void Application_EndRequest(object sender, EventArgs e)
        {
            //ApiCountCacheHelper.AddCount(OcrApiFlag);
            if (HttpContext.Current.Items["ReqTime"] == null)
                return;
            var ticks = BoxUtil.GetInt64FromObject(HttpContext.Current.Items["ReqTime"]);
            var request = new ApiRequestLog()
            {
                BeginTime = ticks,
                EndTime = ServerTime.LocalTime.Ticks
            };
            request.EsTime = new TimeSpan(request.EndTime - request.BeginTime).TotalMilliseconds;
            ApiRequestCacheHelper.Add(RequestFlagConst.OcrApiFlag, request);
        }

        protected void Application_Error(object sender, EventArgs e)
        {
            Exception lastError = Server.GetLastError();
            if (lastError == null)
                return;
            SetErrorLog(lastError.Message);
        }

        private void SetErrorLog(string message)
        {
            try
            {
                CodeProcessHelper.OcrErrorLogResult.Add(new ServerComponent
                {
                    from = "系统",
                    code = Guid.NewGuid().ToString().Replace("-", ""),
                    name = string.Format("【{0}】:{1}", "系统", message),
                    time = ServerTime.LocalTime,
                });
            }
            catch { }
        }

        protected void Application_End(object sender, EventArgs e)
        {
            //SetErrorLog("重启");
            ConfigHelper.IsExit = true;
            LogManager.GetLogger("IIS End").Info("【IIS挂了】 时间:" + ServerTime.LocalTime.ToString("yyyy-MM-dd HH:mm:ss"));
            var oe = Server.GetLastError();
            if (oe != null) //&& !oe.Message.Contains("超时"))
            {
                LogManager.GetLogger("IIS 错误").Error("【IIS挂了】 URL:" + Request.Url, Server.GetLastError());
                Server.ClearError();
            }
        }
    }
}