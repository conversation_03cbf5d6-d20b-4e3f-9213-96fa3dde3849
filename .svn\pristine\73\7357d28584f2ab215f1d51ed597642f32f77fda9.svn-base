// Code generated by Microsoft (R) AutoRest Code Generator *******
// Changes may cause incorrect behavior and will be lost if the code is
// regenerated.

namespace AutorestClient.Models
{
    using Newtonsoft.Json;
    using System.Linq;

    /// <summary>
    /// AllowedAttributes
    /// </summary>
    /// <remarks>
    /// AllowedAttributes Description
    /// </remarks>
    public partial class AllowedAttributes
    {
        /// <summary>
        /// Initializes a new instance of the AllowedAttributes class.
        /// </summary>
        public AllowedAttributes()
        {
          CustomInit();
        }

        /// <summary>
        /// Initializes a new instance of the AllowedAttributes class.
        /// </summary>
        /// <param name="aliased">Range Description</param>
        public AllowedAttributes(int aliased = default(int))
        {
            Aliased = aliased;
            CustomInit();
        }

        /// <summary>
        /// An initialization method that performs custom operations like setting defaults
        /// </summary>
        partial void CustomInit();

        /// <summary>
        /// Gets or sets range Description
        /// </summary>
        [JsonProperty(PropertyName = "Aliased")]
        public int Aliased { get; set; }

    }
}
