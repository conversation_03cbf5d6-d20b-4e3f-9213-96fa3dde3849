﻿using System;
using System.Collections.Generic;
using System.ServiceProcess;
using System.Threading;
using System.Configuration;
using ToolCommon;
using System.Threading.Tasks;

namespace AutoProxy
{
    public partial class Service : ServiceBase
    {
        public Service()
        {
            InitializeComponent();
        }

        protected override void OnStart(string[] args)
        {
            CheckIP();
        }

        public void CheckIP()
        {
            ThreadPool.QueueUserWorkItem((object obj) =>
            {
                while (true)
                {
                    try
                    {
                        var ip = IPHelper.GetNowIP(5);
                        //Log.WriteLog("当前IP：" + ip);
                        if (!string.IsNullOrEmpty(ip))
                        {
                            ip = WebClientExt.GetHtml(string.Format("http://**************:9001/code.ashx?op=proxy&ip={0}", ip), (double)5);
                            //Log.WriteLog("设置IP结果：" + ip);
                        }
                    }
                    catch { }
                    System.Threading.Thread.Sleep(3000);
                }
            });
        }

        protected override void OnStop()
        {
        }




    }
}
