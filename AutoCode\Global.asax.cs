﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Security;
using System.Web.SessionState;
using System.Net;
using System.Threading;
using System.Runtime.InteropServices;
using System.IO;

namespace AutoCode
{
    public class Global : System.Web.HttpApplication
    {

        protected void Application_Start(object sender, EventArgs e)
        {
            try
            {
                FileInfo file = new FileInfo(AppDomain.CurrentDomain.BaseDirectory + "Log4Net.config");
                log4net.Config.XmlConfigurator.Configure(file);

                //ServicePointManager.DefaultConnectionLimit = int.MaxValue;
                //ServicePointManager.MaxServicePoints = int.MaxValue;
                ////ServicePointManager.MaxServicePointIdleTime = 1;
                //ThreadPool.SetMinThreads(50, 50);
                //ThreadPool.SetMaxThreads(100, 500);
            }
            catch { }
            try
            {
                NewTicket.JiSu.JiSuDaMa.LstChaoRenKey = System.Configuration.ConfigurationManager.AppSettings["JiSuDaMaKey"].ToString().Split(new string[] { "|" }, StringSplitOptions.RemoveEmptyEntries).ToList();
                NewTicket.JiSu.JiSuDaMa.StrChaoRenKey = NewTicket.JiSu.JiSuDaMa.LstChaoRenKey.Count > 0 ? NewTicket.JiSu.JiSuDaMa.LstChaoRenKey[0] : "00040007RPTX7530DEEA0057ZSCT003c";
                NewTicket.JiSu.JiSuDaMa.IsJiaSu = System.Configuration.ConfigurationManager.AppSettings["IsJiaSu"].ToString().Equals("True");
                NewTicket.JiSu.JiSuDaMa.GetCode(new byte[1000]);
            }
            catch (Exception oe)
            {
                Console.WriteLine(oe.Message);
            }
            new System.Threading.Thread(delegate(object obj)
            {
                while (true)
                {
                    //1分钟清理3次 16,32,48
                    if (DateTime.Now.Second > 0 && DateTime.Now.Second % 16 == 0)
                    {
                        //NewTicket.JiSu.JiSuDaMa.GetChaoRenKey();
                        ClearMemory();
                    }
                    System.Threading.Thread.Sleep(1000);
                }
            }) { Priority = ThreadPriority.Highest, IsBackground = true }.Start();
            ImageComparator.Init();
        }

        /// <summary>
        /// 释放内存
        /// </summary>
        public void ClearMemory(bool isForce = false)
        {
            try
            {
                if (isForce || DateTime.Now.Second % 14 == 0)
                {
                    GC.Collect();
                    GC.WaitForPendingFinalizers();
                    if (Environment.OSVersion.Platform == PlatformID.Win32NT)
                    {
                        SetProcessWorkingSetSize(System.Diagnostics.Process.GetCurrentProcess().Handle, -1, -1);
                    }
                }
            }
            catch { }
        }

        [DllImport("kernel32.dll", EntryPoint = "SetProcessWorkingSetSize")]
        private static extern int SetProcessWorkingSetSize(IntPtr process, int minSize, int maxSize);

        protected void Session_Start(object sender, EventArgs e)
        {

        }

        protected void Application_BeginRequest(object sender, EventArgs e)
        {

        }

        protected void Application_AuthenticateRequest(object sender, EventArgs e)
        {

        }

        protected void Application_Error(object sender, EventArgs e)
        {

        }

        protected void Session_End(object sender, EventArgs e)
        {

        }

        protected void Application_End(object sender, EventArgs e)
        {

        }
    }
}