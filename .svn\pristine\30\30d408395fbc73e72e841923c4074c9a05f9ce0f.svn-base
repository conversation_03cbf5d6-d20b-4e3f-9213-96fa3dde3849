﻿using CommonLib;
using System;
using System.Linq;
using Account.Web.Common;

namespace Account.Web
{
    public partial class Mail : System.Web.UI.Page
    {
        protected void Page_Load(object sender, EventArgs e)
        {
            if (!IsPostBack)
            {
                string strOp = BoxUtil.GetStringFromObject(Request.QueryString["op"]);
                if (!string.IsNullOrEmpty(strOp))
                {
                    DoOperate(strOp);
                }
            }
            Response.End();
        }

        private void DoOperate(string strOP)
        {
            try
            {
                string strEncrypt = "";
                //OPEntity opp = new OPEntity();
                var notice = new NoticeQueueEntity();
                if (CommonHelper.IsEncrypt)
                {
                    strEncrypt = BoxUtil.GetStringFromObject(Request.QueryString["con"]).Replace(" ", "+");
                    strEncrypt = CommonEncryptHelper.DESDecrypt(strEncrypt, CommonHelper.StrEncrypt);
                    if (!string.IsNullOrEmpty(strEncrypt))
                    {
                        //opp.StrApp = CommonHelper.SubString(strEncrypt, "app=", "&");
                        notice.MacCode = CommonHelper.SubString(strEncrypt, "app=", "&");
                    }
                }
                else
                {
                    //opp.StrApp = BoxUtil.GetStringFromObject(Request.QueryString["app"]);
                    notice.MacCode = BoxUtil.GetStringFromObject(Request.QueryString["app"]);
                }
                var uid = Request.Headers["uid"];
                if (string.IsNullOrEmpty(uid))
                {
                    LogHelper.Log.Info(string.Format("空Header请求！Url:{0} Body:{1}    Headers:{2}"
                        , Request.Url.PathAndQuery
                        , string.Join("|", Request.Form.AllKeys.Select(p => string.Format("{0}->{1}", p, Request.Form[p])))
                        , string.Join("|", Request.Headers.AllKeys.Select(p => string.Format("{0}->{1}", p, Request.Headers[p]))))
                        );
                }
                switch (strOP)
                {
                    case "forgetpwd":
                        if (CommonHelper.IsEncrypt)
                        {
                            notice.To = CommonHelper.SubString(strEncrypt, "email=", "&");
                            notice.MobileNo = CommonHelper.SubString(strEncrypt, "mobile=", "&");
                        }
                        else
                        {
                            notice.To = BoxUtil.GetStringFromObject(Request.QueryString["email"]);
                            notice.MobileNo = BoxUtil.GetStringFromObject(Request.QueryString["mobile"]);
                        }
                        var isEmail = BoxUtil.IsEmail(notice.To);
                        var isMobile = BoxUtil.IsMobile(notice.MobileNo);
                        if (isMobile || isEmail)
                        {
                            if ((isMobile && !CodeHelper.IsExitsCode(notice.MobileNo))
                                || (isEmail && !CodeHelper.IsExitsCode(notice.To)))
                            {
                                Response.Write("当前账号不存在，请先注册账号！");
                                return;
                            }
                            notice.Subject = "密码找回服务";
                            var validateCode = CommonValidateCode.GetValidateCode(isMobile ? notice.MobileNo : notice.To, "OCRREGForgetPwd", true);
                            if (isEmail)
                            {
                                notice.NoticeType = NoticeType.邮件;
                                notice.Body = string.Format("您的验证码为：" + validateCode + ",有效期1小时!");
                            }
                            else if (isMobile)
                            {
                                notice.NoticeType = NoticeType.短信;
                                notice.Body = validateCode;
                            }
                        }
                        if (!string.IsNullOrEmpty(notice.Body))
                        {
                            //SendMail.Send(notice.To, notice.Body, notice.Subject);
                            Response.Write("True");
                        }
                        else
                        {
                            Response.Write("邮箱/手机号格式错误！");
                        }
                        break;
                    case "regaccount":
                        //mail.aspx?email=<EMAIL>&op=regaccount
                        if (CommonHelper.IsEncrypt)
                        {
                            notice.To = CommonHelper.SubString(strEncrypt, "email=", "&");
                            notice.MobileNo = CommonHelper.SubString(strEncrypt, "mobile=", "&");
                        }
                        else
                        {
                            notice.To = BoxUtil.GetStringFromObject(Request.QueryString["email"]);
                            notice.MobileNo = BoxUtil.GetStringFromObject(Request.QueryString["mobile"]);
                        }
                        if (string.IsNullOrEmpty(uid) || CodeHelper.IsCanReg(uid, "", 3))
                        {
                            isMobile = BoxUtil.IsMobile(notice.MobileNo);
                            isEmail = BoxUtil.IsEmail(notice.To);
                            if (isMobile || isEmail)
                            {
                                var account = isMobile ? notice.MobileNo : notice.To;
                                if (CodeHelper.IsExitsCode(account))
                                {
                                    Response.Write("用户已注册，请返回登录！");
                                    return;
                                }
                                notice.Subject = "欢迎注册OCR助手";
                                var validateCode = CommonValidateCode.GetValidateCode(account, "OCRREG", true);
                                if (isEmail)
                                {
                                    notice.NoticeType = NoticeType.邮件;
                                    notice.Body = string.Format("您的验证码为：" + validateCode + ",有效期1小时!");
                                }
                                else if (isMobile)
                                {
                                    notice.NoticeType = NoticeType.短信;
                                    notice.Body = validateCode;
                                }
                            }
                            if (!string.IsNullOrEmpty(notice.Body))
                            {
                                //SendMail.Send(notice.To, notice.Body, notice.Subject);
                                Response.Write("True");
                            }
                            else
                            {
                                Response.Write("邮箱/手机号格式错误！");
                            }
                        }
                        else
                        {
                            CommonHelper._Log.Debug("注册次数超限！token：" + uid + "，账号：" + notice.To + notice.MobileNo);
                            Response.Write("True");
                        }
                        break;
                }
                if (notice != null && notice.NoticeType.GetHashCode() > 0)
                {
                    CommonHelper._Log.Debug(notice.ToString());
                    try
                    {
                        RdsCacheHelper.NoticeQueue.EnqueueCacheMessage(notice);
                    }
                    catch (Exception oe)
                    {
                        CommonHelper._Log.Error("AddNoticeInfo出错:" + notice.ToString(), oe);
                    }
                }
            }
            catch (Exception oe)
            {
                Response.Write("服务器维护中，请稍后重试！");
                CommonHelper._Log.Error(oe);
            }
        }
    }
}