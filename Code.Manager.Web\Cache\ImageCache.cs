﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using Enterprise.Framework.Redis;

namespace BaiDuAPI
{
    public class ImageCache : RedisCacheObject<List<ImageEntity>>
    {
        protected override string CurrentObject_KeyPrefix
        {
            get { return "ImageCache"; }
        }

        protected override string Object_DBName
        {
            get { return "OPS_Cache"; }
        }

        public void Add(string strName, List<ImageEntity> lstMsg)
        {
            if (this.KeyExists(strName))
                this.Remove(strName);
            this.Insert(strName, lstMsg);
        }
    }
}