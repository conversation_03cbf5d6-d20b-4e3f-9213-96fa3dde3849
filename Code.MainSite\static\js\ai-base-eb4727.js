iu.widget("jsonview",{config:{indent:2,quote:true,toggle:true},init:function(){var $element=this.$element;iu.jsonview.insert($element.html(),$element,this.config)}});iu.jsonview.build=function(json,config){if(typeof json!="string"){json=JSON.stringify(json)||""}if(!config)config=0;var i=-1,j,length=json.length,level=0,htmlify=iu.jsonview.htmlify,childCount,childStack=[],chr,key="",val="",missComma,error=iu.jsonview.error=[],date=new Date,html=[],indent=config.indent>-1?config.indent:4,noquote=config.quote==false,toggle=config.toggle==false?"":'<b class="toggle"><i></i></b>';if(nextS()){i++;push('<div class="iu-jsonview'+(toggle?"":" iu-jsonview-toggle-off")+'">')}for(;i<length;i++){chr=json[i];if(chr=="{"){if(nextS()){chr=json[i+1];if(chr=="}"){i++;nextS();val="{}";pushKevVal()}else{pushBlockBegin("{")}}continue}if(chr=="["){if(nextS()){chr=json[i+1];if(chr=="]"){i++;nextS();val="[]";pushKevVal()}else{pushBlockBegin("[")}}continue}if(chr=="}"||chr=="]"){nextS();pushBlockEnd(chr);continue}if(chr=='"'){j=i;if(val){if(key){pushError("illegal string:"+val);pushKevVal();missComma=true}else{val+=chr;continue}}do{chr=json[++i];if(chr=="\\"){++i;continue}if(chr=='"'){val+=json.substr(j,i-j+1);nextS();break}}while(i<length);continue}if(chr==":"){nextS();if(key)pushError("illegal string:"+key);key=val;val="";if(key.indexOf('"')>-1&&!/^".*"$/.test(key))pushError("illegal key:"+key);continue}if(chr==","){nextS();pushKevVal();continue}val+=chr}if(html.length){push("</div>")}if(level>0)pushError("illegal json");if(!error.length)iu.jsonview.error="";if(html.length<3)html=[];return html.join("");function getIndent(){if(!level)return"";for(var i=0,html="",length=level*indent;i<length;i++)html+=" ";return'<i class="indent">'+html+"</i>"}function nextS(){do{var chr=json[++i];if(/\S/.test(chr)){i--;return 1}}while(i<length);return 0}function push(str){html.push(str)}function pushBlockBegin(border){if(childCount){pushComma()}var item,cls=border=="["?"array":"object",dataKey="";item='<span class="prop">';item+=getIndent();item+=toggle;if(key){dataKey=' data-key="'+key.replace(/\W/g,"")+'"';if(noquote)key=key.replace(/^"|"$/g,"");item+='<b class="key"'+dataKey+">"+htmlify(key)+'</b><b class="colon">: </b>'}item+='<b class="border '+cls+'">'+border+"</b>";item+='<i class="more"></i>';item+='<b class="block"'+dataKey+' style="padding-left:'+indent*.5+'em">';key="";setChildCount();push(item);level++;childCount=0;childStack.push(0)}function pushBlockEnd(border){var count,item,cls=border=="]"?"array":"object";pushKevVal();count=childStack.pop();level--;item="</b>";item+=getIndent();item+='<b class="border '+cls+'">'+border+"</b>";item+='<i class="count">// '+count+" item"+(count>1?"s":"")+"</i>";item+="</span>";push(item)}function pushComma(){var i,l=html.length-1,comma='<b class="comma">,</b>';i=html[l].lastIndexOf('<i class="count">');if(i>0){html[l]=html[l].substr(0,i)+comma+html[l].substr(i)}else{html[l]=html[l].substr(0,html[l].length-4)+comma+"</b>"}}function pushError(message){error.push(message)}function pushKevVal(){val=htmlify(val);if(!key&&!val)return;if(childCount&&!missComma){pushComma()}missComma=0;var item,type,title="";item='<b class="prop">';item+=getIndent();if(key){if(noquote)key=key.replace(/^"|"$/g,"");item+='<b class="key">'+htmlify(key)+'</b><b class="colon">: </b>'}if(val){type=getType(val);if(type=="number"){if(/^[1-9]\d{9}$/.test(val)){date.setTime(val*1e3);title=' title="'+date.toLocaleString()+'"'}}else if(type=="string"){if(/^"[1-9]\d{9}"$/.test(val)){date.setTime(val.slice(1,-1)*1e3);title=' title="'+date.toLocaleString()+'"'}}item+='<b class="val '+type+'"'+title+">"+val+"</b>"}item+="</b>";push(item);key="";val="";setChildCount()}function getType(str){if(str[0]=='"')return"string";if(str[0]=="[")return"array";if(str[0]=="{")return"object";if(str=="null")return"null";if(str=="true"||str=="false")return"boolean";if(/^[\d.+-]+$/.test(val))return"number";pushError("illegal unknown:"+val);return"unknown"}function setChildCount(){var length=childStack.length;if(length){childCount=1;childStack[length-1]++}}};iu.jsonview.htmlify=function(s){return s.replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/\s+$/,"")};iu.jsonview.insert=function(json,selector,config){var $element=iu.$(selector),toggle=iu.jsonview.toggle;$element.html(iu.jsonview.build(json,config));$element.off("click",toggle).on("click",".more,.toggle",toggle)};iu.jsonview.toggle=function(){$(this).parent().toggleClass("closed")};iu.widget("zoom",{config:{alt:false,handle:null,scaleMax:10,scaleMin:1,scaleStep:.1,origin:"mouse",outside:"center",change:null},init:function(){var me=this;me.style={rotate:0,scale:1,translateX:0,translateY:0};me.$handle=iu.$(me.config.handle||me.element);me.disabled=1;me.enable();iu.lib.patch(me,"change")},destroy:function(){this.reset();this.disable()},disable:function(){var me=this;me.disabled=1;me.$handle.off("mousewheel DOMMouseScroll",me.events.mousewheel)},enable:function(){var me=this;if(me.disabled){me.disabled=0;me.$handle.on("mousewheel DOMMouseScroll",me.events.mousewheel)}},reset:function(){var me=this,style=me.style;style.rotate=0;style.scale=1;style.translateX=0;style.translateY=0;me.$element.css("transform","");me.__change(style)},transform:function(){var me=this,style=me.style;me.$element.css("transform","rotate("+style.rotate+"deg)"+" translate("+style.translateX+"px,"+style.translateY+"px)"+" scale("+style.scale+")");me.__change(style)},rotate:function(degree){var me=this,style=me.style;if(degree==null){degree=(style.rotate+90)%360}if(isFinite(degree)){style.rotate=degree;me.transform()}},scale:function(step,x,y){var me=this,style=me.style,config=me.config,element=me.element,scale,diff;scale=style.scale+(parseFloat(step||.1)||0);scale<config.scaleMin?scale=config.scaleMin:scale>config.scaleMax?scale=config.scaleMax:0;if(scale==style.scale){return}if(scale==1){style.translateX=0;style.translateY=0}else{if(config.origin=="mouse"){diff=scale-style.scale;if(isFinite(x))style.translateX=style.translateX-(x-element.offsetWidth/2)*diff;if(isFinite(y))style.translateY=style.translateY-(y-element.offsetHeight/2)*diff}else if(config.origin=="center"){style.translateX=0;style.translateY=0}else{style.translateX=config.origin.x==null?0:config.origin.x;style.translateY=config.origin.y==null?0:config.origin.y}}style.scale=scale;me.transform()},translate:function(x,y){var me=this;if(isFinite(x))me.style.translateX=x;if(isFinite(y))me.style.translateY=y;me.transform()},events:{mousewheel:function(e){var me=this,config=me.config;e=e.originalEvent||e;if(e.shiftKey||!config.alt&&e.altKey||config.alt&&!e.altKey){return}var element=me.element,target=e.target,parent=target,offset;while(parent){if(parent==element){offset=iu.lib.offset(e,element);break}parent=parent.parentNode}if(!offset){offset=config.outside=="center"?{x:element.offsetWidth/2,y:element.offsetHeight/2}:iu.zoom.getEventOffset(e,element)}e.preventDefault();me.scale(config.scaleStep*((e.wheelDelta||-e.detail)>0?1:-1),offset.x,offset.y)}}});iu.zoom.getEventOffset=function(e,element){var axis={x1:element.offsetLeft,y1:element.offsetTop},offset={x:e.offsetX,y:e.offsetY};if(offset.x<axis.x1){offset.x=element.offsetWidth}else if(offset.x>axis.x1+element.offsetWidth){offset.x=0}else{offset.x-=axis.x1}if(offset.y<axis.y1){offset.y=element.offsetHeight}else if(offset.y>axis.y1+element.offsetHeight){offset.y=0}else{offset.y-=axis.y1}return offset};iu.zoom.isPageResized=function(){var fn;if(/macintosh|mac os x/i.test(navigator.userAgent)){fn=function(){if(devicePixelRatio<1||innerWidth>screen.availWidth){return false}if(parseInt(devicePixelRatio*10)!=devicePixelRatio*10){return true}return Math.abs(outerWidth-innerWidth)/Math.min(outerWidth,innerWidth)>.05}}else{fn=function(){if(window.devicePixelRatio){return devicePixelRatio!=1}if(screen.deviceXDPI||screen.logicalXDPI){return screen.deviceXDPI/screen.logicalXDPI!=1}}}iu.zoom.isPageResized=fn;return fn()};!function(n,r){function e(n){return!!(""===n||n&&n.charCodeAt&&n.substr)}function t(n){return p?p(n):"[object Array]"===l.call(n)}function o(n){return n&&"[object Object]"===l.call(n)}function a(n,r){var e
n=n||{},r=r||{}
for(e in r)r.hasOwnProperty(e)&&null==n[e]&&(n[e]=r[e])
return n}function i(n,r,e){var t,o,a=[]
if(!n)return a
if(f&&n.map===f)return n.map(r,e)
for(t=0,o=n.length;o>t;t++)a[t]=r.call(e,n[t],t,n)
return a}function u(n,r){return n=Math.round(Math.abs(n)),isNaN(n)?r:n}function c(n){var r=s.settings.currency.format
return"function"==typeof n&&(n=n()),e(n)&&n.match("%v")?{pos:n,neg:n.replace("-","").replace("%v","-%v"),zero:n}:n&&n.pos&&n.pos.match("%v")?n:e(r)?s.settings.currency.format={pos:r,neg:r.replace("%v","-%v"),zero:r}:r}var s={}
s.version="0.4.2",s.settings={currency:{symbol:"$",format:"%s%v",decimal:".",thousand:",",precision:2,grouping:3},number:{precision:0,grouping:3,thousand:",",decimal:"."}}
var f=Array.prototype.map,p=Array.isArray,l=Object.prototype.toString,m=s.unformat=s.parse=function(n,r){if(t(n))return i(n,function(n){return m(n,r)})
if(n=n||0,"number"==typeof n)return n
r=r||s.settings.number.decimal
var e=RegExp("[^0-9-"+r+"]",["g"]),o=parseFloat((""+n).replace(/\((?=\d+)(.*)\)/,"-$1").replace(e,"").replace(r,"."))
return isNaN(o)?0:o},d=s.toFixed=function(n,r){r=u(r,s.settings.number.precision)
var e=+(s.unformat(n)+"e"+r),t=Math.round(e),o=(+(t+"e-"+r)).toFixed(r)
return o},g=s.formatNumber=s.format=function(n,r,e,c){if(t(n))return i(n,function(n){return g(n,r,e,c)})
n=m(n)
var f=a(o(r)?r:{precision:r,thousand:e,decimal:c},s.settings.number),p=u(f.precision),l=0>n?"-":"",h=parseInt(d(Math.abs(n||0),p),10)+"",y=h.length>3?h.length%3:0
return l+(y?h.substr(0,y)+f.thousand:"")+h.substr(y).replace(/(\d{3})(?=\d)/g,"$1"+f.thousand)+(p?f.decimal+d(Math.abs(n),p).split(".")[1]:"")},h=s.formatMoney=function(n,r,e,f,p,l){if(t(n))return i(n,function(n){return h(n,r,e,f,p,l)})
n=m(n)
var d=a(o(r)?r:{symbol:r,precision:e,thousand:f,decimal:p,format:l},s.settings.currency),y=c(d.format),b=n>0?y.pos:0>n?y.neg:y.zero
return b.replace("%s",d.symbol).replace("%v",g(Math.abs(n),u(d.precision),d.thousand,d.decimal))}
s.formatColumn=function(n,r,f,p,l,d){if(!n||!t(n))return[]
var h=a(o(r)?r:{symbol:r,precision:f,thousand:p,decimal:l,format:d},s.settings.currency),y=c(h.format),b=y.pos.indexOf("%s")<y.pos.indexOf("%v")?!0:!1,v=0,x=i(n,function(n,r){if(t(n))return s.formatColumn(n,h)
n=m(n)
var e=n>0?y.pos:0>n?y.neg:y.zero,o=e.replace("%s",h.symbol).replace("%v",g(Math.abs(n),u(h.precision),h.thousand,h.decimal))
return o.length>v&&(v=o.length),o})
return i(x,function(n,r){return e(n)&&n.length<v?b?n.replace(h.symbol,h.symbol+Array(v-n.length+1).join(" ")):Array(v-n.length+1).join(" ")+n:n})},"undefined"!=typeof exports?("undefined"!=typeof module&&module.exports&&(exports=module.exports=s),exports.accounting=s):"function"==typeof define&&define.amd?define([],function(){return s}):(s.noConflict=function(e){return function(){return n.accounting=e,s.noConflict=r,s}}(n.accounting),n.accounting=s),n.accounting=s}(window)
;/**
 * Project: Bootstrap Hover Dropdown
 * Author: Cameron Spear
 * Contributors: Mattia Larentis
 *
 * Dependencies: Bootstrap's Dropdown plugin, jQuery
 *
 * A simple plugin to enable Bootstrap dropdowns to active on hover and provide a nice user experience.
 *
 * License: MIT
 *
 * http://cameronspear.com/blog/bootstrap-dropdown-on-hover-plugin/
 */(function(e,t,n){var r=e();e.fn.dropdownHover=function(n){if("ontouchstart"in document)return this;r=r.add(this.parent());return this.each(function(){function h(e){r.find(":focus").blur();l.instantlyCloseOthers===!0&&r.removeClass("open");t.clearTimeout(c);s.addClass("open");i.trigger(a)}var i=e(this),s=i.parent(),o={delay:100,instantlyCloseOthers:!0},u={delay:e(this).data("delay"),instantlyCloseOthers:e(this).data("close-others")},a="show.bs.dropdown",f="hide.bs.dropdown",l=e.extend(!0,{},o,n,u),c;s.hover(function(e){if(!s.hasClass("open")&&!i.is(e.target))return!0;h(e)},function(){c=t.setTimeout(function(){s.removeClass("open");i.trigger(f)},l.delay)});i.hover(function(e){if(!s.hasClass("open")&&!s.is(e.target))return!0;h(e)});s.find(".dropdown-submenu").each(function(){var n=e(this),r;n.hover(function(){t.clearTimeout(r);n.children(".dropdown-menu").show();n.siblings().children(".dropdown-menu").hide()},function(){var e=n.children(".dropdown-menu");r=t.setTimeout(function(){e.hide()},l.delay)})})})};e(document).ready(function(){e('[data-hover="dropdown"]').dropdownHover()})})(jQuery,this);!function(e,t){if("object"==typeof exports&&"object"==typeof module)module.exports=t();else if("function"==typeof define&&define.amd)define([],t);else{var n=t();for(var r in n)("object"==typeof exports?exports:e)[r]=n[r]}}(this,function(){return function(e){function t(r){if(n[r])return n[r].exports;var i=n[r]={exports:{},id:r,loaded:!1};return e[r].call(i.exports,i,i.exports,t),i.loaded=!0,i.exports}var n={};return t.m=e,t.c=n,t.p="",t(0)}([function(e,t,n){n(6),n(7),e.exports=n(8)},function(e,t,n){(function(t){!function(n){function r(e,t){return function(){e.apply(t,arguments)}}function i(e){if("object"!=typeof this)throw new TypeError("Promises must be constructed via new");if("function"!=typeof e)throw new TypeError("not a function");this._state=null,this._value=null,this._deferreds=[],l(e,r(a,this),r(s,this))}function o(e){var t=this;return null===this._state?void this._deferreds.push(e):void f(function(){var n=t._state?e.onFulfilled:e.onRejected;if(null===n)return void(t._state?e.resolve:e.reject)(t._value);var r;try{r=n(t._value)}catch(i){return void e.reject(i)}e.resolve(r)})}function a(e){try{if(e===this)throw new TypeError("A promise cannot be resolved with itself.");if(e&&("object"==typeof e||"function"==typeof e)){var t=e.then;if("function"==typeof t)return void l(r(t,e),r(a,this),r(s,this))}this._state=!0,this._value=e,u.call(this)}catch(n){s.call(this,n)}}function s(e){this._state=!1,this._value=e,u.call(this)}function u(){for(var e=0,t=this._deferreds.length;t>e;e++)o.call(this,this._deferreds[e]);this._deferreds=null}function c(e,t,n,r){this.onFulfilled="function"==typeof e?e:null,this.onRejected="function"==typeof t?t:null,this.resolve=n,this.reject=r}function l(e,t,n){var r=!1;try{e(function(e){r||(r=!0,t(e))},function(e){r||(r=!0,n(e))})}catch(i){if(r)return;r=!0,n(i)}}var f="function"==typeof t&&t||function(e){setTimeout(e,1)},d=Array.isArray||function(e){return"[object Array]"===Object.prototype.toString.call(e)};i.prototype["catch"]=function(e){return this.then(null,e)},i.prototype.then=function(e,t){var n=this;return new i(function(r,i){o.call(n,new c(e,t,r,i))})},i.all=function(){var e=Array.prototype.slice.call(1===arguments.length&&d(arguments[0])?arguments[0]:arguments);return new i(function(t,n){function r(o,a){try{if(a&&("object"==typeof a||"function"==typeof a)){var s=a.then;if("function"==typeof s)return void s.call(a,function(e){r(o,e)},n)}e[o]=a,0===--i&&t(e)}catch(u){n(u)}}if(0===e.length)return t([]);for(var i=e.length,o=0;o<e.length;o++)r(o,e[o])})},i.resolve=function(e){return e&&"object"==typeof e&&e.constructor===i?e:new i(function(t){t(e)})},i.reject=function(e){return new i(function(t,n){n(e)})},i.race=function(e){return new i(function(t,n){for(var r=0,i=e.length;i>r;r++)e[r].then(t,n)})},i._setImmediateFn=function(e){f=e},i.prototype.always=function(e){var t=this.constructor;return this.then(function(n){return t.resolve(e()).then(function(){return n})},function(n){return t.resolve(e()).then(function(){throw n})})},"undefined"!=typeof e&&e.exports?e.exports=i:n.Promise||(n.Promise=i)}(this)}).call(t,n(2).setImmediate)},function(e,t,n){(function(e,r){function i(e,t){this._id=e,this._clearFn=t}var o=n(3).nextTick,a=Function.prototype.apply,s=Array.prototype.slice,u={},c=0;t.setTimeout=function(){return new i(a.call(setTimeout,window,arguments),clearTimeout)},t.setInterval=function(){return new i(a.call(setInterval,window,arguments),clearInterval)},t.clearTimeout=t.clearInterval=function(e){e.close()},i.prototype.unref=i.prototype.ref=function(){},i.prototype.close=function(){this._clearFn.call(window,this._id)},t.enroll=function(e,t){clearTimeout(e._idleTimeoutId),e._idleTimeout=t},t.unenroll=function(e){clearTimeout(e._idleTimeoutId),e._idleTimeout=-1},t._unrefActive=t.active=function(e){clearTimeout(e._idleTimeoutId);var t=e._idleTimeout;t>=0&&(e._idleTimeoutId=setTimeout(function(){e._onTimeout&&e._onTimeout()},t))},t.setImmediate="function"==typeof e?e:function(e){var n=c++,r=arguments.length<2?!1:s.call(arguments,1);return u[n]=!0,o(function(){u[n]&&(r?e.apply(null,r):e.call(null),t.clearImmediate(n))}),n},t.clearImmediate="function"==typeof r?r:function(e){delete u[e]}}).call(t,n(2).setImmediate,n(2).clearImmediate)},function(e,t){function n(){c=!1,a.length?u=a.concat(u):l=-1,u.length&&r()}function r(){if(!c){var e=setTimeout(n);c=!0;for(var t=u.length;t;){for(a=u,u=[];++l<t;)a&&a[l].run();l=-1,t=u.length}a=null,c=!1,clearTimeout(e)}}function i(e,t){this.fun=e,this.array=t}function o(){}var a,s=e.exports={},u=[],c=!1,l=-1;s.nextTick=function(e){var t=new Array(arguments.length-1);if(arguments.length>1)for(var n=1;n<arguments.length;n++)t[n-1]=arguments[n];u.push(new i(e,t)),1!==u.length||c||setTimeout(r,0)},i.prototype.run=function(){this.fun.apply(null,this.array)},s.title="browser",s.browser=!0,s.env={},s.argv=[],s.version="",s.versions={},s.on=o,s.addListener=o,s.once=o,s.off=o,s.removeListener=o,s.removeAllListeners=o,s.emit=o,s.binding=function(e){throw new Error("process.binding is not supported")},s.cwd=function(){return"/"},s.chdir=function(e){throw new Error("process.chdir is not supported")},s.umask=function(){return 0}},function(e,t){function n(){var e=~navigator.userAgent.indexOf("Android")&&~navigator.vendor.indexOf("Google")&&!~navigator.userAgent.indexOf("Chrome");return e&&navigator.userAgent.match(/AppleWebKit\/(\d+)/).pop()<=534||/MQQBrowser/g.test(navigator.userAgent)}var r=function(){try{return new Blob,!0}catch(e){return!1}}()?window.Blob:function(e,t){var n=new(window.BlobBuilder||window.WebKitBlobBuilder||window.MSBlobBuilder||window.MozBlobBuilder);return e.forEach(function(e){n.append(e)}),n.getBlob(t?t.type:void 0)},i=function(){function e(){var e=this,n=[],i=Array(21).join("-")+(+new Date*(1e16*Math.random())).toString(36),o=XMLHttpRequest.prototype.send;this.getParts=function(){return n.toString()},this.append=function(e,t,r){n.push("--"+i+'\r\nContent-Disposition: form-data; name="'+e+'"'),t instanceof Blob?(n.push('; filename="'+(r||"blob")+'"\r\nContent-Type: '+t.type+"\r\n\r\n"),n.push(t)):n.push("\r\n\r\n"+t),n.push("\r\n")},t++,XMLHttpRequest.prototype.send=function(a){var s,u,c=this;a===e?(n.push("--"+i+"--\r\n"),u=new r(n),s=new FileReader,s.onload=function(){o.call(c,s.result)},s.onerror=function(e){throw e},s.readAsArrayBuffer(u),this.setRequestHeader("Content-Type","multipart/form-data; boundary="+i),t--,0==t&&(XMLHttpRequest.prototype.send=o)):o.call(this,a)}}var t=0;return e.prototype=Object.create(FormData.prototype),e}();e.exports={Blob:r,FormData:n()?i:FormData}},function(e,t,n){var r,i;(function(){function n(e){return!!e.exifdata}function o(e,t){t=t||e.match(/^data\:([^\;]+)\;base64,/im)[1]||"",e=e.replace(/^data\:([^\;]+)\;base64,/gim,"");for(var n=atob(e),r=n.length,i=new ArrayBuffer(r),o=new Uint8Array(i),a=0;r>a;a++)o[a]=n.charCodeAt(a);return i}function a(e,t){var n=new XMLHttpRequest;n.open("GET",e,!0),n.responseType="blob",n.onload=function(e){(200==this.status||0===this.status)&&t(this.response)},n.send()}function s(e,t){function n(n){var r=u(n),i=c(n);e.exifdata=r||{},e.iptcdata=i||{},t&&t.call(e)}if(e.src)if(/^data\:/i.test(e.src)){var r=o(e.src);n(r)}else if(/^blob\:/i.test(e.src)){var i=new FileReader;i.onload=function(e){n(e.target.result)},a(e.src,function(e){i.readAsArrayBuffer(e)})}else{var s=new XMLHttpRequest;s.onload=function(){200==this.status||0===this.status?n(s.response):t(new Error("Could not load image")),s=null},s.open("GET",e.src,!0),s.responseType="arraybuffer",s.send(null)}else if(window.FileReader&&(e instanceof window.Blob||e instanceof window.File)){var i=new FileReader;i.onload=function(e){p&&console.log("Got file of length "+e.target.result.byteLength),n(e.target.result)},i.readAsArrayBuffer(e)}}function u(e){var t=new DataView(e);if(p&&console.log("Got file of length "+e.byteLength),255!=t.getUint8(0)||216!=t.getUint8(1))return p&&console.log("Not a valid JPEG"),!1;for(var n,r=2,i=e.byteLength;i>r;){if(255!=t.getUint8(r))return p&&console.log("Not a valid marker at offset "+r+", found: "+t.getUint8(r)),!1;if(n=t.getUint8(r+1),p&&console.log(n),225==n)return p&&console.log("Found 0xFFE1 marker"),g(t,r+4,t.getUint16(r+2)-2);r+=2+t.getUint16(r+2)}}function c(e){var t=new DataView(e);if(p&&console.log("Got file of length "+e.byteLength),255!=t.getUint8(0)||216!=t.getUint8(1))return p&&console.log("Not a valid JPEG"),!1;for(var n=2,r=e.byteLength,i=function(e,t){return 56===e.getUint8(t)&&66===e.getUint8(t+1)&&73===e.getUint8(t+2)&&77===e.getUint8(t+3)&&4===e.getUint8(t+4)&&4===e.getUint8(t+5)};r>n;){if(i(t,n)){var o=t.getUint8(n+7);o%2!==0&&(o+=1),0===o&&(o=4);var a=n+8+o,s=t.getUint16(n+6+o);return l(e,a,s)}n++}}function l(e,t,n){for(var r,i,o,a,s,u=new DataView(e),c={},l=t;t+n>l;)28===u.getUint8(l)&&2===u.getUint8(l+1)&&(a=u.getUint8(l+2),a in S&&(o=u.getInt16(l+3),s=o+5,i=S[a],r=h(u,l+5,o),c.hasOwnProperty(i)?c[i]instanceof Array?c[i].push(r):c[i]=[c[i],r]:c[i]=r)),l++;return c}function f(e,t,n,r,i){var o,a,s,u=e.getUint16(n,!i),c={};for(s=0;u>s;s++)o=n+12*s+2,a=r[e.getUint16(o,!i)],!a&&p&&console.log("Unknown tag: "+e.getUint16(o,!i)),c[a]=d(e,o,t,n,i);return c}function d(e,t,n,r,i){var o,a,s,u,c,l,f=e.getUint16(t+2,!i),d=e.getUint32(t+4,!i),g=e.getUint32(t+8,!i)+n;switch(f){case 1:case 7:if(1==d)return e.getUint8(t+8,!i);for(o=d>4?g:t+8,a=[],u=0;d>u;u++)a[u]=e.getUint8(o+u);return a;case 2:return o=d>4?g:t+8,h(e,o,d-1);case 3:if(1==d)return e.getUint16(t+8,!i);for(o=d>2?g:t+8,a=[],u=0;d>u;u++)a[u]=e.getUint16(o+2*u,!i);return a;case 4:if(1==d)return e.getUint32(t+8,!i);for(a=[],u=0;d>u;u++)a[u]=e.getUint32(g+4*u,!i);return a;case 5:if(1==d)return c=e.getUint32(g,!i),l=e.getUint32(g+4,!i),s=new Number(c/l),s.numerator=c,s.denominator=l,s;for(a=[],u=0;d>u;u++)c=e.getUint32(g+8*u,!i),l=e.getUint32(g+4+8*u,!i),a[u]=new Number(c/l),a[u].numerator=c,a[u].denominator=l;return a;case 9:if(1==d)return e.getInt32(t+8,!i);for(a=[],u=0;d>u;u++)a[u]=e.getInt32(g+4*u,!i);return a;case 10:if(1==d)return e.getInt32(g,!i)/e.getInt32(g+4,!i);for(a=[],u=0;d>u;u++)a[u]=e.getInt32(g+8*u,!i)/e.getInt32(g+4+8*u,!i);return a}}function h(e,t,n){var r,i="";for(r=t;t+n>r;r++)i+=String.fromCharCode(e.getUint8(r));return i}function g(e,t){if("Exif"!=h(e,t,4))return p&&console.log("Not valid EXIF data! "+h(e,t,4)),!1;var n,r,i,o,a,s=t+6;if(18761==e.getUint16(s))n=!1;else{if(19789!=e.getUint16(s))return p&&console.log("Not valid TIFF data! (no 0x4949 or 0x4D4D)"),!1;n=!0}if(42!=e.getUint16(s+2,!n))return p&&console.log("Not valid TIFF data! (no 0x002A)"),!1;var u=e.getUint32(s+4,!n);if(8>u)return p&&console.log("Not valid TIFF data! (First offset less than 8)",e.getUint32(s+4,!n)),!1;if(r=f(e,s,s+u,v,n),r.ExifIFDPointer){o=f(e,s,s+r.ExifIFDPointer,w,n);for(i in o){switch(i){case"LightSource":case"Flash":case"MeteringMode":case"ExposureProgram":case"SensingMethod":case"SceneCaptureType":case"SceneType":case"CustomRendered":case"WhiteBalance":case"GainControl":case"Contrast":case"Saturation":case"Sharpness":case"SubjectDistanceRange":case"FileSource":o[i]=b[i][o[i]];break;case"ExifVersion":case"FlashpixVersion":o[i]=String.fromCharCode(o[i][0],o[i][1],o[i][2],o[i][3]);break;case"ComponentsConfiguration":o[i]=b.Components[o[i][0]]+b.Components[o[i][1]]+b.Components[o[i][2]]+b.Components[o[i][3]]}r[i]=o[i]}}if(r.GPSInfoIFDPointer){a=f(e,s,s+r.GPSInfoIFDPointer,y,n);for(i in a){switch(i){case"GPSVersionID":a[i]=a[i][0]+"."+a[i][1]+"."+a[i][2]+"."+a[i][3]}r[i]=a[i]}}return r}var p=!1,m=function(e){return e instanceof m?e:this instanceof m?void(this.EXIFwrapped=e):new m(e)};"undefined"!=typeof e&&e.exports&&(t=e.exports=m),t.EXIF=m;var w=m.Tags={36864:"ExifVersion",40960:"FlashpixVersion",40961:"ColorSpace",40962:"PixelXDimension",40963:"PixelYDimension",37121:"ComponentsConfiguration",37122:"CompressedBitsPerPixel",37500:"MakerNote",37510:"UserComment",40964:"RelatedSoundFile",36867:"DateTimeOriginal",36868:"DateTimeDigitized",37520:"SubsecTime",37521:"SubsecTimeOriginal",37522:"SubsecTimeDigitized",33434:"ExposureTime",33437:"FNumber",34850:"ExposureProgram",34852:"SpectralSensitivity",34855:"ISOSpeedRatings",34856:"OECF",37377:"ShutterSpeedValue",37378:"ApertureValue",37379:"BrightnessValue",37380:"ExposureBias",37381:"MaxApertureValue",37382:"SubjectDistance",37383:"MeteringMode",37384:"LightSource",37385:"Flash",37396:"SubjectArea",37386:"FocalLength",41483:"FlashEnergy",41484:"SpatialFrequencyResponse",41486:"FocalPlaneXResolution",41487:"FocalPlaneYResolution",41488:"FocalPlaneResolutionUnit",41492:"SubjectLocation",41493:"ExposureIndex",41495:"SensingMethod",41728:"FileSource",41729:"SceneType",41730:"CFAPattern",41985:"CustomRendered",41986:"ExposureMode",41987:"WhiteBalance",41988:"DigitalZoomRation",41989:"FocalLengthIn35mmFilm",41990:"SceneCaptureType",41991:"GainControl",41992:"Contrast",41993:"Saturation",41994:"Sharpness",41995:"DeviceSettingDescription",41996:"SubjectDistanceRange",40965:"InteroperabilityIFDPointer",42016:"ImageUniqueID"},v=m.TiffTags={256:"ImageWidth",257:"ImageHeight",34665:"ExifIFDPointer",34853:"GPSInfoIFDPointer",40965:"InteroperabilityIFDPointer",258:"BitsPerSample",259:"Compression",262:"PhotometricInterpretation",274:"Orientation",277:"SamplesPerPixel",284:"PlanarConfiguration",530:"YCbCrSubSampling",531:"YCbCrPositioning",282:"XResolution",283:"YResolution",296:"ResolutionUnit",273:"StripOffsets",278:"RowsPerStrip",279:"StripByteCounts",513:"JPEGInterchangeFormat",514:"JPEGInterchangeFormatLength",301:"TransferFunction",318:"WhitePoint",319:"PrimaryChromaticities",529:"YCbCrCoefficients",532:"ReferenceBlackWhite",306:"DateTime",270:"ImageDescription",271:"Make",272:"Model",305:"Software",315:"Artist",33432:"Copyright"},y=m.GPSTags={0:"GPSVersionID",1:"GPSLatitudeRef",2:"GPSLatitude",3:"GPSLongitudeRef",4:"GPSLongitude",5:"GPSAltitudeRef",6:"GPSAltitude",7:"GPSTimeStamp",8:"GPSSatellites",9:"GPSStatus",10:"GPSMeasureMode",11:"GPSDOP",12:"GPSSpeedRef",13:"GPSSpeed",14:"GPSTrackRef",15:"GPSTrack",16:"GPSImgDirectionRef",17:"GPSImgDirection",18:"GPSMapDatum",19:"GPSDestLatitudeRef",20:"GPSDestLatitude",21:"GPSDestLongitudeRef",22:"GPSDestLongitude",23:"GPSDestBearingRef",24:"GPSDestBearing",25:"GPSDestDistanceRef",26:"GPSDestDistance",27:"GPSProcessingMethod",28:"GPSAreaInformation",29:"GPSDateStamp",30:"GPSDifferential"},b=m.StringValues={ExposureProgram:{0:"Not defined",1:"Manual",2:"Normal program",3:"Aperture priority",4:"Shutter priority",5:"Creative program",6:"Action program",7:"Portrait mode",8:"Landscape mode"},MeteringMode:{0:"Unknown",1:"Average",2:"CenterWeightedAverage",3:"Spot",4:"MultiSpot",5:"Pattern",6:"Partial",255:"Other"},LightSource:{0:"Unknown",1:"Daylight",2:"Fluorescent",3:"Tungsten (incandescent light)",4:"Flash",9:"Fine weather",10:"Cloudy weather",11:"Shade",12:"Daylight fluorescent (D 5700 - 7100K)",13:"Day white fluorescent (N 4600 - 5400K)",14:"Cool white fluorescent (W 3900 - 4500K)",15:"White fluorescent (WW 3200 - 3700K)",17:"Standard light A",18:"Standard light B",19:"Standard light C",20:"D55",21:"D65",22:"D75",23:"D50",24:"ISO studio tungsten",255:"Other"},Flash:{0:"Flash did not fire",1:"Flash fired",5:"Strobe return light not detected",7:"Strobe return light detected",9:"Flash fired, compulsory flash mode",13:"Flash fired, compulsory flash mode, return light not detected",15:"Flash fired, compulsory flash mode, return light detected",16:"Flash did not fire, compulsory flash mode",24:"Flash did not fire, auto mode",25:"Flash fired, auto mode",29:"Flash fired, auto mode, return light not detected",31:"Flash fired, auto mode, return light detected",32:"No flash function",65:"Flash fired, red-eye reduction mode",69:"Flash fired, red-eye reduction mode, return light not detected",71:"Flash fired, red-eye reduction mode, return light detected",73:"Flash fired, compulsory flash mode, red-eye reduction mode",77:"Flash fired, compulsory flash mode, red-eye reduction mode, return light not detected",79:"Flash fired, compulsory flash mode, red-eye reduction mode, return light detected",89:"Flash fired, auto mode, red-eye reduction mode",93:"Flash fired, auto mode, return light not detected, red-eye reduction mode",95:"Flash fired, auto mode, return light detected, red-eye reduction mode"},SensingMethod:{1:"Not defined",2:"One-chip color area sensor",3:"Two-chip color area sensor",4:"Three-chip color area sensor",5:"Color sequential area sensor",7:"Trilinear sensor",8:"Color sequential linear sensor"},SceneCaptureType:{0:"Standard",1:"Landscape",2:"Portrait",3:"Night scene"},SceneType:{1:"Directly photographed"},CustomRendered:{0:"Normal process",1:"Custom process"},WhiteBalance:{0:"Auto white balance",1:"Manual white balance"},GainControl:{0:"None",1:"Low gain up",2:"High gain up",3:"Low gain down",4:"High gain down"},Contrast:{0:"Normal",1:"Soft",2:"Hard"},Saturation:{0:"Normal",1:"Low saturation",2:"High saturation"},Sharpness:{0:"Normal",1:"Soft",2:"Hard"},SubjectDistanceRange:{0:"Unknown",1:"Macro",2:"Close view",3:"Distant view"},FileSource:{3:"DSC"},Components:{0:"",1:"Y",2:"Cb",3:"Cr",4:"R",5:"G",6:"B"}},S={120:"caption",110:"credit",25:"keywords",55:"dateCreated",80:"byline",85:"bylineTitle",122:"captionWriter",105:"headline",116:"copyright",15:"category"};m.getData=function(e,t){return(e instanceof Image||e instanceof HTMLImageElement)&&!e.complete?!1:(n(e)?t&&t.call(e):s(e,t),!0)},m.getTag=function(e,t){return n(e)?e.exifdata[t]:void 0},m.getAllTags=function(e){if(!n(e))return{};var t,r=e.exifdata,i={};for(t in r)r.hasOwnProperty(t)&&(i[t]=r[t]);return i},m.pretty=function(e){if(!n(e))return"";var t,r=e.exifdata,i="";for(t in r)r.hasOwnProperty(t)&&(i+="object"==typeof r[t]?r[t]instanceof Number?t+" : "+r[t]+" ["+r[t].numerator+"/"+r[t].denominator+"]\r\n":t+" : ["+r[t].length+" values]\r\n":t+" : "+r[t]+"\r\n");return i},m.readFromBinaryFile=function(e){return u(e)},r=[],i=function(){return m}.apply(t,r),!(void 0!==i&&(e.exports=i))}).call(this)},function(e,t,n){var r,i;!function(){function n(e){var t=e.naturalWidth,n=e.naturalHeight;if(t*n>1048576){var r=document.createElement("canvas");r.width=r.height=1;var i=r.getContext("2d");return i.drawImage(e,-t+1,0),0===i.getImageData(0,0,1,1).data[3]}return!1}function o(e,t,n){var r=document.createElement("canvas");r.width=1,r.height=n;var i=r.getContext("2d");i.drawImage(e,0,0);for(var o=i.getImageData(0,0,1,n).data,a=0,s=n,u=n;u>a;){var c=o[4*(u-1)+3];0===c?s=u:a=u,u=s+a>>1}var l=u/n;return 0===l?1:l}function a(e,t,n){var r=document.createElement("canvas");return s(e,r,t,n),r.toDataURL("image/jpeg",t.quality||.8)}function s(e,t,r,i){var a=e.naturalWidth,s=e.naturalHeight,c=r.width,l=r.height,f=t.getContext("2d");f.save(),u(t,f,c,l,r.orientation);var d=n(e);d&&(a/=2,s/=2);var h=1024,g=document.createElement("canvas");g.width=g.height=h;for(var p=g.getContext("2d"),m=i?o(e,a,s):1,w=Math.ceil(h*c/a),v=Math.ceil(h*l/s/m),y=0,b=0;s>y;){for(var S=0,I=0;a>S;)p.clearRect(0,0,h,h),p.drawImage(e,-S,-y),f.drawImage(g,0,0,h,h,I,b,w,v),S+=h,I+=w;y+=h,b+=v}f.restore(),g=p=null}function u(e,t,n,r,i){switch(i){case 5:case 6:case 7:case 8:e.width=r,e.height=n;break;default:e.width=n,e.height=r}switch(i){case 2:t.translate(n,0),t.scale(-1,1);break;case 3:t.translate(n,r),t.rotate(Math.PI);break;case 4:t.translate(0,r),t.scale(1,-1);break;case 5:t.rotate(.5*Math.PI),t.scale(1,-1);break;case 6:t.rotate(.5*Math.PI),t.translate(0,-r);break;case 7:t.rotate(.5*Math.PI),t.translate(n,-r),t.scale(-1,1);break;case 8:t.rotate(-.5*Math.PI),t.translate(-n,0)}}function c(e){if(window.Blob&&e instanceof Blob){var t=new Image,n=window.URL&&window.URL.createObjectURL?window.URL:window.webkitURL&&window.webkitURL.createObjectURL?window.webkitURL:null;if(!n)throw Error("No createObjectURL function found to create blob url");t.src=n.createObjectURL(e),this.blob=e,e=t}if(!e.naturalWidth&&!e.naturalHeight){var r=this;e.onload=function(){var e=r.imageLoadListeners;if(e){r.imageLoadListeners=null;for(var t=0,n=e.length;n>t;t++)e[t]()}},this.imageLoadListeners=[]}this.srcImage=e}c.prototype.render=function(e,t,n){if(this.imageLoadListeners){var r=this;return void this.imageLoadListeners.push(function(){r.render(e,t,n)})}t=t||{};var i=this.srcImage,o=i.src,u=o.length,c=i.naturalWidth,l=i.naturalHeight,f=t.width,d=t.height,h=t.maxWidth,g=t.maxHeight,p=this.blob&&"image/jpeg"===this.blob.type||0===o.indexOf("data:image/jpeg")||o.indexOf(".jpg")===u-4||o.indexOf(".jpeg")===u-5;f&&!d?d=l*f/c<<0:d&&!f?f=c*d/l<<0:(f=c,d=l),h&&f>h&&(f=h,d=l*f/c<<0),g&&d>g&&(d=g,f=c*d/l<<0);var m={width:f,height:d};for(var w in t)m[w]=t[w];var v=e.tagName.toLowerCase();"img"===v?e.src=a(this.srcImage,m,p):"canvas"===v&&s(this.srcImage,e,m,p),"function"==typeof this.onrender&&this.onrender(e),n&&n()},r=[],i=function(){return c}.apply(t,r),!(void 0!==i&&(e.exports=i))}()},function(e,t){function n(e){function t(e){for(var t=[16,11,10,16,24,40,51,61,12,12,14,19,26,58,60,55,14,13,16,24,40,57,69,56,14,17,22,29,51,87,80,62,18,22,37,56,68,109,103,77,24,35,55,64,81,104,113,92,49,64,78,87,103,121,120,101,72,92,95,98,112,100,103,99],n=0;64>n;n++){var r=F((t[n]*e+50)/100);1>r?r=1:r>255&&(r=255),D[N[n]]=r}for(var i=[17,18,24,47,99,99,99,99,18,21,26,66,99,99,99,99,24,26,56,99,99,99,99,99,47,66,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99],o=0;64>o;o++){var a=F((i[o]*e+50)/100);1>a?a=1:a>255&&(a=255),x[N[o]]=a}for(var s=[1,1.387039845,1.306562965,1.175875602,1,.785694958,.5411961,.275899379],u=0,c=0;8>c;c++)for(var l=0;8>l;l++)U[u]=1/(D[N[u]]*s[c]*s[l]*8),C[u]=1/(x[N[u]]*s[c]*s[l]*8),u++}function n(e,t){for(var n=0,r=0,i=new Array,o=1;16>=o;o++){for(var a=1;a<=e[o];a++)i[t[r]]=[],i[t[r]][0]=n,i[t[r]][1]=o,r++,n++;n*=2}return i}function r(){y=n(W,H),b=n(V,X),S=n(z,q),I=n(Q,Y)}function i(){for(var e=1,t=2,n=1;15>=n;n++){for(var r=e;t>r;r++)A[32767+r]=n,T[32767+r]=[],T[32767+r][1]=n,T[32767+r][0]=r;for(var i=-(t-1);-e>=i;i++)A[32767+i]=n,T[32767+i]=[],T[32767+i][1]=n,T[32767+i][0]=t-1+i;e<<=1,t<<=1}}function o(){for(var e=0;256>e;e++)k[e]=19595*e,k[e+256>>0]=38470*e,k[e+512>>0]=7471*e+32768,k[e+768>>0]=-11059*e,k[e+1024>>0]=-21709*e,k[e+1280>>0]=32768*e+8421375,k[e+1536>>0]=-27439*e,k[e+1792>>0]=-5329*e}function a(e){for(var t=e[0],n=e[1]-1;n>=0;)t&1<<n&&(G|=1<<O),n--,O--,0>O&&(255==G?(s(255),s(0)):s(G),O=7,G=0)}function s(e){M.push(j[e])}function u(e){s(e>>8&255),s(255&e)}function c(e,t){var n,r,i,o,a,s,u,c,l,f=0;var d=8,h=64;for(l=0;d>l;++l){n=e[f],r=e[f+1],i=e[f+2],o=e[f+3],a=e[f+4],s=e[f+5],u=e[f+6],c=e[f+7];var g=n+c,p=n-c,m=r+u,w=r-u,v=i+s,y=i-s,b=o+a,S=o-a,I=g+b,P=g-b,F=m+v,D=m-v;e[f]=I+F,e[f+4]=I-F;var x=.707106781*(D+P);e[f+2]=P+x,e[f+6]=P-x,I=S+y,F=y+w,D=w+p;var U=.382683433*(I-D),C=.5411961*I+U,T=1.306562965*D+U,A=.707106781*F,R=p+A,M=p-A;e[f+5]=M+C,e[f+3]=M-C,e[f+1]=R+T,e[f+7]=R-T,f+=8}for(f=0,l=0;d>l;++l){n=e[f],r=e[f+8],i=e[f+16],o=e[f+24],a=e[f+32],s=e[f+40],u=e[f+48],c=e[f+56];var G=n+c,O=n-c,_=r+u,B=r-u,E=i+s,j=i-s,k=o+a,N=o-a,W=G+k,H=G-k,z=_+E,q=_-E;e[f]=W+z,e[f+32]=W-z;var V=.707106781*(q+H);e[f+16]=H+V,e[f+48]=H-V,W=N+j,z=j+B,q=B+O;var X=.382683433*(W-q),Q=.5411961*W+X,Y=1.306562965*q+X,K=.707106781*z,J=O+K,Z=O-K;e[f+40]=Z+Q,e[f+24]=Z-Q,e[f+8]=J+Y,e[f+56]=J-Y,f++}var $;for(l=0;h>l;++l)$=e[l]*t[l],L[l]=$>0?$+.5|0:$-.5|0;return L}function l(){u(65504),u(16),s(74),s(70),s(73),s(70),s(0),s(1),s(1),s(0),u(1),u(1),s(0),s(0)}function f(e,t){u(65472),u(17),s(8),u(t),u(e),s(3),s(1),s(17),s(0),s(2),s(17),s(1),s(3),s(17),s(1)}function d(){u(65499),u(132),s(0);for(var e=0;64>e;e++)s(D[e]);s(1);for(var t=0;64>t;t++)s(x[t])}function h(){u(65476),u(418),s(0);for(var e=0;16>e;e++)s(W[e+1]);for(var t=0;11>=t;t++)s(H[t]);s(16);for(var n=0;16>n;n++)s(z[n+1]);for(var r=0;161>=r;r++)s(q[r]);s(1);for(var i=0;16>i;i++)s(V[i+1]);for(var o=0;11>=o;o++)s(X[o]);s(17);for(var a=0;16>a;a++)s(Q[a+1]);for(var c=0;161>=c;c++)s(Y[c])}function g(){u(65498),u(12),s(3),s(1),s(0),s(2),s(17),s(3),s(17),s(0),s(63),s(0)}function p(e,t,n,r,i){var o,s=i[0],u=i[240];var l=16,f=63,d=64;for(var h=c(e,t),g=0;d>g;++g)R[N[g]]=h[g];var p=R[0]-n;n=R[0],0==p?a(r[0]):(o=32767+p,a(r[A[o]]),a(T[o]));for(var m=63;m>0&&0==R[m];m--);if(0==m)return a(s),n;for(var w,v=1;m>=v;){for(var y=v;0==R[v]&&m>=v;++v);var b=v-y;if(b>=l){w=b>>4;for(var S=1;w>=S;++S)a(u);b=15&b}o=32767+R[v],a(i[(b<<4)+A[o]]),a(T[o]),v++}return m!=f&&a(s),n}function m(){for(var e=String.fromCharCode,t=0;256>t;t++)j[t]=e(t)}function w(e){if(0>=e&&(e=1),e>100&&(e=100),P!=e){var n=0;n=50>e?Math.floor(5e3/e):Math.floor(200-2*e),t(n),P=e}}function v(){var t=(new Date).getTime();e||(e=50),m(),r(),i(),o(),w(e);(new Date).getTime()-t}var y,b,S,I,P,F=(Math.round,Math.floor),D=new Array(64),x=new Array(64),U=new Array(64),C=new Array(64),T=new Array(65535),A=new Array(65535),L=new Array(64),R=new Array(64),M=[],G=0,O=7,_=new Array(64),B=new Array(64),E=new Array(64),j=new Array(256),k=new Array(2048),N=[0,1,5,6,14,15,27,28,2,4,7,13,16,26,29,42,3,8,12,17,25,30,41,43,9,11,18,24,31,40,44,53,10,19,23,32,39,45,52,54,20,22,33,38,46,51,55,60,21,34,37,47,50,56,59,61,35,36,48,49,57,58,62,63],W=[0,0,1,5,1,1,1,1,1,1,0,0,0,0,0,0,0],H=[0,1,2,3,4,5,6,7,8,9,10,11],z=[0,0,2,1,3,3,2,4,3,5,5,4,4,0,0,1,125],q=[1,2,3,0,4,17,5,18,33,49,65,6,19,81,97,7,34,113,20,50,129,145,161,8,35,66,177,193,21,82,209,240,36,51,98,114,130,9,10,22,23,24,25,26,37,38,39,40,41,42,52,53,54,55,56,57,58,67,68,69,70,71,72,73,74,83,84,85,86,87,88,89,90,99,100,101,102,103,104,105,106,115,116,117,118,119,120,121,122,131,132,133,134,135,136,137,138,146,147,148,149,150,151,152,153,154,162,163,164,165,166,167,168,169,170,178,179,180,181,182,183,184,185,186,194,195,196,197,198,199,200,201,202,210,211,212,213,214,215,216,217,218,225,226,227,228,229,230,231,232,233,234,241,242,243,244,245,246,247,248,249,250],V=[0,0,3,1,1,1,1,1,1,1,1,1,0,0,0,0,0],X=[0,1,2,3,4,5,6,7,8,9,10,11],Q=[0,0,2,1,2,4,4,3,4,7,5,4,4,0,1,2,119],Y=[0,1,2,3,17,4,5,33,49,6,18,65,81,7,97,113,19,34,50,129,8,20,66,145,161,177,193,9,35,51,82,240,21,98,114,209,10,22,36,52,225,37,241,23,24,25,26,38,39,40,41,42,53,54,55,56,57,58,67,68,69,70,71,72,73,74,83,84,85,86,87,88,89,90,99,100,101,102,103,104,105,106,115,116,117,118,119,120,121,122,130,131,132,133,134,135,136,137,138,146,147,148,149,150,151,152,153,154,162,163,164,165,166,167,168,169,170,178,179,180,181,182,183,184,185,186,194,195,196,197,198,199,200,201,202,210,211,212,213,214,215,216,217,218,226,227,228,229,230,231,232,233,234,242,243,244,245,246,247,248,249,250];this.encode=function(e,t,n){var r=(new Date).getTime();t&&w(t),M=new Array,G=0,O=7,u(65496),l(),d(),f(e.width,e.height),h(),g();var i=0,o=0,s=0;G=0,O=7,this.encode.displayName="_encode_";for(var c,m,v,P,F,D,x,T,A,L=e.data,R=e.width,j=e.height,N=4*R,W=0;j>W;){for(c=0;N>c;){for(F=N*W+c,D=F,x=-1,T=0,A=0;64>A;A++)T=A>>3,x=4*(7&A),D=F+T*N+x,W+T>=j&&(D-=N*(W+1+T-j)),c+x>=N&&(D-=c+x-N+4),m=L[D++],v=L[D++],P=L[D++],_[A]=(k[m]+k[v+256>>0]+k[P+512>>0]>>16)-128,B[A]=(k[m+768>>0]+k[v+1024>>0]+k[P+1280>>0]>>16)-128,E[A]=(k[m+1280>>0]+k[v+1536>>0]+k[P+1792>>0]>>16)-128;i=p(_,U,i,y,S),o=p(B,C,o,b,I),s=p(E,C,s,b,I),c+=32}W+=8}if(O>=0){var H=[];H[1]=O+1,H[0]=(1<<O+1)-1,a(H)}if(u(65497),n){for(var z=M.length,q=new Uint8Array(z),V=0;z>V;V++)q[V]=M[V].charCodeAt();M=[];(new Date).getTime()-r;return q}var X="data:image/jpeg;base64,"+btoa(M.join(""));M=[];(new Date).getTime()-r;return X},v()}e.exports=n},function(e,t,n){function r(e,t){var n=this;if(!e)throw new Error("没有收到图片，可能的解决方案：https://github.com/think2011/localResizeIMG/issues/7");t=t||{},n.defaults={width:null,height:null,fieldName:"file",quality:.7},n.file=e;for(var r in t)t.hasOwnProperty(r)&&(n.defaults[r]=t[r]);return this.init()}function i(e){var t=null;return t=e?[].filter.call(document.scripts,function(t){return-1!==t.src.indexOf(e)})[0]:document.scripts[document.scripts.length-1],t?t.src.substr(0,t.src.lastIndexOf("/")):null}function o(e){var t;t=e.split(",")[0].indexOf("base64")>=0?atob(e.split(",")[1]):unescape(e.split(",")[1]);for(var n=e.split(",")[0].split(":")[1].split(";")[0],r=new Uint8Array(t.length),i=0;i<t.length;i++)r[i]=t.charCodeAt(i);return new s.Blob([r.buffer],{type:n})}n.p=i("lrz")+"/",window.URL=window.URL||window.webkitURL;var a=n(1),s=n(4),u=n(5),c=function(e){var t=/OS (\d)_.* like Mac OS X/g.exec(e),n=/Android (\d.*?);/g.exec(e)||/Android\/(\d.*?) /g.exec(e);return{oldIOS:t?+t.pop()<8:!1,oldAndroid:n?+n.pop().substr(0,3)<4.5:!1,iOS:/\(i[^;]+;( U;)? CPU.+Mac OS X/.test(e),android:/Android/g.test(e),mQQBrowser:/MQQBrowser/g.test(e)}}(navigator.userAgent);r.prototype.init=function(){var e=this,t=e.file,n="string"==typeof t,r=/^data:/.test(t),i=new Image,u=document.createElement("canvas"),c=n?t:URL.createObjectURL(t);if(e.img=i,e.blob=c,e.canvas=u,n?e.fileName=r?"base64.jpg":t.split("/").pop():e.fileName=t.name,!document.createElement("canvas").getContext)throw new Error("浏览器不支持canvas");return new a(function(n,a){i.onerror=function(){var e=new Error("加载图片文件失败");throw a(e),e},i.onload=function(){e._getBase64().then(function(e){if(e.length<10){var t=new Error("生成base64失败");throw a(t),t}return e}).then(function(r){var i=null;"object"==typeof e.file&&r.length>e.file.size?(i=new FormData,t=e.file):(i=new s.FormData,t=o(r)),i.append(e.defaults.fieldName,t,e.fileName.replace(/\..+/g,".jpg")),n({formData:i,fileLen:+t.size,base64:r,base64Len:r.length,origin:e.file,file:t});for(var a in e)e.hasOwnProperty(a)&&(e[a]=null);URL.revokeObjectURL(e.blob)})},!r&&(i.crossOrigin="*"),i.src=c})},r.prototype._getBase64=function(){var e=this,t=e.img,n=e.file,r=e.canvas;return new a(function(i){try{u.getData("object"==typeof n?n:t,function(){e.orientation=u.getTag(this,"Orientation"),e.resize=e._getResize(),e.ctx=r.getContext("2d"),r.width=e.resize.width,r.height=e.resize.height,e.ctx.fillStyle="#fff",e.ctx.fillRect(0,0,r.width,r.height),c.oldIOS?e._createBase64ForOldIOS().then(i):e._createBase64().then(i)})}catch(o){throw new Error(o)}})},r.prototype._createBase64ForOldIOS=function(){var e=this,t=e.img,r=e.canvas,i=e.defaults,o=e.orientation;return new a(function(e){!function(){var a=[n(6)];(function(n){var a=new n(t);"5678".indexOf(o)>-1?a.render(r,{width:r.height,height:r.width,orientation:o}):a.render(r,{width:r.width,height:r.height,orientation:o}),e(r.toDataURL("image/jpeg",i.quality))}).apply(null,a)}()})},r.prototype._createBase64=function(){var e=this,t=e.resize,r=e.img,i=e.canvas,o=e.ctx,s=e.defaults,u=e.orientation;switch(u){case 3:o.rotate(180*Math.PI/180),o.drawImage(r,-t.width,-t.height,t.width,t.height);break;case 6:o.rotate(90*Math.PI/180),o.drawImage(r,0,-t.width,t.height,t.width);break;case 8:o.rotate(270*Math.PI/180),o.drawImage(r,-t.height,0,t.height,t.width);break;case 2:o.translate(t.width,0),o.scale(-1,1),o.drawImage(r,0,0,t.width,t.height);break;case 4:o.translate(t.width,0),o.scale(-1,1),o.rotate(180*Math.PI/180),o.drawImage(r,-t.width,-t.height,t.width,t.height);break;case 5:o.translate(t.width,0),o.scale(-1,1),o.rotate(90*Math.PI/180),o.drawImage(r,0,-t.width,t.height,t.width);break;case 7:o.translate(t.width,0),o.scale(-1,1),o.rotate(270*Math.PI/180),o.drawImage(r,-t.height,0,t.height,t.width);break;default:o.drawImage(r,0,0,t.width,t.height)}return new a(function(e){c.oldAndroid||c.mQQBrowser||!navigator.userAgent?!function(){var t=[n(7)];(function(t){var n=new t,r=o.getImageData(0,0,i.width,i.height);e(n.encode(r,100*s.quality))}).apply(null,t)}():e(i.toDataURL("image/jpeg",s.quality))})},r.prototype._getResize=function(){var e=this,t=e.img,n=e.defaults,r=n.width,i=n.height,o=e.orientation,a={width:t.width,height:t.height};if("5678".indexOf(o)>-1&&(a.width=t.height,a.height=t.width),a.width<r||a.height<i)return a;var s=a.width/a.height;for(r&&i?s>=r/i?a.width>r&&(a.width=r,a.height=Math.ceil(r/s)):a.height>i&&(a.height=i,a.width=Math.ceil(i*s)):r?r<a.width&&(a.width=r,a.height=Math.ceil(r/s)):i&&i<a.height&&(a.width=Math.ceil(i*s),a.height=i);a.width>=3264||a.height>=2448;)a.width*=.8,a.height*=.8;return a},window.lrz=function(e,t){return new r(e,t)},window.lrz.version="4.9.40",
e.exports=window.lrz}])});var AIBASE={canPay:false,maxBodySize:10*1024*1024,base:"",recog:function(options){var service=options.service;var appKey=options.appKey;var appSecret=options.appSecret;var base64Data=options.base64Data;var binaryData=options.binaryData;var complete=options.complete;var params=options.params;if(!service||!appKey||!appSecret)return;if(!base64Data&&!binaryData)return;var url=window.AiOcrUrl+"cci_ai/service/v1/"+service;var futian=["classification_futian","recognize_vehicle_certificate_futian","recognize_invoice_futian","recognize_vehicle_regist_page_futian","recognize_vehicle_mortgage_page_futian","recognize_insurance_policy_futian","recognize_leases_detail_futian","recognize_mortgage_contract_futian","recognize_mortgage_vehicle_list_futian","recognize_finance_lease_contract_futian","recognize_claims_transfer_notice_futian","recognize_grant_commitment_letter_futian","recognize_joint_responsibility_distributor_futian","recognize_joint_responsibility_natural_person_futian","recognize_joint_responsibility_legal_futian","futian_external_security","recognize_finance_lease_contract_futian_v1"];if(futian.indexOf(service)>=0){url=window.AiOcrUrl+"cci_ai/service/icr/"+service}if(params){url=iu.http.param(params,url)}var ajaxOptions={type:"POST",url:url,header:{"app-key":appKey,"app-secret":appSecret},timeout:30*1e3,complete:function(text,status,xhr){complete(DPS.ajax.parse(text,xhr.status),status,xhr)}};if(base64Data){ajaxOptions.base64=base64Data}else if(binaryData){ajaxOptions.data=binaryData}iu.http.ajax(ajaxOptions)},recogV0_9:function(options){var service=options.service;var appKey=options.appKey;var appSecret=options.appSecret;var base64Data=options.base64Data;var binaryData=options.binaryData;var complete=options.complete;var params=options.params;if(!appKey||!appSecret)return;if(!base64Data&&!binaryData)return;var url=window.AiOcrUrl+"/ocr_service?app_key="+appKey;if(params){url=iu.http.param(params,url)}var ajaxOptions={type:"POST",url:url,timeout:30*1e3,complete:function(text,status,xhr){if(complete){complete(DPS.ajax.parse(text,xhr.status),status,xhr)}}};var body={app_secret:appSecret,image_data:base64Data};if(options.template){body.template=options.template}ajaxOptions.data=JSON.stringify(body);iu.http.ajax(ajaxOptions)},handle:function(code,message){if(code){code=parseInt(code);var ERROR=AIBASE.ERROR;if(code&&message){DPS.notice.error(message)}else if(code===ERROR.TOKEN_EXPITE){DPS.notice.login()}else if(code===ERROR.INVALID_PARAM){DPS.notice.error("参数错误")}else if(code===ERROR.SERVER_BUSY){}else if(code===ERROR.PERMISSION_DENIED){DPS.notice.error("权限不足")}else if(code===ERROR.NOT_FOUND){DPS.notice.error("无任务")}else if(code===ERROR.USER_DENIED){DPS.notice.error(message||"账号被禁用")}else if(code===ERROR.USER_NOT_EXIST){DPS.notice.error("账号不存在")}else if(code===ERROR.TIMEOUT){DPS.notice.error("请求超时")}else if(code===ERROR.ROOM_DENIED){DPS.notice.error(message||"您没有该房间的录入权限")}else if(code===ERROR.TASK_FINISHED){}else if(code===ERROR.TASK_OCCUPIED){}else if(code===ERROR.TOO_QUICK){DPS.notice.error("操作太快")}else if(code===ERROR.UDPLICATE_DATA){DPS.notice.error("重复数据/重复操作")}else if(code===ERROR.DOUBT_DATA){DPS.notice.error("异常任务, 提交数据包含敏感词")}else if(code===ERROR.USER_FROZEN){DPS.notice.error(message||"账号提交任务异常被冻结, 等待审核")}}},getUrlIntId:function(){return parseInt(location.href.replace(/^.*\/(\d+)/,"$1"))||""},patchImageData:function(list){$.each(list,function(i,item){if(item.image_data){item.image_data=DPS.base64JPG+item.image_data}else{delete item.image_data}})},isWeakPassword:function(password){if(!password)return true;if(password.length<6)return true;if(password.match(/^[0-9]+$/))return true;if(password.match(/^[a-z]+$/))return true;if(password.match(/^[A-Z]+$/))return true;return false},noApplyMaxCount:1e4,apiCategory:[{id:100,home_visible:true,name:"基础OCR",icon:"index-icon-jichu.png",icon_active:"index-icon-jichu-white.png",icon_lg:"index-icon-jichu-lg.png",bg_icon:"index-bg-icon-jichu.png"},{id:200,home_visible:true,name:"证照OCR",icon:"index-icon-zhengzhao.png",icon_active:"index-icon-zhengzhao-white.png",icon_lg:"index-icon-zhengzhao-lg.png",bg_icon:"index-bg-icon-zhengzhao.png"},{id:300,home_visible:true,name:"车辆相关OCR",icon:"index-icon-cheliang.png",icon_active:"index-icon-cheliang-white.png",icon_lg:"index-icon-cheliang-lg.png",bg_icon:"index-bg-icon-cheliang.png"},{id:400,home_visible:true,name:"财报、票据OCR",icon:"index-icon-caibao.png",icon_active:"index-icon-caibao-white.png",icon_lg:"index-icon-caibao-lg.png",bg_icon:"index-bg-icon-caibao.png"},{id:500,home_visible:true,name:"其他OCR",icon:"index-icon-qita.png",icon_active:"index-icon-qita-white.png",icon_lg:"index-icon-qita-lg.png",bg_icon:"index-bg-icon-qita.png"},{id:1010,home_visible:false,name:"图像识别技术",icon:"index-icon-qita.png",icon_active:"index-icon-qita-white.png",icon_lg:"index-icon-qita-lg.png",bg_icon:"index-bg-icon-qita.png"}],macro:{data_valid:1,data_invalid:2,user_status:{valid:1,disabled:2,deleted:-1},user_role:{dev:1,support:2,super_support:4,customer:8},data_status:{valid:1,invalid:2,deleted:-1},apply_status:{applying:0,succeeded:1,failed:2},application_type:{formal:1,test:2},application_status:{valid:1,deleted:-1,applying:0,apply_failed:2},add_count_type:{create_application:1,add_count:2},api_privacy:{is_public:1,is_private:2},pay_status:{unpaid:0,paid:1},app_add_status:{unadded:-1,wait_to_add:0,added:1},pay_way:{alipay:1,weixin:2,support:4},invoice_status:{not_invoiced:0,invoiced:1,failed:-1},trade_invoice_status:{not_invoiced:0,invoiced:1},invoice_mail_status:{not_mailed:0,mailed:1,failed:-1},invoice_type:{electronic:1,paper:2},invoice_title_type:{enterprise:1,person:2},message_status:{deleted:-1,not_read:1,readed:2},from_type:{web:1,mp:2},api_flow_status:{processing:10,failure_of_parameter_validation:20,access_failed:30,ocr_failed:40,succeed:1024,deduction_failed:1025},language:{zh_cn:1,en:2,ja:4,ko:8},deduction_mode:{annual:1,app_count:2,user_balance:4},trade_type:{user_balance:1,count_or_annual:2},belong_company:{intsig:1,shengteng:2}},langName:function(id){var language=AIBASE.macro.language;id=parseInt(id);if(id===language.zh_cn)return"简体中文";else if(id===language.en)return"English";else if(id===language.ja)return"日本語";else if(id===language.ko)return"한글";else return""},categoryName:function(id){var list=AIBASE.apiCategory,name="";list.forEach(function(item){if(item.id===id){name=item.name}});return name},applicationTypeName:function(type){type=parseInt(type);if(type===AIBASE.macro.application_type.formal)return"正式";else if(type===AIBASE.macro.application_type.test)return"测试";else return""},applicationStatusName:function(status){status=parseInt(status);if(status===AIBASE.macro.application_status.valid)return"正常";else if(status===AIBASE.macro.application_status.deleted)return"禁用";else if(status===AIBASE.macro.application_status.applying)return"申请中";else return""},addCountTypeName:function(type){type=parseInt(type);if(type===AIBASE.macro.add_count_type.create_application)return"创建实例初始额度";else if(type===AIBASE.macro.add_count_type.add_count)return"增加额度";else return""},commonInvoicesName:function(type){var map={vat_special_invoice:"增值税专用发票",motor_vehicle_sale_invoice:"机动车销售统一发票",vat_transport_invoice:"货物运输业增值税专用发票",vat_common_invoice:"增值税普通发票",vat_electronic_invoice:"增值税电子普通发票",vat_roll_invoice:"增值税普通发票（卷票）",vat_electronic_toll_invoice:"增值税电子普通发票（通行费）",used_car_purchase_invoice:"二手车销售统一发票",general_machine_invoice:"通用机打发票",quota_invoice:"通用定额发票",passenger_transport_invoice:"旅客运输普票",highway_passenger_invoice:"公路客运发票",shipping_invoice:"船运客票",taxi_ticket:"出租车发票",parking_invoice:"停车费发票",vehicle_toll:"过路过桥费发票、汽车通行费",education_receipt:"教育费收据",air_transport:"行程单",train_ticket:"火车票",other:"其它类型"};return map[type]||type},invoiceStatusName:function(status){var name="";switch(status){case AIBASE.macro.invoice_status.not_invoiced:name="正在开具";break;case AIBASE.macro.invoice_status.invoiced:name="开票成功";break;case AIBASE.macro.invoice_status.failed:name="开票失败";break}return name},invoiceTypeName:function(type){var name="";switch(type){case AIBASE.macro.invoice_type.electronic:name="电子发票";break;case AIBASE.macro.invoice_type.paper:name="纸质发票";break}return name},invoiceTitleTypeName:function(type){var name="";switch(type){case AIBASE.macro.invoice_title_type.enterprise:name="企业单位";break;case AIBASE.macro.invoice_title_type.person:name="个人/非企业单位";break}return name},payStatusName:function(id){id=parseInt(id);if(id===AIBASE.macro.pay_status.unpaid){return"未支付"}else if(id===AIBASE.macro.pay_status.paid){return"已支付"}else{return"其他状态"}},payAddStatusName:function(id){id=parseInt(id);if(id===AIBASE.macro.app_add_status.unadded){return"额度未到帐"}else if(id===AIBASE.macro.app_add_status.wait_to_add){return"正在增加额度"}else if(id===AIBASE.macro.app_add_status.added){return"额度已到账"}else{return"其他状态"}},tradeInvoiceStatusName:function(id){id=parseInt(id);if(id===AIBASE.macro.trade_invoice_status.not_invoiced){return"未开票"}else if(id===AIBASE.macro.trade_invoice_status.invoiced){return"已开票"}else{return"其他状态"}},invoiceMailStatusName:function(id){if(id===AIBASE.macro.invoice_mail_status.not_mailed){return"未邮寄/发送"}else if(id===AIBASE.macro.invoice_mail_status.mailed){return"已邮寄/发送"}else if(id===AIBASE.macro.invoice_mail_status.failed){return"邮寄/发送失败"}else{return"其他状态"}},payWayName:function(id){id=parseInt(id);if(id===AIBASE.macro.pay_way.alipay)return"支付宝";else if(id===AIBASE.macro.pay_way.weixin)return"微信支付";else if(id===AIBASE.macro.pay_way.support)return"线下付款";else return"其他方式"}};DPS.ajax.patch(AIBASE,{request:function(url){var token=window.AiToken;if(token)url=DPS.url.set("token",token,url);return{url:url}},error:AIBASE.handle});AIBASE.ERROR={INVALID_PARAM:101,TOO_QUICK:103,USER_NOT_EXIST:201,PASSWORD_WRONG:202,TOKEN_EXPITE:203,PERMISSION_DENIED:204,USER_DENIED:205,NOT_FOUND:206,UDPLICATE_DATA:207,DOUBT_DATA:208,USER_FROZEN:209,CUSTOM_MESSAGE:209,ROOM_DENIED:301,LANGUAGE_DENIED:302,TASK_NOT_PROCESSING:304,TASK_FINISHED:305,TASK_OCCUPIED:306,SERVER_BUSY:500,TIMEOUT:501};AIBASE.getBase64=function(url,callback){if(!url)return"";var API_URL_TO_BASE64="/service/url-to-base64";DPS.ajax.post(API_URL_TO_BASE64,{url:url},function(data){if(data){var base64=data.image_data;callback(base64)}})};AIBASE.JSONFormat=function(txt,compress){var indentChar="    ";if(/^\s*$/.test(txt)){return""}if(typeof txt==="string"){data=JSON.parse(txt)}else{data=txt}var draw=[],last=false,This=this,line=compress?"":"\n",nodeCount=0,maxDepth=0;var notify=function(name,value,isLast,indent,formObj){nodeCount++;for(var i=0,tab="";i<indent;i++)tab+=indentChar;tab=compress?"":tab;maxDepth=++indent;if(value&&value.constructor==Array){draw.push(tab+(formObj?'"'+name+'":':"")+"["+line);for(var i=0;i<value.length;i++)notify(i,value[i],i==value.length-1,indent,false);draw.push(tab+"]"+(isLast?line:","+line))}else if(value&&typeof value=="object"){draw.push(tab+(formObj?'"'+name+'":':"")+"{"+line);var len=0,i=0;for(var key in value)len++;for(var key in value)notify(key,value[key],++i==len,indent,true);draw.push(tab+"}"+(isLast?line:","+line))}else{if(typeof value=="string")value='"'+value+'"';draw.push(tab+(formObj?'"'+name+'":':"")+value+(isLast?"":",")+line)}};var isLast=true,indent=0;notify("",data,isLast,indent,false);return draw.join("")};function ready(callback){if(document.readyState!=="loading")callback();else if(document.addEventListener)document.addEventListener("DOMContentLoaded",callback);else document.attachEvent("onreadystatechange",function(){if(document.readyState==="complete")callback()})}ready(function(){AIBASE.base=window.AiBaseUrl;AIMACRO=AIBASE.macro});ready(function(){var $html;var $dialog;var $go;var $chat;var $tip;var $category;var $description;var $company;var $name;var $phone;var $email;var creating;var vm={initDom:function(){$html=document.querySelector("html");$dialog=$("#dialog-consult");$go=$(".sidebar-container .go-top");$chat=$(".go-shangqiao-chat");$call=$(".go-call-market");$tip=$dialog.find(".tip");$category=$dialog.find('[name="category"]');$description=$dialog.find('[name="description"]');$company=$dialog.find('[name="company"]');$name=$dialog.find('[name="name"]');$phone=$dialog.find('[name="phone"]');$email=$dialog.find('[name="email"]')},initEvent:function(){$("#dialog-consult-toggle,#dialog-consult [data-dismiss],.sidebar-container .go-consult").on("click",function(){var hidden=$dialog.prop("hidden");if(hidden){$dialog.hide().prop("hidden",false).fadeIn(function(){$description.focus()})}else{$dialog.fadeOut(function(){$dialog.prop("hidden",true)})}});$go.on("click",function(){$html.scrollTop=0});if($go.length){$(window).on("scroll",function(e){$go.toggleClass("invisible",$html.scrollTop<1e3)})}$chat.on("click",function(){window.open("http://p.qiao.baidu.com/cps/chat?siteId=16201407&userId=10218635&siteToken=6029b8f92d0cbe82af9a819f0ef62fdf&cp=&cr=&cw=AI%E5%B9%B3%E5%8F%B0","_black","scrollbars=yes,resizable=1,modal=false,alwaysRaised=yes,height=600,width=900")});$call.on("click",function(){window.location.href="tel://"+"************"});$dialog.find(".btn-commit").on("click",function(){var category=$category.val().trim();var description=$description.val().trim();var company=$company.val().trim();var name=$name.val().trim();var phone=$phone.val().trim();var email=$email.val().trim();if(category===0||category==="0"){$tip.html("请选择咨询意向");$category.focus();return}if(!description){$tip.html("请填写您需要咨询的内容");$description.focus();return}if(!company){$tip.html("请填写您所在的公司名称，个人用户请填写个人");$company.focus();return}if(!name){$tip.html("请填写您的真实姓名");$name.focus();return}if(!DPS.isMobile(phone)){$tip.html("手机号码的格式似乎不正确");$phone.focus();return}if(email){if(!DPS.isEmail(email)){$tip.html("电子邮件的格式似乎不正确");$email.focus();return}}var param={category:category,description:description,company:company,product:"合合AI开放平台Web站",name:name,phone:phone,email:email};vm.submit(param,false)})},submit:function(param,retry){if(creating){return}creating=1;DPS.ajax.post("/consult/create-api",param,function(data,code){creating=0;if(code){DPS.notice.error("信息提交失败，请稍后重试");return}DPS.notice.ok("提交成功，我们会及时处理");$(".sidebar-container .go-consult").triggerHandler("click");$tip.html("")})}};vm.initDom();vm.initEvent()});ready(function(){if(DPS.isIe()){if(!DPS.isIE11()){var message="您正在使用低版本IE浏览器，为了您的使用体验，请切换至谷歌、火狐等浏览器访问。\n\n"+"You are using a low version of IE browser, please switch to Google, Firefox or other modern browsers.\n\n"+"あなたはIEブラウザの低バージョンを使用しています、Google、Firefoxまたはその他の最新のブラウザに切り替えてください。";alert(message)}}});