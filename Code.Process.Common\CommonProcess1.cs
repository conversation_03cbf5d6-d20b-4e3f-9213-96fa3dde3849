﻿using CommonLib;
using DaMaLib;
using log4net;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace Code.Process.Common
{
    public class CommonProcess
    {

        private static ILog _Log;

        static CommonProcess()
        {
            _Log = LogManager.GetLogger("ImageProcess");
        }

        public async static Task AddToProcess(CusImageEntity cusImg)
        {
            if (cusImg.IsValidate)
            {
                var processEntity = new CusImageEntity()
                {
                    DtReceived = ServerTime.DateTime,
                    StrIndex = cusImg.StrIndex,
                    DtAdd = cusImg.DtAdd,
                    DtExpired = cusImg.DtExpired,
                    State = ProcessState.正在处理,
                    SiteFlag = cusImg.SiteFlag,
                    StrImg = cusImg.StrImg,
                    IsLogin = cusImg.IsLogin,
                    Error = (string.Format("接收线程ID：{0}\n", System.Threading.Thread.CurrentThread.ManagedThreadId))
                };
                //var processEntity = AddProcessQueue(cusImg);
                await Process(processEntity);
            }
        }

        private async static Task Process(CusImageEntity processEntity)
        {
            try
            {
                ////_Log.Info("开始处理…");
                ////DateTime begin = DateTime.Now;
                ////processEntity.DtStartProcess = ServerTime.DateTime;
                //ExecDaMa.BeginInvoke(processEntity, CallBack, processEntity);
                await GetCode(processEntity);
                //CacheHelper.ProcessQueue.AddOrUpdate(processEntity.StrIndex, processEntity);
                RdsCacheHelper.ProcessQueue.EnqueueCacheMessage(processEntity.StrIndex, processEntity.StrCode, ServerTime.DateTime.AddSeconds(10));
                processEntity.Error += (string.Format("最终线程ID：{0}\n", System.Threading.Thread.CurrentThread.ManagedThreadId));
                _Log.InfoFormat("【{5}】_{0}_总：{1}ms，消息：{2}ms，处理：{3}ms，[{6}_{7}]：{4}  {8}"
                    , processEntity.State.ToString()
                    , (ServerTime.DateTime - processEntity.DtAdd).TotalMilliseconds.ToString("F0")
                    , (processEntity.DtReceived - processEntity.DtAdd).TotalMilliseconds.ToString("F0")
                    //, (processEntity.DtStartProcess - processEntity.DtReceived).TotalMilliseconds.ToString("F0")
                    , (ServerTime.DateTime - processEntity.DtReceived).TotalMilliseconds.ToString("F0")
                    , processEntity.StrCode
                    , processEntity.SiteFlag
                    , processEntity.ResultFrom
                    , processEntity.IsLogin ? "0" : "1"
                    , processEntity.Error);
            }
            catch (ThreadAbortException ex)
            {
                //processEntity.Error = ex.Message;
                //processEntity.State = ProcessState.失败;
                Thread.ResetAbort();
            }
            catch (Exception oe)
            {
                _Log.Error("消息接收失败,错误原因如下:", oe);
                //processEntity.Error = oe.Message;
                //processEntity.State = ProcessState.失败;
            }
        }

        private async static Task GetCode(CusImageEntity processEntity)
        {
            //System.Threading.Thread.Sleep(rndTmp.Next(300, 3000));
            var byts = CommonCompress.GetByteFromBase64(processEntity.StrImg);
            var localCodeFrom = DaMaType.三六零;
            if (ZhuShouDaMaHelper.IsEnable && string.IsNullOrEmpty(processEntity.StrCode) && processEntity.IsValidate)
            {
                localCodeFrom = DaMaType.助手;
                //processEntity.StrCode = ZhiXingCodeNew.GetCode(byts);
                processEntity.StrCode = await Task.Run(() => ZhuShouDaMaHelper.GetCodeByBytes(byts, processEntity.IsLogin, processEntity.SiteFlag));
            }
            if (processEntity.IsLogin)
            {
                if (_360Code.Is360Enable && string.IsNullOrEmpty(processEntity.StrCode) && processEntity.IsValidate)
                {
                    localCodeFrom = DaMaType.三六零;
                    //processEntity.StrCode = _360Code.GetCode(byts, processEntity.IsLogin);
                    processEntity.StrCode = await Task.Run(() =>
                    {
                        processEntity.Error += (string.Format("处理线程ID：{0}\n", System.Threading.Thread.CurrentThread.ManagedThreadId));
                        return _360Code.GetCode(byts, processEntity.IsLogin);
                    });
                }
                if (DaShiCode.IsDaShiEnable && string.IsNullOrEmpty(processEntity.StrCode) && processEntity.IsValidate)
                {
                    localCodeFrom = DaMaType.大师;
                    processEntity.StrCode = await Task.Run(() => DaShiCode.GetCode(byts, processEntity.IsLogin));
                }
                if (ZhiXingCodeNew.IsEnable && string.IsNullOrEmpty(processEntity.StrCode) && processEntity.IsValidate)
                {
                    localCodeFrom = DaMaType.智行;
                    processEntity.StrCode = await Task.Run(() =>
                    {
                        processEntity.Error += (string.Format("处理线程ID：{0}\n", System.Threading.Thread.CurrentThread.ManagedThreadId));
                        return ZhiXingCodeNew.GetCode(byts);
                    });
                }
                if (QQCode.IsQQEnable && string.IsNullOrEmpty(processEntity.StrCode) && processEntity.IsValidate)
                {
                    localCodeFrom = DaMaType.QQ;
                    processEntity.StrCode = await Task.Run(() => QQCode.GetCode(byts, processEntity.IsLogin));
                }
                if (YunDaMaCode.IsYunDaMaEnable && string.IsNullOrEmpty(processEntity.StrCode) && processEntity.IsValidate)
                {
                    localCodeFrom = DaMaType.云打码;
                    processEntity.StrCode = await Task.Run(() => YunDaMaCode.GetCode(byts, processEntity.IsLogin));
                }
                if (OtherCode.IsEnable && string.IsNullOrEmpty(processEntity.StrCode) && processEntity.IsValidate)
                {
                    localCodeFrom = DaMaType.其他;
                    processEntity.StrCode = await Task.Run(() => OtherCode.GetCode(byts, processEntity.IsLogin));
                }
            }
            else
            {
                if (DaShiCode.IsDaShiEnable && string.IsNullOrEmpty(processEntity.StrCode) && processEntity.IsValidate)
                {
                    localCodeFrom = DaMaType.大师;
                    processEntity.StrCode = await Task.Run(() => DaShiCode.GetCode(byts, processEntity.IsLogin));
                }
                if (!string.IsNullOrEmpty(processEntity.StrCode) && processEntity.StrCode.Split(new string[] { "," }, StringSplitOptions.RemoveEmptyEntries).Length < 4)
                {
                    Console.WriteLine("下单打码失败，重新打码！");
                    processEntity.StrCode = "";
                }
                if (_360Code.Is360Enable && string.IsNullOrEmpty(processEntity.StrCode) && processEntity.IsValidate)
                {
                    localCodeFrom = DaMaType.三六零;
                    //processEntity.StrCode = _360Code.GetCode(byts, processEntity.IsLogin);
                    processEntity.StrCode = await Task.Run(() =>
                        {
                            processEntity.Error += (string.Format("处理线程ID：{0}\n", System.Threading.Thread.CurrentThread.ManagedThreadId));
                            return _360Code.GetCode(byts, processEntity.IsLogin);
                        });
                }
                if (!string.IsNullOrEmpty(processEntity.StrCode) && processEntity.StrCode.Split(new string[] { "," }, StringSplitOptions.RemoveEmptyEntries).Length < 4)
                {
                    Console.WriteLine("下单打码失败，重新打码！");
                    processEntity.StrCode = "";
                }
                if (ZhiXingCodeNew.IsEnable && string.IsNullOrEmpty(processEntity.StrCode) && processEntity.IsValidate)
                {
                    localCodeFrom = DaMaType.智行;
                    //processEntity.StrCode = ZhiXingCodeNew.GetCode(byts);
                    processEntity.StrCode = await Task.Run(() =>
                        {
                            processEntity.Error += (string.Format("处理线程ID：{0}\n", System.Threading.Thread.CurrentThread.ManagedThreadId));
                            return ZhiXingCodeNew.GetCode(byts);
                        });
                }
                if (!string.IsNullOrEmpty(processEntity.StrCode) && processEntity.StrCode.Split(new string[] { "," }, StringSplitOptions.RemoveEmptyEntries).Length < 4)
                {
                    Console.WriteLine("下单打码失败，重新打码！");
                    processEntity.StrCode = "";
                }
                if (QQCode.IsQQEnable && string.IsNullOrEmpty(processEntity.StrCode) && processEntity.IsValidate)
                {
                    localCodeFrom = DaMaType.QQ;
                    //processEntity.StrCode = _360Code.GetCode(byts, processEntity.IsLogin);
                    processEntity.StrCode = await Task.Run(() => QQCode.GetCode(byts, processEntity.IsLogin));
                }
                if (!string.IsNullOrEmpty(processEntity.StrCode) && processEntity.StrCode.Split(new string[] { "," }, StringSplitOptions.RemoveEmptyEntries).Length < 4)
                {
                    Console.WriteLine("下单打码失败，重新打码！");
                    processEntity.StrCode = "";
                }
                if (YunDaMaCode.IsYunDaMaEnable && string.IsNullOrEmpty(processEntity.StrCode) && processEntity.IsValidate)
                {
                    localCodeFrom = DaMaType.云打码;
                    //processEntity.StrCode = _360Code.GetCode(byts, processEntity.IsLogin);
                    processEntity.StrCode = await Task.Run(() => YunDaMaCode.GetCode(byts, processEntity.IsLogin));
                }
                //if (!string.IsNullOrEmpty(processEntity.StrCode) && processEntity.StrCode.Split(new string[] { "," }, StringSplitOptions.RemoveEmptyEntries).Length < 4)
                //{
                //    Console.WriteLine("下单打码失败，重新打码！");
                //    processEntity.StrCode = "";
                //}
                if (OtherCode.IsEnable && string.IsNullOrEmpty(processEntity.StrCode) && processEntity.IsValidate)
                {
                    localCodeFrom = DaMaType.其他;
                    //processEntity.StrCode = ZhiXingCodeNew.GetCode(byts);
                    processEntity.StrCode = await Task.Run(() => OtherCode.GetCode(byts, processEntity.IsLogin));
                }
            }
            if (JiSuDaMa.IsJiSu && string.IsNullOrEmpty(processEntity.StrCode) && processEntity.IsValidate)
            {
                localCodeFrom = DaMaType.极速;
                //processEntity.StrCode = JiSuDaMa.GetCode(byts);
                processEntity.StrCode = await Task.Run(() => JiSuDaMa.GetCode(byts));
            }
            //if (string.IsNullOrEmpty(processEntity.StrCode) && cusImg.IsValidate)
            //{
            //    localCodeFrom = DaMaType.三六零.ToString();
            //    //Console.WriteLine("360打码为空，尝试极速打码！");
            //    processEntity.StrCode = _360Code.GetCode(cusImg.StrImg, cusImg.IsLogin);
            //}
            byts = null;
            processEntity.ResultFrom = localCodeFrom.ToString();
            processEntity.State = string.IsNullOrEmpty(processEntity.StrCode) ? ProcessState.失败 : ProcessState.已完成;
        }

        private static void CallBack(IAsyncResult ar)
        {
            try
            {
                if (ar.IsCompleted)
                {
                    var processEntity = ar.AsyncState as CusImageEntity;
                    //CacheHelper.ProcessQueue.AddOrUpdate(processEntity.StrIndex, processEntity);
                    RdsCacheHelper.ProcessQueue.EnqueueCacheMessage(processEntity.StrIndex, processEntity.StrCode, ServerTime.DateTime.AddSeconds(10));
                    _Log.InfoFormat("【{5}_{7}】_{0}_总：{1}ms，消息：{2}ms，处理：{3}ms，[{6}]：{4}"
                        , processEntity.State.ToString()
                        , (ServerTime.DateTime - processEntity.DtAdd).TotalMilliseconds.ToString("F0")
                        , (processEntity.DtReceived - processEntity.DtAdd).TotalMilliseconds.ToString("F0")
                        //, (processEntity.DtStartProcess - processEntity.DtReceived).TotalMilliseconds.ToString("F0")
                        , (ServerTime.DateTime - processEntity.DtReceived).TotalMilliseconds.ToString("F0")
                        , processEntity.StrCode
                        , processEntity.SiteFlag
                        , processEntity.ResultFrom
                        , processEntity.IsLogin ? "0" : "1");
                    //, new TimeSpan(ServerTime.OffSet).TotalMilliseconds);
                }
            }
            catch (Exception oe)
            {
                _Log.Error("发送打码结果出错！", oe);
            }
        }

    }
    public delegate void GetCodeDele(CusImageEntity processEntity);
}
