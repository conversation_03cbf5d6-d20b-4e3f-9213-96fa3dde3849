﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Diagnostics;
using System.Runtime.InteropServices;
using System.Media;
using NewTicket.Properties;
using System.IO;
using System.Threading;
using System.Security.Cryptography;
using System.Web;
using System.Collections.Specialized;
using System.Threading.Tasks;
using System.Linq;
using System.Text.RegularExpressions;
using System.Globalization;
using System.Runtime.Serialization.Json;
using NewTicket.Common;
using NewTicket.MyControl;
using System.Security.Principal;
using System.Collections.Concurrent;
using System.Linq;

namespace NewTicket
{
    public class CommonMethod
    {
        public static string GetTimeStamp(bool bflag = false)
        {
            var timeSpan = DateTime.UtcNow - new DateTime(1970, 1, 1, 0, 0, 0, 0);
            var result = string.Empty;
            if (bflag)
            {
                result = Convert.ToInt64(timeSpan.TotalSeconds).ToString();
            }
            else
            {
                result = Convert.ToInt64(timeSpan.TotalMilliseconds).ToString();
            }
            return result;
        }

        public static bool IsHasProcess(string strProcessName, bool isKillAll = false)
        {
            bool result = false;
            try
            {
                Process[] processes = Process.GetProcessesByName(strProcessName);
                if (isKillAll || processes.Length > 1)
                {
                    foreach (var item in processes)
                    {
                        item.Kill();
                    }
                    result = false;
                }
                else
                {
                    if (processes.Length > 0)
                    {
                        result = true;
                    }
                }
                processes = null;
            }
            catch (Exception oe)
            {
                Log.WriteError("IsHasProcess出错", oe);
            }
            return result;
        }

        private static Stopwatch swTick = null;

        public static void StartProgram()
        {
            try
            {
                swTick = new Stopwatch();
                swTick.Start();
            }
            catch (Exception oe)
            {
                Log.WriteError(oe);
            }
        }

        public static int GetRunTime()
        {
            int nTime = 0;
            try
            {
                if (swTick == null)
                    StartProgram();
                nTime = swTick.Elapsed.Minutes;
            }
            catch (Exception oe)
            {
                Log.WriteError(oe);
            }
            return nTime;
        }

        #region 网络请求相关

        //private static List<CNNWebClient> lstCache = new List<CNNWebClient>();

        public static System.Collections.Concurrent.ConcurrentBag<CnnWebClient> lstCache = new System.Collections.Concurrent.ConcurrentBag<CnnWebClient>();

        public static CnnWebClient GetOneClient(bool is12306, bool isMobile, string strIP, bool isQuery = false)
        {
            try
            {
                CnnWebClient myClient = null;
                lock (lstCache)
                {
                    if (lstCache.Count > 0)//!isQuery &&
                    {
                        if (string.IsNullOrEmpty(strIP))
                            myClient = lstCache.OrderByDescending(p => p.LastUseTime).FirstOrDefault(p => p.Is12306 == is12306 && !p.IsUsed && !p.IsBusy && p.IsMobile == isMobile && string.IsNullOrEmpty(p.StrIpAddress));
                        else
                            myClient = lstCache.OrderByDescending(p => p.LastUseTime)
                                .FirstOrDefault(p => p.Is12306 == is12306 && !p.IsUsed && !p.IsBusy && isQuery == p.IsQuery
                                    && p.IsMobile == isMobile && strIP.Equals(p.StrIpAddress));
                    }
                    if (myClient == null)
                    {
                        myClient = new CnnWebClient()
                        {
                            IsMobile = isMobile,
                            IsQuery = isQuery,
                            Is12306 = is12306,
                            StrIpAddress = strIP,
                        };
                        if (!myClient.IsQuery)
                            lstCache.Add(myClient);
                    }
                }
                myClient.IsUsed = true;
                myClient.LastUseTime = CommonString.serverTime;
                return myClient;
            }
            catch (Exception oe)
            {
                Log.WriteError("GetOneClient出错", oe);
            }
            return GetOneClient(is12306, isMobile, strIP);
        }

        public static void InitNetPool(int count = 50)
        {
            for (int i = 0; i < count; i++)
            {
                lstCache.Add(new CnnWebClient());
            }
        }
        #endregion

        #region 主窗体消息展示相关

        public static List<ScrollEntity> GetSysMSG(string strMSG)
        {
            List<ScrollEntity> list = new List<ScrollEntity>();
            try
            {
                ScrollEntity entity = new ScrollEntity();
                entity.Text = strMSG;
                entity.ForeColor = System.Drawing.Color.Red;
                list.Add(entity);
            }
            catch (Exception oe)
            {
                Log.WriteError(oe);
            }
            return list;
        }

        private static string[] weekDays = { "日", "一", "二", "三", "四", "五", "六" };
        public static List<ScrollEntity> GetSysMSG(bool isHasUpdate = false)
        {
            List<ScrollEntity> list = new List<ScrollEntity>();
            try
            {
                string responseText = WebClientExt.GetHtml(CommonString.StrUpdateURL + "msgNew.txt?t=" + DateTime.Now.Ticks, (double)5);
                if (!string.IsNullOrEmpty(responseText))
                {
                    string[] array = responseText.Split(new string[] { "\n" }, StringSplitOptions.RemoveEmptyEntries);
                    if (array.Length > 0)
                    {
                        string[] ss = null;
                        foreach (string str in array)
                        {
                            ss = str.Trim().Split(new string[] { "|" }, StringSplitOptions.RemoveEmptyEntries);
                            if (ss != null && ss.Length > 0)
                            {
                                ScrollEntity entity = new ScrollEntity();
                                entity.Text = ss[0];
                                if (entity.Text.StartsWith("助手最新版【"))
                                {
                                    try
                                    {
                                        responseText = CommonMethod.SubString(entity.Text, "助手最新版【", "】");
                                        DateTime dtTmp = CommonString.dtNowDate;
                                        if (DateTime.TryParse(responseText, out dtTmp))
                                        {
                                            if (dtTmp <= CommonString.dtNowDate)
                                            {
                                                continue;
                                            }
                                        }
                                    }
                                    catch { }
                                }
                                try
                                {
                                    entity.ForeColor = System.Drawing.Color.FromName(ss.Length > 1 ? ss[1] : "black");
                                }
                                catch
                                {
                                    entity.ForeColor = System.Drawing.Color.Black;
                                }
                                entity.LnkURL = ss.Length > 2 ? ss[2] : "";
                                list.Add(entity);
                            }
                        }
                        ss = null;
                    }
                    responseText = "";
                    array = null;
                }
            }
            catch (Exception oe)
            {
                Log.WriteError(oe);
            }
            list.Insert(0, new ScrollEntity()
            {
                ForeColor = System.Drawing.Color.Blue,
                Text = string.Format("今天是{0} 星期{1}，可订【{2}({3})】的车票，祝您生活愉快！"
                , CommonString.serverTime.ToString("yyyy年MM月dd日"), weekDays[(int)CommonString.serverTime.DayOfWeek]
                , CommonString.serverTime.Date.AddDays(CommonMethod.GetYuShouDate(CommonString.serverTime) - 1).ToString("yyyy年MM月dd日")
                , ChinaDate.GetNongLiDate(CommonString.serverTime.Date.AddDays(CommonMethod.GetYuShouDate(CommonString.serverTime) - 1)))
            });
            if (CommonString.IsBeta)
            {
                list.Insert(0, new ScrollEntity()
                {
                    ForeColor = System.Drawing.Color.Red,
                    Text = "您使用的是Beta版本，如果发现问题，请第一时间与客服联系，谢谢！"
                });
            }
            if (isHasUpdate)
            {
                list.Insert(0, new ScrollEntity()
                {
                    ForeColor = System.Drawing.Color.OrangeRed,
                    Text = string.Format("发现助手有新版本可以更新，请及时更新！")
                });
            }
            return list;
        }

        public static string GetHello()
        {
            string result = string.Empty;
            int hour = DateTime.Now.Hour;
            if (hour < 9)
            {
                result = "早上好";
            }
            else if (hour < 12)
            {
                result = "上午好";
            }
            else if (hour < 14)
            {
                result = "中午好";
            }
            else if (hour < 17)
            {
                result = "下午好";
            }
            else if (hour < 22)
            {
                result = "晚上好";
            }
            else
            {
                result = "夜深了，洗洗睡吧";
            }
            return result;
        }

        #endregion

        #region 站点可用性相关

        public static string ChangeSiteByType(SiteType type, string nowSite)
        {
            string result = nowSite;
            int index = -1;
            try
            {
                index = CommonString.LstServerURL.IndexOf(nowSite.Replace(type == SiteType.account ? CommonString.StrAccountType : type.ToString(), "{0}"));
                index++;
                index = index < 0 ? 0 : index;
                index = index >= CommonString.LstServerURL.Count ? 0 : index;
                result = string.Format(CommonString.LstServerURL[index], type == SiteType.account ? CommonString.StrAccountType : type.ToString());
            }
            catch { }
            if (string.IsNullOrEmpty(result))
                result = nowSite;
            try
            {
                switch (type)
                {
                    case SiteType.account:
                        CommonString.HostAccountURL = result;
                        break;
                    //case SiteType.email:
                    //    CommonString.HostTicketEmailURL = result;
                    //    break;
                    case SiteType.tt:
                        CommonString.HostTicketURL = result;
                        break;
                    case SiteType.zs:
                        CommonString.HostQueryURL = result;
                        break;
                    case SiteType.bug:
                        CommonString.HostBugReportURL = result;
                        break;
                }
            }
            catch { }
            return result;
        }

        public static bool isSiteCanUse(string strSiteUrl)
        {
            bool result = false;
            try
            {
                string strTmp = WebClientExt.GetHtml(strSiteUrl.TrimEnd('/') + "/pingNew.txt", 3);
                result = !string.IsNullOrEmpty(strTmp) && strTmp.HorspoolIndex("ok") >= 0;
            }
            catch (Exception oe)
            {
                Log.WriteError("isSiteCanUse出错", oe);
            }
            return result;
        }

        //public static bool isUpdateCanUse()
        //{
        //    bool result = false;
        //    try
        //    {
        //        string strTmp = WebClientExt.GetHtml(CommonString.StrUpdateURL + CommonString.UpdateFileName, (double)5);
        //        if (string.IsNullOrEmpty(strTmp))
        //            strTmp = WebClientExt.GetHtml(CommonString.strBakUpdateURL + CommonString.UpdateFileName, (double)5);
        //        result = !string.IsNullOrEmpty(strTmp) && strTmp.HorspoolIndex("strNowVersion") >= 0;
        //    }
        //    catch (Exception oe)
        //    {
        //        Log.WriteError("isUpdateCanUse出错", oe);
        //    }
        //    return result;
        //}

        public static string GetSubStrByURL(string strURL)
        {
            string strTmp = "";
            if (!string.IsNullOrEmpty(strURL))
            {
                try
                {
                    if (strURL.Contains("&"))
                    {
                        strTmp = SubString(strURL, "", "&");
                        string strEncrpt = SubString(strURL, "&");
                        strEncrpt = CommonEncryptHelper.DESEncrypt(strEncrpt, CommonString.StrCommonEncryptKey);
                        //strURL = CommonEncryptHelper.DESDecrypt(strTmp, CommonString.StrCommonEncryptKey);
                        strTmp += "&con=" + System.Web.HttpUtility.UrlEncode(strEncrpt);
                    }
                    else
                    {
                        strTmp = strURL;
                    }
                }
                catch (Exception oe)
                {
                    strTmp = strURL;
                    Log.WriteError("GetSubStrByURL出错", oe);
                }
            }
            return strTmp;
        }

        //public static bool isMobileServiceCanUse()
        //{
        //    bool result = false;
        //    try
        //    {
        //        for (int i = 0; i < 3; i++)
        //        {
        //            result = GetCheckCode() != null;
        //            if (result)
        //            {
        //                break;
        //            }
        //        }
        //    }
        //    catch (Exception oe)
        //    {
        //        if (CommonString.isDebug)
        //        {
        //            Log.WriteError(oe);
        //        }
        //    }
        //    return result;
        //}

        private static List<CheckCode> lstCheckCode = null;
        //public static string StrCommonDeviceNO = "99B965E3-FF19-4ACB-BBC7-0AA69F66B81E";

        public static CheckCode GetCheckCode()
        {
            CheckCode result = null;
            lstCheckCode = new List<CheckCode>()
            {
                //new CheckCode() 
                //{ 
                //     Code = "FmfiM1YYVqI2Vjq2crUjqVEJeAdFmK0RgYryQ/TtIHnil9hs5XRVmS+dINuo4xAd8OkFXUkR49UQBUJX17oOe2nUmsAJIS1ILwGYmTsdf+P4XMAq0SgwPxoYGHJcqRDZJocOOMHghJbAcr7x0uEHoxojjnrQwLFT50Iu5bXHWIBqBZGKwwY+y886bYLQSp3yTOjm+EUu0T2pwq8zuSasBWw7Pkdz6dEA1rez8bTtpVUVSLYidIOwqHgF9u5DyC3wuQ2HYrKJR55dnKU5V/x3u6A==",
                //     Date=CommonString.serverTime,
                //     DeviceNo=""
                //}
            };
            //if (lstCheckCode == null)
            //{
            //    lstCheckCode = new List<CheckCode>();
            //}
            //try
            //{
            //    lstCheckCode.RemoveAll(p => p.Date <= CommonString.serverTime.AddMinutes(-3));
            //}
            //catch { }
            //try
            //{
            //    if (lstCheckCode.Count <= 0)
            //    {
            //        for (int i = 0; i < 3; i++)
            //        {
            //            LoadCheckCodeByServer();
            //            if (lstCheckCode.Count <= 0)
            //            {
            //                System.Threading.Thread.Sleep(5000);
            //            }
            //            else
            //            {
            //                break;
            //            }
            //        }
            //    }
            //    if (lstCheckCode.Count <= 0 && CommonString.IsServiceCheckCodeCanUse)
            //    {
            //        var tmp = CommonMethod.LoadCheckCodeByOtherServer();
            //        if (tmp != null && !string.IsNullOrEmpty(tmp.Code))
            //        {
            //            if (!lstCheckCode.Exists(p => p.Date.ToString("yyyyMMddHHmmss").Equals(tmp.Date.ToString("yyyyMMddHHmmss"))))
            //            {
            //                lstCheckCode.Add(tmp);
            //            }
            //        }
            //    }
            //}
            //catch (Exception oe)
            //{
            //    Log.WriteError(oe);
            //}
            try
            {
                lstCheckCode.RemoveAll(p => p == null || p.Date <= CommonString.serverTime.AddMinutes(-3));
            }
            catch { }
            if (lstCheckCode != null && lstCheckCode.Count > 0)
            {
                var tmp = lstCheckCode.FindAll(p => p.Date >= CommonString.serverTime.AddMinutes(-1) && p.Date <= CommonString.serverTime.AddMinutes(1));
                //tmp = lstCheckCode.FindAll(p => p.Key >= CommonString.serverTime.AddMinutes(-2) && p.Key <= CommonString.serverTime);
                if (tmp != null && tmp.Count > 0 && !string.IsNullOrEmpty(tmp[tmp.Count - 1].Code))
                {
                    result = tmp[tmp.Count - 1];
                    tmp = null;
                }
            }
            return result;
        }

        public static string GetDeviceNo()
        {
            string result = "";
            try
            {
                var tmp = GetCheckCode();
                if (tmp != null && !string.IsNullOrEmpty(tmp.DeviceNo))
                {
                    result = tmp.DeviceNo;
                }
            }
            catch { }
            if (string.IsNullOrEmpty(result))
            {
                if (lstCheckCode != null && lstCheckCode.Count > 0)
                {
                    result = lstCheckCode[0].DeviceNo;
                }
            }
            if (string.IsNullOrEmpty(result))
            {
                result = "111628D76-7474-366A-031D-0CCA1410631";// "99B965E3-FF19-4ACB-BBC7-0AA69F66B81E";
            }
            return result;
        }

        public static void CheckFrameWork()
        {
            if (IsNet45OrNewer())
            {
                var strTmp = CommonMethod.ExecCmd("dir %WINDIR%\\Microsoft.Net\\Framework\\v*");

                if (!strTmp.Contains("v4.0.30319"))
                {
                    System.Windows.Forms.MessageBox.Show("检测到你安装了高版本的.Net，与当前软件运行环境不兼容!\n请打开控制面板=》添加删除程序,卸载本机已安装的.Net\n卸载完成后，安装助手运行环境.Net Framework v4.0.30319\n点确定后打开正确版本的下载地址！"
                        , "抢票助手", System.Windows.Forms.MessageBoxButtons.OK, System.Windows.Forms.MessageBoxIcon.Error);
                    Process.Start("http://download.microsoft.com/download/9/5/A/95A9616B-7A37-4AF6-BC36-D6EA96C8DAAE/dotNetFx40_Full_x86_x64.exe");
                }
                else
                {
                    System.Windows.Forms.MessageBox.Show("助手检测到你安装了多个版本的.Net Framework！\n会造成助手运行不稳定，请打开控制面板=》添加删除程序\n卸载除了.Net Framework v4.0.30319的所有.net版本！"
                        , "抢票助手", System.Windows.Forms.MessageBoxButtons.OK, System.Windows.Forms.MessageBoxIcon.Error);
                }
            }
        }

        private static bool IsNet45OrNewer()
        {
            return Type.GetType("System.Reflection.ReflectionContext", false) != null;
        }

        public static bool isCheckCodeCanUse()
        {
            return false;
            if (lstCheckCode == null || lstCheckCode.Count <= 0)
            {
                GetCheckCode();
            }
            return lstCheckCode != null && lstCheckCode.Count > 0;
        }

        public static CheckCode LoadCheckCodeByOtherServer()
        {
            CheckCode result = null;
            try
            {
                string strTmp = WebClientExt.GetHtml("https://58.61.45.98:1144/otsmobile/domycode", "", "", "", 3, 5);
                if (!string.IsNullOrEmpty(strTmp))
                {
                    //https://58.61.45.98:1144/otsmobile/domycode
                    //668ddede25a14fc2,20150428172711,8e7589054973f01ec68ed0b8cb32370f
                    var codes = strTmp.Split(new string[] { "," }, StringSplitOptions.RemoveEmptyEntries);
                    if (codes != null && codes.Length == 3)
                    {
                        result = new CheckCode();
                        result.Code = codes[2];
                        result.DeviceNo = codes[0];
                        result.Date = DateTime.ParseExact(codes[1], "yyyyMMddHHmmss", null);
                    }
                }
            }
            catch (Exception oe)
            {
                Log.WriteError(oe);
            }
            return result;
        }

        private static bool isFirstLoadCode = true;

        //private static void LoadCheckCodeByServer(string strFile = "")
        //{
        //    try
        //    {
        //        lstCheckCode = new List<CheckCode>();

        //        //return;

        //        if (string.IsNullOrEmpty(strFile))
        //            strFile = Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData).TrimEnd('\\') + "\\" + System.Web.Security.FormsAuthentication.HashPasswordForStoringInConfigFile(CommonString.serverTime.ToString("yyyyMMdd"), "MD5") + ".sconfig";
        //        string html = "";
        //        string strNowKey = CommonEncryptHelper.DESEncrypt(CommonReg.StrMachineKey, CommonString.StrCommonEncryptKey).TrimEnd('=');
        //        try
        //        {
        //            string[] strFiles = Directory.GetFiles(Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData), "*.sconfig", SearchOption.TopDirectoryOnly);
        //            if (strFiles != null && strFiles.Length > 0)
        //            {
        //                foreach (var item in strFiles)
        //                {
        //                    if (item.Equals(strFile))
        //                        continue;
        //                    try
        //                    {
        //                        File.Delete(item);
        //                    }
        //                    catch { }
        //                }
        //            }
        //        }
        //        catch { }
        //        try
        //        {
        //            if (!string.IsNullOrEmpty(strFile) && File.Exists(strFile))
        //            {
        //                html = File.ReadAllText(strFile);
        //                if (!html.StartsWith(strNowKey))
        //                {
        //                    File.Delete(strFile);
        //                    html = "";
        //                }
        //                else
        //                {
        //                    html = CommonMethod.SubString(html, strNowKey);
        //                }
        //            }
        //        }
        //        catch { }

        //        string url = string.Format("ticket.aspx?op=code&app={0}&mac={1}&reg={2}&ver={3}&user={4}&info={5}"
        //            , CommonReg.StrMachineKey, Environment.UserDomainName
        //            , CommonReg.DtRegTime.ToString("yyyy-MM-dd HH:mm:ss"), CommonString.dtNowDate.ToString("yyyy-MM-dd HH:mm:ss")
        //            , Environment.UserName, LocalHelper.GetAllUserInfo());
        //        if (string.IsNullOrEmpty(html))
        //            html = CommonMethod.GetServerHtml(url, SiteType.account, CommonString.HostAccountURL);

        //        if (string.IsNullOrEmpty(html))
        //            return;
        //        try
        //        {
        //            if (!string.IsNullOrEmpty(strFile) && !File.Exists(strFile))
        //            {
        //                File.WriteAllText(strFile, strNowKey + html);
        //            }
        //        }
        //        catch { }
        //        html = CommonEncryptHelper.DESDecrypt(html, CommonString.StrCommonEncryptKey);
        //        html = CommonMethod.Decrypto(html);
        //        if (string.IsNullOrEmpty(html))
        //        {
        //            try
        //            {
        //                if (!string.IsNullOrEmpty(strFile) && File.Exists(strFile))
        //                {
        //                    File.Delete(strFile);
        //                }
        //            }
        //            catch { }
        //        }
        //        string deviceNo = "";
        //        if (html.Contains("Key:"))
        //        {
        //            deviceNo = CommonMethod.SubString(html, "Key:", "|");
        //            html = CommonMethod.SubString(html, "Codes:[", "]");
        //        }
        //        string[] array = html.Trim().Split(new string[] { "|" }, StringSplitOptions.RemoveEmptyEntries);
        //        if (array != null && array.Length > 0)
        //        {
        //            bool isNotCheckExits = isFirstLoadCode;
        //            if (isFirstLoadCode)
        //                isFirstLoadCode = false;
        //            string strKey = "";
        //            //DateTime dtTmp = DateTime.MinValue;
        //            foreach (string str in array)
        //            {
        //                if (!string.IsNullOrEmpty(str.Trim()))
        //                {
        //                    html = str.Trim();
        //                    if (html.Contains("-"))
        //                    {
        //                        strKey = SubString(html, "", "-").Trim();
        //                        html = SubString(html, "-").Trim();
        //                        if (lstCheckCode == null)
        //                        {
        //                            lstCheckCode = new List<CheckCode>();
        //                        }
        //                        if (isNotCheckExits || !lstCheckCode.Exists(p => p.Date.ToString("yyyyMMddHHmmss").Equals(strKey)))
        //                        {
        //                            lstCheckCode.Add(new CheckCode()
        //                            {
        //                                Date = DateTime.ParseExact(strKey, "yyyyMMddHHmmss", null),
        //                                Code = html,
        //                                DeviceNo = deviceNo
        //                            });
        //                        }
        //                    }
        //                }
        //            }
        //        }
        //    }
        //    catch (Exception oe)
        //    {
        //        Log.WriteError(oe);
        //    }
        //}

        //public static bool isWebSiteCanUse(string strTmp = "")
        //{
        //    bool result = false;
        //    try
        //    {
        //        if (string.IsNullOrEmpty(strTmp))
        //        {
        //            strTmp = CommonString.HostAccountURL;
        //        }
        //        for (int i = 0; i < 3; i++)
        //        {
        //            result = isSiteCanUse(strTmp);
        //            if (!result)
        //            {
        //                if (string.IsNullOrEmpty(strTmp))
        //                    ChangeSiteByType(SiteType.account, CommonString.HostAccountURL);
        //            }
        //            else
        //                break;
        //        }
        //    }
        //    catch (Exception oe)
        //    {
        //        Log.WriteError("isWebSiteCanUse出错", oe);
        //    }
        //    return result;
        //}

        static List<string> lstTmpSubURL = new List<string>();

        public static string GetServerHtml(string url, SiteType type, string strNowSite, bool isDecodeURL = true, bool isPost = false, string strPost = "", int retryCount = 3)
        {
            string html = "";
            try
            {
                for (int i = 0; i < CommonString.LstServerURL.Count; i++)
                {
                    if (isDecodeURL)
                    {
                        if (isPost)
                            html = WebClientExt.GetHtml(strNowSite + GetSubStrByURL(url), "", DnsHelper.GetDnsIp(strNowSite), strPost, retryCount, 10);
                        else
                            html = WebClientExt.GetHtml(strNowSite + GetSubStrByURL(url), "", DnsHelper.GetDnsIp(strNowSite), "", retryCount, 3);
                    }
                    else
                    {
                        if (isPost)
                            html = WebClientExt.GetHtml(strNowSite + url, "", DnsHelper.GetDnsIp(strNowSite), strPost, retryCount, 10);
                        else
                            html = WebClientExt.GetHtml(strNowSite + url, "", DnsHelper.GetDnsIp(strNowSite), "", retryCount, 3);
                    }
                    if (!string.IsNullOrEmpty(html))
                    {
                        if (!html.Contains("<html>") && !html.Contains("<script"))
                        {
                            if (html.Trim().Equals("close"))
                            {
                                CommonReg.SetNoReg(true, true);
                            }
                            break;
                        }
                    }
                    //if (string.IsNullOrEmpty(html) && type != SiteType.code)
                    //{
                    //    strNowSite = CommonMethod.ChangeSiteByType(type, strNowSite);
                    //}
                    //else
                    //{
                    //    if (html.Contains("<html>") && type != SiteType.code)
                    //    {
                    //        strNowSite = CommonMethod.ChangeSiteByType(type, strNowSite);
                    //    }
                    //    else
                    //    {
                    //        if (html.Trim().Equals("close"))
                    //        {
                    //            CommonReg.SetNoReg(true, true);
                    //        }
                    //        break;
                    //    }
                    //}
                }
            }
            catch (Exception oe)
            {
                Log.WriteError("GetServerHtml出错", oe);
            }
            return html;
        }

        public static string GetServerHtmlByUrl(string url, string strNowSite, bool isDecodeURL = true)
        {
            string html = "";
            try
            {
                if (isDecodeURL)
                    html = WebClientExt.GetHtml(strNowSite + GetSubStrByURL(url), "", DnsHelper.GetDnsIp(strNowSite), "", 1, 3);
                else
                    html = WebClientExt.GetHtml(strNowSite + url, "", DnsHelper.GetDnsIp(strNowSite), "", 1, 3);
                if (!string.IsNullOrEmpty(html))
                {
                    if (html.Trim().Equals("close"))
                    {
                        CommonReg.SetNoReg(true, true);
                    }
                }
            }
            catch (Exception oe)
            {
                Log.WriteError(oe);
            }
            return html;
        }

        public static void GetQueryTicketURLNew()
        {
            string strTmp = "";
            try
            {
                bool result = false;
                var cookie = GetCompleteCookie(false, GetRandomQueryCookie(), null);
                strTmp = WebClientExt.GetHtml(CommonString.QueryTicketURL + "?leftTicketDTO.train_date=2018-11-26&leftTicketDTO.from_station=BJP&leftTicketDTO.to_station=SHH&purpose_codes=ADULT", cookie, CommonString.StrLocalComIP, "", 3, 5);
                if (!string.IsNullOrEmpty(strTmp))
                {
                    if (strTmp.HorspoolIndex("c_url\"") >= 0)
                    {
                        strTmp = CommonMethod.SubString(strTmp, "c_url\":\"", "\"");
                        if (!string.IsNullOrEmpty(strTmp))
                        {
                            result = true;
                            CommonString.QueryTicketURL = "https://kyfw.12306.cn/otn/" + strTmp;
                        }

                    }
                    else if (strTmp.Contains("messages\":[\"网络繁忙\"]"))
                    {
                        IPHelper.AutoChangeIP(false, CommonString.StrLocalComIP);
                    }
                }
                //if (!result)
                //{
                //    strTmp = WebClientExt.GetHtml("http://trip.163.com/m/huoche/get12306queryx.do", "", "", "", 3, 5).Trim();
                //    if (!string.IsNullOrEmpty(strTmp) && strTmp.Contains("leftTicket/"))
                //    {
                //        CommonString.QueryTicketURL = strTmp;
                //    }
                //}
            }
            catch (Exception oe)
            {
                Log.WriteError(oe);
            }
            finally
            {
                strTmp = null;
            }
        }

        public static void GetQueryTicketURL()
        {
            string strTmp = "";
            try
            {
                var cookie = GetCompleteCookie(false, GetRandomQueryCookie(), null);
                strTmp = WebClientExt.GetHtml(CommonString.QueryTicketInitUrl, cookie, "", "", 3, 5);//CommonString.StrLocalComIP
                if (!string.IsNullOrEmpty(strTmp))
                {
                    if (strTmp.Contains("网络繁忙"))
                    {
                        if (Settings.Default.IsAutoChangIP)
                        {
                            IPHelper.ChangIP();
                        }
                        else
                            IPHelper.AutoChangeIP(false, CommonString.StrLocalComIP);
                        return;
                    }
                    if (strTmp.Contains("var other_buy_date"))
                    {
                        CommonString.NYuShouDate = DateTimeUtil.GetYuShouDate(strTmp);
                        //CommonMethod.GetYuShouDate(CommonString.serverTime, true);
                    }
                    if (strTmp.HorspoolIndex("CLeftTicketUrl = '") >= 0)
                    {
                        strTmp = CommonMethod.SubString(strTmp, "CLeftTicketUrl = '");
                        string strQuery = CommonMethod.SubString(strTmp, "", "'");
                        if (!string.IsNullOrEmpty(strQuery))
                        {
                            CommonString.QueryTicketURL = "https://kyfw.12306.cn/otn/" + strQuery;
                        }
                        string strDate = CommonMethod.SubString(strTmp, "var studentMaxdate");
                        strDate = CommonMethod.SubString(strDate, "'", "'");
                        if (!string.IsNullOrEmpty(strDate))
                        {
                            if (BoxUtil.GetDateTimeFromObject(strDate) > MaxDate)
                            {
                                MaxDate = BoxUtil.GetDateTimeFromObject(strDate);
                            }
                        }
                        strDate = CommonMethod.SubString(strTmp, "var otherMaxdate");
                        strDate = CommonMethod.SubString(strDate, "'", "'");
                        if (!string.IsNullOrEmpty(strDate))
                        {
                            if (BoxUtil.GetDateTimeFromObject(strDate) > MaxDate)
                            {
                                MaxDate = BoxUtil.GetDateTimeFromObject(strDate);
                            }
                        }
                        strDate = "";

                    }
                }
            }
            catch (Exception oe)
            {
                Log.WriteError(oe);
            }
            finally
            {
                strTmp = null;
            }
        }

        #endregion

        #region 席别相关
        public static List<string> GetGroupByValue(string strName)
        {
            if (CommonString.lstGroupStation == null || CommonString.lstGroupStation.Count <= 0)
                initGroupStation(false);
            KeyValuePair<string, List<string>> key
                = CommonString.lstGroupStation.Find(pp => pp.Value != null && pp.Value.Count > 0
                    && (pp.Key.Contains(strName) || pp.Value.Contains(strName)));
            return string.IsNullOrEmpty(key.Key) ? new List<string>() : key.Value;
        }

        //public static List<KeyValueEntity> GetGroupByValue(string strName)
        //{
        //    if (CommonString.lstGroupStation == null || CommonString.lstGroupStation.Count <= 0)
        //        initGroupStation();
        //    KeyValuePair<string, List<KeyValueEntity>> key
        //        = CommonString.lstGroupStation.Find(pp => pp.Value != null
        //            && pp.Value.Exists(t => t.Value.Equals(strName) || t.Key.Equals(strName)));
        //    return string.IsNullOrEmpty(key.Key) ? new List<KeyValueEntity>() : key.Value;
        //}

        public static string GetStationByValue(string strName)
        {
            var items = CommonString.Stations.FirstOrDefault(v => v.Code.Equals(strName, StringComparison.OrdinalIgnoreCase));
            return items != null ? items.Name : "";
        }

        public static System.Web.Script.Serialization.JavaScriptSerializer JavaScriptSerializer = new System.Web.Script.Serialization.JavaScriptSerializer();

        public static List<string> GetAllSeatByUser(List<Passenger> lstTMPPassage)
        {
            List<string> lstNewType = new List<string>();
            try
            {
                if (lstTMPPassage != null && lstTMPPassage.Count > 0)
                {
                    lstTMPPassage.ForEach(delegate(Passenger pss)
                    {
                        if (pss != null && pss.LstSeatType != null)
                        {
                            foreach (var item in pss.LstSeatType)
                            {
                                if (!lstNewType.Contains(item.Key))
                                {
                                    lstNewType.Add(item.Key);
                                }
                            }
                        }
                    });
                }
            }
            catch (Exception oe)
            {
                Log.WriteError("GetAllSeatByUser出错", oe);
            }
            return lstNewType;
        }

        public static string GetSeatTypeByCode(string strCode, string TrainNo)
        {
            string strResult = CommonMethod.GetValueFromSeatTypeByKey(strCode);
            if (string.IsNullOrEmpty(strResult) && !string.IsNullOrEmpty(TrainNo))
            {
                strResult = TrainNo.StartsWith("C") || TrainNo.StartsWith("D") || TrainNo.StartsWith("G") ? "O" : "1";
            }
            return strResult;
        }

        //public static bool Is

        public static string GetValueFromSeatTypeByKey(string key)
        {
            string strTmp = "";
            try
            {
                strTmp = CommonString.SeatTypes.Find(kk => kk.Key == key || kk.Value == key).Value;
            }
            catch { }
            return strTmp;
        }

        public static List<AttentionItem> GetLstByIndex(List<AttentionItem> lstAll, int index, int maxCount = 1)
        {
            maxCount = maxCount < 1 ? 1 : maxCount;
            index = index > maxCount ? maxCount : index;
            var lst = new List<AttentionItem>();
            if (lstAll != null && lstAll.Count > 0)
            {
                double percent = index * 1.0 / (maxCount - index);

                if (percent == 0)
                {
                    foreach (var item in lstAll)
                    {
                        lst.Add(item.Copy());
                    }
                }
                else if (percent > 0 && percent < 1)
                {
                    //1/3,从0.3处开始
                    //2/3,从0.6处开始
                    //3/3，从1开始
                    int nstartIndex = (int)(percent * lstAll.Count);
                    var lstTmp = new List<AttentionItem>();
                    lstTmp.AddRange(lstAll.GetRange(nstartIndex, lstAll.Count - nstartIndex));
                    lstTmp.AddRange(lstAll.GetRange(0, nstartIndex));
                    foreach (var item in lstTmp)
                    {
                        lst.Add(item.Copy());
                    }
                }
                else
                {
                    for (int i = lstAll.Count - 1; i >= 0; i--)
                    {
                        lst.Add(lstAll[i].Copy());
                    }
                }
            }
            return lst;
        }
        #endregion

        #region 共享票相关


        public static bool IsHasCommonTicket(string trainNo, string dates, string froms, string tos, bool isMobile = false)
        {
            bool result = false;
            try
            {
                result = GetCommonTicket(trainNo, dates, froms, tos, isMobile) != null;
            }
            catch (Exception oe)
            {
                Log.WriteError("IsHasCommonTicket出错", oe);
            }
            return result;
        }

        public static void ClearBadTickets()
        {
            Task.Factory.StartNew(() =>
            {
                while (!CommonString.isExit)
                {
                    System.Threading.Thread.Sleep(1000);
                    ClearBadTicket();
                }
            });
        }

        private static void ClearBadTicket()
        {
            var lstBad = new List<string>();
            try
            {
                if (CommonString.IsComCanUse && CommonString.CommonComTicket != null)
                {
                    foreach (var item in CommonString.CommonComTicket)
                    {
                        if (item.Value != null && item.Value.dtTicket.AddMinutes(3) <= CommonString.serverTime)
                        {
                            lstBad.Add(item.Key);
                        }
                    }
                    if (lstBad.Count > 0)
                    {
                        TicketEntity ticTmp = null;
                        foreach (var item in lstBad)
                        {
                            CommonString.CommonComTicket.TryRemove(item, out ticTmp);
                        }
                    }
                }
            }
            catch (Exception oe)
            {
                //Log.WriteError(oe);
            }
            lstBad = new List<string>();
            try
            {
                if (CommonString.IsMobileCanUse && CommonString.CommonMobileTicket != null)
                {
                    foreach (var item in CommonString.CommonMobileTicket)
                    {
                        if (item.Value != null && item.Value.dtTicket.AddMinutes(3) <= CommonString.serverTime)
                        {
                            lstBad.Add(item.Key);
                        }
                    }
                    if (lstBad.Count > 0)
                    {
                        TicketEntity ticTmp = null;
                        foreach (var item in lstBad)
                        {
                            CommonString.CommonMobileTicket.TryRemove(item, out ticTmp);
                        }
                    }
                }
            }
            catch (Exception oe)
            {
                //Log.WriteError(oe);
            }
            lstBad = null;
        }

        private static TicketEntity GetCommonTicketBySingleNo(string item, bool isMobile = false)
        {
            TicketEntity tic = null;

            if (isMobile)
            {
                if (CommonString.CommonMobileTicket.ContainsKey(item)
                    && CommonString.CommonMobileTicket[item] != null
                    && CommonString.CommonMobileTicket[item].CanSub
                    && CommonString.CommonMobileTicket[item].dtTicket.AddMinutes(3) >= CommonString.serverTime)
                {
                    tic = (TicketEntity)CommonString.CommonMobileTicket[item].Clone();
                }
            }
            else
            {
                if (CommonString.CommonComTicket.ContainsKey(item)
                    && CommonString.CommonComTicket[item] != null
                    && CommonString.CommonComTicket[item].CanSub
                    && CommonString.CommonComTicket[item].dtTicket.AddMinutes(3) >= CommonString.serverTime)
                {
                    tic = (TicketEntity)CommonString.CommonComTicket[item].Clone();
                }
            }
            return tic;
        }

        public static TicketEntity GetCommonTicket(string trainNo, string dates, string froms, string tos, bool isMobile = false)
        {
            TicketEntity tic = null;
            if ((isMobile && CommonString.CommonMobileTicket.Count > 0) || (!isMobile && CommonString.CommonComTicket.Count > 0))
            {
                string[] strTNo = trainNo.Split(new string[] { "," }, StringSplitOptions.RemoveEmptyEntries);
                string[] strTDate = dates.Split(new string[] { "," }, StringSplitOptions.RemoveEmptyEntries);
                string[] strTFrom = froms.Split(new string[] { "," }, StringSplitOptions.RemoveEmptyEntries);
                string[] strTTo = tos.Split(new string[] { "," }, StringSplitOptions.RemoveEmptyEntries);
                //var trainNo = ticket.TrainNo + "-" + ticket.Date.ToString("yyyy-MM-dd") + "-" + ticket.FromSationName + "-" + ticket.ToStationName;
                if (strTNo != null && strTNo.Length > 0 && strTDate != null && strTDate.Length > 0
                    && strTFrom != null && strTFrom.Length > 0 && strTTo != null && strTTo.Length > 0)
                {
                    foreach (var no in strTNo)
                    {
                        if (tic != null)
                        {
                            break;
                        }
                        foreach (var date in strTDate)
                        {
                            if (tic != null)
                            {
                                break;
                            }
                            foreach (var from in strTFrom)
                            {
                                if (tic != null)
                                {
                                    break;
                                }
                                foreach (var to in strTTo)
                                {
                                    tic = GetCommonTicketBySingleNo(no + "-" + date + "_" + from + "_" + to, isMobile);
                                    if (tic != null)
                                    {
                                        break;
                                    }
                                }
                            }
                        }
                    }
                }
                strTNo = null;
                strTDate = null;
                strTFrom = null;
                strTTo = null;
            }
            return tic;
        }

        public static void ClearCommonTicket(string trainNo, string dates, string froms, string tos, bool isClearAll = false)
        {
            if (string.IsNullOrEmpty(trainNo) || string.IsNullOrEmpty(dates))
                isClearAll = true;
            if (isClearAll)
            {
                CommonString.CommonComTicket = new ConcurrentDictionary<string, TicketEntity>();
                CommonString.CommonMobileTicket = new ConcurrentDictionary<string, TicketEntity>();
            }
            else
            {
                string[] strTNo = trainNo.Split(new string[] { "," }, StringSplitOptions.RemoveEmptyEntries);
                string[] strTDate = dates.Split(new string[] { "," }, StringSplitOptions.RemoveEmptyEntries);
                string[] strTFrom = froms.Split(new string[] { "," }, StringSplitOptions.RemoveEmptyEntries);
                string[] strTTo = tos.Split(new string[] { "," }, StringSplitOptions.RemoveEmptyEntries);
                if (strTNo != null && strTNo.Length > 0 && strTDate != null && strTDate.Length > 0
                    && strTFrom != null && strTFrom.Length > 0 && strTTo != null && strTTo.Length > 0)
                {
                    var item = "";
                    TicketEntity ticTmp = null;
                    foreach (var no in strTNo)
                    {
                        foreach (var date in strTDate)
                        {
                            foreach (var from in strTFrom)
                            {
                                foreach (var to in strTTo)
                                {
                                    item = no + "-" + date + "_" + from + "_" + to;
                                    if (CommonString.CommonComTicket.ContainsKey(item))// && CommonString.CommonComTicket[item] != null && CommonString.CommonComTicket[item].strSubCode == strSubCode)
                                    {
                                        CommonString.CommonComTicket.TryRemove(item, out ticTmp);
                                    }
                                    if (CommonString.CommonMobileTicket.ContainsKey(item))// && CommonString.CommonMobileTicket[item] != null && CommonString.CommonMobileTicket[item].strSubCode == strSubCode)
                                    {
                                        CommonString.CommonMobileTicket.TryRemove(item, out ticTmp);
                                    }
                                }
                            }
                        }
                    }
                }
                strTNo = null;
                strTDate = null;
                strTFrom = null;
                strTTo = null;
            }
        }

        public static void SetCommonTicket(bool isMobile, TicketEntity value, bool isSendToCache = true, bool isSetLocal = true)
        {
            if (value == null || !value.CanSub)
                return;
            ClearBadTicket();
            if (isSetLocal)
            {
                if (!SetLocalTicket(isMobile, value))
                {
                    //ThreadPool.QueueUserWorkItem(new WaitCallback(obj =>
                    //{
                    //服务器推送信息，立马下单
                    SubOrderByCommonTicket(isMobile);
                    //}));
                }
                else
                    return;
            }
            SendTicketToServer(isMobile, value, isSendToCache, isSetLocal);
        }

        public static bool SetLocalTicket(bool isMobile, TicketEntity value)
        {
            try
            {
                var trainNo = value.TrainNo + "-" + value.AttentionItem.Key;
                // ticket.Date.ToString("yyyy-MM-dd") + "-" + ticket.FromSationName + "-" + ticket.ToStationName;
                if (!isMobile)
                {
                    if (CommonString.CommonComTicket.ContainsKey(trainNo)
                        && CommonString.CommonComTicket[trainNo] != null &&
                        CommonString.CommonComTicket[trainNo].strSubCode.Equals(value.strSubCode))
                        return true;
                    CommonString.CommonComTicket[trainNo] = (TicketEntity)value.Clone();
                }
                else
                {
                    if (CommonString.CommonMobileTicket.ContainsKey(trainNo)
                        && CommonString.CommonMobileTicket[trainNo] != null &&
                        CommonString.CommonMobileTicket[trainNo].strSubCode.Equals(value.strSubCode))
                        return true;
                    CommonString.CommonMobileTicket[trainNo] = (TicketEntity)value.Clone();
                }
            }
            catch (Exception oe)
            {
                Log.WriteError("SetLocalTicket出错", oe);
            }
            return false;
        }

        static void SubOrderByCommonTicket(bool isMobile)
        {
            if (!CommonString.IsQiangIng || CommonString.LstUser == null || CommonString.LstUser.Count <= 0)
            {
                return;
            }
            //Task.Factory.StartNew(() =>
            //{
            try
            {
                Parallel.ForEach<UserEntity>(CommonString.LstUser.FindAll(p => !p.IsExit && !p.isAccountError
                    && ((isMobile && !p.IsOnMobileSub)
                        || (!isMobile && (!p.IsOnComSub)))), uut =>//|| (p.IsOnComSub && p.IsOnTestMode)
                {
                    //CommonMSG.AddMSG(uut.StrNowUserName + ":-Start");
                    //ThreadPool.QueueUserWorkItem(delegate(object obj)
                    //{
                    try
                    {
                        uut.StartQiang(isMobile);
                    }
                    catch (Exception oe)
                    {
                        Console.WriteLine("SubOrderByCommonTicket:" + oe.Message);
                    }
                    //CommonMSG.AddMSG(uut.StrNowUserName + ":-End");
                    //});
                });
            }
            catch (AggregateException oe)
            {
                foreach (Exception item in oe.InnerExceptions)
                {
                    Log.WriteError("SubOrderByCommonTicket异常", item);
                }
            }
            catch (Exception oe)
            {
                Log.WriteError("SubOrder出错", oe);
            }
            //});
        }

        static void SendTicketToServer(bool isMobile, TicketEntity ticket, bool isSendToCache = true, bool isSetLocal = true)
        {
            if (!isSendToCache)
            {
                return;
            }

            #region SendToServer
            ThreadPool.QueueUserWorkItem((object obj) =>
            {
                #region SendToCache

                if (CommonCacheHelper.IsServeCacheEnable)
                {
                    try
                    {
                        if (ticket.LstSeatTypes != null && ticket.LstTicketNum.Count == 1 && ticket.LstTicketNum.Keys.Contains("无座"))
                        {
                        }
                        else
                        {
                            //[10-25_TXX_HBB_K7042_ADULT],[PurPoseCode_Location_TrainNo_TrainID_strFromTelCode_strToTelCode_StrYuPiao_strSubCode]
                            //[10-25_TXX_HBB_K7042_ADULT]
                            string strKey = string.Format("[{0}_{1}_{2}_{3}_{4}"
                                , ticket.AttentionItem.Date.ToString("yyyy-MM-dd")
                                , ticket.AttentionItem.FromStation.Code, ticket.AttentionItem.ToStation.Code
                                , ticket.TrainNo, isMobile ? ticket.AttentionItem.PurposeMobileCode : ticket.AttentionItem.PurposeCode);
                            //[PurPoseCode_Location_TrainNo_TrainID_strFromTelCode_strToTelCode_StrYuPiao_strSubCode]
                            string strValue = string.Format("[{0}_{1}_{2}_{3}_{4}_{5}_{6}_{7}_{8}]"
                                , isMobile ? ticket.AttentionItem.PurposeMobileCode : ticket.AttentionItem.PurposeCode
                                , ticket.strLocationCode
                                , ticket.TrainNo
                                , ticket.TrainID
                                , ticket.strFromTelCode
                                , ticket.strToTelCode
                                , ticket.strYuPiao
                                , ticket.StrNewYuPiao
                                , ticket.strSubCode);

                            List<string> lstTmpSeat = new List<string>();
                            //lstTmpSeat.Add(strKey + "]," + strValue);
                            if (ticket.LstTicketNum.Count > 0)
                            {
                                foreach (var item in ticket.LstTicketNum)
                                {
                                    if (ticket.AttentionItem.LstAllSeatType.Contains(item.Key))
                                        lstTmpSeat.Add(string.Format("{0}_{1}],{2},{4},{3}"
                                            , strKey, item.Key, strValue, CommonString.serverTime.ToString("HH:mm:ss fff"),
                                            isMobile ? "1" : "0"));
                                }
                            }
                            lstTmpSeat.ForEach(delegate(string str)
                            {
                                CommonCacheHelper.StrTicket = str;
                            });
                        }
                    }
                    catch (Exception oe)
                    {
                        Log.WriteError("SendToServer出错", oe);
                    }
                }

                //Send To OtherWindows
                try
                {
                    var otherWindows = LocalHelper.GetOtherWindows();
                    if (otherWindows != null && otherWindows.Count > 0)
                    {
                        var ticketInfo = JsonHelper.To<TicketEntity>(ticket);
                        var ptrInfo = Marshal.StringToHGlobalAnsi(ticketInfo);
                        foreach (var window in otherWindows)
                        {
                            WindowsAPI.SendMessage(window, NativeMethods.TicketMsgType, ptrInfo, ptrInfo);
                        }
                    }
                }
                catch
                {
                    // ignored
                }

                #endregion
                string url = "";
                try
                {
                    url = string.Format("ticket.aspx?op=query&app={0}&mac={1}&reg={2}&ver={3}&user={4}&no={5}&date={6}&tick={7}&from={8}&to={9}"
                       , CommonReg.StrMachineKey, Environment.UserDomainName
                       , CommonReg.DtRegTime.ToString("yyyy-MM-dd HH:mm:ss"), CommonString.dtNowDate.ToString("yyyy-MM-dd HH:mm:ss")
                       , ticket.LeftTicketDescription, ticket.TrainNo + (ticket.IsMobile ? "[手]" : "[电]")
                       , ticket.AttentionItem.Date.ToString("yyyy-MM-dd"), ticket.dtTicket.ToString("yyyy-MM-dd HH:mm:ss")
                       , ticket.FromSationName, ticket.ToStationName);
                }
                catch (Exception oe)
                {
                    Log.WriteError("SendToServer出错", oe);
                }
                //try
                //{
                //    if (!string.IsNullOrEmpty(url) && CommonString.IsQueryLog)
                //        url = CommonMethod.GetServerHtml(url, SiteType.zs, CommonString.HostQueryURL);
                //}
                //catch (Exception oe)
                //{
                //    Log.WriteError(oe);
                //}
            });
            #endregion
        }

        #endregion

        #region 加密相关

        public static T Deserialize<T>(Stream stream)
        {
            T result;
            try
            {
                DataContractJsonSerializer dataContractJsonSerializer = new DataContractJsonSerializer(typeof(T));
                result = (T)((object)dataContractJsonSerializer.ReadObject(stream));
            }
            catch (Exception)
            {
                result = default(T);
            }
            return result;
        }

        public static T Deserialize<T>(string json)
        {
            T result;
            try
            {
                using (MemoryStream memoryStream = new MemoryStream(Encoding.UTF8.GetBytes(json)))
                {
                    DataContractJsonSerializer dataContractJsonSerializer = new DataContractJsonSerializer(typeof(T));
                    result = (T)((object)dataContractJsonSerializer.ReadObject(memoryStream));
                }
            }
            catch (Exception)
            {
                result = default(T);
            }
            return result;
        }

        private static SymmetricAlgorithm mobjCryptoService = new RijndaelManaged();
        private static string Key = "123qwe";
        /// <summary>   
        /// 获得密钥   
        /// </summary>   
        /// <returns>密钥</returns>   
        public static byte[] GetLegalKey()
        {
            string sTemp = Key;
            mobjCryptoService.GenerateKey();
            byte[] bytTemp = mobjCryptoService.Key;
            int KeyLength = bytTemp.Length;
            if (sTemp.Length > KeyLength)
                sTemp = sTemp.Substring(0, KeyLength);
            else if (sTemp.Length < KeyLength)
                sTemp = sTemp.PadRight(KeyLength, ' ');
            return Encoding.ASCII.GetBytes(sTemp);
        }
        /// <summary>   
        /// 获得初始向量IV   
        /// </summary>   
        /// <returns>初试向量IV</returns>   
        public static byte[] GetLegalIV()
        {
            string sTemp = "#!@#esdwDFd!@#!@#454646@#$@54@#$#@56";
            mobjCryptoService.GenerateIV();
            byte[] bytTemp = mobjCryptoService.IV;
            int IVLength = bytTemp.Length;
            if (sTemp.Length > IVLength)
                sTemp = sTemp.Substring(0, IVLength);
            else if (sTemp.Length < IVLength)
                sTemp = sTemp.PadRight(IVLength, ' ');
            return Encoding.ASCII.GetBytes(sTemp);
        }
        /// <summary>   
        /// 加密方法   
        /// </summary>   
        /// <param name="Source">待加密的串</param>   
        /// <returns>经过加密的串</returns>   
        public static string Encrypto(string Source)
        {
            using (MemoryStream ms = new MemoryStream())
            {
                byte[] bytIn = Encoding.UTF8.GetBytes(Source);
                mobjCryptoService.Key = GetLegalKey();
                mobjCryptoService.IV = GetLegalIV();
                ICryptoTransform encrypto = mobjCryptoService.CreateEncryptor();
                CryptoStream cs = new CryptoStream(ms, encrypto, CryptoStreamMode.Write);
                cs.Write(bytIn, 0, bytIn.Length);
                bytIn = null;
                cs.FlushFinalBlock();
                byte[] bytOut = ms.ToArray();
                return Convert.ToBase64String(bytOut);
            }
        }
        /// <summary>   
        /// 解密方法   
        /// </summary>   
        /// <param name="Source">待解密的串</param>   
        /// <returns>经过解密的串</returns>   
        public static string Decrypto(string Source)
        {
            //if (!string.IsNullOrEmpty(Source) && Source.Length % 4 > 0)
            //{
            //    Source = Source.PadRight(Source.Length + (4 - (Source.Length % 4)), '=');
            //}
            byte[] bytIn = Convert.FromBase64String(Source);
            using (MemoryStream ms = new MemoryStream(bytIn, 0, bytIn.Length))
            {
                bytIn = null;
                mobjCryptoService.Key = GetLegalKey();
                mobjCryptoService.IV = GetLegalIV();
                ICryptoTransform encrypto = mobjCryptoService.CreateDecryptor();
                CryptoStream cs = new CryptoStream(ms, encrypto, CryptoStreamMode.Read);
                StreamReader sr = new StreamReader(cs);
                return sr.ReadToEnd();
            }
        }
        #endregion

        #region 播放音乐相关

        public static clsMCI play = new clsMCI();

        /// <summary>
        /// 播放音频文件
        /// </summary>
        /// <param name="type">0:提示音，1：系统警告音，2：用户音频</param>
        /// <param name="fileName"></param>
        public static void Play(int type = 0, string fileName = "")
        {
            if (type == 2)
            {
                if (!string.IsNullOrEmpty(fileName))
                {
                    try
                    {
                        play.FileName = fileName;
                        if (!string.IsNullOrEmpty(play.FileName))
                        {
                            Settings.Default.StrUserMusic = play.FileName;
                        }
                        else
                        {
                            Settings.Default.StrUserMusic = string.Empty;
                        }
                        Settings.Default.Save();
                    }
                    catch { }
                }
                if (!string.IsNullOrEmpty(Settings.Default.StrUserMusic))
                {
                    try
                    {
                        play.FileName = Settings.Default.StrUserMusic;
                    }
                    catch { }
                }
                if (!string.IsNullOrEmpty(play.FileName))
                {
                    play.play();
                }
                else
                {
                    type = 1;
                }
            }

            if (type == 0)
            {
                new SoundPlayer(Resources.chord).Play();
            }
            else if (type == 1)
            {
                new SoundPlayer(Resources.notify).Play();
            }
            else if (type == 3)
            {
                new SoundPlayer(Resources.message).Play();
            }
            else if (type == 4)
            {
                try
                {
                    play.FileName = CommonString.strUpdateMSG;
                    play.play();
                }
                catch (Exception oe)
                {
                    Log.WriteError(oe);
                }
            }
        }

        public static void Stop()
        {
            if (!string.IsNullOrEmpty(play.FileName))
            {
                try
                {
                    play.StopT();
                    CommonString.IsPlayedMusic = false;
                }
                catch
                {
                    // ignored
                }
            }
        }
        #endregion

        #region 起售时间

        private static DateTime maxDate = DateTime.MinValue;

        public static DateTime MaxDate
        {
            get
            {
                if (maxDate == DateTime.MinValue)
                {
                    maxDate = CommonString.serverTime.Date.AddDays(GetYuShouDate(CommonString.serverTime) - 1);
                }
                return CommonMethod.maxDate;
            }
            set { CommonMethod.maxDate = value; }
        }

        public static int GetYuShouDate(DateTime dtTime, bool isRefresh = false)
        {
            if (isRefresh)
            {
                CommonString.NYuShouDate = DateTimeUtil.GetYuShouDate();
            }
            return CommonString.NYuShouDate;
            ////'2014-06-01','2014-09-30','2014-12-01','2014-12-31','2015-01-01','2015-03-31'
            //int nDay = 0;
            //if (dtTime.Year >= 2016)
            //{
            //    nDay = CommonString.NYuShouDate;
            //}
            //else
            //{
            //    if (dtTime.Date.Month == 12)
            //    {
            //        //12月1日，预售期由现行20天延长至30天；
            //        //12月2日至12月6日，预售期每天再比上一日延长6天；
            //        //12月6日起，预售期延长至60天
            //        if (dtTime.Day == 1)
            //        {
            //            nDay = 30;
            //        }
            //        else if (dtTime.Day > 1 && dtTime.Day < 7)
            //        {
            //            nDay = 30 + (dtTime.Day - 1) * 6;
            //        }
            //        else
            //        {
            //            nDay = 60;
            //        }
            //    }
            //    else
            //    {
            //        nDay = 20;
            //    }
            //}
            //return nDay;
        }

        /// <summary>
        /// 获取起售时间
        /// </summary>
        /// <param name="strStation"></param>
        /// <param name="dtNow"></param>
        /// <returns></returns>
        public static DateTime GetPreSellingTimeDate(string strStation)
        {
            return GetPreSellingTimeDateByStation(strStation, CommonString.serverTime);
        }

        /// <summary>
        /// 获取起售时间
        /// </summary>
        /// <param name="strStation"></param>
        /// <param name="dtNow"></param>
        /// <returns></returns>
        public static DateTime GetPreSellingTimeDateByStation(string strStation, DateTime dtCurrent)
        {
            string result = GetPreSellingTimeStr(strStation);
            decimal milSec = 0;
            return GetPreSellingTimeDateByTime(result, ref milSec);
        }

        public static DateTime GetPreSellingTimeDateByTime(string strTimeSpan, ref decimal milSec, int nSecond = 0)
        {
            return GetPreSellingTimeDateByTime(strTimeSpan, CommonString.serverTime, ref milSec, nSecond);
        }

        public static DateTime GetPreSellingTimeDateByTime(string strTimeSpan, DateTime dtCurrent, ref decimal milSec, int nSecond = 0)
        {
            DateTime dtNow = CommonString.serverTime;
            if (!string.IsNullOrEmpty(strTimeSpan) && DateTime.TryParse(dtCurrent.ToString("yyyy-MM-dd") + " " + strTimeSpan, out dtNow))
            {
                if (dtNow > dtCurrent)
                {
                    //dtNow = dtNow.AddSeconds(-1);
                    milSec = 0;
                    if (nSecond > 0)
                    {
                        dtNow = dtNow.AddSeconds(-nSecond);
                    }
                }
                if (dtNow < dtCurrent)
                    dtNow = dtCurrent;
                //DateTime.Parse(dtCurrent.ToString("yyyy-MM-dd") + " " + dtCurrent.AddHours(-1).ToString("HH") + ":00")
            }
            return dtNow;
        }

        public static string GetPreSellingTimeStr(string strStation)
        {
            string result = "";
            if (!string.IsNullOrEmpty(strStation))
            {
                result = CommonString.LstReleaseTickets.FirstOrDefault(p => p.Value.Contains(strStation)).Key;
                //for (int i = 0; i < CommonString.LstReleaseTickets.Count; i++)
                //{
                //    if (CommonString.LstReleaseTickets[i].lstStation.Contains(strStation))
                //    {
                //        result = CommonString.LstReleaseTickets[i].strTime;
                //        break;
                //    }
                //}
            }
            return result;
        }

        #endregion

        #region 网络连接

        ////private const int INTERNET_CONNECTION_MODEM = 1;
        ////private const int INTERNET_CONNECTION_LAN = 2;
        //[DllImport("winInet.dll")]
        //private static extern bool InternetGetConnectedState(ref int dwFlag, int dwReserved);

        //public static bool IsOnLine()
        //{
        //    int dwFlag = 0;
        //    return CommonString.IsOnLine = InternetGetConnectedState(ref dwFlag, 0);
        //}
        [DllImport("wininet.dll")]
        private static extern bool InternetGetConnectedState(int reservedValue, out int connectionDescription);

        public static RouteMode NowInternetType()
        {
            int num = 1;
            int num2 = 2;
            int num3 = 4;
            int num4 = 8;
            try
            {
                int num5;
                if (InternetGetConnectedState(0, out num5))
                {
                    if ((num5 & num) == num)
                    {
                        return RouteMode.ADSL;
                    }
                    if ((num5 & num2) == num2)
                    {
                        return RouteMode.路由器;
                    }
                    if ((num5 & num3) == num3)
                    {
                        return RouteMode.代理;
                    }
                    if ((num5 & num4) == num4)
                    {
                        return RouteMode.其他;
                    }
                }
            }
            catch { }
            return RouteMode.未联网;
        }
        #endregion

        #region 初始化数据

        //[DllImport("wininet.dll", CharSet = CharSet.Ansi, SetLastError = true, PreserveSig = true)]
        //public static extern int InternetOpen(string lpszCallerName, int dwAccessType, string lpszProxyName, string lpszProxyBypass, int dwFlags);  

        public static void ConnectKeFu(bool isOpen = false)
        {
            if (isOpen)
            {
                if (CommonString.IsQQCanUse)
                    System.Diagnostics.Process.Start(CommonString.StrQQ);
                else
                    System.Windows.Forms.MessageBox.Show("联系客服已下架，感谢您的关注！", "提示");
            }
            //System.Diagnostics.Process.Start("https://me.alipay.com/12306zhushou");
        }

        [DllImport("wininet.dll", EntryPoint = "InternetSetOptionA", CharSet = CharSet.Ansi, SetLastError = true, PreserveSig = true)]
        public static extern bool InternetSetOption(IntPtr hInternet, int dwOption, ref int lpBuffer, int dwBufferLength);

        public static bool InitStation(bool isLocal = true)
        {
            bool result = false;
            string strStation = "";
            CommonString.IsLoadStations = true;
            if (isLocal)
            {
                strStation = CommonString.StrOldStations;
            }
            else
            {
                try
                {
                    strStation = WebClientExt.GetHtml(CommonString.StrUpdateURL + "config/station_name.js?t=" + DateTime.Now.Ticks, (double)5);
                    if (!string.IsNullOrEmpty(strStation) && !strStation.StartsWith("var station_names ='"))
                    {
                        strStation = null;
                    }
                    if (string.IsNullOrEmpty(strStation))
                    {
                        strStation = WebClientExt.GetHtml("https://kyfw.12306.cn/otn/resources/js/framework/station_name.js?r=" + DateTime.Now.Ticks
                            , "", CommonString.StrLocalComIP, "", 3, 5).Trim();
                    }
                    if (!string.IsNullOrEmpty(strStation) && strStation.StartsWith("var station_names ='") && strStation.HorspoolIndex("@") > 0)
                    {
                        strStation = CommonMethod.SubString(strStation, "var station_names ='", "';").Trim();
                    }
                }
                catch
                {
                    strStation = "";
                }
            }
            string[] array = null;
            try
            {
                if (!string.IsNullOrEmpty(strStation))
                {
                    array = strStation.Split(new char[] { '@' }, StringSplitOptions.RemoveEmptyEntries);
                    if (array == null || array.Length < 100)
                    {
                        return result;
                    }
                    CommonString.Stations = new List<TrainStation>();
                    string[] arrayTmp = null;
                    for (int i = 0; i < array.Length; i++)
                    {
                        arrayTmp = array[i].Split(new char[] { '|' }, StringSplitOptions.RemoveEmptyEntries);
                        CommonString.Stations.Add(new TrainStation
                        {
                            ShortCut = arrayTmp[0],
                            Name = arrayTmp[1],
                            Code = arrayTmp[2],
                            Pinyin = arrayTmp[3],
                            Sipinyin = arrayTmp[4],
                            Id = int.Parse(arrayTmp[5])
                        });
                    }
                    arrayTmp = null;
                }
            }
            catch { }
            finally
            {
                CommonString.IsLoadStations = false;
                strStation = null;
                array = null;
            }
            result = CommonString.Stations != null && CommonString.Stations.Count > 0;
            return result;
        }

        public static void InitConfig()
        {
            Task.Factory.StartNew(() =>
            {
                CommonString.lstGroupStation = new List<KeyValuePair<string, List<string>>>();
                InitOthers();
                if (!InitStation(false))
                {
                    InitStation();
                }
                if (!InitTimeSpan(true))
                {
                    InitTimeSpan();
                }
                //initGroupStation();
                IPHelper.LoadConfig();
                CommonMSG.SendMSG();
                //NewTicketHelper.InitLoginPool();
                CommonMethod.isCheckCodeCanUse();
                CommonReg.isBad("！@#！@#！@");
                CommonReg.LoadBadSoft();
                //CommonReg.SendInfo();
                initGroupStation(false);
            });
            ValidateCode.Init();
            ClearBadTickets();
        }

        public static void InitCommonMobile()
        {
            if (CommonString.IsMobileCanUse)
            {
                CommonString.CommonMobileInit = new Mobile.ClsuserInfo();
                for (int i = 0; i < 3; i++)
                {
                    if (!MHelper.AuthoridAccount(ref CommonString.CommonMobileInit))
                    {
                        CommonString.CommonMobileInit = new Mobile.ClsuserInfo();
                    }
                    else
                    {
                        break;
                    }
                }
                if (CommonString.CommonMobileInit == null
                    || string.IsNullOrEmpty(CommonString.CommonMobileInit.strCookie))
                {
                    CommonString.CommonMobileInit = null;
                }
            }
        }

        //public static bool IsCanQuickLogin
        //{
        //    get
        //    {
        //        return CommonString.serverTime.Hour == 6;
        //    }
        //}

        public static void AddQuickLoginParam(QuickLogin quick, bool isAddToServer = true)
        {
            if (quick != null && !string.IsNullOrEmpty(quick.StrIp) && !string.IsNullOrEmpty(quick.StrCookie) && !string.IsNullOrEmpty(quick.StrCode))
            {
                try
                {
                    ValidateCode.AddQuickCodeToWeb(quick);
                }
                catch (Exception oe)
                {
                    Log.WriteError("AddQuickParam出错", oe);
                }
            }
        }

        public static QuickLogin GetQuickLoginParam(string strComIp = "")
        {
            QuickLogin result = null;
            try
            {
                result = ValidateCode.GetQuickCodeFromWeb(strComIp);
            }
            catch (Exception oe)
            {
                Log.WriteError("GetQuickParam出错", oe);
            }
            return result;
        }

        public static void ReportHost()
        {
            ThreadPool.QueueUserWorkItem((object obj) =>
            {
                try
                {
                    string strIP = CommonString.StrLocalMobileIP;
                    CommonReg.ReportHost(strIP, true);
                    strIP = CommonString.StrLocalComIP;
                    CommonReg.ReportHost(strIP, false);
                }
                catch (Exception oe)
                {
                    Log.WriteError(oe);
                }
                //ImgLogHelper.Run();
            });
        }

        //public static bool isUpdateCanUse()
        //{
        //    bool result = false;
        //    try
        //    {
        //        foreach (var strUrl in CommonString.LstUpdateUrls)
        //        {
        //            result = isSiteCanUse(strUrl);
        //            if (result)
        //            {
        //                CommonString.StrUpdateURL = strUrl;
        //                break;
        //            }
        //        }
        //    }
        //    catch (Exception oe)
        //    {
        //        Log.WriteError("isWebSiteCanUse出错", oe);
        //    }
        //    return result;
        //}

        private static string GetUpdateHtmlByTimes(string strUrlContext, string strUpdateUrl, int times = 3)
        {
            string html = "";
            try
            {
                for (int i = 0; i < times; i++)
                {
                    html = CommonMethod.GetServerHtmlByUrl(strUrlContext, strUpdateUrl);
                    if (!string.IsNullOrEmpty(html))
                    {
                        if (html.Contains("<script"))
                        {
                            html = "";
                        }
                        else
                        {
                            break;
                        }
                    }
                }
            }
            catch (Exception oe)
            {
                Log.WriteError("GetUpdateHtmlByTimes", oe);
            }
            return html;
        }

        private static string GetUpdateHtml(string url, ref string strUpdate)
        {
            string html = "";
            try
            {
                if (!string.IsNullOrEmpty(strUpdate))
                {
                    html = GetUpdateHtmlByTimes(url, strUpdate);
                    if (string.IsNullOrEmpty(html))
                    {
                        strUpdate = "";
                    }
                }
                if (string.IsNullOrEmpty(html))
                {
                    for (int i = 0; i < CommonString.LstUpdateUrls.Count; i++)
                    {
                        var strUrl = CommonString.LstUpdateUrls[i];

                        html = GetUpdateHtmlByTimes(url, strUrl);
                        if (!string.IsNullOrEmpty(html))
                        {
                            strUpdate = strUrl;
                            break;
                        }
                    }
                }
            }
            catch (Exception oe)
            {
                Log.WriteError("GetUpdateHtml", oe);
            }
            return html;
        }

        public static bool InitServers()
        {
            try
            {
                List<string> lstTmpServer = new List<string>();
                string html = "";
                string strNowDate = CommonString.serverTime.ToString("MMddHHmm");
                string url = string.Format("ticket.aspx?op=sconfig&app={0}&mac={1}&ver={2}&user={3}&t={4}"
                    , (CommonReg.StrMachineKey), (Environment.UserDomainName), (CommonString.dtNowDate.ToString("yyyy-MM-dd HH:mm:ss"))
                    , (Environment.UserName), strNowDate);
                var strUrl = CommonString.StrUpdateURL;
                html = GetUpdateHtml(url, ref strUrl);
                if (!string.IsNullOrEmpty(strUrl) && !strUrl.Equals(CommonString.StrUpdateURL))
                {
                    CommonString.StrUpdateURL = strUrl;
                }
                if (!string.IsNullOrEmpty(html) && html.HorspoolIndex("故障") < 0)
                {
                    if (html.HorspoolIndex("\r\n") > 0)
                        html = CommonMethod.SubString(html, "\r\n").Trim();
                    if (!string.IsNullOrEmpty(html) && !html.ToLower().Contains("<"))
                    {
                        try
                        {
                            html = CommonEncryptHelper.DES3Decrypt(html, CommonString.StrCommonEncryptKey, strNowDate);
                        }
                        catch (Exception oe)
                        {
                            Log.WriteError(oe);
                        }
                    }
                    else
                        return false;
                }
                else
                    return false;
                //if (string.IsNullOrEmpty(html))
                //    html = WebClientExt.GetHtml(CommonString.StrUpdateURL + "serverNew.txt", (double)5);
                //if (string.IsNullOrEmpty(html))
                //    return false;
                //html = CommonEncryptHelper.DESDecrypt(html, CommonString.StrCommonEncryptKey);
                //html = CommonMethod.Decrypto(html);
                string strServer = "";
                if (html.Contains("|"))
                {
                    strServer = CommonMethod.SubString(html, "|");
                    html = CommonMethod.SubString(html, "", "|");
                }
                string[] array = html.Trim().Split(new string[] { "\n" }, StringSplitOptions.RemoveEmptyEntries);
                if (array != null && array.Length > 0)
                {
                    foreach (string str in array)
                    {
                        if (!string.IsNullOrEmpty(str.Trim()) && !lstTmpServer.Contains(str.Trim()))
                        {
                            if (str.Contains("["))
                            {
                                lstTmpServer.Add(str.Trim().Substring(0, str.IndexOf("[")));
                                CommonString.StrAccountType = CommonMethod.SubString(str, "[", "]");
                            }
                            else
                            {
                                lstTmpServer.Add(str.Trim());
                            }
                        }
                    }
                }
                if (lstTmpServer != null && lstTmpServer.Count > 0)
                {
                    CommonString.LstServerURL = lstTmpServer;
                }
                ////todo undo
                //strServer = "127.0.0.1\n21";//
                if (!string.IsNullOrEmpty(strServer))
                {
                    string[] server = strServer.Trim().Split(new string[] { "\n" }, StringSplitOptions.RemoveEmptyEntries);
                    if (server != null && server.Length >= 2)
                    {
                        server[0] = server[0].Trim();
                        server[1] = server[1].Trim();
                        if (!CommonCacheHelper.IsConnected ||
                            (CommonCacheHelper.strIP != server[0] && CommonCacheHelper.nPort.ToString() != server[1]))
                        {
                            CommonCacheHelper.strIP = server[0].Trim();//"127.0.0.1";
                            CommonCacheHelper.nPort = BoxUtil.GetInt32FromObject(server[1].Trim());
                        }
                        if (server.Length > 2)
                        {
                            for (int i = 2; i < server.Length; i++)
                            {
                                strServer = CommonMethod.SubString(server[i], "", ":");
                                switch (strServer)
                                {
                                    case "webcode":
                                        CommonString.IsWebAutoCodeConfig = CommonMethod.SubString(server[i], ":").Equals("1");
                                        break;
                                    case "localcode":
                                        CommonString.IsLocalAutoCodeConfig = CommonMethod.SubString(server[i], ":").Equals("1");
                                        break;
                                    case "upcode":
                                        LocalImageHelper.IsSendToServer = CommonMethod.SubString(server[i], ":").Equals("1");
                                        break;
                                    case "codeconfig":
                                        var codes = new List<AutoCodeEntity>();
                                        var result = CommonMethod.SubString(server[i], ":");
                                        result = CommonEncryptHelper.DES3Decrypt(result, CommonString.StrCommonEncryptKey);
                                        string[] strT = result.Split(new string[] { "|" }, StringSplitOptions.RemoveEmptyEntries);
                                        if (strT != null && strT.Length > 0)
                                        {
                                            foreach (var item in strT)
                                            {
                                                codes.Add(new AutoCodeEntity(item));
                                            }
                                        }
                                        if (CommonString.LstAutoCode == null)
                                            CommonString.LstAutoCode = new List<AutoCodeEntity>();
                                        if (codes.Count > 0)
                                        {
                                            codes.ForEach(p =>
                                            {
                                                if (!CommonString.LstAutoCode.Exists(q => q.StrName.Equals(p.StrName)))
                                                {
                                                    CommonString.LstAutoCode.Add(p);
                                                }
                                            });
                                        }
                                        break;
                                    case "code1config":
                                        result = CommonMethod.SubString(server[i], ":");
                                        result = CommonEncryptHelper.DES3Decrypt(result, CommonString.StrCommonEncryptKey);
                                        if (!string.IsNullOrEmpty(result))
                                        {
                                            //result = "http://localhost:55257/";
                                            //result = "http://img.jzjiu.com/";"http://localhost:23205/";// "http://**************:81/";//
                                            CommonString.AutoCodeURL = result.Trim();//"http://**************:999/";//
                                        }
                                        break;

                                    #region 极速服务器IP

                                    //case "codeserver":
                                    //    result = CommonMethod.SubString(server[i], ":");
                                    //    strT = result.Split(new string[] { "," }, StringSplitOptions.RemoveEmptyEntries);
                                    //    var ipStr = "";
                                    //    var nPort = 0;
                                    //    if (strT != null && strT.Length > 1)
                                    //    {
                                    //        if (IPHelper.IsIPv4(strT[0]))
                                    //        {
                                    //            ipStr = strT[0];
                                    //        }
                                    //        nPort = BoxUtil.GetInt32FromObject(strT[1], 8082);
                                    //    }
                                    //    try
                                    //    {
                                    //        if (!string.IsNullOrEmpty(ipStr))
                                    //        {
                                    //            monocaptcha.Util.NServerIP = ipStr;
                                    //            monocaptcha.Util.NServerPort = nPort;
                                    //        }
                                    //    }
                                    //    catch (Exception oe)
                                    //    {
                                    //        Log.WriteError(oe);
                                    //    }
                                    //    break;
                                    //case "codeserverbak":
                                    //    result = CommonMethod.SubString(server[i], ":");
                                    //    strT = result.Split(new string[] { "," }, StringSplitOptions.RemoveEmptyEntries);
                                    //    ipStr = "";
                                    //    nPort = 0;
                                    //    if (strT != null && strT.Length > 1)
                                    //    {
                                    //        if (IPHelper.IsIPv4(strT[0]))
                                    //        {
                                    //            ipStr = strT[0];
                                    //        }
                                    //        nPort = BoxUtil.GetInt32FromObject(strT[1], 8082);
                                    //    }
                                    //    try
                                    //    {
                                    //        if (!string.IsNullOrEmpty(ipStr))
                                    //        {
                                    //            monocaptcha.Util.NBakServerIP = ipStr;
                                    //            monocaptcha.Util.NBakServerPort = nPort;
                                    //        }
                                    //    }
                                    //    catch (Exception oe)
                                    //    {
                                    //        Log.WriteError(oe);
                                    //    }
                                    //    break;
                                    #endregion

                                    case "optime":
                                        var timeStr = CommonMethod.SubString(server[i], ":");
                                        var strTimes = timeStr.Split(new string[] { "," }, StringSplitOptions.RemoveEmptyEntries);
                                        if (strTimes != null && strTimes.Length == 3)
                                        {
                                            DtOperateStart = BoxUtil.GetInt32FromObject(strTimes[0], DtOperateStart);
                                            DtOrderOperateEnd = BoxUtil.GetInt32FromObject(strTimes[1], DtOrderOperateEnd);
                                            DtLoginOperateEnd = BoxUtil.GetInt32FromObject(strTimes[2], DtLoginOperateEnd);
                                            if (DtOperateStart <= 0)
                                            {
                                                DtOperateStart = 65945;
                                            }
                                            if (DtOrderOperateEnd <= 0)
                                            {
                                                DtOrderOperateEnd = 230000;
                                            }
                                            if (DtLoginOperateEnd <= 0)
                                            {
                                                DtLoginOperateEnd = 65945;
                                            }
                                        }
                                        break;
                                    //case "checkpiaochi":
                                    //    CommonString.IsCheckPiaoChi = CommonMethod.SubString(server[i], ":").Equals("1");
                                    //    break;
                                    //case "advancecode":
                                    //    CommonString.IsAdvanceCode = CommonMethod.SubString(server[i], ":").Equals("1");
                                    //    break;
                                    case "checkcode":
                                        CommonString.IsServiceCheckCodeCanUse = CommonMethod.SubString(server[i], ":").Equals("1");
                                        break;
                                    //case "nocode":
                                    //    CommonString.IsCanNoCode = CommonMethod.SubString(server[i], ":").Equals("1");
                                    //    break;
                                    case "subserver":
                                        CommonString.IsSubByServer = CommonMethod.SubString(server[i], ":").Equals("1");
                                        break;
                                    case "quick":
                                        CommonString.IsQuickEnable = CommonMethod.SubString(server[i], ":").Equals("1");
                                        break;
                                    case "async":
                                        CommonString.IsForceAsync = CommonMethod.SubString(server[i], ":").Equals("1");
                                        break;
                                    case "codesleep":
                                        if (CommonString.NMaxWaitCodeTime <= 0)
                                            CommonString.NMaxWaitCodeTime = BoxUtil.GetInt32FromObject(CommonMethod.SubString(server[i], ":"), 3000);
                                        break;
                                    case "ordertime"://下单总时间控制
                                        if (CommonString.NMaxOrderSubmitTime <= 0)
                                            CommonString.NMaxOrderSubmitTime = BoxUtil.GetInt32FromObject(CommonMethod.SubString(server[i], ":"), 3000);
                                        break;
                                    case "ordertimecontrol"://是否控制下单总时间
                                        CommonString.IsNeedOrderTimeControl = CommonMethod.SubString(server[i], ":").Equals("1");
                                        break;
                                    case "nocodeordertime"://免码下单时间控制
                                        if (CommonString.NMaxOrderNoCodeSubmitTime <= 0)
                                            CommonString.NMaxOrderNoCodeSubmitTime = BoxUtil.GetInt32FromObject(CommonMethod.SubString(server[i], ":"), 1500);
                                        break;
                                    case "servercache"://云推送
                                        CommonCacheHelper.IsServeCacheEnable = CommonMethod.SubString(server[i], ":").Equals("1");
                                        break;
                                    //case "quicklogin":
                                    //    CommonString.IsQuickLogin = CommonMethod.SubString(server[i], ":").Equals("1");
                                    //    break;
                                    case "notickquerycache"://捡漏是否云推送
                                        CommonString.IsNoTickQueryCache = CommonMethod.SubString(server[i], ":").Equals("1");
                                        break;
                                    case "oldlogin"://老版登录是否可用
                                        CommonString.IsOldLoginCanUse = CommonMethod.SubString(server[i], ":").Equals("1");
                                        break;
                                    case "cookiefromserver"://老版登录是否可用
                                        CommonString.IsGetCookieFromServer = CommonMethod.SubString(server[i], ":").Equals("1");
                                        break;
                                    case "cookieurl"://老版登录是否可用
                                        CommonString.StrCookieUrl = CommonMethod.Base64(CommonMethod.SubString(server[i], ":"));
                                        break;
                                    case "devicecookie"://老版登录是否可用
                                        CommonString.StrDeviceCookie = CommonMethod.Base64(CommonMethod.SubString(server[i], ":"));
                                        break;
                                    case "querylog":
                                        CommonString.IsQueryLog = CommonMethod.SubString(server[i], ":").Equals("1");
                                        break;
                                    case "normalorderquerycount":
                                        CommonString.IsNormalOrderNotQueryCount = !(CommonMethod.SubString(server[i], ":").Equals("1"));
                                        break;
                                    default:
                                        break;
                                }
                            }
                        }
                    }
                }
            }
            catch (Exception oe)
            {
                Log.WriteError(oe);
            }
            finally
            {
                if (CommonCacheHelper.IsServeCacheEnable && !CommonCacheHelper.IsConnected)
                {
                    if (string.IsNullOrEmpty(CommonCacheHelper.strIP))
                    {
                        CommonCacheHelper.strIP = IPHelper.GetIP("ticket.oldfish.cn");
                        CommonCacheHelper.nPort = 21;
                    }
                    if (!string.IsNullOrEmpty(CommonCacheHelper.strIP))
                    {
                        ThreadPool.QueueUserWorkItem((object obj) =>
                        {
                            CommonCacheHelper.Connect();
                        });
                    }
                }
            }
            return CommonString.LstServerURL != null && CommonString.LstServerURL.Count > 0;
        }
        public static bool IsAdministrator()
        {
            var result = false;
            try
            {
                WindowsIdentity current = WindowsIdentity.GetCurrent();
                WindowsPrincipal windowsPrincipal = new WindowsPrincipal(current);
                result = windowsPrincipal.IsInRole(WindowsBuiltInRole.Administrator);
            }
            catch (Exception oe)
            {
                Log.WriteError("判断是否以管理员运行出错！", oe);
            }
            return result;
        }

        public static void Exit()
        {
            CommonString.isExit = true;
            CommonString.CleanUpCurlLib();
            System.Windows.Forms.Application.Exit();
            System.Environment.Exit(0);
        }

        public static bool InitTimeSpan(bool isFromNet = false)
        {
            bool result = false;
            if (!isFromNet)
            {
                CommonString.LstReleaseTickets = new ConcurrentDictionary<string, List<string>>();
                CommonString.LstReleaseTickets["8:00"] = new List<string>();
                CommonString.LstReleaseTickets["8:30"] = new List<string>();
                CommonString.LstReleaseTickets["9:00"] = new List<string>();
                CommonString.LstReleaseTickets["9:30"] = new List<string>();
                CommonString.LstReleaseTickets["10:00"] = new List<string>();
                CommonString.LstReleaseTickets["10:30"] = new List<string>();
                CommonString.LstReleaseTickets["11:00"] = new List<string>();
                CommonString.LstReleaseTickets["11:30"] = new List<string>();
                CommonString.LstReleaseTickets["12:00"] = new List<string>();
                CommonString.LstReleaseTickets["12:30"] = new List<string>();
                CommonString.LstReleaseTickets["13:00"] = new List<string>();
                CommonString.LstReleaseTickets["13:30"] = new List<string>();
                CommonString.LstReleaseTickets["14:00"] = new List<string>();
                CommonString.LstReleaseTickets["14:30"] = new List<string>();
                CommonString.LstReleaseTickets["15:00"] = new List<string>();
                CommonString.LstReleaseTickets["15:30"] = new List<string>();
                CommonString.LstReleaseTickets["16:00"] = new List<string>();
                CommonString.LstReleaseTickets["16:30"] = new List<string>();
                CommonString.LstReleaseTickets["17:00"] = new List<string>();
                CommonString.LstReleaseTickets["17:30"] = new List<string>();
                CommonString.LstReleaseTickets["18:00"] = new List<string>();
                CommonString.LstReleaseTickets["8:00"].AddRange("北京西、南京、南京南、同江".Split(new string[] { "、" }, StringSplitOptions.RemoveEmptyEntries));
                CommonString.LstReleaseTickets["8:30"].AddRange("白城、成都东、东莞、东莞东、惠州、济宁、南充、宁波、日照、山海关、汕头、松原、乌兰浩特、乌鲁木齐南、南充北、乌鲁木齐".Split(new string[] { "、" }, StringSplitOptions.RemoveEmptyEntries));
                CommonString.LstReleaseTickets["9:00"].AddRange("阿克苏、阿克陶、阿拉山口、阿图什、巴楚、北屯市、博乐、布列开、茶陵、茶陵南、成都、大庆、大庆西、福海、哈密、和静、和什托洛盖、和田、霍尔果斯、缙云、精河、精河南、喀什、克拉玛依、库车、库尔勒、奎屯、兰州、兰州西、丽水、柳园、柳园南、轮台、玛纳斯、玛纳斯湖、墨玉、尼勒克、皮山、萍乡、萍乡北、齐齐哈尔、青田、沙湾县、莎车、鄯善、鄯善北、深圳北、沈阳北、石河子、疏勒、吐哈、吐鲁番、吐鲁番北、温州、乌西、五五、武义、新和、焉耆、叶城、伊宁、伊宁东、宜春、英吉沙、永康、泽普、成都南、柳园南、和硕、马兰、大庆东、齐齐哈尔南、福利区、西固、兰州新区、中川机场".Split(new string[] { "、" }, StringSplitOptions.RemoveEmptyEntries));
                CommonString.LstReleaseTickets["9:30"].AddRange("鳌江、苍南、德清、德清西、奉化、海宁、海宁西、湖州、加格达奇、嘉善南、嘉兴、嘉兴南、金山北、乐清、临海、漠河、南昌、南昌西、讷河、宁海、青岛、青岛北、瑞安、三门县、上虞、上虞北、绍兴、绍兴北、绅坊、深圳、沈阳、松江南、台州、桐乡、温岭、温州南、雁荡山、永嘉、余杭、余姚、余姚北、长兴、长兴南、庄桥".Split(new string[] { "、" }, StringSplitOptions.RemoveEmptyEntries));
                CommonString.LstReleaseTickets["10:00"].AddRange("北京、贵阳、哈尔滨西、深圳东、贵阳北、哈尔滨北、贵安、芜湖、东至、宣城、广德、黄山、池州、绩溪县、铜陵北、南陵、泾县、宁国、歙县北、绩溪北、旌德、黄山北、马鞍山东、歙县、当涂东、繁昌西、祁门、戈江、铜陵、阿勒泰、霍城".Split(new string[] { "、" }, StringSplitOptions.RemoveEmptyEntries));
                CommonString.LstReleaseTickets["10:30"].AddRange("敦煌、哈尔滨、杭州、嘉峪关、深圳西、威海、西安北、烟台、银川、嘉峪关南、荣成、文登东、威海北、牟平、烟台南、桃村北、海阳北、莱阳、莱西北、即墨北".Split(new string[] { "、" }, StringSplitOptions.RemoveEmptyEntries));
                CommonString.LstReleaseTickets["11:00"].AddRange("滨江、广州、哈尔滨东、九江、香坊、重庆、重庆北".Split(new string[] { "、" }, StringSplitOptions.RemoveEmptyEntries));
                CommonString.LstReleaseTickets["11:30"].AddRange("白银市、白银西、大武口、低窝铺、定边、定西、甘谷、皋兰、高台、古浪、固原、瓜州、广州东、杭州东、河口南、红寺堡、惠农、金昌、景泰、靖远、镜铁山、酒泉、临泽、灵武、陇西、绿化、平凉、青铜峡、清水、山丹、石嘴山、疏勒河、太阳山、谭家井、天水、天祝、同心、渭南镇、武山、武威、武威南、西安、夏官营、新阳镇、盐池、永登、玉门、张掖、长征、中宁、中宁东、中卫、民乐、张掖西、临泽南、高台南、清水北、酒泉南、玉门、长庆桥、泾川、宁东、宁东南、渭源、漳县、岷县、哈达铺、陇南、姚渡、榆中、定西北、通渭、秦安、天水南、东岔".Split(new string[] { "、" }, StringSplitOptions.RemoveEmptyEntries));
                CommonString.LstReleaseTickets["12:00"].AddRange("安阳、安阳东、宝鸡、宝鸡南、北京北、汉口、江山、金华、兰溪、临汾、临汾西、龙游、衢州、新乡、新乡东、义乌、运城、运城北、长春、长春西、诸暨、武义北、永康南、缙云西、濮阳".Split(new string[] { "、" }, StringSplitOptions.RemoveEmptyEntries));
                CommonString.LstReleaseTickets["12:30"].AddRange("安庆、安庆西、北京南、巢湖、肥东、广元、汉中、合肥、合肥北城、合肥南、黄梅、金寨、六安、庐江、绵阳、全椒、舒城、太湖、天柱山、桐城、武昌、宿松、广元南、昭化、合肥西、长临河、巢湖东、无为".Split(new string[] { "、" }, StringSplitOptions.RemoveEmptyEntries));
                CommonString.LstReleaseTickets["13:00"].AddRange("亳州、赤峰、达州、大连、大连北、阜南、阜阳、广州南、淮南、淮南东、霍邱、内江、三堂集、水家湖、天津南、天津西、通辽、昭通、郑州东、贾鲁河、绿博园、运粮河、宋城路、内江北、南曹、孟庄、新郑机场、开封北、兰考南、民权北".Split(new string[] { "、" }, StringSplitOptions.RemoveEmptyEntries));
                CommonString.LstReleaseTickets["13:30"].AddRange("博鳌、大同、东方、海口、海口东、吉林、陵水、六盘水、美兰、琼海、三亚、上海虹桥、神州、天津、万宁、文昌、武汉、亚龙湾、遵义".Split(new string[] { "、" }, StringSplitOptions.RemoveEmptyEntries));
                CommonString.LstReleaseTickets["14:00"].AddRange("北滘、碧江、常平、潮汕、潮阳、福田、潮州、春湾、大埔、丹霞山、东升、丰顺、佛山、高州、古镇、光明城、广州北、和平、河源、鲘门、呼和浩特、呼和浩特东、虎门、华城、惠东、惠州南、惠州西、济南、济南东、济南西、江门、揭阳、葵潭、乐昌、雷州、龙川、陆丰、茂名东、梅州、明珠、南朗、南头、南雄、平湖、坪石、普宁、前山、清远、庆盛、饶平、容桂、三水、汕尾、韶关、韶关东、深圳坪山、石家庄、石家庄北、始兴、顺德、顺德学院、太原南、唐家湾、小榄、新会、新兴县、信宜、兴宁、徐闻、阳春、英德、英德西、源潭、湛江、湛江西、樟木头、肇庆、郑州、郑州西、中山、中山北、珠海、珠海北、郁南、南江口、云浮东、怀集、广宁、三水南、肇庆东、佛山西".Split(new string[] { "、" }, StringSplitOptions.RemoveEmptyEntries));
                CommonString.LstReleaseTickets["14:30"].AddRange("包头、包头东、长沙南、德州、德州东、邯郸、邯郸东、昆明、昆明南、攀枝花、上海、太原、西昌、宜昌、宜昌东、乐昌东".Split(new string[] { "、" }, StringSplitOptions.RemoveEmptyEntries));
                CommonString.LstReleaseTickets["15:00"].AddRange("安塘、巴彦高勒、白壁关、白音察干、白云鄂博、柏果、宝拉格、贲红、查布嘎、察素齐、岔江、柴沟堡、楚雄南、达拉特旗、达拉特西、大板、大理、大陆号、大营、代县、岱岳、德令哈、甸心、定襄、东胜、东胜西、东淤地、东镇、东庄、豆罗、兑镇、额济纳、二连、发耳、繁峙、汾阳、丰镇、风陵渡、富源、高村、格尔木、公庙子、古东、古交、广安、广水、广通北、海石湾、河边、河津、鹤庆、黑井、红果、洪洞、洪洞西、侯马、侯马西、化德、怀仁、怀仁东、湟源、霍州、霍州东、集宁南、稷山、建水、江所田、交城、介休、介休东、晋中、经棚、开鲁、岢岚、昆阳、拉萨、老羊壕、乐都、丽江、林东、林西、临河、灵丘、灵石、灵石东、柳林南、鲁番、陆良、禄丰南、罗平、吕梁、麻城、麻城北、茅草坪、蒙自北、明安、那曲、南宁、尼木、宁武、盘关、平安驿、平关、平社、平田、平旺、平型关、平遥、平遥古城、祁县、祁县东、旗下营、秦家庄、沁县、清徐、曲靖、曲水县、仁布、日喀则、萨拉齐、赛汗塔拉、三家寨、桑根达来、厦门、厦门北、厦门高崎、山阴、商都、商丘、商丘南、神池、神头、师宗、十堰、石林、寿阳、朔州、松河、遂宁、太谷、太谷西、太原北、太原东、天镇、通海、土贵乌拉、土牧尔台、万州、威箐、威舍、文水、闻喜、闻喜西、乌海、乌海西、乌拉山、乌拉特前旗、乌兰哈达、五台山、五原、五寨、武乡、西斗铺、西小召、锡林浩特、下社、襄汾、襄汾西、祥云、小雨谷、孝南、孝西、忻州、新绛、信阳、信阳东、兴和西、轩岗、宣威、延安、阳高、阳明堡、阳曲、阳泉曲、宜良北、营盘湾、应县、永济、永济北、榆次、榆社、雨格、玉溪、元谋、原平、月亮田、枣林、张兰、长沙、赵城、哲里木、镇城底、正镶白旗、朱日和、卓资东、卓资山、蒙自、屏边、河口北、杨林、小新街、南宁东、西宁、民和南、乐都南、海东西、大通西、门源、鄂尔多斯、杭锦后旗、龙洞堡、汾河、西乌旗、白音华南、茶卡、苏尼特左旗、阿巴嘎旗、昆独仑召、万州北、广安南、嵩明、曲靖北、富源北、富宁、广南县、普者黑、弥勒、石林西、树木岭、香樟路、湘府路、洞井、先锋、芙蓉南、暮云、九郎山、田心东、大丰、株洲南、昭山、荷塘、板塘、乌兰察布、旗下营南".Split(new string[] { "、" }, StringSplitOptions.RemoveEmptyEntries));
                CommonString.LstReleaseTickets["15:30"].AddRange("安德、安顺、鞍山、鞍山西、巴中、白沙、白山市、北碚、草海、承德、大关、大英东、丹东、德昌、德阳、都江堰、都匀、独山、峨边、峨眉、丰都、涪陵、涪陵北、福泉、抚顺北、阜新、甘洛、赶水、赣州、高兴、广汉、贵定、贵定南、汉源、合川、红光镇、华蓥、潢川、吉安、佳木斯、简阳、江津、江油、井冈山、开江、凯里、乐山北、离堆公园、梁平、六枝、龙里、隆昌、罗江、麻尾、眉山、米易、冕宁、牡丹江、盘锦、盘锦北、彭山、彭水、彭州、蓬安、郫县、郫县西、平顶山、平顶山西、普雄、綦江、黔江、秦皇岛、青城山、渠县、荣昌、三汇镇、上海南、施秉、石柱县、水富、唐山、通化、桐子林、桐梓、潼南、图们、土溪、旺苍、武隆、西昌南、息烽、犀浦、犀浦东、喜德、秀山、延吉、盐津、燕岗、宜宾、彝良、迎宾路、营山、永川、永郎、酉阳、玉屏、岳池、岳阳、岳阳东、越西、张家口、张家口南、长寿、长寿北、镇远、织金、重庆南、周口、朱杨溪、株洲、株洲西、竹园坝、驻马店、驻马店西、资阳、资中、自贡、青莲、罗江东、广汉北、青白江东、新都东、双流机场、新津、彭山北、眉山东、青神、乐山、峨眉山、龙里北、贵定县、三都县、榕江、从江、都匀东、铜仁南、三穗、凯里南、贵定北、普定、简阳南、资阳北、资中北、隆昌北、荣昌北、永川东、大足南、苍溪、阆中、南部、武胜、巴中东、曾口、平昌、石桥、石梯、织金北、纳雍、长寿湖、垫江、梁平南、平坝南、安顺西、关岭、普安县、盘州、大方南、法启、八步、新津南、双流西、璧山、安靖、安多、贵阳东、沙岭子西、朝天、剑门关、青川、江油北".Split(new string[] { "、" }, StringSplitOptions.RemoveEmptyEntries));
                CommonString.LstReleaseTickets["16:00"].AddRange("安康、安亭北、八达岭、霸州、白涧、宝坻、保定、保定东、北戴河、北京东、北马圈子、滨海、滨海北、泊头、沧州、沧州西、昌黎、昌平、昌平北、承德东、磁山、磁县、大营镇、定州、定州东、东光、福州、福州南、抚宁、高碑店、高碑店东、高邑、高邑西、藁城、固安、关林、官厅、官厅西、郭磊庄、海拉尔、汉沽、菏泽、衡水、衡阳、衡阳东、虎什哈、花桥、怀柔、怀柔北、黄村、惠山、蓟州、晋州、井店、井陉、井南、静海、军粮城北、康城、康庄、昆山、昆山南、涞源、廊坊、廊坊北、黎城、临沂、临沂北、柳州、龙华、隆化、卢龙、芦台、潞城、滦河、滦平、滦县、洛阳、洛阳东、洛阳龙门、满洲里、密云北、南宫东、南口、南翔北、南峪、娘子关、潘家店、平泉、迁安、前磨头、青县、清河、清河城、清华园、饶阳、任丘、三河县、三家店、沙城、沙河、沙河市、沙岭子、上板城、上板城南、上海西、涉县、深州、十渡、石景山南、顺义、苏州、苏州北、苏州新区、苏州园区、肃宁、绥芬河、唐山北、塘沽、天津北、通州西、王瞳、望都、文安、无锡、无锡东、无锡新区、吴桥、武安、武清、下板城、下花园、下台子、襄阳、襄阳东、小寺沟、辛集、新乐、新杖子、邢台、邢台东、兴隆县、徐水、宣化、延庆、岩会、燕郊、燕山、阳澄湖、阳泉、阳泉北、阳邑、杨村、杨柳青、野三坡、鹰手营子、榆林、玉田县、元氏、枣强、正定机场、涿州、涿州东、百里峡、正定、于家堡、胜芳、霸州西、白沟、白洋淀、兰陵北、伊敏、阿尔山北、临城".Split(new string[] { "、" }, StringSplitOptions.RemoveEmptyEntries));
                CommonString.LstReleaseTickets["16:30"].AddRange("阿城、阿尔山、阿金、阿里河、阿龙山、阿木尔、安达、安广、安家、安平、安图、昂昂溪、敖力布告、八角台、八面城、八面通、八仙筒、巴林、鲅鱼圈、白河、白奎堡、白狼、白泉、白石山、白音胡硕、白音他拉、宝林、宝龙山、宝清、宝泉岭、保康、北安、北票南、北台、背荫河、本溪、本溪湖、笔架山、勃利、博克图、布海、蔡家沟、苍石、草河口、草市、柴岗、柴河、昌图、昌图西、常德、朝阳南、朝阳川、朝阳地、朝阳镇、辰清、陈相屯、晨明、成高子、成吉思汗、赤峰西、楚山、春阳、嵯岗、达家沟、大安、大安北、大巴、大堡、大成、大官屯、大红旗、大虎山、大林、大平房、大青沟、大石桥、大石头、大石寨、大屯、大兴、大兴沟、大雁、大杨树、大营子、带岭、刀尔登、到保、道清、得耳布尔、德伯斯、德惠、德惠西、灯塔、登沙河、滴道、东边井、东戴河、东二道河、东方红、东丰、东海、东津、东京城、东来、东明村、东通化、东辛庄、杜家、对青山、敦化、恩施、二道湾、二龙、二龙山屯、二密河、范家屯、丰乐镇、冯屯、凤凰城、扶余、扶余北、福利屯、抚顺、抚远、富海、富锦、富拉尔基、富裕、嘎什甸子、盖州、盖州西、甘河、甘旗卡、干沟、高桥镇、高山子、大连西、葛根庙、根河、工农湖、公营子、公主岭、公主岭南、沟帮子、孤家子、菇园、古城镇、古莲、官字井、灌水、广宁寺、归流河、郭家店、果松、哈拉海、哈拉苏、海北、海城、海城西、海林、海龙、海伦、海坨子、寒葱沟、寒岭、浩良河、和龙、鹤北、鹤岗、鹤立、黑河、黑水、黑台、横道河子、红花沟、红山、红星、红兴隆、红彦、洪河、呼兰、呼源、呼中、葫芦岛、葫芦岛北、虎林、虎石台、华家、桦林、桦南、换新天、皇姑屯、黄柏、黄花筒、黄泥河、黄松甸、浑河、火连寨、霍林郭勒、鸡东、鸡冠山、鸡西、吉舒、吉文、集安、纪家沟、夹心子、建昌、建三江、建设、江桥、江源、姜家、蛟河、金宝屯、金河、金坑、金山屯、金杖子、金州、锦河、锦州、锦州南、九三、九台、九台南、巨宝、峻德、开安、开通、开原、开原西、康金井、克东、克山、克一河、口前、库都尔、库伦、宽甸、奎山、拉古、拉哈、拉林、兰岗、兰棱、朗乡、老边、老府、老莱、老营、梨树镇、李家、李石寨、里木店、利川、连山关、莲江口、两家、亮甲店、辽阳、辽源、辽中、林海、林口、林源、临江、凌海、凌源、凌源东、刘家店、刘家河、柳河、柳树屯、六合镇、龙嘉、龙江、龙井、龙爪沟、龙镇、鹿道、露水河、漯河、漯河西、旅顺、麻山、马莲河、马林、马桥河、马三家、满归、茂林、帽儿山、梅河口、美溪、孟家岗、米沙子、密山、免渡河、庙岭、明城、明水河、磨刀石、莫尔道嘎、木里图、穆棱、乃林、奈曼、南岔、南芬、南关岭、南口前、南木、南桥、南台、南杂木、嫩江、能家、泥河子、碾子山、宁安、宁家、牛家、牛心台、农安、磐石、泡子、裴德、皮口、偏岭、瓢儿屯、平安、平安镇、平房、平岗、平山、平台、平洋、平庄、平庄南、普兰店、普湾、七里河、七台河、祁家堡、蕲春、前锋、前进镇、前卫、乾安、桥头、秦家、青山、清河门、清原、庆安、庆丰、曲阜、曲阜东、泉阳、绕阳河、热水、三间房、三江口、三十家、三十里堡、三义井、三源浦、沙海、沙河口、沙后所、山城镇、山河屯、山市、上园、尚家、尚志、舍力虎、深井子、神树、沈家、沈阳东、石城、石磷、石岭、石桥子、石人、石人城、石山、石头、石岘、世博园、首山、舒兰、双城堡、双城北、双丰、双河镇、双辽、双鸭山、水洞、水泉、四道湾、四方台、四合永、四平、四平东、松江河、松江镇、松树、松树镇、宋、苏家屯、绥化、绥棱、绥阳、绥中、绥中北、孙家、孙吴、索伦、塔尔气、塔哈、塔河、台安、太平川、太平镇、太阳升、泰安、泰康、泰来、泰山、汤池、汤山城、汤旺河、汤原、洮南、桃山、陶家屯、陶赖昭、天岗、天桥岭、天义、田师府、铁厂、铁力、铁岭、铁岭西、通北、通沟、通化县、通途、通远堡、图里河、图强、吐列毛杜、团结、驼腰岭、瓦房店、瓦房店西、歪头山、湾沟、万发屯、万乐、汪清、王府、王岗、王兆屯、潍坊、苇河、苇子沟、卫东、卫星、渭津、魏杖子、温春、倭肯、沃皮、卧里屯、乌尔旗汗、乌奴耳、乌伊岭、吴家屯、五叉沟、五常、五大连池、五道沟、五家、五棵树、五龙背、五女山、五营、西丰、西岗子、西林、西柳、西麻山、西哲里木、汐子、下城子、下马塘、仙人桥、香兰、襄河、向阳、小河沿、小岭、小市、小扬气、谢家镇、新绰源、新城子、新华、新华屯、新立屯、新立镇、新林、新民、新青、新邱、新松浦、新窝铺、新友谊、新肇、兴城、兴凯、兴隆店、兴隆镇、杏树、杏树屯、熊岳城、徐家、许家屯、鸭园、牙克石、亚布力、亚布力南、烟筒山、烟筒屯、羊草、敖汉、阳岔、杨岗、杨树岭、杨杖子、姚家、姚千户屯、叶柏寿、一间堡、一面坡、一面山、伊春、伊尔施、伊拉哈、伊林、伊图里河、依安、义县、益阳、银浪、迎春、营城子、营口、营口东、永安乡、友好、余粮堡、榆树、榆树台、榆树屯、玉泉、元宝山、咋子、扎赉诺尔西、扎兰屯、扎鲁特、张家界、张维屯、章党、章古台、彰武、长春南、长甸、长岭子、长山屯、长汀镇、赵光、肇东、镇赉、镇西、治安、周家、周家屯、周水子、朱家沟、淄博、松原北、弓棚子、三井子、沈阳南、本溪新城、南芬北、通远堡西、凤城东、五龙背东、蛟河西、威虎岭北、大石头南、安图西、延吉西、图们北、珲春、青龙、靖宇、三道湖、抚松、丹东西、东港北、北井子、大孤山、青堆、庄河北、花园口、城子坦、皮口、广宁寺、双吉、珠斯花、查干湖、双阳、伊通、建安、高花、近海、台安南、盘山".Split(new string[] { "、" }, StringSplitOptions.RemoveEmptyEntries));
                CommonString.LstReleaseTickets["17:00"].AddRange("宝华山、栟茶、常州、常州北、丹徒、丹阳、丹阳北、东台、阜宁、桂林、桂林北、海安县、怀化、淮安、建湖、江都、江宁、姜堰、晋城、晋城北、荆门、荆州、景德镇、句容西、溧水、溧阳、龙岩、娄底、南通、南阳、戚墅堰、潜江、如东、如皋、上饶、邵阳、沭阳、泗洪、泗阳、随州、泰州、天门南、瓦屋山、仙林、盐城、扬州、洋河、宜兴、鹰潭、永州、枣阳、长治、长治北、镇江、镇江南、江宁西、桂林西".Split(new string[] { "、" }, StringSplitOptions.RemoveEmptyEntries));
                CommonString.LstReleaseTickets["17:30"].AddRange("安化、安龙、安仁、百色、北海、北流、册亨、岑溪、郴州、郴州西、辰溪、崇左、慈利、砀山、道州、德安、邓州、低庄、定南、东安东、东海县、东明县、东乡、防城港北、分宜、丰城、丰城南、扶绥、抚州、富川、高安、高平、巩义、巩义南、共青城、贵港、贵溪、桂平、韩城、汉寿、合浦、河唇、贺州、鹤壁、鹤壁东、横峰、衡南、衡山、衡山西、湖口、化州、黄口、会同、获嘉、吉首、济源、嘉峰、江华、江永、焦作、焦作东、金城江、进贤、靖州、开封、来宾、来宾北、濑湍、兰考、乐平市、耒阳、耒阳西、冷水江东、黎塘、澧县、醴陵、醴陵东、连云港、连云港东、涟源、廉江、临澧、临湘、临颍、灵宝、灵宝西、零陵、龙南、龙市、芦溪、庐山、鲁山、陆川、鹿寨、路口铺、罗城、麻阳、茂名、猛洞河、汨罗、汨罗东、渑池、渑池南、民权、南城、南丹、南丰、南召、内乡、宁陵县、宁明、宁乡、彭泽、邳州、平果、平南南、凭祥、祁东、祁阳、钦州、钦州东、沁阳、渠旧、渠黎、全州南、容县、融安、融水、汝阳、汝州、瑞昌、瑞金、三江县、三门峡、三门峡南、三门峡西、韶山、神木、石门县北、双牌、松桃、遂溪、泰和、汤阴、唐河、藤县、田东、田林、田阳、亭亮、通道、桐柏、铜仁、万年、卫辉、文地、梧州、梧州南、西峡、峡江、夏石、夏邑县、湘潭、湘乡、襄垣、向塘、新安县、新干、新化、新晃、新沂、新余、新余北、信丰、兴安北、兴国、兴业、兴义、修武、徐州、徐州东、许昌、许昌东、溆浦、牙屯堡、炎陵、偃师、阳城、宜春西、宜州、弋阳、义马、永福南、永修、攸县、攸县南、于都、余江、虞城县、玉林、玉山、月山、樟树、樟树东、长葛、长垣、镇平、资溪、鹰潭北、抚州东、进贤南、玉山南、湘潭北、韶山南、娄底南、邵阳北、新化南、溆浦南、怀化南、新晃西、芷江、大余、三江南、恭城、鹿寨北、南阳寨、黄河景区、武陟、修武西、南宁西、隆安东、田东北、会昌北、德保、靖西、阳朔、邵东、双峰北、博白、砀山南、永城北、萧县北、宾阳、柳江、瑞昌西".Split(new string[] { "、" }, StringSplitOptions.RemoveEmptyEntries));
                CommonString.LstReleaseTickets["18:00"].AddRange("安口窑、安陆、安溪、巴东、巴山、白河东、白河县、白水江、蚌埠、蚌埠南、博山、博兴、蔡家坡、曹县、昌乐、城固、城阳、赤壁、赤壁北、滁州、滁州北、磁窑、大荔、大冶北、大竹园、丹凤、当阳、定陶、定远、东营、鄂州、鄂州东、费县、凤县、凤阳、凤州、福安、福鼎、福清、富县、富县东、甘泉、甘泉北、高密、高滩、葛店南、古田、谷城、固始、固镇、冠豸山、光泽、海湾、海阳、涵江、汉川、汉阴、合阳、贺胜桥东、横沟桥东、红安、红安西、宏庆、花湖、花山南、花园、华容、华容东、华容南、华山、华山北、淮北、淮滨、黄冈、黄冈东、黄冈西、黄陵、黄石、黄石北、黄石东、黄州、徽县、嘉善、嘉祥、建宁县北、建瓯、建始、建阳、将乐、胶州、胶州北、角美、界首市、晋江、京山、靖边、莒南、莒县、巨野、鄄城、来舟、莱芜东、莱西、蓝村、连江、梁山、聊城、临清、临淄、灵璧、龙山镇、陇县、芦潮港、罗山、罗源、略阳、马鞍山、毛坝、毛坝关、米脂、勉县、庙山、闽清、明港、明港东、明光、南仇、南湖东、南靖、南平、南平南、宁德、平邑、平原、莆田、蒲城、蒲城东、普安、岐山、千河、千阳、秦岭、青龙山、青州市、清涧县、泉州、泉州东、确山、乳山、三明、三明北、三原、山坡东、商城、商洛、商南、上杭、邵武、沈丘、石泉县、顺昌、泗水、泗县、松江、松滋、绥德、遂平、孙镇、台前、太姥山、泰宁、郯城、汤逊湖、桃村、滕州、滕州东、天门、潼关、土地堂东、万源、韦庄、渭南、渭南北、渭南南、文登、涡阳、乌龙泉南、吴堡、五莲、武当山、武功、武穴、武夷山、西安南、西平、西乡、浠水、霞浦、仙桃西、咸宁、咸宁北、咸宁东、咸宁南、咸阳、咸阳秦都、项城、小河镇、孝感、孝感北、新县、兴平、宿州、宿州东、宣汉、旬阳、旬阳北、阎良、兖州、砚川、燕子砭、阳谷、阳平关、阳新、杨陵、杨陵南、沂南、沂水、宜城、应城、永安、永定、永乐店、永泰、尤溪、禹城、云梦、云霄、郓城、枣庄、枣庄西、柞水、张桥、章丘、漳平、漳浦、漳州、漳州东、长汀、长阳、诏安、镇安、枝城、枝江北、纸坊东、钟家村、钟祥、诸城、子长、子洲、紫阳、棕溪、邹城、左岭、晏城、仙游、惠安、礼泉、乾县、永寿、长武、彬县、息县、两当、开阳、南江、平度、坪上、陵城、临邑、商河、阳信、滨州、利津南、东营南、长汀南、古田会址、黄陵南、虢镇、光山、枣庄东、枫林、太和北、阿房宫、鄠邑、佛坪、洋县西、城固北、宁强南".Split(new string[] { "、" }, StringSplitOptions.RemoveEmptyEntries));
            }
            else
            {
                try
                {
                    string strStation = WebClientExt.GetHtml(CommonString.StrUpdateURL + "config/qss.js?t=" + DateTime.Now.Ticks, (double)5);
                    if (!string.IsNullOrEmpty(strStation) && !strStation.Contains("var citys = {"))
                    {
                        strStation = null;
                    }
                    if (string.IsNullOrEmpty(strStation))
                    {
                        strStation = WebClientExt.GetHtml("https://kyfw.12306.cn/otn/resources/js/query/qss.js", "", CommonString.StrLocalComIP, "", 3, 5);
                    }
                    if (!string.IsNullOrEmpty(strStation) && strStation.Contains("var citys = {"))
                    {
                        strStation = CommonMethod.SubString(strStation, "var citys = {", "}");
                        string[] strTmp = strStation.Replace("\":\"", "|").Replace("\"", "").Replace(",", "").Trim()
                            .Split(new string[] { "\n", "\t" }, StringSplitOptions.RemoveEmptyEntries);
                        if (strTmp != null && strTmp.Length > 0)
                        {
                            if (CommonString.LstReleaseTickets == null)
                            {
                                CommonString.LstReleaseTickets = new ConcurrentDictionary<string, List<string>>();
                            }
                            string[] strTT = null;
                            foreach (string str in strTmp)
                            {
                                //萍乡北|09:00/11:00/14:00
                                if (str.HorspoolIndex("|") < 0)
                                    continue;
                                strTT = str.Split(new string[] { "|" }, StringSplitOptions.RemoveEmptyEntries);
                                if (strTT != null && strTT.Length == 2)
                                {
                                    strTT[1] = strTT[1].TrimStart('0').Trim();
                                    strTT[0] = strTT[0].Trim();
                                    if (strTT[1].Contains("/"))
                                        strTT[1] = CommonMethod.SubString(strTT[1], "", "/");
                                    if (!CommonString.LstReleaseTickets.ContainsKey(strTT[1]))
                                        CommonString.LstReleaseTickets[strTT[1]] = new List<string>();
                                    if (!CommonString.LstReleaseTickets[strTT[1]].Contains(strTT[0]))
                                    {
                                        CommonString.LstReleaseTickets[strTT[1]].Add(strTT[0]);
                                    }
                                }
                            }
                            strTT = null;
                        }
                        strTmp = null;
                    }
                    strStation = null;
                }
                catch { }
            }
            result = CommonString.LstReleaseTickets != null && CommonString.LstReleaseTickets.Count > 0;
            return result;
        }

        private static void initGroupStation(bool isLocal = true, string strUrl = "")
        {
            string strTmp = WebClientExt.GetHtml((string.IsNullOrEmpty(strUrl) ? CommonString.StrUpdateURL : strUrl).TrimEnd('/') + "/同城.txt", (double)5);
            string[] strTT = strTmp.Split(new string[] { "\r\n" }, StringSplitOptions.RemoveEmptyEntries);
            if (strTT != null && strTT.Length > 0)
            {
                List<KeyValuePair<string, List<string>>> lstTmp = new List<KeyValuePair<string, List<string>>>();
                string ss = string.Empty;
                List<string> lst = new List<string>();
                string[] str = null;
                foreach (string item in strTT)
                {
                    ss = item.Trim();
                    lst = new List<string>();
                    str = ss.Split(new string[] { "|" }, StringSplitOptions.RemoveEmptyEntries);
                    for (int i = 0; i < str.Length; i++)
                    {
                        if (CommonString.Stations.Exists(p => p.Name.Equals(str[i])))
                        {
                            lst.Add(str[i]);
                        }
                        else
                        {
                            CommonMSG.AddMSG(string.Format("同城站点[{0}]已失效，请修正客服修正！", str[i]));
                        }
                    }
                    lstTmp.Add(new KeyValuePair<string, List<string>>(ss, lst));
                }
                if (lstTmp != null && lstTmp.Count > 0)
                {
                    CommonString.lstGroupStation = lstTmp;
                }
                ss = null;
                lst = null;
                str = null;
            }
            strTT = null;
            strTmp = null;
        }

        private static void InitOthers()
        {
            try
            {
                CommonString.SeatTypeNames = new string[]
			{
				"硬卧",
				"硬座",
				"软座",
				"软卧",
				"一等座",
				"二等座",
				"商务座",
				"特等座",
				"高级软卧",
				"动卧",
				"高级动卧",
                //"无座",
                //"其他"
			};

                CommonString.LstQueryTicketType = new ConcurrentDictionary<string, string>();
                /*"yz_num": "无",
                "rz_num": "--",
                "yw_num": "2",
                "rw_num": "11",
                "gr_num": "7",
                "zy_num": "--",
                "ze_num": "--",
                "tz_num": "--",
                "gg_num": "--",
                "yb_num": "--",
                "wz_num": "无",
                "qt_num": "--",
                "swz_num": "--"*/
                CommonString.LstQueryTicketType["1"] = "yz_num";
                CommonString.LstQueryTicketType["2"] = "rz_num";
                CommonString.LstQueryTicketType["3"] = "yw_num";
                CommonString.LstQueryTicketType["4"] = "rw_num";
                CommonString.LstQueryTicketType["6"] = "gr_num";
                CommonString.LstQueryTicketType["9"] = "swz_num";
                CommonString.LstQueryTicketType["A"] = "gr_num";
                CommonString.LstQueryTicketType["F"] = "rw_num";
                CommonString.LstQueryTicketType["M"] = "zy_num";
                CommonString.LstQueryTicketType["O"] = "ze_num";
                CommonString.LstQueryTicketType["P"] = "tz_num";
                CommonString.LstQueryTicketType["无座"] = "wz_num";

                CommonString.SeatTypes = new List<KeyValueEntity>()
			{
				new KeyValueEntity("棚车", "0"),
				new KeyValueEntity("硬座", "1"),//yz_num
				new KeyValueEntity("软座", "2"),//rz_num
				new KeyValueEntity("硬卧", "3"),//yw_num
				new KeyValueEntity("软卧", "4"),//rw_num
				new KeyValueEntity("包厢硬卧", "5"),
				new KeyValueEntity("高级软卧", "6"),//gr_num
				new KeyValueEntity("一等软座", "7"),
				new KeyValueEntity("二等软座", "8"),
				new KeyValueEntity("商务座", "9"),//swz_num
				new KeyValueEntity("高级动卧", "A"),//gr_num
				new KeyValueEntity("混编硬座", "B"),
				new KeyValueEntity("混编硬卧", "C"),
				new KeyValueEntity("包厢软座", "D"),
				new KeyValueEntity("特等软座", "E"),
				new KeyValueEntity("动卧", "F"),//rw_num
				new KeyValueEntity("二人软包", "G"),
				new KeyValueEntity("一人软包", "H"),
				new KeyValueEntity("一等双软", "I"),
				new KeyValueEntity("二等双软", "J"),
				new KeyValueEntity("混编软座", "K"),
				new KeyValueEntity("混编软卧", "L"),
				new KeyValueEntity("一等座", "M"),//zy_num
				new KeyValueEntity("二等座", "O"),//ze_num
				new KeyValueEntity("特等座", "P"),//tz_num
				new KeyValueEntity("观光座", "Q"),
				new KeyValueEntity("一等包座", "S"),
                //new KeyValueEntity("无座", "W"),//wz_num
                //new KeyValueEntity("其他", "*")
			};
                CommonString.SeatTypeLocations = new List<KeyValuePair<string, int>>()
                {
                    //32 商务座 25 特等座 31 一等座 30二等座 21高级软卧
                    //23软卧动卧 28硬卧 24软座 29硬座 28无座 22其他 
				    new KeyValuePair<string, int>("硬座", 29),//yz_num
				    new KeyValuePair<string, int>("软座", 24),//rz_num
				    new KeyValuePair<string, int>("硬卧", 28),//yw_num
				    new KeyValuePair<string, int>("软卧", 23),//rw_num
				    new KeyValuePair<string, int>("高级软卧", 21),//gr_num
				    new KeyValuePair<string, int>("商务座", 32),//swz_num
				    new KeyValuePair<string, int>("动卧", 23),//rw_num
				    new KeyValuePair<string, int>("一等座", 31),//zy_num
				    new KeyValuePair<string, int>("二等座", 30),//ze_num
				    new KeyValuePair<string, int>("特等座", 25),//tz_num
				    new KeyValuePair<string, int>("其他", 22),
				    new KeyValuePair<string, int>("高级动卧", 21),//gr_num
                };
                CommonString.AllSeatTypes = new ConcurrentDictionary<string, string>();
                foreach (var item in CommonString.SeatTypes)
                {
                    CommonString.AllSeatTypes[item.Value] = item.Key;
                }
                /*0，随机
                3，上铺
                2，中铺
                1，下铺*/
                CommonString.IDCardTypes = new List<KeyValueEntity>()
			{
				new KeyValueEntity("二代身份证", "1"),
				new KeyValueEntity("一代身份证", "2"),
				new KeyValueEntity("港澳通行证", "C"),
				new KeyValueEntity("台湾通行证", "G"),
				new KeyValueEntity("护照", "B"),
				new KeyValueEntity("外国人居留证", "H")
			};
                //CommonString.IDCardTypeNames = new List<string>() { "二代身份证", "一代身份证", "港澳通行证", "台湾通行证", "护照" };
                CommonString.UserTypes = new List<KeyValueEntity>()
			{
				new KeyValueEntity("成人票", "1"),
				new KeyValueEntity("儿童票", "2"),
				new KeyValueEntity("学生票", "3"),
				new KeyValueEntity("残军票", "4")
			};
                CommonString.UserTypeNames = new List<string>() { "成人票", "儿童票", "学生票", "残军票" };
            }
            catch { }
        }
        #endregion

        #region 余票相关操作

        public static List<KeyValuePair<string, int>> GetYuPiaoInfo(string strYuPiaoInfo, List<string> lstTypes, bool isNoControlSeat = false)
        {
            //O*****0062O*****3024M*****0037
            KeyValuePair<string, int>[] lstTicket = new KeyValuePair<string, int>[lstTypes.Count + 1];
            List<KeyValuePair<string, int>> lstOtherTicket = new List<KeyValuePair<string, int>>();
            int hasValue = 0;
            string key;
            while (!string.IsNullOrEmpty(strYuPiaoInfo) && strYuPiaoInfo.Length > 0)
            {
                if (int.TryParse(strYuPiaoInfo.Substring(6, 4), out hasValue) && hasValue > 0)
                {
                    key = CommonString.AllSeatTypes[strYuPiaoInfo.Substring(0, 1)];
                    //key = CommonString.SeatTypes.Find(mm => mm.Value == strYuPiaoInfo.Substring(0, 1));
                    if (hasValue > 0 && hasValue < 3000)
                    {
                        if (!string.IsNullOrEmpty(key))
                        {
                            if (lstTypes.Contains(key))
                            {
                                lstTicket[lstTypes.IndexOf(key)] = new KeyValuePair<string, int>(key, hasValue);
                                //lstTmp[key.Key] = hasValue;
                            }
                            else
                            {
                                if (isNoControlSeat)
                                {
                                    if (!lstOtherTicket.Exists(p => p.Key.Equals(key)))
                                    {
                                        lstOtherTicket.Add(new KeyValuePair<string, int>(key, hasValue));
                                    }
                                }
                            }
                        }
                    }
                    else if (hasValue > 3000)
                    {
                        lstTicket[lstTypes.Count] = new KeyValuePair<string, int>(key, hasValue);
                        //lstTmp["无座"] = hasValue - 3000;
                    }
                }
                strYuPiaoInfo = strYuPiaoInfo.Substring(10);
            }
            strYuPiaoInfo = null;
            lstTypes = null;
            lstOtherTicket.AddRange(lstTicket);
            lstTicket = null;
            lstOtherTicket.RemoveAll(p => p.Value <= 0);
            //lstTicket = lstTicket.OrderByDescending(p => p.Value).ToList();
            return lstOtherTicket;
        }

        public static string GetYuPiaoInfo(string strYuPiaoInfo
            , ref ConcurrentDictionary<string, int> lstYuPiao, ref List<string> lstSeatType)
        {
            string strTicket = "";
            try
            {
                lstYuPiao = new ConcurrentDictionary<string, int>();
                lstSeatType = new List<string>();
                string strKey = string.Empty;
                int hasValue = 0;
                KeyValueEntity key;
                while (!string.IsNullOrEmpty(strYuPiaoInfo) && strYuPiaoInfo.Length > 0)
                {
                    strKey = strYuPiaoInfo.Substring(0, 1);
                    if (int.TryParse(strYuPiaoInfo.Substring(6, 4), out hasValue) && hasValue > 0)
                    {
                        key = CommonString.SeatTypes.Find(mm => mm.Value == strKey);
                        if (!string.IsNullOrEmpty(key.Value) && !lstSeatType.Contains(key.Value))
                            lstSeatType.Add(key.Value);
                        if (hasValue > 0 && hasValue < 3000)
                        {
                            if (!string.IsNullOrEmpty(key.Key))
                            {
                                lstYuPiao[key.Key] = hasValue;
                            }
                        }
                        else if (hasValue > 3000)
                        {
                            lstYuPiao["无座"] = hasValue - 3000;
                        }
                    }
                    strYuPiaoInfo = strYuPiaoInfo.Substring(10);
                }
                strYuPiaoInfo = null;
                strKey = null;
                if (lstYuPiao.Count > 0)
                {
                    foreach (KeyValuePair<string, int> item in lstYuPiao)
                    {
                        if (!item.Key.Equals("无座"))
                            strTicket += string.Format("{0}:{1}张,", item.Key, item.Value);
                    }
                    if (lstYuPiao.ContainsKey("无座"))
                        strTicket += string.Format("无座:{0}张", lstYuPiao["无座"]);
                    else
                        strTicket = strTicket.TrimEnd(',');
                }
            }
            catch (Exception oe)
            {
                Log.WriteError("GetYuPiaoInfo出错", oe);
            }
            if (string.IsNullOrEmpty(strTicket))
                strTicket = "--";
            return strTicket;
        }

        public static string GetYuPiaoStr(string strYuPiaoInfo)
        {
            string strTicket = "";
            try
            {
                var lstType = new ConcurrentDictionary<string, int>();
                var lstTypeName = new List<string>();
                strTicket = GetYuPiaoInfo(strYuPiaoInfo, ref lstType, ref lstTypeName);
                lstType = null;
                lstTypeName = null;
            }
            catch (Exception oe)
            {
                Log.WriteError("GetYuPiaoStr出错", oe);
            }
            return strTicket;
        }

        #endregion

        #region 其他

        [DllImport("user32.dll")]
        public static extern bool FlashWindow(IntPtr hWnd, bool bInvert);

        /*.NET Version	Clr.dll Build 4.0.30319.<Build>)
.NET 4.0	4.0.30319.0 to 4.0.30319.17000
.NET 4.5	4.0.30319.17001 to 4.0.3019.18400
.NET 4.5.1	4.0.30319.18401 to 4.0.30319.34000
.NET 4.5.2	From 4.0.30319.34000*/
        public static bool IsHignNetVersion(int baseVersionMain = 4, string baseVersionSec = ".0")
        {
            bool result = false;
            if (Environment.Version.Major == 4)
            {
                result = Environment.Version.Build == 30319 && Environment.Version.Revision > 17000;
            }
            else
            {
                result = Environment.Version.Major > 4;
            }
            return result;
        }

        public static void GetDeviceIdCookie(ref string strCookie)
        {
            if (!strCookie.Contains("RAIL_DEVICEID="))
            {
                strCookie = string.Format(";{0};{1}", DeviceCookieHelper.GetRandomDeviceCookie().TrimStart(';').TrimEnd(';'), strCookie.TrimStart(';'));
            }
        }

        public static string GetRandomQueryCookie()
        {
            //return "_jc_save_fromStation=%u5317%u4EAC%2CBJP; _jc_save_toStation=%u4E0A%u6D77%2CSHH; _jc_save_fromDate=2014-12-22; _jc_save_toDate=2014-12-22; _jc_save_wfdc_flag=dc";
            return string.Format("JSESSIONID={0}B448{1}3C{0}11{1}; _jc_save_detail=true; _jc_save_showZtkyts=true; BIGipServerotn=1072693770.{0}.0000; _jc_save_fromStation=%u8944%u9633%2CXFN; _jc_save_toStation=%u5E7F%u5DDE%u5357%2CIZQ; _jc_save_fromDate=2015-02-05; _jc_save_toDate=2015-02-04; _jc_save_wfdc_flag=dc; "
                , CommonString.RndTmp.Next(0, 999999).ToString("000000"), CommonString.RndTmp.Next(0, 888888).ToString("000000"));
        }

        public static string GetQueryCookie(bool isMobile, string strCookie)
        {
            string result = //isMobile ? strCookie.Replace("0000", CommonString.RndTmp.Next(1, 10000).ToString("0000")) : strCookie;
                strCookie.Replace("0000", CommonString.RndTmp.Next(1, 10000).ToString("0000"));
            //if (!strCookie.Contains("_jc_save_wfdc_flag=dc;"))
            //{
            //    //%u5317%u4EAC%2CBJP
            //    result += string.Format(" current_captcha_type=C;_jc_save_fromStation=%u5317%u4eac%u897f%2CBXP; _jc_save_toStation=%u62c9%u8428%2CLSO; _jc_save_fromDate={0}; _jc_save_toDate={0}; _jc_save_wfdc_flag=dc;"
            //        , CommonString.serverTime.Date.ToString("yyyy-MM-dd"));
            //}
            return result;
        }

        public static string GetCompleteCookie(bool isMobile, string strCookie, AttentionItem item)
        {
            if (!strCookie.Contains("RAIL_DEVICEID"))
            {
                //JSESSIONID=0A02F015607E9467E70053D57D5A06F56902A02F2D; __NRF=54E7362CBF10DBA3668C1F7CE759BB4B;  BIGipServerotn=368050698.24610.0000;
                // _jc_save_zwdch_fromStation=%u5317%u4EAC%2CBJP; _jc_save_fromStation=%u6B66%u6C49%2CWHN; _jc_save_toStation=%u4E0A%u6D77%2CSHH; 
                //_jc_save_fromDate=2016-09-10; _jc_save_toDate=2016-09-09;
                //_jc_save_wfdc_flag=dc; _jc_save_zwdch_cxlx=0; _jc_save_detail=true; _jc_save_showIns=true;

                //return string.Format("{1}; _jc_save_fromStation={2}; _jc_save_toStation={3}; _jc_save_fromDate={4}; _jc_save_toDate={5}; _jc_save_wfdc_flag=dc;_jc_save_zwdch_cxlx=0; _jc_save_detail=true;_jc_save_showIns=true"
                //    , CommonString.serverTime.Date.AddDays(CommonString.RndTmp.Next(0, 10)).ToString("yyyy-MM-dd"), strCookie.TrimEnd(';'));

                //Cookie: route=91036359bb8a8a461c164a04f8f1250b252; fp_ver=4.6.1; RAIL_EXPIRATION=1505340187771; RAIL_DEVICEID=1MCdo62-OUH5hyrsBuc3mRh45Y6u5AuMaxblpRJYe1ugHnxaplrpAiVZVuo1krwsrLJMgorbIpkmhDHLOewfmCzFbpg7eawkcdzFJ670bXmL7rZVEatjAutXFZdSobt6IgXazJ-9r63iBdW4UcKlvUhNW3i9mQ98oX;
                return string.Format("{0}; _jc_save_zwdch_fromStation={1}; _jc_save_fromStation={1}; _jc_save_toStation={2}; _jc_save_fromDate={3}; _jc_save_toDate={3};_jc_save_wfdc_flag=dc; _jc_save_zwdch_cxlx=0; _jc_save_detail=true; _jc_save_showIns=true;route=91036359bb8a8a461c164a04f8f1250b252; fp_ver=4.6.1; {4};"
                    , strCookie.TrimEnd(';')
                    , item != null ? HttpUtility.UrlEncodeUnicode(item.FromStation.Name + "," + item.FromStation.Code) : ""
                    , item != null ? HttpUtility.UrlEncodeUnicode(item.ToStation.Name + "," + item.ToStation.Code) : ""
                    , item != null ? item.Date.ToString("yyyy-MM-dd") : CommonString.serverTime.ToString("yyyy-MM-dd")
                    , DeviceCookieHelper.GetRandomDeviceCookie()
                    );
            }
            return strCookie;
        }

        public static string GetRepeatCookie(string strCookie)
        {
            for (int i = 0; i < CommonString.RndTmp.Next(1, 10); i++)
            {
                strCookie += " ;";
            }
            return strCookie;
        }

        public static int CompareTimeStr(NoticeArgs d1, NoticeArgs d2)
        {
            if (d1 == null || d2 == null)
                return -1;
            try
            {
                return d2.dtLog.CompareTo(d1.dtLog);
            }
            catch
            {
                return -1;
            }
        }

        public static NameValueCollection GetPublicCollect(string strInstanceID = "")
        {
            NameValueCollection pubCollect = new NameValueCollection();
            pubCollect.Add("x-wl-app-version", NewTicket.Mobile.API.StrAppVersion);
            pubCollect.Add("x-wl-platform-version", NewTicket.Mobile.API.StrAppPlatformVersion);
            //pubCollect.Add("Cookie2", "$Version=1");
            pubCollect.Add("Origin", "file://");
            if (!string.IsNullOrEmpty(strInstanceID))
                pubCollect.Add("WL-Instance-Id", strInstanceID);
            return pubCollect;
        }

        static int[] wQuan = { 7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2 };
        static string checkWei = "10X98765432";

        public static bool CheckCardNO(string id)
        {
            bool result = true;
            try
            {
                if (string.IsNullOrEmpty(id) || !System.Text.RegularExpressions.Regex.IsMatch(id, "^\\d{17}(\\d|X|x)$"))
                {
                    result = false;
                }
                else
                {
                    string number17 = id.Substring(0, 17);
                    string number18 = id.Substring(17);
                    int sum = 0;
                    for (int i = 0; i < 17; i++)
                    {
                        sum = sum + Convert.ToInt32(number17[i].ToString()) * wQuan[i];
                    }
                    if (!number18.Equals(checkWei[sum % 11].ToString(), StringComparison.OrdinalIgnoreCase))
                    {
                        result = false;
                    }
                }
            }
            catch (Exception oe)
            {
                Log.WriteError(oe);
            }
            id = null;
            return result;
        }

        public static bool HasSameTrain(List<string> lstOld, List<string> lstNew)
        {
            bool result = false;
            try
            {
                if (lstOld != null && lstOld.Count > 0
                   && lstNew != null && lstNew.Count > 0)
                {
                    foreach (var item in lstOld)
                    {
                        if (lstNew.Contains(item))
                        {
                            result = true;
                            break;
                        }
                    }
                }
            }
            catch (Exception oe)
            {
                Log.WriteError("HasSameTrain出错", oe);
            }
            return result;
        }

        public static List<string> GetAllTrain(List<string> lstOld, List<string> lstNew)
        {
            try
            {
                if (lstOld != null && lstOld.Count > 0)
                {
                    lstOld.RemoveAll(p => lstNew.Contains(p));
                    lstNew.AddRange(lstOld);
                }
            }
            catch (Exception oe)
            {
                Log.WriteError(oe);
            }
            return lstNew;
        }

        #endregion

        #region 时间相关

        public static bool isZhengDian(DateTime dtTime)
        {
            return dtTime.Minute > 58 || dtTime.Minute < 2 || (dtTime.Minute > 28 && dtTime.Minute < 32);
        }

        public static string GetSuspendTime(DateTime dtStart)
        {
            string strTime = string.Empty;
            TimeSpan ts = new TimeSpan(DateTime.Now.Ticks - dtStart.Ticks);
            if (ts.Hours > 0)
            {
                strTime += string.Format("{0}小时", ts.Hours);
            }
            if (ts.Minutes > 0)
            {
                strTime += string.Format("{0}分", ts.Minutes);
            }
            if (ts.Seconds > 0)
            {
                strTime += string.Format("{0}秒", ts.Seconds);
            }
            if (ts.Milliseconds > 0)
            {
                strTime += string.Format("{0}毫秒", ts.Milliseconds);
            }
            return strTime;
        }

        public static string GetTimeStr(int second)
        {
            string strTemp = string.Empty;
            TimeSpan ts = new TimeSpan(0, 0, second);
            if (ts.Hours > 0)
            {
                strTemp += string.Format("{0}时", ts.Hours);
            }
            if (ts.Minutes > 0 || !string.IsNullOrEmpty(strTemp))
            {
                strTemp += string.Format("{0}分", ts.Minutes);
            }
            if (ts.Seconds > 0)
            {
                strTemp += string.Format("{0}秒", ts.Seconds);
            }
            return strTemp;
        }

        public static int NWebStartHour
        {
            get
            {
                if (DtOperateStart % 100 > 0)
                    return BoxUtil.GetInt32FromObject(DtOperateStart.ToString().PadLeft(6, '0').Substring(0, 2)) + 1;
                else
                    return BoxUtil.GetInt32FromObject(DtOperateStart.ToString().PadLeft(6, '0').Substring(0, 2));
            }
        }

        public static int NWebEndHour
        {
            get
            {
                if (DtOrderOperateEnd % 100 > 0)
                    return BoxUtil.GetInt32FromObject(DtOrderOperateEnd.ToString().PadLeft(6, '0').Substring(0, 2)) + 1;
                else
                    return BoxUtil.GetInt32FromObject(DtOrderOperateEnd.ToString().PadLeft(6, '0').Substring(0, 2));
            }
        }

        public static DateTime DtWebStartDate(bool isNext = false)
        {
            var dtTmp = CommonString.serverTime;
            if (isNext)
            {
                dtTmp = dtTmp.AddDays(1);
            }
            var strTmp = DtOperateStart.ToString().PadLeft(6, '0');
            strTmp = dtTmp.ToString("yyyy-MM-dd") + " " + strTmp.Substring(0, 2) + ":" + strTmp.Substring(2, 2) + ":" + strTmp.Substring(4);
            dtTmp = BoxUtil.GetDateTimeFromObject(strTmp);
            return dtTmp;
        }

        public static DateTime DtWebEndDate(bool isNext = false)
        {
            var dtTmp = CommonString.serverTime;
            if (isNext)
            {
                dtTmp = dtTmp.AddDays(1);
            }
            var strTmp = DtOrderOperateEnd.ToString().PadLeft(6, '0');
            strTmp = dtTmp.ToString("yyyy-MM-dd") + " " + strTmp.Substring(0, 2) + ":" + strTmp.Substring(2, 2) + ":" + strTmp.Substring(4);
            dtTmp = BoxUtil.GetDateTimeFromObject(strTmp);
            return dtTmp;
        }

        public static int DtOperateStart = 55945;

        public static int DtOrderOperateEnd = 230000;

        public static int DtLoginOperateEnd = 235959;

        public static bool isCanOperate(bool isLogin = false, bool isNetCheck = true)
        {
            if (CommonReg.NowUserIsExpired)
                return false;

            int nowTime = CommonString.serverTime.Hour * 10000 + CommonString.serverTime.Minute * 100 + CommonString.serverTime.Second;
            if (isLogin)
            {
                return nowTime >= DtOperateStart && nowTime <= DtLoginOperateEnd;
            }
            else
            {
                return nowTime >= DtOperateStart && nowTime <= DtOrderOperateEnd;
            }
            //if (CommonString.serverTime.Hour == 6)
            //{
            //    if (CommonString.serverTime.Minute == 59 && CommonString.serverTime.Second >= 30)
            //    {
            //        return true;
            //    }
            //}
            //else
            //{
            //    if (CommonString.serverTime.Hour >= 7)
            //    {
            //        return isLogin ? (CommonString.serverTime.Hour < 23 || (CommonString.serverTime.Hour == 23 && CommonString.serverTime.Minute <= 59))
            //            : CommonString.serverTime.Hour < 23;
            //    }
            //}
            //return false;
        }

        public static DateTime GetDateTime(string gmt)
        {
            DateTime result = DateTime.MinValue;
            try
            {
                string text = "";
                if (gmt.IndexOf("+0") != -1)
                {
                    gmt = gmt.Replace("GMT", "");
                    text = "ddd, dd MMM yyyy HH':'mm':'ss zzz";
                }
                if (gmt.ToUpper().IndexOf("GMT") != -1)
                {
                    text = "ddd, dd MMM yyyy HH':'mm':'ss 'GMT'";
                }
                if (text != "")
                {
                    result = DateTime.ParseExact(gmt, text, CultureInfo.InvariantCulture, DateTimeStyles.AdjustToUniversal);
                    result = result.ToLocalTime();
                }
                else
                {
                    result = Convert.ToDateTime(gmt);
                }
            }
            catch
            {
            }
            return result;
        }
        #endregion

        #region 共享方法

        /// <summary>
        /// Base64加密，解密方法
        /// </summary>
        /// <param name="strTmp">输入字符串</param>
        /// <param name="isEncode">true-加密,false-解密</param>
        public static string Base64(string strTmp, bool isEncode = false)
        {
            try
            {
                if (isEncode)
                {
                    return System.Convert.ToBase64String(System.Text.Encoding.UTF8.GetBytes(strTmp));
                }
                else
                {
                    return System.Text.Encoding.UTF8.GetString(System.Convert.FromBase64String(strTmp));
                }
            }
            catch (Exception oe)
            {
                return "";
            }
        }

        public static void ShutDown()
        {
            try
            {
                CommonMethod.ExecCmd("shutdown -s -t 5 ");
            }
            catch (Exception oe)
            {
                CommonMethod.Exit();
            }
        }

        public static void ClearCookie()
        {
            try
            {
                var process = new Process
                   {
                       StartInfo =
                       {
                           FileName = "RunDll32.exe",
                           Arguments = "InetCpl.cpl,ClearMyTracksByProcess 255",
                           UseShellExecute = false,
                           RedirectStandardInput = true,
                           RedirectStandardOutput = true,
                           RedirectStandardError = true,
                           CreateNoWindow = false
                       }
                   };
                process.Start();
                process.WaitForExit();
            }
            catch
            {
            }
        }

        public static string ExecCmd(string str)
        {
            //process用于调用外部程序
            using (System.Diagnostics.Process p = new System.Diagnostics.Process())
            {
                //调用cmd.exe
                p.StartInfo.FileName = "cmd.exe";
                //不显示程序窗口
                p.StartInfo.CreateNoWindow = true;
                //是否指定操作系统外壳进程启动程序
                p.StartInfo.UseShellExecute = false;
                //可能接受来自调用程序的输入信息
                //重定向标准输入
                p.StartInfo.RedirectStandardInput = true;
                //重定向标准输出
                p.StartInfo.RedirectStandardOutput = true;
                //重定向错误输出
                p.StartInfo.RedirectStandardError = true;
                //启动程序
                p.Start();
                //输入命令
                p.StandardInput.WriteLine(str);
                //一定要关闭。
                p.StandardInput.WriteLine("exit");
                return p.StandardOutput.ReadToEnd();
            }
        }

        public static string RemoveHtmlChar(string strMSG, char strStartChar = '<', char strEndChar = '>')
        {
            var result = new StringBuilder();
            while (!string.IsNullOrEmpty(strMSG) && strMSG.IndexOf(strStartChar) >= 0)
            {
                result.Append(strMSG.Substring(0, strMSG.IndexOf(strStartChar)));
                strMSG = strMSG.Substring(strMSG.IndexOf(strStartChar) + 1);
                if (strMSG.IndexOf(strEndChar) >= 0)
                {
                    strMSG = strMSG.Substring(strMSG.IndexOf(strEndChar) + 1);
                }
            }
            result.Append(strMSG);
            return result.ToString();
        }

        public static string ToString(NameValueCollection values)
        {
            var bodyBuilder = new StringBuilder(256);
            if (values != null)
            {
                try
                {
                    foreach (string key in values)
                    {
                        //bodyBuilder.AppendFormat("&{0}={1}", HttpUtility.UrlEncode(key.Trim(), Encoding.UTF8).Replace("+", "%20"),
                        //    HttpUtility.UrlEncode(values[key] == null ? "" : values[key].Trim(), Encoding.UTF8).Replace("+", "%20"));
                        bodyBuilder.AppendFormat("&{0}={1}", key.Trim(), values[key] == null ? "" : values[key].Trim());
                    }
                }
                catch
                {
                    // ignored
                }
            }
            return bodyBuilder.ToString().Replace("|TMPCODE|", "").TrimStart('&');
        }

        public static string SubString(string strSource, string strSpilt, string strEnd = "")
        {
            return strSource.SubStringHorspool(strSpilt, strEnd).Trim();
        }

        public static DateTime GetNtpTime()
        {
            return DateTimeUtil.GetNowTime();
        }

        public static void GetServerTime()
        {
            Task.Factory.StartNew(() =>
            {
                CommonMethod.GetNtpTime();
            });
        }
        #endregion

        #region 浏览器代理

        private static Random rndAgent = new Random();

        public static string GetRandomAgent()
        {
            string strTmp = "";
            try
            {
                strTmp = CommonString.LstAgent[rndAgent.Next(0, CommonString.LstAgent.Count)];
            }
            catch { }
            if (string.IsNullOrEmpty(strTmp))
                strTmp = CommonString.StrCommonAgent;
            return strTmp;
        }

        public static List<string> GetAgentByUrl(string AgentURL)
        {
            List<string> lstURL = new List<string>();
            try
            {
                string strTmp = WebClientExt.GetHtml(AgentURL, "", "", "", 3, 10);
                if (!string.IsNullOrEmpty(strTmp))
                {
                    string[] sAgent = strTmp.Split(new string[] { "<li>" }, StringSplitOptions.RemoveEmptyEntries);
                    foreach (var item in sAgent)
                    {
                        if (item.StartsWith("<a"))
                        {
                            strTmp = CommonMethod.SubString(item, ">", "</a>").Trim();
                            if (!string.IsNullOrEmpty(strTmp) && !lstURL.Contains(strTmp))
                            {
                                lstURL.Add(strTmp);
                            }
                        }
                    }
                }
            }
            catch { }
            return lstURL;
        }
        #endregion

        private static List<string> _lstForbiden = new List<string>();

        private static readonly object ObjLock = new object();
        public static void AddForbiden(string strIp)
        {
            try
            {
                if (IPHelper.NEnableComIPCount > 500)
                {
                    lock (ObjLock)
                    {
                        if (!_lstForbiden.Contains(strIp))
                        {
                            _lstForbiden.Add(strIp);
                        }
                    }
                }
                else
                {
                    RemoveForbiden();
                }
            }
            catch (Exception oe)
            {
                Log.WriteError("AddForbiden出错", oe);
            }
        }

        public static bool isForbiden(string strIP)
        {
            bool result = false;
            try
            {
                result = _lstForbiden.Contains(strIP);
            }
            catch (Exception oe)
            {
                Log.WriteError("isForbiden出错", oe);
            }
            return result;
        }

        public static void RemoveForbiden()
        {
            try
            {
                lock (ObjLock)
                {
                    _lstForbiden = new List<string>();
                }
            }
            catch (Exception oe)
            {
                Log.WriteError("RemoveForbiden出错", oe);
            }
        }

        public static bool GetLoginKeyURL(string strIP, ref string cookie, bool isLogin, ref List<string> lstKey, ref string strMSG, ref bool isNeedRandCode)
        {
            bool result = false;
            lstKey = GetKeyURL(strIP, isLogin, ref cookie, ref strMSG, ref result, ref isNeedRandCode);
            return result;
        }

        //public static string GetrandName(string url, string webResult = "")
        //{
        //    string result = string.Empty;
        //    HttpHelper httpHelper = new HttpHelper(url);
        //    if (string.IsNullOrEmpty(webResult))
        //    {
        //        webResult = httpHelper.GetResponseSTRING();
        //    }
        //    Regex regex = new Regex("/otn/dynamicJs(.*?)\" type");
        //    string str = string.Empty;
        //    if (regex.Match(webResult).Groups.Count > 0)
        //    {
        //        str = regex.Match(webResult).Groups[1].Value;
        //    }
        //    webResult = new HttpHelper(new LinkAddress().Loadjs + str)
        //    {
        //        Request =
        //        {
        //            Referer = url
        //        }
        //    }.GetResponseSTRING();
        //    regex = new Regex("function gc\\(\\){var (.*?)='(.*?)';");
        //    if (regex.Match(webResult).Groups.Count > 1)
        //    {
        //        result = regex.Match(webResult).Groups[2].Value;
        //    }
        //    regex = new Regex("/otn/dynamicJs/(.*?)'");
        //    if (regex.Matches(webResult).Count > 1)
        //    {
        //        httpHelper = new HttpHelper(new LinkAddress().Loadjs + "/" + regex.Match(webResult).Groups[1].Value);
        //        httpHelper.GetResponseSTRING();
        //    }
        //    return result;
        //}


        private static List<string> GetKeyURL(string strIP, bool isLogin, ref string cookie, ref string strMSG
            , ref bool result, ref bool isNeedRandCode, int count = 1)
        {
            result = false;
            isNeedRandCode = true;
            List<string> lstTemp = new List<string>();
            try
            {
                string strTmp = "";
                string strURL = "";
                if (isLogin)
                    strURL = "https://kyfw.12306.cn/otn/login/init";
                else
                    strURL = "https://kyfw.12306.cn/otn/leftTicket/init";

                strTmp = WebClientExt.GetHtml(strURL, ref cookie, strIP, "", 1, 5);
                //cookie = ";" + cookie;
                //if (isLogin)
                //{
                //    NewTicketHelper.AuthUAMTK(strIP, ref strURL, out strMSG, ref cookie);
                //}
                if (strTmp.Contains("/otn/dynamicJs/"))
                {
                    result = true;
                    strTmp = "https://kyfw.12306.cn/otn/dynamicJs/" + SubString(strTmp, "/otn/dynamicJs/", "\"");
                    lstTemp = GetSubKeyByType(strIP, strTmp, cookie);
                    //if (lstTemp == null && strURL.Contains("login"))
                    //{
                    //    return GetLoginKeyURL(strIP, ref cookie, ref strMSG, isGetKey);
                    //}
                }
                if (isLogin)
                {
                    string strCookieUrl = "";
                    if (!CommonString.IsGetCookieFromServer)
                    {
                        //strURL = "https://kyfw.12306.cn/otn/HttpZF/GetJS";
                        //strTmp = WebClientExt.GetHtml(strURL, ref cookie, strIP, "", 1, 5);
                        //if (!string.IsNullOrEmpty(strTmp) && strTmp.Contains("RAIL_EXPIRATION"))
                        //{
                        //}
                        strCookieUrl = GetServerCookie(strTmp);
                        if (!string.IsNullOrEmpty(strCookieUrl) && !strCookieUrl.Contains("otn/"))
                        {
                            strCookieUrl = "";
                        }
                    }

                    if (string.IsNullOrEmpty(strCookieUrl))
                    {
                        strCookieUrl = CommonString.StrCookieUrl;
                    }

                    if (!string.IsNullOrEmpty(strCookieUrl) && strCookieUrl.Contains("otn/"))
                        GetNewCookieStr(strIP, ref cookie, strCookieUrl);
                    //NewTicketHelper.GetDeviceIdCookie(strIP, ref cookie);
                }
                //else
                //{
                //    if (count <= 3)
                //    {
                //        count++;
                //        if (string.IsNullOrEmpty(cookie))
                //        {
                //            cookie = " ";
                //        }
                //        return GetKeyURL(strIP, ref  cookie, ref strMSG, ref result, ref isNeedRandCode, count);
                //    }
                //}
                strTmp = null;
            }
            catch { }
            return lstTemp;
        }

        private static string GetServerCookie(string sendstr)
        {
            var strCookieUrl = "";

            var url = string.Format("Code.ashx?op=device&app={0}&mac={1}&reg={2}&ver={3}"
                 , CommonReg.StrMachineKey, Environment.UserDomainName
                 , CommonReg.DtRegTime.ToString("yyyy-MM-dd HH:mm:ss"),
                 CommonString.dtNowDate.ToString("yyyy-MM-dd HH:mm:ss"));
            var html = CommonMethod.GetServerHtml(url, SiteType.code, CommonString.AutoCodeURL, false, false, "", 1);
            if (!string.IsNullOrEmpty(html) && html.HorspoolIndex("故障") < 0)
            {
                strCookieUrl = CommonEncryptHelper.DESDecrypt(html, CommonString.StrCommonEncryptKey);
            }
            return strCookieUrl;
        }

        public static void GetNewCookieStr(string strIP, ref string cookie, string strCookieUrl)
        {
            strCookieUrl = string.Format("https://kyfw.12306.cn/{0}", strCookieUrl.Replace("https://kyfw.12306.cn/", "").Replace(" ", "%20").TrimStart('/'));
            var html = WebClientExt.GetHtml(strCookieUrl, cookie, strIP, "", 1, 5);
            //callbackFunction('{\"exp\":\"1493864107846\",\"cookieCode\":\"FDZgBbCr4NzggyTw_V-NZL0W89mtsl05\",\"dfp\":\"pCtPA2I069Gk5LNFrpDbUev7pnvjzBCLVYlJNtq8SMplMqkbaFewtNMq4qkMhnzv2_Pfc_A5jbUGN_tWooezHVNjwQCkYF5NG_jh1W2cuJNeua4dJpTjjJ4FYEy0OIlBW5LpF83SxR8t0HxTvxHLuIcHrt7nhJTL\"}')
            if (!string.IsNullOrEmpty(html) && html.Contains("callbackFunction"))
            {
                html = html.Replace("callbackFunction('", "");
                html = html.Replace("')", "");

                var exp = CommonMethod.SubString(html, "\"exp\":\"", "\"");
                var strNewCookie = "";
                if (!string.IsNullOrEmpty(exp))
                {
                    strNewCookie += string.Format(";RAIL_EXPIRATION={0}", exp);
                }

                var dfp = CommonMethod.SubString(html, "\"dfp\":\"", "\"");
                if (!string.IsNullOrEmpty(dfp))
                {
                    strNewCookie += string.Format(";RAIL_DEVICEID={0}", dfp);
                }

                var cookieCode = CommonMethod.SubString(html, "\"cookieCode\":\"", "\"");
                if (!string.IsNullOrEmpty(cookieCode))
                {
                    strNewCookie += string.Format(";RAIL_OkLJUJ={0}", cookieCode);
                }
                cookie += strNewCookie;
                DeviceCookieHelper.Add(exp, strNewCookie);
                cookie += ";fp_ver=4.6.1;";
            }
        }

        public static string GetNewCookie(string strCookie, string strNewCookie)
        {
            string strTmpCookie = strCookie;
            if (!string.IsNullOrEmpty(strNewCookie))
            {
                List<string> lstTmp = new List<string>();
                lstTmp.AddRange(strNewCookie.Split(new string[] { ",", ";" }, StringSplitOptions.RemoveEmptyEntries));
                string strItem = "";
                foreach (string item in lstTmp)
                {
                    if (!item.Trim().ToLower().StartsWith("path=")
                        && !item.Trim().ToLower().StartsWith("expires=")
                        && !item.Trim().ToLower().StartsWith("httponly=")
                        && !item.Trim().ToLower().StartsWith("domain=.")
                        && item.IndexOf("=") > 0)
                    {
                        strItem = CommonMethod.SubString(item.Trim(), "", "=") + "=";
                        if (!strTmpCookie.Contains(strItem))
                        {
                            strTmpCookie += string.Format(";{0};", item.Trim());
                        }
                        else
                        {
                            strTmpCookie = strTmpCookie.Replace(strItem + CommonMethod.SubString(strTmpCookie, strItem, ";"), item);
                        }
                    }
                }
            }
            return strTmpCookie.Replace(" ", "").Replace(";;", ";").TrimStart(';');
        }

        static MSScriptControl.ScriptControlClass js = new MSScriptControl.ScriptControlClass();
        public static string GetEvalResult(string strKey)
        {
            string strReuslt = "";
            try
            {
                object obj = null;
                try
                {
                    js.Language = "javascript";
                    js.UseSafeSubset = true;
                    js.Reset();
                    obj = js.Eval(CommonString.strScript +
                        string.Format(";encode32(bin216(Base32.encrypt('{0}', '{1}')));", "1111", strKey));
                }
                catch (Exception oe)
                {
                    //Unable to cast COM object of type 'MSScriptControl.ScriptControlClass' to interface type 'MSScriptControl.IScriptControl'. This operation failed because the QueryInterface call on the COM component for the interface with IID '{0E59F1D3-1FBE-11D0-8FF2-00A0D10038BC}' failed due to the following error: 加载类型库/DLL 时出错。 (Exception from HRESULT: 0x80029C4A (TYPE_E_CANTLOADLIBRARY))
                    CommonMSG.AddMSG("获取Key出错：请联系客服协助！详细信息：" + oe.Message);
                    Log.WriteError("GetEvalResult出错", oe);
                }
                if (obj != null && !string.IsNullOrEmpty(obj.ToString()))
                    strReuslt = obj.ToString();
            }
            catch (Exception oe)
            {
                Log.WriteError("GetEvalResult出错", oe);
            }
            return strReuslt;
        }
        public static string GetEvalResult1(string strKey)
        {
            string strReuslt = "";
            try
            {
                object obj = null;
                try
                {
                    js.Language = "javascript";
                    js.Reset();
                    js.UseSafeSubset = true;
                    obj = js.Eval(strKey);
                }
                catch (Exception oe)
                {
                    //Unable to cast COM object of type 'MSScriptControl.ScriptControlClass' to interface type 'MSScriptControl.IScriptControl'. This operation failed because the QueryInterface call on the COM component for the interface with IID '{0E59F1D3-1FBE-11D0-8FF2-00A0D10038BC}' failed due to the following error: 加载类型库/DLL 时出错。 (Exception from HRESULT: 0x80029C4A (TYPE_E_CANTLOADLIBRARY))
                    CommonMSG.AddMSG("获取Key出错：请联系客服协助！详细信息：" + oe.Message);
                    Log.WriteError("GetEvalResult出错", oe);
                }
                if (obj != null && !string.IsNullOrEmpty(obj.ToString()))
                    strReuslt = obj.ToString();
            }
            catch (Exception oe)
            {
                Log.WriteError("GetEvalResult出错", oe);
            }
            return strReuslt;
        }

        public static List<string> GetSubKeyByType(string strIP, string url, string cookie, string token = "")
        {
            //MSScriptControl.ScriptControlClass sc = new MSScriptControl.ScriptControlClass();
            //sc.Language = "javascript";
            List<string> lstTemp = new List<string>();
            try
            {
                //type == 0 ? CommonString.strLoginKeyURL
                //: (type == 1 ? CommonString.strOrderKeyURL : CommonString.strQueryKeyURL)

                //$(document).ready(function(){(function(){})();});function gc()
                //var form = document.forms[0];//var form = $('#orderForm')[0];
                string html = WebClientExt.GetHtml(url, cookie, strIP, "", 1, 5);
                //HttpRequest httpRequest = new HttpRequest();
                //httpRequest.OperationName = string.Format("查询{0}加密Key", type == 0 ? "登录" : (type == 2 ? "查询" : "订单"));
                //httpRequest.Method = "GET";
                //httpRequest.Url = type == 0 ? strLoginKeyURL : (type == 1 ? strOrderKeyURL : strQueryKeyURL);
                //string html = HTTP.RequestNoSyncHtml(httpRequest, ref cookie);
                if (!string.IsNullOrEmpty(html))
                {
                    var keyName = "key";
                    if (html.Contains("+':'+ticket;}})(jQuery);"))
                    {
                        string strTmp = CommonMethod.SubString(html, "", "+':'+ticket;}})(jQuery);");
                        keyName = strTmp.Substring(strTmp.LastIndexOf("return ") + "return ".Length);
                        strTmp = null;
                    }
                    if ((html.HorspoolIndex("\"hidden\" ") > 0 || html.HorspoolIndex("$(document).ready") > 0) && html.HorspoolIndex(string.Format("var {0}='", keyName)) > 0)
                    {
                        string strTmpNext = CommonMethod.SubString(html, "$(document).ready(");
                        if (strTmpNext.Contains("jq({url :'"))
                        {
                            string strNextURL = "https://kyfw.12306.cn/" + CommonMethod.SubString(strTmpNext, "jq({url :'", "'").TrimStart('/');
                            if (!string.IsNullOrEmpty(strNextURL))
                            {
                                WebClientExt.GetHtml(strNextURL, cookie, strIP, "_json_att=" + (string.IsNullOrEmpty(token) ? "" : "&REPEAT_SUBMIT_TOKEN=" + token), 1, 3);
                            }
                        }
                        string strKey = CommonMethod.SubString(html, string.Format("var {0}='", keyName), "'");
                        //string strTTObj = string.Empty;
                        //if (html.HorspoolIndex("function bin216") > 0)
                        //{
                        //    html = html.Substring(html.HorspoolIndex("function bin216"));
                        //    if (html.HorspoolIndex("function aj()") > 0)
                        //    {
                        //        if (html.HorspoolIndex("+'\" ticket=\"'+") > 0)
                        //        {
                        //            strTTObj = html.Substring(html.HorspoolIndex("+'\" ticket=\"'+") + "+'\" ticket=\"'+".Length);
                        //            strTTObj = strTTObj.Substring(0, strTTObj.HorspoolIndex("'")).Replace("]", "}'").Replace("keyVlues[", "'{").TrimEnd('+');
                        //        }
                        //        html = html.Substring(0, html.HorspoolIndex("function aj()"));
                        //    }
                        //}
                        if (!string.IsNullOrEmpty(strKey))
                        {
                            string strKeys = html;
                            while (strKeys.HorspoolIndex("name=\"") > 0)
                            {
                                strKeys = strKeys.Substring(strKeys.HorspoolIndex("name=\"") + "name=\"".Length);
                                if (!strKeys.StartsWith("'") && !lstTemp.Contains(strKeys.Substring(0, strKeys.HorspoolIndex("\""))))
                                {
                                    lstTemp.Add(strKeys.Substring(0, strKeys.HorspoolIndex("\"")));
                                    string ssTemp = strKeys.Substring(0, strKeys.HorspoolIndex("/>"));
                                    if (ssTemp.HorspoolIndex("ticket=\"") >= 0)
                                    {
                                        ssTemp = ssTemp.Substring(ssTemp.HorspoolIndex("ticket=") + "ticket=\"".Length);
                                        if (!ssTemp.StartsWith("'"))
                                        {
                                            lstTemp.Add(strKeys.Substring(0, strKeys.HorspoolIndex("\"")));
                                        }
                                        else
                                            lstTemp.Add("undefined");
                                    }
                                    else
                                        lstTemp.Add("");
                                }
                            }
                            html = string.Empty;
                            lstTemp.Add(strKey);
                            lstTemp.Add(HttpUtility.UrlEncode(GetEvalResult(strKey)));
                            //else
                            //    SendMail.Send("解析Key出错，可能已经升级了！");
                        }
                        //else
                        //{
                        //    SendMail.Send("解析到的Key为空，可能已经升级了！");
                        //}
                    }
                    else
                    {
                        Console.WriteLine(html);
                    }
                }
                else
                {
                    lstTemp = null;
                }
            }
            catch (Exception oe)
            {
            }
            if (lstTemp != null && lstTemp.Count > 0 && !lstTemp.Contains("myversion"))
            {
                lstTemp.Add("myversion");
                lstTemp.Add("undefined");
            }
            return lstTemp;
        }
        //public static bool isYuShou(AttentionItem item, DateTime dtStart)
        //{
        //    bool result = false;
        //    if (item != null && !string.IsNullOrEmpty(item.TrainNo) && DateTime.Now.Date.AddDays(19) <= item.Date.Date)
        //    {
        //        if (item.TrainNo.StartsWith("D"))
        //        {
        //            result = dtStart <= DateTime.Parse(DateTime.Now.Date.ToString("yyyy-MM-dd") + " 11:02:00")
        //                && dtStart >= DateTime.Parse(DateTime.Now.Date.ToString("yyyy-MM-dd") + " 10:59:00");
        //        }
        //        else if (item.TrainNo.StartsWith("G"))
        //        {
        //            result = dtStart <= DateTime.Parse(DateTime.Now.Date.ToString("yyyy-MM-dd") + " 14:02:00")
        //                && dtStart >= DateTime.Parse(DateTime.Now.Date.ToString("yyyy-MM-dd") + " 13:59:00");
        //        }
        //        else
        //        {
        //            string strDate = GetPreSellingTimeStr(item.FromStation.Name);
        //            if (!string.IsNullOrEmpty(strDate))
        //            {
        //                DateTime dtYuShou = DateTime.Parse(strDate);
        //                result = dtStart <= dtYuShou.AddMinutes(10) && dtStart >= dtYuShou.AddMinutes(-10);
        //            }
        //        }
        //    }
        //    return result;
        //}

        internal static bool IsYuShou(DateTime dateTime)
        {
            return CommonString.serverTime.Date.AddDays(CommonString.NYuShouDate - 1).Equals(dateTime.Date);
        }
    }

    public class QuickLogin
    {
        public string StrIp { get; set; }

        public string StrCode { get; set; }

        public string StrCookie { get; set; }
    }
}
#region 废弃代码

///// <summary>
///// 汉字转换为Unicode编码
///// </summary>
///// <param name="str">要编码的汉字字符串</param>
///// <returns>Unicode编码的的字符串</returns>
//public static string ToUnicode(string str)
//{
//    byte[] bts = Encoding.Unicode.GetBytes(str);
//    string r = "";
//    for (int i = 0; i < bts.Length; i += 2)
//        r += "\\u" + bts[i + 1].ToString("x").PadLeft(2, '0') + bts[i].ToString("x").PadLeft(2, '0');
//    bts = null;
//    return r;
//}


//private static void initPassenger()
//{
//    //CommonString.lstVirualPassenger = new List<Passenger>();

//    //string[] strTT = strTmp.Split(new string[] { "\r\n" }, StringSplitOptions.RemoveEmptyEntries);
//    //string[] strInfo = null;
//    //if (strTT != null && strTT.Length > 0)
//    //{
//    //    string ss = string.Empty;
//    //    foreach (string item in strTT)
//    //    {
//    //        strInfo = item.Split(new string[] { "\t" }, StringSplitOptions.RemoveEmptyEntries);
//    //        //姚立武	511121197807101233
//    //        Passenger pass = new Passenger();
//    //        pass.Name = strInfo[0];
//    //        pass.IDCard = strInfo[1];
//    //        pass.SetCardType("二代身份证");
//    //        pass.SetUserType("成人票");
//    //        pass.SetSeatType("硬卧");
//    //        CommonString.lstVirualPassenger.Add(pass);
//    //    }
//    //}
//}

//#region 订单提交相关

//public static int GetHasCount(string ticketInfo, out int noCount)
//{
//    int hasCount = 0;
//    noCount = 0;
//    string strTemp = string.Empty;
//    string strTicket = ticketInfo;
//    int i = 0;
//    while (i < strTicket.Length)
//    {
//        noCount = 0;
//        strTemp = strTicket.Substring(i, 10);
//        if (strTemp.StartsWith("0"))
//        {
//            int.TryParse(strTemp.Substring(6, 4), out noCount);
//            if (noCount >= 3000)
//                noCount = noCount - 3000;
//        }
//        else
//        {
//            if (int.TryParse(strTemp.Substring(6, 4), out noCount))
//                hasCount += noCount;
//        }
//        i += 10;
//    }
//    return hasCount;
//}
//#endregion

//public static void LeaveDotsAndSlashesEscaped()
//{
//    return;
//    var getSyntaxMethod =
//        typeof(UriParser).GetMethod("GetSyntax", BindingFlags.Static | BindingFlags.NonPublic);
//    if (getSyntaxMethod == null)
//    {
//        throw new MissingMethodException("UriParser", "GetSyntax");
//    }

//    var uriParser = getSyntaxMethod.Invoke(null, new object[] { "https" });

//    var setUpdatableFlagsMethod =
//        uriParser.GetType().GetMethod("SetUpdatableFlags", BindingFlags.Instance | BindingFlags.NonPublic);
//    if (setUpdatableFlagsMethod == null)
//    {
//        throw new MissingMethodException("UriParser", "SetUpdatableFlags");
//    }

//    setUpdatableFlagsMethod.Invoke(uriParser, new object[] { 0 });
//}

//public static bool Is12306OK(IPEndPoint ipPoint, bool isLogin = false)
//{
//    bool result = false;
//    string html = HttpHelper.Get(ipPoint, new HttpHeader()
//    {
//        Url = CommonString.OTSWebMain
//    }, ref CommonString.CommonCookie);
//    //string html = HTTP.RequestNoSyncHtml(
//    //    new HttpRequest() { Timeout = 1000, Url = Settings.Default.OTSWebMain, OperationName = "保持心跳连接…" }, ref CommonString.CommonCookie);
//    if (!string.IsNullOrEmpty(html) && html.HorspoolIndex("<link") > 0)
//    {
//        if (!isLogin)
//        {
//            html = HttpHelper.Get(ipPoint, new HttpHeader()
//            {
//                Url = "http://dynamic.12306.cn/otsquery/query/queryRemanentTicketAction.do?method=init"
//            }, ref CommonString.CommonCookie);
//            //html = HTTP.RequestNoSyncHtml(
//            //   new HttpRequest() { Url = "http://dynamic.12306.cn/otsquery/query/queryRemanentTicketAction.do?method=init", OperationName = "保持心跳连接…" }, ref CommonString.CommonCookie);
//            //
//            result = !string.IsNullOrEmpty(html) && html.HorspoolIndex("拒绝访问") < 0;
//        }
//        else
//            result = true;
//    }
//    return result;
//}

//public static bool TimeInRangle(DateTime time, int index)
//{
//    bool result;
//    if (index == 1)
//    {
//        result = ((time.Hour >= 0 && time.Hour <= 6) || (time.Hour == 6 && time.Minute == 0));
//    }
//    else
//    {
//        if (index == 2)
//        {
//            result = ((time.Hour >= 6 && time.Hour <= 12) || (time.Hour == 12 && time.Minute == 0));
//        }
//        else
//        {
//            if (index == 3)
//            {
//                result = ((time.Hour >= 12 && time.Hour <= 18) || (time.Hour == 18 && time.Minute == 0));
//            }
//            else
//            {
//                result = (index != 4 || (time.Hour >= 18 && time.Hour <= 23) || (time.Hour == 0 && time.Minute == 0));
//            }
//        }
//    }
//    return result;
//}

//public static bool TimeInRangle(DateTime time, string textRange)
//{
//    bool result;
//    try
//    {
//        textRange = (string.IsNullOrEmpty(textRange) ? "00:00--00:00" : textRange);
//        textRange = textRange.Replace("--00:00", "--24:00");
//        string[] array = textRange.Split(new string[]
//        {
//            ":",
//            "--"
//        }, 4, StringSplitOptions.RemoveEmptyEntries);
//        int num = time.Hour * 60 + time.Minute;
//        int num2 = int.Parse(array[0]) * 60 + int.Parse(array[1]);
//        int num3 = int.Parse(array[2]) * 60 + int.Parse(array[3]);
//        result = (num >= num2 && num <= num3);
//    }
//    catch
//    {
//        result = true;
//    }
//    return result;
//}

//public static NameValueCollection AutoLearnRule(NameValueCollection oldValues, string formName, string html, out bool isChange)
//{
//    isChange = false;
//    NameValueCollection loginValues = oldValues;
//    try
//    {
//        if (!string.IsNullOrEmpty(html))
//        {
//            if (html.HorspoolIndex("请输入验证码") > 0)
//            {
//                string ImgCode = html.Substring(html.HorspoolIndex("请输入验证码：") + "请输入验证码：".Length);
//                ImgCode = ImgCode.Substring(ImgCode.HorspoolIndex("src=") + "src=\"".Length);
//                ImgCode = ImgCode.Substring(0, ImgCode.HorspoolIndex("\"")).Replace("otsweb/", "").TrimStart('/');
//                if (ValidateCode.GetImgURLByType(formName.Equals("loginForm") ? 0 : 1).HorspoolIndex(ImgCode) < 0)
//                {
//                    if (formName.Equals("loginForm"))
//                        ValidateCode.strLoginImg = "https://dynamic.12306.cn/otsweb/" + ImgCode;
//                    else
//                        ValidateCode.strOrderImg = "https://dynamic.12306.cn/otsweb/" + ImgCode;
//                }
//            }
//            //if (html.HorspoolIndex("dynamicJsAction") > 0)
//            //{
//            //    string strJS = html.Substring(html.HorspoolIndex("dynamicJsAction"));
//            //    strJS = strJS.Substring(0, strJS.HorspoolIndex("\""));
//            //    if (!string.IsNullOrEmpty(strJS))
//            //    {
//            //        if (strJS.ToLower().HorspoolIndex("login") >= 0)
//            //        {
//            //            if (strLoginKeyURL.HorspoolIndex(strJS) < 0)
//            //            {
//            //                strLoginKeyURL = string.Format("https://dynamic.12306.cn/otsweb/{0}", strJS);
//            //            }
//            //        }
//            //        else if (strJS.ToLower().HorspoolIndex("query") >= 0)
//            //        {
//            //            if (strQueryKeyURL.HorspoolIndex(strJS) < 0)
//            //            {
//            //                strQueryKeyURL = string.Format("https://dynamic.12306.cn/otsweb/{0}", strJS);
//            //            }
//            //        }
//            //        else
//            //        {
//            //            if (strOrderKeyURL.HorspoolIndex(strJS) < 0)
//            //            {
//            //                strOrderKeyURL = string.Format("https://dynamic.12306.cn/otsweb/{0}", strJS);
//            //            }
//            //        }
//            //    }
//            //}
//            string strHasName = "<form name=\"" + formName + "\"";
//            if (html.HorspoolIndex(strHasName) < 0)
//            {
//                strHasName = "<form id=\"" + formName + "\"";
//            }
//            if (html.HorspoolIndex(strHasName) > 0)
//            {
//                string strKey = string.Empty;
//                string strValue = html;
//                string strVal = html;
//                html = html.Substring(html.HorspoolIndex(strHasName) + (strHasName).Length);
//                html = html.Substring(0, html.HorspoolIndex("</form>"));
//                //
//                string strTTValue = string.Empty;
//                while (html.HorspoolIndex("\"hidden\"") >= 0)
//                {
//                    html = html.Substring(html.HorspoolIndex("\"hidden\"") + "\"hidden\"".Length);
//                    strTTValue = html.Substring(0, html.HorspoolIndex(">"));
//                    if (strTTValue.HorspoolIndex("name=") >= 0)
//                    {
//                        strTTValue = strTTValue.Substring(strTTValue.HorspoolIndex("name=") + "name=\"".Length);
//                        strKey = strTTValue.Substring(0, strTTValue.HorspoolIndex("\""));
//                        if (loginValues.GetValues(strKey) == null || loginValues.GetValues(strKey).Length <= 0)
//                        {
//                            isChange = true;
//                            if (strTTValue.HorspoolIndex("value=\"") >= 0)
//                            {
//                                strTTValue = strTTValue.Substring(strTTValue.HorspoolIndex("value=") + "value=\"".Length);
//                                loginValues[strKey] = strTTValue.Substring(0, strTTValue.HorspoolIndex("\""));
//                            }
//                            else
//                            {
//                                loginValues[strKey] = "";
//                            }
//                        }
//                    }
//                    else
//                    {
//                        strTTValue = string.Empty;
//                    }
//                }
//                string strTemp = string.Empty;
//                if (formName.Equals("loginForm"))
//                {
//                    //form_tk特殊处理
//                    if (strVal.HorspoolIndex("var form_tk=\"") > 0)
//                    {
//                        strTemp = strVal.Substring(strVal.HorspoolIndex("var form_tk=\"") + "var form_tk=\"".Length);
//                        strTemp = strTemp.Substring(0, strTemp.HorspoolIndex("\""));
//                        loginValues["form_tk"] = strTemp;
//                        strTemp = string.Empty;
//                    }
//                }
//            }
//        }
//    }
//    catch (Exception oe)
//    {
//        Log.WriteError(oe);
//        //SendMail.SendException(oe);
//    }
//    return loginValues;
//}

//public static string GetOrderJsURL(IPEndPoint strNowIPPoint, string cookie)
//{
//    string strTmp = string.Empty;
//    string html = HttpHelper.Get(strNowIPPoint, new HttpHeader()
//    {
//        Url = CommonString.QuerySingleActionUrl + "?method=init"
//    }, ref cookie);
//    if (!string.IsNullOrEmpty(html) && html.HorspoolIndex("dynamicJsAction") > 0)
//    {
//        strTmp = html.Substring(html.HorspoolIndex("dynamicJsAction"));
//        strTmp = strTmp.Substring(0, strTmp.HorspoolIndex("\""));
//        strTmp = string.Format("https://dynamic.12306.cn/otsweb/{0}", strTmp);
//    }
//    return strTmp;
//}

#endregion