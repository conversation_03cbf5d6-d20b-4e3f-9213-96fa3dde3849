﻿
using CommonLib;
using System;
using System.Collections.Generic;
using System.Collections.Specialized;
using System.Security.Cryptography;
using System.Text;

namespace MathOcr
{
    /// <summary>
    /// https://console.ecloud.10086.cn/api/query/smartlib/mobile/recognize?apiType=FORMULA_RECOGNITION&apiName=%E5%85%AC%E5%BC%8F%E8%AF%86%E5%88%AB
    /// </summary>
    public class Cloud10086 : BaseMathRec
    {
        public Cloud10086()
        {
            OcrType = MathOcrType.移动云;
            MaxExecPerTime = 15;

            LstJsonPreProcessArray = new List<object>() { "body", "items" };
            IsJsonArrayString = true;
        }


        private const string clientSecret = "e10adc3949ba59abbe56e057f20f883e";

        protected override string GetHtml(OcrContent content)
        {
            GetPool();
            DateTime startTime = new DateTime(1970, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc);
            var requestTimestamp = Math.Round((DateTime.UtcNow - startTime).TotalMilliseconds, MidpointRounding.AwayFromZero).ToString("F0");
            //1720884836452
            var apiType = "FORMULA_RECOGNITION";
            var strUrl = "/mobile/experience/call";
            //d38713ee462c8b28ddd575eb7c6e3c7353765cd6
            var strSign = appSign(requestTimestamp, strUrl, clientSecret);
            //1720884836452:d38713ee462c8b28ddd575eb7c6e3c7353765cd6
            strSign = Convert.ToBase64String(Encoding.UTF8.GetBytes(string.Concat(requestTimestamp, ":", strSign)));
            //MTcyMDg4NDgzNjQ1MjpkMzg3MTNlZTQ2MmM4YjI4ZGRkNTc1ZWI3YzZlM2M3MzUzNzY1Y2Q2
            var strPost = "{\"signData\":\"" + strSign + "\",\"apiType\":\"" + apiType + "\",\"params\":{\"image\":\"" + content.strBase64 + "\"}}";
            var headers = new NameValueCollection() {
                { "Pool-Id",poolId}
            };
            var strTmp = WebClientSyncExt.GetHtml("https://console.ecloud.10086.cn/api/query/aiconsole" + strUrl + "?sign=" + strSign, strPost, ExecTimeOutSeconds, headers);

            return strTmp;
        }

        private string poolId = string.Empty;

        private void GetPool()
        {
            if (string.IsNullOrEmpty(poolId))
            {
                var strTmp = WebClientSyncExt.GetHtml("https://console.ecloud.10086.cn/api/query/smartlib/mobile/config.prod.json", ExecTimeOutSeconds);
                if (!string.IsNullOrEmpty(strTmp))
                {
                    poolId = CommonHelper.SubString(strTmp, "\"poolId\": \"", "\"");
                }
            }
            if (string.IsNullOrEmpty(poolId))
            {
                poolId = "CIDC-RP-25";
            }
        }

        private string appSign(string timeSpan, string url, string secretKey)
        {
            var plainText = "POST:" + url + ":" + timeSpan;
            byte[] signByteArrary = HmacSha1Sign(plainText, secretKey);
            return BitConverter.ToString(signByteArrary).Replace("-", "").ToLower();

        }

        private byte[] HmacSha1Sign(string str, string key)
        {
            using (HMACSHA1 hmac = new HMACSHA1(Encoding.UTF8.GetBytes(key)))
            {
                return hmac.ComputeHash(Encoding.UTF8.GetBytes(str));
            }
        }
    }
}