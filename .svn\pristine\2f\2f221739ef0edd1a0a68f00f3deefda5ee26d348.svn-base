﻿using System;
using System.Collections.Generic;
using System.Web;

namespace Account.Web
{
    public class CodeEntity
    {
        public string StrRemark { get; set; } = "";

        public string StrAppCode { get; set; } = "";

        public string StrPwd { get; set; } = "";

        public string StrNickName { get; set; }

        public string StrType { get; set; } = "";

        public DateTime DtReg { get; set; } = DateTime.MinValue;

        public DateTime DtExpire { get; set; } = DateTime.MinValue;

        public bool IsValidate
        {
            get
            {
                return DtExpire > DtReg;
            }
        }

        public bool IsExpired
        {
            get
            {
                return DtExpire < CommonLib.ServerTime.DateTime;
            }
        }

        public bool IsForbid { get; set; } = false;

        public int NMaxWindow { get; set; } = 1;

        public int NMaxLogin { get; set; } = 5;
    }
}