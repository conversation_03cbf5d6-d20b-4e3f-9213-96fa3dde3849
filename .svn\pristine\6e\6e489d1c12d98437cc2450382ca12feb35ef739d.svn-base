/* 产品页 */
.product-info {
  padding-bottom: 100px;
}

/* 产品介绍 */
.product-info .card-item:nth-child(1) .card-content.active::before {
  background-image: url("../image/server_bg_11.png");
}
.product-info .card-item:nth-child(2) .card-content.active::before {
  background-image: url("../image/server_bg_21.png");
}

/* 功能优势 */
.compare-list {
  margin-top: -3rem;
}
.compare-item {
  background-color: #dddfe7;
  border-radius: 4px;
  position: relative;
}
.good-list {
  position: relative;
  top: 6px;
  padding: 24px 16px !important;
  transform: translateY(16px);
  border: 1px solid #fff;
  border-radius: 4px;
  background-image: -moz-linear-gradient(
    90deg,
    rgb(255, 255, 255) 0%,
    rgb(243, 245, 249) 100%
  );
  background-image: -webkit-linear-gradient(
    90deg,
    rgb(255, 255, 255) 0%,
    rgb(243, 245, 249) 100%
  );
  background-image: -ms-linear-gradient(
    90deg,
    rgb(255, 255, 255) 0%,
    rgb(243, 245, 249) 100%
  );
  box-shadow: 0px 10px 40px 0px rgba(0, 17, 86, 0.1);
}
.good-item {
  display: flex;
  margin: 6px 0;
}

.recommend-wrap {
  position: absolute;
  top: 0;
  left: 0;
  width: 92px;
  height: 92px;
  z-index: 2;
  overflow: hidden;
  pointer-events: none;
}
.recommend-content {
  position: absolute;
  top: 50%;
  left: 50%;
  text-align: center;
  width: 200%;
  height: 32px;
  line-height: 32px;
  font-size: 14px;
  background-color: #ef163a;
  transform: translate(calc(-50% - 12px), calc(-50% - 10px)) rotate(-45deg);
  color: #fff;
}
.compare-item.recommend {
  background-image: url("../image/ad_bg.png");
  background-size: cover;
  background-repeat: no-repeat;
}
.color-red {
  color: #f12319;
}
.product-six-cell .card-list {
  margin-top: 4rem !important;
}
.product-six-cell .card-list li {
  margin-bottom: 3rem !important;
}

/* 产品覆盖 */
.model-list {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  grid-template-rows: repeat(3, 1fr);
  gap: 40px 32px;
  justify-items: left;
  align-items: center;
}
.model-item {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 40px;
}
.model-item img {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.phone-mode img {
  width: 280px;
  transition: all 0.3s ease-out;
}
.news-img1 {
  opacity: 0;
  transform: translate(2rem, 7.6rem);
}
.news-img2 {
  opacity: 0;
  transform: translate(-2rem, 7.6rem);
}
.phone-mode-img.active .news-img1 {
  animation: anim1 0.3s;
  animation-delay: 0.2s;
  animation-fill-mode: forwards;
}
.phone-mode-img.active .news-img2 {
  animation: anim2 0.3s;
  animation-delay: 0.4s;
  animation-fill-mode: forwards;
}

@keyframes anim1 {
  0% {
    transform: translate(2rem, 10.6rem);
  }
  100% {
    opacity: 1;
    transform: translate(2rem, 7.6rem);
  }
}
@keyframes anim2 {
  0% {
    transform: translate(-2rem, 10.6rem);
  }
  100% {
    opacity: 1;
    transform: translate(-2rem, 7.6rem);
  }
}
@media screen and (max-width: 575px) {
  .model-list {
    grid-template-columns: repeat(3, 1fr);
    grid-template-rows: repeat(5, 1fr);
    gap: 40px 24px;
  }
  .model-item {
    height: 32px;
  }
}
