﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace monocaptcha
{
    public interface IAnswerResult
    {
        /// <summary>
        /// status：200  正常返回
        ///       ：301  数据格式不正确
        ///       ：401  token不存在
        ///       ：402  该账户余额不足
        ///       ：403  该账户有恶意行为，已被禁止
        ///       ：501  服务器错误，请联系管理员
        ///       
        /// answer 除status==200 外，其他情况均为 null
        /// 
        /// </summary>
        /// <param name="status">返回的状态码</param>
        /// <param name="answer">正确答案</param>
        void handle(ResponseEnum status, string answer);

        byte[] getPictureData();

        void setPictureData(byte[] pictureData);
    }
}