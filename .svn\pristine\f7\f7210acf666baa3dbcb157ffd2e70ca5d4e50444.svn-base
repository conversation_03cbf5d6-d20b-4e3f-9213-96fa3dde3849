﻿using CommonLib;
using System;

namespace Account.Web
{
    public partial class UserLogin : System.Web.UI.Page
    {
        public string strErrorMsg = string.Empty;

        protected void Page_Load(object sender, EventArgs e)
        {
            if (Request.RequestType.Equals("POST"))
            {
                string username = Request.Form["username"];
                string password = Request.Form["password"];

                if (string.IsNullOrEmpty(username) || string.IsNullOrEmpty(password))
                {
                    Response.Write("<script>alert('用户名或密码不能为空！');</script>");
                }
                else if (!BoxUtil.IsEmail(username) && !BoxUtil.IsMobile(username))
                {
                    Response.Write("<script>alert('用户名格式不正确，请检查后重试！');</script>");
                }
                else
                {
                    Server.Transfer("Code.aspx?op=login&account=" + username + "&pwd=" + password + "&isweb=1");
                }
            }
        }
    }
}