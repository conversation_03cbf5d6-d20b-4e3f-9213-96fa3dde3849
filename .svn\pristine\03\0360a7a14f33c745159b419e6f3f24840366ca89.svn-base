﻿using System;
using System.Threading;

namespace CommonLib
{
    public class ServerTime
    {
        //http://global.apis.naver.com/currentTime
        //https://moapi.wps.cn/time
        public static long OffSet;
        private static long _lastOffSet;

        public static DateTime DateTime => LocalTime.AddTicks(OffSet);

        public static DateTime LocalTime => DateTime.UtcNow.AddHours(8);

        static ServerTime()
        {
            new Thread(p =>
            {
                ProcessTimeOffSet();
            })
            { Priority = ThreadPriority.Highest, IsBackground = true }.Start();
        }

        private static void ProcessTimeOffSet()
        {
            while (!ConfigHelper.IsExit)
            {
                var off = SNtpClient.Instance.GetNetworkTimeOffset();
                if (off != -9999)
                {
                    SetOffSet(off);
                    Thread.Sleep(10 * 1000);
                }
                else
                {
                    Thread.Sleep(1 * 1000);
                }
            }
        }

        private static void SetOffSet(long off)
        {
            if (_lastOffSet != 0)
            {
                OffSet = (_lastOffSet + OffSet + off) / 3;
            }
            else
            {
                OffSet = off;
            }

            //Console.WriteLine($"校正时间：{DateTime.ToString("HH:mm:ss fff")}\n差异值：{new TimeSpan(off).TotalMilliseconds.ToString("F2")}ms");
            _lastOffSet = off;
        }
    }
}
