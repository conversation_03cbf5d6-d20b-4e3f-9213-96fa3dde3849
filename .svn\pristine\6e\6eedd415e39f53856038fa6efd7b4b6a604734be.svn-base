﻿using System;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Drawing.Imaging;
using System.IO;
using System.IO.Compression;
using System.Text;

namespace CommonLib
{
    public static class CommonCompress
    {
        public static Image FastFromFile(string fileName)
        {
            using (MemoryStream ms = new MemoryStream(File.ReadAllBytes(fileName)))
                return Image.FromStream(ms);
        }

        public static byte[] ImageToByte(Bitmap img)
        {
            byte[] buffer = null;
            using (var ms = new MemoryStream())
            {
                img.Save(ms, ImageFormat.Jpeg);
                buffer = ms.ToArray();
            }
            return buffer;
        }

        public static Bitmap BytesToImage(byte[] buffer)
        {
            using (var ms = new MemoryStream(buffer))
            {
                return new Bitmap(ms);
            }
        }

        public static Bitmap GetImageFromBase64(bool isLogin, string base64String, string strIndex, ref string path,
            bool isSave = true)
        {
            Bitmap bit = null;
            try
            {
                path = ServerTime.DateTime.ToString("MMdd") + "\\Old\\";

                var b = Convert.FromBase64String(base64String.Replace("%2B", "+"));
                using (var ms = new MemoryStream(b))
                {
                    using (var img = Image.FromStream(ms))
                    {
                        if (!Directory.Exists(ConfigHelper.StrImagePath + path))
                        {
                            Directory.CreateDirectory(ConfigHelper.StrImagePath + path);
                        }
                        path += strIndex + ".jpg";
                        bit = new Bitmap(img);
                        if (isSave)
                            bit.Save(ConfigHelper.StrImagePath + path, ImageFormat.Jpeg);
                        //using (var hanZi = new Bitmap(img).Clone(BaiDuCode.rtgHanZi, PixelFormat.DontCare))
                        //{
                        //    hanZi.Save(AppDomain.CurrentDomain.BaseDirectory + path, ImageFormat.Jpeg);
                        //    hanZi.Dispose();
                        //}
                        img.Dispose();
                    }
                }
            }
            catch (Exception oe)
            {
                Console.WriteLine(oe.Message);
            }
            return bit;
        }

        public static Bitmap GetImageByPath(string path)
        {
            Bitmap bit = null;
            try
            {
                if (!File.Exists(path))
                {
                    if (File.Exists(ConfigHelper.StrImagePath + path))
                    {
                        using (var img = CommonCompress.FastFromFile(ConfigHelper.StrImagePath + path))
                        {
                            bit = new Bitmap(img);
                            img.Dispose();
                        }
                    }
                }
                else
                {
                    using (var img = CommonCompress.FastFromFile(path))
                    {
                        bit = new Bitmap(img);
                        img.Dispose();
                    }
                }
            }
            catch (Exception oe)
            {
                Console.WriteLine(oe.Message);
            }
            return bit;
        }

        public static Bitmap GetImageFromByts(byte[] byts)
        {
            Bitmap bit = null;
            try
            {
                using (var ms = new MemoryStream(byts))
                {
                    using (var img = Image.FromStream(ms))
                    {
                        bit = new Bitmap(img);
                        img.Dispose();
                    }
                }
            }
            catch (Exception oe)
            {
                Console.WriteLine(oe.Message);
            }
            return bit;
        }
        public static byte[] Bitmap2Byte(Bitmap bitmap)
        {
            using (MemoryStream stream = new MemoryStream())
            {
                bitmap.Save(stream, JpgInfo, JpgParams);
                byte[] data = new byte[stream.Length];
                stream.Seek(0, SeekOrigin.Begin);
                stream.Read(data, 0, Convert.ToInt32(stream.Length));
                return data;
            }
        }

        public static Bitmap GetImageFromBase64(string base64String)
        {
            Bitmap bit = null;
            try
            {
                var b = Convert.FromBase64String(base64String.Replace("%2B", "+"));
                bit = GetImageFromByts(b);
            }
            catch (Exception oe)
            {
                Console.WriteLine(oe.Message);
            }
            return bit;
        }

        public static int NHanZiStart = 117;

        public static int NHanZiLoginWidth = 170;

        public static int NHanZiOrderWidth = 176;

        public static int NHanZiHeight = 30;

        public static string GetHanZiFromBytes(byte[] byts, bool isLogin = false)
        {
            using (var bit = GetImageFromByts(byts))
            {
                return CommonCompress.GetBase64ByImage(bit, CommonCompress.NHanZiStart, CommonCompress.NHanZiHeight
                    , isLogin ? CommonCompress.NHanZiLoginWidth : CommonCompress.NHanZiOrderWidth);
            }
        }

        public static byte[] GetByteFromBase64(string base64String)
        {
            byte[] bit = null;
            try
            {
                bit = Convert.FromBase64String(base64String.Replace("%2B", "+"));
            }
            catch (Exception oe)
            {
                Console.WriteLine(oe.Message);
            }
            return bit;
        }

        public static string ToBase64Str(string strOld)
        {
            var result = "";
            try
            {
                var b = Encoding.UTF8.GetBytes(strOld);
                result = Convert.ToBase64String(b).Replace("+", "%2B");
            }
            catch (Exception oe)
            {
                Console.WriteLine(oe.Message);
            }
            return result;
        }

        public static string GetBase64StrByBase64(string strOld)
        {
            return strOld.Replace("+", "%2B");
        }

        /// <summary>
        ///     字符串压缩
        /// </summary>
        /// <param name="data"></param>
        /// <returns></returns>
        private static byte[] Compress(byte[] data)
        {
            try
            {
                var ms = new MemoryStream();
                var zip = new GZipStream(ms, CompressionMode.Compress, true);
                zip.Write(data, 0, data.Length);
                zip.Close();
                var buffer = new byte[ms.Length];
                ms.Position = 0;
                ms.Read(buffer, 0, buffer.Length);
                ms.Close();
                return buffer;
            }
            catch (Exception e)
            {
                throw new Exception(e.Message);
            }
        }

        /// <summary>
        ///     字符串解压缩
        /// </summary>
        /// <param name="data"></param>
        /// <returns></returns>
        private static byte[] Decompress(byte[] data)
        {
            try
            {
                var ms = new MemoryStream(data);
                var zip = new GZipStream(ms, CompressionMode.Decompress, true);
                var msreader = new MemoryStream();
                var buffer = new byte[0x1000];
                while (!ConfigHelper.IsExit)
                {
                    var reader = zip.Read(buffer, 0, buffer.Length);
                    if (reader <= 0)
                    {
                        break;
                    }
                    msreader.Write(buffer, 0, reader);
                }
                zip.Close();
                ms.Close();
                msreader.Position = 0;
                buffer = msreader.ToArray();
                msreader.Close();
                return buffer;
            }
            catch (Exception e)
            {
                throw new Exception(e.Message);
            }
        }

        public static string CompressString(string str)
        {
            var compressString = "";
            var compressBeforeByte = Encoding.GetEncoding("UTF-8").GetBytes(str);
            var compressAfterByte = Compress(compressBeforeByte);
            //compressString = Encoding.GetEncoding("UTF-8").GetString(compressAfterByte);  
            compressString = Convert.ToBase64String(compressAfterByte);
            return compressString;
        }

        public static string DecompressString(string str)
        {
            var compressString = "";
            //byte[] compressBeforeByte = Encoding.GetEncoding("UTF-8").GetBytes(str);  
            var compressBeforeByte = Convert.FromBase64String(str);
            var compressAfterByte = Decompress(compressBeforeByte);
            compressString = Encoding.GetEncoding("UTF-8").GetString(compressAfterByte);
            return compressString;
        }

        public static string ByteToString(byte[] bytes)
        {
            var strBuilder = new StringBuilder();
            foreach (var bt in bytes)
            {
                strBuilder.AppendFormat("{0:X2}", bt);
            }
            return strBuilder.ToString();
        }

        public static byte[] StringToByte(string str)
        {
            var bytes = new byte[str.Length / 2];
            for (var i = 0; i < str.Length / 2; i++)
            {
                var btvalue = Convert.ToInt32(str.Substring(i * 2, 2), 16);
                bytes[i] = (byte)btvalue;
            }
            return bytes;
        }

        private static ImageCodecInfo jpgInfo;

        public static ImageCodecInfo JpgInfo
        {
            get
            {
                if (jpgInfo == null)
                {
                    jpgInfo = GetEncoder();
                }
                return CommonCompress.jpgInfo;
            }
            set { CommonCompress.jpgInfo = value; }
        }
        private static EncoderParameters jpgParams;

        public static EncoderParameters JpgParams
        {
            get
            {
                if (jpgParams == null)
                {
                    jpgParams = new EncoderParameters();
                    var myEncoderParameter = new EncoderParameter(System.Drawing.Imaging.Encoder.Quality, 100L);
                    jpgParams.Param[0] = myEncoderParameter;
                }
                return CommonCompress.jpgParams;
            }
            set { CommonCompress.jpgParams = value; }
        }

        public static string GetBase64ByImage(Bitmap bit, int start = 0, int height = 30, int width = 0, bool isFirst = true)
        {
            var base64 = "";
            byte[] byt = null;
            try
            {
                lock (bit)
                {
                    using (var ms = new MemoryStream())
                    {
                        using (var bitmap = CaptureImage(bit, start, height, width))
                        {
                            bitmap.Save(ms, JpgInfo, JpgParams);
                            //var fileName = @"D:\助手\Image\0108\Old\" + Guid.NewGuid().ToString() + ".jpg";
                            //bitmap.Save(fileName, jpgInfo, jpgParams);
                            //byt = File.ReadAllBytes(fileName);
                            ms.Position = 0;
                            byt = new byte[ms.Length];
                            ms.Read(byt, 0, Convert.ToInt32(ms.Length));
                            ms.Close();
                            bitmap.Dispose();
                        }
                    }
                }
                if (byt.Length > 0)
                {
                    base64 = Convert.ToBase64String(byt);
                }
            }
            catch (Exception oe)
            {
                //Console.WriteLine(oe.Message);
                if (isFirst && oe.Message.Contains("对象当前正在其他地方使用") || oe.Message.ToLower().Contains("object is currently"))
                {
                    isFirst = false;
                    return GetBase64ByImage((Bitmap)bit.Clone(), start, height, width, isFirst);
                }
            }
            finally
            {
                byt = null;
            }
            return base64;
        }

        public static string GetBase64ByImageFile(string strFile, int start = 0, int height = 30, int width = 0)
        {
            var base64 = "";
            byte[] byt = null;
            try
            {
                using (var ms = new MemoryStream())
                {
                    using (var bitmap = CaptureImage(strFile, start, height, width))
                    {
                        //var fileName = @"D:\助手\Image\0108\Old\" + Guid.NewGuid().ToString() + ".jpg";
                        //Bitmap.Save(fileName);
                        //byt = File.ReadAllBytes(fileName);
                        bitmap.Save(ms, JpgInfo, JpgParams);
                        ms.Position = 0;
                        byt = new byte[ms.Length];
                        ms.Read(byt, 0, Convert.ToInt32(ms.Length));
                        ms.Flush();
                        bitmap.Dispose();
                    }
                }
                if (byt.Length > 0)
                {
                    base64 = Convert.ToBase64String(byt);
                }
            }
            catch (Exception oe)
            {
                Console.WriteLine(oe.Message);
            }
            finally
            {
                byt = null;
            }
            return base64;
        }

        //public static byte[] GetBase64ByImageFile(string strFile, int start = 0, int height = 30, int width = 0)
        //{
        //    byte[] byt = null;
        //    try
        //    {
        //        using (var ms = new MemoryStream())
        //        {
        //            using (var Bitmap = CaptureImage(strFile, start, height, width))
        //            {
        //                Bitmap.Save(ms, ImageFormat.Png);
        //                ms.Position = 0;
        //                byt = new byte[ms.Length];
        //                ms.Read(byt, 0, Convert.ToInt32(ms.Length));
        //                ms.Flush();
        //            }
        //        }
        //    }
        //    catch (Exception oe)
        //    {
        //        Console.WriteLine(oe.Message);
        //    }
        //    return byt;
        //}

        public static string GetBase64ByImageFile1(string strFile)
        {
            var base64 = "";
            byte[] byt = null;
            try
            {
                using (var ms = new MemoryStream())
                {
                    using (var bitmap = CommonCompress.FastFromFile(strFile))
                    {
                        bitmap.Save(ms, JpgInfo, JpgParams);
                        ms.Position = 0;
                        byt = new byte[ms.Length];
                        ms.Read(byt, 0, Convert.ToInt32(ms.Length));
                        ms.Flush();
                    }
                }
                if (byt.Length > 0)
                {
                    base64 = Convert.ToBase64String(byt);
                }
            }
            catch (Exception oe)
            {
                Console.WriteLine(oe.Message);
            }
            finally
            {
                byt = null;
            }
            return base64;
        }

        /// <summary>
        /// 从图片中截取部分生成新图
        /// </summary>
        /// <param name="sFromFilePath "> 原始图片 </param>
        private static Bitmap CaptureImage(string sFromFilePath, int start = 0, int height = 30, int width = 0)
        {
            Bitmap bitmap = null;

            //载入底图 
            using (var fromImage = CommonCompress.FastFromFile(sFromFilePath))
            {
                //创建新图位图 
                bitmap = CaptureImage(new Bitmap(fromImage), start, height, width);
            }
            return bitmap;
        }

        public static Bitmap CaptureImage(Bitmap bit, int start = 0, int height = 30, int width = 0)
        {
            Bitmap bitmap = null;

            if (width > 0)
            {
                //创建新图位图 
                bitmap = new Bitmap(width, height);
            }
            else
            {
                //创建新图位图
                bitmap = new Bitmap(bit.Width - start, height);
            }

            // 目标区域
            Rectangle destRect = new Rectangle(0, 0, bitmap.Width, bitmap.Height);
            // 源图区域
            Rectangle srcRect = new Rectangle(start, 0, bitmap.Width, bitmap.Height);

            //for (int i = 0; i < bitmap.Width; i++)
            //{
            //    for (int j = 0; j < bitmap.Height; j++)
            //    {
            //        bitmap.SetPixel(i, j, bit.GetPixel(start + i, j));
            //    }
            //}
            //创建作图区域 
            using (var graphic = Graphics.FromImage(bitmap))
            {
                graphic.SmoothingMode = SmoothingMode.HighQuality;
                graphic.CompositingQuality = CompositingQuality.HighQuality;
                //截取原图相应区域写入作图区 
                graphic.DrawImage(bit, destRect, srcRect, GraphicsUnit.Pixel);
                graphic.Dispose();
            }
            //bitmap.Save(@"D:\助手\Image\0108\Old\" + Guid.NewGuid().ToString() + ".jpg", ImageFormat.Jpeg);
            return bitmap;
        }

        private static ImageCodecInfo GetEncoder(string strType = "image/jpeg")
        {
            ImageCodecInfo[] codecs = ImageCodecInfo.GetImageDecoders();
            foreach (ImageCodecInfo codec in codecs)
            {
                if (codec.MimeType == strType)
                {
                    return codec;
                }
            }
            return null;
        }

        /// <summary> 
        /// 将其它格式的图片转为JPG文件 
        /// </summary> 
        public static Bitmap ToJPG(Bitmap source)
        {
            MemoryStream ms = new MemoryStream();
            source.Save(ms, ImageFormat.Jpeg);
            return new Bitmap(Bitmap.FromStream(ms));
        }

        ///// <summary> 
        ///// 将其它格式的图片转为PNG文件 
        ///// </summary> 
        //public static Bitmap ToPNG(Bitmap source)
        //{
        //    //注意，先定义Bitmap类，否则会报A generic error occurred in GDI+ 
        //    Bitmap bmp = new Bitmap(source);
        //    MemoryStream ms = new MemoryStream();
        //    bmp.Save(ms, ImageFormat.Png);
        //    return FromBytes(ms.ToArray());
        //}
    }
}