﻿using CommonLib;
using System.Collections.Generic;

namespace HanZiOcr
{
    /// <summary>
    /// https://cloud.tencent.com/document/product/866/33526
    /// </summary>
    public class TencentAPIBasicRec : BaseOcrRec
    {
        public TencentAPIBasicRec()
        {
            OcrGroup = OcrGroupType.腾讯;
            OcrType = HanZiOcrType.腾讯印刷体;
            MaxExecPerTime = 18;

            LstJsonPreProcessArray = new List<object>() { "Response", "TextDetections" };
            IsSupportVertical = true;
            IsSupportUrlOcr = true;
            DicDeserializeVerticalJson = new Dictionary<string, string> { { "left", "X" }, { "top", "Y" }, { "height", "Height" }, { "width", "Width" }, { "location", "ItemPolygon" }, { "words", "DetectedText" } };
        }

        protected override string GetHtml(OcrContent content)
        {
            var postStr = string.IsNullOrEmpty(content.url) ? "{\"ImageBase64\":\"" + content.strBase64 + "\"}" : "{\"ImageUrl\":\"" + content.url + "\"}";
            var result = TencentCloudSignHelper.DoRequest("ocr", "ocr.tencentcloudapi.com", "ap-shanghai", "GeneralBasicOCR", "2018-11-19", postStr);
            return result;
        }

        public override string GetHtmlByUrl(OcrContent content)
        {
            return GetHtml(content);
        }
    }
}