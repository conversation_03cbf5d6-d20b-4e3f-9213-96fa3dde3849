﻿using System;
using System.Collections.Generic;
using System.Collections.Specialized;
using System.IO;
using System.Security.Cryptography;
using System.Text;
using System.Web.Script.Serialization;

namespace CommonLib
{
    public class HanWangHelper
    {
        //static HanWangHelper()
        //{
        //    var lstAccount = new List<AccountDto>
        //    {
        //        new AccountDto() { secretKey = "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.*******************************************************************************************.1HMCoRKSHTyY0jE16fT4-FEJWV7NFxg4Ok5h0qY3KG4" },
        //        new AccountDto() { secretKey = "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.*******************************************************************************************.fhGVd9r50t00Y-NQhrIQvfaTHFYG0xQAjXenqAl9VPs" },
        //        new AccountDto() { secretKey = "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.*******************************************************************************************.Jc809XXjr9YM1emd-ifCsJgxU25UaFAnDmrbr1zWaDc" },
        //        new AccountDto() { secretKey = "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.*******************************************************************************************.AiXYoW8cJqf0BuPTLCJGKzHe57Lv2inHkq06dogGJ5g" }
        //    };
        //    AccountHelper.RegAccount(OcrGroupType.汉王.GetHashCode(), lstAccount);
        //}

        //public static string GetToken()
        //{
        //    return AccountHelper.GetAccount(OcrGroupType.汉王.GetHashCode())?.secretKey;
        //}

        const string accessId = "64377f4ab69078ccd83f0794";
        private static JavaScriptSerializer JavaScriptSerializer = new JavaScriptSerializer();

        public static NameValueCollection GetHeader(string method = "POST")
        {
            var utcTime = DateTime.UtcNow.ToString("yyyy-MM-ddTHH:mm:ss");
            var nonce = Guid.NewGuid().ToString().ToUpper();
            var head = new NameValueCollection() {
                { "x-rs-access-id",accessId},
                { "x-rs-nonce",nonce},
                { "x-rs-timestamp",utcTime},
                { "x-rs-signature",getSignature(method,nonce,utcTime)},
                { "xweb_xhr","1"},
                { "Referer","https://servicewechat.com/wx32da44f0a1f83498/5/page-frame.html"}
            };

            return head;
        }

        public static string Upload(byte[] content, string ext)
        {
            var result = string.Empty;
            var head = GetHeader("GET");
            var html = WebClientSyncExt.GetHtml("https://api.hanvonscanner.com/file-system/file/upload/aliyun/sts?platformId=aliyun-data&resourcePath=ocr-process-temp", "", 10, head);
            if (!string.IsNullOrEmpty(html) && html.Length > 2)
            {
                try
                {
                    var token = JavaScriptSerializer.Deserialize<AliYunTokenRoot>(html)?.data;

                    if (token != null && !string.IsNullOrEmpty(token.accessKeyId))
                    {
                        var localFile = "ocr-process-temp/miniAPP/wxfile://temp/" + Guid.NewGuid().ToString().ToLower() + "." + ext;

                        var client = new Aliyun.OSS.OssClient(token.endpoint, token.accessKeyId, token.accessKeySecret, token.securityToken);
                        try
                        {
                            client.PutObject(token.bucketName, localFile, new MemoryStream(content));
                            result = token.ossEndpoint + "/" + localFile;
                        }
                        catch (Exception ex)
                        {
                            Console.WriteLine(ex.Message);
                        }
                    }
                }
                catch { }
            }
            return result;
        }

        private static string getSignature(string method, string nonce, string timeSpan)
        {
            var n = method + "&" + accessId + "&" + nonce + "&" + timeSpan;
            return HmacSha1Sign(n, "4r94huhxs2o0t3p5dc6a5yz67vmwijuc");
        }

        private static string HmacSha1Sign(string str, string key)
        {
            using (HMACSHA1 hmac = new HMACSHA1(Encoding.UTF8.GetBytes(key)))
            {
                var byt = hmac.ComputeHash(Encoding.UTF8.GetBytes(str));
                return Convert.ToBase64String(byt);
            }
        }

        class AliYunTokenRoot
        {
            public AliYunToken data { get; set; }
        }

        class AliYunToken
        {
            public string bucketName { get; set; }

            public string endpoint { get; set; }

            public string ossEndpoint { get; set; }

            public string region { get; set; }

            public string accessKeyId { get; set; }

            public string accessKeySecret { get; set; }

            public string securityToken { get; set; }

            public string expiration { get; set; }

            public string resourcePath { get; set; }
        }
    }
}
