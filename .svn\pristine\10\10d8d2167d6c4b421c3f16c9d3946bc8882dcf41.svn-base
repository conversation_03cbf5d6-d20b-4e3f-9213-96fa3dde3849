﻿using Code.Process.Common;
using CommonLib;
using System.Web;

namespace Code.Process.Web
{
    /// <summary>
    /// Code 的摘要说明
    /// </summary>
    public class Code : IHttpHandler
    {

        private const string OpParam = "op";
        private const string DefaultContentType = "text/plain";

        public void ProcessRequest(HttpContext context)
        {
            var action = context.Request.Params[OpParam];
            if (string.IsNullOrEmpty(action))
            {
                return;
            }
            context.Response.ContentType = DefaultContentType;

            var result = "success";
            switch (action)
            {
                case "ocr":
                    var json = context.Request.QueryString["param"];
                    if (!string.IsNullOrEmpty(json))
                    {
                        json = HttpUtility.UrlDecode(json);
                        ProcessNew.ProcessOcrByImg(json, true);
                    }
                    break;
                default:
                    break;
            }
            context.Response.Write(result);
        }

        public bool IsReusable
        {
            get
            {
                return false;
            }
        }
    }
}