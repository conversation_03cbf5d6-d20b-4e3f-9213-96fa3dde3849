﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net;
using System.Text;

namespace NewTicket
{
    public class DingDingMessageUtil
    {

        public static String access_token = "c42a0474a2ce1b800764661144507e0be03b9e399c0ae3691357b2fa709d8522";

        public static void sendTextMessage(String msg)
        {
            try
            {
                DingMessage message = new DingMessage();
                message.Msgtype = ("link");
                message.Text = (new DingMessageInfo(msg));
                var url = ("https://oapi.dingtalk.com/robot/send?access_token=" + access_token);
                System.Net.HttpWebRequest request;
                request = (System.Net.HttpWebRequest)WebRequest.Create(url);
                request.Method = "POST";
                request.ContentType = "application/json;charset=UTF-8";

                byte[] payload;
                string paraUrlCoded = "{\"msgtype\": \"link\", \"link\": {\"text\": \"定票成功\", \"title\": \"" + msg + "\", \"picUrl\": \"\", \"messageUrl\": \"https://kyfw.12306.cn/otn/queryOrder/initNoComplete\"}}";
                paraUrlCoded = JavaScriptSerializerExtensions.Serialize(message);
                payload = System.Text.Encoding.UTF8.GetBytes(paraUrlCoded);
                request.ContentLength = payload.Length;
                Stream writer = request.GetRequestStream();
                writer.Write(payload, 0, payload.Length);
                writer.Close();
                System.Net.HttpWebResponse response;
                response = (System.Net.HttpWebResponse)request.GetResponse();
                System.IO.Stream s;
                s = response.GetResponseStream();
                string StrDate = "";
                string strValue = "";
                StreamReader Reader = new StreamReader(s, Encoding.UTF8);
                while ((StrDate = Reader.ReadLine()) != null)
                {
                    strValue += StrDate + "\r\n";
                }
                var html = strValue;
            }
            catch (Exception e)
            {
                Console.WriteLine(e.Message);
            }
        }
        public static void main(String[] args)
        {
            DingDingMessageUtil.sendTextMessage("hello");
        }
    }

    class DingMessage
    {
        public DingMessageInfo Text { get; set; }
        public String Msgtype { get; set; }
    }

    class DingMessageInfo
    {
        public String Content { get; set; }
        public DingMessageInfo(String content)
        {
            Content = content;
        }
    }
}
