﻿
using CommonLib;
using Newtonsoft.Json;
using System.Collections.Generic;
using System.Linq;

namespace HanZiOcr
{
    /// <summary>
    /// 旷视AI
    /// https://console.faceplusplus.com.cn/documents/7776484
    /// </summary>
    public class FaceAIRec : BaseOcrRec
    {
        public FaceAIRec()
        {
            OcrType = HanZiOcrType.FaceAI;
            MaxExecPerTime = 10;

            LstJsonPreProcessArray = new List<object>() { "result" };
            LstJsonNextProcessArray = new List<object>() { "child-objects" };
            StrResultJsonSpilt = "value";
            IsSupportUrlOcr = true;

            IsSupportVertical = true;
            LstVerticalLocation = new List<object>() { "position" };
        }

        private List<string> lstKeys = new List<string>() {
            "api_key=4i_tGzJZynGTBmHQpm8T5C8wy7_W1wXW&api_secret=yIA4shNJd-iMJrDDCC-dmKa0CM6RQhvO"
            ,"api_key=4diXTcIKqzKR1kE7HqJxQhdSKoDaQlQS&api_secret=CBrU3vHl8sPdkEUc2efZaBBhhdZRBaX4"
            ,"api_key=0Gcc0MBy-ySxBnoyMD48qZPjvPH0vEDd&api_secret=tEPE3RGbmz-4H8EtQCIo2YIHfO_Cmxml",
            "api_key=T_ohwyZ4qPexYQGOM6Qpp3-8tRxums_U&api_secret=hlXd8o2G_e_Q6_1FlXvBt09dohPQ9zg-"
        };

        protected override string GetHtml(OcrContent content)
        {
            return RequestHtmlContent(content.strBase64);
        }

        public override string GetHtmlByUrl(OcrContent content)
        {
            return RequestHtmlContent(null, content.url);
        }

        private string RequestHtmlContent(string strBase64, string imgUrl = null)
        {
            var strPost = lstKeys.GetRndItem()
                + (string.IsNullOrEmpty(imgUrl) ?
                "&image_base64=" + System.Web.HttpUtility.UrlEncode(strBase64)
                : "&image_url=" + System.Web.HttpUtility.UrlEncode(imgUrl));
            var url = "https://api-cn.faceplusplus.com/imagepp/v1/recognizetext";
            var strTmp = WebClientSyncExt.GetHtml(url, strPost, ExecTimeOutSeconds);

            return BoxUtil.DeUnicode(strTmp);
        }

        /*
         [
          {
            "y": 248,
            "x": 125
          },
          {
            "y": 248,
            "x": 136
          },
          {
            "y": 259,
            "x": 137
          },
          {
            "y": 260,
            "x": 126
          }
        ]
         */
        protected override LocationInfo GetLocationByStr(string locationInfoStr)
        {
            var lstLocations = JsonConvert.DeserializeObject<List<VLocationInfo2>>(locationInfoStr, new JsonSerializerSettings());

            LocationInfo location = null;
            if (lstLocations?.Count == 4)
            {
                var minLeft = lstLocations.Select(p => p.x).Min();
                var minTop = lstLocations.Select(p => p.y).Min();
                var maxLeft = lstLocations.Select(p => p.x).Max();
                var maxTop = lstLocations.Select(p => p.y).Max();
                location = new LocationInfo()
                {
                    left = (int)minLeft,
                    top = (int)minTop,
                    width = (int)(maxLeft - minLeft),
                    height = (int)(maxTop - minTop),
                };
            }
            return location;
        }
    }
}