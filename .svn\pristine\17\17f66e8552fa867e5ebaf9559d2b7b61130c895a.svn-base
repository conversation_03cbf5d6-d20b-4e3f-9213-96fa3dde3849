﻿using Microsoft.AspNet.SignalR.Client;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.IO.Compression;
using System.Linq;
using System.Reflection;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace CommonLib
{
    #region 接口定义

    /// <summary>
    /// 连接管理接口 - 负责核心连接功能
    /// </summary>
    public interface IConnectionManager : IDisposable
    {
        Task<bool> StartAsync();
        Task StopAsync();
        Task<bool> SendAsync(string method, params object[] args);
        void On<T1, T2>(string eventName, Action<T1, T2> handler);
        void On<T>(string eventName, Action<T> handler);
        void On(string eventName, Action handler);

        /// <summary>
        /// 获取客户端ID
        /// </summary>
        string ClientId { get; }

        /// <summary>
        /// 获取当前连接状态
        /// </summary>
        ConnectionState ConnectionState { get; }

        /// <summary>
        /// 是否已连接
        /// </summary>
        bool IsConnected { get; }
    }

    /// <summary>
    /// 网络事件发布接口 - 定义通讯组件发布的事件
    /// </summary>
    public interface INetworkEventPublisher
    {
        /// <summary>
        /// 网络活动开始事件
        /// </summary>
        event EventHandler<NetworkActivityEventArgs> NetworkActivityStarted;

        /// <summary>
        /// 网络活动完成事件
        /// </summary>
        event EventHandler<NetworkActivityEventArgs> NetworkActivityCompleted;

        /// <summary>
        /// 连接状态变更事件
        /// </summary>
        event EventHandler<ConnectionStatusEventArgs> ConnectionStatusChanged;

        /// <summary>
        /// 消息发送完成事件
        /// </summary>
        event EventHandler<MessageSendEventArgs> MessageSendCompleted;

        /// <summary>
        /// 错误事件
        /// </summary>
        event EventHandler<ErrorEventArgs> ErrorOccurred;

        /// <summary>
        /// 传输信息更新事件
        /// </summary>
        event EventHandler<TransportInfoEventArgs> TransportInfoUpdated;
    }

    #endregion

    #region 事件参数和接口

    /// <summary>
    /// 网络活动事件参数，用于跟踪单个网络活动的完整生命周期
    /// </summary>
    public class NetworkActivityEventArgs : EventArgs
    {
        /// <summary>
        /// 活动ID
        /// </summary>
        public Guid ActivityId { get; }

        /// <summary>
        /// 活动类型
        /// </summary>
        public string ActivityType { get; }

        /// <summary>
        /// 时间戳
        /// </summary>
        public DateTime Timestamp { get; }

        /// <summary>
        /// 活动是否已完成
        /// </summary>
        public bool IsCompleted { get; private set; }

        /// <summary>
        /// 完成状态（成功/失败）
        /// </summary>
        public bool? IsSuccess { get; private set; }

        /// <summary>
        /// 活动持续时间
        /// </summary>
        public TimeSpan? Duration { get; private set; }

        /// <summary>
        /// 错误信息
        /// </summary>
        public Exception Error { get; private set; }

        // 构造函数 - 活动开始
        public NetworkActivityEventArgs(string activityType)
        {
            ActivityId = Guid.NewGuid();
            ActivityType = activityType;
            Timestamp = ServerTime.DateTime;
            IsCompleted = false;
        }

        // 标记活动完成
        public void MarkCompleted(bool success, TimeSpan duration, Exception error = null)
        {
            IsCompleted = true;
            IsSuccess = success;
            Duration = duration;
            Error = error;
        }
    }

    /// <summary>
    /// 传输信息事件参数
    /// </summary>
    public class TransportInfoEventArgs : EventArgs
    {
        /// <summary>
        /// 传输类型
        /// </summary>
        public string Transport { get; }

        /// <summary>
        /// 时间戳
        /// </summary>
        public DateTime Timestamp { get; }

        public TransportInfoEventArgs(string transport)
        {
            Transport = transport;
            Timestamp = ServerTime.DateTime;
        }
    }

    /// <summary>
    /// 连接状态事件参数
    /// </summary>
    public class ConnectionStatusEventArgs : EventArgs
    {
        /// <summary>
        /// 当前连接状态
        /// </summary>
        public ConnectionState State { get; }

        /// <summary>
        /// 前一个状态
        /// </summary>
        public ConnectionState PreviousState { get; }

        public TimeSpan? Latency { get; }

        /// <summary>
        /// 状态变更附加信息
        /// </summary>
        public string Message { get; }

        /// <summary>
        /// 时间戳
        /// </summary>
        public DateTime Timestamp { get; }

        public ConnectionStatusEventArgs(ConnectionState state, TimeSpan? latency = null, string message = null)
            : this(state, ConnectionState.Disconnected, message, latency)
        {
        }

        public ConnectionStatusEventArgs(
            ConnectionState state,
            ConnectionState previousState,
            string message = null,
            TimeSpan? latency = null)
        {
            State = state;
            PreviousState = previousState;
            Message = message;
            Latency = latency;
            Timestamp = ServerTime.DateTime;
        }
    }

    /// <summary>
    /// 消息发送结果事件参数
    /// </summary>
    public class MessageSendEventArgs : EventArgs
    {
        public string Method { get; }
        public bool Success { get; }
        public TimeSpan? SendTime { get; }
        public Exception Error { get; }

        /// <summary>
        /// 时间戳
        /// </summary>
        public DateTime Timestamp { get; }

        public MessageSendEventArgs(string method, bool success, TimeSpan? sendTime = null, Exception error = null)
        {
            Method = method;
            Success = success;
            SendTime = sendTime;
            Error = error;
            Timestamp = ServerTime.DateTime;
        }
    }

    /// <summary>
    /// 网络质量变化事件参数
    /// </summary>
    public class NetworkQualityEventArgs : EventArgs
    {
        /// <summary>
        /// 旧的网络质量
        /// </summary>
        public NetworkQuality OldQuality { get; }

        /// <summary>
        /// 新的网络质量
        /// </summary>
        public NetworkQuality NewQuality { get; }

        /// <summary>
        /// 当前延迟
        /// </summary>
        public TimeSpan? CurrentLatency { get; }

        /// <summary>
        /// 变化趋势(负值表示变差，正值表示变好)
        /// </summary>
        public int QualityTrend => (int)OldQuality - (int)NewQuality;

        /// <summary>
        /// 时间戳
        /// </summary>
        public DateTime Timestamp { get; }

        public NetworkQualityEventArgs(NetworkQuality oldQuality, NetworkQuality newQuality, TimeSpan? latency = null)
        {
            OldQuality = oldQuality;
            NewQuality = newQuality;
            CurrentLatency = latency;
            Timestamp = ServerTime.DateTime;
        }
    }

    /// <summary>
    /// 错误事件参数
    /// </summary>
    public class ErrorEventArgs : EventArgs
    {
        /// <summary>
        /// 发生的异常
        /// </summary>
        public Exception Error { get; }

        /// <summary>
        /// 错误来源
        /// </summary>
        public string Source { get; }

        /// <summary>
        /// 时间戳
        /// </summary>
        public DateTime Timestamp { get; }

        public ErrorEventArgs(Exception error, string source = null)
        {
            Error = error;
            Source = source;
            Timestamp = ServerTime.DateTime;
        }
    }

    /// <summary>
    /// 网络质量等级
    /// </summary>
    public enum NetworkQuality
    {
        极优,  // < 50ms
        优秀,  // 50 - 100ms
        良好,       // 100 - 200ms
        一般,       // 200 - 400ms
        较差,       // 400 - 1000ms
        严重,    // >= 1000ms 或连接问题
        未知    // 刚启动
    }

    /// <summary>
    /// 重连原因
    /// </summary>
    public enum ReconnectionReason
    {
        Manual,             // 手动触发
        ConnectionLost,     // 连接丢失
        Timeout,            // 操作超时
        HeartbeatFailure,   // 心跳失败
        SendFailure,        // 消息发送失败
        ServerError         // 服务器错误
    }

    /// <summary>
    /// 连接状态接口 - 简化后的状态模式核心
    /// </summary>
    internal interface IConnectionState
    {
        ConnectionState State { get; }
        Task<bool> ProcessAsync(ResilientSignalRClient client);
        Task<bool> SendMessageAsync(ResilientSignalRClient client, string method, object[] args);
    }

    #endregion

    #region 配置类

    /// <summary>
    /// 连接配置 
    /// </summary>
    public class ConnectionConfig
    {
        // 连接超时
        public TimeSpan ConnectionTimeout { get; set; } = TimeSpan.FromSeconds(30);

        // 压缩选项
        /// <summary>
        /// 是否启用数据压缩(大消息)
        /// </summary>
        public bool EnableCompression { get; set; } = true;

        /// <summary>
        /// 压缩阈值(字节)，超过此大小的消息将被压缩
        /// </summary>
        public int CompressionThreshold { get; set; } = 4096; // 4KB

        /// <summary>
        /// 是否根据网络质量自动调整压缩阈值
        /// </summary>
        public bool EnableDynamicCompressionThreshold { get; set; } = true;

        /// <summary>
        /// 创建适合跨洲高延迟网络的配置
        /// </summary>
        public static ConnectionConfig CreateHighLatencyConfig()
        {
            return new ConnectionConfig
            {
                ConnectionTimeout = TimeSpan.FromSeconds(30),
                CompressionThreshold = 1024,
                EnableCompression = true,
                EnableDynamicCompressionThreshold = true
            };
        }
    }

    /// <summary>
    /// 心跳配置
    /// </summary>
    public class HeartbeatConfig
    {
        // 心跳基础间隔
        public TimeSpan Interval { get; set; } = TimeSpan.FromSeconds(15);

        // 心跳超时
        public TimeSpan Timeout { get; set; } = TimeSpan.FromSeconds(10);

        // 最短和最长心跳间隔
        public TimeSpan MinInterval { get; set; } = TimeSpan.FromSeconds(5);
        public TimeSpan MaxInterval { get; set; } = TimeSpan.FromSeconds(30);

        // 连续失败多少次触发重连
        public int FailuresBeforeReconnect { get; set; } = 5;

        // 自适应心跳参数
        /// <summary>
        /// 是否启用自适应心跳
        /// </summary>
        public bool EnableAdaptiveHeartbeat { get; set; } = true;

        /// <summary>
        /// 如果最近有网络活动，跳过心跳的时间窗口
        /// </summary>
        public TimeSpan ActivityWindow { get; set; } = TimeSpan.FromSeconds(5);

        /// <summary>
        /// 创建适合跨洲高延迟网络的心跳配置
        /// </summary>
        public static HeartbeatConfig CreateHighLatencyConfig()
        {
            return new HeartbeatConfig
            {
                Interval = TimeSpan.FromSeconds(20),
                MinInterval = TimeSpan.FromSeconds(10),
                MaxInterval = TimeSpan.FromSeconds(40),
                Timeout = TimeSpan.FromSeconds(30),
                FailuresBeforeReconnect = 3,
                EnableAdaptiveHeartbeat = true,
                ActivityWindow = TimeSpan.FromSeconds(5)
            };
        }
    }

    /// <summary>
    /// 重连配置
    /// </summary>
    public class ReconnectionConfig
    {
        // 最大重连次数，-1表示无限重试
        public int MaxAttempts { get; set; } = -1;

        // 初始延迟
        public TimeSpan InitialDelay { get; set; } = TimeSpan.FromSeconds(1);

        // 最大延迟
        public TimeSpan MaxDelay { get; set; } = TimeSpan.FromSeconds(30);

        // 退避指数
        public double BackoffExponent { get; set; } = 1.5;

        // 随机抖动因子 (0-1之间)
        public double JitterFactor { get; set; } = 0.1;

        /// <summary>
        /// 创建适合跨洲高延迟网络的重连配置
        /// </summary>
        public static ReconnectionConfig CreateHighLatencyConfig()
        {
            return new ReconnectionConfig
            {
                InitialDelay = TimeSpan.FromSeconds(2),
                MaxDelay = TimeSpan.FromMinutes(2),
                BackoffExponent = 1.5,
                JitterFactor = 0.3,
                MaxAttempts = -1
            };
        }
    }

    /// <summary>
    /// 诊断配置类，用于控制监控行为
    /// </summary>
    public class DiagnosticConfig
    {
        /// <summary>
        /// 是否启用诊断
        /// </summary>
        public bool EnableDiagnostics { get; set; } = true;

        /// <summary>
        /// 最大样本数量
        /// </summary>
        public int MaxSampleCount { get; set; } = 100;

        /// <summary>
        /// 统计时间窗口（用于计算统计数据）
        /// </summary>
        public TimeSpan StatisticsWindow { get; set; } = TimeSpan.FromMinutes(5);

        /// <summary>
        /// 创建默认配置
        /// </summary>
        public static DiagnosticConfig CreateDefault()
        {
            return new DiagnosticConfig
            {
                EnableDiagnostics = true,
                MaxSampleCount = 100,
                StatisticsWindow = TimeSpan.FromMinutes(5)
            };
        }

        /// <summary>
        /// 创建不启用诊断的配置
        /// </summary>
        public static DiagnosticConfig CreateDisabled()
        {
            return new DiagnosticConfig
            {
                EnableDiagnostics = false
            };
        }
    }

    #endregion

    /// <summary>
    /// 网络诊断服务 - 基于事件的网络监控和统计
    /// </summary>
    public class NetworkDiagnosticService : IDisposable
    {
        private readonly object _lock = new object();
        private DateTime _lastActivityTime = ServerTime.DateTime;
        private readonly List<(DateTime timestamp, TimeSpan latency)> _latencySamples = new List<(DateTime timestamp, TimeSpan latency)>(100);
        private readonly Queue<double> _recentRTTs = new Queue<double>(100);
        private readonly Dictionary<Guid, DateTime> _pendingActivities = new Dictionary<Guid, DateTime>();
        private bool _enabled;

        // 配置
        private readonly DiagnosticConfig _config;

        // 健康报告
        private readonly ConnectionHealth _healthReport = new ConnectionHealth();

        // 事件
        public event EventHandler<NetworkQualityEventArgs> NetworkQualityChanged;

        public NetworkDiagnosticService(DiagnosticConfig config = null)
        {
            _config = config ?? DiagnosticConfig.CreateDefault();
            _enabled = _config.EnableDiagnostics;
            Reset();
        }

        /// <summary>
        /// 启用诊断
        /// </summary>
        public void Enable()
        {
            lock (_lock)
            {
                _enabled = true;
            }
        }

        /// <summary>
        /// 禁用诊断
        /// </summary>
        public void Disable()
        {
            lock (_lock)
            {
                _enabled = false;
            }
        }

        /// <summary>
        /// 获取健康报告
        /// </summary>
        public ConnectionHealth GetHealthReport()
        {
            return _healthReport;
        }

        /// <summary>
        /// 重置诊断数据
        /// </summary>
        public void Reset()
        {
            if (!_enabled) return;

            lock (_lock)
            {
                _lastActivityTime = ServerTime.DateTime;
                _latencySamples.Clear();
                _recentRTTs.Clear();
                _pendingActivities.Clear();

                _healthReport.TotalInteractions = 0;
                _healthReport.SuccessfulInteractions = 0;
                _healthReport.FailedInteractions = 0;
                _healthReport.LastLatency = null;
                _healthReport.AverageLatency = null;
                _healthReport.P80AverageLatency = null;
                _healthReport.Last5MinuteAverageLatency = null;
                _healthReport.MinLatency = null;
                _healthReport.MaxLatency = null;
                _healthReport.Jitter = null;
                _healthReport.Quality = NetworkQuality.未知;
                // 不重置连接计数和断开计数
            }
        }

        #region 事件处理方法

        /// <summary>
        /// 处理网络活动开始事件
        /// </summary>
        internal void OnNetworkActivityStarted(object sender, NetworkActivityEventArgs e)
        {
            if (!_enabled || e.IsCompleted) return;

            lock (_lock)
            {
                if (!e.IsCompleted) // 只处理开始事件
                {
                    _pendingActivities[e.ActivityId] = e.Timestamp;
                }
            }
        }

        /// <summary>
        /// 处理网络活动完成事件
        /// </summary>
        internal void OnNetworkActivityCompleted(object sender, NetworkActivityEventArgs e)
        {
            if (!_enabled) return;

            if (!e.IsCompleted || !e.Duration.HasValue) return;

            lock (_lock)
            {
                _lastActivityTime = e.Timestamp;
                _healthReport.TotalInteractions++;
                _healthReport.LastActivityTime = _lastActivityTime;

                if (e.IsSuccess.HasValue && e.IsSuccess.Value)
                {
                    _healthReport.SuccessfulInteractions++;
                    _healthReport.LastLatency = e.Duration.Value;

                    // 更新延迟统计
                    _latencySamples.Add((e.Timestamp, e.Duration.Value));

                    if (_latencySamples.Count > _config.MaxSampleCount)
                        _latencySamples.RemoveAt(0);

                    // 更新RTT历史和抖动
                    _recentRTTs.Enqueue(e.Duration.Value.TotalMilliseconds);
                    if (_recentRTTs.Count > _config.MaxSampleCount)
                        _recentRTTs.Dequeue();

                    UpdateLatencyStatistics();

                    // 更新网络质量
                    UpdateNetworkQuality(e.Duration.Value);
                }
                else
                {
                    _healthReport.FailedInteractions++;
                }

                // 移除待处理活动
                _pendingActivities.Remove(e.ActivityId);
            }
        }

        /// <summary>
        /// 处理传输信息更新事件
        /// </summary>
        internal void OnTransportInfoUpdated(object sender, TransportInfoEventArgs e)
        {
            if (!_enabled) return;

            lock (_lock)
            {
                _healthReport.Transport = e.Transport;
            }
        }

        /// <summary>
        /// 处理连接状态变化事件
        /// </summary>
        internal void OnConnectionStatusChanged(object sender, ConnectionStatusEventArgs e)
        {
            if (!_enabled) return;

            lock (_lock)
            {
                // 更新当前状态
                _healthReport.State = e.State;

                // 改进：只有在状态确实发生变化且变为Connected时才增加连接计数
                if (e.State == ConnectionState.Connected && e.PreviousState != ConnectionState.Connected)
                {
                    _healthReport.ConnectionCount++;
                }
                // 只有在状态确实从Connected变为Disconnected时才增加断开计数
                else if (e.State == ConnectionState.Disconnected && e.PreviousState == ConnectionState.Connected)
                {
                    _healthReport.DisconnectionCount++;
                }
            }
        }

        /// <summary>
        /// 处理消息发送事件
        /// </summary>
        internal void OnMessageSendCompleted(object sender, MessageSendEventArgs e)
        {
            if (!_enabled) return;

            // 已通过网络活动事件处理，这里不需要额外处理
        }

        /// <summary>
        /// 处理错误事件
        /// </summary>
        internal void OnErrorOccurred(object sender, ErrorEventArgs e)
        {
            if (!_enabled) return;

            // 记录错误信息，目前仅记录最后错误
            lock (_lock)
            {
                _healthReport.LastError = e.Error;
                _healthReport.LastErrorTime = e.Timestamp;
                _healthReport.LastErrorSource = e.Source;
            }
        }

        #endregion

        /// <summary>
        /// 更新网络质量
        /// </summary>
        private void UpdateNetworkQuality(TimeSpan latency)
        {
            NetworkQuality newQuality = CalculateNetworkQuality(latency);

            if (_healthReport.Quality != newQuality)
            {
                var oldQuality = _healthReport.Quality;
                _healthReport.Quality = newQuality;

                _healthReport.Latency = latency.TotalMilliseconds;

                // 触发网络质量变化事件
                NetworkQualityChanged?.Invoke(this, new NetworkQualityEventArgs(oldQuality, newQuality, latency));
            }
        }

        /// <summary>
        /// 计算网络质量
        /// </summary>
        private NetworkQuality CalculateNetworkQuality(TimeSpan latency)
        {
            // 根据延迟评估网络质量
            double ms = latency.TotalMilliseconds;

            if (ms < 50)
                return NetworkQuality.极优;
            else if (ms >= 50 && ms < 100)
                return NetworkQuality.优秀;
            else if (ms >= 100 && ms < 150)
                return NetworkQuality.良好;
            else if (ms >= 150 && ms < 200)
                return NetworkQuality.一般;
            else if (ms >= 200 && ms < 300)
                return NetworkQuality.较差;
            else
                return NetworkQuality.严重;
        }

        /// <summary>
        /// 更新延迟统计数据
        /// </summary>
        private void UpdateLatencyStatistics()
        {
            if (_latencySamples.Count > 0)
            {
                // 改动+15：优化统计计算，减少LINQ查询
                double avgMs = 0;
                double min = double.MaxValue;
                double max = 0;

                foreach (var sample in _latencySamples)
                {
                    double ms = sample.latency.TotalMilliseconds;
                    avgMs += ms;

                    if (ms < min) min = ms;
                    if (ms > max) max = ms;
                }

                avgMs /= _latencySamples.Count;
                _healthReport.AverageLatency = TimeSpan.FromMilliseconds(avgMs);
                _healthReport.MinLatency = TimeSpan.FromMilliseconds(min);
                _healthReport.MaxLatency = TimeSpan.FromMilliseconds(max);

                // 取中间的80%数据
                if (_latencySamples.Count > 2)
                {
                    // 创建临时数组，避免多次排序
                    var sortedSamples = _latencySamples
                        .Select(ts => ts.latency.TotalMilliseconds)
                        .OrderBy(ms => ms)
                        .ToArray();

                    int skipCount = (int)Math.Floor(_latencySamples.Count * 0.1);
                    int takeCount = (int)Math.Floor(_latencySamples.Count * 0.8);

                    double p80Avg = 0;
                    for (int i = skipCount; i < skipCount + takeCount && i < sortedSamples.Length; i++)
                    {
                        p80Avg += sortedSamples[i];
                    }
                    p80Avg /= takeCount;

                    _healthReport.P80AverageLatency = TimeSpan.FromMilliseconds(p80Avg);
                }

                // 近5分钟平均时间
                DateTime fiveMinsAgo = ServerTime.DateTime.AddMinutes(-5);
                double last5MinSum = 0;
                int last5MinCount = 0;

                foreach (var sample in _latencySamples)
                {
                    if (sample.timestamp > fiveMinsAgo)
                    {
                        last5MinSum += sample.latency.TotalMilliseconds;
                        last5MinCount++;
                    }
                }

                if (last5MinCount > 0)
                {
                    _healthReport.Last5MinuteAverageLatency = TimeSpan.FromMilliseconds(last5MinSum / last5MinCount);
                }

                // 计算抖动
                if (_recentRTTs.Count >= 2)
                {
                    _healthReport.Jitter = CalculateJitter(_recentRTTs.ToList());
                }
            }
        }

        /// <summary>
        /// 计算网络抖动
        /// </summary>
        private double CalculateJitter(List<double> rtts)
        {
            if (rtts.Count < 2)
                return 0;

            double sum = 0;
            for (int i = 1; i < rtts.Count; i++)
            {
                sum += Math.Abs(rtts[i] - rtts[i - 1]);
            }

            return sum / (rtts.Count - 1);
        }

        public void Dispose()
        {
        }
    }

    /// <summary>
    /// 连接健康信息
    /// </summary>
    [Serializable]
    public class ConnectionHealth
    {
        public ConnectionState State { get; set; }
        public NetworkQuality Quality { get; set; } = NetworkQuality.未知;
        public double? Latency { get; set; }
        public TimeSpan? LastLatency { get; set; }
        public TimeSpan? AverageLatency { get; set; }
        public TimeSpan? P80AverageLatency { get; set; }
        public TimeSpan? Last5MinuteAverageLatency { get; set; }
        public TimeSpan? MinLatency { get; set; }
        public TimeSpan? MaxLatency { get; set; }
        public double? Jitter { get; set; }
        public int DisconnectionCount { get; set; }
        public int ConnectionCount { get; set; }
        public DateTime LastActivityTime { get; set; }
        public string Transport { get; set; }

        // 错误信息
        public Exception LastError { get; set; }
        public DateTime LastErrorTime { get; set; }
        public string LastErrorSource { get; set; }

        // 网络交互统计
        public int TotalInteractions { get; set; }
        public int SuccessfulInteractions { get; set; }
        public int FailedInteractions { get; set; }

        public double SuccessRate => TotalInteractions > 0 ?
            (double)SuccessfulInteractions / TotalInteractions * 100 : 0;

        //public string HeartBeatInfo(int heartBeatInterval)
        //{
        //    var sb = new StringBuilder();
        //    sb.AppendLine($"{State}-{Quality}（{Transport}）心跳:{heartBeatInterval}s");
        //    sb.Append($"最近: {Latency ?? 0:F0}ms，5分钟：{Last5MinuteAverageLatency?.TotalMilliseconds ?? 0:F0}ms，80%：{P80AverageLatency?.TotalMilliseconds ?? 0:F0}ms，总平均: {AverageLatency?.TotalMilliseconds ?? 0:F0}ms");
        //    return sb.ToString();
        //}

        public override string ToString()
        {
            var sb = new StringBuilder();
            sb.AppendLine($"=====WSS连接统计=====");
            // 修改标签使其更清晰
            sb.AppendLine($"连接状态：{State} - {Quality}（{Transport}）");
            sb.AppendLine();
            sb.AppendLine($"延迟: {Latency ?? 0:F0}ms，最小: {MinLatency?.TotalMilliseconds ?? 0:F0}ms，最大: {MaxLatency?.TotalMilliseconds ?? 0:F0}ms");
            //sb.AppendLine($"5分钟：{Last5MinuteAverageLatency?.TotalMilliseconds ?? 0:F0}ms，80%：{P80AverageLatency?.TotalMilliseconds ?? 0:F0}ms，总平均: {AverageLatency?.TotalMilliseconds ?? 0:F0}ms");
            sb.AppendLine($"总次数: {TotalInteractions},成功率: {SuccessRate:F1}%");
            //sb.AppendLine();
            //sb.AppendLine($"网络交互: 总计 {TotalInteractions}, 成功: {SuccessfulInteractions}, 失败: {FailedInteractions}, 成功率: {SuccessRate:P2}");
            //sb.AppendLine($"连接次数: 成功: {ConnectionCount}次，连接失败: {DisconnectionCount}次");

            //if (Jitter.HasValue)
            //    sb.AppendLine($"网络抖动: {Jitter.Value:F0}ms");

            DateTime activityTime = LastActivityTime == DateTime.MinValue ? ServerTime.DateTime : LastActivityTime;
            sb.AppendLine($"最后活动: {activityTime:HH:mm:ss fff}");
            sb.AppendLine();
            //// 添加最后错误信息
            //if (LastError != null)
            //{
            //    sb.AppendLine();
            //    sb.AppendLine($"最后错误: [{LastErrorTime:HH:mm:ss}] {LastErrorSource} - {LastError.Message}");
            //}

            return sb.ToString();
        }

        internal bool IsNetworkActive(TimeSpan timeSpan)
        {
            return (ServerTime.DateTime - LastActivityTime) < timeSpan;
        }
    }

    #region 连接状态实现

    /// <summary>
    /// 断开连接状态
    /// </summary>
    internal class DisconnectedState : IConnectionState
    {
        public ConnectionState State => ConnectionState.Disconnected;

        public async Task<bool> ProcessAsync(ResilientSignalRClient client)
        {
            // 断开状态时尝试连接
            return await client.InitializeConnectionAsync();
        }

        public Task<bool> SendMessageAsync(ResilientSignalRClient client, string method, object[] args)
        {
            // 断开状态无法发送消息
            client.RaiseMessageSendCompleted(method, false, null, new InvalidOperationException("连接未建立，无法发送消息"));

            return Task.FromResult(false);
        }
    }

    /// <summary>
    /// 已连接状态
    /// </summary>
    internal class ConnectedState : IConnectionState
    {
        public ConnectionState State => ConnectionState.Connected;

        public Task<bool> ProcessAsync(ResilientSignalRClient client)
        {
            // 已连接状态不需要额外处理
            return Task.FromResult(true);
        }

        public Task<bool> SendMessageAsync(ResilientSignalRClient client, string method, object[] args)
        {
            // 发送消息
            return client.SendMessageAsync(method, args);
        }
    }

    /// <summary>
    /// 重连状态
    /// </summary>
    internal class ReconnectingState : IConnectionState
    {
        public ConnectionState State => ConnectionState.Reconnecting;
        private readonly ReconnectionReason _reason;

        public ReconnectingState(ReconnectionReason reason = ReconnectionReason.ConnectionLost)
        {
            _reason = reason;
        }

        public async Task<bool> ProcessAsync(ResilientSignalRClient client)
        {
            // 执行重连
            return await client.PerformReconnectionAsync(_reason);
        }

        public Task<bool> SendMessageAsync(ResilientSignalRClient client, string method, object[] args)
        {
            // 重连状态下无法发送消息
            client.RaiseMessageSendCompleted(method, false, null, new InvalidOperationException("正在重连，无法发送消息"));

            return Task.FromResult(false);
        }
    }

    #endregion

    #region 辅助类

    /// <summary>
    /// 弱事件管理器 - 避免事件订阅导致的内存泄漏
    /// </summary>
    internal class WeakEventManager
    {
        private readonly Dictionary<string, List<WeakDelegate>> _events = new Dictionary<string, List<WeakDelegate>>();
        private readonly object _lock = new object();

        public void AddEventHandler<TEventArgs>(EventHandler<TEventArgs> handler) where TEventArgs : EventArgs
        {
            if (handler == null)
                return;

            lock (_lock)
            {
                var eventName = typeof(TEventArgs).FullName;
                if (!_events.TryGetValue(eventName, out var weakHandlers))
                {
                    weakHandlers = new List<WeakDelegate>();
                    _events[eventName] = weakHandlers;
                }

                weakHandlers.Add(new WeakDelegate<TEventArgs>(handler));
            }
        }

        public void RemoveEventHandler<TEventArgs>(EventHandler<TEventArgs> handler) where TEventArgs : EventArgs
        {
            if (handler == null)
                return;

            lock (_lock)
            {
                var eventName = typeof(TEventArgs).FullName;
                if (!_events.TryGetValue(eventName, out var weakHandlers))
                    return;

                for (int i = weakHandlers.Count - 1; i >= 0; i--)
                {
                    var weakDelegate = weakHandlers[i];
                    if (!weakDelegate.IsAlive)
                    {
                        weakHandlers.RemoveAt(i);
                        continue;
                    }

                    if (weakDelegate.IsMatch(handler))
                    {
                        weakHandlers.RemoveAt(i);
                        break;
                    }
                }

                if (weakHandlers.Count == 0)
                    _events.Remove(eventName);
            }
        }

        public void RaiseEvent<TEventArgs>(object sender, TEventArgs args) where TEventArgs : EventArgs
        {
            List<EventHandler<TEventArgs>> handlers = null;

            lock (_lock)
            {
                var eventName = typeof(TEventArgs).FullName;
                if (!_events.TryGetValue(eventName, out var weakHandlers))
                    return;

                // 收集仍然有效的处理器
                handlers = new List<EventHandler<TEventArgs>>();
                for (int i = weakHandlers.Count - 1; i >= 0; i--)
                {
                    var weakDelegate = weakHandlers[i] as WeakDelegate<TEventArgs>;
                    if (weakDelegate == null || !weakDelegate.IsAlive)
                    {
                        weakHandlers.RemoveAt(i);
                        continue;
                    }

                    var handler = weakDelegate.GetHandler();
                    if (handler != null)
                        handlers.Add(handler);
                }

                if (weakHandlers.Count == 0)
                    _events.Remove(eventName);
            }

            // 在锁外调用处理器，避免死锁
            if (handlers?.Count > 0)
            {
                foreach (var handler in handlers)
                {
                    try
                    {
                        handler(sender, args);
                    }
                    catch (Exception ex)
                    {
                        Debug.WriteLine($"Event handler exception: {ex}");
                    }
                }
            }
        }

        private abstract class WeakDelegate
        {
            public abstract bool IsAlive { get; }
            public abstract bool IsMatch(Delegate @delegate);
        }

        private class WeakDelegate<TEventArgs> : WeakDelegate where TEventArgs : EventArgs
        {
            private readonly WeakReference _targetReference;
            private readonly MethodInfo _method;

            public WeakDelegate(EventHandler<TEventArgs> handler)
            {
                _targetReference = new WeakReference(handler.Target);
                _method = handler.Method;
            }

            public override bool IsAlive => _targetReference.Target != null;

            public override bool IsMatch(Delegate @delegate)
            {
                if (@delegate is EventHandler<TEventArgs> handler)
                {
                    return handler.Target == _targetReference.Target && handler.Method == _method;
                }
                return false;
            }

            public EventHandler<TEventArgs> GetHandler()
            {
                var target = _targetReference.Target;
                if (target == null)
                    return null;

                return (EventHandler<TEventArgs>)Delegate.CreateDelegate(typeof(EventHandler<TEventArgs>), target, _method);
            }
        }
    }

    #endregion

    /// <summary>
    /// 连接状态管理器 - 统一管理状态流转
    /// </summary>
    internal class ConnectionStateManager
    {
        private readonly ResilientSignalRClient _client;
        private IConnectionState _currentState;
        private readonly object _stateLock = new object();

        public ConnectionState CurrentState => _currentState.State;

        public ConnectionStateManager(ResilientSignalRClient client)
        {
            _client = client ?? throw new ArgumentNullException(nameof(client));
            _currentState = new DisconnectedState();
        }

        /// <summary>
        /// 处理SignalR原生状态变更
        /// </summary>
        public void HandleNativeStateChange(ConnectionState newState, ConnectionState oldState, string reason)
        {
            Console.WriteLine(reason);
            lock (_stateLock)
            {
                // 原生SignalR事件作为主要状态源
                switch (newState)
                {
                    case ConnectionState.Connected:
                        if (_currentState.State != ConnectionState.Connected)
                        {
                            SetState(new ConnectedState(), oldState, reason);
                        }
                        break;

                    case ConnectionState.Disconnected:
                        if (_currentState.State == ConnectionState.Connected)
                        {
                            // 从已连接变为断开时，不要立即转为Disconnected状态
                            // 重连逻辑将在Connection.Closed事件中处理
                        }
                        else if (_currentState.State != ConnectionState.Disconnected &&
                                 _currentState.State != ConnectionState.Reconnecting)
                        {
                            SetState(new DisconnectedState(), oldState, reason);
                        }
                        break;

                    case ConnectionState.Reconnecting:
                        if (_currentState.State != ConnectionState.Reconnecting)
                        {
                            SetState(new ReconnectingState(), oldState, reason);
                        }
                        break;
                }
            }
        }

        /// <summary>
        /// 设置新状态并触发事件
        /// </summary>
        public void SetState(IConnectionState newState, ConnectionState previousState, string reason)
        {
            lock (_stateLock)
            {
                if (newState == null)
                    throw new ArgumentNullException(nameof(newState));

                if (newState.State == CurrentState)
                    return;

                ConnectionState oldState = CurrentState;
                _currentState = newState;

                // 使用统一的方法触发状态变更事件
                _client.RaiseConnectionStatusChanged(oldState, newState.State, reason);

                // 根据新状态执行相应动作
                if (newState.State == ConnectionState.Connected)
                {
                    _client.OnConnected();
                }
                else if (newState.State == ConnectionState.Reconnecting ||
                         newState.State == ConnectionState.Disconnected)
                {
                    _client.OnDisconnectedOrReconnecting();
                }
            }
        }

        /// <summary>
        /// 获取当前状态实例
        /// </summary>
        public IConnectionState GetCurrentState()
        {
            lock (_stateLock)
            {
                return _currentState;
            }
        }

        /// <summary>
        /// 处理重连完成
        /// </summary>
        public void HandleReconnectionCompleted(bool success, string reason)
        {
            lock (_stateLock)
            {
                if (success)
                {
                    if (_currentState.State != ConnectionState.Connected)
                    {
                        SetState(new ConnectedState(), _currentState.State, reason);
                    }
                }
                else
                {
                    // 重连失败，保持重连状态，让重连机制继续工作
                }
            }
        }
    }

    /// <summary>
    /// 弹性SignalR客户端 - 专为跨洲远距离网络连接优化
    /// 注意：本类维护两种状态：
    /// 1. 应用层逻辑状态 - 通过_stateManager管理，表示客户端的逻辑状态
    /// 2. 物理连接状态 - 通过_connection.State直接访问，表示底层SignalR连接的实际状态
    /// 这两种状态在某些场景下可能不同步，例如"重连中"状态下底层连接可能是断开的
    /// </summary>
    public class ResilientSignalRClient : IConnectionManager, INetworkEventPublisher, IDisposable
    {
        #region 事件与观察者模式实现

        // 使用弱事件处理器避免内存泄漏
        private readonly WeakEventManager _eventManager = new WeakEventManager();

        // 事件定义
        public event EventHandler<ConnectionStatusEventArgs> ConnectionStatusChanged
        {
            add => _eventManager.AddEventHandler(value);
            remove => _eventManager.RemoveEventHandler(value);
        }

        public event EventHandler<MessageSendEventArgs> MessageSendCompleted
        {
            add => _eventManager.AddEventHandler(value);
            remove => _eventManager.RemoveEventHandler(value);
        }

        public event EventHandler<ErrorEventArgs> ErrorOccurred
        {
            add => _eventManager.AddEventHandler(value);
            remove => _eventManager.RemoveEventHandler(value);
        }

        // 网络事件
        public event EventHandler<NetworkActivityEventArgs> NetworkActivityStarted
        {
            add => _eventManager.AddEventHandler(value);
            remove => _eventManager.RemoveEventHandler(value);
        }

        public event EventHandler<NetworkActivityEventArgs> NetworkActivityCompleted
        {
            add => _eventManager.AddEventHandler(value);
            remove => _eventManager.RemoveEventHandler(value);
        }

        public event EventHandler<TransportInfoEventArgs> TransportInfoUpdated
        {
            add => _eventManager.AddEventHandler(value);
            remove => _eventManager.RemoveEventHandler(value);
        }

        #endregion

        #region 属性与字段

        // 配置
        public ConnectionConfig ConnectionConfig { get; }
        public HeartbeatConfig HeartbeatConfig { get; }
        public ReconnectionConfig ReconnectionConfig { get; }

        // 公开属性
        public string ClientId { get; }

        public ConnectionState ConnectionState => _stateManager.CurrentState;
        public bool IsConnected => ConnectionState == ConnectionState.Connected;

        // 核心组件
        private readonly string _serverUrl;
        private readonly string _hubName;

        private HubConnection _connection;
        private IHubProxy _hubProxy;

        // 资源管理
        private readonly CancellationTokenSource _masterTokenSource = new CancellationTokenSource();
        private readonly SemaphoreSlim _connectionLock = new SemaphoreSlim(1, 1);
        private readonly Random _random = new Random();
        private bool _disposed;

        // 重连状态
        private int _reconnectAttempts;
        private Timer _heartbeatTimer;
        private Timer _reconnectTimer;
        private TimeSpan _realHeartBeatTimeSpan;

        private readonly ConnectionStateManager _stateManager;

        private readonly NetworkDiagnosticService _diagnosticService;

        #endregion

        /// <summary>
        /// 检查底层SignalR连接是否物理上已连接
        /// </summary>
        private bool IsPhysicallyConnected()
        {
            return _connection != null && _connection.State != ConnectionState.Disconnected;
        }

        /// <summary>
        /// 构造函数
        /// </summary>
        public ResilientSignalRClient(
            string serverUrl,
            string hubName,
            string clientId = null)
        {
            _serverUrl = serverUrl ?? throw new ArgumentNullException(nameof(serverUrl));
            _hubName = hubName ?? throw new ArgumentNullException(nameof(hubName));
            ClientId = clientId ?? Guid.NewGuid().ToString();

            // 使用配置工厂方法创建高延迟网络配置
            ConnectionConfig = ConnectionConfig.CreateHighLatencyConfig();
            HeartbeatConfig = HeartbeatConfig.CreateHighLatencyConfig();
            ReconnectionConfig = ReconnectionConfig.CreateHighLatencyConfig();

            // 初始化状态管理器
            _stateManager = new ConnectionStateManager(this);

            _diagnosticService = new NetworkDiagnosticService();
            RegisterNetworkDiagnostiEvents();
        }

        /// <summary>
        /// 注册事件处理
        /// </summary>
        private void RegisterNetworkDiagnostiEvents()
        {
            // 注册网络活动开始事件
            NetworkActivityStarted += (sender, e) =>
                _diagnosticService?.OnNetworkActivityStarted(sender, e);

            // 注册网络活动完成事件
            NetworkActivityCompleted += (sender, e) =>
                _diagnosticService?.OnNetworkActivityCompleted(sender, e);

            // 注册传输信息更新事件
            TransportInfoUpdated += (sender, e) =>
                _diagnosticService?.OnTransportInfoUpdated(sender, e);

            // 注册连接状态变更事件
            ConnectionStatusChanged += (sender, e) =>
                _diagnosticService?.OnConnectionStatusChanged(sender, e);

            // 注册错误事件
            ErrorOccurred += (sender, e) =>
                _diagnosticService?.OnErrorOccurred(sender, e);

            // 注册消息发送完成事件
            MessageSendCompleted += (sender, e) =>
                _diagnosticService?.OnMessageSendCompleted(sender, e);
        }

        #region 公开方法

        /// <summary>
        /// 处理并发送消息
        /// </summary>
        public async Task<bool> SendMessageAsync(string method, object[] args)
        {
            if (string.IsNullOrEmpty(method))
                throw new ArgumentNullException(nameof(method));

            //// 处理参数 (压缩等)
            //object[] processedArgs = await ProcessArgumentsAsync(args);

            // 发送消息并记录活动
            var activityEvent = new NetworkActivityEventArgs(method);
            _eventManager.RaiseEvent(this, activityEvent);

            var stopwatch = Stopwatch.StartNew();
            bool success = false;

            try
            {
                // 使用带超时的异步调用
                using (var cts = new CancellationTokenSource(ConnectionConfig.ConnectionTimeout))
                {
                    await InvokeHubMethodAsync(method, args);
                    success = true;
                }
            }
            catch (OperationCanceledException ex)
            {
                // 超时或取消
                RaiseMessageSendCompleted(method, false, stopwatch.Elapsed, ex);
                success = false;
            }
            catch (Exception ex)
            {
                // 其他错误
                RaiseMessageSendCompleted(method, false, stopwatch.Elapsed, ex);
                success = false;
            }
            finally
            {
                // 记录活动
                activityEvent.MarkCompleted(success, stopwatch.Elapsed, success ? null : new Exception("Message send failed"));
                _eventManager.RaiseEvent(this, activityEvent);

                if (success)
                {
                    // 触发消息发送成功事件
                    RaiseMessageSendCompleted(method, true, stopwatch.Elapsed);
                }
            }

            return success;
        }

        internal void OnConnected()
        {
            StartHeartbeat();

            if (_reconnectTimer != null)
            {
                _reconnectTimer.Dispose();
                _reconnectTimer = null;
            }
        }

        internal void OnDisconnectedOrReconnecting()
        {
            StopHeartbeat();

            _reconnectTimer = new Timer(
                async _ =>
                {
                    try
                    {
                        await StartAsync();
                    }
                    catch (Exception oe)
                    {
                        Console.WriteLine(oe.Message);
                    }
                },
                null,
                new TimeSpan(0, 0, 10),
                Timeout.InfiniteTimeSpan
            );
        }

        internal void RaiseConnectionStatusChanged(ConnectionState oldState, ConnectionState newState, string reason)
        {
            var args = new ConnectionStatusEventArgs(newState, oldState, reason);
            _eventManager.RaiseEvent(this, args);
        }

        /// <summary>
        /// 启动连接
        /// </summary>
        public async Task<bool> StartAsync()
        {
            ThrowIfDisposed();

            await _connectionLock.WaitAsync();
            try
            {
                // 使用状态管理器获取当前状态处理启动
                return await _stateManager.GetCurrentState().ProcessAsync(this);
            }
            finally
            {
                _connectionLock.Release();
            }
        }

        /// <summary>
        /// 停止连接
        /// </summary>
        public async Task StopAsync()
        {
            if (_disposed)
                return;

            await _connectionLock.WaitAsync();
            try
            {
                // 停止心跳
                StopHeartbeat();

                // 关闭物理连接（如果存在且未断开）
                if (IsPhysicallyConnected())
                {
                    try
                    {
                        _connection.Stop();
                    }
                    catch (Exception ex)
                    {
                        RaiseError(ex, "关闭连接");
                    }
                }

                // 设置为断开状态
                _stateManager.SetState(
                    new DisconnectedState(),
                    _stateManager.CurrentState,
                    "客户端主动停止连接"
                );
            }
            finally
            {
                _connectionLock.Release();
            }
        }

        /// <summary>
        /// 发送消息
        /// </summary>
        public async Task<bool> SendAsync(string method, SignalRRequestEntity entity)
        {
            return await SendAsync(method, CompressStringToGzip(JsonConvert.SerializeObject(entity)));
        }

        byte[] CompressStringToGzip(string input)
        {
            byte[] inputBytes = Encoding.UTF8.GetBytes(input);
            try
            {
                using (MemoryStream outputStream = new MemoryStream())
                {
                    using (GZipStream gzipStream = new GZipStream(outputStream, CompressionMode.Compress))
                    {
                        gzipStream.Write(inputBytes, 0, inputBytes.Length);
                    }
                    return outputStream.ToArray();
                    //double compressionRatio = (double)result.Length / inputBytes.Length;
                    //Console.WriteLine($"原始长度: {inputBytes.Length}字节 | 压缩后: {result.Length}字节 | 压缩率: {compressionRatio:P}");
                    //return result;
                }
            }
            catch (Exception oe)
            {
                Console.WriteLine(oe.Message);
            }
            return inputBytes;
        }

        /// <summary>
        /// 发送消息
        /// </summary>
        public async Task<bool> SendAsync(string method, params object[] args)
        {
            ThrowIfDisposed();

            if (string.IsNullOrEmpty(method))
                throw new ArgumentNullException(nameof(method));

            return await _stateManager.GetCurrentState().SendMessageAsync(this, method, args);
        }

        /// <summary>
        /// 注册服务器回调
        /// </summary>
        public void On<T1, T2>(string eventName, Action<T1, T2> handler)
        {
            ThrowIfDisposed();
            EnsureHubProxy();
            _hubProxy.On(eventName, handler);
        }

        /// <summary>
        /// 注册服务器回调
        /// </summary>
        public void On<T>(string eventName, Action<T> handler)
        {
            ThrowIfDisposed();
            EnsureHubProxy();
            _hubProxy.On(eventName, handler);
        }

        /// <summary>
        /// 注册服务器回调
        /// </summary>
        public void On(string eventName, Action handler)
        {
            ThrowIfDisposed();
            EnsureHubProxy();
            _hubProxy.On(eventName, handler);
        }

        #endregion

        #region 内部方法

        /// <summary>
        /// 初始化连接
        /// </summary>
        internal async Task<bool> InitializeConnectionAsync()
        {
            try
            {
                // 创建连接 (如果已存在则复用)
                if (_connection == null)
                {
                    _connection = new HubConnection(_serverUrl)
                    {
                        TransportConnectTimeout = ConnectionConfig.ConnectionTimeout
                    };

                    // 注册事件处理器
                    RegisterConnectionEvents();
                }

                // 创建Hub代理
                EnsureHubProxy();

                // 检测服务器是否可用的逻辑
                if (!CommonClientRequest.CheckServerAvailability())
                {
                    return false;
                }

                // 启动连接
                await _connection.Start();

                // 发布传输信息更新事件
                _eventManager.RaiseEvent(this, new TransportInfoEventArgs(_connection.Transport.Name));

                // 重置状态
                _reconnectAttempts = 0;

                // 现在不需要主动设置状态，StateChanged事件会触发状态管理器处理
                // 只有在事件处理不及时的情况下再手动设置
                if (_stateManager.CurrentState != ConnectionState.Connected)
                {
                    _stateManager.SetState(new ConnectedState(), _stateManager.CurrentState, "连接初始化成功");
                }

                return true;
            }
            catch (Exception ex)
            {
                RaiseError(ex, "初始化连接");

                // 设置为断开状态
                _stateManager.SetState(new DisconnectedState(), _stateManager.CurrentState, $"连接初始化失败: {ex.Message}");

                BaseRecHelper.ReportMsg("初始化连接失败", ex);

                return false;
            }
        }

        /// <summary>
        /// 执行重连操作
        /// </summary>
        internal async Task<bool> PerformReconnectionAsync(ReconnectionReason reason = ReconnectionReason.ConnectionLost)
        {
            // 检查最大重连次数
            if (ReconnectionConfig.MaxAttempts > 0 && _reconnectAttempts >= ReconnectionConfig.MaxAttempts)
            {
                _stateManager.SetState(
                    new DisconnectedState(),
                    _stateManager.CurrentState,
                    $"达到最大重连尝试次数 ({ReconnectionConfig.MaxAttempts})，停止重试"
                );
                return false;
            }

            try
            {
                // 计算重连延迟
                var delay = CalculateReconnectDelay(_reconnectAttempts, reason);
                _reconnectAttempts++;

                _stateManager.SetState(
                    new ReconnectingState(reason),
                    _stateManager.CurrentState,
                    $"尝试重连 (第 {_reconnectAttempts} 次)，延迟 {delay.TotalSeconds:F1} 秒..."
                );

                // 等待指定时间
                await Task.Delay(delay, _masterTokenSource.Token);

                // 关闭现有连接
                if (IsPhysicallyConnected())
                {
                    try
                    {
                        _connection.Stop();
                    }
                    catch { /* 忽略关闭错误 */ }
                }

                // 检测服务器是否可用的逻辑
                if (!CommonClientRequest.CheckServerAvailability())
                {
                    return false;
                }

                // 尝试连接
                await _connection.Start();

                // 重置状态
                _reconnectAttempts = 0;

                // 通知状态管理器重连成功
                _stateManager.HandleReconnectionCompleted(true, "重连成功");

                return true;
            }
            catch (OperationCanceledException)
            {
                // 操作已取消
                return false;
            }
            catch (Exception ex)
            {
                RaiseError(ex, "重连");

                _eventManager.RaiseEvent(
                    this,
                    new ConnectionStatusEventArgs(
                        ConnectionState.Reconnecting,
                        _stateManager.CurrentState,
                        $"重连失败: {ex.Message}"
                    )
                );

                return false;
            }
        }

        /// <summary>
        /// 计算重连延迟
        /// </summary>
        internal TimeSpan CalculateReconnectDelay(int attemptCount, ReconnectionReason reason)
        {
            // 获取错误类型对应的重连策略
            double backoffFactor = 1.5;
            bool immediate = false;

            // 根据重连原因调整延迟计算
            switch (reason)
            {
                case ReconnectionReason.Timeout:
                    backoffFactor = 1.2;  // 超时错误使用较小的退避因子
                    immediate = attemptCount == 0;  // 第一次超时立即重试
                    break;

                case ReconnectionReason.ConnectionLost:
                    backoffFactor = 1.5;  // 默认退避因子
                    immediate = false;
                    break;

                case ReconnectionReason.ServerError:
                    backoffFactor = 2.0;  // 服务器错误使用较大的退避因子
                    immediate = false;
                    break;

                case ReconnectionReason.HeartbeatFailure:
                    backoffFactor = 1.3;
                    immediate = false;
                    break;

                case ReconnectionReason.Manual:
                    backoffFactor = 1.0;
                    immediate = true;  // 手动重连立即执行
                    break;
            }

            if (immediate)
                return TimeSpan.Zero;

            // 基础延迟 * 退避指数^尝试次数
            double delayMs = ReconnectionConfig.InitialDelay.TotalMilliseconds *
                Math.Pow(backoffFactor, Math.Min(attemptCount, 10));

            // 添加随机抖动
            double jitterRange = delayMs * ReconnectionConfig.JitterFactor;
            double jitter = (2 * _random.NextDouble() - 1) * jitterRange;

            // 限制最大延迟
            delayMs = Math.Min(delayMs + jitter, ReconnectionConfig.MaxDelay.TotalMilliseconds);

            return TimeSpan.FromMilliseconds(Math.Max(0, delayMs));
        }

        /// <summary>
        /// 调用Hub方法
        /// </summary>
        internal Task InvokeHubMethodAsync(string method, object[] args)
        {
            EnsureHubProxy();
            return _hubProxy.Invoke(method, args);
        }

        /// <summary>
        /// 启动心跳
        /// </summary>
        internal void StartHeartbeat()
        {
            StopHeartbeat();
            _realHeartBeatTimeSpan = HeartbeatConfig.MinInterval;
            _heartbeatTimer = new Timer(
                async _ => await ExecuteHeartbeatAsync(),
                null,
                HeartbeatConfig.MinInterval,
                Timeout.InfiniteTimeSpan
            );
        }

        /// <summary>
        /// 停止心跳
        /// </summary>
        internal void StopHeartbeat()
        {
            if (_heartbeatTimer != null)
            {
                _heartbeatTimer.Dispose();
                _heartbeatTimer = null;
            }

            if (_reconnectTimer != null)
            {
                _reconnectTimer.Dispose();
                _reconnectTimer = null;
            }
        }

        public ConnectionHealth GetHealth()
        {
            return _diagnosticService?.GetHealthReport();
        }

        /// <summary>
        /// 执行心跳
        /// </summary>
        private async Task ExecuteHeartbeatAsync()
        {
            try
            {
                // 如果需要跳过心跳
                if (ShouldSkipHeartbeat())
                {
                    // 调度下一次心跳
                    RescheduleHeartbeat();
                    return;
                }

                //// 构建心跳信息
                //string heartbeatInfo = GetHealth()?.HeartBeatInfo((int)_realHeartBeatTimeSpan.TotalSeconds);

                //Console.WriteLine(health);

                //bool success = await SendAsync("Heartbeat", ClientId, heartbeatInfo);

                bool success = await SendAsync("DoProcess", new SignalRRequestEntity()
                {
                    OpType = "Heartbeat",
                    Content = string.Empty
                });

                // 调度下一次心跳
                RescheduleHeartbeat();
            }
            catch (Exception ex)
            {
                // 捕获所有异常，确保心跳定时器继续工作
                RaiseError(ex, "心跳执行");
                RescheduleHeartbeat();
            }
        }

        /// <summary>
        /// 重新调度下一次心跳
        /// </summary>
        private void RescheduleHeartbeat()
        {
            if (_heartbeatTimer != null && !_disposed)
            {
                var interval = CalculateOptimalHeartbeatInterval();
                _realHeartBeatTimeSpan = interval;
                _heartbeatTimer.Change(interval, Timeout.InfiniteTimeSpan);
            }
        }

        /// <summary>
        /// 是否应该跳过心跳
        /// </summary>
        private bool ShouldSkipHeartbeat()
        {
            if (!HeartbeatConfig.EnableAdaptiveHeartbeat)
                return false;

            var health = _diagnosticService.GetHealthReport();
            // 1. 检查最近的网络活动
            bool recentActivity = health.IsNetworkActive(HeartbeatConfig.ActivityWindow);

            // 2. 检查当前网络质量 - 优质网络可以更激进地跳过心跳
            bool stableNetwork = health.Quality == NetworkQuality.极优 ||
                                  health.Quality == NetworkQuality.优秀 ||
                                  health.Quality == NetworkQuality.良好;

            // 3. 检查连接状态稳定性 - 连接状态稳定可以更激进地跳过心跳
            bool stableConnection = _stateManager.CurrentState == ConnectionState.Connected;

            // 组合逻辑：有最近活动且网络稳定或连接稳定时跳过心跳
            return recentActivity && (stableNetwork || stableConnection);
        }

        /// <summary>
        /// 计算最佳心跳间隔
        /// </summary>
        private TimeSpan CalculateOptimalHeartbeatInterval()
        {
            // 基础间隔，网络状况因子，抖动因子
            double baseIntervalMs = HeartbeatConfig.Interval.TotalMilliseconds;
            double factor = 1.0;

            var health = _diagnosticService.GetHealthReport();
            // 1. 应用网络质量因子 - 更好的网络 = 更长的间隔
            switch (health.Quality)
            {
                case NetworkQuality.极优: factor *= 2.5; break;
                case NetworkQuality.优秀: factor *= 2.0; break;
                case NetworkQuality.良好: factor *= 1.5; break;
                case NetworkQuality.一般: factor *= 1.0; break;
                case NetworkQuality.较差: factor *= 0.7; break;
                case NetworkQuality.严重: factor *= 0.5; break;
            }

            // 2. 应用抖动因子 - 更高的抖动 = 更长的间隔(避免网络不稳定时频繁心跳)
            double jitter = health.Jitter ?? 0;
            if (jitter > 200) factor *= 1.5;
            else if (jitter > 100) factor *= 1.3;
            else if (jitter > 50) factor *= 1.1;

            // 3. 应用活动因子 - 最近有活动 = 更长的间隔
            if (health.IsNetworkActive(TimeSpan.FromSeconds(10)))
            {
                factor *= 1.3; // 最近10秒有活动，增加30%间隔
            }

            // 计算最终间隔并确保在配置范围内
            double intervalMs = baseIntervalMs * factor;
            return TimeSpan.FromMilliseconds(
                Math.Max(HeartbeatConfig.MinInterval.TotalMilliseconds,
                Math.Min(intervalMs, HeartbeatConfig.MaxInterval.TotalMilliseconds)));
        }

        /// <summary>
        /// 确保Hub代理已创建
        /// </summary>
        internal void EnsureHubProxy()
        {
            if (_hubProxy == null)
            {
                if (_connection == null)
                    throw new InvalidOperationException("连接尚未初始化");

                _hubProxy = _connection.CreateHubProxy(_hubName);
            }
        }

        /// <summary>
        /// 注册连接事件处理器
        /// </summary>
        internal void RegisterConnectionEvents()
        {
            _connection.StateChanged += (stateChange) =>
            {
                // 将SignalR原生状态变更交由状态管理器处理
                _stateManager.HandleNativeStateChange(
                    stateChange.NewState,
                    stateChange.OldState,
                    $"SignalR状态变更: {stateChange.OldState} -> {stateChange.NewState}"
                );
            };

            _connection.Reconnecting += () =>
            {
                // 通知状态管理器
                _stateManager.HandleNativeStateChange(
                    ConnectionState.Reconnecting,
                    _stateManager.CurrentState,
                    "SignalR开始重新连接"
                );
            };

            _connection.Reconnected += () =>
            {
                _reconnectAttempts = 0;

                // 通知状态管理器
                _stateManager.HandleNativeStateChange(
                    ConnectionState.Connected,
                    _stateManager.CurrentState,
                    "SignalR重新连接成功"
                );
            };

            _connection.Closed += () =>
            {
                // 主动关闭或已释放资源，切换到断开状态
                _stateManager.SetState(
                    new DisconnectedState(),
                    _stateManager.CurrentState,
                    "连接已关闭"
                );
            };

            _connection.Error += (ex) =>
            {
                RaiseError(ex, "连接错误");
            };
        }

        /// <summary>
        /// 触发消息发送完成事件
        /// </summary>
        internal void RaiseMessageSendCompleted(string method, bool success, TimeSpan? sendTime, Exception error = null)
        {
            _eventManager.RaiseEvent(
                this,
                new MessageSendEventArgs(method, success, sendTime, error)
            );
        }

        /// <summary>
        /// 触发错误事件
        /// </summary>
        internal void RaiseError(Exception error, string source = null)
        {
            if (error == null)
                return;

            try
            {
                _eventManager.RaiseEvent(
                    this,
                    new ErrorEventArgs(error, source)
                );
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"触发错误事件时出错: {ex.Message}");
            }
        }

        /// <summary>
        /// 检查对象是否已释放
        /// </summary>
        private void ThrowIfDisposed()
        {
            if (_disposed)
            {
                Dispose();
                throw new ObjectDisposedException(nameof(ResilientSignalRClient));
            }
        }

        #endregion

        #region 资源释放

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            _disposed = true;

            try
            {
                // 停止心跳
                StopHeartbeat();

                // 取消所有操作
                if (!_masterTokenSource.IsCancellationRequested)
                    _masterTokenSource.Cancel();

                try
                {
                    _connection?.Stop();
                }
                catch { /* 忽略关闭错误 */ }
                try
                {
                    _connection?.Dispose();
                }
                catch { /* 忽略关闭错误 */ }
                try
                {
                    _connection = null;
                }
                catch { /* 忽略关闭错误 */ }
            }
            catch (Exception oe)
            {
                Console.WriteLine(oe.Message);
            }
            finally
            {
                // 释放资源
                _masterTokenSource.Dispose();
                _connectionLock.Dispose();
                _heartbeatTimer?.Dispose();
                _reconnectTimer?.Dispose();
            }
        }

        #endregion
    }


    [Serializable]
    public class SignalRRequestEntity
    {
        public SignalRRequestEntity()
        {
            Server = ConfigHelper.OcrServer;
            ServerId = ServerUuidUtil.Server.Id;
            ClientType = ServerUuidUtil.Mode;
            Version = ServerInfo.DtNowVersion.Ticks;
            ClientId = CommonClientRequest.ClientID;
        }

        public string Id { get; set; }

        public string Server { get; set; }

        public string ServerId { get; set; }

        public string ClientType { get; set; }

        public long Version { get; set; }

        public string ClientId { get; set; }

        public int TimeOut { get; set; }

        public string OpType { get; set; }

        public object Content { get; set; }
    }
}
