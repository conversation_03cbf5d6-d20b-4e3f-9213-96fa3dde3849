﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Text;

namespace BaiDuAPI
{
    public class WuYiCode
    {
        public static bool IsWuYi { get; set; }

        private static string Escape(string str)
        {
            StringBuilder stringBuilder = new StringBuilder();
            for (int i = 0; i < str.Length; i++)
            {
                char value = str[i];
                if (HttpUtility.UrlEncode(value.ToString()).Length > 1)
                {
                    stringBuilder.Append(HttpUtility.UrlEncode(value.ToString()).ToUpper());
                }
                else
                {
                    stringBuilder.Append(value);
                }
            }
            return stringBuilder.ToString();
        }

        public static string doVerify(string strImg)
        {
            string result = "";
            try
            {
                strImg = strImg.Replace("%2B", "_");
                var html = WebClientExt.GetHtml("http://120.24.41.144/code.php", "", "", "code=" + Escape(strImg), 1, 5);
                if (!string.IsNullOrEmpty(html))
                {
                    if (html.Split(new char[] { '|' }).Length > 1)
                    {
                        string text3 = html.Split(new char[] { '|' })[1];
                        string[] array2 = new string[text3.Length];
                        for (int i = 0; i < text3.Length; i++)
                        {
                            int num = int.Parse(text3.Substring(i, 1));
                            int nX = ((num > 4) ? (32 + (num - 5) * 73) : (32 + (num - 1) * 73));
                            int nY = ((num > 4) ? 120 : 40);
                            array2[i] = nX + "," + nY;
                        }
                        result = string.Join(",", array2);
                    }
                }
            }
            catch (Exception oe)
            {
                Console.WriteLine(oe.Message);
            }
            return result;
        }

    }
}