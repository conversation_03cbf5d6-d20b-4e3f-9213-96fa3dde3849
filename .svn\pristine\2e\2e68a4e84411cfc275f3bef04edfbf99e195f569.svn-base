﻿using CommonLib;
using System;
using System.Collections.Generic;
using System.Collections.Specialized;
using System.IO;

namespace TransOcr
{
    /// <summary>
    /// 腾讯翻译君-微信小程序
    /// </summary>
    public class QQTransPicRec : BaseOcrRec
    {
        public QQTransPicRec()
        {
            OcrGroup = OcrGroupType.腾讯;
            OcrType = TransOcrType.腾讯翻译;
            MaxExecPerTime = 24;

            LstJsonPreProcessArray = new List<object>() { "ret", "imageRecords" };

            IsSupportVertical = true;
            IsDesrializeVerticalByLocation = true;
            DicDeserializeVerticalJson = new Dictionary<string, string> { { "left", "x" }, { "top", "y" }, { "width", "w" }, { "height", "h" }, { "words", "sourceText" }, { "trans", "targetText" } };
            InitLanguage();
        }

        #region 支持的语言

        //中文	zh
        //英文 en
        //日文 jp
        //韩文 kr
        //自动识别（中英互译）	auto

        private void InitLanguage()
        {
            TransLanguageDic = new Dictionary<TransLanguageTypeEnum, string>();
            TransLanguageDic.Add(TransLanguageTypeEnum.自动, "auto");
            TransLanguageDic.Add(TransLanguageTypeEnum.中文, "zh");
            TransLanguageDic.Add(TransLanguageTypeEnum.英文, "en");
            TransLanguageDic.Add(TransLanguageTypeEnum.日语, "jp");
            TransLanguageDic.Add(TransLanguageTypeEnum.韩语, "kr");
        }

        #endregion

        protected override string GetHtml(OcrContent content)
        {
            string from = "", to = "";
            InitLanguage(content, ref from, ref to);
            //HttpUtility.UrlEncode(content.strBase64)

            var url = string.Format("https://wxapp.translator.qq.com/imageData?source={0}&target={1}&platform=WeChat_APP", from, to);
            var file = new UploadFileInfo()
            {
                Name = "file",
                Filename = "1." + content.fileExt,
                ContentType = ApplicationTypeHelper.GetApplicationType(content.fileExt),
                Stream = new MemoryStream(Convert.FromBase64String(content.strBase64))
            };
            var values = new NameValueCollection() {
                { "candidateLangs",string.Format("{0}|{1}",Equals(content.from,TransLanguageTypeEnum.自动)?(Equals(content.to,TransLanguageTypeEnum.中文)?"en":"zh"):from,to)},
                { "guid",Guid.NewGuid().ToString()},
                { "myfile","wxfile://temp/1." + content.fileExt
},
                { "platform","WeChat_APP"},
                { "scene","doc"},
                { "source","auto"},
                { "target","auto"},
                { "type",content.fileExt},
                { "user","wechatLittle"},
            };
            var headers = new NameValueCollection() {
                {"User-Agent","Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/53.0.2785.143 Safari/537.36 MicroMessenger/7.0.9.501 NetType/WIFI MiniProgramEnv/Windows WindowsWechat" },
                {"Referer","https://servicewechat.com/wxb1070eabc6f9107e/116/page-frame.html" },
            };
            var result = PostFile(url, new[] { file }, values, headers);
            return result;
        }

    }
}