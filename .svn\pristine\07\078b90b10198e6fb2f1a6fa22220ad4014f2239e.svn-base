﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="12.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProductVersion>
    </ProductVersion>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectGuid>{F74FCA00-AE7C-45EB-8BFB-55D7B2BB5DB4}</ProjectGuid>
    <ProjectTypeGuids>{349c5851-65df-11da-9384-00065b846f21};{fae04ec0-301f-11d3-bf4b-00c04f79efbc}</ProjectTypeGuids>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>Account.Web</RootNamespace>
    <AssemblyName>Account.Web</AssemblyName>
    <TargetFrameworkVersion>v4.7.2</TargetFrameworkVersion>
    <UseIISExpress>true</UseIISExpress>
    <TargetFrameworkProfile />
    <FileUpgradeFlags>
    </FileUpgradeFlags>
    <UpgradeBackupLocation>
    </UpgradeBackupLocation>
    <OldToolsVersion>4.0</OldToolsVersion>
    <IISExpressSSLPort />
    <IISExpressAnonymousAuthentication />
    <IISExpressWindowsAuthentication />
    <IISExpressUseClassicPipelineMode />
    <UseGlobalApplicationHostFile />
    <Use64BitIISExpress />
    <NuGetPackageImportStamp>
    </NuGetPackageImportStamp>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <FilesToIncludeForPublish>OnlyFilesToRunTheApp</FilesToIncludeForPublish>
    <PlatformTarget>x86</PlatformTarget>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="BouncyCastle.Crypto, Version=1.9.0.0, Culture=neutral, PublicKeyToken=0e99375e54769942, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Portable.BouncyCastle.1.9.0\lib\net40\BouncyCastle.Crypto.dll</HintPath>
    </Reference>
    <Reference Include="FreeSql, Version=3.2.680.0, Culture=neutral, PublicKeyToken=a33928e5d4a4b39c, processorArchitecture=MSIL">
      <HintPath>..\..\packages\FreeSql.3.2.680\lib\net451\FreeSql.dll</HintPath>
    </Reference>
    <Reference Include="FreeSql.Provider.MySql, Version=3.2.680.0, Culture=neutral, PublicKeyToken=09c4767ab6141ff5, processorArchitecture=MSIL">
      <HintPath>..\..\packages\FreeSql.Provider.MySql.3.2.680\lib\net452\FreeSql.Provider.MySql.dll</HintPath>
    </Reference>
    <Reference Include="FreeSql.Provider.Sqlite, Version=3.2.680.0, Culture=neutral, PublicKeyToken=5800863e689c9dd8, processorArchitecture=MSIL">
      <HintPath>..\..\packages\FreeSql.Provider.Sqlite.3.2.680\lib\net45\FreeSql.Provider.Sqlite.dll</HintPath>
    </Reference>
    <Reference Include="Google.Protobuf, Version=3.21.7.0, Culture=neutral, PublicKeyToken=a7d26565bac4d604, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Google.Protobuf.3.21.7\lib\net45\Google.Protobuf.dll</HintPath>
    </Reference>
    <Reference Include="K4os.Compression.LZ4, Version=1.2.16.0, Culture=neutral, PublicKeyToken=2186fa9121ef231d, processorArchitecture=MSIL">
      <HintPath>..\..\packages\K4os.Compression.LZ4.1.2.16\lib\net46\K4os.Compression.LZ4.dll</HintPath>
    </Reference>
    <Reference Include="K4os.Compression.LZ4.Streams, Version=1.2.16.0, Culture=neutral, PublicKeyToken=2186fa9121ef231d, processorArchitecture=MSIL">
      <HintPath>..\..\packages\K4os.Compression.LZ4.Streams.1.2.16\lib\net46\K4os.Compression.LZ4.Streams.dll</HintPath>
    </Reference>
    <Reference Include="K4os.Hash.xxHash, Version=1.0.7.0, Culture=neutral, PublicKeyToken=32cd54395057cec3, processorArchitecture=MSIL">
      <HintPath>..\..\packages\K4os.Hash.xxHash.1.0.7\lib\net46\K4os.Hash.xxHash.dll</HintPath>
    </Reference>
    <Reference Include="log4net, Version=1.2.10.0, Culture=neutral, PublicKeyToken=692fbea5521e1304, processorArchitecture=x86">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\DLL\log4net.dll</HintPath>
    </Reference>
    <Reference Include="MySql.Data, Version=8.0.31.0, Culture=neutral, PublicKeyToken=c5687fc88969c44d, processorArchitecture=MSIL">
      <HintPath>..\..\packages\MySql.Data.8.0.31\lib\net452\MySql.Data.dll</HintPath>
    </Reference>
    <Reference Include="Newtonsoft.Json, Version=13.0.0.0, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Newtonsoft.Json.13.0.1\lib\net45\Newtonsoft.Json.dll</HintPath>
    </Reference>
    <Reference Include="Qiniu, Version=8.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Qiniu.SDK.8.0.0\lib\net45\Qiniu.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Buffers, Version=4.0.3.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\..\packages\System.Buffers.4.5.1\lib\net461\System.Buffers.dll</HintPath>
    </Reference>
    <Reference Include="System.Configuration.Install" />
    <Reference Include="System.Data" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="System.Data.SQLite, Version=*********, Culture=neutral, PublicKeyToken=db937bc2d44ff139, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Stub.System.Data.SQLite.Core.NetFramework.*********\lib\net45\System.Data.SQLite.dll</HintPath>
    </Reference>
    <Reference Include="System.DirectoryServices" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Management" />
    <Reference Include="System.Memory, Version=4.0.1.2, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\..\packages\System.Memory.4.5.5\lib\net461\System.Memory.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.Http" />
    <Reference Include="System.Runtime.CompilerServices.Unsafe, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL" />
    <Reference Include="System.Transactions" />
    <Reference Include="System.Web" />
    <Reference Include="System.Web.ApplicationServices" />
    <Reference Include="System.Web.DynamicData" />
    <Reference Include="System.Web.Entity" />
    <Reference Include="System.Web.Extensions" />
    <Reference Include="System.Xml" />
    <Reference Include="System.Configuration" />
    <Reference Include="System.Web.Services" />
    <Reference Include="System.EnterpriseServices" />
    <Reference Include="System.Web.Mobile" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="ZstdNet, Version=*******, Culture=neutral, PublicKeyToken=c5687fc88969c44d, processorArchitecture=MSIL">
      <HintPath>..\..\packages\MySql.Data.8.0.31\lib\net452\ZstdNet.dll</HintPath>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <Content Include="Agreemeut.aspx" />
    <Content Include="static\css\pay.css" />
    <Content Include="static\css\upgrade.css" />
    <Content Include="static\image\bg3.jpg" />
    <Content Include="static\image\icon_hint_blue.svg" />
    <Content Include="static\image\pay\alipay.jpg" />
    <Content Include="static\image\pay\alipay.png" />
    <Content Include="static\image\pay\alipay_lim[limit].png" />
    <Content Include="static\image\pay\guide.png" />
    <Content Include="static\image\pay\guide_lim[limit].png" />
    <Content Include="static\image\pay\loading.gif" />
    <Content Include="static\image\pay\pay_down_ico.png" />
    <Content Include="static\image\pay\unipay.jpg" />
    <Content Include="static\image\pay\use_1.png" />
    <Content Include="static\image\pay\use_1_lim[limit].png" />
    <Content Include="static\image\pay\use_2.png" />
    <Content Include="static\image\pay\use_2_lim[limit].png" />
    <Content Include="static\image\pay\wave.png" />
    <Content Include="static\image\pay\wave_lim[limit].png" />
    <Content Include="static\image\pay\wechat-pay.png" />
    <Content Include="static\image\pay\wechat-pay_lim[limit].png" />
    <Content Include="static\image\pay\weixin.jpg" />
    <Content Include="static\image\radio-checked1.svg" />
    <Content Include="static\image\radio-unchecked.svg" />
    <Content Include="static\js\buypack.js" />
    <Content Include="ToPay.aspx" />
    <Content Include="UserUpgrade.aspx" />
    <Content Include="static\css\main.css" />
    <Content Include="static\image\icon.png" />
    <Content Include="UserMac.aspx" />
    <Content Include="UserIndex.aspx" />
    <Content Include="UserReg.aspx" />
    <Content Include="UserForgetPwd.aspx" />
    <Content Include="UserLogin.aspx" />
    <Content Include="Desc.aspx" />
    <Content Include="AllUser.aspx" />
    <Content Include="CSS\style.css" />
    <Content Include="DB\Code.txt" />
    <Content Include="Default.aspx" />
    <Content Include="Detail.aspx" />
    <Content Include="gpt\img\my_default.png" />
    <Content Include="gpt\img\sys_default.png" />
    <Content Include="gpt\index.html" />
    <Content Include="gpt\static\css\chat.css" />
    <Content Include="gpt\static\css\font-awesome.min.css" />
    <Content Include="gpt\static\css\monokai_sublime.min.css" />
    <Content Include="gpt\static\font\fontawesome-webfont.svg" />
    <Content Include="gpt\static\js\chat.min.js" />
    <Content Include="gpt\static\js\clipboard.min.js" />
    <Content Include="gpt\static\js\enc-base64-min.js" />
    <Content Include="gpt\static\js\highlight.min.js" />
    <Content Include="gpt\static\js\hmac-sha256.js" />
    <Content Include="gpt\static\js\HmacSHA1.js" />
    <Content Include="gpt\static\js\jquery.cookie.min.js" />
    <Content Include="gpt\static\js\jquery.js" />
    <Content Include="gpt\static\js\js.js" />
    <Content Include="gpt\static\js\marked.min.js" />
    <Content Include="gpt\static\js\md5.js" />
    <Content Include="gpt\static\js\micromark.bundle.js" />
    <Content Include="gpt\static\js\sha1.js" />
    <Content Include="gpt\static\js\ajax.min.js" />
    <Content Include="gpt\static\js\socket.min.js" />
    <Content Include="gpt\static\js\transformpcm.worker.js" />
    <Content Include="gpt\static\js\tripledes.js" />
    <Content Include="gpt\static\js\vconsole.min.js" />
    <Content Include="gpt\static\js\voice.min.js" />
    <Content Include="IPList.aspx" />
    <Content Include="math\auto-render.min.js" />
    <Content Include="math\contextMenu.js" />
    <Content Include="math\copy.png" />
    <Content Include="math\katex.min.css" />
    <Content Include="math\katex.min.js" />
    <Content Include="math\refresh.png" />
    <Content Include="math\view.aspx" />
    <Content Include="math\view.html" />
    <Content Include="Pay.aspx" />
    <Content Include="Server.aspx" />
    <Content Include="static\js\clearbox.js" />
    <Content Include="static\js\clearbox\config\default\cb_config.js" />
    <Content Include="static\js\clearbox\config\default\cb_style.css" />
    <Content Include="static\js\clearbox\config\default\pic\blank.gif" />
    <Content Include="static\js\clearbox\config\default\pic\btm_dl.gif" />
    <Content Include="static\js\clearbox\config\default\pic\btm_max.gif" />
    <Content Include="static\js\clearbox\config\default\pic\btm_next.gif" />
    <Content Include="static\js\clearbox\config\default\pic\btm_prev.gif" />
    <Content Include="static\js\clearbox\config\default\pic\btm_rot_l.gif" />
    <Content Include="static\js\clearbox\config\default\pic\btm_rot_r.gif" />
    <Content Include="static\js\clearbox\config\default\pic\close.png" />
    <Content Include="static\js\clearbox\config\default\pic\next.png" />
    <Content Include="static\js\clearbox\config\default\pic\no_flash.gif" />
    <Content Include="static\js\clearbox\config\default\pic\no_html.gif" />
    <Content Include="static\js\clearbox\config\default\pic\no_iframe.gif" />
    <Content Include="static\js\clearbox\config\default\pic\no_image.gif" />
    <Content Include="static\js\clearbox\config\default\pic\no_inner.gif" />
    <Content Include="static\js\clearbox\config\default\pic\no_quicktime.gif" />
    <Content Include="static\js\clearbox\config\default\pic\no_winmedia.gif" />
    <Content Include="static\js\clearbox\config\default\pic\no_winmediaavi.gif" />
    <Content Include="static\js\clearbox\config\default\pic\no_winmediamp3.gif" />
    <Content Include="static\js\clearbox\config\default\pic\no_winmediampg.gif" />
    <Content Include="static\js\clearbox\config\default\pic\no_winmediawav.gif" />
    <Content Include="static\js\clearbox\config\default\pic\no_winmediawma.gif" />
    <Content Include="static\js\clearbox\config\default\pic\no_winmediawmv.gif" />
    <Content Include="static\js\clearbox\config\default\pic\no_youtube.gif" />
    <Content Include="static\js\clearbox\config\default\pic\pause.png" />
    <Content Include="static\js\clearbox\config\default\pic\prev.png" />
    <Content Include="static\js\clearbox\config\default\pic\start.png" />
    <Content Include="static\js\clearbox\config\default\pic\s_btm.png" />
    <Content Include="static\js\clearbox\config\default\pic\s_btmleft.png" />
    <Content Include="static\js\clearbox\config\default\pic\s_btmright.png" />
    <Content Include="static\js\clearbox\config\default\pic\s_left.png" />
    <Content Include="static\js\clearbox\config\default\pic\s_right.png" />
    <Content Include="static\js\clearbox\config\default\pic\s_top.png" />
    <Content Include="static\js\clearbox\config\default\pic\s_topleft.png" />
    <Content Include="static\js\clearbox\config\default\pic\s_topright.png" />
    <Content Include="static\js\clearbox\config\grow_transparent\cb_config.js" />
    <Content Include="static\js\clearbox\config\grow_transparent\cb_style.css" />
    <Content Include="static\js\clearbox\config\grow_transparent\pic\blank.gif" />
    <Content Include="static\js\clearbox\config\grow_transparent\pic\btm_dl.gif" />
    <Content Include="static\js\clearbox\config\grow_transparent\pic\btm_max.gif" />
    <Content Include="static\js\clearbox\config\grow_transparent\pic\btm_next.gif" />
    <Content Include="static\js\clearbox\config\grow_transparent\pic\btm_prev.gif" />
    <Content Include="static\js\clearbox\config\grow_transparent\pic\btm_rot_l.gif" />
    <Content Include="static\js\clearbox\config\grow_transparent\pic\btm_rot_r.gif" />
    <Content Include="static\js\clearbox\config\grow_transparent\pic\close.png" />
    <Content Include="static\js\clearbox\config\grow_transparent\pic\next.png" />
    <Content Include="static\js\clearbox\config\grow_transparent\pic\no_flash.gif" />
    <Content Include="static\js\clearbox\config\grow_transparent\pic\no_html.gif" />
    <Content Include="static\js\clearbox\config\grow_transparent\pic\no_iframe.gif" />
    <Content Include="static\js\clearbox\config\grow_transparent\pic\no_image.gif" />
    <Content Include="static\js\clearbox\config\grow_transparent\pic\no_inner.gif" />
    <Content Include="static\js\clearbox\config\grow_transparent\pic\no_quicktime.gif" />
    <Content Include="static\js\clearbox\config\grow_transparent\pic\no_winmedia.gif" />
    <Content Include="static\js\clearbox\config\grow_transparent\pic\no_winmediaavi.gif" />
    <Content Include="static\js\clearbox\config\grow_transparent\pic\no_winmediamp3.gif" />
    <Content Include="static\js\clearbox\config\grow_transparent\pic\no_winmediampg.gif" />
    <Content Include="static\js\clearbox\config\grow_transparent\pic\no_winmediawav.gif" />
    <Content Include="static\js\clearbox\config\grow_transparent\pic\no_winmediawma.gif" />
    <Content Include="static\js\clearbox\config\grow_transparent\pic\no_winmediawmv.gif" />
    <Content Include="static\js\clearbox\config\grow_transparent\pic\no_youtube.gif" />
    <Content Include="static\js\clearbox\config\grow_transparent\pic\pause.png" />
    <Content Include="static\js\clearbox\config\grow_transparent\pic\prev.png" />
    <Content Include="static\js\clearbox\config\grow_transparent\pic\start.png" />
    <Content Include="static\js\clearbox\config\grow_transparent\pic\s_btm.png" />
    <Content Include="static\js\clearbox\config\grow_transparent\pic\s_btmleft.png" />
    <Content Include="static\js\clearbox\config\grow_transparent\pic\s_btmright.png" />
    <Content Include="static\js\clearbox\config\grow_transparent\pic\s_left.png" />
    <Content Include="static\js\clearbox\config\grow_transparent\pic\s_right.png" />
    <Content Include="static\js\clearbox\config\grow_transparent\pic\s_top.png" />
    <Content Include="static\js\clearbox\config\grow_transparent\pic\s_topleft.png" />
    <Content Include="static\js\clearbox\config\grow_transparent\pic\s_topright.png" />
    <Content Include="static\js\clearbox\core\cb_core.js" />
    <Content Include="static\js\clearbox\language\en\cb_language.js" />
    <Content Include="static\js\clearbox\language\fr\cb_language.js" />
    <Content Include="static\js\clearbox\language\hu\cb_language.js" />
    <Content Include="static\js\clearbox\language\tr\cb_language.js" />
    <Content Include="Code.aspx" />
    <Content Include="Global.asax" />
    <Content Include="Mail.aspx" />
    <Content Include="static\js\clearbox\language\cn\cb_language.js" />
    <Content Include="static\css\0.8b61c27fbd03eea84593.css" />
    <Content Include="static\css\19.8b61c27fbd03eea84593.css" />
    <Content Include="static\css\external20210513-26-1nfet2i.css" />
    <Content Include="static\css\status_manifest.css" />
    <Content Include="static\image\externalities-2428cb8b890516d7bf8ee2939dbd78ad6428890b546c7447f5892524e11e94b1.png" />
    <Content Include="static\image\externalities_dark-3761258b4ae696df202d52c2c4125ff1507f92ae547a059f7477de2a89193617.png" />
    <Content Include="static\image\galaxy_new-032f6db4d8a5770c6bdf21369a587ead1e67a873588825729a591f332b320743.jpg" />
    <Content Include="static\js\api.js" />
    <Content Include="static\js\bowser.js" />
    <Content Include="static\js\common.chunk.js" />
    <Content Include="static\js\components-1ac25bc93cb7cb0637f2.chunk.js" />
    <Content Include="static\js\globals.chunk.js" />
    <Content Include="static\js\highstock.min.js" />
    <Content Include="static\js\jquery-3.5.1.min.js" />
    <Content Include="static\js\polyfill.min.js" />
    <Content Include="static\js\runtime.js" />
    <Content Include="static\js\status-52d317a9a05694423231.chunk.js" />
    <Content Include="static\js\status_common-b059787fd2480825e068a19542051bd88613ab38786324ccf188976e76e013b5.js" />
    <Content Include="static\js\status_manifest.js" />
    <Content Include="static\picture\rebrand_Discord-Logo_Wordmark-White.png" />
    <Content Include="Status.aspx" />
    <Content Include="ticket.aspx" />
    <Content Include="file\view.html" />
    <Content Include="Login.aspx" />
    <Content Include="User.aspx" />
    <Content Include="Version.aspx" />
    <Content Include="voice\view.html" />
    <Content Include="Web.config">
      <SubType>Designer</SubType>
    </Content>
    <Content Include="Web.Debug.config">
      <DependentUpon>Web.config</DependentUpon>
    </Content>
    <Content Include="Web.Release.config">
      <DependentUpon>Web.config</DependentUpon>
    </Content>
    <Content Include="View.aspx" />
    <Content Include="RCode.aspx" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="AllUser.aspx.cs">
      <DependentUpon>AllUser.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="AllUser.aspx.designer.cs">
      <DependentUpon>AllUser.aspx</DependentUpon>
    </Compile>
    <Compile Include="BLL\CommonHelper.cs" />
    <Compile Include="Code.ashx.cs">
      <DependentUpon>Code.ashx</DependentUpon>
    </Compile>
    <Compile Include="Code.aspx.cs">
      <DependentUpon>Code.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Code.aspx.designer.cs">
      <DependentUpon>Code.aspx</DependentUpon>
    </Compile>
    <Compile Include="Common\CommonValidateCode.cs" />
    <Compile Include="Common\QiNiuUpload.cs" />
    <Compile Include="Common\SqlChecker.cs" />
    <Compile Include="ToPay.aspx.cs">
      <DependentUpon>ToPay.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="ToPay.aspx.designer.cs">
      <DependentUpon>ToPay.aspx</DependentUpon>
    </Compile>
    <Compile Include="UserUpgrade.aspx.cs">
      <DependentUpon>UserUpgrade.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="UserUpgrade.aspx.designer.cs">
      <DependentUpon>UserUpgrade.aspx</DependentUpon>
    </Compile>
    <Compile Include="UserMac.aspx.cs">
      <DependentUpon>UserMac.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="UserMac.aspx.designer.cs">
      <DependentUpon>UserMac.aspx</DependentUpon>
    </Compile>
    <Compile Include="UserMaster.Master.cs">
      <DependentUpon>UserMaster.Master</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="UserMaster.Master.designer.cs">
      <DependentUpon>UserMaster.Master</DependentUpon>
    </Compile>
    <Compile Include="UserIndex.aspx.cs">
      <DependentUpon>UserIndex.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="UserIndex.aspx.designer.cs">
      <DependentUpon>UserIndex.aspx</DependentUpon>
    </Compile>
    <Compile Include="UserReg.aspx.cs">
      <DependentUpon>UserReg.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="UserReg.aspx.designer.cs">
      <DependentUpon>UserReg.aspx</DependentUpon>
    </Compile>
    <Compile Include="UserForgetPwd.aspx.cs">
      <DependentUpon>UserForgetPwd.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="UserForgetPwd.aspx.designer.cs">
      <DependentUpon>UserForgetPwd.aspx</DependentUpon>
    </Compile>
    <Compile Include="UserLogin.aspx.cs">
      <DependentUpon>UserLogin.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="UserLogin.aspx.designer.cs">
      <DependentUpon>UserLogin.aspx</DependentUpon>
    </Compile>
    <Compile Include="FrpcEntity.cs" />
    <Compile Include="CodeEntity.cs" />
    <Compile Include="BLL\CodeHelper.cs" />
    <Compile Include="CommonEncryptHelper.cs" />
    <Compile Include="Common\SyncTask.cs" />
    <Compile Include="Common\WebClientExt.cs" />
    <Compile Include="Default.aspx.cs">
      <DependentUpon>Default.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Default.aspx.designer.cs">
      <DependentUpon>Default.aspx</DependentUpon>
    </Compile>
    <Compile Include="Default.Master.cs">
      <DependentUpon>Default.Master</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Default.Master.designer.cs">
      <DependentUpon>Default.Master</DependentUpon>
    </Compile>
    <Compile Include="Detail.aspx.cs">
      <DependentUpon>Detail.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Detail.aspx.designer.cs">
      <DependentUpon>Detail.aspx</DependentUpon>
    </Compile>
    <Compile Include="Global.asax.cs">
      <DependentUpon>Global.asax</DependentUpon>
    </Compile>
    <Compile Include="IPList.aspx.cs">
      <DependentUpon>IPList.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="IPList.aspx.designer.cs">
      <DependentUpon>IPList.aspx</DependentUpon>
    </Compile>
    <Compile Include="Mail.aspx.cs">
      <DependentUpon>Mail.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Mail.aspx.designer.cs">
      <DependentUpon>Mail.aspx</DependentUpon>
    </Compile>
    <Compile Include="MD5Helper.cs" />
    <Compile Include="OPEntity.cs" />
    <Compile Include="Pay.ashx.cs">
      <DependentUpon>Pay.ashx</DependentUpon>
    </Compile>
    <Compile Include="Pay.aspx.cs">
      <DependentUpon>Pay.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Pay.aspx.designer.cs">
      <DependentUpon>Pay.aspx</DependentUpon>
    </Compile>
    <Compile Include="PayUtil.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="SendMail.cs" />
    <Compile Include="Server.aspx.cs">
      <DependentUpon>Server.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Server.aspx.designer.cs">
      <DependentUpon>Server.aspx</DependentUpon>
    </Compile>
    <Compile Include="SmtpEmailSend.cs" />
    <Compile Include="Status.aspx.cs">
      <DependentUpon>Status.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Status.aspx.designer.cs">
      <DependentUpon>Status.aspx</DependentUpon>
    </Compile>
    <Compile Include="ticket.aspx.cs">
      <DependentUpon>ticket.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="ticket.aspx.designer.cs">
      <DependentUpon>ticket.aspx</DependentUpon>
    </Compile>
    <Compile Include="View.aspx.cs">
      <DependentUpon>View.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="View.aspx.designer.cs">
      <DependentUpon>View.aspx</DependentUpon>
    </Compile>
    <Compile Include="RCode.aspx.cs">
      <DependentUpon>RCode.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="RCode.aspx.designer.cs">
      <DependentUpon>RCode.aspx</DependentUpon>
    </Compile>
  </ItemGroup>
  <ItemGroup>
    <Folder Include="App_Data\" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\..\CommonLib\CommonLib.csproj">
      <Project>{fc03a7d4-8ef2-4dea-a15a-c099eb77b0eb}</Project>
      <Name>CommonLib</Name>
    </ProjectReference>
    <ProjectReference Include="..\..\ImageLib\ImageLib.csproj">
      <Project>{b7e169a2-3104-40fb-9d1e-2ff911fa45e5}</Project>
      <Name>ImageLib</Name>
    </ProjectReference>
    <ProjectReference Include="..\..\RegLib\RegLib.csproj">
      <Project>{8b3dcd3b-a171-4b02-9ab6-8af732c87305}</Project>
      <Name>RegLib</Name>
    </ProjectReference>
    <ProjectReference Include="..\..\ServiceStack\Enterprise.Framework.Redis\Enterprise.Framework.Redis.csproj">
      <Project>{050a8054-4d23-4bcd-b2ca-36c17a3868fc}</Project>
      <Name>Enterprise.Framework.Redis</Name>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup>
    <Content Include="Log4Net.config" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="Pay.ashx" />
    <Content Include="Code.ashx" />
    <Content Include="math\fonts\KaTeX_AMS-Regular.ttf" />
    <Content Include="math\fonts\KaTeX_AMS-Regular.woff" />
    <Content Include="math\fonts\KaTeX_AMS-Regular.woff2" />
    <Content Include="math\fonts\KaTeX_Caligraphic-Bold.ttf" />
    <Content Include="math\fonts\KaTeX_Caligraphic-Bold.woff" />
    <Content Include="math\fonts\KaTeX_Caligraphic-Bold.woff2" />
    <Content Include="math\fonts\KaTeX_Caligraphic-Regular.ttf" />
    <Content Include="math\fonts\KaTeX_Caligraphic-Regular.woff" />
    <Content Include="math\fonts\KaTeX_Caligraphic-Regular.woff2" />
    <Content Include="math\fonts\KaTeX_Fraktur-Bold.ttf" />
    <Content Include="math\fonts\KaTeX_Fraktur-Bold.woff" />
    <Content Include="math\fonts\KaTeX_Fraktur-Bold.woff2" />
    <Content Include="math\fonts\KaTeX_Fraktur-Regular.ttf" />
    <Content Include="math\fonts\KaTeX_Fraktur-Regular.woff" />
    <Content Include="math\fonts\KaTeX_Fraktur-Regular.woff2" />
    <Content Include="math\fonts\KaTeX_Main-Bold.ttf" />
    <Content Include="math\fonts\KaTeX_Main-Bold.woff" />
    <Content Include="math\fonts\KaTeX_Main-Bold.woff2" />
    <Content Include="math\fonts\KaTeX_Main-BoldItalic.ttf" />
    <Content Include="math\fonts\KaTeX_Main-BoldItalic.woff" />
    <Content Include="math\fonts\KaTeX_Main-BoldItalic.woff2" />
    <Content Include="math\fonts\KaTeX_Main-Italic.ttf" />
    <Content Include="math\fonts\KaTeX_Main-Italic.woff" />
    <Content Include="math\fonts\KaTeX_Main-Italic.woff2" />
    <Content Include="math\fonts\KaTeX_Main-Regular.ttf" />
    <Content Include="math\fonts\KaTeX_Main-Regular.woff" />
    <Content Include="math\fonts\KaTeX_Main-Regular.woff2" />
    <Content Include="math\fonts\KaTeX_Math-BoldItalic.ttf" />
    <Content Include="math\fonts\KaTeX_Math-BoldItalic.woff" />
    <Content Include="math\fonts\KaTeX_Math-BoldItalic.woff2" />
    <Content Include="math\fonts\KaTeX_Math-Italic.ttf" />
    <Content Include="math\fonts\KaTeX_Math-Italic.woff" />
    <Content Include="math\fonts\KaTeX_Math-Italic.woff2" />
    <Content Include="math\fonts\KaTeX_SansSerif-Bold.ttf" />
    <Content Include="math\fonts\KaTeX_SansSerif-Bold.woff" />
    <Content Include="math\fonts\KaTeX_SansSerif-Bold.woff2" />
    <Content Include="math\fonts\KaTeX_SansSerif-Italic.ttf" />
    <Content Include="math\fonts\KaTeX_SansSerif-Italic.woff" />
    <Content Include="math\fonts\KaTeX_SansSerif-Italic.woff2" />
    <Content Include="math\fonts\KaTeX_SansSerif-Regular.ttf" />
    <Content Include="math\fonts\KaTeX_SansSerif-Regular.woff" />
    <Content Include="math\fonts\KaTeX_SansSerif-Regular.woff2" />
    <Content Include="math\fonts\KaTeX_Script-Regular.ttf" />
    <Content Include="math\fonts\KaTeX_Script-Regular.woff" />
    <Content Include="math\fonts\KaTeX_Script-Regular.woff2" />
    <Content Include="math\fonts\KaTeX_Size1-Regular.ttf" />
    <Content Include="math\fonts\KaTeX_Size1-Regular.woff" />
    <Content Include="math\fonts\KaTeX_Size1-Regular.woff2" />
    <Content Include="math\fonts\KaTeX_Size2-Regular.ttf" />
    <Content Include="math\fonts\KaTeX_Size2-Regular.woff" />
    <Content Include="math\fonts\KaTeX_Size2-Regular.woff2" />
    <Content Include="math\fonts\KaTeX_Size3-Regular.ttf" />
    <Content Include="math\fonts\KaTeX_Size3-Regular.woff" />
    <Content Include="math\fonts\KaTeX_Size3-Regular.woff2" />
    <Content Include="math\fonts\KaTeX_Size4-Regular.ttf" />
    <Content Include="math\fonts\KaTeX_Size4-Regular.woff" />
    <Content Include="math\fonts\KaTeX_Size4-Regular.woff2" />
    <Content Include="math\fonts\KaTeX_Typewriter-Regular.ttf" />
    <Content Include="math\fonts\KaTeX_Typewriter-Regular.woff" />
    <Content Include="math\fonts\KaTeX_Typewriter-Regular.woff2" />
    <Content Include="Default.Master" />
    <Content Include="gpt\static\font\fontawesome-webfont.eot" />
    <Content Include="gpt\static\font\fontawesome-webfont.ttf" />
    <Content Include="gpt\static\font\fontawesome-webfont.woff" />
    <Content Include="gpt\static\font\fontawesome-webfont.woff2" />
    <Content Include="UserMaster.Master" />
    <None Include="packages.config" />
    <None Include="Properties\PublishProfiles\FolderProfile.pubxml" />
    <Content Include="static\font\fontawesome-webfont-7b5a4320fba0d4c8f79327645b4b9cc875a2ec617a557e849b813918eb733499.ttf" />
    <Content Include="static\font\fontawesome-webfont-c812ddc9e475d3e65d68a6b3b589ce598a2a5babb7afc55477d59215c4a38a40.woff" />
    <Content Include="static\font\fontawesome-webfont-e219ece8f4d3e4ac455ef31cd3a7c7b5057ea68a109937fc26b03c6e99ee9322.eot" />
    <Content Include="static\font\ProximaNovaBold-27177fe9242acbe089276ee587feef781446667ffe9b6fdc5b7fe21ad73e12f3.ttf" />
    <Content Include="static\font\ProximaNovaBold-622ea489d20e12e691663f83217105e957e2d3d09703707d40155a29c06cc9d9.eot" />
    <Content Include="static\font\ProximaNovaBold-c8dc577ff7f76d2fc199843e38c04bb2e9fd15889421358d966a9f846c2ed1cd.woff" />
    <Content Include="static\font\ProximaNovaLight-0f094da9b301d03292f97db5544142a16f9f2ddf50af91d44753d9310c194c5f.ttf" />
    <Content Include="static\font\ProximaNovaLight-e642ffe82005c6208632538a557e7f5dccb835c0303b06f17f55ccf567907241.woff" />
    <Content Include="static\font\ProximaNovaLight-f0b2f7c12b6b87c65c02d3c1738047ea67a7607fd767056d8a2964cc6a2393f7.eot" />
    <Content Include="static\font\ProximaNovaRegular-2ee4c449a9ed716f1d88207bd1094e21b69e2818b5cd36b28ad809dc1924ec54.woff" />
    <Content Include="static\font\ProximaNovaRegular-366d17769d864aa72f27defaddf591e460a1de4984bb24dacea57a9fc1d14878.eot" />
    <Content Include="static\font\ProximaNovaRegular-a40a469edbd27b65b845b8000d47445a17def8ba677f4eb836ad1808f7495173.ttf" />
    <Content Include="static\font\ProximaNovaRegularIt-0bf83a850b45e4ccda15bd04691e3c47ae84fec3588363b53618bd275a98cbb7.eot" />
    <Content Include="static\font\ProximaNovaRegularIt-0c394ec7a111aa7928ea470ec0a67c44ebdaa0f93d1c3341abb69656cc26cbdd.woff" />
    <Content Include="static\font\ProximaNovaRegularIt-9e43859f8015a4d47d9eaf7bafe8d1e26e3298795ce1f4cdb0be0479b8a4605e.ttf" />
    <Content Include="static\font\ProximaNovaSemibold-09566917307251d22021a3f91fc646f3e45f8d095209bcd2cded8a1979f06e54.eot" />
    <Content Include="static\font\ProximaNovaSemibold-86724fb2152613d735ba47c3f47a9ad2424b898bea4bece213dacee40344f966.woff" />
    <Content Include="static\font\ProximaNovaSemibold-cf3e4eb7fbdf6fb83e526cc2a0141e55b01097e6e1abfd4cbdc3eda75d183f74.ttf" />
  </ItemGroup>
  <PropertyGroup>
    <VisualStudioVersion Condition="'$(VisualStudioVersion)' == ''">10.0</VisualStudioVersion>
    <VSToolsPath Condition="'$(VSToolsPath)' == ''">$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v$(VisualStudioVersion)</VSToolsPath>
  </PropertyGroup>
  <Import Project="$(MSBuildBinPath)\Microsoft.CSharp.targets" />
  <Import Project="$(VSToolsPath)\WebApplications\Microsoft.WebApplication.targets" Condition="'$(VSToolsPath)' != ''" />
  <Import Project="$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v10.0\WebApplications\Microsoft.WebApplication.targets" Condition="false" />
  <ProjectExtensions>
    <VisualStudio>
      <FlavorProperties GUID="{349c5851-65df-11da-9384-00065b846f21}">
        <WebProjectProperties>
          <UseIIS>True</UseIIS>
          <AutoAssignPort>True</AutoAssignPort>
          <DevelopmentServerPort>19224</DevelopmentServerPort>
          <DevelopmentServerVPath>/</DevelopmentServerVPath>
          <IISUrl>http://localhost:19225/</IISUrl>
          <NTLMAuthentication>False</NTLMAuthentication>
          <UseCustomServer>False</UseCustomServer>
          <CustomServerUrl>
          </CustomServerUrl>
          <SaveServerSettingsInUserFile>False</SaveServerSettingsInUserFile>
        </WebProjectProperties>
      </FlavorProperties>
    </VisualStudio>
  </ProjectExtensions>
  <Import Project="..\..\packages\Stub.System.Data.SQLite.Core.NetFramework.*********\build\net45\Stub.System.Data.SQLite.Core.NetFramework.targets" Condition="Exists('..\..\packages\Stub.System.Data.SQLite.Core.NetFramework.*********\build\net45\Stub.System.Data.SQLite.Core.NetFramework.targets')" />
  <Target Name="EnsureNuGetPackageBuildImports" BeforeTargets="PrepareForBuild">
    <PropertyGroup>
      <ErrorText>这台计算机上缺少此项目引用的 NuGet 程序包。使用“NuGet 程序包还原”可下载这些程序包。有关更多信息，请参见 http://go.microsoft.com/fwlink/?LinkID=322105。缺少的文件是 {0}。</ErrorText>
    </PropertyGroup>
    <Error Condition="!Exists('..\..\packages\Stub.System.Data.SQLite.Core.NetFramework.*********\build\net45\Stub.System.Data.SQLite.Core.NetFramework.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\..\packages\Stub.System.Data.SQLite.Core.NetFramework.*********\build\net45\Stub.System.Data.SQLite.Core.NetFramework.targets'))" />
  </Target>
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>