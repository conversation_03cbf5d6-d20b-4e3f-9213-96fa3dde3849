﻿using CommonLib;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Security.Cryptography;
using System.Security.Policy;
using System.Text;

namespace HanZiOcr
{
    /// <summary>
    /// ByteDance-微信小程序
    /// http://ailab-cv-sdk.bytedance.com/docs/1818/ocr_normal_v1/
    /// </summary>
    public class ByteDanceRec : BaseOcrRec
    {
        public ByteDanceRec()
        {
            OcrGroup = OcrGroupType.字节;
            OcrType = HanZiOcrType.字节;
            MaxExecPerTime = 28;
            LstJsonPreProcessArray = new List<object>() { "data", "line_texts" };

            IsSupportVertical = true;
            IsJsonArrayStringWithLocation = true;
            LstJsonLocationProcessArray = new List<object>() { "data", "line_rects" };
            StrResultJsonSpilt = "words";
            IsDesrializeVerticalByLocation = true;
            DicDeserializeVerticalJson = new Dictionary<string, string> { { "left", "x" }, { "top", "y" } };
        }

        private const string clientId = "2c6c9ae7-7d9b-45ce-9ddb-5a6af5c93b39";
        private const string clientSecret = "558fefa9-15c4-4ebd-9a81-0b2340bfd815";

        protected override string GetHtml(OcrContent content)
        {
            var noticeStr = new Random().Next().ToString();
            var timeSpan = Convert.ToInt64((DateTime.UtcNow - new DateTime(1970, 1, 1, 0, 0, 0, 0)).TotalSeconds).ToString();
            string[] signElement = { timeSpan, noticeStr, clientSecret };
            Array.Sort(signElement);
            var rawSign = string.Join("", signElement);
            var sign = ToMd5(rawSign);
            var strPost = "image_base64=" + System.Web.HttpUtility.UrlEncode(content.strBase64) + "&api_key=" + clientId + "&timestamp=" + timeSpan + "&nonce=" + noticeStr + "&sign=" + sign;
            var strTmp = WebClientSyncExt.GetHtml("https://small-server.bytedance.com/ocr/ocr_normal", ""
                , strPost, "https://servicewechat.com/wx462e1c1acec3cc25/9/page-frame.html", ExecTimeOutSeconds);

            //strTmp = strTmp.Replace("\r","").Replace("\n", "").Replace("\t", "");

            return strTmp;
        }

        private string ToMd5(string strContent)
        {
            string result = "";
            if (!string.IsNullOrEmpty(strContent))
            {
                using (SHA1 md5 = SHA1.Create())
                {
                    var byts = md5.ComputeHash(Encoding.UTF8.GetBytes(strContent));
                    StringBuilder hex = new StringBuilder(byts.Length * 2);
                    foreach (byte b in byts)
                        hex.AppendFormat("{0:x2}", b);
                    result = hex.ToString();
                }
            }
            return result.ToLower();
        }
    }
}