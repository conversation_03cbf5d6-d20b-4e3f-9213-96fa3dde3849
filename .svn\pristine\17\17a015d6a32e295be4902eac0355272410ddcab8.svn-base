// Code generated by Microsoft (R) AutoRest Code Generator *******
// Changes may cause incorrect behavior and will be lost if the code is
// regenerated.

namespace AutorestClient
{
    using Models;
    using System.Threading;
    using System.Threading.Tasks;

    /// <summary>
    /// Extension methods for HelloZipOperations.
    /// </summary>
    public static partial class HelloZipOperationsExtensions
    {
            /// <param name='operations'>
            /// The operations group for this extension method.
            /// </param>
            /// <param name='name'>
            /// </param>
            /// <param name='test'>
            /// </param>
            public static HelloZipResponse Get(this IHelloZipOperations operations, string name = default(string), string test = default(string))
            {
                return operations.GetAsync(name, test).GetAwaiter().GetResult();
            }

            /// <param name='operations'>
            /// The operations group for this extension method.
            /// </param>
            /// <param name='name'>
            /// </param>
            /// <param name='test'>
            /// </param>
            /// <param name='cancellationToken'>
            /// The cancellation token.
            /// </param>
            public static async Task<HelloZipResponse> GetAsync(this IHelloZipOperations operations, string name = default(string), string test = default(string), CancellationToken cancellationToken = default(CancellationToken))
            {
                using (var _result = await operations.GetWithHttpMessagesAsync(name, test, null, cancellationToken).ConfigureAwait(false))
                {
                    return _result.Body;
                }
            }

            /// <param name='operations'>
            /// The operations group for this extension method.
            /// </param>
            /// <param name='name'>
            /// </param>
            /// <param name='test'>
            /// </param>
            /// <param name='body'>
            /// </param>
            public static HelloZipResponse Create(this IHelloZipOperations operations, string name = default(string), string test = default(string), HelloZip body = default(HelloZip))
            {
                return operations.CreateAsync(name, test, body).GetAwaiter().GetResult();
            }

            /// <param name='operations'>
            /// The operations group for this extension method.
            /// </param>
            /// <param name='name'>
            /// </param>
            /// <param name='test'>
            /// </param>
            /// <param name='body'>
            /// </param>
            /// <param name='cancellationToken'>
            /// The cancellation token.
            /// </param>
            public static async Task<HelloZipResponse> CreateAsync(this IHelloZipOperations operations, string name = default(string), string test = default(string), HelloZip body = default(HelloZip), CancellationToken cancellationToken = default(CancellationToken))
            {
                using (var _result = await operations.CreateWithHttpMessagesAsync(name, test, body, null, cancellationToken).ConfigureAwait(false))
                {
                    return _result.Body;
                }
            }

            /// <param name='operations'>
            /// The operations group for this extension method.
            /// </param>
            /// <param name='name'>
            /// </param>
            /// <param name='test'>
            /// </param>
            /// <param name='body'>
            /// </param>
            public static HelloZipResponse Post(this IHelloZipOperations operations, string name = default(string), string test = default(string), HelloZip body = default(HelloZip))
            {
                return operations.PostAsync(name, test, body).GetAwaiter().GetResult();
            }

            /// <param name='operations'>
            /// The operations group for this extension method.
            /// </param>
            /// <param name='name'>
            /// </param>
            /// <param name='test'>
            /// </param>
            /// <param name='body'>
            /// </param>
            /// <param name='cancellationToken'>
            /// The cancellation token.
            /// </param>
            public static async Task<HelloZipResponse> PostAsync(this IHelloZipOperations operations, string name = default(string), string test = default(string), HelloZip body = default(HelloZip), CancellationToken cancellationToken = default(CancellationToken))
            {
                using (var _result = await operations.PostWithHttpMessagesAsync(name, test, body, null, cancellationToken).ConfigureAwait(false))
                {
                    return _result.Body;
                }
            }

            /// <param name='operations'>
            /// The operations group for this extension method.
            /// </param>
            /// <param name='name'>
            /// </param>
            /// <param name='test'>
            /// </param>
            public static HelloZipResponse Delete(this IHelloZipOperations operations, string name = default(string), string test = default(string))
            {
                return operations.DeleteAsync(name, test).GetAwaiter().GetResult();
            }

            /// <param name='operations'>
            /// The operations group for this extension method.
            /// </param>
            /// <param name='name'>
            /// </param>
            /// <param name='test'>
            /// </param>
            /// <param name='cancellationToken'>
            /// The cancellation token.
            /// </param>
            public static async Task<HelloZipResponse> DeleteAsync(this IHelloZipOperations operations, string name = default(string), string test = default(string), CancellationToken cancellationToken = default(CancellationToken))
            {
                using (var _result = await operations.DeleteWithHttpMessagesAsync(name, test, null, cancellationToken).ConfigureAwait(false))
                {
                    return _result.Body;
                }
            }

    }
}
