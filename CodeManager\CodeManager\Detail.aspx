<%@ Page Title="OCR助手产品介绍" Language="C#" MasterPageFile="~/Site.Master" AutoEventWireup="true" %>
<%@ Import Namespace="Account.Web" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="server">
    <meta name="description" content="OCR文字识别助手是一款集文字、表格、公式、文档及翻译于一体的智能生产力工具。支持100+语言识别，多平台兼容，全球云端存储，业内顶级识别速度。" />
    <meta name="keywords" content="免费OCR,免费文字识别,免费图片识别,免费图文识别,免费图片转文字,OCR识别,文字识别,图片识别,图片文字识别,图片转文字,图片转表格,图片转公式,文档识别,文档翻译" />
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=5.0" />
    <link rel="preconnect" href="https://lsw-fast.lenovo.com.cn" crossorigin />
    <script type="application/ld+json">{"@context":"http://schema.org","@type":"SoftwareApplication","name":"OCR Assistant","description":"OCR Assistant is an efficient productivity tool that integrates text, tables, formulas, documents, and translation.","category":"Productivity","applicationCategory":"Business","image":"https://lsw-fast.lenovo.com.cn/appstore/apps/adp/logo/7273-2024-06-05074650-1717588010656.gif","screenshot":["https://lsw-fast.lenovo.com.cn/appstore/apps/adp/img/3441-2023-07-18051430-1689671670795.png","https://lsw-fast.lenovo.com.cn/appstore/apps/adp/img/4112-2023-07-18051443-1689671683656.png","https://lsw-fast.lenovo.com.cn/appstore/apps/adp/img/0653-2023-07-18051453-1689671693673.png","https://lsw-fast.lenovo.com.cn/appstore/apps/adp/img/6000-2023-07-18051502-1689671702366.png"],"aggregateRating":{"@type":"AggregateRating","worstRating":"1","bestRating":"5","ratingValue":"4.8","ratingCount":"<%=((System.DateTime.Now.ToUniversalTime().Ticks - 621355968000000000) / 100000000000*3).ToString() %>"},"offers":{"@type":"Offer","price":0,"priceCurrency":"USD","category":"free"},"operatingSystem": "Windows"}</script>

    <!-- 只加载必要的样式，避免与Site.Master冲突 -->
    <style type="text/css">
        /* 产品页面专用样式 - 避免全局冲突 */
        .product-page-wrapper {
            position: relative;
            width: 100%;
            font-family: "Microsoft YaHei", Arial, sans-serif;
        }

        /* 横幅轮播样式 */
        .product-page-wrapper .camscanner_banner {
            position: relative;
            width: 100%;
            height: 400px;
            overflow: hidden;
            background: #f5f5f5;
        }

        .product-page-wrapper .camscanner_banner .bd {
            position: relative;
            width: 100%;
            height: 100%;
        }

        .product-page-wrapper .camscanner_banner .bd ul {
            position: relative;
            width: 100%;
            height: 100%;
            margin: 0;
            padding: 0;
            list-style: none;
        }

        .product-page-wrapper .camscanner_banner .bd li {
            position: absolute;
            width: 100%;
            height: 100%;
            background-size: cover;
            background-position: center;
            background-repeat: no-repeat;
        }

        .product-page-wrapper .camscanner_banner .hd {
            position: absolute;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            z-index: 10;
        }

        .product-page-wrapper .camscanner_banner .hd ul {
            display: flex;
            margin: 0;
            padding: 0;
            list-style: none;
        }

        .product-page-wrapper .camscanner_banner .hd li {
            width: 40px;
            height: 40px;
            line-height: 40px;
            text-align: center;
            background: rgba(0,0,0,0.5);
            color: white;
            margin: 0 5px;
            border-radius: 50%;
            cursor: pointer;
            transition: all 0.3s;
        }

        .product-page-wrapper .camscanner_banner .hd li.on,
        .product-page-wrapper .camscanner_banner .hd li:hover {
            background: #007cfa;
        }

        /* 导航菜单样式 */
        .product-page-wrapper .camscanner_menu {
            background: #fff;
            border-bottom: 1px solid #e1e6ed;
            padding: 15px 0;
            position: sticky;
            top: 60px;
            z-index: 100;
        }

        .product-page-wrapper .warp {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 15px;
        }

        .product-page-wrapper .camscanner_menu .fl {
            float: left;
            margin: 0;
            padding: 0;
            list-style: none;
            display: flex;
        }

        .product-page-wrapper .camscanner_menu .fr {
            float: right;
            margin: 0;
            padding: 0;
            list-style: none;
        }

        .product-page-wrapper .camscanner_menu li {
            margin-right: 20px;
        }

        .product-page-wrapper .camscanner_menu a {
            display: block;
            padding: 10px 15px;
            color: #333;
            text-decoration: none;
            border-radius: 4px;
            transition: all 0.3s;
        }

        .product-page-wrapper .camscanner_menu a:hover {
            background: #007cfa;
            color: white;
        }

        .product-page-wrapper .camscanner_menu .style a {
            background: #007cfa;
            color: white;
            font-weight: bold;
        }

        /* 内容区域样式 */
        .product-page-wrapper .main {
            padding: 40px 0;
        }

        .product-page-wrapper .biaoti {
            text-align: center;
            margin-bottom: 40px;
        }

        .product-page-wrapper .tit {
            font-size: 32px;
            color: #333;
            margin-bottom: 10px;
            font-weight: bold;
        }

        .product-page-wrapper .info {
            font-size: 16px;
            color: #666;
        }

        .product-page-wrapper .sales_x {
            display: flex;
            flex-wrap: wrap;
            justify-content: space-around;
            align-items: flex-start;
        }

        .product-page-wrapper .d1 {
            width: 300px;
            text-align: center;
            margin: 20px;
            padding: 20px;
            background: #f9f9f9;
            border-radius: 8px;
            transition: all 0.3s;
        }

        .product-page-wrapper .d1:hover {
            transform: translateY(-5px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .product-page-wrapper .d1 .pic img {
            width: 80px;
            height: 80px;
            margin-bottom: 15px;
        }

        .product-page-wrapper .d1 dt {
            font-size: 18px;
            font-weight: bold;
            color: #333;
            margin-bottom: 10px;
        }

        .product-page-wrapper .d1 .info {
            font-size: 14px;
            color: #666;
            line-height: 1.5;
        }

        /* 清除浮动 */
        .product-page-wrapper .clear {
            clear: both;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .product-page-wrapper .camscanner_menu .fl {
                flex-direction: column;
                width: 100%;
            }

            .product-page-wrapper .camscanner_menu .fr {
                float: none;
                margin-top: 10px;
            }

            .product-page-wrapper .sales_x {
                flex-direction: column;
                align-items: center;
            }

            .product-page-wrapper .d1 {
                width: 90%;
                margin: 10px;
            }
        }
    </style>
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="server">
    <!-- 产品页面内容容器 -->
    <div class="product-page-wrapper" style="margin-top: 60px;">

        <!-- 产品横幅 -->
        <div class="camscanner_banner">
            <div class="bd">
                <ul>
                    <li style="background-image: url('<%=CommonRequest.GetStaticUrl(Request) %>static/v1/image/bg3.jpg');">
                        <div class="warp"></div>
                    </li>
                </ul>
            </div>
        </div>

        <header>
            <h1 class="product-title" style="position: absolute; width: 1px; height: 1px; padding: 0; margin: -1px; overflow: hidden; clip: rect(0, 0, 0, 0); white-space: nowrap; border: 0;">OCR助手智能文字识别一体化解决方案</h1>
        </header>

        <!-- 应用场景导航 -->
        <nav class="camscanner_menu" id="tip" aria-label="产品导航">
            <div class="warp">
                <ul class="fl">
                    <li><a href="#scene-section">应用场景</a></li>
                    <li><a href="#feature-section">功能介绍</a></li>
                    <li><a href="#review-section">用户评价</a></li>
                    <li><a href="#media-section">媒体报道</a></li>
                </ul>
                <ul class="fr">
                    <li class="style"><a href="<%=CommonRequest.GetDownLoadUrl(Request) %>"><b>立即下载</b></a></li>
                </ul>
            </div>
        </nav>

        <!-- 应用场景介绍 -->
        <section id="scene-section" class="main warp">
            <div class="biaoti">
                <h2 class="tit">电脑扫描仪，随时记录，轻松分享</h2>
                <p class="info">"电脑上最好的100个软件之一"</p>
            </div>
            <div class="sales_x">
                <dl class="d1">
                    <dd class="pic">
                        <img src="<%=CommonRequest.GetStaticUrl(Request) %>static/v1/picture/cam.001.png" alt="轻松处理各种场景"></dd>
                    <dt>轻松处理各种场景</dt>
                    <span class="info">办公一族非常需要，能非常方便的处理转换图文</span>
                </dl>
                <dl class="d1">
                    <dd class="pic">
                        <img src="<%=CommonRequest.GetStaticUrl(Request) %>static/v1/picture/cam.002.png" alt="自动图像识别预处理"></dd>
                    <dt>自动图像识别预处理</dt>
                    <span class="info">图片扫描生成自动锐化提亮，倾斜矫正</span>
                </dl>
                <dl class="d1">
                    <dd class="pic">
                        <img src="<%=CommonRequest.GetStaticUrl(Request) %>static/v1/picture/cam.003.png" alt="打字到手酸的同学们的神器"></dd>
                    <dt>打字到手酸的同学们的神器</dt>
                    <span class="info">OCR识别，图片瞬间变文本，告别打字打到手酸</span>
                </dl>
            </div>
        </section>

        <div class="clear"></div>

        <!-- 功能介绍 -->
        <section id="feature-section" class="main warp">
            <div class="biaoti">
                <h2 class="tit">核心功能特色</h2>
                <p class="info">全方位智能OCR解决方案</p>
            </div>
            <div class="sales_x">
                <dl class="d1">
                    <dd class="pic">
                        <img src="<%=CommonRequest.GetStaticUrl(Request) %>static/v1/picture/feature1.png" alt="AI智能OCR识别" style="width: 60px; height: 60px;"></dd>
                    <dt>AI-智能OCR识别</dt>
                    <span class="info">截图/拍照/文件，一键操作，深度加速。支持识别100+语言，助力全球业务展开。账号特权，云端存储，不限设备，不限次数。</span>
                </dl>
                <dl class="d1">
                    <dd class="pic">
                        <img src="<%=CommonRequest.GetStaticUrl(Request) %>static/v1/picture/feature2.png" alt="轻松处理各种文档" style="width: 60px; height: 60px;"></dd>
                    <dt>轻松处理各种文档</dt>
                    <span class="info">支持Office全家桶(Word,PPT,Excel等)。支持PDF文档扫描及转换。支持全文扫描，支持全文翻译。各种办公文档智能解析，处理结果支持一键下载。</span>
                </dl>
                <dl class="d1">
                    <dd class="pic">
                        <img src="<%=CommonRequest.GetStaticUrl(Request) %>static/v1/picture/feature3.png" alt="云上OCR轻享服务" style="width: 60px; height: 60px;"></dd>
                    <dt>云上OCR，轻享服务</dt>
                    <span class="info">依托于助手高性能服务器，用户只需要一个账号即可轻松享用各种大厂提供的服务。众星捧月，只为让您提升工作生活效率！</span>
                </dl>
            </div>
        </section>

        <!-- 用户评价 -->
        <section id="review-section" class="main warp">
            <div class="biaoti">
                <h2 class="tit">用户评价</h2>
                <p class="info">来自不同行业用户的真实反馈</p>
            </div>
            <div class="sales_x">
                <dl class="d1">
                    <dd class="pic">
                        <img src="<%=CommonRequest.GetStaticUrl(Request) %>static/v1/picture/cam.005.png" alt="办公族"></dd>
                    <dt>John - 办公族</dt>
                    <span class="info">工作原因要经常扫描东西发邮件给客户，用的机会多。OCR助手自动识别，剪裁方便，关键能有多种格式互相转换，简直逆天哦哦哦，真心推荐！！</span>
                </dl>
                <dl class="d1">
                    <dd class="pic">
                        <img src="<%=CommonRequest.GetStaticUrl(Request) %>static/v1/picture/cam.006.png" alt="审计"></dd>
                    <dt>Abby - 审计</dt>
                    <span class="info">审计人员尤其适合使用，图片处理之后效果灰常好，很清晰，值得推荐。就查账翻凭证的时候啊，就你懂的啊，就有了OCR助手超级方便呢～</span>
                </dl>
                <dl class="d1">
                    <dd class="pic">
                        <img src="<%=CommonRequest.GetStaticUrl(Request) %>static/v1/picture/cam.007.png" alt="医生"></dd>
                    <dt>Han - 医生</dt>
                    <span class="info">门诊每天的手写病历和处方可以用OCR助手，很容易的将图片转成数字化，并存储，在没有实现数字化系统的小门诊可以很容易的实现病例和处方的讨论！</span>
                </dl>
            </div>
        </section>

        <!-- 媒体报道 -->
        <section id="media-section" class="main warp">
            <div class="biaoti">
                <h2 class="tit">媒体报道</h2>
                <p class="info">权威媒体的认可与推荐</p>
            </div>
            <div class="sales_x">
                <dl class="d1">
                    <dd class="pic">
                        <img src="<%=CommonRequest.GetStaticUrl(Request) %>static/v1/picture/c103.png" alt="电脑上最好的50个软件之一"></dd>
                    <dt>电脑上最好的50个软件之一</dt>
                    <span class="info">权威科技媒体评选，OCR助手凭借出色的识别准确率和用户体验，荣获"电脑上最好的50个软件之一"称号。</span>
                </dl>
                <dl class="d1">
                    <dd class="pic">
                        <img src="<%=CommonRequest.GetStaticUrl(Request) %>static/v1/picture/c102.png" alt="OCR助手，超级扫描仪"></dd>
                    <dt>OCR助手，超级扫描仪</dt>
                    <span class="info">专业软件评测机构深度测评，称赞OCR助手为"超级扫描仪"，在文字识别领域表现卓越。</span>
                </dl>
                <dl class="d1">
                    <dd class="pic">
                        <img src="<%=CommonRequest.GetStaticUrl(Request) %>static/v1/picture/c100.png" alt="上班族必备10款应用"></dd>
                    <dt>上班族必备10款应用</dt>
                    <span class="info">职场效率专家推荐，OCR助手入选"上班族必备10款应用"，助力职场人士提升工作效率。</span>
                </dl>
            </div>
        </section>

        <br />
        <br />
        <br />
    </div>

    <!-- 产品页面专用样式 -->
    <style type="text/css">
        /* 产品页面样式命名空间，避免与Site.Master冲突 */
        .product-page-wrapper {
            position: relative;
            width: 100%;
        }

        .product-page-wrapper .mod-btn {
            display: inline-block;
            box-sizing: border-box;
            width: 140px;
            height: 40px;
            line-height: 40px;
            padding: 0;
            border: 1px solid transparent;
            border-radius: 3px;
            font-size: 15px;
            text-align: center;
            cursor: pointer;
            transition: border-color .2s, color .2s, background-color .2s;
        }

        .product-page-wrapper .mod-btn-blue {
            color: #fff;
            background-color: #007cfa;
        }

        .product-page-wrapper .mod-btn-blue:hover {
            background-color: #3396fb;
        }

        .product-page-wrapper .mod-btn-blue:focus {
            background-color: #0064ed;
        }

        /* 确保产品页面内容不受Site.Master样式影响 */
        .product-page-wrapper .camscanner_banner {
            position: relative;
            z-index: 1;
        }

        .product-page-wrapper .camscanner_menu {
            position: relative;
            z-index: 2;
        }

        /* 响应式调整 */
        @media (max-width: 768px) {
            .product-page-wrapper .warp {
                padding: 0 15px;
            }

            .product-page-wrapper .camscanner_menu ul.fl {
                width: 100% !important;
            }

            .product-page-wrapper .camscanner_menu ul.fl li {
                width: 50% !important;
                margin-bottom: 10px;
            }
        }
    </style>

    <!-- 产品页面JavaScript功能 -->
    <script type="text/javascript">
        // 使用原生JavaScript实现平滑滚动，避免jQuery冲突
        document.addEventListener('DOMContentLoaded', function() {
            // 平滑滚动到锚点
            var menuLinks = document.querySelectorAll('.camscanner_menu a[href^="#"]');
            menuLinks.forEach(function(link) {
                link.addEventListener('click', function(e) {
                    e.preventDefault();
                    var targetId = this.getAttribute('href').substring(1);
                    var targetElement = document.getElementById(targetId);
                    if (targetElement) {
                        var offsetTop = targetElement.offsetTop - 80;
                        window.scrollTo({
                            top: offsetTop,
                            behavior: 'smooth'
                        });
                    }
                });
            });

            // 简单的悬停效果
            var cards = document.querySelectorAll('.product-page-wrapper .d1');
            cards.forEach(function(card) {
                card.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-5px)';
                });
                card.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0)';
                });
            });
        });
    </script>
</asp:Content>
