<%@ Page Title="OCR助手产品介绍" Language="C#" MasterPageFile="~/Site.Master" AutoEventWireup="true" %>
<%@ Import Namespace="Account.Web" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="server">
    <meta name="description" content="OCR文字识别助手是一款集文字、表格、公式、文档及翻译于一体的智能生产力工具。支持100+语言识别，多平台兼容，全球云端存储，业内顶级识别速度。" />
    <meta name="keywords" content="免费OCR,免费文字识别,免费图片识别,免费图文识别,免费图片转文字,OCR识别,文字识别,图片识别,图片文字识别,图片转文字,图片转表格,图片转公式,文档识别,文档翻译" />
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=5.0" />
    <link rel="preconnect" href="https://lsw-fast.lenovo.com.cn" crossorigin />
    <script type="application/ld+json">{"@context":"http://schema.org","@type":"SoftwareApplication","name":"OCR Assistant","description":"OCR Assistant is an efficient productivity tool that integrates text, tables, formulas, documents, and translation.","category":"Productivity","applicationCategory":"Business","image":"https://lsw-fast.lenovo.com.cn/appstore/apps/adp/logo/7273-2024-06-05074650-1717588010656.gif","screenshot":["https://lsw-fast.lenovo.com.cn/appstore/apps/adp/img/3441-2023-07-18051430-1689671670795.png","https://lsw-fast.lenovo.com.cn/appstore/apps/adp/img/4112-2023-07-18051443-1689671683656.png","https://lsw-fast.lenovo.com.cn/appstore/apps/adp/img/0653-2023-07-18051453-1689671693673.png","https://lsw-fast.lenovo.com.cn/appstore/apps/adp/img/6000-2023-07-18051502-1689671702366.png"],"aggregateRating":{"@type":"AggregateRating","worstRating":"1","bestRating":"5","ratingValue":"4.8","ratingCount":"<%=((System.DateTime.Now.ToUniversalTime().Ticks - 621355968000000000) / 100000000000*3).ToString() %>"},"offers":{"@type":"Offer","price":0,"priceCurrency":"USD","category":"free"},"operatingSystem": "Windows"}</script>

    <!-- 产品页面专用样式 -->
    <link rel="stylesheet" type="text/css" href="<%=CommonRequest.GetStaticUrl(Request) %>static/v1/css/common_new.css?1=1">
    <link rel="stylesheet" type="text/css" href="<%=CommonRequest.GetStaticUrl(Request) %>static/v1/css/index_new.css">
    <link rel="stylesheet" href="<%=CommonRequest.GetStaticUrl(Request) %>static/v1/css/style.1545014209769.css" type="text/css" media="all">
    <link rel="stylesheet" href="<%=CommonRequest.GetStaticUrl(Request) %>static/v1/css/styles.css" type="text/css" media="all">

    <!-- 产品页面专用脚本 -->
    <script src="/static/js/autocdn.js" type="text/javascript"></script>
    <script type="text/javascript" src="<%=CommonRequest.GetStaticUrl(Request) %>static/v1/js/log.min.js"></script>
    <script type="text/javascript" src="<%=CommonRequest.GetStaticUrl(Request) %>static/v1/js/common_sdk.min.js"></script>
    <script src="//cdn.bootcdn.net/ajax/libs/jquery/1.4.2/jquery.min.js" type="text/javascript" onerror="autoRetry(this)"></script>
    <script type="text/javascript" src="<%=CommonRequest.GetStaticUrl(Request) %>static/v1/js/body.1509421810036.js"></script>
    <script type="text/javascript" src="<%=CommonRequest.GetStaticUrl(Request) %>static/v1/js/jquery.SuperSlide.2.1.1.js"></script>
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="server">
    <!-- 产品页面内容容器 -->
    <div class="product-page-wrapper" style="margin-top: 60px;">

        <!-- 产品横幅轮播 -->
        <div class="camscanner_banner index_card">
            <div class="bd">
                <ul style="position: relative; width: 100%; height: 400px;">
                    <li style="background-image: url('<%=CommonRequest.GetStaticUrl(Request) %>static/v1/image/bg3.jpg'); position: absolute; width: 100%; left: 0px; top: 0px; display: list-item;">
                        <div class="warp">
                        </div>
                    </li>
                    <li style="background-image: url('<%=CommonRequest.GetStaticUrl(Request) %>static/v1/image/banner1.png'); position: absolute; width: 100%; left: 0px; top: 0px; display: none;">
                        <div class="warp">
                        </div>
                    </li>
                    <li style="background-image: url('<%=CommonRequest.GetStaticUrl(Request) %>static/v1/image/camscanner_banner.png'); position: absolute; width: 100%; left: 0px; top: 0px; display: none;">
                        <div class="warp">
                        </div>
                    </li>
                </ul>
            </div>
            <div class="hd">
                <ul>
                    <li class="on">1</li>
                    <li class="">2</li>
                    <li class="">3</li>
                    <li class="">4</li>
                </ul>
            </div>
        </div>

        <header>
            <h1 class="product-title" style="position: absolute; width: 1px; height: 1px; padding: 0; margin: -1px; overflow: hidden; clip: rect(0, 0, 0, 0); white-space: nowrap; border: 0;">OCR助手智能文字识别一体化解决方案</h1>
        </header>

        <!-- 应用场景导航 -->
        <nav class="camscanner_menu" id="tip" aria-label="产品导航">
            <div class="warp">
                <ul class="fl" style="width: 650px;">
                    <li style="width: 25%; margin-right: 0;"><a class="a_anli" href="#scene-section">应用场景</a></li>
                    <li style="width: 25%; margin-right: 0;"><a class="a_gongneng" href="#feature-section">功能介绍</a></li>
                    <li style="width: 25%; margin-right: 0;"><a class="a_fangan" href="#review-section">用户评价</a></li>
                    <li style="width: 25%; margin-right: 0;"><a class="a_ccc" href="#media-section">媒体报道</a></li>
                </ul>
                <ul class="fr" style="height: 46px; margin-top: 8px;">
                    <li class="style" style="line-height: 40px; width: 100%;"><a href="<%=CommonRequest.GetDownLoadUrl(Request) %>" style="height: 40px; width: 100%;"><b>立即下载</b></a></li>
                </ul>
            </div>
        </nav>

        <!-- 应用场景介绍 -->
        <section id="scene-section" class="main warp a_anli_content">
            <div class="sales_x">
                <div class="biaoti">
                    <p class="tit mtit">电脑扫描仪，随时记录，轻松分享</p>
                    <p class="info minfo">"电脑上最好的100个软件之一"</p>
                </div>
                <dl class="d1">
                    <dd class="pic">
                        <img src="<%=CommonRequest.GetStaticUrl(Request) %>static/v1/picture/cam.001.png" alt="轻松处理各种场景"></dd>
                    <dt>轻松处理各种场景</dt>
                    <span class="info">办公一族非常需要，能非常方便的处理转换图文</span>
                </dl>
                <dl class="d1">
                    <dd class="pic">
                        <img src="<%=CommonRequest.GetStaticUrl(Request) %>static/v1/picture/cam.002.png" alt="自动图像识别预处理"></dd>
                    <dt>自动图像识别预处理</dt>
                    <span class="info">图片扫描生成自动锐化提亮，倾斜矫正</span>
                </dl>
                <dl class="d1 kkel">
                    <dd class="pic">
                        <img src="<%=CommonRequest.GetStaticUrl(Request) %>static/v1/picture/cam.003.png" alt="打字到手酸的同学们的神器"></dd>
                    <dt>打字到手酸的同学们的神器</dt>
                    <span class="info">OCR识别，图片瞬间变文本，告别打字打到手酸</span>
                </dl>
            </div>
        </section>

        <div class="clear"></div>

        <!-- 功能介绍 -->
        <main>
        <section id="feature-section" class="advantage a_gongneng_content">
            <div class="tu_a">
                <div class="warp">
                    <div class="ccb_tr">
                        <h2 class="ccb_rt_a">AI-智能OCR识别</h2>
                        <div class="ccb_rt_b">
                            <p>截图/拍照/文件，一键操作，深度加速<br>
                                支持识别100+语言，助力全球业务展开<br>
                                账号特权，云端存储，不限设备，不限次数。<br>
                                出差/异地，随时登录，随时使用！
                            </p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="tu_e">
                <div class="warp">
                    <div class="ccb_tr linx">
                        <h2 class="ccb_rt_a">
                            轻松处理各种文档
                        </h2>
                        <div class="ccb_rt_b">
                            <p>支持Office全家桶(Word,PPT,Excel等)<br>
                                支持PDF文档扫描及转换<br>
                                支持全文扫描，支持全文翻译<br>
                                各种办公文档智能解析，处理结果支持一键下载
                            </p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="tu_b">
                <div class="warp">
                    <div class="ccb_lr">
                        <div class="ccb_rt_a">云上OCR，轻享服务</div>
                        <div class="ccb_rt_b">
                            <span>依托于助手高性能服务器，用户只需要一个账号<br>
                                即可轻松享用各种大厂提供的服务<br>
                                众星捧月，只为让您提升工作生活效率！<br>
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 用户评价 -->
        <section id="review-section" class="advantage a_fangan_content">
            <div class="ying camying">
                <div class="user">
                    <h2 class="titx">用户评价</h2>
                    <div class="user_pj">
                        <div class="user_pj_a">
                            <div class="bd bd_x">
                                <div class="tempWrap" style="overflow: hidden; position: relative; width: 1021px">
                                    <ul style="width: 4084px; left: -1992.58px; position: relative; overflow: hidden; padding: 0px; margin: 0px;">
                                        <li style="float: left; width: 1021px;"><span>工作原因要经常扫描东西发邮件给客户，用的机会多。OCR助手自动识别，剪裁方便，关键能有多种格式互相转换，简直逆天哦哦哦，真心推荐！！</span></li>
                                        <li style="float: left; width: 1021px;"><span>审计人员尤其适合使用，图片处理之后效果灰常好，很清晰，值得推荐。就查账翻凭证的时候啊，就你懂的啊，就有了OCR助手超级方便呢～</span></li>
                                        <li style="float: left; width: 1021px;"><span>门诊每天的手写病历和处方可以用OCR助手，很容易的将图片转成数字化，并存储，在没有实现数字化系统的小门诊可以很容易的实现病例和处方的讨论！有不足但是很棒的软件！</span></li>
                                        <li style="float: left; width: 1021px;"><span>不得不说，对于学生党太好用了，书上的重点都可以OCR识别下来再排版打印，写论文到图书馆拍资料都是用的它，很喜欢。推荐给好多同学用~</span></li>
                                    </ul>
                                </div>
                            </div>

                            <div class="hd bd_xe">
                                <ul>
                                    <li class="">
                                        <span class="hd_ax">
                                            <img src="<%=CommonRequest.GetStaticUrl(Request) %>static/v1/picture/cam.005.png" alt="办公族"></span>
                                        <span class="hd_bx">
                                            <span class="hd_bx_a">John</span>
                                            <span class="hd_bx_b">办公族</span>
                                        </span>
                                    </li>
                                    <li class="">
                                        <span class="hd_ax">
                                            <img src="<%=CommonRequest.GetStaticUrl(Request) %>static/v1/picture/cam.006.png" alt="审计"></span>
                                        <span class="hd_bx">
                                            <span class="hd_bx_a">Abby</span>
                                            <span class="hd_bx_b">审计</span>
                                        </span>
                                    </li>
                                    <li class="on">
                                        <span class="hd_ax">
                                            <img src="<%=CommonRequest.GetStaticUrl(Request) %>static/v1/picture/cam.007.png" alt="医生"></span>
                                        <span class="hd_bx">
                                            <span class="hd_bx_a">Han</span>
                                            <span class="hd_bx_b">医生</span>
                                        </span>
                                    </li>
                                    <li class="">
                                        <span class="hd_ax">
                                            <img src="<%=CommonRequest.GetStaticUrl(Request) %>static/v1/picture/cam.008.png" alt="学生"></span>
                                        <span class="hd_bx">
                                            <span class="hd_bx_a">Mia</span>
                                            <span class="hd_bx_b">学生</span>
                                        </span>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 媒体报道 -->
        <section id="media-section" class="media warp camsmedia a_ccc_content" style="margin-top: 80px;">
            <h2 class="tit">媒体报道</h2>
            <div class="news_bd">
                <div class="slideBoxx">
                    <div class="bd">
                        <ul>
                            <li style="display: none;">
                                <span class="news_bd_a">
                                    <img src="<%=CommonRequest.GetStaticUrl(Request) %>static/v1/picture/c103.png" alt="电脑上最好的50个软件之一"></span>
                                <span class="news_bd_b"><a href="javascript:;?t=1703579806708">电脑上最好的50个软件之一</a></span>
                            </li>
                            <li style="display: none;">
                                <span class="news_bd_a">
                                    <img src="<%=CommonRequest.GetStaticUrl(Request) %>static/v1/picture/c102.png" alt="OCR助手，超级扫描仪"></span>
                                <span class="news_bd_b"><a href="javascript:;?t=1703579806708">OCR助手，超级扫描仪</a></span>
                            </li>
                            <li style="display: list-item;">
                                <span class="news_bd_a">
                                    <img src="<%=CommonRequest.GetStaticUrl(Request) %>static/v1/picture/c100.png" alt="上班族必备10款应用，OCR助手榜上有名"></span>
                                <span class="news_bd_b"><a href="javascript:;?t=1703579806708">上班族必备10款应用，OCR助手榜上有名</a></span>
                            </li>
                        </ul>
                    </div>
                    <div class="h36"></div>
                    <div class="hd">
                        <ul>
                            <li class=""></li>
                            <li class=""></li>
                            <li class="on"></li>
                        </ul>
                    </div>
                </div>
            </div>
        </section>
        </main>

        <br />
        <br />
        <br />
    </div>

    <!-- 产品页面专用样式 -->
    <style type="text/css">
        /* 产品页面样式命名空间，避免与Site.Master冲突 */
        .product-page-wrapper {
            position: relative;
            width: 100%;
        }

        .product-page-wrapper .mod-btn {
            display: inline-block;
            box-sizing: border-box;
            width: 140px;
            height: 40px;
            line-height: 40px;
            padding: 0;
            border: 1px solid transparent;
            border-radius: 3px;
            font-size: 15px;
            text-align: center;
            cursor: pointer;
            transition: border-color .2s, color .2s, background-color .2s;
        }

        .product-page-wrapper .mod-btn-blue {
            color: #fff;
            background-color: #007cfa;
        }

        .product-page-wrapper .mod-btn-blue:hover {
            background-color: #3396fb;
        }

        .product-page-wrapper .mod-btn-blue:focus {
            background-color: #0064ed;
        }

        /* 确保产品页面内容不受Site.Master样式影响 */
        .product-page-wrapper .camscanner_banner {
            position: relative;
            z-index: 1;
        }

        .product-page-wrapper .camscanner_menu {
            position: relative;
            z-index: 2;
        }

        /* 响应式调整 */
        @media (max-width: 768px) {
            .product-page-wrapper .warp {
                padding: 0 15px;
            }

            .product-page-wrapper .camscanner_menu ul.fl {
                width: 100% !important;
            }

            .product-page-wrapper .camscanner_menu ul.fl li {
                width: 50% !important;
                margin-bottom: 10px;
            }
        }
    </style>

    <!-- 产品页面JavaScript功能 -->
    <script type="text/javascript">
        // 确保jQuery加载完成后执行
        $(document).ready(function() {
            // 横幅轮播功能
            if (typeof jQuery !== 'undefined' && jQuery.fn.slide) {
                jQuery(".camscanner_banner").slide({
                    titCell: ".hd ul",
                    mainCell: ".bd ul",
                    effect: "fold",
                    autoPlay: true,
                    autoPage: true,
                    trigger: "click",
                    delayTime: 1000,
                    interTime: 5000
                });

                // 用户评价轮播
                jQuery(".user_pj_a").slide({
                    mainCell: ".bd ul",
                    effect: "left",
                    autoPlay: true
                });

                // 媒体报道轮播
                jQuery(".slideBoxx").slide({
                    mainCell: ".bd ul",
                    autoPlay: true
                });
            }

            // 平滑滚动到锚点
            $('.camscanner_menu a[href^="#"]').on('click', function(e) {
                e.preventDefault();
                var target = $(this.getAttribute('href'));
                if (target.length) {
                    $('html, body').stop().animate({
                        scrollTop: target.offset().top - 80
                    }, 1000);
                }
            });
        });
    </script>

    <!-- 加载必要的外部脚本 -->
    <script type="text/javascript" src="<%=CommonRequest.GetStaticUrl(Request) %>static/v1/js/wp-embed.min.js"></script>
    <script type="text/javascript" src="<%=CommonRequest.GetStaticUrl(Request) %>static/v1/js/common_new.js"></script>
</asp:Content>
