﻿<%@ Page Title="OCR助手产品介绍" Language="C#" MasterPageFile="~/Site.Master" AutoEventWireup="true" %>
<%@ Import Namespace="Account.Web" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="server">
    <meta name="description" content="OCR文字识别助手是一款集文字、表格、公式、文档及翻译于一体的智能生产力工具。支持100+语言识别，多平台兼容，全球云端存储，业内顶级识别速度。" />
    <meta name="keywords" content="免费OCR,免费文字识别,免费图片识别,免费图文识别,免费图片转文字,OCR识别,文字识别,图片识别,图片文字识别,图片转文字,图片转表格,图片转公式,文档识别,文档翻译" />
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=5.0" />
    <link rel="preconnect" href="https://lsw-fast.lenovo.com.cn" crossorigin />
    <script type="application/ld+json">{"@context":"http://schema.org","@type":"SoftwareApplication","name":"OCR Assistant","description":"OCR Assistant is an efficient productivity tool that integrates text, tables, formulas, documents, and translation.","category":"Productivity","applicationCategory":"Business","image":"https://lsw-fast.lenovo.com.cn/appstore/apps/adp/logo/7273-2024-06-05074650-1717588010656.gif","screenshot":["https://lsw-fast.lenovo.com.cn/appstore/apps/adp/img/3441-2023-07-18051430-1689671670795.png","https://lsw-fast.lenovo.com.cn/appstore/apps/adp/img/4112-2023-07-18051443-1689671683656.png","https://lsw-fast.lenovo.com.cn/appstore/apps/adp/img/0653-2023-07-18051453-1689671693673.png","https://lsw-fast.lenovo.com.cn/appstore/apps/adp/img/6000-2023-07-18051502-1689671702366.png"],"aggregateRating":{"@type":"AggregateRating","worstRating":"1","bestRating":"5","ratingValue":"4.8","ratingCount":"<%=((System.DateTime.Now.ToUniversalTime().Ticks - 621355968000000000) / 100000000000*3).ToString() %>"},"offers":{"@type":"Offer","price":0,"priceCurrency":"USD","category":"free"},"operatingSystem": "Windows"}</script>

    <!-- 加载必要的外部样式和脚本，但避免冲突 -->
    <link rel="stylesheet" type="text/css" href="<%=CommonRequest.GetStaticUrl(Request) %>static/css/main.css">
    <script type="text/javascript" src="https://cdn.jsdelivr.net/npm/jquery@3.6.0/jquery.min.js"></script>

    <!-- 产品页面样式覆盖，确保与Site.Master兼容 -->
    <style type="text/css">
        /* 重置产品页面容器，避免继承Site.Master样式 */
        .product-page-wrapper {
            font-family: "Microsoft YaHei", Arial, sans-serif;
            line-height: 1.5;
            color: #333;
        }

        .product-page-wrapper * {
            box-sizing: border-box;
        }

        /* 横幅轮播样式 - 恢复原有效果 */
        .product-page-wrapper .camscanner_banner {
            position: relative;
            width: 100%;
            height: 400px;
            overflow: hidden;
        }

        .product-page-wrapper .camscanner_banner .bd {
            position: relative;
            width: 100%;
            height: 100%;
        }

        .product-page-wrapper .camscanner_banner .bd ul {
            position: relative;
            width: 100%;
            height: 100%;
            margin: 0;
            padding: 0;
            list-style: none;
        }

        .product-page-wrapper .camscanner_banner .bd li {
            position: absolute;
            width: 100%;
            height: 100%;
            background-size: cover;
            background-position: center;
            background-repeat: no-repeat;
            display: none;
        }

        .product-page-wrapper .camscanner_banner .bd li:first-child {
            display: block;
        }

        .product-page-wrapper .camscanner_banner .hd {
            position: absolute;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            z-index: 10;
        }

        .product-page-wrapper .camscanner_banner .hd ul {
            display: flex;
            margin: 0;
            padding: 0;
            list-style: none;
        }

        .product-page-wrapper .camscanner_banner .hd li {
            width: 12px;
            height: 12px;
            background: rgba(255,255,255,0.5);
            margin: 0 5px;
            border-radius: 50%;
            cursor: pointer;
            transition: all 0.3s;
            text-indent: -9999px;
            overflow: hidden;
        }

        .product-page-wrapper .camscanner_banner .hd li.on,
        .product-page-wrapper .camscanner_banner .hd li:hover {
            background: #fff;
        }

        /* 导航菜单样式 - 恢复原有布局 */
        .product-page-wrapper .camscanner_menu {
            background: linear-gradient(135deg, #34495e 0%, #2c3e50 100%);
            padding: 0;
            position: relative;
            z-index: 100;
        }

        .product-page-wrapper .camscanner_menu .warp {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 15px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            min-height: 60px;
        }

        .product-page-wrapper .camscanner_menu .fl {
            display: flex;
            margin: 0;
            padding: 0;
            list-style: none;
        }

        .product-page-wrapper .camscanner_menu .fr {
            margin: 0;
            padding: 0;
            list-style: none;
        }

        .product-page-wrapper .camscanner_menu .fl li {
            margin-right: 30px;
        }

        .product-page-wrapper .camscanner_menu .fl a {
            display: block;
            padding: 15px 20px;
            color: rgba(255,255,255,0.8);
            text-decoration: none;
            font-size: 16px;
            transition: all 0.3s;
            border-radius: 4px;
        }

        .product-page-wrapper .camscanner_menu .fl a:hover {
            color: #fff;
            background: rgba(255,255,255,0.1);
        }

        .product-page-wrapper .camscanner_menu .fr .style a {
            background: #3498db;
            color: white;
            padding: 12px 25px;
            border-radius: 25px;
            font-weight: bold;
            text-decoration: none;
            transition: all 0.3s;
        }

        .product-page-wrapper .camscanner_menu .fr .style a:hover {
            background: #2980b9;
            transform: translateY(-2px);
        }

        /* 主要内容区域样式 */
        .product-page-wrapper .main {
            padding: 60px 0;
            background: #fff;
        }

        .product-page-wrapper .warp {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 15px;
        }

        .product-page-wrapper .biaoti {
            text-align: center;
            margin-bottom: 50px;
        }

        .product-page-wrapper .biaoti .tit {
            font-size: 32px;
            color: #333;
            margin-bottom: 15px;
            font-weight: bold;
        }

        .product-page-wrapper .biaoti .info {
            font-size: 16px;
            color: #666;
            line-height: 1.6;
        }

        /* 应用场景样式 - 恢复原有三栏布局 */
        .product-page-wrapper .sales_x {
            display: flex;
            justify-content: space-between;
            flex-wrap: wrap;
            gap: 30px;
        }

        .product-page-wrapper .d1 {
            flex: 1;
            min-width: 300px;
            text-align: center;
            padding: 40px 20px;
            border-radius: 8px;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        /* 应用场景卡片颜色 */
        .product-page-wrapper #scene-section .d1:nth-child(1) {
            background: linear-gradient(135deg, #34495e 0%, #2c3e50 100%);
            color: white;
        }

        .product-page-wrapper #scene-section .d1:nth-child(2) {
            background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
            color: white;
        }

        .product-page-wrapper #scene-section .d1:nth-child(3) {
            background: linear-gradient(135deg, #2ecc71 0%, #27ae60 100%);
            color: white;
        }

        .product-page-wrapper .d1:hover {
            transform: translateY(-10px);
            box-shadow: 0 15px 35px rgba(0,0,0,0.1);
        }

        .product-page-wrapper .d1 .pic {
            margin-bottom: 20px;
        }

        .product-page-wrapper .d1 .pic img {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            background: rgba(255,255,255,0.2);
            padding: 15px;
        }

        .product-page-wrapper .d1 dt {
            font-size: 20px;
            font-weight: bold;
            margin-bottom: 15px;
            color: inherit;
        }

        .product-page-wrapper .d1 .info {
            font-size: 14px;
            line-height: 1.6;
            opacity: 0.9;
            color: inherit;
        }

        /* 功能介绍样式 */
        .product-page-wrapper #feature-section {
            background: #f8f9fa;
        }

        .product-page-wrapper #feature-section .d1 {
            background: white;
            color: #333;
            border: 1px solid #e9ecef;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
        }

        .product-page-wrapper #feature-section .d1:hover {
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
        }

        /* 用户评价样式 */
        .product-page-wrapper #review-section .d1 {
            background: white;
            color: #333;
            border: 1px solid #e9ecef;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
        }

        /* 媒体报道样式 */
        .product-page-wrapper #media-section {
            background: #f8f9fa;
        }

        .product-page-wrapper #media-section .d1 {
            background: white;
            color: #333;
            border: 1px solid #e9ecef;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
        }

        /* 清除浮动 */
        .product-page-wrapper .clear {
            clear: both;
        }
    </style>
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="server">
    <!-- 产品页面内容容器 -->
    <div class="product-page-wrapper" style="margin-top: 60px;">

        <!-- 产品横幅轮播 - 恢复原有设计 -->
        <div class="camscanner_banner" id="banner">
            <div class="bd">
                <ul>
                    <li style="background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%); display: flex; align-items: center; justify-content: center;">
                        <div class="warp" style="display: flex; align-items: center; justify-content: center; color: white; text-align: center;">
                            <div style="display: flex; align-items: center; max-width: 1200px;">
                                <div style="width: 100px; height: 100px; background: rgba(52, 152, 219, 0.2); border-radius: 20px; margin-right: 40px; display: flex; align-items: center; justify-content: center; font-size: 48px; color: #3498db;">📱</div>
                                <div style="text-align: left;">
                                    <h1 style="font-size: 42px; margin: 0; font-weight: bold; text-shadow: 0 2px 4px rgba(0,0,0,0.3);">电脑扫描仪，随时记录，轻松分享</h1>
                                    <p style="font-size: 20px; margin: 15px 0 0 0; opacity: 0.9;">电脑上最好用100个软件之一</p>
                                </div>
                            </div>
                        </div>
                    </li>
                    <li style="background: linear-gradient(135deg, #34495e 0%, #2c3e50 100%); display: flex; align-items: center; justify-content: center;">
                        <div class="warp" style="display: flex; align-items: center; justify-content: center; color: white; text-align: center;">
                            <div style="display: flex; align-items: center; max-width: 1200px;">
                                <div style="width: 100px; height: 100px; background: rgba(46, 204, 113, 0.2); border-radius: 20px; margin-right: 40px; display: flex; align-items: center; justify-content: center; font-size: 48px; color: #2ecc71;">🤖</div>
                                <div style="text-align: left;">
                                    <h1 style="font-size: 42px; margin: 0; font-weight: bold; text-shadow: 0 2px 4px rgba(0,0,0,0.3);">AI智能识别，高效办公</h1>
                                    <p style="font-size: 20px; margin: 15px 0 0 0; opacity: 0.9;">支持多种文档格式智能识别</p>
                                </div>
                            </div>
                        </div>
                    </li>
                    <li style="background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%); display: flex; align-items: center; justify-content: center;">
                        <div class="warp" style="display: flex; align-items: center; justify-content: center; color: white; text-align: center;">
                            <div style="display: flex; align-items: center; max-width: 1200px;">
                                <div style="width: 100px; height: 100px; background: rgba(155, 89, 182, 0.2); border-radius: 20px; margin-right: 40px; display: flex; align-items: center; justify-content: center; font-size: 48px; color: #9b59b6;">☁️</div>
                                <div style="text-align: left;">
                                    <h1 style="font-size: 42px; margin: 0; font-weight: bold; text-shadow: 0 2px 4px rgba(0,0,0,0.3);">云端同步，随时随地访问</h1>
                                    <p style="font-size: 20px; margin: 15px 0 0 0; opacity: 0.9;">多平台同步，数据安全可靠</p>
                                </div>
                            </div>
                        </div>
                    </li>
                </ul>
            </div>
            <div class="hd">
                <ul>
                    <li class="on">1</li>
                    <li>2</li>
                    <li>3</li>
                </ul>
            </div>
        </div>

        <header>
            <h1 class="product-title" style="position: absolute; width: 1px; height: 1px; padding: 0; margin: -1px; overflow: hidden; clip: rect(0, 0, 0, 0); white-space: nowrap; border: 0;">OCR助手智能文字识别一体化解决方案</h1>
        </header>

        <!-- 应用场景导航 -->
        <nav class="camscanner_menu" id="tip" aria-label="产品导航">
            <div class="warp">
                <ul class="fl">
                    <li><a href="#scene-section">应用场景</a></li>
                    <li><a href="#feature-section">功能介绍</a></li>
                    <li><a href="#review-section">用户评价</a></li>
                    <li><a href="#media-section">媒体报道</a></li>
                </ul>
                <ul class="fr">
                    <li class="style"><a href="<%=CommonRequest.GetDownLoadUrl(Request) %>"><b>立即下载</b></a></li>
                </ul>
            </div>
        </nav>

        <!-- 应用场景介绍 -->
        <section id="scene-section" class="main warp">
            <div class="biaoti">
                <h2 class="tit">电脑扫描仪，随时记录，轻松分享</h2>
                <p class="info">"电脑上最好的100个软件之一"</p>
            </div>
            <div class="sales_x">
                <dl class="d1">
                    <dd class="pic">
                        <div style="width: 80px; height: 80px; background: rgba(255,255,255,0.15); border-radius: 50%; margin: 0 auto; display: flex; align-items: center; justify-content: center; font-size: 40px;">📱</div>
                    </dd>
                    <dt>轻松处理各种场景</dt>
                    <span class="info">办公一族非常需要，能非常方便的处理转换图文</span>
                </dl>
                <dl class="d1">
                    <dd class="pic">
                        <div style="width: 80px; height: 80px; background: rgba(255,255,255,0.15); border-radius: 50%; margin: 0 auto; display: flex; align-items: center; justify-content: center; font-size: 40px;">🔍</div>
                    </dd>
                    <dt>自动图像识别预处理</dt>
                    <span class="info">图片扫描生成自动锐化提亮，倾斜矫正</span>
                </dl>
                <dl class="d1">
                    <dd class="pic">
                        <div style="width: 80px; height: 80px; background: rgba(255,255,255,0.15); border-radius: 50%; margin: 0 auto; display: flex; align-items: center; justify-content: center; font-size: 40px;">✍️</div>
                    </dd>
                    <dt>打字到手酸的同学们的神器</dt>
                    <span class="info">OCR识别，图片瞬间变文本，告别打字打到手酸</span>
                </dl>
            </div>
        </section>

        <div class="clear"></div>

        <!-- 功能介绍 -->
        <section id="feature-section" class="main warp">
            <div class="biaoti">
                <h2 class="tit">核心功能特色</h2>
                <p class="info">全方位智能OCR解决方案</p>
            </div>
            <div class="sales_x">
                <dl class="d1">
                    <dd class="pic">
                        <div style="width: 80px; height: 80px; background: #34495e; border-radius: 50%; margin: 0 auto; display: flex; align-items: center; justify-content: center; font-size: 40px; color: white;">🤖</div>
                    </dd>
                    <dt>AI-智能OCR识别</dt>
                    <span class="info">截图/拍照/文件，一键操作，深度加速。支持识别100+语言，助力全球业务展开。账号特权，云端存储，不限设备，不限次数。</span>
                </dl>
                <dl class="d1">
                    <dd class="pic">
                        <div style="width: 80px; height: 80px; background: #3498db; border-radius: 50%; margin: 0 auto; display: flex; align-items: center; justify-content: center; font-size: 40px; color: white;">📄</div>
                    </dd>
                    <dt>轻松处理各种文档</dt>
                    <span class="info">支持Office全家桶(Word,PPT,Excel等)。支持PDF文档扫描及转换。支持全文扫描，支持全文翻译。各种办公文档智能解析，处理结果支持一键下载。</span>
                </dl>
                <dl class="d1">
                    <dd class="pic">
                        <div style="width: 80px; height: 80px; background: #2ecc71; border-radius: 50%; margin: 0 auto; display: flex; align-items: center; justify-content: center; font-size: 40px; color: white;">☁️</div>
                    </dd>
                    <dt>云上OCR，轻享服务</dt>
                    <span class="info">依托于助手高性能服务器，用户只需要一个账号即可轻松享用各种大厂提供的服务。众星捧月，只为让您提升工作生活效率！</span>
                </dl>
            </div>
        </section>

        <!-- 用户评价 - 参考原版设计 -->
        <section id="review-section" class="main warp">
            <div class="biaoti">
                <h2 class="tit">用户评价</h2>
                <p class="info">来自不同行业用户的真实反馈</p>
            </div>

            <!-- 用户评价轮播容器 -->
            <div class="review-carousel" style="background: white; border-radius: 15px; padding: 50px; box-shadow: 0 10px 40px rgba(0,0,0,0.08); position: relative; margin: 40px 0; border: 1px solid #e8ecf0;">
                <!-- 评价内容 -->
                <div class="review-content" style="text-align: center; margin-bottom: 40px;">
                    <div class="quote-icon" style="font-size: 60px; color: #3498db; margin-bottom: 25px; opacity: 0.3;">"</div>
                    <p class="review-text" style="font-size: 20px; line-height: 1.8; color: #2c3e50; max-width: 900px; margin: 0 auto; font-style: italic; font-weight: 300;">
                        门诊每天的手写病历和处方可以用OCR助手，很容易的将图片转成数字化，并存储，在没有实现数字化系统的小门诊可以很容易的实现病例和处方的讨论！
                    </p>
                </div>

                <!-- 用户信息 -->
                <div class="reviewer-profile" style="display: flex; justify-content: center; align-items: center; margin-bottom: 30px;">
                    <div class="current-reviewer" style="display: flex; align-items: center; gap: 20px;">
                        <div class="reviewer-avatar-main" style="width: 80px; height: 80px; border-radius: 50%; background: linear-gradient(135deg, #34495e 0%, #2c3e50 100%); display: flex; align-items: center; justify-content: center; font-size: 36px; color: white; box-shadow: 0 5px 15px rgba(52, 73, 94, 0.3);">
                            �‍⚕️
                        </div>
                        <div class="reviewer-details" style="text-align: left;">
                            <div class="reviewer-name-main" style="font-size: 18px; font-weight: bold; color: #2c3e50; margin-bottom: 5px;">Han</div>
                            <div class="reviewer-title-main" style="font-size: 14px; color: #7f8c8d;">医生</div>
                            <div class="reviewer-rating" style="color: #f39c12; font-size: 16px; margin-top: 5px;">★★★★★</div>
                        </div>
                    </div>
                </div>

                <!-- 其他用户头像 -->
                <div class="other-reviewers" style="display: flex; justify-content: center; align-items: center; gap: 15px; margin-bottom: 30px;">
                    <div class="reviewer-avatar-small" data-index="0" style="width: 50px; height: 50px; border-radius: 50%; background: linear-gradient(135deg, #34495e 0%, #2c3e50 100%); display: flex; align-items: center; justify-content: center; font-size: 20px; color: white; cursor: pointer; opacity: 0.6; transition: all 0.3s;">
                        👨‍💼
                    </div>
                    <div class="reviewer-avatar-small" data-index="1" style="width: 50px; height: 50px; border-radius: 50%; background: linear-gradient(135deg, #3498db 0%, #2980b9 100%); display: flex; align-items: center; justify-content: center; font-size: 20px; color: white; cursor: pointer; opacity: 0.6; transition: all 0.3s;">
                        👩‍💼
                    </div>
                    <div class="reviewer-avatar-small active" data-index="2" style="width: 50px; height: 50px; border-radius: 50%; background: linear-gradient(135deg, #2ecc71 0%, #27ae60 100%); display: flex; align-items: center; justify-content: center; font-size: 20px; color: white; cursor: pointer; opacity: 1; transform: scale(1.1); transition: all 0.3s;">
                        👨‍⚕️
                    </div>
                </div>

                <!-- 进度指示器 -->
                <div class="review-indicators" style="display: flex; justify-content: center; gap: 8px;">
                    <span class="indicator" data-index="0" style="width: 30px; height: 3px; background: #bdc3c7; border-radius: 2px; cursor: pointer; transition: all 0.3s;"></span>
                    <span class="indicator" data-index="1" style="width: 30px; height: 3px; background: #bdc3c7; border-radius: 2px; cursor: pointer; transition: all 0.3s;"></span>
                    <span class="indicator active" data-index="2" style="width: 30px; height: 3px; background: #3498db; border-radius: 2px; cursor: pointer; transition: all 0.3s;"></span>
                </div>
            </div>
        </section>

        <!-- 媒体报道 - 参考原版设计 -->
        <section id="media-section" class="main warp" style="background: #f8f9fa;">
            <div class="biaoti">
                <h2 class="tit">媒体报道</h2>
                <p class="info">权威媒体的认可与推荐</p>
            </div>

            <!-- 媒体报道轮播容器 -->
            <div class="media-carousel" style="background: white; border-radius: 15px; padding: 50px; box-shadow: 0 10px 40px rgba(0,0,0,0.08); position: relative; margin: 40px 0; border: 1px solid #e8ecf0;">
                <!-- 媒体报道卡片 -->
                <div class="media-card" style="text-align: center;">
                    <!-- 媒体logo -->
                    <div class="media-logo" style="margin-bottom: 30px;">
                        <div class="media-brand" style="display: inline-flex; align-items: center; gap: 15px; padding: 20px 40px; background: linear-gradient(135deg, #34495e 0%, #2c3e50 100%); border-radius: 12px; color: white; font-size: 18px; font-weight: 600; box-shadow: 0 5px 20px rgba(52, 73, 94, 0.2);">
                            <div style="width: 40px; height: 40px; background: rgba(255,255,255,0.2); border-radius: 8px; display: flex; align-items: center; justify-content: center; font-size: 20px;">📰</div>
                            <span class="media-name">新浪新闻</span>
                        </div>
                    </div>

                    <!-- 报道内容 -->
                    <div class="media-content" style="margin-bottom: 40px;">
                        <div class="quote-marks" style="display: flex; justify-content: space-between; align-items: flex-start; margin-bottom: 20px;">
                            <div style="font-size: 60px; color: #3498db; opacity: 0.3; line-height: 1;">"</div>
                            <div style="font-size: 60px; color: #3498db; opacity: 0.3; line-height: 1; transform: rotate(180deg);">"</div>
                        </div>
                        <h3 class="media-title" style="font-size: 24px; color: #2c3e50; margin: 0 auto; font-weight: 600; line-height: 1.4; max-width: 600px;">
                            上班族必备10款应用，OCR助手榜上有名
                        </h3>
                        <p class="media-description" style="font-size: 16px; color: #7f8c8d; margin-top: 15px; line-height: 1.6;">
                            权威媒体深度评测，OCR助手凭借卓越性能获得认可
                        </p>
                    </div>

                    <!-- 媒体切换按钮 -->
                    <div class="media-nav" style="display: flex; justify-content: center; gap: 20px; margin-bottom: 30px;">
                        <button class="media-btn" data-index="0" style="padding: 8px 16px; border: 2px solid #bdc3c7; background: white; border-radius: 20px; font-size: 12px; color: #7f8c8d; cursor: pointer; transition: all 0.3s; font-weight: 500;">TIME</button>
                        <button class="media-btn" data-index="1" style="padding: 8px 16px; border: 2px solid #bdc3c7; background: white; border-radius: 20px; font-size: 12px; color: #7f8c8d; cursor: pointer; transition: all 0.3s; font-weight: 500;">科技日报</button>
                        <button class="media-btn active" data-index="2" style="padding: 8px 16px; border: 2px solid #3498db; background: #3498db; border-radius: 20px; font-size: 12px; color: white; cursor: pointer; transition: all 0.3s; font-weight: 500;">新浪新闻</button>
                    </div>
                </div>

                <!-- 进度指示器 -->
                <div class="media-indicators" style="display: flex; justify-content: center; gap: 8px;">
                    <span class="indicator" data-index="0" style="width: 30px; height: 3px; background: #bdc3c7; border-radius: 2px; cursor: pointer; transition: all 0.3s;"></span>
                    <span class="indicator" data-index="1" style="width: 30px; height: 3px; background: #bdc3c7; border-radius: 2px; cursor: pointer; transition: all 0.3s;"></span>
                    <span class="indicator active" data-index="2" style="width: 30px; height: 3px; background: #3498db; border-radius: 2px; cursor: pointer; transition: all 0.3s;"></span>
                </div>
            </div>
        </section>

        <br />
        <br />
        <br />
    </div>

    <!-- 产品页面专用样式 -->
    <style type="text/css">
        /* 产品页面样式命名空间，避免与Site.Master冲突 */
        .product-page-wrapper {
            position: relative;
            width: 100%;
        }

        .product-page-wrapper .mod-btn {
            display: inline-block;
            box-sizing: border-box;
            width: 140px;
            height: 40px;
            line-height: 40px;
            padding: 0;
            border: 1px solid transparent;
            border-radius: 3px;
            font-size: 15px;
            text-align: center;
            cursor: pointer;
            transition: border-color .2s, color .2s, background-color .2s;
        }

        .product-page-wrapper .mod-btn-blue {
            color: #fff;
            background-color: #007cfa;
        }

        .product-page-wrapper .mod-btn-blue:hover {
            background-color: #3396fb;
        }

        .product-page-wrapper .mod-btn-blue:focus {
            background-color: #0064ed;
        }

        /* 确保产品页面内容不受Site.Master样式影响 */
        .product-page-wrapper .camscanner_banner {
            position: relative;
            z-index: 1;
        }

        .product-page-wrapper .camscanner_menu {
            position: relative;
            z-index: 2;
        }

        /* 响应式调整 */
        @media (max-width: 768px) {
            .product-page-wrapper .warp {
                padding: 0 15px;
            }

            .product-page-wrapper .camscanner_menu ul.fl {
                width: 100% !important;
            }

            .product-page-wrapper .camscanner_menu ul.fl li {
                width: 50% !important;
                margin-bottom: 10px;
            }
        }
    </style>

    <!-- 产品页面JavaScript功能 -->
    <script type="text/javascript">
        // 使用原生JavaScript实现功能，避免jQuery冲突
        document.addEventListener('DOMContentLoaded', function() {
            // 横幅轮播功能
            var banner = document.getElementById('banner');
            if (banner) {
                var slides = banner.querySelectorAll('.bd li');
                var dots = banner.querySelectorAll('.hd li');
                var currentSlide = 0;
                var slideInterval;

                // 显示指定幻灯片
                function showSlide(index) {
                    // 隐藏所有幻灯片
                    slides.forEach(function(slide) {
                        slide.style.display = 'none';
                    });

                    // 移除所有点的活动状态
                    dots.forEach(function(dot) {
                        dot.classList.remove('on');
                    });

                    // 显示当前幻灯片和激活对应的点
                    if (slides[index]) {
                        slides[index].style.display = 'flex';
                        dots[index].classList.add('on');
                        currentSlide = index;
                    }
                }

                // 下一张幻灯片
                function nextSlide() {
                    var next = (currentSlide + 1) % slides.length;
                    showSlide(next);
                }

                // 开始自动轮播
                function startSlideshow() {
                    slideInterval = setInterval(nextSlide, 4000);
                }

                // 停止自动轮播
                function stopSlideshow() {
                    clearInterval(slideInterval);
                }

                // 初始化轮播
                showSlide(0);
                startSlideshow();

                // 点击指示器切换幻灯片
                dots.forEach(function(dot, index) {
                    dot.addEventListener('click', function() {
                        stopSlideshow();
                        showSlide(index);
                        startSlideshow();
                    });
                });

                // 鼠标悬停时暂停轮播
                banner.addEventListener('mouseenter', stopSlideshow);
                banner.addEventListener('mouseleave', startSlideshow);
            }

            // 平滑滚动到锚点
            var menuLinks = document.querySelectorAll('.camscanner_menu a[href^="#"]');
            menuLinks.forEach(function(link) {
                link.addEventListener('click', function(e) {
                    e.preventDefault();
                    var targetId = this.getAttribute('href').substring(1);
                    var targetElement = document.getElementById(targetId);
                    if (targetElement) {
                        var offsetTop = targetElement.offsetTop - 80;
                        window.scrollTo({
                            top: offsetTop,
                            behavior: 'smooth'
                        });
                    }
                });
            });

            // 卡片悬停效果
            var cards = document.querySelectorAll('.product-page-wrapper .d1');
            cards.forEach(function(card) {
                card.addEventListener('mouseenter', function() {
                    if (!this.style.transform.includes('translateY')) {
                        this.style.transform = 'translateY(-10px)';
                    }
                });
                card.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0)';
                });
            });

            // 用户评价轮播功能
            var reviewData = [
                {
                    text: "工作原因要经常扫描东西发邮件给客户，用的机会多。OCR助手自动识别，剪裁方便，关键能有多种格式互相转换，简直逆天哦哦哦，真心推荐！！",
                    name: "John",
                    title: "办公族",
                    emoji: "👨‍💼",
                    gradient: "linear-gradient(135deg, #34495e 0%, #2c3e50 100%)"
                },
                {
                    text: "审计人员尤其适合使用，图片处理之后效果灰常好，很清晰，值得推荐。就查账翻凭证的时候啊，就你懂的啊，就有了OCR助手超级方便呢～",
                    name: "Abby",
                    title: "审计",
                    emoji: "👩‍💼",
                    gradient: "linear-gradient(135deg, #3498db 0%, #2980b9 100%)"
                },
                {
                    text: "门诊每天的手写病历和处方可以用OCR助手，很容易的将图片转成数字化，并存储，在没有实现数字化系统的小门诊可以很容易的实现病例和处方的讨论！",
                    name: "Han",
                    title: "医生",
                    emoji: "👨‍⚕️",
                    gradient: "linear-gradient(135deg, #2ecc71 0%, #27ae60 100%)"
                }
            ];

            var mediaData = [
                {
                    title: "电脑上最好的50个软件之一",
                    name: "TIME",
                    icon: "📰",
                    description: "国际权威媒体深度评测，OCR助手获得高度认可",
                    gradient: "linear-gradient(135deg, #34495e 0%, #2c3e50 100%)"
                },
                {
                    title: "OCR助手，超级扫描仪",
                    name: "科技日报",
                    icon: "📱",
                    description: "专业科技媒体评选，技术创新获得业界肯定",
                    gradient: "linear-gradient(135deg, #3498db 0%, #2980b9 100%)"
                },
                {
                    title: "上班族必备10款应用，OCR助手榜上有名",
                    name: "新浪新闻",
                    icon: "📰",
                    description: "权威媒体深度评测，OCR助手凭借卓越性能获得认可",
                    gradient: "linear-gradient(135deg, #2ecc71 0%, #27ae60 100%)"
                }
            ];

            // 初始化用户评价轮播
            var currentReview = 2; // 默认显示第3个（医生）
            var reviewText = document.querySelector('.review-text');
            var reviewAvatarMain = document.querySelector('.reviewer-avatar-main');
            var reviewNameMain = document.querySelector('.reviewer-name-main');
            var reviewTitleMain = document.querySelector('.reviewer-title-main');
            var reviewAvatarsSmall = document.querySelectorAll('.reviewer-avatar-small');
            var reviewIndicators = document.querySelectorAll('.review-indicators .indicator');

            function updateReview(index) {
                if (reviewText && reviewData[index]) {
                    // 更新评价内容
                    reviewText.textContent = reviewData[index].text;

                    // 更新主要用户信息
                    if (reviewAvatarMain) {
                        reviewAvatarMain.textContent = reviewData[index].emoji;
                        reviewAvatarMain.style.background = reviewData[index].gradient;
                    }
                    if (reviewNameMain) reviewNameMain.textContent = reviewData[index].name;
                    if (reviewTitleMain) reviewTitleMain.textContent = reviewData[index].title;

                    // 更新小头像状态
                    reviewAvatarsSmall.forEach(function(avatar, i) {
                        if (i === index) {
                            avatar.style.opacity = '1';
                            avatar.style.transform = 'scale(1.1)';
                            avatar.classList.add('active');
                        } else {
                            avatar.style.opacity = '0.6';
                            avatar.style.transform = 'scale(1)';
                            avatar.classList.remove('active');
                        }
                    });

                    // 更新指示器
                    reviewIndicators.forEach(function(indicator, i) {
                        if (i === index) {
                            indicator.style.background = '#3498db';
                            indicator.classList.add('active');
                        } else {
                            indicator.style.background = '#bdc3c7';
                            indicator.classList.remove('active');
                        }
                    });
                }
            }

            // 添加点击事件
            reviewAvatarsSmall.forEach(function(avatar, index) {
                avatar.addEventListener('click', function() {
                    currentReview = index;
                    updateReview(index);
                });
            });

            reviewIndicators.forEach(function(indicator, index) {
                indicator.addEventListener('click', function() {
                    currentReview = index;
                    updateReview(index);
                });
            });

            // 初始化媒体报道轮播
            var currentMedia = 2; // 默认显示新浪新闻
            var mediaTitle = document.querySelector('.media-title');
            var mediaName = document.querySelector('.media-name');
            var mediaDescription = document.querySelector('.media-description');
            var mediaBrand = document.querySelector('.media-brand');
            var mediaButtons = document.querySelectorAll('.media-btn');
            var mediaIndicators = document.querySelectorAll('.media-indicators .indicator');

            function updateMedia(index) {
                if (mediaTitle && mediaData[index]) {
                    // 更新内容
                    mediaTitle.textContent = mediaData[index].title;
                    if (mediaName) mediaName.textContent = mediaData[index].name;
                    if (mediaDescription) mediaDescription.textContent = mediaData[index].description;

                    // 更新品牌样式
                    if (mediaBrand) {
                        mediaBrand.style.background = mediaData[index].gradient;
                        var icon = mediaBrand.querySelector('div');
                        if (icon) icon.textContent = mediaData[index].icon;
                    }

                    // 更新按钮状态
                    mediaButtons.forEach(function(btn, i) {
                        if (i === index) {
                            btn.style.borderColor = '#3498db';
                            btn.style.background = '#3498db';
                            btn.style.color = 'white';
                            btn.classList.add('active');
                        } else {
                            btn.style.borderColor = '#bdc3c7';
                            btn.style.background = 'white';
                            btn.style.color = '#7f8c8d';
                            btn.classList.remove('active');
                        }
                    });

                    // 更新指示器
                    mediaIndicators.forEach(function(indicator, i) {
                        if (i === index) {
                            indicator.style.background = '#3498db';
                            indicator.classList.add('active');
                        } else {
                            indicator.style.background = '#bdc3c7';
                            indicator.classList.remove('active');
                        }
                    });
                }
            }

            // 添加点击事件
            mediaButtons.forEach(function(btn, index) {
                btn.addEventListener('click', function() {
                    currentMedia = index;
                    updateMedia(index);
                });
            });

            mediaIndicators.forEach(function(indicator, index) {
                indicator.addEventListener('click', function() {
                    currentMedia = index;
                    updateMedia(index);
                });
            });

            // 自动轮播用户评价
            setInterval(function() {
                currentReview = (currentReview + 1) % reviewData.length;
                updateReview(currentReview);
            }, 5000);

            // 自动轮播媒体报道
            setInterval(function() {
                currentMedia = (currentMedia + 1) % mediaData.length;
                updateMedia(currentMedia);
            }, 4000);

            // 滚动时的导航高亮效果
            window.addEventListener('scroll', function() {
                var scrollPos = window.scrollY + 100;
                var sections = document.querySelectorAll('section[id]');
                var navLinks = document.querySelectorAll('.camscanner_menu a[href^="#"]');

                sections.forEach(function(section) {
                    var sectionTop = section.offsetTop;
                    var sectionHeight = section.offsetHeight;
                    var sectionId = section.getAttribute('id');

                    if (scrollPos >= sectionTop && scrollPos < sectionTop + sectionHeight) {
                        navLinks.forEach(function(link) {
                            link.classList.remove('active');
                            if (link.getAttribute('href') === '#' + sectionId) {
                                link.classList.add('active');
                            }
                        });
                    }
                });
            });
        });
    </script>
</asp:Content>
