<%@ Page Title="OCR助手产品介绍" Language="C#" MasterPageFile="~/Site.Master" AutoEventWireup="true" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="server">
    <meta name="description" content="OCR助手产品详细介绍，了解文字识别、公式识别、表格提取、多语言翻译等全场景智能OCR功能。支持桌面和网页版，多个引擎选择，高识别率。" />
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=5.0" />
    <link rel="preconnect" href="https://lsw-fast.lenovo.com.cn" crossorigin />
    <script type="application/ld+json">{"@context":"http://schema.org","@type":"SoftwareApplication","name":"OCR Assistant","description":"OCR Assistant is an efficient productivity tool that integrates text, tables, formulas, documents, and translation.","category":"Productivity","applicationCategory":"Business","image":"https://lsw-fast.lenovo.com.cn/appstore/apps/adp/logo/7273-2024-06-05074650-1717588010656.gif","screenshot":["https://lsw-fast.lenovo.com.cn/appstore/apps/adp/img/3441-2023-07-18051430-1689671670795.png","https://lsw-fast.lenovo.com.cn/appstore/apps/adp/img/4112-2023-07-18051443-1689671683656.png","https://lsw-fast.lenovo.com.cn/appstore/apps/adp/img/0653-2023-07-18051453-1689671693673.png","https://lsw-fast.lenovo.com.cn/appstore/apps/adp/img/6000-2023-07-18051502-1689671702366.png"],"aggregateRating":{"@type":"AggregateRating","worstRating":"1","bestRating":"5","ratingValue":"4.8","ratingCount":"<%=((System.DateTime.Now.ToUniversalTime().Ticks - 621355968000000000) / 100000000000*3).ToString() %>"},"offers":{"@type":"Offer","price":0,"priceCurrency":"USD","category":"free"},"operatingSystem": "Windows"}</script>
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="server">
    <iframe id="frm" onload="this.height=this.contentWindow.document.body.scrollHeight+50" width="100%" height="650px" style="margin-top: 60px" frameborder="no" border="0" marginwidth="0" marginheight="0" scrolling="yes" allowtransparency="yes"></iframe>
    <script type="text/javascript">
        function getCurrentLanguage() {
            var pathParts = window.location.pathname.split('/');
            if (pathParts.length > 1 && pathParts[1] && pathParts[1].length > 0) {
                var possibleLang = pathParts[1];
                if (possibleLang.length >= 2 && possibleLang.indexOf('.') === -1) {
                    return possibleLang;
                }
            }
            return "zh-Hans";
        }
        document.getElementById('frm').src = "/" + getCurrentLanguage() + "/Product.aspx";
        var iFrames = document.getElementsByTagName('iframe');
        function iResize() {
            for (var i = 0, j = iFrames.length; i < j; i++) {
                var bHeight = iFrames[i].contentWindow && iFrames[i].contentWindow.document && iFrames[i].contentWindow.document.body ? iFrames[i].contentWindow.document.body.scrollHeight : 0;
                var dHeight = iFrames[i].contentWindow && iFrames[i].contentWindow.document && iFrames[i].contentWindow.document.documentElement ? iFrames[i].contentWindow.document.documentElement.scrollHeight : 0;
                var cHeight = iFrames[i].document && iFrames[i].document.documentElement ? iFrames[i].document.documentElement.scrollHeight : 0;
                var dHeight = window.innerHeight - 100;
                iFrames[i].style.height = Math.max(Math.max(Math.max(bHeight, dHeight), cHeight), dHeight) + 'px';
            }
        }
        window.setInterval("iResize()", 200);
    </script>
</asp:Content>
