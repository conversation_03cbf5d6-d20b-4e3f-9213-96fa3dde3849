﻿using CommonLib;
using System;
using System.Collections.Generic;
using System.Collections.Specialized;
using System.IO;

namespace MathOcr
{
    /// <summary>
    /// https://ai.baidu.com/ai-doc/OCR/Ok3h7xxva
    /// </summary>
    public class BaiDuAILiteAppRec : BaseMathRec
    {
        public BaiDuAILiteAppRec()
        {
            OcrGroup = OcrGroupType.百度;
            OcrType = MathOcrType.百度AI;
            MaxExecPerTime = 30;

            LstJsonPreProcessArray = new List<object>() { "words_result" };
            StrResultJsonSpilt = "words";
            //IsSupportVertical = true;
            IsSupportUrlOcr = true;
        }

        protected override string GetHtml(OcrContent content)
        {
            return PostFileResult(content.strBase64, null);
        }

        public override string GetHtmlByUrl(OcrContent content)
        {
            return PostFileResult(null, content.url);
        }

        public override void Reset()
        {
            strToken = "";
            base.Reset();
        }

        private string PostFileResult(string strBase64, string imgUrl, int retryCount = 0)
        {
            var result = "";
            InitToken();
            if (!string.IsNullOrEmpty(strToken))
            {
                try
                {
                    var url = "https://ai.baidu.com/weapp/rest/2.0/ocr/v1/formula";
                    List<UploadFileInfo> lstFile = new List<UploadFileInfo>();
                    if (string.IsNullOrEmpty(imgUrl))
                    {
                        var file = new UploadFileInfo()
                        {
                            Name = "image",
                            Filename = "test.png",
                            ContentType = "image/png",
                            Stream = new MemoryStream(Convert.FromBase64String(strBase64))
                        };
                        lstFile.Add(file);
                    }
                    var values = new NameValueCollection() {
                    { "vertexes_location","true"},
                    { "detect_direction","true"},
                    { "url",imgUrl},
                };
                    var headers = new NameValueCollection()
                {
                    { "sessionkey",strToken}
                };
                    result = PostFile(url, lstFile, values, headers);
                    if (result?.Contains("\"data\":\"未登录状态禁止使用\"") == true)
                    {
                        strToken = "";
                        InitToken();
                        if (retryCount < 3 && !string.IsNullOrEmpty(strToken))
                        {
                            retryCount++;
                            return PostFileResult(strBase64, imgUrl, retryCount);
                        }
                    }
                }
                catch { }
            }
            return result;
        }

        private const string strTokenSpilt = "\"sessionKey\":\"";

        private string strToken = "";

        private void InitToken()
        {
            if (string.IsNullOrEmpty(strToken))
            {
                strToken = GetToken();
            }
        }
        private string GetToken()
        {
            var result = "";
            var token = WebClientSyncExt.GetHtml("https://ai.baidu.com/weapp/login?code=" + Guid.NewGuid().ToString(), ExecTimeOutSeconds);

            if (!string.IsNullOrEmpty(token) && token.Contains(strTokenSpilt))
            {
                result = token.Substring(token.IndexOf(strTokenSpilt) + strTokenSpilt.Length);
                result = result.Substring(0, result.IndexOf("\""));
            }
            return result;
        }


    }
}