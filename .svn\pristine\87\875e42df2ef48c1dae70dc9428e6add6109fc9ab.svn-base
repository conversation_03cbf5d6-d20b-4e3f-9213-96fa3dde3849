﻿using CommonLib;
using log4net;
using System;
using System.Text;

namespace Code.Client.Web
{
    public static class SubOrder
    {
        public static string SubmitOrder(string strContext, string strCookie, string strIP, int type = 0)
        {
            var result = "";
            if (!string.IsNullOrEmpty(strContext) && !string.IsNullOrEmpty(strCookie) && !string.IsNullOrEmpty(strIP))
            {
                try
                {
                    strContext = GetBase64Result(strContext);
                    strCookie = GetBase64Result(strCookie);
                    switch (type)
                    {
                        case 0:
                            result =
                                WebClientSyncExt.GetHtml(
                                    "https://kyfw.12306.cn/otn/confirmPassenger/confirmSingleForQueueAsys?module=kjgp",
                                    strCookie, strContext, 1, 3);
                            break;
                        case 1:
                            result =
                                WebClientSyncExt.GetHtml(
                                    "https://kyfw.12306.cn/otn/confirmPassenger/confirmSingle?module=kjgp",
                                    strCookie, strContext, 1, 3);
                            break;
                        case 2:
                            result =
                                WebClientSyncExt.GetHtml(
                                    "https://kyfw.12306.cn/otn/confirmPassenger/confirmSingleForQueue?module=cmgp",
                                    strCookie,
                                    strContext, 1, 3);
                            break;
                        default:
                            break;
                    }
                }
                catch (Exception oe)
                {
                    LogManager.GetLogger("Order").Error("【下单】请求出错！", oe);
                }
            }
            return result;
        }

        private static string GetBase64Result(string strOld)
        {
            var result = "";
            try
            {
                var bytes = Convert.FromBase64String(strOld.Replace("%2B", "+"));
                result = Encoding.UTF8.GetString(bytes);
            }
            catch (Exception oe)
            {
                LogManager.GetLogger("Order").Error("【图片】反解析出错！", oe);
            }
            return result;
        }
    }
}