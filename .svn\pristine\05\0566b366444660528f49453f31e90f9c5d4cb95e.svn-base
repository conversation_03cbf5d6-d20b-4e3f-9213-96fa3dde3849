﻿using CommonLib;
using System;
using System.Collections.Generic;
using System.IO;

namespace HanZiOcr
{
    /// <summary>
    /// VIVO小程序-AI文档扫码
    /// ViVo手写
    /// </summary>
    public class VivoAIRec : BaseOcrRec
    {
        public VivoAIRec()
        {
            OcrGroup = OcrGroupType.VIVO;
            OcrType = HanZiOcrType.VivoAI;
            MaxExecPerTime = 23;

            IsProcessJsonResultByArray = true;
            IsSupportVertical = true;
            LstJsonPreProcessArray = new System.Collections.Generic.List<object>() { "result", "ocr" };
            StrResultJsonSpilt = "words";
            LstVerticalLocation = new List<object>() { "location" };
        }

        protected override string GetHtml(OcrContent content)
        {
            var byt = Convert.FromBase64String(content.strBase64);
            var result = PostFileResult(byt);
            return result;
        }

        private string PostFileResult(byte[] content)
        {
            var result = "";
            try
            {
                var url = "https://ai-text-recg.vivo.com.cn/ai_lab/handwriting_recg";
                var file = new UploadFileInfo()
                {
                    Name = "image",
                    Filename = "test.jpg",
                    ContentType = "image/jpg",
                    Stream = new MemoryStream(content)
                };
                result = PostFile(url, new[] { file }, null);
            }
            catch (Exception)
            {

            }
            return result;
        }

        protected override LocationInfo GetLocationByStr(string locationInfoStr)
        {
            LocationInfo location = null;
            if (!string.IsNullOrEmpty(locationInfoStr))
            {
                var loc = ConstHelper.JavaScriptSerializer.Deserialize<VivoLoction>(locationInfoStr);
                if (loc != null)
                {
                    location = new LocationInfo()
                    {
                        top = Math.Min(loc.top_left.y, loc.top_right.y),
                        left = Math.Min(loc.top_left.x, loc.down_left.x),
                        width = Math.Max(loc.top_right.x - loc.top_left.x, loc.down_right.x - loc.down_left.x),
                        height = Math.Max(loc.down_left.y - loc.top_left.y, loc.down_right.y - loc.top_right.y),
                    };
                }
            }

            return location;
        }

        class VivoLocPoint
        {
            /// <summary>
            /// 
            /// </summary>
            public double x { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public double y { get; set; }
        }

        class VivoLoction
        {
            /// <summary>
            /// 
            /// </summary>
            public VivoLocPoint down_left { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public VivoLocPoint down_right { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public VivoLocPoint top_left { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public VivoLocPoint top_right { get; set; }
        }

    }
}