<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Default"
         xmlns='http://schemas.microsoft.com/developer/msbuild/2003' ToolsVersion="4.0">

  <PropertyGroup>
    <BuildSolutionDir>$(MSBuildProjectDirectory)/..</BuildSolutionDir>
    <SrcDir>$(BuildSolutionDir)/src</SrcDir>
    <Configuration Condition="$(Configuration) == ''">Release</Configuration>
    <NuGetPackageDir>$(BuildSolutionDir)/NuGet/</NuGetPackageDir>
  </PropertyGroup>

  <PropertyGroup>
    <DoBuildSolutionsDependsOn>
      BeforeBuildSolutions;
      BuildSolutions
    </DoBuildSolutionsDependsOn>
  </PropertyGroup>

  <Target Name="BeforeBuildSolutions">
    <Message Text="*****Before building solution*****" Importance="high"/>
  </Target>

  <Target Name="BuildSolutions">
    <ItemGroup>
      <NugetPackageFilesToDelete Include="$(NuGetPackageDir)/*.nupkg"/>
    </ItemGroup>
    <Delete Files="@(NugetPackageFilesToDelete)" />
    <MSBuild Projects="$(BuildSolutionDir)/src/ServiceStack.sln" Targets="Restore" />
  </Target>

  <Target Name="Default" DependsOnTargets="$(DoBuildSolutionsDependsOn)">

    <!-- ServiceStack -->
    <MSBuild Projects="$(SrcDir)/ServiceStack/ServiceStack.csproj"
             Targets="Build;Pack"
             Properties="Configuration=$(Configuration)" />

    <!-- ServiceStack.Api.OpenApi -->
    <MSBuild Projects="$(BuildSolutionDir)/src/ServiceStack.Api.OpenApi/ServiceStack.Api.OpenApi.csproj" Targets="Restore" />
    <MSBuild Projects="$(BuildSolutionDir)/src/ServiceStack.Api.OpenApi/ServiceStack.Api.OpenApi.csproj"
             Targets="Build;Pack"
             Properties="Configuration=$(Configuration)" />

    <!-- ServiceStack.Client -->
    <MSBuild Projects="$(SrcDir)/ServiceStack.Client/ServiceStack.Client.csproj"
             Targets="Build;Pack"
             Properties="Configuration=$(Configuration)" />

    <!-- ServiceStack.Common -->
    <MSBuild Projects="$(SrcDir)/ServiceStack.Common/ServiceStack.Common.csproj"
             Targets="Build;Pack"
             Properties="Configuration=$(Configuration)" />

    <!-- ServiceStack.HttpClient -->
    <MSBuild Projects="$(SrcDir)/ServiceStack.HttpClient/ServiceStack.HttpClient.csproj"
             Targets="Build;Pack"
             Properties="Configuration=$(Configuration)" />

    <!-- ServiceStack.GrpcClient -->
    <MSBuild Projects="$(SrcDir)/ServiceStack.GrpcClient/ServiceStack.GrpcClient.csproj"
             Targets="Build;Pack"
             Properties="Configuration=$(Configuration)" />

    <!-- ServiceStack.Interfaces -->
    <MSBuild Projects="$(SrcDir)/ServiceStack.Interfaces/ServiceStack.Interfaces.csproj"
             Targets="Build;Pack"
             Properties="Configuration=$(Configuration)" />

    <!-- ServiceStack.Kestrel -->
    <MSBuild Projects="$(SrcDir)/ServiceStack.Kestrel/ServiceStack.Kestrel.csproj"
             Targets="Build;Pack"
             Properties="Configuration=$(Configuration)" />

    <!-- ServiceStack.Mvc -->
    <MSBuild Projects="$(SrcDir)/ServiceStack.Mvc/ServiceStack.Mvc.csproj"
             Targets="Build;Pack"
             Properties="Configuration=$(Configuration)" />

    <!-- ServiceStack.NetFramework -->
    <MSBuild Projects="$(SrcDir)/ServiceStack.NetFramework/ServiceStack.NetFramework.csproj"
             Targets="Build;Pack"
             Properties="Configuration=$(Configuration)" />

    <!-- ServiceStack.RabbitMq -->
    <MSBuild Projects="$(SrcDir)/ServiceStack.RabbitMq/ServiceStack.RabbitMq.csproj"
             Targets="Build;Pack"
             Properties="Configuration=$(Configuration)" />

    <!-- ServiceStack.Razor -->
    <MSBuild Projects="$(SrcDir)/ServiceStack.Razor/ServiceStack.Razor.csproj"
             Targets="Build;Pack"
             Properties="Configuration=$(Configuration)" />

    <!-- ServiceStack.Server -->
    <MSBuild Projects="$(SrcDir)/ServiceStack.Server/ServiceStack.Server.csproj"
             Targets="Build;Pack"
             Properties="Configuration=$(Configuration)" />

    <!-- ServiceStack.Extensions -->
    <MSBuild Projects="$(SrcDir)/ServiceStack.Extensions/ServiceStack.Extensions.csproj"
             Targets="Build;Pack"
             Properties="Configuration=$(Configuration)" />

    <!-- AuthRepos -->
    <!-- ServiceStack.Authentication.MongoDb -->
    <MSBuild Projects="$(SrcDir)/ServiceStack.Authentication.MongoDb/ServiceStack.Authentication.MongoDb.csproj"
             Targets="Build;Pack"
             Properties="Configuration=$(Configuration)" />

    <!-- ServiceStack.Authentication.RavenDb -->
    <MSBuild Projects="$(SrcDir)/ServiceStack.Authentication.RavenDb/ServiceStack.Authentication.RavenDb.csproj"
             Targets="Build;Pack"
             Properties="Configuration=$(Configuration)" />

    <!-- Caching -->
    <!-- ServiceStack.Caching.Memcached -->
    <MSBuild Projects="$(SrcDir)/ServiceStack.Caching.Memcached/ServiceStack.Caching.Memcached.csproj"
             Targets="Build;Pack"
             Properties="Configuration=$(Configuration)" />

    <!-- Formats -->
    <!-- ServiceStack.MsgPack -->
    <MSBuild Projects="$(SrcDir)/ServiceStack.MsgPack/ServiceStack.MsgPack.csproj"
             Targets="Build;Pack"
             Properties="Configuration=$(Configuration)" />

    <!-- ServiceStack.ProtoBuf -->
    <MSBuild Projects="$(SrcDir)/ServiceStack.ProtoBuf/ServiceStack.ProtoBuf.csproj"
             Targets="Build;Pack"
             Properties="Configuration=$(Configuration)" />

    <!-- ServiceStack.Desktop -->
    <MSBuild Projects="$(SrcDir)/ServiceStack.Desktop/ServiceStack.Desktop.csproj"
             Targets="Build;Pack"
             Properties="Configuration=$(Configuration)" />


    <!-- Copy all *.nupkg to /NuGet -->
    <ItemGroup>
      <NugetPackagesToMove Include="$(BuildSolutionDir)/src/**/bin/$(Configuration)/*.nupkg"/>
    </ItemGroup>
    <Move SourceFiles="@(NugetPackagesToMove)" DestinationFolder="$(NuGetPackageDir)" />
  </Target>

</Project>
