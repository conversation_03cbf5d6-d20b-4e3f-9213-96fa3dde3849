﻿using CommonLib;
using System;
using System.Collections.Generic;
using System.Collections.Specialized;
using System.IO;

namespace TableOcr
{
    /// <summary>
    /// http://developer.hanvon.com/table/toAllTable.do
    /// </summary>
    public class HanWangRec : BaseTableRec
    {
        public HanWangRec()
        {
            OcrGroup = OcrGroupType.汉王;
            OcrType = TableOcrType.汉王通用;
            MaxExecPerTime = 23;
            //IsSupportUrlOcr = true;

            LstJsonPreProcessArray = new List<object>() { "data", "elems", "type=2" };
            LstJsonNextProcessArray = new List<object> { "cells" };
            LstJsonResultProcessArray = new List<object>() { "lines", 0, "text" };
            LstRowIndex = new List<object>() { "rowstart", "rowend" };
            LstColumnIndex = new List<object>() { "colstart", "colend" };
            IsSupportUrlOcr = true;
        }

        protected override string GetHtml(OcrContent content)
        {
            return RequestHtmlContent(content.strBase64);
        }

        public override string GetHtmlByUrl(OcrContent content)
        {
            return RequestHtmlContent(null, content.url);
        }

        private string RequestHtmlContent(string strBase64, string imgUrl = null)
        {
            var strPost = "{\"image\":\"" + (string.IsNullOrEmpty(imgUrl) ? strBase64 : imgUrl) + "\"}";
            var url = "http://api.hanvon.com/hc/v2/ocr/table";
            var header = new NameValueCollection() {
                { "Token",HanWangHelper.GetToken()}
            };
            var strTmp = WebClientSyncExt.GetHtml(url, strPost, ExecTimeOutSeconds, header);

            return strTmp;
        }

    }
}