﻿using CommonLib;
using System;
using System.Collections.Generic;
using System.Collections.Specialized;

namespace DocOcr
{
    /// <summary>
    /// https://ocr.wdku.net/
    /// </summary>
    public class WdkuRec : BaseDocOcrRec
    {
        public WdkuRec()
        {
            OcrType = DocOcrType.Wdku;
            MaxExecPerTime = 20;
            ResultType = ResutypeEnum.网页;
        }

        private const string StrIdSpilt = "\"id\":\"";
        private const string StrTimeSpilt = "\"time\":";
        private const string StrUrlSpilt = "\"url\":\"";
        private static string GetUrl(string fileExt, out string id, out string time)
        {
            var result = string.Empty;
            id = string.Empty;
            time = string.Empty;
            var html = WebClientSyncExt.GetHtml("https://ocr.wdku.net/upload_presigneturl?ft=jpg&name=1." + fileExt);
            if (html.Contains(StrUrlSpilt))
            {
                result = CommonHelper.SubString(html, StrUrlSpilt, "\"").Replace("\\/", "/");
                id = CommonHelper.SubString(html, StrIdSpilt, "\"");
                time = CommonHelper.SubString(html, StrTimeSpilt, "}");
            }

            return result;
        }
        private static string SubUrl(ref string id, ref string time)
        {
            var strPost =
                "{\"ids\":\"" + id + "\",\"ts\":" + time
                + ",\"innerformat\":\"docx_accurate\",\"pass\":\"\",\"lang\":\"1,2\",\"autorotation\":\"1\",\"combine\":\"1\",\"a4\":\"0\",\"color\":\"1\",\"custom\":\"\",\"special\":0,\"replace\":\"\",\"txt_charset\":\"utf-8\",\"force\":\"\"}";
            var html = WebClientSyncExt.GetHtml("https://ocr.wdku.net/submitOcr?type=free", "", strPost, 1, 10);
            id = CommonHelper.SubString(html, StrIdSpilt, "\"");
            time = CommonHelper.SubString(html, StrTimeSpilt, "}");
            return html;
        }

        protected override string GetHtml(OcrContent content)
        {
            var result = string.Empty;
            var imgUrl = GetUrl(content.fileExt, out var id, out var time);
            if (!string.IsNullOrEmpty(imgUrl))
            {
                var html = WebClientSyncExt.GetHtml(imgUrl, content.strBase64, 5
                    , new NameValueCollection
                    {
                        { "Content-Type", "image/png" },
                    });
                result = SubUrl(ref id, ref time);
                if (!result.Contains("success"))
                {
                    result = string.Empty;
                }
            }
            return result;
        }

        protected override ResultEntity GetProcessText(OcrContent content, string html)
        {
            var id = CommonHelper.SubString(html, StrIdSpilt, "\"");
            var time = CommonHelper.SubString(html, StrTimeSpilt, "}");

            var downUrl = string.Format("https://ocr.wdku.net/downResultTransferLocal?service=ocr&id={0}&t={1}&type=title", id, time);

            var entity = GetFileResult(downUrl);

            entity.viewUrl = downUrl;
            var file = new DownLoadInfo()
            {
                fileType = OcrFileType.Xls,
                desc = "Wdku-XLS",
                url = downUrl,
            };
            entity.files.Add(file);
            //entity.downloadHtml = ConstHelper.GetDownLoadHtml(entity, OcrType.GetHashCode(), true);
            return entity;
        }

        private string GetStatus(string taskId)
        {
            var result = "";
            try
            {
                var id = CommonHelper.SubString(taskId, "id=", "&");
                var time = CommonHelper.SubString(taskId, "t=", "&");
                var url = "https://ocr.wdku.net/waitResult2?id={0}&t={1}&r=0.{2}";

                var strPost = "check=yes";

                result = WebClientSyncExt.GetHtml(string.Format(url, id, time, ServerTime.DateTime.Ticks), strPost, ExecTimeOutSeconds);
            }
            catch (Exception)
            {

            }
            return result;
        }

        public override ProcessStateEntity QueryFileStatuMethod(string taskId)
        {
            var html = GetStatus(taskId);
            var desc = CommonHelper.SubString(html, "\"desc\":\"", "\"");
            var processStatus = new ProcessStateEntity()
            {
                taskId = taskId
            };
            if (desc.Contains("success"))
            {
                processStatus.state = OcrProcessState.处理成功;
                processStatus.desc = "处理完毕，可以下载了！";
            }
            else if (desc.Contains("error"))
            {
                processStatus.state = OcrProcessState.处理失败;
                processStatus.desc = "处理失败，" + desc;
            }
            else if (desc.Contains("converting"))
            {
                processStatus.state = OcrProcessState.处理中;
                processStatus.desc = "正在处理…";
            }
            else
            {
                processStatus.state = OcrProcessState.未知状态;
                Console.WriteLine("当前状态：" + html);
            }
            return processStatus;
        }


    }
}