﻿using System.Collections.Generic;
using System.Text.RegularExpressions;

namespace CommonLib
{
    public class CommonStyle
    {
        internal static string ReplacePunctuationAuto(string text, string lang)
        {
            if (string.IsNullOrEmpty(text))
            {
                return text;
            }

            text = Full2Half(text);

            text = InsertSpace(text);

            text = TransSymbol(text, lang);

            text = RemoveDuplicateSymbol(text);

            return text;
        }

        /**
         * 在中文与英文字母/用于数学、科学和工程的希腊字母/数字之间添加空格
         * Insert a space between Chinese character and English/Greek/Number character.
         *
         * update base on @link https://github.com/Rakume/pangu.php/blob/master/pangu.php
         *
         * @link https://github.com/mzlogin/chinese-copywriting-guidelines#空格
         *
         * @param string $text
         *
         * @return null|string|string[]
         */
        internal static string InsertSpace(string text)
        {
            //spacing
            var o = "⺀-⻿⼀-⿟぀-ゟ゠-ヺー-ヿ㄀-ㄯ㈀-㋿㐀-䶿一-鿿豈-﫿";
            text = Regex.Replace(text, "([\\.]{2,}|…)([" + o + "])", "$1 $2");
            text = Regex.Replace(text, "([" + o + "])\\:([A-Z0-9\\(\\)])", "$1：$2");

            text = Regex.Replace(text, "([" + o + "])([`\"״])", "$1 $2");
            text = Regex.Replace(text, "([`\"״])([" + o + "])", "$1 $2");
            text = Regex.Replace(text, "([`\"\u05f4]+)[ ]* (.+?)[ ]* ([`\"\u05f4]+)", "$1$2$3");

            text = Regex.Replace(text, "([" + o + "])('[^s])", "$1 $2");
            text = Regex.Replace(text, "(')([" + o + "])", "$1 $2");
            text = Regex.Replace(text, "([A-Za-z0-9" + o + "])( )('s)", "$1's");

            text = Regex.Replace(text, "([" + o + "])(#)([" + o + "]+)(#)([" + o + "])", "$1 $2$3$4 $5");
            text = Regex.Replace(text, "([" + o + "])(#([^ ]))", "$1 $2");
            text = Regex.Replace(text, "(([^ ])#)([" + o + "])", "$1 $3");
            text = Regex.Replace(text, "([" + o + "])([\\+\\-\\*\\/=&\\|<>])([A-Za-z0-9])", "$1 $2 $3");

            text = Regex.Replace(text, "([A-Za-z0-9])([\\+\\-\\*\\/=&\\|<>])([" + o + "])", "$1 $2 $3");
            text = Regex.Replace(text, "([/]) ([a-z\\-_\\./]+)", "$1$2");
            text = Regex.Replace(text, "([/\\.])([A-Za-z\\-_\\./]+) ([/])", "$1$2$3");
            text = Regex.Replace(text, "([" + o + "])([\\(\\[\\{<>“])", "$1 $2");
            text = Regex.Replace(text, "([\\)\\]\\}<>“])([" + o + "])", "$1 $2");


            text = Regex.Replace(text, "([\\(\\[\\{<\u201c]+)[ ]*(.+?)[ ]*([\\)\\]\\}>\u201d]+)", "$1$2$3");
            text = Regex.Replace(text, "([A-Za-z0-9" + o + "])[ ]*([“])([A-Za-z0-9" + o + "\\-_ ]+)([“])", "$1 $2$3$4");
            text = Regex.Replace(text, "([“])([A-Za-z0-9" + o + "\\-_ ]+)([“])[ ]*([A-Za-z0-9" + o + "])", "$1$2$3 $4");
            text = Regex.Replace(text, "([A-Za-z0-9])([\\(\\[\\{])", "$1 $2");
            text = Regex.Replace(text, "([\\)\\]\\}])([A-Za-z0-9])", "$1 $2");
            text = Regex.Replace(text, "([" + o + "])([A-Za-zͰ-Ͽ0-9@\\$%\\^&\\*\\-\\+\\\\=\\|/¡-ÿ⅐-↏✀—➿])", "$1 $2");
            text = Regex.Replace(text, "([A-Za-zͰ-Ͽ0-9~\\$%\\^&\\*\\-\\+\\\\=\\|/!;:,\\.\\?¡-ÿ⅐-↏✀—➿])([" + o + "])", "$1 $2");
            text = Regex.Replace(text, "(%)([A-Za-z])", "$1 $2");
            text = Regex.Replace(text, "([ ]*)([\u00b7\u2022\u2027])([ ]*)", "・");

            ////去除中文之间的英文（半角）空格
            //text = Regex.Replace(text, "(?<=[\u4e00-\u9fa5])(\u0020)(?=[\u4e00-\u9fa5])", string.Empty);
            ////去除中文之间的中文（全角）空格
            //text = Regex.Replace(text, "(?<=[\u4e00-\u9fa5])(\u3000)(?=[\u4e00-\u9fa5])", string.Empty);
            ////在中文字符与英文字符之间增加空格
            //text = Regex.Replace(text, @"(?<=[\u4e00-\u9fa5])([a-zA-Z])(?=[a-zA-Z]{0,})", " $1");
            ////在英文字符与中文字符之间增加空格
            //text = Regex.Replace(text, @"(?<=[a-zA-Z])([\u4e00-\u9fa5])(?=[\u4e00-\u9fa5]{0,})", " $1");
            ////在数字与英文字符之间增加空格
            //text = Regex.Replace(text, "([0-9])([A-Za-z])", "$1 $2");
            //去掉计量百分号或者单位之间的空格
            text = Regex.Replace(text, "([0-9])([ ]*)([%°])", "$1$3");

            return text;
        }

        internal static string RemoveDuplicateSymbol(string text)
        {
            // 不重复使用中文标点符号，重复时只保留第一个
            text = Regex.Replace(text, "([！？。，；：、“”‘’『』「」〖〗【】《》（）])\\1{1,}", "\\1");
            // 不重复使用英文标点符号，重复时只保留第一个
            text = Regex.Replace(text, "([!?.,;:'\"[]<>()])\\1{1,}", "\\1");

            // 正确使用省略号
            text = Regex.Replace(text, "([。\\.]){3,}|(…){1}", "……");
            text = Regex.Replace(text, "(……){2,}", "……");

            //时间中间为英文间隔
            text = Regex.Replace(text, "([0-9])：([0-9])", "$1:$2");
            return text;
        }

        static readonly Dictionary<string, string> Full2HalfDictionary = new Dictionary<string, string> {{ "０" , "0"},{ "１" , "1"},{ "２" , "2"},{ "３" , "3"},{ "４" , "4"},
                    { "５" , "5"},{ "６" , "6"},{ "７" , "7"},{ "８" , "8"},{ "９" , "9"},
                    { "Ａ" , "A"},{ "Ｂ" , "B"},{ "Ｃ" , "C"},{ "Ｄ" , "D"},{ "Ｅ" , "E"},
                    { "Ｆ" , "F"},{ "Ｇ" , "G"},{ "Ｈ" , "H"},{ "Ｉ" , "I"},{ "Ｊ" , "J"},
                    { "Ｋ" , "K"},{ "Ｌ" , "L"},{ "Ｍ" , "M"},{ "Ｎ" , "N"},{ "Ｏ" , "O"},
                    { "Ｐ" , "P"},{ "Ｑ" , "Q"},{ "Ｒ" , "R"},{ "Ｓ" , "S"},{ "Ｔ" , "T"},
                    { "Ｕ" , "U"},{ "Ｖ" , "V"},{ "Ｗ" , "W"},{ "Ｘ" , "X"},{ "Ｙ" , "Y"},
                    { "Ｚ" , "Z"},{ "ａ" , "a"},{ "ｂ" , "b"},{ "ｃ" , "c"},{ "ｄ" , "d"},
                    { "ｅ" , "e"},{ "ｆ" , "f"},{ "ｇ" , "g"},{ "ｈ" , "h"},{ "ｉ" , "i"},
                    { "ｊ" , "j"},{"ｋ"  , "k"},{"ｌ" , "l"},{"ｍ" , "m"},{"ｎ" , "n"},
                    {"ｏ" , "o"},{ "ｐ"  , "p"},{ "ｑ" , "q"},{ "ｒ" , "r"},{ "ｓ" , "text"},
                    {"ｔ" , "t"},{"ｕ"   , "u"},{"ｖ" , "v"},{"ｗ" , "w"},{"ｘ" , "x"},
                    {"ｙ" , "y"},{"ｚ"   , "z"},
                    {"－" , "-"},{"　"   , " "},{"／" , "/"},
                    {"％" , "%"},{"＃"  , "#"},{"＠" , "@"},{"＆" , "&"},{"＜" , "<"},
                    {"＞" , ">"},{"［"  , "["},{"］" , "]"},{"｛" , "{"},{"｝" , "}"},
                    {"＼" , "\\"},{"｜" , "|"},{"＋" , "+"},{"＝" , "="},{"＿" , "_"},
                    {"＾" , "^"},{"￣"  , "~"},{"｀" , "`" }};

        static readonly string[] ChineseSymbol = {
            "，", "；", "：", "？", "！", "……", "—", "～", "（", "）", "【", "】", "“", "”", "‘", "’"
        };
        static readonly string[] EnglishSymbol = {
            ",", ";", ":", "?", "!", "…", "-", "~", "(", ")", "[", "]", "\"", "\"","'","'"
        };

        /**
         * 有限度的全角转半角（英文、数字、空格以及某些特殊字符等使用半角字符）
         * Limited full-width to half-width transformer.
         *
         * @link https://github.com/mzlogin/chinese-copywriting-guidelines#全角和半角
         *
         * @param string $text
         *
         * @return null|string|string[]
         */
        internal static string Full2Half(string text)
        {
            foreach (var key in Full2HalfDictionary.Keys)
            {
                text = Regex.Replace(text, "[" + key + "]", Full2HalfDictionary[key]);
            }
            return text;
        }

        /**
        * 根据语言类型，把文字中的标点符号转换为中文标点符号
        * @param  {String} text    识别结果文字
        * @param  {String} lang franc-min 识别的语言类型字符串
        * @return {String}      转换以后的文字
*/
        internal static string TransSymbol(string s, string lang)
        {
            if (ifUseChineseSymbol(lang))
            {
                for (int i = 0; i < ChineseSymbol.Length; i++)
                {
                    s = Regex.Replace(s, "[" + EnglishSymbol[i] + "]", ChineseSymbol[i]);
                }
                s = Regex.Replace(s, "[\"|“]([^\"“”]+?)[\"|”]", "“$1”");
                s = Regex.Replace(s, "['|‘]([^'‘’]+?)['|’]", "‘$1’");
            }
            else
            {
                for (int i = 0; i < EnglishSymbol.Length; i++)
                {
                    s = Regex.Replace(s, "[" + ChineseSymbol[i] + "]", EnglishSymbol[i]);
                }
            }
            return s;
        }

        internal static Dictionary<string, bool> langUseChineseSymbols = new Dictionary<string, bool>() { { "zh", true }, { "jp", true } };

        /**
        * 是否使用中文标点符号
        * @param  {String} lang franc-min 识别的语言类型字符串
        * @return {Boolean} [description]
*/
        internal static bool ifUseChineseSymbol(string lang)
        {
            return langUseChineseSymbols.ContainsKey(lang) && langUseChineseSymbols[lang];
        }
    }
}
