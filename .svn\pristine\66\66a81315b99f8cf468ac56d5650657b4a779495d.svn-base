﻿using System;
using System.IO;
using System.Security.Cryptography;
using System.Text;

namespace CommonLib
{
    public static class DES
    {
        /// <summary>
        /// 通过字符串获取MD5值，返回32位字符串。
        /// </summary>
        /// <param name="str"></param>
        /// <returns></returns>
        public static string GetMD5String(string str)
        {
            using (MD5 md5 = MD5.Create())
            {
                byte[] data2 = md5.ComputeHash(Encoding.UTF8.GetBytes(str));

                return GetbyteToString(data2);
            }
        }

        private static string GetbyteToString(byte[] data)
        {
            StringBuilder sb = new StringBuilder();
            for (int i = 0; i < data.Length; i++)
            {
                sb.Append(data[i].ToString("x2"));
            }
            data = null;
            return sb.ToString();
        }

        //private static byte[] Keys = { 0x12, 0x34, 0x56, 0x78, 0x90, 0xAB, 0xCD, 0xEF };
        private static readonly string strPwd = "qwer1234";
        //默认密钥向量
        private static readonly string strIV = "0E2BF5DAA84EC3D0";

        private static byte[] pwdByte;
        private static byte[] ivByte;

        /// <summary>
        ///     DES加密字符串
        /// </summary>
        /// <param name="encryptString">待加密的字符串</param>
        /// <param name="encryptKey">加密密钥,要求为8位</param>
        /// <returns>加密成功返回加密后的字符串，失败返回源串 </returns>
        public static string EncryptDES(string encryptString)
        {
            try
            {
                //byte[] rgbKey = Encoding.UTF8.GetBytes(strPwd);//转换为字节
                //byte[] rgbIV = Encoding.UTF8.GetBytes(strIV);
                if (pwdByte == null)
                {
                    pwdByte = Encoding.UTF8.GetBytes(strPwd); //转换为字节
                }
                if (ivByte == null)
                {
                    ivByte = Encoding.UTF8.GetBytes(strIV); //转换为字节
                }
                var inputByteArray = Encoding.UTF8.GetBytes(encryptString);
                using (var dCSP = new DESCryptoServiceProvider()) //实例化数据加密标准
                {
                    using (var mStream = new MemoryStream()) //实例化内存流
                    {
                        //将数据流链接到加密转换的流
                        using (
                            var cStream = new CryptoStream(mStream, dCSP.CreateEncryptor(pwdByte, ivByte),
                                CryptoStreamMode.Write))
                        {
                            cStream.Write(inputByteArray, 0, inputByteArray.Length);
                            cStream.FlushFinalBlock();
                            encryptString = Convert.ToBase64String(mStream.ToArray());
                        }
                    }
                }
            }
            catch
            {
            }
            return encryptString;
        }

        /// <summary>
        ///     DES解密字符串
        /// </summary>
        /// <param name="decryptString">待解密的字符串</param>
        /// <param name="decryptKey">解密密钥,要求为8位,和加密密钥相同</param>
        /// <returns>解密成功返回解密后的字符串，失败返源串</returns>
        public static string DecryptDES(string decryptString)
        {
            try
            {
                //byte[] rgbKey = Encoding.UTF8.GetBytes(strPwd);
                //byte[] rgbIV = Encoding.UTF8.GetBytes(strIV);
                var inputByteArray = Convert.FromBase64String(decryptString);
                if (pwdByte == null)
                {
                    pwdByte = Encoding.UTF8.GetBytes(strPwd); //转换为字节
                }
                if (ivByte == null)
                {
                    ivByte = Encoding.UTF8.GetBytes(strIV); //转换为字节
                }
                using (var DCSP = new DESCryptoServiceProvider())
                {
                    using (var mStream = new MemoryStream())
                    {
                        var cStream = new CryptoStream(mStream, DCSP.CreateDecryptor(pwdByte, ivByte),
                            CryptoStreamMode.Write);
                        cStream.Write(inputByteArray, 0, inputByteArray.Length);
                        cStream.FlushFinalBlock();
                        decryptString = Encoding.UTF8.GetString(mStream.ToArray());
                    }
                }
            }
            catch
            {
            }
            return decryptString;
        }
    }
}