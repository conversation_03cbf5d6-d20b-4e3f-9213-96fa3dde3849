﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Security.Cryptography;
using System.Text;

namespace EvalJs.ServiceMsg
{
    internal class BasicFunc
    {
        public static bool IsFileInUse(string fileName)
        {
            bool result = true;
            FileStream fileStream = null;
            try
            {
                fileStream = new FileStream(fileName, FileMode.Open, FileAccess.Read, FileShare.None);
                result = false;
            }
            catch
            {
                result = true;
            }
            finally
            {
                bool flag = fileStream != null;
                if (flag)
                {
                    fileStream.Close();
                }
            }
            return result;
        }

        public static string MD5String(string value)
        {
            MD5 mD = new MD5CryptoServiceProvider();
            byte[] bytes = Encoding.Default.GetBytes(value);
            byte[] array = mD.ComputeHash(bytes);
            mD.Clear();
            string text = "";
            for (int i = 0; i < array.Length; i++)
            {
                text += array[i].ToString("x").PadLeft(2, '0');
            }
            return text;
        }
    }
}
