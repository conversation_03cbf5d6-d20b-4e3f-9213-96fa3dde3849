﻿using CommonLib;
using System;
using System.Collections.Generic;

namespace HanZiOcr
{
    /// <summary>
    /// 腾讯优图X-Lab
    /// https://open.youtu.qq.com/#/open/experience/general
    /// </summary>
    public class YouTuXLabRec : BaseOcrRec
    {
        public YouTuXLabRec()
        {
            OcrGroup = OcrGroupType.腾讯;
            OcrType = HanZiOcrType.优图XLab;
            MaxExecPerTime = 20;
            State = EnableState.禁用;

            LstJsonPreProcessArray = new List<object>() { "data", "text_items" };
            StrResultJsonSpilt = "text";
            IsSupportUrlOcr = true;

            IsSupportVertical = true;
            LstVerticalLocation = new List<object>() { "points" };
        }

        private string strAppId = "10200";

        protected override string GetHtml(OcrContent content)
        {
            return PostFileResult(content.strBase64);
        }

        public override string GetHtmlByUrl(OcrContent content)
        {
            return PostFileResult(null, content.url);
        }

        private string PostFileResult(string strBase64, string imgUrl = null)
        {
            //IsVerticalOcr = true;
            //YouTuXLabRec
            string strPost = "{\"request_id\":\"" + Guid.NewGuid().ToString() + "\"," +
                (string.IsNullOrEmpty(imgUrl) ?
                "\"image\":\"" + strBase64 : "\"url\":\"" + imgUrl)
                + "\",\"appid\":\"" + strAppId + "\"}";

            var result = WebClientSyncExt.GetHtml("https://s.youtux.qq.com/xlab/ocr/character", "svc_openid=" + Guid.NewGuid().ToString().Replace("-", ""), strPost, "", ExecTimeOutSeconds);

            //"{\"errorcode\":0,\"errormsg\":\"OK\",\"items\":[{\"itemcoord\":{\"x\":0,\"y\":3,\"width\":192,\"height\":20},\"itemconf\":0.9895762801170349,\"itemstring\":\"请点击下图中所有的靴子火柴\",\"coords\":[],\"words\":[{\"character\":\"请\",\"confidence\":0.9976868629455566},{\"character\":\"点\",\"confidence\":0.9913544058799744},{\"character\":\"击\",\"confidence\":0.9694445133209229},{\"character\":\"下\",\"confidence\":0.9992054104804993},{\"character\":\"图\",\"confidence\":0.9906545281410217},{\"character\":\"中\",\"confidence\":0.9999581575393677},{\"character\":\"所\",\"confidence\":1.0},{\"character\":\"有\",\"confidence\":0.9998621940612793},{\"character\":\"的\",\"confidence\":0.9999899864196777},{\"character\":\"靴\",\"confidence\":0.9252281785011292},{\"character\":\"子\",\"confidence\":0.999544084072113},{\"character\":\"火\",\"confidence\":0.9967591166496277},{\"character\":\"柴\",\"confidence\":0.994803249835968}],\"candword\":[],\"parag\":{\"word_size\":17,\"parag_no\":0},\"coordpoint\":{\"x\":[0,3,191,3,191,22,0,22]},\"wordcoordpoint\":[]}],\"session_id\":\"\",\"angle\":0.0,\"class\":[],\"recognize_warn_code\":[],\"recognize_warn_msg\":[]}"
            return result;
        }

    }
}