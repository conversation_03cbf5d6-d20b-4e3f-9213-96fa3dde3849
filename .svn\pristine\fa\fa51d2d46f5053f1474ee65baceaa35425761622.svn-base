﻿using CommonLib;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Collections.Specialized;
using System.IO;

namespace HanZiOcr
{
    /// <summary>
    /// 达观AI体验中心-小程序
    /// </summary>
    public class DaGuanAIRec : BaseOcrRec
    {
        public DaGuanAIRec()
        {
            OcrType = HanZiOcrType.达观AI;
            MaxExecPerTime = 23;

            LstJsonPreProcessArray = new List<object>() { "item", "result", "result", "img_data_list" };
            LstJsonNextProcessArray = new List<object>() { "text_info" };
            StrResultJsonSpilt = "text_string";
            IsSupportVertical = true;
            LstVerticalLocation = new List<object>() { "box_points" };
        }

        private const string strTokenSpilt = "\"accessToken\":\"";
        private const string strPathSpilt = "\"path\":\"";

        private string strToken = "";

        private void InitToken()
        {
            if (string.IsNullOrEmpty(strToken))
            {
                strToken = GetToken();
            }
        }
        private string GetToken()
        {
            var result = "";
            var token = WebClientSyncExt.GetHtml("https://idps2-mini-app.datagrand.cn/api/dgauth/user/login", "{\"password\":\"fXo5qr0C3wIQPNXWbMvGFA==\",\"systemFlag\":\"idps\",\"username\":\"Y+HfDu1KeHCe4RFaOk+mzg==\"}", ExecTimeOutSeconds);

            if (!string.IsNullOrEmpty(token) && token.Contains(strTokenSpilt))
            {
                result = token.Substring(token.IndexOf(strTokenSpilt) + strTokenSpilt.Length);
                result = result.Substring(0, result.IndexOf("\""));
            }
            return result;
        }

        public override void Reset()
        {
            strToken = "";
            base.Reset();
        }

        protected override string GetHtml(OcrContent content)
        {
            InitToken();
            var result = "";
            if (!string.IsNullOrEmpty(strToken))
            {
                var byt = Convert.FromBase64String(content.strBase64);
                var file = PostFileResult(byt);
                if (!string.IsNullOrEmpty(file))
                {
                    var headers = new NameValueCollection
                {
                    {"referer", "https://servicewechat.com/wxfed820253998e371/16/page-frame.html"},
                    { "Authorization", "Bearer "+strToken }
                };
                    result = WebClientSyncExt.GetHtml("https://idps2-mini-app.datagrand.cn/api/extracting/v2/task", "{\"file_info_list\":[{\"path\":\"" + file + "\",\"origin_name\":\"1.png\"}],\"async_task\":false,\"feature_type_id\":28}", 10, headers);
                }
            }
            return result;
        }

        private string PostFileResult(byte[] content)
        {
            var result = "";
            try
            {
                var url = "https://idps2-mini-app.datagrand.cn/api/upload/business?business_type=ocr_extract";
                var file = new UploadFileInfo()
                {
                    Name = "files",
                    Filename = "1.png",
                    ContentType = "image/png",
                    Stream = new MemoryStream(content)
                };
                var headers = new NameValueCollection
                {
                    {"referer", "https://servicewechat.com/wxfed820253998e371/16/page-frame.html"},
                    { "Authorization", "Bearer "+strToken }
                };
                var html = PostFile(url, new[] { file }, null, headers);
                if (!string.IsNullOrEmpty(html) && html.Contains(strPathSpilt))
                {
                    result = html.Substring(html.IndexOf(strPathSpilt) + strPathSpilt.Length);
                    result = result.Substring(0, result.IndexOf("\""));
                }
            }
            catch (Exception)
            {

            }
            return result;
        }

    }
}