﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Configuration;
using System.Collections;
using log4net;
using System.Web.Security;
using CommonLib;

namespace Account.Web
{
    public class CommonHelper
    {
        public static readonly ILog _Log;

        static CommonHelper()
        {
            _Log = LogManager.GetLogger("WebLog");
        }

        private static string strDBFile = "Code.txt";
        private static string strPWD = "";

        public static string StrPWD
        {
            get
            {
                if (string.IsNullOrEmpty(strPWD))
                {
                    try
                    {
                        strPWD = ConfigurationManager.AppSettings.Get("pwd");
                    }
                    catch (Exception oe)
                    {
                        strPWD = "123";
                    }
                }
                return CommonHelper.strPWD;
            }
            set { CommonHelper.strPWD = value; }
        }
        private static string strEncrypt = "";

        public static string StrEncrypt
        {
            get
            {
                if (string.IsNullOrEmpty(strEncrypt))
                {
                    try
                    {
                        strEncrypt = ConfigurationManager.AppSettings.Get("Encrypt");
                    }
                    catch (Exception oe)
                    {
                        strEncrypt = "!(*&^%$#";
                    }
                }
                return CommonHelper.strEncrypt;
            }
            set { CommonHelper.strEncrypt = value; }
        }

        public static bool IsEncrypt
        {
            get
            {
                try
                {
                    return ConfigurationManager.AppSettings.Get("IsEncrypt").Equals("true");
                }
                catch (Exception oe)
                {
                }
                return false;
            }
        }

        public static string MD5(string str)
        {
            return FormsAuthentication.HashPasswordForStoringInConfigFile(str, "MD5").ToString();
        }

        public static string GetTimeStamp(bool bflag = true)
        {
            TimeSpan timeSpan = DateTime.UtcNow - new DateTime(1970, 1, 1, 0, 0, 0, 0);
            string result = string.Empty;
            if (bflag)
            {
                result = Convert.ToInt64(timeSpan.TotalSeconds).ToString();
            }
            else
            {
                result = Convert.ToInt64(timeSpan.TotalMilliseconds).ToString();
            }
            return result;
        }

        public static string SubString(string strSource, string strSpilt, string strEnd = "")
        {
            return SubStringHorspool(strSource, strSpilt, strEnd).Trim();
        }

        //Horspool匹配算法
        public static string SubStringHorspool(string str, string strStart, string strEnd = "")
        {
            int index;
            if (!string.IsNullOrEmpty(strStart))
            {
                index = str.IndexOf(strStart);
                if (index >= 0)
                {
                    str = str.Substring(index + strStart.Length);
                }
                else
                    str = "";
            }
            if (!string.IsNullOrEmpty(str) && !string.IsNullOrEmpty(strEnd))
            {
                index = str.IndexOf(strEnd);
                if (index >= 0)
                {
                    str = str.Substring(0, index);
                }
                //else
                //    str = "";
            }
            return str;
        }


        private static string strConnectString = string.Empty;

        private static IFreeSql _DBHelper = null;

        private static object objLock = new object();

        public static IFreeSql DBHelper
        {
            get
            {
                lock (objLock)
                {
                    if (string.IsNullOrEmpty(strConnectString))
                    {
                        var dbFilePath = ConfigurationManager.AppSettings.Get("DBFilePath");
                        var isMySql = false;
                        if (!string.IsNullOrEmpty(dbFilePath) && dbFilePath.StartsWith("Data Source"))
                        {
                            isMySql = true;
                            strConnectString = dbFilePath;
                        }
                        else
                        {
                            if (string.IsNullOrEmpty(dbFilePath))
                            {
                                dbFilePath = AppDomain.CurrentDomain.BaseDirectory;
                            }
                            if (File.Exists(dbFilePath + "\\DB\\" + strDBFile))
                                strConnectString = "Data Source=" + dbFilePath + "\\DB\\" + strDBFile;
                            else
                                strConnectString = "Data Source=" + Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData) + "\\DB\\" + strDBFile;
                        }

                        _DBHelper = new FreeSql.FreeSqlBuilder()
                        .UseConnectionString(isMySql ? FreeSql.DataType.MySql : FreeSql.DataType.Sqlite, strConnectString).Build();
                        _DBHelper.Aop.CommandBefore += Aop_CommandBefore;
                        _DBHelper.Aop.CurdAfter += Aop_CurdAfter;
                    }
                }
                return CommonHelper._DBHelper;
            }
            set { CommonHelper._DBHelper = value; }
        }

        private static void Aop_CommandBefore(object sender, FreeSql.Aop.CommandBeforeEventArgs e)
        {
            if (e.Command.CommandText.Contains("truncate") || e.Command.CommandText.Contains("drop table"))
            {
                LogHelper.Log.Info(string.Format("BadSQL:{0}", e.Command.CommandText));
                throw new Exception("Bad Request");
            }
            if (e.Command.CommandText.Contains("insert ") || e.Command.CommandText.Contains("update ") || e.Command.CommandText.Contains("delete "))
            {
                if (e.Command.CommandText.Contains("this_is_a_test_string") || e.Command.CommandText.Contains("md5(") || e.Command.CommandText.Contains("die(")
                    || e.Command.CommandText.Contains("print(") || e.Command.CommandText.Contains("sleep(") || e.Command.CommandText.Contains("{") || e.Command.CommandText.Contains("}")
                    || e.Command.CommandText.Contains("$") || e.Command.CommandText.Contains("#") || e.Command.CommandText.Contains("/*") || e.Command.CommandText.Contains("()"))
                {
                    LogHelper.Log.Info(string.Format("BadSQL:{0}", e.Command.CommandText));
                    throw new Exception("Bad Request");
                }
            }
        }

        private static void Aop_CurdAfter(object sender, FreeSql.Aop.CurdAfterEventArgs e)
        {
            if (e.ElapsedMilliseconds > 100)
            {
                LogHelper.Log.Info(string.Format("执行超时：{0}ms,SQL:{1}", e.ElapsedMilliseconds.ToString("F0"), e.Sql));
            }
            if (e.Exception != null)
            {
                LogHelper.Log.Error($"SQL:{e.Sql}", e.Exception);
            }
        }

        private static string strEamil = "";

        public static string StrEamil
        {
            get
            {
                if (string.IsNullOrEmpty(strEamil))
                {
                    try
                    {
                        strEamil = ConfigurationManager.AppSettings.Get("strEmailAccount");
                    }
                    catch (Exception oe)
                    {
                        strEamil = "<EMAIL>";
                    }
                }
                return CommonHelper.strEamil;
            }
            set { CommonHelper.strEamil = value; }
        }

        public static string StrRelpace
        {
            get
            {
                try
                {
                    return ConfigurationManager.AppSettings.Get("StrRelpace");
                }
                catch { }
                return "";
            }
        }

        private static List<RelpaceEntity> lstReplace;

        public static List<RelpaceEntity> LstReplace
        {
            get
            {
                if (lstReplace == null || lstReplace.Count <= 0)
                {
                    lstReplace = new List<RelpaceEntity>();
                    if (!string.IsNullOrEmpty(StrRelpace))
                    {
                        foreach (var item in StrRelpace.Split(new string[] { "|" }, StringSplitOptions.RemoveEmptyEntries))
                        {
                            try
                            {
                                if (item.Contains(","))
                                {
                                    RelpaceEntity entity = new RelpaceEntity();
                                    entity.StrFirst = SubString(item, "", ",");
                                    entity.StrNext = SubString(item, ",");
                                    lstReplace.Add(entity);
                                }
                            }
                            catch (Exception oe)
                            {
                                Console.WriteLine(oe.Message);
                            }
                        }
                    }
                }
                return CommonHelper.lstReplace;
            }
            set { CommonHelper.lstReplace = value; }
        }

        public static string GetReplaceStr(string strOld)
        {
            try
            {
                if (LstReplace != null && LstReplace.Count > 0)
                {
                    foreach (var item in LstReplace)
                    {
                        strOld = strOld.Replace(item.StrFirst, item.StrNext);
                    }
                }
            }
            catch (Exception oe)
            {
                Console.WriteLine(oe.Message);
            }
            return strOld;
        }
        private static List<EmailEntity> lstEmailAccount;
        private static Random rndAccount = new Random();

        public static void SetForbiden(string strAccount = "")
        {
            try
            {
                for (int i = 0; i < lstEmailAccount.Count; i++)
                {
                    if (!string.IsNullOrEmpty(strAccount) && lstEmailAccount[i].StrAccount.Equals(strAccount))
                    {
                        lstEmailAccount[i].IsForbiden = true;
                        lstEmailAccount[i].DtExpired = ServerTime.DateTime.AddHours(1);
                    }
                    else
                    {
                        if (lstEmailAccount[i].IsForbiden && lstEmailAccount[i].DtExpired <= ServerTime.DateTime)
                        {
                            lstEmailAccount[i].IsForbiden = false;
                        }
                    }
                }
            }
            catch (Exception oe)
            {
                Console.WriteLine(oe.Message);
            }
        }

        public static EmailEntity GetRndEmail()
        {
            EmailEntity email = new EmailEntity();
            try
            {
                if (lstEmailAccount == null || lstEmailAccount.Count <= 0)
                {
                    lstEmailAccount = new List<EmailEntity>();
                    foreach (var item in CommonHelper.StrEamil.Split(new string[] { ";" }, StringSplitOptions.RemoveEmptyEntries))
                    {
                        string[] info = item.Split(new string[] { "|" }, StringSplitOptions.None);
                        if (info != null && info.Length > 0)
                        {
                            lstEmailAccount.Add(new EmailEntity()
                            {
                                StrAccount = info[0],
                                StrPwd = CommonHelper.StrEamilPWD,
                                IsForbiden = false,
                                IsNotSplitAccount = info.Length > 1 && info[1].Equals("1"),
                            });
                        }
                    }
                }
            }
            catch (Exception oe)
            {
                Console.WriteLine(oe.Message);
            }
            SetForbiden();
            try
            {
                if (lstEmailAccount != null && lstEmailAccount.Count > 0)
                {
                    var tmp = lstEmailAccount.FindAll(p => !p.IsForbiden);
                    email = tmp[rndAccount.Next(0, tmp.Count)];
                }
            }
            catch (Exception oe)
            {
                Console.WriteLine(oe.Message);
            }
            if (email == null || string.IsNullOrEmpty(email.StrAccount))
            {
                email = new EmailEntity() { StrAccount = "<EMAIL>", StrPwd = "Aa@qpzs01", IsForbiden = false };
            }
            return email;
        }

        private static string strEamilPWD = "";

        public static string StrEamilPWD
        {
            get
            {
                if (string.IsNullOrEmpty(strEamilPWD))
                {
                    try
                    {
                        strEamilPWD = ConfigurationManager.AppSettings.Get("strEmailPWD");
                    }
                    catch (Exception oe)
                    {
                        strEamilPWD = "Aa@qpzs";
                    }
                }
                return CommonHelper.strEamilPWD;
            }
            set { CommonHelper.strEamilPWD = value; }
        }

        /// <summary>
        /// 本地路径转换成URL相对路径
        /// </summary>
        /// <param name="filePath"></param>
        /// <returns></returns>
        public static string ConvertFileToUrl(string filePath)
        {
            ////获取程序根目录
            var tmpRootDir = System.Web.HttpContext.Current.Server.MapPath(System.Web.HttpContext.Current.Request.ApplicationPath.ToString());
            return filePath.Replace(tmpRootDir, "").Replace(@"\", @"/");
        }
    }

    public class RelpaceEntity
    {
        public string StrFirst { get; set; }

        public string StrNext { get; set; }
    }
    public class FileComparer : IComparer
    {
        int IComparer.Compare(Object o1, Object o2)
        {
            FileInfo fi1 = o1 as FileInfo;
            FileInfo fi2 = o2 as FileInfo;
            return fi2.CreationTime.CompareTo(fi1.CreationTime);
        }
    }
    public class DirectoryComparer : IComparer
    {
        int IComparer.Compare(Object o1, Object o2)
        {
            DirectoryInfo fi1 = o1 as DirectoryInfo;
            DirectoryInfo fi2 = o2 as DirectoryInfo;
            return fi2.CreationTime.CompareTo(fi1.CreationTime);
        }
    }
}
