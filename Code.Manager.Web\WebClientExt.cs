﻿using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Collections.Specialized;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.Cache;
using System.Reflection;
using System.Text;
using System.Threading.Tasks;
using System.Web;
using CommonLib;

namespace Code.Manager.Web
{
    public delegate byte[] GetImage(
        CNNWebClient myClient, string Url, ref string CookieStr, string ipAddress = "", string strPost = "",
        bool isMobile = false, NameValueCollection collect = null);

    public delegate string GetNoSyncHtml(
        string Url, ref string CookieStr, out bool isCache, string ipAddress = "", string strPost = ""
        , string Referer = "", NameValueCollection collect = null, int timeOut = 2, bool isMobile = false);

    public class WebClientExt
    {
        private static readonly ConcurrentBag<CNNWebClient> lstCache = new ConcurrentBag<CNNWebClient>();

        private static string smethod_0(string string_5)
        {
            var text = string_5;
            var uri = new Uri(text, true);
            if (!string.IsNullOrEmpty(uri.Query))
            {
                var array = uri.Query.Split('&');
                var text2 = string.Empty;
                var array2 = array;
                foreach (var text3 in array2)
                {
                    if (!string.IsNullOrEmpty(text3))
                    {
                        var array3 = text3.Split('=');
                        if (array3.Length >= 2)
                        {
                            var text4 = text2;
                            text2 = string.Concat(text4, string.IsNullOrEmpty(text2) ? "" : "&", array3[0], "=",
                                HttpUtility.UrlEncode(array3[1], Encoding.GetEncoding("gb2312")));
                        }
                    }
                }
                text = text.Replace(uri.Query, text2);
            }
            return text;
        }

        public static bool DCRouter(string sUser, string sPwd, string sUrl, string sPostData, ref string routeType)
        {
            bool result;
            routeType = "";
            WebResponse res = null;
            try
            {
                sUrl = smethod_0(sUrl);
                var domain = "";
                var httpWebRequest = (HttpWebRequest) WebRequest.Create(sUrl);
                httpWebRequest.ProtocolVersion = HttpVersion.Version11;
                httpWebRequest.Method = "GET";
                httpWebRequest.KeepAlive = true;
                httpWebRequest.PreAuthenticate = true;
                httpWebRequest.AllowAutoRedirect = true;
                httpWebRequest.Headers.Add(HttpRequestHeader.AcceptEncoding, "gzip, deflate");
                httpWebRequest.Accept =
                    "image/gif, image/jpeg, image/pjpeg, image/pjpeg, application/x-shockwave-flash, application/vnd.ms-powerpoint, application/vnd.ms-excel, application/msword, */*";
                httpWebRequest.ContentType = "application/x-www-form-urlencoded";
                httpWebRequest.UserAgent =
                    "Mozilla/5.0 (Windows NT 5.1) AppleWebKit/534.30 (KHTML, like Gecko) Chrome/12.0.742.12 Safari/534.30";
                httpWebRequest.Referer = sUrl;
                httpWebRequest.Timeout = 5000;
                if (sUser != string.Empty)
                {
                    var networkCredential = new NetworkCredential(sUser, sPwd, domain);
                    httpWebRequest.Credentials = networkCredential.GetCredential(new Uri(sUrl), string.Empty);
                }
                else
                {
                    httpWebRequest.Credentials = CredentialCache.DefaultCredentials;
                }
                if (!string.IsNullOrEmpty(sPostData))
                {
                    httpWebRequest.Method = "POST";
                    httpWebRequest.ContentType = "application/x-www-form-urlencoded";
                    var bytes = Encoding.Default.GetBytes(sPostData);
                    httpWebRequest.ContentLength = bytes.Length;
                    using (var requestStream = httpWebRequest.GetRequestStream())
                    {
                        requestStream.Write(bytes, 0, bytes.Length);
                    }
                }
                res = httpWebRequest.GetResponse();
                using (var streamReader = new StreamReader(res.GetResponseStream(), Encoding.Default))
                {
                    streamReader.ReadToEnd();
                    streamReader.Close();
                }
                result = true;
            }
            catch (Exception oe)
            {
                result = false;
                if (oe as WebException != null)
                {
                    res = (oe as WebException).Response as HttpWebResponse;
                }
            }
            try
            {
                if (res != null && !string.IsNullOrEmpty(res.Headers["WWW-Authenticate"]))
                {
                    routeType = res.Headers["WWW-Authenticate"].Trim().Replace("Basic realm=\"", "").Replace("\"", "");
                }
            }
            catch
            {
            }
            try
            {
                if (res != null)
                {
                    res.Close();
                    res = null;
                }
            }
            catch
            {
            }
            return result;
        }

        public static string GetHtml(string Url, ref string CookieStr, string ipAddress = ""
            , string strPost = "", string Referer = "", int timeOut = 2, bool isMobile = false,
            NameValueCollection collect = null)
        {
            var isCache = false;
            return GetHtml(Url, ref CookieStr, out isCache, ipAddress, strPost, Referer, timeOut, isMobile, collect);
        }

        private static CNNWebClient GetOneClient(bool isMobile = false)
        {
            try
            {
                CNNWebClient myClient = null;
                lock (lstCache)
                {
                    if (lstCache.Count > 0)
                    {
                        if (isMobile)
                        {
                            myClient = lstCache.FirstOrDefault(p => p.isMobile && !p.IsBusy && !p.IsUsed);
                        }
                        else
                            myClient = lstCache.FirstOrDefault(p => !p.IsBusy && !p.IsUsed);
                    }
                    if (myClient == null)
                    {
                        myClient = new CNNWebClient();
                        if (isMobile)
                            myClient.isMobile = true;
                        lstCache.Add(myClient);
                    }
                    myClient.IsUsed = true;
                }
                return myClient;
            }
            catch (Exception oe)
            {
            }
            return GetOneClient();
        }

        public static string GetHtml(string Url, ref string CookieStr, out bool isCache, string ipAddress = ""
            , string strPost = "", string Referer = "", int timeOut = 2, bool isMobile = false,
            NameValueCollection collect = null)
        {
            isCache = false;
            var dtStart = ServerTime.DateTime;
            var result = "";
            var is12306 = Url.Contains(".12306.cn");
            var myClient = GetOneClient();
            try
            {
                myClient.Headers.Clear();
                //if (isMobile)
                //    myClient.Credentials = CredentialCache.DefaultCredentials;
                myClient.Timeout = timeOut;
                myClient.isMobile = isMobile;
                myClient.StrIPAddress = ipAddress;
                myClient.Is12306 = is12306;

                if (!Url.Contains("leftTicket/query"))
                {
                    if (Url.Contains("ocr.bj.baidubce.com") || Url.Contains("googleapis.com"))
                        myClient.Headers.Add("Content-Type: application/json");
                    else
                        myClient.Headers.Add("Content-Type: application/x-www-form-urlencoded; charset=UTF-8");
                }
                if (Url.Contains("confirmSingleForQueueAsys") || Url.Contains("ocr.bj.baidubce.com") ||
                    Url.Contains("googleapis.com") || Url.Contains("azure.cn"))
                    myClient.Headers.Add("Accept: application/json");
                else
                    myClient.Headers.Add("Accept: text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8");
                myClient.Headers.Add("Accept-Language: zh-CN,zh;q=0.8");
                myClient.Headers.Add("Cache-Control: no-cache");

                if (is12306)
                {
                    if (!Url.Contains("confirmSingleForQueueAsys") || Url.Contains("azure.cn"))
                        myClient.Headers.Add("X-Requested-With: XMLHttpRequest");
                }
                else
                {
                    if (!string.IsNullOrEmpty(Referer))
                        myClient.Headers.Add("Referer: " + Referer);
                }
                if (!string.IsNullOrEmpty(CookieStr))
                {
                    myClient.Headers.Add("Cookie: " + CookieStr);
                }
                if (is12306 || Url.Contains("oldfish") || Url.Contains("train") || Url.Contains("azure.cn")
                    || Url.Contains("?op=") || Url.Contains("ip.cn") || Url.Contains(".ashx")
                    || Url.Contains("googleapis.com") || Url.Contains("ocr.bj.baidubce.com"))
                    myClient.Encoding = Encoding.UTF8;
                if (collect != null && collect.Count > 0)
                {
                    foreach (string key in collect)
                    {
                        try
                        {
                            myClient.Headers.Add(string.Format("{0}: {1}", key.Trim(),
                                collect[key] == null ? "" : collect[key].Trim()));
                        }
                        catch (Exception oe)
                        {
                            Console.WriteLine(oe.Message);
                        }
                    }
                }
                if (isMobile)
                {
                    myClient.Headers.Add(
                        "User-Agent: Mozilla/5.0 (iPhone; CPU iPhone OS 7_1_2 like Mac OS X) AppleWebKit/537.51.2 (KHTML, like Gecko) Mobile/11D257 (367745632)/Worklight/6.0.0");
                }
                //if (Url.Contains("/query?"))
                //{
                //    result = _12306Helper.lstQuery[(int)(DateTime.Now.Ticks % 4)];
                //}
                //else
                {
                    var tmp = DoWebRequest(Url, strPost, is12306, myClient);
                    result = tmp.Result;
                    //var uri = new Uri(Url, is12306);
                    //if (string.IsNullOrEmpty(strPost))
                    //    result = myClient.DownloadString(uri);
                    //else
                    //    result = myClient.UploadString(uri, strPost);
                }
                if (is12306 && !string.IsNullOrEmpty(myClient.ResponseHeaders["Set-Cookie"]))
                {
                }
                var loc = myClient.ResponseHeaders["Location"];
                if (!string.IsNullOrEmpty(loc) && !Url.Equals(loc) && !loc.Contains("error.html"))
                {
                    if (is12306 && !loc.Contains("12306.cn"))
                    {
                        //if (!myClient.ResponseHeaders["Location"].Contains("train"))
                        //    IPHelper.AutoChangeIP(isMobile, ipAddress);
                    }
                    else
                    {
                        return GetHtml(loc, ref CookieStr, ipAddress, strPost, Referer, timeOut);
                    }
                }
                loc = null;
                //if (Url.Contains("baidu") && !string.IsNullOrEmpty(myClient.ResponseHeaders["Date"]))//header.Url.Contains("12306") ||
                //{
                //    try
                //    {
                //        result = myClient.ResponseHeaders["Date"];
                //    }
                //    catch { }
                //}
                if (string.IsNullOrEmpty(result))
                {
                    result = " ";
                }
            }
            catch (OutOfMemoryException oe)
            {
                Console.WriteLine(oe.Message);
            }
            catch (Exception oe)
            {
                //log4net.LogManager.GetLogger("Order").Error(oe);
                if (oe.Message.Contains("套接字"))
                {
                    Console.WriteLine(oe.Message);
                }
                if (oe as WebException != null)
                {
                    var response = (oe as WebException).Response as HttpWebResponse;
                    try
                    {
                        if (response != null)
                        {
                            switch (response.StatusCode)
                            {
                                case HttpStatusCode.Forbidden: //403
                                    if (is12306)
                                    {
                                        if (Url.Contains("leftTicket/query"))
                                        {
                                            result = "403";
                                        }
                                        else
                                        {
                                            if (response != null)
                                            {
                                                result =
                                                    new StreamReader(response.GetResponseStream()).ReadToEnd().Trim();
                                            }
                                        }
                                        //if (!result.StartsWith("bad"))
                                        //    IPHelper.AutoChangeIP(isMobile, ipAddress);
                                    }
                                    break;
                                case HttpStatusCode.MethodNotAllowed: //405
                                    //if (Url.Contains("12306."))
                                    //{
                                    //    if (response != null)
                                    //    {
                                    //        result = new StreamReader(response.GetResponseStream()).ReadToEnd().Trim();
                                    //    }
                                    //    if (!result.StartsWith("bad"))
                                    //        IPHelper.AutoChangeIP(isMobile, ipAddress);
                                    //}
                                    break;
                                case HttpStatusCode.Unauthorized:
                                    break;
                                case HttpStatusCode.InternalServerError: //500
                                    break;
                                case HttpStatusCode.NotFound: //404
                                    result = " ";
                                    break;
                                case HttpStatusCode.RequestTimeout: //超时
                                    //if (new TimeSpan(DateTime.Now.Ticks - dtStart.Ticks).TotalSeconds > CommonString.NMaxRequestTimeOut)
                                    //{
                                    //    IPHelper.RemoveUnIP(isMobile, ipAddress, IPHelper.AutoChangeIP(isMobile, ipAddress, false), false);
                                    //}
                                    break;
                                case HttpStatusCode.ServiceUnavailable: //503
                                    break;
                            }
                        }
                        else if (!string.IsNullOrEmpty(oe.Message))
                        {
                            if (oe.Message.Contains("403 Forbidden"))
                            {
                                if (is12306 && Url.Contains("leftTicket/query"))
                                    result = "403";
                                //IPHelper.AutoChangeIP(isMobile, ipAddress);
                            }
                            else if (!string.IsNullOrEmpty(ipAddress) && oe.Message.Contains("操作超时"))
                            {
                                //IPHelper.RemoveUnIP(isMobile, ipAddress, IPHelper.AutoChangeIP(isMobile, ipAddress, false), false);
                            }
                            else if (oe.Message.Contains("(404)"))
                            {
                                result = " ";
                            }
                        }
                    }
                    catch
                    {
                    }
                    finally
                    {
                        try
                        {
                            if (response != null)
                            {
                                response.Close();
                                response = null;
                            }
                        }
                        catch
                        {
                        }
                    }
                }
                else if (!string.IsNullOrEmpty(oe.Message))
                {
                    if (oe.Message.Contains("403 Forbidden"))
                    {
                        if (is12306 && Url.Contains("leftTicket/query"))
                            result = "403";
                        //IPHelper.AutoChangeIP(isMobile, ipAddress);
                    }
                    else if (!string.IsNullOrEmpty(ipAddress) && oe.Message.Contains("操作超时"))
                    {
                        //if (new TimeSpan(DateTime.Now.Ticks - dtStart.Ticks).TotalSeconds > CommonString.NMaxRequestTimeOut)
                        //{
                        //    IPHelper.RemoveUnIP(isMobile, ipAddress, IPHelper.AutoChangeIP(isMobile, ipAddress, false), false);
                        //}
                    }
                    else if (oe.Message.Contains("(404)"))
                    {
                        result = " ";
                    }
                }
            }
            finally
            {
                if (myClient.ResponseHeaders != null && !string.IsNullOrEmpty(myClient.ResponseHeaders["Set-Cookie"]))
                {
                    CookieStr = GetNewCookie(CookieStr, myClient.ResponseHeaders["Set-Cookie"].Trim());
                    //CookieStr = myClient.ResponseHeaders["Set-Cookie"].Replace("Path=/otn", " ").Replace("Path=/", "").Replace("path=/", "").Replace(",", "").Trim();
                }
                try
                {
                    Url = null;
                    strPost = null;
                    ipAddress = null;
                    strPost = null;
                    Referer = null;
                    collect = null;
                }
                catch
                {
                }
                try
                {
                    if (myClient != null && myClient.ResponseHeaders != null &&
                        !string.IsNullOrEmpty(myClient.ResponseHeaders["Age"]))
                        isCache = true;
                }
                catch
                {
                }
                try
                {
                    if (myClient.IsBusy)
                        myClient.CancelAsync();
                }
                catch
                {
                }
                myClient.IsUsed = false;
                //try
                //{
                //    myClient.Dispose();
                //}
                //catch { }
                //try
                //{
                //    myClient = null;
                //}
                //catch { }
            }
            return result;
        }

        private static async Task<string> DoWebRequest(string Url, string strPost, bool is12306, CNNWebClient myClient)
        {
            var result = "";
            var uri = new Uri(Url, is12306);
            if (string.IsNullOrEmpty(strPost))
                result = await myClient.DownloadStringTaskAsync(uri);
            else
                result = await myClient.UploadStringTaskAsync(uri, strPost);
            return result;
        }

        private static string GetNewCookie(string strCookie, string strNewCookie)
        {
            var strTmpCookie = strCookie;
            if (!string.IsNullOrEmpty(strNewCookie))
            {
                var lstTmp = new List<string>();
                lstTmp.AddRange(strNewCookie.Split(new[] {",", ";"}, StringSplitOptions.RemoveEmptyEntries));
                var strItem = "";
                foreach (var item in lstTmp)
                {
                    if (!item.Trim().ToLower().StartsWith("path=")
                        && !item.Trim().ToLower().StartsWith("expires=")
                        && !item.Trim().ToLower().StartsWith("httponly=")
                        && !item.Trim().ToLower().StartsWith("domain=.")
                        && item.IndexOf("=") > 0)
                    {
                        strItem = CommonHelper.SubStringHorspool(item.Trim(), "", "=") + "=";
                        if (!strTmpCookie.Contains(strItem))
                        {
                            strTmpCookie += string.Format(";{0};", item.Trim());
                        }
                        else
                        {
                            strTmpCookie =
                                strTmpCookie.Replace(
                                    strItem + CommonHelper.SubStringHorspool(strTmpCookie, strItem, ";"), item);
                        }
                    }
                }
            }
            return strTmpCookie.Replace(" ", "").Replace(";;", ";").TrimStart(';');
        }

        private static string RequestNoSyncHtml(string Url, ref string CookieStr, out bool isCache,
            string ipAddress = ""
            , string strPost = "", string Referer = "", NameValueCollection collect = null, int timeOut = 2,
            bool isMobile = false)
        {
            isCache = false;
            return GetHtml(Url, ref CookieStr, out isCache, ipAddress, strPost, "", timeOut, isMobile, collect);
        }

        private static string GetCookie(string CookieStr)
        {
            var result = "";
            var myArray = CookieStr.Split(',');
            if (myArray.Any())
            {
                //result = "Cookie: ";
                foreach (var str in myArray)
                {
                    var CookieArray = str.Split(';');
                    result += CookieArray[0].Trim();
                    result += "; ";
                }
                result = result.Substring(0, result.Length - 2);
            }
            return result;
        }

        #region Web

        public static string GetHtml(string url, double timeOut)
        {
            return GetHtml(url, "", "", "", 1, timeOut > 0 ? (int) timeOut : 2);
        }

        public static string GetHtml(string url, int retryCount = 1)
        {
            return GetHtml(url, "", "", "", retryCount);
        }

        public static string GetHtml(string url, int retryCount, ref long nMsec)
        {
            var strTmp = string.Empty;
            if (retryCount <= 0)
                retryCount = 1;
            var stop = new Stopwatch();
            stop.Start();
            for (var i = 0; i < retryCount; i++)
            {
                stop.Restart();
                strTmp = GetHtml(url, "", "", "", 1);
                if (!string.IsNullOrEmpty(strTmp))
                    break;
            }
            stop.Stop();
            nMsec = stop.ElapsedMilliseconds;
            return strTmp;
        }

        public static string GetHtml(string url, string cookie, string ipAddress, string post = "", int retryCount = 1,
            int timeOut = 2)
        {
            return GetHtml(url, ref cookie, ipAddress, post, retryCount, timeOut, false);
            //string strTmp = string.Empty;
            //if (retryCount <= 0)
            //    retryCount = 1;
            //for (int i = 0; i < retryCount; i++)
            //{
            //    strTmp = GetHtml(url, ref cookie, ipAddress, post, "", timeOut);
            //    if (!string.IsNullOrEmpty(strTmp))
            //        break;
            //}
            //return strTmp;
        }

        public static string GetHtml(string url, ref string cookie, string ipAddress, string post = ""
            , int retryCount = 1, int timeOut = 2, bool isMobile = false)
        {
            var strTmp = string.Empty;
            retryCount = retryCount <= 0 ? 1 : retryCount;
            for (var i = 0; i < retryCount; i++)
            {
                strTmp = GetHtml(url, ref cookie, ipAddress, post, "", timeOut, isMobile);
                if (!string.IsNullOrEmpty(strTmp))
                    break;
            }
            if (strTmp.Equals(" "))
                strTmp = "";
            return strTmp;
        }

        #endregion

        #region Mobile

        public static string GetMobileHtml(string url, ref string cookie, string ipAddress = "", string post = ""
            , int timeOut = 2, NameValueCollection collect = null)
        {
            return GetHtml(url, ref cookie, ipAddress, post, "", timeOut, true, collect);
        }

        public static string GetMobileHtml(string url, ref string cookie, out bool isCache, string ipAddress = "",
            string post = ""
            , int timeOut = 2, NameValueCollection collect = null)
        {
            return GetHtml(url, ref cookie, out isCache, ipAddress, post, "", timeOut, true, collect);
        }

        public static string GetMobileHtml(string url, ref string cookie, string post,
            NameValueCollection collect = null, int timeOut = 1)
        {
            return GetHtml(url, ref cookie, "", post, "", timeOut, true, collect);
        }

        public static string GetMobileHtml(string url, string cookie, string post, NameValueCollection collect = null,
            int retryCount = 1)
        {
            return GetMobileHtml(url, ref cookie, post, collect, retryCount);
        }

        public static string GetMobileHtml(string url, int retryCount = 1)
        {
            return GetMobileHtml(url, "", "", null, retryCount);
        }

        public static string GetMobileHtml(string url, double timeOut)
        {
            return GetMobileHtml(url, "", "", "", 1, timeOut > 0 ? (int) timeOut : 2);
        }

        public static string GetMobileHtml(string url, string cookie, string ipAddress, string post = "",
            int retryCount = 1, int timeOut = 2, NameValueCollection pubCollect = null)
        {
            var strTmp = string.Empty;
            retryCount = retryCount <= 0 ? 1 : retryCount;
            for (var i = 0; i < retryCount; i++)
            {
                strTmp = GetHtml(url, ref cookie, ipAddress, post, "", timeOut, true, pubCollect);
                if (!string.IsNullOrEmpty(strTmp))
                    break;
            }
            return strTmp;
        }

        #endregion

        #region WebSync

        //public static string GetSyncHtml(string url, int retryCount = 1)
        //{
        //    return GetSyncHtml(url, "", "");
        //}

        //public static string GetSyncHtml(string url, double timeOut)
        //{
        //    return GetSyncHtml(url, "", "", "", 1, timeOut > 0 ? (int)timeOut : 2);
        //}

        public static string GetSyncHtml(string url, string cookie, string ipAddress = "", string post = "",
            int retryCount = 1, int timeout = 2)
        {
            return GetSyncHtml(url, ref cookie, ipAddress, post, null, retryCount, timeout);
        }

        public static string GetSyncHtml(string url, ref string cookie, string ipAddress = ""
            , string post = "", NameValueCollection collect = null, int retryCount = 1, int timeout = 2)
        {
            var isCache = false;

            return GetSyncHtml(url, ref cookie, out isCache, ipAddress, post, collect, retryCount, timeout);
        }

        public static string GetSyncHtml(string url, ref string cookie, out bool isCache, string ipAddress = "",
            string post = ""
            , NameValueCollection collect = null, int retryCount = 1, int timeout = 2, bool isMobile = false)
        {
            isCache = false;
            var strTmp = string.Empty;
            retryCount = retryCount <= 0 ? 1 : retryCount;
            timeout = timeout < 0 ? 2 : timeout;

            for (var i = 0; i < retryCount; i++)
            {
                GetNoSyncHtml handler = null;
                try
                {
                    handler = RequestNoSyncHtml;
                    var async = handler.BeginInvoke(url, ref cookie, out isCache, ipAddress, post, "", collect, timeout,
                        isMobile, null, null);
                    async.AsyncWaitHandle.WaitOne(timeout*1000, true); //采用异步等待的方式。直到取到合适的值
                    //if (async.IsCompleted)
                    //{
                    //    strTmp = handler.EndInvoke(async);
                    //}
                    strTmp = handler.EndInvoke(ref cookie, out isCache, async);
                    handler = null;
                    async.AsyncWaitHandle.Close();
                    async.AsyncWaitHandle.Dispose();
                    //async.AsyncWaitHandle.SafeWaitHandle.Close();
                    //async.AsyncWaitHandle.SafeWaitHandle.Dispose();
                    async = null;
                }
                catch (OutOfMemoryException oe)
                {
                    Console.WriteLine(oe.Message);
                }
                catch (ObjectDisposedException oe)
                {
                    Console.WriteLine(oe.Message);
                }
                catch (Exception oe)
                {
                    Console.WriteLine(oe.Message);
                }
                finally
                {
                    handler = null;
                }
                if (!string.IsNullOrEmpty(strTmp))
                    break;
            }
            return strTmp;
        }

        #endregion
    }

    ///// <summary>
    ///// 过期时回调委托
    ///// </summary>
    ///// <param name="userdata"></param>
    //public delegate void TimeoutCaller(object userdata);

    public class CNNWebClient : WebClient
    {
        private int _timeOut = 3;

        private string strHost = "";

        private string strIPAddress = "";

        public bool IsUsed { get; set; }
        public bool isMobile { get; set; }
        public bool Is12306 { get; set; }

        public string StrIPAddress
        {
            get { return strIPAddress; }
            set { strIPAddress = value; }
        }

        public string StrHost
        {
            get { return strHost; }
            set { strHost = value; }
        }

        /// <summary>
        ///     过期时间
        /// </summary>
        public int Timeout
        {
            get
            {
                if (_timeOut <= 0)
                    _timeOut = 3;
                return _timeOut;
            }
            set
            {
                if (value <= 0)
                    _timeOut = 3;
                _timeOut = value;
            }
        }

        ~CNNWebClient()
        {
            Dispose(false);
        }

        public new void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        public override string ToString()
        {
            return string.Format("{0}-{1}", StrHost, StrIPAddress);
        }

        //public bool RemoteCertificateValidationCallback(object sender, X509Certificate certificate, X509Chain chain, SslPolicyErrors errors)
        //{
        //    return true;
        //}

        /// <summary>
        ///     重写GetWebRequest,添加WebRequest对象超时时间
        /// </summary>
        /// <param name="address"></param>
        /// <returns></returns>
        protected override WebRequest GetWebRequest(Uri address)
        {
            HttpWebRequest NowRequest = null;
            //if (DateTime.Now.Second % 2 == 0)
            //{
            //    //System.Threading.Thread.Sleep(1);
            //    System.GC.Collect();
            //}

            if (!string.IsNullOrEmpty(StrIPAddress))
                address = new Uri(address.Scheme + "://" + StrIPAddress + address.PathAndQuery, true);
            //if (isMobile)
            //    ServicePointManager.ServerCertificateValidationCallback = new RemoteCertificateValidationCallback(this.RemoteCertificateValidationCallback);
            try
            {
                NowRequest = (HttpWebRequest) base.GetWebRequest(address);
            }
            catch (Exception oe)
            {
                NowRequest = (HttpWebRequest) WebRequest.Create(address);
                Console.WriteLine(oe.Message);
            }
            //address.AbsoluteUri.HorspoolIndex("leftTicket") > 0 ? (HttpWebRequest)HttpWebRequest.Create(address.AbsoluteUri) :
            NowRequest.ProtocolVersion = HttpVersion.Version11;
            //是否使用 Nagle 不使用 提高效率 
            NowRequest.ServicePoint.UseNagleAlgorithm = false;
            //最大连接数 
            NowRequest.ServicePoint.ConnectionLimit = 1000;
            //数据是否缓冲 false 提高效率
            NowRequest.AllowWriteStreamBuffering = false;
            NowRequest.ServicePoint.Expect100Continue = false;
            NowRequest.Headers.Add("Accept-Encoding: gzip, deflate, sdch");
            NowRequest.AutomaticDecompression = DecompressionMethods.Deflate | DecompressionMethods.GZip;
            NowRequest.AllowAutoRedirect = false;
            NowRequest.KeepAlive = true;
            if (Is12306)
            {
                StrHost = isMobile ? "mobile.12306.cn" : "kyfw.12306.cn";
                //request.KeepAlive = true;
                if (!isMobile)
                {
                    NowRequest.UserAgent =
                        "Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/43.0.2357.81 Safari/537.36";
                    NowRequest.Referer = "https://kyfw.12306.cn/otn/leftTicket/init";
                    //if (address.AbsoluteUri.ToString().Contains("passcode"))
                    //{
                    //    request.AllowAutoRedirect = false;
                    //}
                    //else
                    //{
                    //    request.Referer = "https://kyfw.12306.cn/otn/leftTicket/init";
                    //}
                }
                else
                {
                    NowRequest.UserAgent =
                        "Mozilla/5.0 (iPhone; CPU iPhone OS 7_0_4 like Mac OS X) AppleWebKit/537.51.1 (KHTML, like Gecko) Mobile/11B554a (407546549)/Worklight/6.0.0";
                    NowRequest.Credentials = CredentialCache.DefaultCredentials;
                }
                if (address.AbsoluteUri.Contains("/query"))
                {
                    NowRequest.Headers.Add("If-None-Match", ServerTime.DateTime.Ticks.ToString());
                    NowRequest.CachePolicy = new HttpRequestCachePolicy(HttpRequestCacheLevel.NoCacheNoStore);
                    //SetHeaderValue(request.Headers, "If-Modified-Since", "0");
                    //strIPAddress = string.Format("{0}.{1}.{2}.{3}", new Random().Next(10, 120), new Random().Next(120, 250), new Random().Next(130, 250), new Random().Next(1, 250));
                    //CommonMethod.SetHeaderValue(request.Headers, "X-Forwarded-For", strIPAddress);
                    //CommonMethod.SetHeaderValue(request.Headers, "Proxy-Client-IP", strIPAddress);
                    //CommonMethod.SetHeaderValue(request.Headers, "WL-Proxy-Client-IP", strIPAddress);
                }
                //CommonMethod.SetHeaderValue(request.Headers, "If-Modified-Since", "Wed, 31 Dec 1969 16:00:00 GMT");
            }
            else
            {
                StrHost = address.Host;
                NowRequest.UserAgent =
                    "Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/43.0.2357.81 Safari/537.36";
            }
            try
            {
                NowRequest.Host = StrHost;
                NowRequest.Timeout = 1000*Timeout;
                //NowRequest.ReadWriteTimeout = 1000;// *Timeout;
            }
            catch
            {
            }
            //if (request.Proxy != null)
            //    request.Proxy = GlobalProxySelection.GetEmptyWebProxy();
            //if (NowRequest.Proxy != null)
            {
                NowRequest.Proxy = null;
            }
            //HttpHelper.SetHeaderValue(request.Headers, "Connection", "Close");
            return NowRequest;
        }

        //protected override WebResponse GetWebResponse(WebRequest request)
        //{
        //    WebResponse result = null;
        //    try
        //    {
        //        result = base.GetWebResponse(request);
        //    }
        //    catch (Exception oe)
        //    {
        //        Console.WriteLine(oe.Message);
        //    }
        //    finally
        //    {
        //        ////request.Abort();
        //        //request = null;
        //    }
        //    return result;
        //}

        private void SetHeaderValue(WebHeaderCollection header, string name, string value)
        {
            var property = typeof (WebHeaderCollection).GetProperty("InnerCollection",
                BindingFlags.Instance | BindingFlags.NonPublic);
            if (property != null)
            {
                var collection = property.GetValue(header, null) as NameValueCollection;
                collection[name] = value;
            }
        }
    }
}