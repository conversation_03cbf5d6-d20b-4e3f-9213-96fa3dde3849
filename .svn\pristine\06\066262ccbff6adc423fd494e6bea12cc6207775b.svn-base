﻿using CommonLib;
using log4net;
using Newtonsoft.Json;
using System;
using System.Collections.Concurrent;
using System.Collections.Specialized;
using System.Diagnostics;
using System.Threading;
using System.Threading.Tasks;
using System.Web;

namespace Code.Process.Common
{
    public class ProcessNew
    {
        #region 构造函数

        static ProcessNew()
        {
            _Log = LogManager.GetLogger("ServicesManager");
        }

        #endregion

        protected static ILog _Log;
        public static bool IsExit = false;

        private static ConcurrentDictionary<string, OcrTimeEntity> OcrTimeCache = new ConcurrentDictionary<string, OcrTimeEntity>();

        public static void InitOcrEngine()
        {
            HanZiOcr.ConstHelper.Init();
            MathOcr.ConstHelper.Init();
            TableOcr.ConstHelper.Init();
            DocOcr.ConstHelper.Init();
            TransOcr.ConstHelper.Init();
            Task.Factory.StartNew(() =>
            {
                BaseRecHelper.ReportToServer();
            });
        }

        /// <summary>
        /// 启动进程
        /// </summary>
        public static void StartProcess(bool isSync = true)
        {
            //var strInfo = "";
            //strInfo +="=============\n"+ HanZiOcr.BaiDuAIRec.GetRecImg();
            //strInfo += "=============\n" + HanZiOcr.BaiDuAPIRec.GetRecImg();
            //strInfo += "=============\n" + HanZiOcr.BaiDuRec.GetRecImg();
            //strInfo += "=============\n" + HanZiOcr.BaiDuTuShuRec.GetRecImg();
            //strInfo += "=============\n" + HanZiOcr.BingImageRec.GetRecImg();
            //strInfo += "=============\n" + HanZiOcr.MicroSoftRec.GetRecImg();
            _Log.Info("消息接收主线程开启成功");
            Task.Factory.StartNew(() =>
            {
                BeginReceiveFileStatusMessage();
            });
            if (isSync)
            {
                Task.Factory.StartNew(() =>
                {
                    BeginReceiveMessage();
                });
            }
            else
            {
                BeginReceiveMessage();
            }
            OcrProcessThread();
        }

        public static void SendOcrResult(OcrContent content)
        {
            content.processVersion = ServerInfo.DtNowVersion.Ticks;
            OcrRsultPool.Add(content);
        }

        public static void ProcessFileResult(ProcessStateEntity content)
        {
            FileResultPool.Add(content);
        }

        /// <summary>
        /// 停止进程
        /// </summary>
        public static void StopProgress()
        {
            try
            {
                IsExit = true;
                _Log.Info("消息接收主线程停止");
            }
            catch (Exception ex)
            {
                _Log.Error(ex.Message);
            }
        }

        /// <summary>
        /// 汇报开始处理池
        /// </summary>
        private static readonly BlockingCollection<CusImageEntity> OcReportPool = new BlockingCollection<CusImageEntity>();

        /// <summary>
        /// OCR处理池
        /// </summary>
        private static readonly BlockingCollection<CusImageEntity> OcrProcessPool = new BlockingCollection<CusImageEntity>();

        /// <summary>
        /// OCR结果池
        /// </summary>
        private static readonly BlockingCollection<OcrContent> OcrRsultPool = new BlockingCollection<OcrContent>();

        /// <summary>
        /// OCR处理池
        /// </summary>
        private static readonly BlockingCollection<ProcessStateEntity> FileResultPool = new BlockingCollection<ProcessStateEntity>();

        private static void OcrProcessThread()
        {
            new Thread(p =>
            {
                try
                {
                    foreach (var processEntity in OcrProcessPool.GetConsumingEnumerable())
                    {
                        CommonProcess.AddToProcess(processEntity);
                    }
                }
                catch { }
            })
            { IsBackground = true, Priority = ThreadPriority.Highest }.Start();
            new Thread(p =>
            {
                try
                {
                    foreach (var processEntity in OcReportPool.GetConsumingEnumerable())
                    {
                        var currentTicks = ServerTime.DateTime.Ticks;
                        // 处理时间记录
                        try
                        {
                            OcrTimeCache.TryGetValue(processEntity.StrIndex, out var timeEntity);
                            timeEntity = timeEntity ?? new OcrTimeEntity();
                            timeEntity.OcrServerReported = currentTicks;
                            OcrTimeCache[processEntity.StrIndex] = timeEntity;
                        }
                        catch { }
                        var strTmp = WebClientSyncExt.GetHtml(ServerInfo.HostAccount?.FullUrl + "code.ashx?op=reportbeginprocess&server=" + HttpUtility.UrlEncode(ConfigHelper.OcrServer) + "&id=" + processEntity.StrIndex + "&start=" + processEntity.DtAdd + "&push=" + processEntity.DtOcrServerGet + "&receive=" + processEntity.DtReceived + "&current=" + currentTicks + "&version=" + ServerInfo.DtNowVersion.Ticks, "", 15);
                        _Log.InfoFormat("反馈Ocr Server Id:{0},Result:{1}", processEntity.StrIndex, strTmp);
                    }
                }
                catch { }
            })
            { IsBackground = true, Priority = ThreadPriority.AboveNormal }.Start();
            new Thread(p =>
            {
                try
                {
                    foreach (var content in OcrRsultPool.GetConsumingEnumerable())
                    {
                        // OCR结果汇报给Server
                        try
                        {
                            if (!string.IsNullOrEmpty(content?.result?.autoText) || content.result?.files?.Count > 0)
                            {
                                try
                                {
                                    OcrTimeCache.TryGetValue(content.id, out var timeEntity);
                                    timeEntity = timeEntity ?? new OcrTimeEntity();
                                    content.OcrTime = timeEntity;
                                    content.OcrTime.OcrServerBegin = content.startTicks;
                                    content.OcrTime.OcrServerEnd = content.endTicks;
                                    content.OcrTime.OcrServerStartGetBase64 = content.startBase64Ticks;
                                    content.OcrTime.OcrServerEndGetBase64 = content.endBase64Ticks;
                                    content.OcrTime.OcrServerResolveCompleted = content.htmlResolveTicks;
                                    content.OcrTime.OcrServerGetHtmlCompleted = content.htmlResolveTicks;
                                    content.OcrTime.OcrServerReportedResult = ServerTime.DateTime.Ticks;
                                }
                                catch { }
                                content.Server = ConfigHelper.OcrServer;
                                Stopwatch stopwatch = Stopwatch.StartNew();
                                var strPost = JsonConvert.SerializeObject(content);
                                var headers = new NameValueCollection
                                {
                                    { "Content-Encoding", "gzip" },
                                    { "Content-Type", "application/octet-stream" }
                                };
                                var result = WebClientSyncExt.GetHtml(ServerInfo.HostAccount?.FullUrl + "code.ashx?op=ocrresult" + "&version=" + ServerInfo.DtNowVersion.Ticks, strPost, 15, headers);
                                _Log.Info(
                                    $"添加OCR结果-{content.processName}:{result},耗时:{stopwatch.ElapsedMilliseconds.ToString("F2")}ms");
                            }
                        }
                        catch (Exception oe)
                        {
                            _Log.Error("添加OCR结果异常！" + oe.Message, oe);
                        }
                    }
                }
                catch { }
            })
            { IsBackground = true, Priority = ThreadPriority.Highest }.Start();
            new Thread(p =>
            {
                try
                {
                    foreach (var content in FileResultPool.GetConsumingEnumerable())
                    {
                        // 文件状态查询结果给Server
                        try
                        {
                            Stopwatch stopwatch = Stopwatch.StartNew();
                            var strPost = JsonConvert.SerializeObject(content);
                            var headers = new NameValueCollection
                        {
                            { "Content-Encoding", "gzip" },
                            { "Content-Type", "application/octet-stream" }
                        };
                            var result = WebClientSyncExt.GetHtml(ServerInfo.HostAccount?.FullUrl + "code.ashx?op=filestateresult" + "&version=" + ServerInfo.DtNowVersion.Ticks, strPost, 15, headers);
                            _Log.Info(
                                $"添加文件状态结果:{result},耗时:{stopwatch.ElapsedMilliseconds.ToString("F2")}ms");
                        }
                        catch (Exception oe)
                        {
                            _Log.Error("添加文件结果异常！" + oe.Message, oe);
                        }
                    }
                }
                catch { }
            })
            { IsBackground = true, Priority = ThreadPriority.AboveNormal }.Start();
        }

        /// <summary>
        /// 接收消息
        /// </summary>
        public static void BeginReceiveMessage()
        {
            //System.Threading.Thread.Sleep(30 * 1000);
            //var bytes = System.IO.File.ReadAllBytes(@"D:\助手\Image\0108\Old\11.jpg");//***********.jpg");
            //var tmp = DaMaLib.ZhuShouDaMaHelper.GetCodeByBytes(bytes, false, SiteFlag.助手);

            Console.WriteLine($"处理线程ID：{Thread.CurrentThread.ManagedThreadId}\n");
            var timeOutSec = 30;
            while (!IsExit)
            {
                try
                {
                    _Log.InfoFormat("{0} 开始接收消息", ServerTime.DateTime.ToString("HH:mm:ss"));
                    try
                    {
                        CusImageEntity img = null;
                        var strWaitOcr = WebClientSyncExt.GetHtml(ServerInfo.HostAccount?.FullUrl + "code.ashx?op=waitocr&timeout=" + timeOutSec
                            + "&server=" + HttpUtility.UrlEncode(ConfigHelper.OcrServer) + "&version=" + ServerInfo.DtNowVersion.Ticks, "", timeOutSec);
                        if (!string.IsNullOrEmpty(strWaitOcr) && strWaitOcr.Length > 50)
                        {
                            var dtReceived = ServerTime.DateTime.Ticks;
                            try
                            {
                                img = JsonConvert.DeserializeObject<CusImageEntity>(strWaitOcr);
                                img.DtReceived = dtReceived;
                            }
                            catch (Exception oe)
                            {
                                _Log.Error("反序列化失败！", oe);
                            }
                        }

                        //    Console.WriteLine(string.Format("Now:{0},DtAdd:{1},Expire:{2}"
                        //, ServerTime.DateTime.ToString("HH:mm:ss fff"), new DateTime(img.DtAdd).ToString("HH:mm:ss fff"), new DateTime(img.DtExpired).ToString("HH:mm:ss fff")));
                        if (img != null && img.IsValidate)
                        {
                            OcrTimeCache.TryAdd(img.StrIndex, new OcrTimeEntity()
                            {
                                UserStartRequest = img.DtUser,
                                ServerReceivedUserRequest = img.DtAdd,
                                ServerAlloted = img.DtOcrServerGet,
                                OcrServerAccepted = img.DtReceived
                            });
                            // 收到消息开始处理
                            OcrProcessPool.Add(img);
                            OcReportPool.Add(img);
                        }
                        else
                        {
                            GC.Collect();
                        }
                        Thread.Sleep(100);
                    }
                    catch (Exception ex)
                    {
                        _Log.Error("接收消息消息并分配线程时出错,错误原因如下:", ex);
                        Thread.Sleep(1000);
                    }
                }
                catch (Exception ex)
                {
                    _Log.Error("轮询消息接收消息时出错,错误原因如下:", ex);
                }
            }
        }

        /// <summary>
        /// 接收消息
        /// </summary>
        public static void BeginReceiveFileStatusMessage()
        {
            Console.WriteLine(string.Format("处理线程ID：{0}\n", System.Threading.Thread.CurrentThread.ManagedThreadId));
            var timeOutSec = 30;
            while (!IsExit)
            {
                try
                {
                    _Log.InfoFormat("{0} 开始接收文件状态消息", ServerTime.DateTime.ToString("HH:mm:ss"));

                    #region 单线程Block

                    try
                    {
                        var dtStart = DateTime.Now.Ticks;
                        CusFileStatusEntity img = null;
                        var strWaitFileState = WebClientSyncExt.GetHtml(ServerInfo.HostAccount?.FullUrl + "code.ashx?op=waitfilestate&timeout=" + timeOutSec
                            + "&server=" + HttpUtility.UrlEncode(ConfigHelper.OcrServer) + "&version=" + ServerInfo.DtNowVersion.Ticks, "", timeOutSec);
                        if (!string.IsNullOrEmpty(strWaitFileState) && strWaitFileState.Length > 10)
                        {
                            try
                            {
                                img = JsonConvert.DeserializeObject<CusFileStatusEntity>(strWaitFileState);
                            }
                            catch (Exception oe)
                            {
                                _Log.Error("反序列化失败！", oe);
                            }
                        }

                        if (img == null)
                        {
                            var maxSleepSec = (int)(timeOutSec * 0.5);
                            if (new TimeSpan(DateTime.Now.Ticks - dtStart).TotalSeconds < maxSleepSec)
                            {
                                _Log.Info("等待时间不足，强制休息！");
                                Thread.Sleep(maxSleepSec);
                            }
                            else
                            {
                                Thread.Sleep(1000);
                            }
                            continue;
                        }
                        Task.Run(() =>
                        {
                            CommonProcess.AddToFileStatusProcess(img);
                        });
                    }
                    catch (Exception ex)
                    {
                        _Log.Error("接收消息消息并分配线程时出错,错误原因如下:", ex);
                        System.Threading.Thread.Sleep(1000);
                    }

                    #endregion
                }
                catch (Exception ex)
                {
                    _Log.Error("轮询消息接收消息时出错,错误原因如下:", ex);
                }
            }
        }

    }
}
