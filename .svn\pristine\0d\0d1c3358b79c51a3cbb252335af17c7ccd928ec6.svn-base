﻿using CommonLib;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Web;

namespace CommonLib
{
    public class FileResultHtmlHelper
    {
        private const string StrOneFileTemplte = "<li onclick=\"javascript:download('【Url】','【Param】');\" title='【FileName】'>【FileName】</li>";

        public static string GetDownLoadHtml(OcrContent content)
        {
            if (content?.result?.files?.Count > 0)
            {
                var strFile = GetFileDownHtml(content.result.files);
                var result = StrOnFileHtmlTemplete
                    .Replace("【Files】", strFile)
                    .Replace("【ocrType】", content.processId.ToString())
                    .Replace("【taskId】", content.result.autoText)
                    .Replace("【isCheck】", "true"
                    );
                return result;
            }
            return null;
        }

        private static string GetFileDownHtml(List<DownLoadInfo> files)
        {
            StringBuilder sbFiles = new StringBuilder();
            files.ForEach(file =>
            {
                var downHtml = StrOneFileTemplte.Replace("【FileName】", file.desc).Replace("【Param】", file.param).Replace("【Url】", file.url);
                sbFiles.Append(downHtml);
            });
            return sbFiles.ToString();
        }

        public const string StrOnFileHtmlTemplete = @"<html>
<head>
<meta charset='UTF-8' http-equiv='X-UA-Compatible' content='IE=edge'>
<style>
#nav{margin: 0 auto;}
ul,li{
padding: 5px;
}
ul{
margin-top:0px;
margin-left:0px;
}
li{
font-size: 30px;
line-height: 1;
cursor:hand;
font-family:suxingme,'Open Sans',Arial,'Hiragino Sans GB','Microsoft YaHei','STHeiti','WenQuanYi Micro Hei',SimSun,sans-serif;
border: 1px solid;
text-align: center;
margin-right: 10px;
margin-bottom: 10px;
color:#0000FF;
display:inline-block;
*display:inline;
*zoom:1;
}
.info {
    padding: 4px 8px;
    background-color: #19B5FE;
    color: #fff;
    font-size: 22px;
    line-height: 1;
    font-weight: 400;
    margin: 3px 5px 3px 0;
    border-radius: 2px;
    display: inline-block;
}
.error {
    padding: 4px 8px;
    background-color: #ff5e5c;
    color: #fff;
    font-size: 28px;
    line-height: 1;
    font-weight: 400;
    margin: 3px 5px 3px 0;
    border-radius: 2px;
    display: inline-block;
}
.success {
    padding: 4px 8px;
    background-color: #1ac756;
    color: #fff;
    font-size: 12px;
    line-height: 1;
    font-weight: 400;
    margin: 3px 5px 3px 0;
    border-radius: 2px;
    display: inline-block;
}
</style>
</head>
<body>
<div id='nav'>
<div class='info' style='margin-top:0px;text-align:left;'>下载地址:</div>
<p id='tipmsg' class='success' style='margin-top:0px;text-align:left;'></p>
<ul id='downlist'>
【Files】
</ul>
</div>
</body>
<script>
function httpPost(URL,PARAMS){
var temp=document.createElement('form');
temp.action=URL;temp.method='post';
temp.style.display = 'none';
for(var x in PARAMS){
var opt=document.createElement('textarea');
opt.name=x;
opt.value=PARAMS[x];
temp.appendChild(opt);
}
document.body.appendChild(temp);
temp.submit();
return temp;
};
function download(url,param){
if(param == ''){
window.rectangle.href=url;
}
else{
var arr=param.split('&');
var params={};for(var i=0;i<arr.length; i++) {
var arr2=arr[i].split('=');params[arr2[0]]=arr2[1];
};
httpPost(url,params);
}
}
function checkStatusFun() {
    var httpRequest = new XMLHttpRequest();
    httpRequest.onreadystatechange = function() {
        if (httpRequest.readyState === 4) {
            if (httpRequest.status === 200) {
                var data = eval('('+httpRequest.responseText+')');
                if (data){
					document.getElementById('tipmsg').setAttribute('class', 'success');
                    document.getElementById('tipmsg').innerText = data.desc;
                    if(data.state == 2){
                        setTimeout(function(){
                            document.getElementById('tipmsg').style.display = 'none';
                        }, 5 * 1000 );
                    }else if(data.state == 3){
						document.getElementById('tipmsg').setAttribute('class', 'error');
                        document.getElementById('downlist').style.display = 'none';
                    }else{
                        setTimeout(function(){
	                    checkStatusFun();
                        }, 3 * 1000 );
                    }
                }
            }
        }
    };
	httpRequest.open('GET', '/Code.ashx?op=fileStaus&taskId=【taskId】&ocrType=【ocrType】&time=' + new Date().getTime(), false);
	httpRequest.setRequestHeader('Content-type','application/json');
	httpRequest.send();
}
var checkStatus = 【isCheck】;
if(checkStatus){
	checkStatusFun();
}
</script>
</html>";
    }
}