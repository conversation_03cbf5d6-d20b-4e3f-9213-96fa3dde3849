﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using Enterprise.Framework.Redis;

namespace BaiDuAPI
{
    public class ListCache : RedisCacheObject<List<string>>
    {
        protected override string CurrentObject_KeyPrefix
        {
            get { return "ListCache"; }
        }

        protected override string Object_DBName
        {
            get { return "OPS_Cache"; }
        }

        public void Add(string strKey, List<string> lstValue)
        {
            if (this.KeyExists(strKey))
                this.Remove(strKey);
            this.Insert(strKey, lstValue);
        }
    }
}