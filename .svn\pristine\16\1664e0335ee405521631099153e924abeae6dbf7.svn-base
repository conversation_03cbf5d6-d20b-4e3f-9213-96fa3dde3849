﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <PackageId>ServiceStack.MsgPack</PackageId>
    <AssemblyName>ServiceStack.MsgPack</AssemblyName>
    <TargetFrameworks>net472;netstandard2.0;net6.0</TargetFrameworks>
    <Title>Message Pack support for ServiceStack. Includes typed MsgPack Client</Title>
    <PackageDescription>
      Add the MsgPack binary format and endpoint to a ServiceStack web service host.
    </PackageDescription>
    <PackageTags>MsgPack;MessagePack;Message;Pack;Fast;Binary;Serializer;Format;ContentType;REST;Web;Services;ServiceStack</PackageTags>
  </PropertyGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\..\ServiceStack.Text\src\ServiceStack.Text\ServiceStack.Text.csproj" />
    <ProjectReference Include="..\ServiceStack.Interfaces\ServiceStack.Interfaces.csproj" />
    <ProjectReference Include="..\ServiceStack.Client\ServiceStack.Client.csproj" />
    <ProjectReference Include="..\ServiceStack.Common\ServiceStack.Common.csproj" />
    <ProjectReference Include="..\ServiceStack\ServiceStack.csproj" />
    <PackageReference Include="MsgPack.Cli" Version="1.0.1" />
  </ItemGroup>

  <ItemGroup Condition=" '$(TargetFramework)' == 'net472' ">
    <Reference Include="System" />
    <Reference Include="Microsoft.CSharp" />
  </ItemGroup>

</Project>
