﻿using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Net;
using System.Net.Security;
using System.Net.Sockets;
using System.Security.Authentication;
using System.Security.Cryptography.X509Certificates;
using System.Text;
using System.Threading;
using System.IO.Compression;
using System.Drawing;
using System.Collections.Specialized;

namespace RegTools
{
    public delegate void AsyncTCPCaller(IPEndPoint remoteEndPoint);
    /// <summary>
    /// HTTP协议头包装
    /// </summary>
    public class HttpHeader
    {
        public HttpHeader()
            : this("")
        {
        }

        public HttpHeader(string url)
        {
            this.Url = url;
        }

        public string Url
        {
            get;
            set;
        }

        public string Host
        {
            get;
            set;
        }

        public string Accept
        {
            get;
            set;
        }

        public string Referer
        {
            get;
            set;
        }

        public string Cookies
        {
            get;
            set;
        }

        public string Body
        {
            get;
            set;
        }
    }

    /// <summary>
    /// HTTP回应包装
    /// </summary>
    public class HttpResponse
    {
        internal HttpResponse(string header,
            byte[] body)
        {
            this.Header = header;
            this.Body = body;
        }

        //暂未将回应HTTP协议头转换为HttpHeader类型
        public string Header
        {
            get;
            set;
        }

        public byte[] Body
        {
            get;
            set;
        }

        private string strBody = string.Empty;

        public string StrBody
        {
            get { return strBody; }
            set { strBody = value; }
        }
    }

    /// <summary>
    /// HttpHelper
    /// </summary>
    public static class HttpHelper
    {
        public static X509CertificateCollection m_X509 = null;

        /// <summary>
        /// 提交方法
        /// </summary>
        enum HttpMethod
        {
            GET,
            POST
        }

        #region Socket

        static bool ValidateServerCertificate(
                 object sender,
                 X509Certificate certificate,
                 X509Chain chain,
                 SslPolicyErrors sslPolicyErrors)
        {
            /*
            if (sslPolicyErrors == SslPolicyErrors.None)
                return true;
            Console.WriteLine("Certificate error: {0}", sslPolicyErrors);
            return false;
            */
            return true;
        }

        private static List<string> lstAgent = new List<string>() {
            "Mozilla/5.0 (Windows; U; Windows NT 5.1; en-US; rv:1.8.1.12)Gecko/20080219 Firefox/2.0.0.12 Navigator/9.0.0.6",
            "Mozilla/5.0 (Windows; U; Windows NT 5.2) AppleWebKit/525.13 (KHTML,like Gecko) Chrome/0.2.149.27 Safari/525.13",
            "Mozilla/5.0 (Windows; U; Windows NT 5.2)AppleWebKit/525.13 (KHTML, like Gecko) Version/3.1Safari/525.13",
            "Mozilla/5.0 (Macintosh; PPC Mac OS X; U; en)Opera 8.0",
            "Opera/9.27 (Windows NT 5.2; U; zh-cn)",
            "Mozilla/5.0 (Windows; U; Windows NT 5.2)Gecko/2008070208 Firefox/3.0.1",
            "Mozilla/4.0 (compatible; MSIE 8.0; Windows NT6.0)",
            "Mozilla/5.0 (compatible; MSIE 10.0; Windows NT 6.1; WOW64; Trident/6.0;)"
        };

        //public static string GetHtml(string url, string cookie, string post = "", string refer = "")
        //{
        //    return GetHtml(url, ref cookie, post, refer);
        //}

        //public static string GetHtml(string url, string cookie, string post = "", int retryCount = 0)
        //{
        //    return GetHtml(url, cookie, post, "", retryCount);
        //}

        public static string GetHtml(string url, int retryCount = 0, int timeOut = 0)
        {
            return GetHtml(url, "", "", "", retryCount, timeOut);
        }

        public static string GetHtml(string url, string cookie, string post = "", string refer = "", int retryCount = 0, int timeOut = 0)
        {
            string strTmp = string.Empty;
            retryCount = retryCount <= 0 ? 1 : retryCount;
            for (int i = 0; i < retryCount; i++)
            {
                strTmp = GetHtml(url, ref cookie, post, refer, timeOut);
                if (!string.IsNullOrEmpty(strTmp))
                    break;
            }
            return strTmp;
        }

        public static string GetHtml(string url, ref string cookie, string post = "", string refer = "", int timeOut = 0)
        {
            string html = string.Empty;
            var request = CreateRequest(!string.IsNullOrEmpty(post), url, cookie, refer, timeOut <= 0 ? 3000 : timeOut, timeOut <= 0 ? 3000 : timeOut);
            try
            {
                if (!string.IsNullOrEmpty(post))
                {
                    try
                    {
                        byte[] bs = Encoding.UTF8.GetBytes(post);
                        using (Stream reqStream = request.GetRequestStream())
                        {
                            reqStream.Write(bs, 0, bs.Length);
                        }
                    }
                    catch (Exception oe)
                    {
                        Console.WriteLine(oe.Message);
                    }
                }
                using (HttpWebResponse response = (HttpWebResponse)request.GetResponse())
                {
                    if (response.Headers["Set-Cookie"] != null && !string.IsNullOrEmpty(response.Headers["Set-Cookie"].ToString()))
                    {
                        cookie = response.Headers["Set-Cookie"].ToString().Replace("Path=/otn", " ").Replace("path=/", "").Replace(",", "").Trim();
                    }
                    if (url.Contains("baidu") && !string.IsNullOrEmpty(response.Headers["Date"]))//header.Url.Contains("12306") ||
                    {
                        try
                        {
                            html = response.Headers["Date"];
                        }
                        catch { }
                    }
                    if (!url.Contains("baidu"))
                    {
                        using (Stream stream = response.GetResponseStream())
                        {
                            Encoding encoding = Encoding.UTF8;
                            if (response.ContentEncoding.ToLower().Contains("gzip"))
                            {
                                using (GZipStream gZipStream = new GZipStream(stream, CompressionMode.Decompress))
                                {
                                    using (StreamReader streamReader = new StreamReader(gZipStream, encoding))
                                    {
                                        html = streamReader.ReadToEnd();
                                    }
                                }
                            }
                            else if (response.ContentEncoding.ToLower().Contains("deflate"))
                            {
                                using (DeflateStream deflateStream = new DeflateStream(stream, CompressionMode.Decompress))
                                {
                                    using (StreamReader streamReader2 = new StreamReader(deflateStream, encoding))
                                    {
                                        html = streamReader2.ReadToEnd();
                                    }
                                }
                            }
                            else
                            {
                                using (StreamReader ss = new StreamReader(stream, encoding))
                                {
                                    html = ss.ReadToEnd();
                                }
                            }
                        }
                    }
                }
            }
            catch (Exception oe)
            {
                Console.WriteLine("=====================\n" + oe.Message + "\n====================");
            }
            return html;
        }

        public static Bitmap RequestImage(string url, ref string cookie)
        {
            var request = CreateRequest(false, url, cookie, "", 5000, 5000);
            try
            {
                using (WebResponse response = request.GetResponse())
                {
                    if (response != null && response.Headers["Set-Cookie"] != null && !string.IsNullOrEmpty(response.Headers["Set-Cookie"].ToString()))
                    {
                        cookie = response.Headers["Set-Cookie"].ToString().Replace("Path=/otn", " ").Replace("path=/", "").Replace(",", "").Trim();
                    }
                    using (Stream stream = response.GetResponseStream())
                    {
                        return new Bitmap(stream);
                    }
                }
            }
            catch (Exception oe)
            {
                Console.WriteLine(oe.Message);
            }
            return null;
        }

        public static byte[] RequestNoSyncImage(string url, ref  string cookie)
        {
            var request = CreateRequest(false, url, cookie, "", 5000, 5000);
            try
            {
                using (WebResponse response = request.GetResponse())
                {
                    if (response != null && response.Headers["Set-Cookie"] != null && !string.IsNullOrEmpty(response.Headers["Set-Cookie"].ToString()))
                    {
                        cookie = response.Headers["Set-Cookie"].ToString().Replace("Path=/otn", " ").Replace("path=/", "").Replace(",", "").Trim();
                    }
                    using (Stream stream = response.GetResponseStream())
                    {
                        byte[] buffer = new byte[2500];
                        int bytesRead;
                        MemoryStream ms = new MemoryStream();
                        bool finished = false;
                        while (!finished)
                        {
                            bytesRead = stream.Read(buffer, 0, buffer.Length);
                            if (bytesRead > 0)
                            {
                                ms.Write(buffer, 0, bytesRead);
                            }
                            else
                            {
                                finished = true;
                            }
                        }
                        return ms.ToArray();
                        //MemoryStream ms = new MemoryStream();
                        //new Bitmap(stream).Save(ms, System.Drawing.Imaging.ImageFormat.Gif);
                        //return ms.GetBuffer();
                    }
                }
            }
            catch (Exception oe)
            {
                Console.WriteLine(oe.Message);
            }
            return null;
        }

        public static HttpWebRequest CreateRequest(bool isPost, string url, string cookieContainer, string referer, int timeout = -1, int readWriteTimeout = -1)
        {
            Uri uri = new Uri(url);
            HttpWebRequest req = WebRequest.Create(uri) as HttpWebRequest;
            req.Method = isPost ? "POST" : "GET";

            req.ContentType = "application/x-www-form-urlencoded";
            req.Headers.Add("Accept-Language: zh-cn");
            req.Headers.Add("Accept-Encoding: gzip, identity");
            req.Headers.Add("Pragma", "no-cache");
            req.Headers.Add("If-None-Match", DateTime.Now.Ticks.ToString());
            if (url.IndexOf("confirmPassenger") < 0)
                req.Headers.Add("x-requested-with", "XMLHttpRequest");
            req.IfModifiedSince = new DateTime(1970, 1, 1);
            req.KeepAlive = true;
            req.UserAgent = "Mozilla/5.0 (compatible; MSIE 9.0; Windows NT 6.1; WOW64; Trident/5.0; MASP)";
            req.ProtocolVersion = HttpVersion.Version11;

            //req.Accept = "text/html,application/json,text/javascript,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8";
            //req.Headers[HttpRequestHeader.CacheControl] = "no-cache";
            if (!string.IsNullOrEmpty(referer))
            {
                req.Referer = referer;
            }
            SetHeaderValue(req.Headers, "Host", GetHost(url));
            if (!string.IsNullOrEmpty(cookieContainer))
            {
                req.Headers["Cookie"] = cookieContainer.Trim();
            }
            //req.Headers["Accept-Charset"] = "GBK,utf-8;q=0.7,*;q=0.3";
            //req.Headers["If-None-Match"] = "635166035939370989";
            req.Timeout = timeout > 0 ? timeout : 3000;
            req.ReadWriteTimeout = timeout > 0 ? timeout : 3000;
            //req.Headers["X-Forwarded-For"] = string.Format("{0}.{1}.{2}.{3}", new Random().Next(10, 120), new Random().Next(120, 250), new Random().Next(130, 250), new Random().Next(1, 250));
            //SetHeaderValue(req.Headers, "X-Forwarded-For", string.Format("{0}.{1}.{2}.{3}", new Random().Next(10, 120), new Random().Next(120, 250), new Random().Next(130, 250), new Random().Next(1, 250)));
            req.AllowAutoRedirect = true;
            SetHeaderValue(req.Headers, "Connection", "Close");
            //SetHeaderValue(req.Headers, "Expect", "100-continue");
            //req.ServicePoint.Expect100Continue = true;
            //req.ServicePoint.UseNagleAlgorithm = false;
            //req.ServicePoint.ConnectionLimit = 65500;
            //req.AllowWriteStreamBuffering = false;
            return req;
        }

        public static void SetHeaderValue(WebHeaderCollection header, string name, string value)
        {
            var property = typeof(WebHeaderCollection).GetProperty("InnerCollection",
                System.Reflection.BindingFlags.Instance | System.Reflection.BindingFlags.NonPublic);
            if (property != null)
            {
                var collection = property.GetValue(header, null) as NameValueCollection;
                collection[name] = value;
            }
        }

        public static byte[] GetImageBuffer(IPEndPoint endpoint, HttpHeader header, ref string strCookie)
        {
            HttpResponse response = GetResponse(HttpMethod.GET, endpoint, header, m_X509, ref strCookie);
            return response.Body;
        }

        public delegate void MethodHandler();
        public static void InvokeNoneException(MethodHandler method, int timeOut = 0)
        {
            bool isFinish = false;
            try
            {
                Stopwatch stopwatch = new Stopwatch();
                Thread thread = new Thread(delegate()
                 {
                     try
                     {
                         method();
                         isFinish = true;
                     }
                     catch (Exception oe)
                     {
                         Console.WriteLine(oe.Message);
                     }
                 });
                thread.Priority = ThreadPriority.Highest;
                thread.IsBackground = true;

                stopwatch.Start();
                thread.Start();
                while (timeOut > 0 && !isFinish && stopwatch.ElapsedMilliseconds < (long)timeOut)
                {
                    Thread.Sleep(100);
                }
                stopwatch.Stop();
                try
                {
                    if (thread != null && thread.IsAlive)
                    {
                        thread.Abort();
                    }
                }
                catch (Exception oe)
                {
                    Console.WriteLine(oe.Message);
                }
            }
            catch (Exception oe)
            {
                Console.WriteLine(oe.Message);
            }
        }

        public static string Get(string url)
        {
            bool isCache = false;
            string strCookie = "";
            return Get(GetIPEndPoint(url), new HttpHeader() { Url = url }, null, ref strCookie, out isCache);
        }

        public static string Get(IPEndPoint endpoint, string url)
        {
            bool isCache = false;
            string strCookie = "";
            return Get(endpoint, new HttpHeader() { Url = url }, m_X509, ref strCookie, out isCache);
        }

        public static string Get(IPEndPoint endpoint,
            HttpHeader header, ref string strCookie)
        {
            bool isCache = false;
            return Get(endpoint, header, m_X509, ref strCookie, out isCache);
        }

        public static string Get(IPEndPoint endpoint,
            HttpHeader header, ref string strCookie, out bool isCache, int timeOut = 0)
        {
            string html = "";
            if (timeOut < 0)
            {
                isCache = false;
                string cookie = strCookie;
                bool cache = false;
                InvokeNoneException(delegate()
                {
                    html = Get(endpoint, header, m_X509, ref cookie, out cache);
                }, timeOut);
                isCache = cache;
            }
            else
            {
                html = Get(endpoint, header, m_X509, ref strCookie, out isCache);
            }
            return html;
        }

        public static string Get(IPEndPoint endpoint,
            HttpHeader header, ref string strCookie, out bool isCache)
        {
            return Get(endpoint, header, m_X509, ref strCookie, out isCache);
        }

        public static string Get(IPEndPoint endpoint,
            HttpHeader header,
            X509CertificateCollection x509certs, ref string strCookie, out bool isCache)
        {
            return InternalSslSocketHttp(HttpMethod.GET, endpoint, header, x509certs, ref strCookie, out isCache);
        }

        public static string Get(IPEndPoint endpoint,
            HttpHeader header,
            X509CertificateCollection x509certs, ref string strCookie)
        {
            bool isCache = false;
            return InternalSslSocketHttp(HttpMethod.GET, endpoint, header, x509certs, ref strCookie, out isCache);
        }

        public static string Post(IPEndPoint endpoint,
            HttpHeader header, ref string strCookie)
        {
            return Post(endpoint, header, m_X509, ref strCookie);
        }

        public static string Post(IPEndPoint endpoint,
            HttpHeader header,
            X509CertificateCollection x509certs, ref string strCookie)
        {
            bool isCache = false;
            return Post(endpoint, header, x509certs, ref strCookie, out isCache);
        }

        public static string Post(IPEndPoint endpoint,
            HttpHeader header,
            X509CertificateCollection x509certs, ref string strCookie, out bool isCache)
        {
            return InternalSslSocketHttp(HttpMethod.POST, endpoint, header, x509certs, ref strCookie, out isCache);
        }

        static string GetHeader(string Name, string all)
        {
            string strTmp = string.Empty;
            if (all.IndexOf(Name) >= 0)
            {
                all = all.Substring(all.IndexOf(Name) + Name.Length).Trim();
                try
                {
                    strTmp = all.Substring(0, all.IndexOf("\n")).Trim();
                }
                catch (Exception oe)
                {
                    Console.WriteLine(oe.Message);
                }
            }
            if (strTmp != null && strTmp.ToLower().StartsWith("gbk"))
            {
                strTmp = "gb2312";
            }
            return strTmp;
        }

        static string InternalSslSocketHttp(HttpMethod method,
            IPEndPoint endpoint,
            HttpHeader header,
            X509CertificateCollection x509certs, ref string strCookie, out bool isCache)
        {
            isCache = false;
            HttpResponse response = GetResponse(method, endpoint, header, x509certs, ref strCookie);
            string html = string.Empty;
            if (response != null)
            {
                if (response.Body != null && !response.Header.Contains("403 Forbidden") && !response.Header.Contains("503 Service Unavailable"))
                {
                    if (response.Header.Contains("Set-Cookie:") && header.Url.Contains("12306"))
                    {
                        html = GetCookies(response.Header);
                        if (!string.IsNullOrEmpty(html))
                        {
                            strCookie = html;
                        }
                        html = string.Empty;
                    }
                    if (header.Url.Contains("12306") && response.Header.Contains("Age:"))
                        isCache = true;
                    //if (response.Header.Contains("\r\nDate:") && (header.Url.Contains("time")))//header.Url.Contains("12306") ||
                    //{
                    //    try
                    //    {
                    //        string strDate = response.Header.Substring(response.Header.IndexOf("\r\nDate:") + "\r\nDate:".Length);
                    //        CommonString.serverTime = DateTime.Parse(strDate.Substring(0, strDate.IndexOf("\r")));
                    //    }
                    //    catch { }
                    //}
                    html = GetStringByByte(response);
                    response.StrBody = html;
                }
                else
                {
                }
            }
            if (response != null)
            {
                try
                {
                    response = null;
                }
                catch (Exception oe)
                {
                    Console.WriteLine(oe.Message);
                }
            }
            return html;
        }

        private static string GetStringByByte(HttpResponse response)
        {
            string html = string.Empty;
            try
            {
                MemoryStream tmpStream = new MemoryStream(response.Body);
                if (response.Header.IndexOf("gzip", StringComparison.OrdinalIgnoreCase) >= 0)
                {
                    tmpStream = unGzip(tmpStream);
                }
                Encoding encoding = Encoding.UTF8;
                string strCharSet = "";
                if (response.Header.IndexOf("charset=") > 0)
                    strCharSet = GetHeader("charset=", response.Header);
                strCharSet = string.IsNullOrEmpty(strCharSet) ? "UTF-8" : strCharSet;
                try
                {
                    encoding = Encoding.GetEncoding(strCharSet);
                }
                catch (Exception oe)
                {
                    encoding = Encoding.GetEncoding("UTF-8");
                    Debug.WriteLine(oe.Message);
                }
                if (tmpStream != null)
                {
                    html = encoding.GetString(tmpStream.ToArray());
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine(ex.Message);
            }
            return html;
        }

        /// <summary>
        /// 解压数据流
        /// </summary>
        /// <param name="data">数据流, 压缩或未压缩的.</param>
        /// <returns>返回解压缩的数据流</returns>
        //private static MemoryStream unGzip(MemoryStream data)
        //{
        //    if (data == null)
        //    {
        //        throw new ArgumentNullException("data cannot be null.", "data");
        //    }

        //    data.Seek(0, SeekOrigin.Begin);
        //    MemoryStream result = data;

        //    GZipStream gs = new GZipStream(data, CompressionMode.Decompress);
        //    result = new MemoryStream(1024);

        //    try
        //    {
        //        byte[] buffer = new byte[1024];
        //        int length = -1;

        //        do
        //        {
        //            length = gs.Read(buffer, 0, buffer.Length);
        //            result.Write(buffer, 0, length);
        //        }
        //        while (length != 0);

        //        gs.Flush();
        //        result.Flush();
        //    }
        //    finally
        //    {
        //        gs.Close();
        //    }

        //    return result;
        //}

        /// <summary>  
        /// ZIP解压  
        /// </summary>  
        /// <param name="zippedData"></param>  
        /// <returns></returns>  
        private static MemoryStream unGzip(MemoryStream ms)
        {
            MemoryStream outBuffer = new MemoryStream();
            try
            {
                using (GZipStream compressedzipStream = new GZipStream(ms, CompressionMode.Decompress))
                {
                    byte[] block = new byte[1024];
                    while (true)
                    {
                        int bytesRead = compressedzipStream.Read(block, 0, block.Length);
                        if (bytesRead <= 0)
                            break;
                        else
                            outBuffer.Write(block, 0, bytesRead);
                    }
                    compressedzipStream.Close();
                }
            }
            catch (Exception oe)
            {
                Console.WriteLine(oe.Message);
            }
            return outBuffer;
        }

        private static readonly ManualResetEvent TimeoutObject = new ManualResetEvent(false);

        /// <summary>
        /// 异步回调方法
        /// </summary>
        /// <param name="asyncresult"></param>
        public static void CallBackMethod(IAsyncResult ar)
        {
            //终止等待，使阻塞的线程继续
            TimeoutObject.Set();
        }

        static HttpResponse GetResponse(HttpMethod method,
            IPEndPoint endpoint,
            HttpHeader header,
            X509CertificateCollection x509certs, ref string strCookie, bool isImage = false)
        {
            TcpClient tcp = new TcpClient();
            HttpResponse response = null;
            try
            {
                tcp.SendTimeout = 1000;
                tcp.ReceiveTimeout = 1000;

                // Create the delegate. Initiate the asychronous call.
                IAsyncResult result = new AsyncTCPCaller(tcp.Connect).BeginInvoke(endpoint, null, null);
                result.AsyncWaitHandle.WaitOne(2500);

                try
                {
                    result = null;
                    //// Close the wait handle.
                    //result.AsyncWaitHandle.Close();
                }
                catch (Exception oe)
                {
                    Console.WriteLine(oe.Message);
                }

                header.Cookies = strCookie;

                //tcp.Connect(endpoint);

                if (tcp != null && tcp.Connected)
                {
                    byte[] buff = ParseHttpHeaderToBytes(method, header);  //生成协议包
                    if (x509certs == null && header.Url.IndexOf("12306.cn") > 0)
                    {
                        x509certs = m_X509;
                    }
                    if (x509certs != null)
                    {
                        using (SslStream ssl = new SslStream(tcp.GetStream(),
                                                false,
                                                new RemoteCertificateValidationCallback(ValidateServerCertificate),
                                                null))
                        {
                            ssl.AuthenticateAsClient("SslServerName",
                                x509certs,
                                SslProtocols.Default,
                                false);
                            if (ssl.IsAuthenticated)
                            {
                                ssl.Write(buff, 0, buff.Length);
                                ssl.Flush();
                                response = ReadResponse(ssl);
                            }
                        }
                    }
                    else
                    {
                        using (NetworkStream ns = tcp.GetStream())
                        {
                            ns.Write(buff, 0, buff.Length);
                            ns.Flush();
                            response = ReadResponse(ns);
                        }
                    }
                }
            }
            catch (System.Security.Authentication.AuthenticationException ex)
            {
                Console.WriteLine(ex.Message);
            }
            catch (Exception ex)
            {
                Console.WriteLine(ex.Message);
            }
            try
            {
                if (tcp != null && !tcp.Connected && endpoint.Port.Equals(443))
                {
                    if (response != null && !string.IsNullOrEmpty(response.Header) && response.Header.IndexOf(" 302 Moved Temporarily") > 0)
                    {
                        header.Url = GetLocation(response.Header);
                        if (!string.IsNullOrEmpty(header.Url))
                            return GetResponse(method, endpoint, header, x509certs, ref strCookie);
                    }
                    //else if (response == null || response.Body == null || response.Body.Length <= 0)
                    //{
                    //    CommonMethod.AutoChangeIP(endpoint.Address.ToString());
                    //}
                }
                if (tcp != null)
                {
                    tcp.Close();
                }
                tcp = null;
            }
            catch (Exception ex)
            {
                Console.WriteLine(ex.Message);
            }
            return response;
        }

        private static HttpResponse ReadResponse(Stream sm)
        {
            HttpResponse response = null;
            byte[] buff = null;
            string header = ReadHeaderProcess(sm, out buff);
            if (!string.IsNullOrEmpty(header))
            {
                int start = header.IndexOf("TRANSFER-ENCODING: CHUNKED", StringComparison.OrdinalIgnoreCase);
                if (start > 0 && buff.Length > 0)
                {
                    buff = ChunkedReadResponse(new MemoryStream(buff));
                }
                response = new HttpResponse(header, buff);
            }
            return response;
        }

        //static string ReadHeaderProcess(Stream sm)
        //{
        //    StringBuilder bulider = new StringBuilder();
        //    string strTmp = "";
        //    while (!string.IsNullOrEmpty(strTmp = ReadLine(sm)))
        //    {
        //        bulider.AppendLine(strTmp);
        //        continue;
        //        try
        //        {
        //            int read = sm.ReadByte();
        //            if (read != -1)
        //            {
        //                byte b = (byte)read;
        //                bulider.Append((char)b);
        //                string temp = bulider.ToString();
        //                if (temp.EndsWith("\r\n\r\n"))//Http协议头尾
        //                {
        //                    break;
        //                }
        //            }
        //            else
        //            {
        //                break;
        //            }
        //        }
        //        catch (Exception ex)
        //        {
        //            Debug.WriteLine(ex.Message);
        //            break;
        //        }
        //    }
        //    return bulider.ToString();
        //}

        ///*
        //* 注意：该方法仅供测试，实际使用时请根据需要定制
        //*/
        //static byte[] SpecialReadResponse(Stream sm)
        //{
        //    ArrayList array = new ArrayList();
        //    StringBuilder bulider = new StringBuilder();
        //    int length = 0;
        //    DateTime now = DateTime.Now;
        //    while (true)
        //    {
        //        byte[] buff = new byte[1024 * 10];
        //        int len = sm.Read(buff, 0, buff.Length);
        //        if (len > 0)
        //        {
        //            length += len;
        //            byte[] reads = new byte[len];
        //            Array.Copy(buff, 0, reads, 0, len);
        //            array.Add(reads);
        //            bulider.Append(Encoding.Default.GetString(reads));
        //        }
        //        else
        //        {
        //            break;
        //        }
        //        string temp = bulider.ToString();
        //        if (temp.ToUpper().Contains("</HTML>"))
        //        {
        //            break;
        //        }
        //        if (DateTime.Now.Subtract(now).TotalSeconds >= 30)
        //        {
        //            break;//超时30秒则跳出
        //        }
        //    }
        //    byte[] bytes = new byte[length];
        //    int index = 0;
        //    for (int i = 0; i < array.Count; i++)
        //    {
        //        byte[] temp = (byte[])array[i];
        //        Array.Copy(temp, 0, bytes,
        //            index, temp.Length);
        //        index += temp.Length;
        //    }
        //    return bytes;
        //}

        private static string ReadLine(Stream stream)
        {
            StringBuilder bulider = new StringBuilder(256);
            while (true)
            {
                int b = stream.ReadByte();
                if (b == -1)
                {
                    return "";
                }
                if (b == 10)
                {
                    break;
                }
                if (b != 13)
                {
                    bulider.Append((char)b);
                }
            }
            return bulider.ToString().Trim();
        }

        /// <summary>
        /// 查找html开始部分(去除了html头)\r\n\r\n后面第一个byte的位置
        /// </summary>
        /// <param name="szHtmlBuffer">接受到的byte数组</param>
        /// <returns>第一个byte位置</returns>
        private static int FindBodyStartPosition(byte[] szHtmlBuffer)
        {
            for (int i = 0; i < szHtmlBuffer.Length - 3; ++i)
            {
                if (szHtmlBuffer[i] == 13 && szHtmlBuffer[i + 1] == 10 && szHtmlBuffer[i + 2] == 13 && szHtmlBuffer[i + 3] == 10)
                    return i + 4;
            }
            return -1;
        }

        private static string ReadHeaderProcess(Stream stream, out byte[] body)
        {
            string strHead = string.Empty;
            int iTotalCount = 0;
            body = null;
            if (stream != null)
            {
                try
                {
                    body = GetSSLStreamByte(stream, ref iTotalCount);
                }
                catch (Exception oe)
                {
                    Console.WriteLine(oe.Message);
                }
                //iTotalCount = body.Length;
                if (body != null && body.Length > 0)
                {
                    strHead = Encoding.UTF8.GetString(body).Replace("\r\n\r\nHTTP/1.1", "\r\nHTTP/1.1");

                    if (!string.IsNullOrEmpty(strHead)
                        && strHead.IndexOf("TRANSFER-ENCODING: CHUNKED", StringComparison.OrdinalIgnoreCase) < 0
                        && strHead.IndexOf("gzip", StringComparison.OrdinalIgnoreCase) < 0
                        && strHead.IndexOf("\r\n\r\n") > 0)
                    {
                        body = Encoding.UTF8.GetBytes(strHead.Substring(strHead.IndexOf("\r\n\r\n") + "\r\n\r\n".Length));
                    }
                    else
                    {
                        int iStart = FindBodyStartPosition(body);
                        if (iStart > 0)
                        {
                            //System.Windows.Forms.MessageBox.Show(strHead);
                            strHead = strHead.Substring(0, iStart);
                            if (!string.IsNullOrEmpty(strHead))
                            {
                                if (strHead.IndexOf("\r\nLocation:") < 0)
                                    body = GetByteByStartIndex(body, ref iTotalCount, iStart);
                            }
                        }
                    }
                }
            }
            return strHead;
        }

        private static byte[] GetByteByStartIndex(byte[] responseBytes, ref int iTotalCount, int iStart)
        {
            if (iStart == iTotalCount)
            {
                return null;
            }
            List<byte> lstTmp = new List<byte>();
            lstTmp.AddRange(responseBytes);
            lstTmp.RemoveRange(0, iStart);
            //Array.Copy(responseBytes, iStart, szSwapBuffer, 0, iTotalCount);
            //总长度不再计算头http头部了
            iTotalCount -= iStart;
            return lstTmp.ToArray();
        }

        static byte[] ChunkedReadResponse(Stream sm)
        {
            List<byte> lstAll = new List<byte>();
            try
            {
                int chunked = 0;
                int ntmp = 0;
                while ((chunked = GetChunked(sm)) > 0)
                {
                    ntmp = 0;
                    byte[] buff = new byte[chunked];
                    try
                    {
                        int inread = sm.Read(buff, 0, buff.Length);
                        while (inread < buff.Length && ntmp < 10)
                        {
                            ntmp++;
                            inread += sm.Read(buff, inread, buff.Length - inread);
                        }
                        lstAll.AddRange(buff);
                        if (sm.ReadByte() != -1)//读取段末尾的\r\n
                        {
                            sm.ReadByte();
                        }
                    }
                    catch (Exception)
                    {
                        break;
                    }
                    //chunked = GetChunked(sm);
                }
            }
            catch (Exception oe)
            {
                Console.WriteLine(oe.Message);
            }
            return lstAll == null ? null : lstAll.ToArray();
        }

        private static int GetChunked(Stream stream)
        {
            int nTmp = 0;
            try
            {
                nTmp = Convert.ToInt32(ReadLine(stream), 16);
            }
            catch (Exception oe)
            {
            }
            return nTmp;
        }

        private static byte[] GetNetWorkStreamByte(Stream instream, ref int iTotalCount)
        {
            List<byte> array = new List<byte>();
            const int bufferLen = 1024;
            byte[] buffer = new byte[bufferLen];
            int count = -1;
            try
            {
                while ((count = instream.Read(buffer, 0, bufferLen)) > 0)
                {
                    if (count != bufferLen)
                    {
                        List<byte> tmp = new List<byte>();
                        tmp.AddRange(buffer);
                        if (tmp.Count > count)
                        {
                            tmp.RemoveRange(count, buffer.Length - count);
                        }
                        array.AddRange(tmp.ToArray());
                    }
                    else
                        array.AddRange(buffer);
                    iTotalCount += count;
                    //if ((count == bufferLen && ((NetworkStream)instream).DataAvailable))
                    //    count = instream.Read(buffer, 0, bufferLen);
                    //else
                    //    count = 0;
                }
            }
            catch (Exception oe)
            {
                Console.WriteLine(oe.Message);
            }
            array = array == null ? new List<byte>() : array;
            return array.ToArray();
        }

        static byte[] GetSSLStreamByte(Stream sslStream, ref int iTotalCount)
        {
            byte[] buffer = new byte[5120];
            MemoryStream stream = new MemoryStream();
            int bytes = -1;
            try
            {
                while ((bytes = sslStream.Read(buffer, 0, buffer.Length)) > 0)
                {
                    // Use "bytes" instead of "buffer.Length" here
                    stream.Write(buffer, 0, bytes);
                }
            }
            catch (Exception oe)
            {
                Console.WriteLine(oe.Message);
            }
            return stream.ToArray();
            //List<byte> array = new List<byte>();
            //const int bufferLen = 1024;
            //byte[] buffer = new byte[bufferLen];
            //int count = -1;
            //try
            //{
            //    do
            //    {
            //        //if (((SslStream)sslStream).DataAvailable)
            //        {
            //            count = sslStream.Read(buffer, 0, buffer.Length);
            //            iTotalCount += count;
            //            if (count != bufferLen)
            //            {
            //                List<byte> tmp = new List<byte>();
            //                tmp.AddRange(buffer);
            //                if (tmp.Count > count)
            //                {
            //                    tmp.RemoveRange(count, buffer.Length - count);
            //                }
            //                array.AddRange(tmp.ToArray());
            //            }
            //            else
            //                array.AddRange(buffer);
            //        }
            //    }
            //    while (count > 0);
            //}
            //catch (Exception oe)
            //{
            //    Console.WriteLine(oe.Message);
            //}
            //array = array == null ? new List<byte>() : array;
            //return array.ToArray();
        }
        //对于网络流，我们无法获取其长度。
        //故而从网上找了这个方法，以一种使用指定缓存长度的方式读取流。
        public static byte[] Read2Buffer(Stream stream, int BufferLen)
        {
            // 如果指定的无效长度的缓冲区，则指定一个默认的长度作为缓存大小
            if (BufferLen < 1)
            {
                BufferLen = 0x8000;
            }

            // 初始化一个缓存区
            byte[] buffer = new byte[BufferLen];
            int read = 0;
            int block;
            try
            {
                // 每次从流中读取缓存大小的数据，直到读取完所有的流为止
                while ((block = stream.Read(buffer, read, buffer.Length - read)) > 0)
                {
                    // 重新设定读取位置
                    read += block;

                    // 检查是否到达了缓存的边界，检查是否还有可以读取的信息
                    if (read == buffer.Length)
                    {
                        // 尝试读取一个字节
                        int nextByte = stream.ReadByte();

                        // 读取失败则说明读取完成可以返回结果
                        if (nextByte == -1)
                        {
                            return buffer;
                        }

                        // 调整数组大小准备继续读取
                        byte[] newBuf = new byte[buffer.Length * 2];
                        Array.Copy(buffer, newBuf, buffer.Length);
                        newBuf[read] = (byte)nextByte;

                        // buffer是一个引用（指针），这里意在重新设定buffer指针指向一个更大的内存
                        buffer = newBuf;
                        read++;
                    }
                }
            }
            catch (Exception oe)
            {
                Console.WriteLine(oe.Message);
            }
            // 如果缓存太大则使用ret来收缩前面while读取的buffer，然后直接返回
            byte[] ret = new byte[read];
            Array.Copy(buffer, ret, read);
            return ret;
        }

        #endregion

        #region  Helper

        /// <summary>
        /// 将HTTP协议头转换为Bytes数据
        /// </summary>
        /// <param name="method">HTTP方法</param>
        /// <param name="header">HTTP协议头</param>
        /// <returns>Bytes数据</returns>
        static byte[] ParseHttpHeaderToBytes(HttpMethod method, HttpHeader header)
        {
            StringBuilder bulider = new StringBuilder(256);
            if (method.Equals(HttpMethod.POST))
            {
                bulider.AppendLine(string.Format("POST {0} HTTP/1.1",
                    header.Url.Replace("http://kyfw.12306.cn", "").Replace("https://kyfw.12306.cn", "")));
            }
            else
            {
                bulider.AppendLine(string.Format("GET {0} HTTP/1.0",
                header.Url.Replace("http://kyfw.12306.cn", "").Replace("https://kyfw.12306.cn", "")));
            }
            bulider.AppendLine("Content-Type: application/x-www-form-urlencoded");
            if (!string.IsNullOrEmpty(header.Accept))
            {
                bulider.AppendLine(string.Format("Accept:{0}",
                 header.Accept));
            }
            else
            {
                //if (header.Url.IndexOf("submutOrderRequest") < 0 && header.Url.IndexOf("checkOrderInfo") < 0)
                {
                    bulider.AppendLine("Accept: */*");
                }
                //else
                //{
                //    bulider.AppendLine("Accept: image/gif, image/jpeg, image/pjpeg, image/pjpeg, application/x-shockwave-flash, application/vnd.ms-excel, application/vnd.ms-powerpoint, application/msword, application/x-ms-application, application/x-ms-xbap, application/vnd.ms-xpsdocument, application/xaml+xml, */*");
                //}
            }
            bulider.AppendLine("Accept-Language: zh-cn");
            bulider.AppendLine("Accept-Encoding: gzip, identity");
            bulider.AppendLine("Cache-Control: no-cache");
            if (!string.IsNullOrEmpty(header.Referer))
                bulider.AppendLine(string.Format("Referer: {0}",
                    header.Referer));
            //else
            //    bulider.AppendLine(string.Format("Referer: {0}",
            //        header.Url));
            if (header.Url.ToLower().IndexOf("query") < 0 && header.Url.ToLower().IndexOf("waityime") < 0)
                bulider.AppendLine("x-requested-with: XMLHttpRequest");
            bulider.AppendLine(string.Format("X-Forwarded-For: {0}", string.Format("{0}.{1}.{2}.{3}", new Random().Next(10, 120), new Random().Next(120, 250), new Random().Next(130, 250), new Random().Next(1, 250))));

            bulider.AppendLine("User-Agent: Mozilla/5.0 (compatible; MSIE 9.0; Windows NT 6.1; Trident/5.0)");
            if (header.Url.ToLower().IndexOf("confirmpassengeraction") < 0)
            {
                bulider.AppendLine("Pragma: no-cache");
            }
            if (!string.IsNullOrEmpty(header.Host))
                bulider.AppendLine(string.Format("Host: {0}",
                    header.Host));
            else
                bulider.AppendLine(string.Format("Host: {0}",
                   GetHost(header.Url)));
            if (!string.IsNullOrEmpty(header.Cookies))
                bulider.AppendLine(string.Format("Cookie: {0}", header.Cookies.Replace("Path=/otsweb;", "").Replace("path=/;", "")));
            if (header.Url.ToLower().IndexOf("query") > 0)
                bulider.AppendLine("If-None-Match: " + DateTime.Now.Ticks.ToString());
            if (method.Equals(HttpMethod.POST))
            {
                if (!string.IsNullOrEmpty(header.Body))
                {
                    bulider.AppendLine(string.Format("Content-Length: {0}",
                        Encoding.Default.GetBytes(header.Body).Length));
                }
                else
                {
                    bulider.AppendLine("Content-Length: 0");
                }
            }
            if (header.Url.ToLower().IndexOf("confirmpassengeraction") > 0)
                bulider.AppendLine("Expect: 100-continue");
            bulider.AppendLine("Connection: Close");
            if (method.Equals(HttpMethod.POST) && !string.IsNullOrEmpty(header.Body))
                bulider.Append("\r\n" + header.Body);
            else
                bulider.Append("\r\n");
            return Encoding.Default.GetBytes(bulider.ToString());
        }

        /// <summary>
        /// 将HTTP协议头转换为Bytes数据
        /// </summary>
        /// <param name="method">HTTP方法</param>
        /// <param name="header">HTTP协议头</param>
        /// <returns>Bytes数据</returns>
        static byte[] ParseHttpHeaderToBytes1(HttpMethod method, HttpHeader header)
        {
            StringBuilder bulider = new StringBuilder(256);
            if (method.Equals(HttpMethod.POST))
            {
                bulider.AppendLine(string.Format("POST {0} HTTP/1.1",
                    header.Url.Replace("https://dynamic.12306.cn", "")));
            }
            else
            {
                if (header.Url.ToLower().StartsWith("https"))
                {
                    bulider.AppendLine(string.Format("GET {0} HTTP/1.1",
                    header.Url.Replace("https://dynamic.12306.cn", "")));
                }
                else
                {
                    bulider.AppendLine(string.Format("GET {0} HTTP/1.0",
                    header.Url.Replace("http://dynamic.12306.cn", "")));
                }
            }
            bulider.AppendLine("Content-Type: application/x-www-form-urlencoded");
            if (!string.IsNullOrEmpty(header.Accept))
            {
                bulider.AppendLine(string.Format("Accept:{0}",
                 header.Accept));
            }
            else
            {
                bulider.AppendLine("Accept: image/gif, image/jpeg, image/pjpeg, image/pjpeg, application/x-shockwave-flash, application/vnd.ms-excel, application/vnd.ms-powerpoint, application/msword, application/x-ms-application, application/x-ms-xbap, application/vnd.ms-xpsdocument, application/xaml+xml, */*");
            }
            bulider.AppendLine("Accept-Language: zh-cn");
            bulider.AppendLine("Accept-Encoding: gzip, identity");
            bulider.AppendLine("Cache-Control: no-cache");
            if (!string.IsNullOrEmpty(header.Referer))
                bulider.AppendLine(string.Format("Referer: {0}",
                    header.Referer));
            //else
            //    bulider.AppendLine(string.Format("Referer: {0}",
            //        header.Url));
            if (header.Url.ToLower().IndexOf("queryleftticket") < 0 && header.Url.ToLower().IndexOf("submutorderrequest") < 0
                && header.Url.ToLower().IndexOf("confirmpassengeraction") < 0)
            {
                bulider.AppendLine("x-requested-with: XMLHttpRequest");
            }
            bulider.AppendLine(string.Format("X-Forwarded-For: {0}", string.Format("{0}.{1}.{2}.{3}", new Random().Next(10, 120), new Random().Next(120, 250), new Random().Next(130, 250), new Random().Next(1, 250))));
            bulider.AppendLine("User-Agent: Mozilla/5.0 (compatible; MSIE 9.0; Windows NT 6.1; Trident/5.0)");
            if (!string.IsNullOrEmpty(header.Host))
                bulider.AppendLine(string.Format("Host: {0}",
                    header.Host));
            else
                bulider.AppendLine(string.Format("Host: {0}",
                   GetHost(header.Url)));
            if (!string.IsNullOrEmpty(header.Cookies))
                bulider.AppendLine(string.Format("Cookie: {0}",
                    header.Cookies));
            //bulider.AppendLine("Pragma: no-cache");
            //bulider.AppendLine("If-None-Match: 635166035939370989");
            if (method.Equals(HttpMethod.POST))
            {
                if (!string.IsNullOrEmpty(header.Body))
                {
                    bulider.AppendLine(string.Format("Content-Length: {0}", Encoding.Default.GetBytes(header.Body).Length));
                }
                else
                {
                    bulider.AppendLine("Content-Length: 0");
                }
            }
            //if (header.Url.ToLower().IndexOf("checkorderinfo") > 0
            //    //|| header.Url.ToLower().IndexOf("confirmpassengeraction") > 0
            //    )
            //{
            //    bulider.AppendLine("Expect: 100-continue");
            //}
            bulider.AppendLine("Connection: Close");
            bulider.Append("\r\n");
            if (method.Equals(HttpMethod.POST) && !string.IsNullOrEmpty(header.Body))
            {
                bulider.AppendLine(header.Body);
            }
            return Encoding.Default.GetBytes(bulider.ToString());
        }

        /// <summary>
        /// 从Url中提取Host信息
        /// </summary>
        /// <param name="url">Url</param>
        /// <returns>Host信息</returns>
        public static string GetHost(string url)
        {
            string host = string.Empty;
            try
            {
                Uri uri = new Uri(url);
                host = uri.Host;
            }
            catch (Exception ex)
            {
                Debug.WriteLine(ex.Message);
            }
            return host;
        }

        /// <summary>
        /// 通过Host获取IP地址
        /// </summary>
        /// <param name="host">Host</param>
        /// <returns>IP地址</returns>
        public static IPAddress GetAddress(string host)
        {
            IPAddress address = IPAddress.Any;
            try
            {
                IPAddress[] alladdress = Dns.GetHostAddresses(host);
                if (alladdress.Length > 0)
                {
                    address = alladdress[0];
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine(ex.Message);
            }
            return address;
        }

        /// <summary>
        /// 从HTTP返回头协议中取Set-Cookie信息（即Cookies）
        /// </summary>
        /// <param name="responseHeader">HTTP返回头协议</param>
        /// <returns>Cookies</returns>
        public static string GetCookies(string responseHeader)
        {
            StringBuilder cookies = new StringBuilder(256);
            using (StringReader reader = new StringReader(responseHeader))
            {
                string strLine = reader.ReadLine();
                while (strLine != null)
                {
                    if (strLine.StartsWith("Set-Cookie:"))
                    {
                        string temp = strLine.Remove(0, 12);
                        if (!temp.EndsWith(";"))
                        {
                            temp = temp + "; ";
                        }
                        //temp = temp.Replace("Path=/otsweb;", "").Replace("path=/;", "").Trim();
                        if (!string.IsNullOrEmpty(temp))
                            cookies.Append(temp);
                    }
                    strLine = reader.ReadLine();
                }
            }
            return cookies.ToString();
        }

        /// <summary>
        /// 从HTTP返回头协议中去Location地址(一般出现在301跳转)
        /// </summary>
        /// <param name="responseHeader">HTTP返回头协议</param>
        /// <returns>Location地址</returns>
        public static string GetLocation(string responseHeader)
        {
            string result = string.Empty;
            using (StringReader reader = new StringReader(responseHeader))
            {
                string strLine = reader.ReadLine();
                while (strLine != null)
                {
                    if (strLine.StartsWith("Location:"))
                    {
                        result = strLine.Remove(0, 10);
                    }
                    strLine = reader.ReadLine();
                }
            }
            return result;
        }
        public static IPEndPoint GetIPEndPoint(string strHost)
        {
            int nPort = 80;
            if (strHost.StartsWith("http://"))
            {
                strHost = strHost.Replace("http://", "");
            }
            else if (strHost.StartsWith("https://"))
            {
                nPort = 443;
                strHost = strHost.Replace("https://", "");
            }
            if (strHost.IndexOf("/") > 0)
                strHost = strHost.Substring(0, strHost.IndexOf("/"));
            try
            {
                var address = Dns.GetHostAddresses(strHost);
                if (address.Length > 0)
                {
                    return new IPEndPoint(address[0], nPort);
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine(ex.Message);
            }
            return new IPEndPoint(IPAddress.Parse("*************"), nPort);
        }
        public static string ToString(NameValueCollection values)
        {
            StringBuilder bodyBuilder = new StringBuilder(256);
            if (values != null)
            {
                try
                {
                    foreach (string key in values)
                    {
                        //bodyBuilder.AppendFormat("&{0}={1}", HttpUtility.UrlEncode(key.Trim(), Encoding.UTF8).Replace("+", "%20"),
                        //    HttpUtility.UrlEncode(values[key] == null ? "" : values[key].Trim(), Encoding.UTF8).Replace("+", "%20"));
                        bodyBuilder.AppendFormat("&{0}={1}", key.Trim(), values[key] == null ? "" : values[key].Trim());
                    }
                }
                catch { }
                if (bodyBuilder.ToString().Length > 0 && values.Count > 0)
                {
                    bodyBuilder.Remove(0, 1);
                }
            }
            return bodyBuilder.ToString();
        }

        #region Cookie
        //public static string GetCookieString()
        //{
        //    return "JSESSIONID=" + GetCookieByName("JSESSIONID") + "; BIGipServerotsweb=" + GetCookieByName("BIGipServerotsweb");
        //}

        //public static string GetCookieByName(string string_1)
        //{
        //    Cookie cookie = GetAllCookies().Find((Cookie cookie_0) => cookie_0.Name == string_1);
        //    return (cookie != null) ? cookie.Value : string.Empty;
        //}

        //public static List<Cookie> GetAllCookies()
        //{
        //    List<Cookie> list = new List<Cookie>();
        //    Hashtable hashtable = (Hashtable)cookieContainer.GetType().InvokeMember("m_domainTable", BindingFlags.Instance | BindingFlags.NonPublic | BindingFlags.GetField, null, cookieContainer, new object[0]);
        //    foreach (object current in hashtable.Values)
        //    {
        //        SortedList sortedList = (SortedList)current.GetType().InvokeMember("m_list", BindingFlags.Instance | BindingFlags.NonPublic | BindingFlags.GetField, null, current, new object[0]);
        //        foreach (CookieCollection cookieCollection in sortedList.Values)
        //        {
        //            foreach (Cookie item in cookieCollection)
        //            {
        //                list.Add(item);
        //            }
        //        }
        //    }
        //    return list;
        //}
        #endregion

        public static string SubString(string strSource, string strSpilt, string strEnd = "")
        {
            string result = "";
            try
            {
                int nIndex = strSource.IndexOf(strSpilt);
                if (nIndex >= 0)
                {
                    result = strSource.Substring(nIndex + strSpilt.Length);
                    if (!string.IsNullOrEmpty(strEnd))
                    {
                        nIndex = result.IndexOf(strEnd);
                        if (nIndex >= 0)
                        {
                            result = result.Substring(0, nIndex);
                        }
                    }
                }
            }
            catch (Exception oe)
            {
                Console.WriteLine(oe.Message);
            }
            return result.Trim();
        }
        #endregion
    }
}
