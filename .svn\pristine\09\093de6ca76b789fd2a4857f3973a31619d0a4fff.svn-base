﻿using System.Threading;
// ReSharper disable All

namespace TicketServer
{
    class Program
    {
        private static void Main(string[] args)
        {
            bool flag;
            new Mutex(false, "TicketServer", out flag);
            if (flag)
            {
                HttpServer httpServer = new MyHttpServer(8888);
                httpServer.Listen();
            }
        }
    }
}
