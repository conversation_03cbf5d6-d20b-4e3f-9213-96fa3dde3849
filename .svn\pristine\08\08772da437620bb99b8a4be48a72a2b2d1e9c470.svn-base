﻿using CommonLib;
using System;
using System.Collections.Generic;
using System.Collections.Specialized;
using System.IO;

namespace TableOcr
{
    /// <summary>
    /// TextIn体验中心
    /// </summary>
    public class HuoShanAPIRec : BaseTableRec
    {
        public HuoShanAPIRec()
        {
            OcrGroup = OcrGroupType.字节;
            OcrType = TableOcrType.火山API;
            MaxExecPerTime = 26;
            //IsSupportUrlOcr = true;

            LstJsonPreProcessArray = new List<object>() { "Result", "data", "table_infos" };
            LstJsonNextProcessArray = new List<object>() { "cell_infos" };
            LstJsonResultProcessArray = new List<object>() { "cell_text" };
            LstRowIndex = new List<object>() { "start_row", "end_row" };
            LstColumnIndex = new List<object>() { "start_col", "end_col" };
        }

        protected override string GetHtml(OcrContent content)
        {
            var byt = Convert.FromBase64String(content.strBase64);
            var html = PostFileResult(byt, content.fileExt);
            return html;
        }

        private string PostFileResult(byte[] content, string fileExt)
        {
            var result = "";
            try
            {
                var url = "https://www.volcengine.com/api/exp/2/model-i";
                var file = new UploadFileInfo()
                {
                    Name = "__upload",
                    Filename = "1." + fileExt,
                    ContentType = ApplicationTypeHelper.GetApplicationType(fileExt),
                    Stream = new MemoryStream(content)
                };
                var values = new NameValueCollection() {
                { "__upload_key","image"},
                { "__upload_path","/ocr-process/v1/ocr_table_rec"}
            };
                var headers = new NameValueCollection() {
                { "Sec-Fetch-Site","cross-site"},
                { "Sec-Fetch-Mode","cors"},
            };
                result = PostFile(url, new[] { file
    }, values, headers);
            }
            catch (Exception)
            {

            }
            return result;
        }

    }
}