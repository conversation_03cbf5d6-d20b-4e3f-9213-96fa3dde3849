using System.Collections.Generic;

namespace ServiceStack;

public static class HttpMethods
{
    static readonly string[] allVerbs = {
        "OPTIONS", "GET", "HEAD", "POST", "PUT", "DELETE", "TRACE", "CONNECT", // RFC 2616
        "PROPFIND", "PROP<PERSON>TCH", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>O<PERSON>", "<PERSON><PERSON><PERSON>", "UN<PERSON><PERSON><PERSON>",    // RFC 2518
        "VERSION-CONTROL", "REPOR<PERSON>", "CHECKOUT", "<PERSON>EC<PERSON><PERSON>", "UNCHECKOUT",
        "<PERSON><PERSON><PERSON><PERSON>KSPACE", "UPDATE", "LABEL", "MER<PERSON>", "BASELINE-CONTROL", "<PERSON><PERSON><PERSON>IVITY",  // RFC 3253
        "ORDERPATCH", // RFC 3648
        "ACL",        // RFC 3744
        "PATCH",      // https://datatracker.ietf.org/doc/draft-dusseault-http-patch/
        "SEARCH",     // https://datatracker.ietf.org/doc/draft-reschke-webdav-search/
        "BCOPY", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NO<PERSON>F<PERSON>",
        "<PERSON><PERSON><PERSON>",  "SUBSCRIBE", "UNSUBSCRIBE" //MS Exchange WebDav: http://msdn.microsoft.com/en-us/library/aa142917.aspx
    };

    public static HashSet<string> AllVerbs = new(allVerbs);

    public static bool Exists(string httpMethod) => AllVerbs.Contains(httpMethod.ToUpper());
    public static bool HasVerb(string httpVerb) => Exists(httpVerb);

    public const string Get = "GET";
    public const string Put = "PUT";
    public const string Post = "POST";
    public const string Delete = "DELETE";
    public const string Options = "OPTIONS";
    public const string Head = "HEAD";
    public const string Patch = "PATCH";
}