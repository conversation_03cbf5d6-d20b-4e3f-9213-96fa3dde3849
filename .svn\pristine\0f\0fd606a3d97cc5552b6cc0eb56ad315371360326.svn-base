﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Net;

namespace ToolCommon
{
    public class WebClientExt
    {

        public static string GetHtml(string url, double timeOut)
        {
            return GetHtml(url, "", "", "", 1, timeOut > 0 ? (int)timeOut : 2);
        }

        public static string GetHtml(string url, int retryCount = 1)
        {
            return GetHtml(url, "", "");
        }

        public static string GetHtml(string url, string cookie, string ipAddress, string post = "", int retryCount = 1, int timeOut = 2)
        {
            string strTmp = string.Empty;
            retryCount = retryCount <= 0 ? 1 : retryCount;
            for (int i = 0; i < retryCount; i++)
            {
                //strTmp = GetHtmlTest(url, ref cookie, ipAddress, post, "", timeOut);
                strTmp = GetHtml(url, ref cookie, ipAddress, post, "", timeOut);
                if (!string.IsNullOrEmpty(strTmp))
                    break;
            }
            return strTmp;
        }

        public static string GetHtml(string Url, ref string CookieStr, string ipAddress = "", string strPost = "", string Referer = "", int timeOut = 2)
        {
            DateTime dtStart = DateTime.Now;
            string result = "";
            CNNWebClient myClient = new CNNWebClient();
            myClient.Timeout = timeOut;
            if (!string.IsNullOrEmpty(ipAddress))
                myClient.StrIPAddress = ipAddress;
            try
            {
                myClient.Headers.Add("Content-Type: application/x-www-form-urlencoded");
                if (Url.IndexOf("10086") >= 0)
                    myClient.Headers.Add("User-Agent: Mozilla/5.0 (Windows NT 6.1; WOW64; rv:31.0) Gecko/20100101 Firefox/31.0");
                else
                    myClient.Headers.Add("User-Agent: Mozilla/5.0 (compatible; MSIE 10.0; Windows NT 6.2; WOW64; Trident/6.0)");
                myClient.Headers.Add("Cache-Control: no-cache");
                myClient.Headers.Add("Pragma: no-cache");
                if (Url.IndexOf("12306") >= 0 || Url.Contains("qingyidai"))
                {
                    myClient.Headers.Add("X-Requested-With: XMLHttpRequest");
                }
                else
                {
                    if (!string.IsNullOrEmpty(Referer))
                        myClient.Headers.Add("Referer: " + Referer);
                }
                if (CookieStr != "")
                {
                    myClient.Headers.Add("Cookie: " + CookieStr);
                }
                myClient.Encoding = Encoding.UTF8;
                if (string.IsNullOrEmpty(strPost))
                    result = myClient.DownloadString(new Uri(Url, true));
                else
                    result = myClient.UploadString(new Uri(Url, true), strPost);
                if (!string.IsNullOrEmpty(myClient.ResponseHeaders["Set-Cookie"]))
                {
                    if (string.IsNullOrEmpty(CookieStr) && Url.IndexOf("12306") > 0)
                    {
                        CookieStr = myClient.ResponseHeaders["Set-Cookie"].Replace("Path=/otn", " ").Replace("path=/", "").Replace(",", "").Trim();
                    }
                    else if (Url.IndexOf("10086") > 0)
                    {
                        if (CookieStr.IndexOf("JSESSIONID=") >= 0 && myClient.ResponseHeaders["Set-Cookie"].IndexOf("JSESSIONID=") >= 0)
                        {
                            string strTmp = CookieStr.Substring(CookieStr.IndexOf("JSESSIONID="));
                            CookieStr = CookieStr.Substring(0, CookieStr.IndexOf("JSESSIONID=")) + strTmp.Substring(strTmp.IndexOf(";") + 1);
                        }
                        CookieStr += myClient.ResponseHeaders["Set-Cookie"]
                            .Replace("cell_cookie=\"\";", "").Replace("Path=/huc", "")
                            .Replace("Path=/", " ").Replace("HttpOnly", "").Replace(",", "").Trim();
                        while (CookieStr.IndexOf("Expires=") >= 0)
                        {
                            string strTmp = CookieStr.Substring(CookieStr.IndexOf("Expires="));
                            CookieStr = CookieStr.Substring(0, CookieStr.IndexOf("Expires=")) + strTmp.Substring(strTmp.IndexOf(";") + 1);
                        }
                        while (CookieStr.IndexOf("Domain=") >= 0)
                        {
                            string strTmp = CookieStr.Substring(CookieStr.IndexOf("Domain="));
                            CookieStr = CookieStr.Substring(0, CookieStr.IndexOf("Domain=")) + strTmp.Substring(strTmp.IndexOf(";") + 1);
                        }
                        while (CookieStr.IndexOf("  ") >= 0)
                        {
                            CookieStr = CookieStr.Replace("  ", " ");
                        }
                        CookieStr = CookieStr.Replace(";;", ";").Replace("; ;", ";");
                        //Expires=Tue, 18-Aug-2082 09:54:49 GMT;
                    }
                }
                if (!string.IsNullOrEmpty(myClient.ResponseHeaders["Location"]))
                {
                    if (myClient.ResponseHeaders["Location"].IndexOf("wap2.jsp") < 0)
                        return GetHtml(myClient.ResponseHeaders["Location"], ref CookieStr, ipAddress, strPost, Referer, timeOut);
                    else
                    {
                        if (CookieStr.IndexOf("infoversion=2;") < 0)
                        {
                            CookieStr += " infoversion=2; loginstatusCookie:400";
                        }
                    }
                }
                if (Url.Contains("baidu") && !string.IsNullOrEmpty(myClient.ResponseHeaders["Date"]))//header.Url.Contains("12306") ||
                {
                    try
                    {
                        result = myClient.ResponseHeaders["Date"];
                    }
                    catch { }
                }
            }
            catch (Exception oe)
            {
            }
            finally
            {
                try
                {
                    if (myClient.IsBusy)
                        myClient.CancelAsync();
                }
                catch { }
                try
                {
                    myClient.Dispose();
                }
                catch { }
                try
                {
                    myClient = null;
                }
                catch { }
            }
            return result;
        }
    }

    public class CNNWebClient : WebClient
    {
        private int _timeOut = 3;
        private string strIPAddress = "";

        public string StrIPAddress
        {
            get { return strIPAddress; }
            set { strIPAddress = value; }
        }

        /// <summary>
        /// 过期时间
        /// </summary>
        public int Timeout
        {
            get
            {
                return _timeOut;
            }
            set
            {
                if (value <= 0)
                    _timeOut = 10;
                _timeOut = value;
            }
        }

        /// <summary>
        /// 重写GetWebRequest,添加WebRequest对象超时时间
        /// </summary>
        /// <param name="address"></param>
        /// <returns></returns>
        protected override WebRequest GetWebRequest(Uri address)
        {
            if (DateTime.Now.Second % 3 == 0)
            {
                //System.Threading.Thread.Sleep(1);
                System.GC.Collect();
            }
            if (!string.IsNullOrEmpty(StrIPAddress))
                address = new Uri(address.AbsoluteUri.Replace(address.Host, StrIPAddress), true);

            HttpWebRequest request = (HttpWebRequest)base.GetWebRequest(address);//address.AbsoluteUri.IndexOf("leftTicket") > 0 ? (HttpWebRequest)HttpWebRequest.Create(address.AbsoluteUri) :
            if (request.Proxy != null)
            {
                request.Proxy = null;
            }
            request.ProtocolVersion = HttpVersion.Version10;
            request.ServicePoint.ConnectionLimit = int.MaxValue;
            request.AllowWriteStreamBuffering = false;
            //request.ServicePoint.Expect100Continue = false;
            //request.ServicePoint.UseNagleAlgorithm = false;
            request.AutomaticDecompression = DecompressionMethods.Deflate | DecompressionMethods.GZip;
            if (address.AbsoluteUri.ToString().IndexOf("12306") >= 0)
                request.AllowAutoRedirect = true;
            else
                request.AllowAutoRedirect = false;
            //if (!isImg)
            //{
            if (address.AbsoluteUri.ToString().IndexOf("12306") >= 0)
                request.KeepAlive = false;
            else
                request.KeepAlive = true;
            request.IfModifiedSince = new DateTime(1970, 1, 1);
            request.UserAgent = "Mozilla/4.0 (compatible; MSIE 8.0; Windows NT 5.1; Trident/4.0; .NET CLR 2.0.50727; .NET CLR 3.0.4506.2152; .NET4.0C; .NET4.0E; .NET CLR 3.5.30729; InfoPath.3)";
            //request.Headers.Add("If-None-Match", DateTime.Now.Ticks.ToString());
            //request.CachePolicy = new HttpRequestCachePolicy(HttpRequestCacheLevel.NoCacheNoStore);

            //HttpHelper.SetHeaderValue(request.Headers, "If-Modified-Since", "Wed, 31 Dec 1969 16:00:00 GMT");
            //request.Headers.Add("If-None-Match", DateTime.Now.Ticks.ToString());
            //}
            if (address.AbsoluteUri.ToString().IndexOf("12306") >= 0)
            {
                request.Timeout = 1000 * Timeout;
                request.ReadWriteTimeout = 1000 * Timeout;
            }
            //HttpHelper.SetHeaderValue(request.Headers, "Connection", "Close");
            return request;
        }
    }
}
