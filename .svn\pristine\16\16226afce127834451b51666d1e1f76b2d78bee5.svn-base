﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="12.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{2D245CFB-BF26-4B49-8E5C-1A25FA8DCA44}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>HanZiOcr</RootNamespace>
    <AssemblyName>HanZiOcr</AssemblyName>
    <TargetFrameworkVersion>v4.5</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <TargetFrameworkProfile />
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <Prefer32Bit>false</Prefer32Bit>
    <PlatformTarget>AnyCPU</PlatformTarget>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="log4net, Version=1.2.15.0, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a, processorArchitecture=x86">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\DLL\log4net.dll</HintPath>
    </Reference>
    <Reference Include="Newtonsoft.Json, Version=8.0.0.0, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed, processorArchitecture=MSIL">
      <HintPath>..\packages\Newtonsoft.Json.8.0.3\lib\net45\Newtonsoft.Json.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Core" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Net" />
    <Reference Include="System.Net.Http" />
    <Reference Include="System.Web" />
    <Reference Include="System.Web.Extensions" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="BaiMiaoBaiDuRec.cs" />
    <Compile Include="BaiMiaoXunFeiRec.cs" />
    <Compile Include="BitMainRec.cs" />
    <Compile Include="HanWangRec.cs" />
    <Compile Include="HeHeChinaEnglishRec.cs" />
    <Compile Include="YandexRec.cs" />
    <Compile Include="SouGouBroswerRec.cs" />
    <Compile Include="YiXiangWeiLaiRec.cs" />
    <Compile Include="YiTuRec.cs" />
    <Compile Include="HeHeRec.cs" />
    <Compile Include="JinShanAPIRec.cs" />
    <Compile Include="HaiMaRec.cs" />
    <Compile Include="FeiJiangRec.cs" />
    <Compile Include="CloudWalkRec.cs" />
    <Compile Include="DuGuangRec.cs" />
    <Compile Include="ChineseOcrLiteRec.cs" />
    <Compile Include="XueErSiHandRec.cs" />
    <Compile Include="XueErSiDemoRec.cs" />
    <Compile Include="BaiMiaoYouDaoRec.cs" />
    <Compile Include="VivoAIRec.cs" />
    <Compile Include="XunFeiKuaiDuRec.cs" />
    <Compile Include="XunFeiXiaoZhiRec.cs" />
    <Compile Include="YiDaoRec.cs" />
    <Compile Include="XueErSiAPIRec.cs" />
    <Compile Include="UUkitApiRec.cs" />
    <Compile Include="AICloudApiRec.cs" />
    <Compile Include="FlittoAppRec.cs" />
    <Compile Include="NewOCRAPIRec.cs" />
    <Compile Include="OuLuRec.cs" />
    <Compile Include="MicrosoftTransRec.cs" />
    <Compile Include="NewOCRRec.cs" />
    <Compile Include="SouGouShouJiRec.cs" />
    <Compile Include="XinHuoRec.cs" />
    <Compile Include="YiSiZhiRec.cs" />
    <Compile Include="ConstHelper.cs" />
    <Compile Include="JDAIRec.cs" />
    <Compile Include="VivoRec.cs" />
    <Compile Include="WangYiRec.cs" />
    <Compile Include="AICloudRec.cs" />
    <Compile Include="FaceAIRec.cs" />
    <Compile Include="QQAIRec.cs" />
    <Compile Include="QuNaLiteAppRec.cs" />
    <Compile Include="BaseOcrRec.cs" />
    <Compile Include="TaiKangLiteAppRec.cs" />
    <Compile Include="WeiXinLiteAppRec.cs" />
    <Compile Include="BaiDuAILiteAppRec.cs" />
    <Compile Include="XunJiePDFRec.cs" />
    <Compile Include="HanZiOcrType.cs" />
    <Compile Include="SouGouDeepRec.cs" />
    <Compile Include="CckejiLiteAppRec.cs" />
    <Compile Include="XunFeiLiteAppRec.cs" />
    <Compile Include="YouDaoRec.cs" />
    <Compile Include="XunFeiAPIRec.cs" />
    <Compile Include="YouDaoAPIRec.cs" />
    <Compile Include="YingYinYiLiteAppRec.cs" />
    <Compile Include="GrapeCityRec.cs" />
    <Compile Include="YouTuLiteAppRec.cs" />
    <Compile Include="YouTuAPIRec.cs" />
    <Compile Include="YouTuRec.cs" />
    <Compile Include="SouGouRec.cs" />
    <Compile Include="BaiDuCloudAIRec.cs" />
    <Compile Include="BaiDuAPIRec.cs" />
    <Compile Include="BaiDuAIRec.cs" />
    <Compile Include="GoogleRec.cs" />
    <Compile Include="GoogleRecTest.cs" />
    <Compile Include="OnLineOCRRec.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\CommonLib\CommonLib.csproj">
      <Project>{fc03a7d4-8ef2-4dea-a15a-c099eb77b0eb}</Project>
      <Name>CommonLib</Name>
    </ProjectReference>
    <ProjectReference Include="..\ImageLib\ImageLib.csproj">
      <Project>{b7e169a2-3104-40fb-9d1e-2ff911fa45e5}</Project>
      <Name>ImageLib</Name>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup>
    <None Include="packages.config" />
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>