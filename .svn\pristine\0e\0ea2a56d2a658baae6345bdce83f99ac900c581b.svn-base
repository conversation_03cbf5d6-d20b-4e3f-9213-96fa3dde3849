﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="12.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProductVersion>
    </ProductVersion>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectGuid>{CEAB2047-A095-4A96-8B23-325C6BB5690D}</ProjectGuid>
    <ProjectTypeGuids>{349C5851-65DF-11DA-9384-00065B846F21};{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}</ProjectTypeGuids>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>ServiceStack.RazorHostTests</RootNamespace>
    <AssemblyName>ServiceStack.RazorHostTests</AssemblyName>
    <UseIISExpress>false</UseIISExpress>
    <TargetFrameworkVersion>v4.7.2</TargetFrameworkVersion>
    <FileUpgradeFlags>
    </FileUpgradeFlags>
    <OldToolsVersion>4.0</OldToolsVersion>
    <UpgradeBackupLocation />
    <TargetFrameworkProfile />
    <IISExpressSSLPort />
    <IISExpressAnonymousAuthentication />
    <IISExpressWindowsAuthentication />
    <IISExpressUseClassicPipelineMode />
    <SolutionDir Condition="$(SolutionDir) == '' Or $(SolutionDir) == '*Undefined*'">..\..\src\</SolutionDir>
    <RestorePackages>true</RestorePackages>
    <Use64BitIISExpress />
    <UseGlobalApplicationHostFile />
    <NuGetPackageImportStamp>
    </NuGetPackageImportStamp>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>True</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>False</Optimize>
    <OutputPath>bin\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>True</Optimize>
    <OutputPath>bin\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="mscorlib" />
    <Reference Include="System" />
    <Reference Include="System.ComponentModel.DataAnnotations" />
    <Reference Include="System.Data" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="System.Net" />
    <Reference Include="System.Runtime.Serialization" />
    <Reference Include="System.ServiceModel" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Threading" />
    <Reference Include="System.Threading.Tasks" />
    <Reference Include="System.Web" />
    <Reference Include="System.Web.ApplicationServices" />
    <Reference Include="System.Web.DynamicData" />
    <Reference Include="System.Web.Entity" />
    <Reference Include="System.Web.Extensions" />
    <Reference Include="System.Xml" />
    <Reference Include="System.Configuration" />
    <Reference Include="System.Xml.Linq" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="Global.asax" />
    <Content Include="Web.config">
      <SubType>Designer</SubType>
    </Content>
    <Content Include="Web.Debug.config">
      <DependentUpon>Web.config</DependentUpon>
    </Content>
    <Content Include="Web.Release.config">
      <DependentUpon>Web.config</DependentUpon>
    </Content>
  </ItemGroup>
  <ItemGroup>
    <Compile Include="AppHost.cs" />
    <Compile Include="Global.asax.cs">
      <DependentUpon>Global.asax</DependentUpon>
    </Compile>
    <Compile Include="Properties\AssemblyInfo.cs" />
  </ItemGroup>
  <ItemGroup>
    <Folder Include="App_Data\" />
    <Folder Include="css\" />
    <Folder Include="js\" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\..\ServiceStack.Text\src\ServiceStack.Text\ServiceStack.Text.csproj" />
    <ProjectReference Include="..\..\..\ServiceStack.OrmLite\src\ServiceStack.OrmLite\ServiceStack.OrmLite.csproj" />
    <ProjectReference Include="..\..\..\ServiceStack.OrmLite\src\ServiceStack.OrmLite.Sqlite\ServiceStack.OrmLite.Sqlite.csproj" />
    <ProjectReference Include="..\..\src\ServiceStack.Interfaces\ServiceStack.Interfaces.csproj" />
    <ProjectReference Include="..\..\src\ServiceStack.Client\ServiceStack.Client.csproj" />
    <ProjectReference Include="..\..\src\ServiceStack.Common\ServiceStack.Common.csproj" />
    <ProjectReference Include="..\..\src\ServiceStack\ServiceStack.csproj" />
    <ProjectReference Include="..\..\src\ServiceStack.Razor\ServiceStack.Razor.csproj" />
  </ItemGroup>
  
  <ItemGroup>
    <Content Include="Views\Shared\SimplyLayout.cshtml" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="Views\Shared\LayoutWithSection.cshtml" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="Views\Shared\LayoutWithManySections.cshtml" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="Views\Shared\LayoutWithOptionalSection.cshtml" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="Views\Shared\LayoutWithOptionalSectionWithDefaults.cshtml" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="Views\ViewThatUsesModelCSharp.cshtml" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="Views\ViewThatUsesLayoutAndSection.cshtml" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="Views\ViewThatUsesLayout.cshtml" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="Views\ViewThatUsesLayoutAndManySection.cshtml" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="Views\ViewThatUsesLayoutAndModel.cshtml" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="Views\ViewThatUsesLayoutAndOptionalSection.cshtml" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="Views\ViewThatUsesLayoutAndOptionalSectionOverridingDefaults.cshtml" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="Views\ViewThatUsesLayoutAndOptionalSectionWithDefaults.cshtml" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="Views\Rockstars.cshtml" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="CatchAll.cshtml" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="CatchAllDynamic.cshtml" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="Views\Shared\HtmlReport.cshtml" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="Embedded\CatchEmbedded.cshtml" />
    <EmbeddedResource Include="Embedded\Views\Rockstars.cshtml" />
    <EmbeddedResource Include="Embedded\Views\Shared\HtmlReport.cshtml" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="_CatchAllPartial.cshtml" />
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="Microsoft.CSharp" Version="4.7.0" />
    <PackageReference Include="NUnit" Version="3.13.2" />
    <PackageReference Include="System.Buffers" Version="4.5.1" />
    <PackageReference Include="System.Memory" Version="4.5.4" />
    <PackageReference Include="System.Runtime.CompilerServices.Unsafe" Version="4.7.1" />
    <PackageReference Include="System.ValueTuple" Version="4.5.0" />
  </ItemGroup>
  <PropertyGroup>
    <VisualStudioVersion Condition="'$(VisualStudioVersion)' == ''">10.0</VisualStudioVersion>
    <VSToolsPath Condition="'$(VSToolsPath)' == ''">$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v$(VisualStudioVersion)</VSToolsPath>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Signed|AnyCPU'">
    <OutputPath>bin\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <Optimize>true</Optimize>
    <DebugType>pdbonly</DebugType>
    <PlatformTarget>AnyCPU</PlatformTarget>
    <ErrorReport>prompt</ErrorReport>
    <CodeAnalysisRuleSet>MinimumRecommendedRules.ruleset</CodeAnalysisRuleSet>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <Import Project="$(MSBuildBinPath)\Microsoft.CSharp.targets" />
  <Import Project="$(VSToolsPath)\WebApplications\Microsoft.WebApplication.targets" Condition="'$(VSToolsPath)' != ''" />
  <Import Project="$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v10.0\WebApplications\Microsoft.WebApplication.targets" Condition="false" />
  <ProjectExtensions>
    <VisualStudio>
      <FlavorProperties GUID="{349C5851-65DF-11DA-9384-00065B846F21}">
        <WebProjectProperties>
          <UseIIS>False</UseIIS>
          <AutoAssignPort>False</AutoAssignPort>
          <DevelopmentServerPort>58772</DevelopmentServerPort>
          <DevelopmentServerVPath>/</DevelopmentServerVPath>
          <IISUrl>
          </IISUrl>
          <NTLMAuthentication>False</NTLMAuthentication>
          <UseCustomServer>False</UseCustomServer>
          <CustomServerUrl>
          </CustomServerUrl>
          <SaveServerSettingsInUserFile>False</SaveServerSettingsInUserFile>
        </WebProjectProperties>
      </FlavorProperties>
    </VisualStudio>
    <MonoDevelop>
      <Properties VerifyCodeBehindFields="True" VerifyCodeBehindEvents="True">
        <XspParameters Port="8080" Address="127.0.0.1" SslMode="None" SslProtocol="Default" KeyType="None" CertFile="" KeyFile="" PasswordOptions="None" Password="" Verbose="True" />
      </Properties>
    </MonoDevelop>
  </ProjectExtensions>
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>