﻿using System;
using System.Runtime.CompilerServices;
using System.Threading;
namespace NewTicket
{
    /// <summary>
    /// ICycleEngine的抽象实现，循环引擎直接继承它并实现DoDetect方法即可。
    /// </summary>
    public abstract class BaseCycleEngine : ICycleEngine
    {
        private volatile bool bool_0 = true;
        private const int SleepTime = 1000;
        private int totalSleepCount = 0;
        private int detectSpanInSecs = 0;
        private ManualResetEvent manualResetEvent_0 = new ManualResetEvent(false);

        public event CbDelegate<Exception> EngineStopped;

        public BaseCycleEngine()
        {
            this.EngineStopped += delegate{};
        }
        /// <summary>
        /// 每次循环时，引擎需要执行的核心动作。
        /// (1)该方法不允许抛出异常。
        /// (2)该方法中不允许调用BaseCycleEngine.Stop()方法，否则会导致死锁。
        /// </summary>
        /// <returns>返回值如果为false，表示退出引擎循环线程</returns>
        protected abstract bool DoDetect();

        public virtual void Start()
        {
            this.totalSleepCount = (this.detectSpanInSecs * 1000) / 1000;
            if ((this.detectSpanInSecs >= 0) && this.bool_0)
            {
                this.manualResetEvent_0.Reset();
                this.bool_0 = false;
                new CbDelegate(this.Worker).BeginInvoke(null, null);
            }
        }
        /// <summary>
        /// 停止引擎。千万不要在DoDetect方法中调用该方法，会导致死锁，可以改用StopAsyn方法。
        /// </summary>
        public virtual void Stop()
        {
            if (!this.bool_0)
            {
                this.bool_0 = true;
                this.manualResetEvent_0.WaitOne();
                this.manualResetEvent_0.Reset();
            }
        }

        protected virtual void Worker()
        {
            Exception exception = null;
            try
            {
                while (!this.bool_0)
                {
                    for (int i = 0; i < this.totalSleepCount; i++)
                    {
                        if (this.bool_0)
                        {
                            break;
                        }
                        Thread.Sleep(1000);
                    }
                    if (!this.DoDetect())
                    {
                        return;
                    }
                }
            }
            catch (Exception exception2)
            {
                exception = exception2;
                throw;
            }
            finally
            {
                this.bool_0 = true;
                this.manualResetEvent_0.Set();
                this.EngineStopped(exception);
            }
        }

        public virtual int DetectSpanInSecs
        {
            get
            {
                return this.detectSpanInSecs;
            }
            set
            {
                this.detectSpanInSecs = value;
            }
        }

        public bool IsRunning
        {
            get
            {
                return !this.bool_0;
            }
        }
    }

}