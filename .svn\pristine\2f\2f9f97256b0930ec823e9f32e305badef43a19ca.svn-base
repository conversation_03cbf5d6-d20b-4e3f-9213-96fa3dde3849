﻿using CommonLib;
using System;
using System.Collections.Generic;
using System.Collections.Specialized;
using System.Web;

namespace DocOcr
{
    public class XunJiePDFRec : BaseDocOcrRec
    {
        public XunJiePDFRec()
        {
            OcrGroup = OcrGroupType.迅捷;
            OcrType = DocOcrType.迅捷PDF;
            ResultType = ResutypeEnum.网页;
            MaxExecPerTime = 5;
            IsDownLoadFile = true;
            //文件上限2M
            FileSizeLimit = 1024 * 1024 * 2;
            AllowUploadFileTypes = new List<string>() { "pdf", "jpg", "gif", "png", "bmp" };
        }

        private const string strUploadUrl = "https://app.xunjiepdf.com/api/Upload?tasktype=ocr&phonenumber=&loginkey=&machineid={0}&token={1}&limitsize=20480&pdfname=1.{6}&queuekey={2}&uploadtime=&filecount=1&fileindex=1&pagerange=all&picturequality=&outputfileextension={3}&picturerotate=0%2Cundefined&filesequence=0%2Cundefined&filepwd=&iconsize=&picturetoonepdf=&isshare=0&softname=pdfonlineconverter&softversion=V5.0&validpagescount=2000&limituse=1&filespwdlist=&fileCountwater=0&languagefrom={4}&languageto={5}&cadverchose=&pictureforecolor=&picturebackcolor=&parainfo=maxlimit%3Achar2000page0&id=WU_FILE_0&name=1.{6}&type=application%2Fpdf&lastModifiedDate=&size=";

        private string GetNewId(ref string macId, ref string token)
        {
            var result = "";
            macId = Guid.NewGuid().ToString().Replace("-", "").ToLower();
            var html = WebClientSyncExt.GetHtml("https://app.xunjiepdf.com/api/producetoken", "machineid=" + macId, ExecTimeOutSeconds);
            //{"code":10000,"guid":"403c2a192aec44d1b159edfd3e2db1ba","token":"03dfc627fcf95d659041447f3de052c9"}
            if (html.Contains("\"guid\":\""))
            {
                html = html.Substring(html.IndexOf("\"guid\":\"") + "\"guid\":\"".Length);
                result = html.Substring(0, html.IndexOf("\""));
                html = html.Substring(html.IndexOf("\"token\":\"") + "\"token\":\"".Length);
                token = html.Substring(0, html.IndexOf("\""));
            }
            return result;
        }

        protected override string GetHtml(OcrContent content)
        {
            var result = "";
            var macId = "";
            var token = "";

            string from = "", to = "";
            InitLanguage(content, ref from, ref to);

            var fileExt = content.fileExt;
            var guid = GetNewId(ref macId, ref token);
            var byt = Convert.FromBase64String(content.strBase64);
            var tranFileType = ResultFileType == OcrFileType.PDF ? "pdf" : (ResultFileType == OcrFileType.Txt ? "txt" : "docx");
            var upLoadUrl = string.Format(strUploadUrl, macId, token, guid, tranFileType, from, to, fileExt, HttpUtility.UrlEncode(ApplicationTypeHelper.GetApplicationType(fileExt)));

            if (UploadPic(upLoadUrl, byt))
            {
                result = "{" +
                    string.Format("\"fileId\":\"{0}\",\"fileType\":\"{1}\",\"url\":\"{2}\""
                    , guid
                    , tranFileType
                    , string.Format("https://app.xunjiepdf.com/download/fileid/{0}", guid)) + "}";
            }
            return result;
        }

        private string PostFileResult(string taskId)
        {
            var result = "";
            try
            {
                var url = "http://app.xunjiepdf.com/api/progress";
                var values = new NameValueCollection {
                    { "tasktag", taskId },
                    { "limituse", "-1" } };
                result = PostFile(url, null, values);

                if (!string.IsNullOrEmpty(result))
                {
                    result = CommonHelper.SubString(result, "\"message\":\"", "\"");
                }
            }
            catch (Exception)
            {

            }
            return result;
        }

        private bool UploadPic(string url, byte[] b)
        {
            var result = false;
            var strTmpHtml = WebClientSyncExt.GetHtml(url, "", Convert.ToBase64String(b), "", 5, new NameValueCollection()
            {
                { "Content-Type","application/octet-stream"}
            });
            if (strTmpHtml.Contains("成功")|| strTmpHtml.Contains("完成"))
            {
                result = true;
            }
            return result;
        }

        protected override ResultEntity GetProcessText(OcrContent content, string html)
        {
            //{"status":2
            //"is_preview_docx_ready":true,"is_preview_pdf_ready":true,"is_preview_uncomparison_docx_ready":true,"is_preview_uncomparison_pdf_ready":true
            //"is_full_docx_ready":true,"is_full_pdf_ready":true,"is_full_uncomparison_docx_ready":true,"is_full_uncomparison_pdf_ready":true
            //,"preview_status":2}
            //result = "1";
            var entity = GetFileResult(CommonHelper.SubString(html, "\"fileId\":\"", "\""));

            if (!string.IsNullOrEmpty(entity.autoText))
            {
                var fileType = CommonHelper.GetFileType(CommonHelper.SubString(html, "\"fileType\":\"", "\""));
                var file = new DownLoadInfo()
                {
                    fileType = fileType,
                    desc = "迅捷PDF-" + fileType.ToString(),
                    url = CommonHelper.SubString(html, "\"url\":\"", "\""),
                };
                entity.viewUrl = file.url;
                entity.files.Add(file);
                //entity.downloadHtml = ConstHelper.GetDownLoadHtml(entity, OcrType.GetHashCode(), true);
            }
            return entity;
        }

        public override ProcessStateEntity QueryFileStatuMethod(string taskId)
        {
            var html = PostFileResult(taskId);
            var processStatus = new ProcessStateEntity()
            {
                state = OcrProcessState.未知状态,
                taskId = taskId
            };
            if (html.Contains("成功"))
            {
                processStatus.state = OcrProcessState.处理成功;
                processStatus.desc = "处理完毕，可以下载了！";
            }
            else if (html.Contains("正在处理"))
            {
                processStatus.state = OcrProcessState.处理中;
                processStatus.desc = string.Format("处理中，请稍后…", processStatus.privewPercent, processStatus.percent);
            }
            else if (html.Contains("未处理"))
            {
                processStatus.state = OcrProcessState.待处理;
                processStatus.desc = "排队中，请稍后…";
            }
            else if (html.Contains("失败"))
            {
                processStatus.state = OcrProcessState.处理失败;
                processStatus.desc = "处理失败，详细：" + html;
            }
            else if (!string.IsNullOrEmpty(html))
            {
                processStatus.desc = "处理结果：" + html;
            }
            else
            {
                Console.WriteLine("彩云翻译查询状态异常：" + html);
            }
            return processStatus;
        }
    }
}