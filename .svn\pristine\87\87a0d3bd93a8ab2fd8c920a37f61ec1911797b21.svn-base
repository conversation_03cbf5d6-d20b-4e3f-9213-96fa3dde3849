﻿<?xml version="1.0" encoding="utf-8"?>
<ClassDiagram MajorVersion="1" MinorVersion="1">
  <Interface Name="ServiceStack.Messaging.IMessageService">
    <Position X="0.5" Y="0.5" Width="1.75" />
    <Compartments>
      <Compartment Name="Properties" Collapsed="true" />
    </Compartments>
    <TypeIdentifier />
  </Interface>
  <Interface Name="ServiceStack.Messaging.IMessageFactory" BaseTypeListCollapsed="true">
    <Position X="2.5" Y="2" Width="2.25" />
    <TypeIdentifier />
  </Interface>
  <Interface Name="ServiceStack.Messaging.IMessageQueueClientFactory">
    <Position X="2.5" Y="0.5" Width="2.25" />
    <TypeIdentifier />
  </Interface>
  <Interface Name="ServiceStack.Messaging.IMessageProducer">
    <Position X="5" Y="0.5" Width="2" />
    <TypeIdentifier />
  </Interface>
  <Interface Name="ServiceStack.Messaging.IMessageQueueClient">
    <Position X="5" Y="2" Width="2" />
    <TypeIdentifier />
  </Interface>
  <Interface Name="ServiceStack.Messaging.IMessage">
    <Position X="0.5" Y="3.25" Width="1.75" />
    <TypeIdentifier />
  </Interface>
  <Interface Name="ServiceStack.Messaging.IMessage&lt;T&gt;">
    <Position X="2.5" Y="3.5" Width="2.25" />
    <TypeIdentifier />
  </Interface>
  <Font Name="Segoe UI" Size="9" />
</ClassDiagram>