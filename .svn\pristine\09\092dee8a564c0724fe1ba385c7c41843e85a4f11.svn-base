﻿using CommonLib;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Web.Script.Serialization;

namespace TableOcr
{
    public class ConstHelper
    {
        public static JavaScriptSerializer JavaScriptSerializer = new JavaScriptSerializer();
        //public static Dictionary<string, string> lstBaiDuOcrTypes = new Dictionary<string, string>();
        //public static Dictionary<string, string> lstBaiDuOcrAPIs = new Dictionary<string, string>();

        static ConstHelper()
        {
            //https://ai.baidu.com/docs#/OCR-API/8368a4e3

            //lstBaiDuOcrTypes.Add("表格文字识别", "https%3A%2F%2Faip.baidubce.com%2Frest%2F2.0%2Focr%2Fv1%2Fform");

            //lstBaiDuOcrAPIs.Add("表格文字识别", "https://aip.baidubce.com/rest/2.0/ocr/v1/form");
        }

        public static void Init()
        {
            var dicTypes = InitRecTypes();
            BaseRecHelper.InitOcrTypes(OcrType.表格, dicTypes);
        }

        static Dictionary<int, BaseRec> InitRecTypes()
        {
            var dicOcrs = new Dictionary<int, BaseRec>();
            var allOCRS = Assembly.GetExecutingAssembly().GetTypes()
                .Where(t => !t.IsAbstract && t.BaseType.Equals(typeof(BaseTableRec))).ToList();

            if (allOCRS != null && allOCRS.Any())
            {
                allOCRS.ForEach(p =>
                {
                    Type objtype = Type.GetType(p.FullName, true);
                    var obj = Activator.CreateInstance(objtype) as BaseTableRec;
                    if (obj != null)
                    {
                        dicOcrs.Add(obj.OcrType.GetHashCode(), obj);
                    }
                });
            }
            return dicOcrs;
        }
    }
}
