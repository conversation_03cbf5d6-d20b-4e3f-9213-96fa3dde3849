﻿using CommonLib;
using System;
using System.Collections.Generic;
using System.Collections.Specialized;
using System.Diagnostics;
using System.IO;
using System.Net;
using System.Text;

namespace HanZiOcr
{
    /// <summary>
    /// 飞桨官网
    /// https://www.paddlepaddle.org.cn
    /// https://www.paddlepaddle.org.cn/hub/scene/ocr
    /// </summary>
    public class FeiJiangRec : BaseOcrRec
    {
        public FeiJiangRec()
        {
            OcrType = HanZiOcrType.飞桨;

            MaxExecPerTime = 23;

            LstJsonPreProcessArray = new List<object>() { "result" };
            LstJsonNextProcessArray = new List<object>() { "data" };
            IsSupportVertical = true;
            StrResultJsonSpilt = "text";
            LstVerticalLocation = new List<object>() { "text_box_position" };
            IsSupportUrlOcr = true;
        }

        protected override string GetHtml(OcrContent content)
        {
            var url = "https://www.paddlepaddle.org.cn/paddlehub-api/image_classification/chinese_ocr_db_crnn_mobile";
            var strPost = "{\"image\":\"" + content.strBase64 + "\"}";
            var strTmp = WebClientSyncExt.GetHtml(url, "", strPost, "", ExecTimeOutSeconds);

            return strTmp;
        }

        public override string GetHtmlByUrl(OcrContent content)
        {
            var url = "https://www.paddlepaddle.org.cn/paddlehub-api/image_classification/chinese_ocr_db_crnn_mobile";
            var strPost = "{\"image_url\":\"" + content.url + "\"}";
            var strTmp = WebClientSyncExt.GetHtml(url, "", strPost, "", ExecTimeOutSeconds);

            return strTmp;
        }

    }
}