﻿using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading;

namespace CommonLib
{
    public class LocalMemoryCache<T>
    {
        public string CacheName { get; set; }
        public TimeSpan TsExpired { get; set; }
        public bool ClearAfterGet { get; set; }

        private string FileDirectory;

        public LocalMemoryCache(string dbName, TimeSpan tsExpired, bool isNeverExpired, bool clearAfterGet)
        {
            ClearAfterGet = clearAfterGet;
            CacheName = dbName;
            TsExpired = tsExpired;
            FileDirectory = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Cache", CacheName);
            try
            {
                if (!Directory.Exists(FileDirectory))
                {
                    Directory.CreateDirectory(FileDirectory);
                }
            }
            catch (Exception oe)
            {
                LogHelper.Log.Error("创建缓存目录出错！" + CacheName, oe);
            }
            if (!isNeverExpired)
            {
                var timer = new System.Timers.Timer
                {
                    Interval = 10000
                };
                timer.Elapsed += (object sender, System.Timers.ElapsedEventArgs e) =>
                {
                    CleanExpiredObjects();
                };
                timer.Start();
            }
        }

        private Mutex GetMutex(string objId)
        {
            if (Mutex.TryOpenExisting(CacheName + objId, out Mutex mutex))
            {
                mutex.WaitOne();
            }
            else
            {
                mutex = new Mutex(false, CacheName + objId);
                mutex.WaitOne();
            }
            return mutex;
        }

        public int KeysCount(DateTime dtMin)
        {
            var files = Directory.GetFiles(FileDirectory);
            if (dtMin.Year > 1000)
                return files.Where(p => new FileInfo(p).LastWriteTime >= dtMin).Count();
            return files.Length;
        }

        public List<string> Keys()
        {
            return Directory.GetFiles(FileDirectory).Select(p => p.Replace(FileDirectory, "").Replace(":", "-").Replace(".", "_").Replace("/", "")).ToList();
        }

        private string GetFileName(string objectId)
        {
            return Path.Combine(FileDirectory, objectId.Replace(":", "-").Replace(".", "_"));
        }

        public void Remove(string objectId)
        {
            using (var mutex = GetMutex(objectId))
            {
                try
                {
                    var filePath = GetFileName(objectId);
                    File.Delete(filePath);
                }
                catch (Exception ex)
                {
                    LogHelper.Log.Error("Set Error：" + ex.Message, ex);
                }
                finally
                {
                    mutex.ReleaseMutex();
                }
            }
        }

        public void Set(string objectId, T obj)
        {
            using (var mutex = GetMutex(objectId))
            {
                try
                {
                    var filePath = GetFileName(objectId);
                    File.WriteAllText(filePath, JsonConvert.SerializeObject(obj), Encoding.UTF8);
                }
                catch (Exception ex)
                {
                    LogHelper.Log.Error("Set Error：" + ex.Message, ex);
                }
                finally
                {
                    mutex.ReleaseMutex();
                }
            }
        }

        public void Increment(string objectId)
        {
            using (var mutex = GetMutex(objectId))
            {
                try
                {
                    var filePath = GetFileName(objectId);
                    int result = 0;
                    if (File.Exists(filePath))
                    {
                        try
                        {
                            result = JsonConvert.DeserializeObject<int>(File.ReadAllText(filePath, Encoding.UTF8));
                        }
                        catch { }
                    }
                    long newValue = Interlocked.Increment(ref result);
                    File.WriteAllText(filePath, newValue.ToString(), Encoding.UTF8);
                }
                catch (Exception ex)
                {
                    LogHelper.Log.Error("Set Error：" + ex.Message, ex);
                }
                finally
                {
                    mutex.ReleaseMutex();
                }
            }
        }

        public void Decrement(string objectId)
        {
            using (var mutex = GetMutex(objectId))
            {
                try
                {
                    var filePath = GetFileName(objectId);
                    int result = 0;
                    if (File.Exists(filePath))
                    {
                        try
                        {
                            result = JsonConvert.DeserializeObject<int>(File.ReadAllText(filePath, Encoding.UTF8));
                        }
                        catch { }
                    }
                    long newValue = Interlocked.Decrement(ref result);
                    File.WriteAllText(filePath, newValue.ToString(), Encoding.UTF8);
                }
                catch (Exception ex)
                {
                    LogHelper.Log.Error("Set Error：" + ex.Message, ex);
                }
                finally
                {
                    mutex.ReleaseMutex();
                }
            }
        }

        public T Get(string objectId)
        {
            using (var mutex = GetMutex(objectId))
            {
                var result = default(T);
                try
                {
                    var filePath = GetFileName(objectId);
                    if (File.Exists(filePath))
                    {
                        result = JsonConvert.DeserializeObject<T>(File.ReadAllText(filePath, Encoding.UTF8));
                        if (ClearAfterGet)
                        {
                            File.Delete(filePath);
                        }
                    }
                }
                catch (Exception ex)
                {
                    LogHelper.Log.Error("Get Error：" + ex.Message, ex);
                }
                finally
                {
                    mutex.ReleaseMutex();
                }
                return result;
            }
        }

        private void CleanExpiredObjects()
        {
            using (var mutex = GetMutex("Clean"))
            {
                try
                {
                    var files = Directory.GetFiles(FileDirectory);
                    foreach (var file in files)
                    {
                        try
                        {
                            if (new FileInfo(file).LastWriteTime.Add(TsExpired) < DateTime.Now)
                            {
                                try
                                {
                                    File.Delete(file);
                                }
                                catch { }
                            }
                        }
                        catch { }
                    }
                }
                catch (Exception ex)
                {
                    LogHelper.Log.Error("CleanExpiredObjects时出现错误：" + ex.Message, ex);
                }
                finally
                {
                    mutex.ReleaseMutex();
                }
            }
        }
    }

}
