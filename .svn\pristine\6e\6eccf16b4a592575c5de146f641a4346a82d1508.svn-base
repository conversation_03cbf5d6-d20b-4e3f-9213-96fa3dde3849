let Navbar=function(){let e=[{index:"首页"},{"json-format":"JSON美化"},{"json-diff":"JSON比对"},{"en-decode":"编码转换"},{"qr-code":"二维码/解码"},{"image-base64":"图片Base64"},{"code-beautify":"代码美化"},{more:"更多FH工具&gt;&gt;"}],l='<div class="mod-pageheader">\n        <div class="mod-sitedesc mod-fehelper">\n            <div class="logo-box"><span class="q-logo"><img src="https://static.baidufe.com/fehelper/static/img/fe-128.png" title="WEB前端助手（FeHelper）" alt="WEB前端助手（FeHelper）"></span> \n            </div>\n            <div class="mod-btitle">\n                <div class="x-name"><a href="/fehelper/index/index.html">Fe<PERSON>elper ( 浏览器插件 )</a><button id="btnInstallExtension" style="display: none"></button></div>\n                <div class="x-desc">2011年，FeHelper作为开发者专用的Chrome浏览器扩展在Google Chrome Webstore发布1.0版本，截至目前持续更新中，欢迎大家安装使用！</div>\n            </div>\n        </div>\n        <div class="mod-topbar" id="modTopbar">\n            <div class="wrapper-box clearfix">\n                <div class="mainnav-box"><ul class="q-menubox">#fe-menus-here#</ul></div>\n                <div class="subnav-box">\n                    <ul class="q-navbox">\n                        <li class="q-navitem">\n                            <a href="/fehelper/feedback.html" class="x-fbk"><span>意见反馈&gt;&gt;</span></a>\n                        </li>\n                    </ul>\n                </div>\n            </div>\n        </div>\n    </div>'.replace(/\n+|\s{2,}/gm," ").replace("#fe-menus-here#",e.map(e=>{let l=Object.keys(e)[0],n=`/fehelper/${l}/index.html`,i="";if("undefined"!=typeof window&&void 0!==window.document){let e=location.pathname.split("/").slice(-2).shift()||"fehelper";i=l===e?"q-selected":"",n=`${"fehelper"===e?"../fehelper":".."}/${l}/index.html`}return`<li class="q-menuitem ${i}"><a href="${n}">${e[l]}</a></li>`}).join('<li class="q-sp">|</li>'));if("undefined"!=typeof window)if(document.querySelectorAll("#pageContainer>div.mod-pageheader").length){let e=location.pathname.split("/").slice(-2).shift()||"fehelper";document.querySelector(`li.q-menuitem a[href$="${e}/index.html"]`).parentNode.classList.add("q-selected")}else{let e=document.createElement("div");e.innerHTML=l;let n=document.querySelector("#pageContainer");n.insertBefore(e.childNodes[0],n.firstChild)}return{htmlHeader:l,tools:e}}();"undefined"!=typeof module&&(module.exports=Navbar);