﻿<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <PackageId>ServiceStack.Extensions</PackageId>
        <AssemblyName>ServiceStack.Extensions</AssemblyName>
        <RootNamespace>ServiceStack</RootNamespace>
        <LangVersion>default</LangVersion>
        <Title>ServiceStack.Extensions</Title>
        <PackageDescription>ServiceStack extensions for .NET Core Apps, including GrpcFeature</PackageDescription>
        <PackageTags>ServiceStack;gRPC;server;GrpcFeature</PackageTags>
        <TargetFrameworks>net5.0;net6.0</TargetFrameworks>
    </PropertyGroup>

    <ItemGroup Condition=" '$(TargetFramework)' == 'net5.0' ">
        <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="5.0.17" />
    </ItemGroup>
    <ItemGroup Condition=" '$(TargetFramework)' == 'net6.0' ">
        <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="6.0.5" />
    </ItemGroup>

    <ItemGroup>
        <ProjectReference Include="..\..\..\ServiceStack.Text\src\ServiceStack.Text\ServiceStack.Text.csproj" />
        <ProjectReference Include="..\ServiceStack.Interfaces\ServiceStack.Interfaces.csproj" />
        <ProjectReference Include="..\ServiceStack.Client\ServiceStack.Client.csproj" />
        <ProjectReference Include="..\ServiceStack.Common\ServiceStack.Common.csproj" />
        <ProjectReference Include="..\ServiceStack\ServiceStack.csproj" />
        <ProjectReference Include="..\ServiceStack.GrpcClient\ServiceStack.GrpcClient.csproj" />

        <PackageReference Include="Grpc.AspNetCore.Server" Version="2.46.0" />
        <PackageReference Include="Grpc.Core.Api" Version="2.46.1" />
        <PackageReference Include="protobuf-net" Version="3.1.4" />
        <PackageReference Include="protobuf-net.Grpc.AspNetCore" Version="1.0.152" />
        
        <PackageReference Include="System.Threading.Channels" Version="5.0.0" />

        <PackageReference Include="Microsoft.IdentityModel.Tokens" Version="6.17.0" />
        <PackageReference Include="System.IdentityModel.Tokens.Jwt" Version="6.17.0" />
    </ItemGroup>

</Project>
