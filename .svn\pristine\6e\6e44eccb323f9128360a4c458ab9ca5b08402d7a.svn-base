﻿using System;
using System.Windows.Forms;

namespace NewTicket
{
    public partial class frm12306 : Form
    {
        public string User { get; set; }

        public string Cookie { get; set; }

        public string Url { get; set; }

        public string IpAddress { get; set; }

        public frm12306()
        {
            InitializeComponent();
            wbNet.ScriptErrorsSuppressed = true;
        }

        private void frm12306_Load(object sender, EventArgs e)
        {
            this.Text = "12306官网-当前用户【" + User + "】";
            var acceptLanguageHeader = "Cookie:" + <PERSON>ie;
            //if (!string.IsNullOrEmpty(IpAddress))
            //{
            //    Url = Url.Replace("kyfw.12306.cn", IpAddress);
            //    acceptLanguageHeader += "\nHost:kyfw.12306.cn";
            //}
            wbNet.Navigate(Url, null, null, acceptLanguageHeader);
        }

        //对错误进行处理 
        void Window_Error(object sender, HtmlElementErrorEventArgs e)
        {
            // 自己的处理代码 
            e.Handled = true;
        }

        private void wbNet_DocumentCompleted(object sender, WebBrowserDocumentCompletedEventArgs e)
        {
            try
            {
                if (wbNet.Document != null)
                    wbNet.Document.Window.Error += new HtmlElementErrorEventHandler(Window_Error);
            }
            catch (Exception oe)
            {
                Console.WriteLine(oe.Message);
            }
        }
    }
}
