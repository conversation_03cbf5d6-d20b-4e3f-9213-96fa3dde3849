﻿<%@ Master Language="C#" AutoEventWireup="true" CodeBehind="Site.master.cs" Inherits="Account.Web.Site" %>

<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" xml:lang="zh-CN" lang="zh-CN">
<head runat="server">
    <meta http-equiv="X-UA-Compatible" content="IE=Edge,chrome=1" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <title>OCR文字识别助手_致力于提升您的工作效率！</title>
    <meta name="description" content="OCR文字识别助手是一款集文字、表格、公式、文档及翻译于一体的高效率生产力工具。支持一键截图/贴图，支持控件中元素的精确选择；支持提取图片、文档的内容，高精度还原文件布局和格式；支持百度、有道、阿里、腾讯、讯飞、汉王、MathPix等15+高精度通道；" />
    <meta name="keywords" content="文字识别，图片识别，识别图片中的文字，图片文字识别，图片转文字，图片转表格，图片转公式，文字识别软件，在线文字识别，在线OCR，OCR识别，OCR，名片识别，证件识别，文档识别，OCR助手，OCR文字识别助手，OCR图片文字识别助手" />
    <asp:ContentPlaceHolder ID="head" runat="server">
    </asp:ContentPlaceHolder>
    <link rel="stylesheet" href="statics/css/new-base.css" type="text/css" />
    <link rel="stylesheet" href="statics/css/animate.css" type="text/css" />
    <link rel="stylesheet" href="statics/css/bootstrap.min.css" type="text/css" />
    <link rel="stylesheet" href="statics/css/global.css" type="text/css" />

    <script src="statics/js/jquery-3.6.0.min.js" type="text/javascript"></script>
    <script src="statics/js/jquery.scrollAnimations.min.js" type="text/javascript"></script>
</head>
<body>
    <div class="home-container root">
        <link rel="stylesheet" type="text/css" href="statics/css/index1.css" />
        <script src="statics/js/index.min1.js" defer="" type="text/javascript"></script>

        <header class="v4_header_pc">
            <div sg-data-eventtype="FORM_SUBMIT_SUCCEESS" sg-data-convertid="2708317" class="sg-data"></div>
            <div class="header_pc_left">
                <a href="Index.aspx" title="首页 | AI智能文字识别" class="pc-logo-wrap ml-4 mr-5">
                    <img class="pc-logo" src="https://scm-file-new-1301864755.cos.ap-beijing.myqcloud.com/f0481b1f-3226-415a-bcc4-9b28e2fb0f9f/static/v1/image/icon-logo.png">
                </a>
                <ul class="top-nav">
                    <li>
                        <a class="color-default" href="Index.aspx">首页</a>&nbsp;
                    </li>
                    <li>
                        <a class="color-default" data-sub="product-sub-nav">文字识别</a>&nbsp;
              <div class="triangle"></div>
                    </li>
                    <li>
                        <a href="Version.aspx" class="color-default">
                            <span>会员版本</span>
                        </a>
                    </li>
                    <li>
                        <a href="Status.aspx;" class="color-default">
                            <span>服务状态</span>
                        </a>
                    </li>
                    <li>
                        <a class="color-default" data-sub="cloud-sub-nav">关于我们</a>&nbsp;
              <div class="triangle"></div>
                    </li>

                </ul>
                <div class="product-con">
                    <!-- 产品服务 -->
                    <section id="product-sub-nav" class="pls-nav-dropdown" style="visibility: hidden; height: 0px; display: none;">
                        <div class="d-flex nav-drown-con" style="height: 0px; transition: transform 0.2s ease-in-out 0s,opacity 0.2s ease-in-out 0s; opacity: 0; transform: translate(0px, -100%);">
                            <div class="prdocu-sub-left">
                                <div class="mr-auto">
                                    <img class="p-icon" src="statics/picture/p_4.png" alt="">
                                    <div class="mt-4">
                                        <h6>OCR文字识别助手</h6>
                                        <p class="color-gray fn14 mt-3 sub-left-desc">集文字、表格、公式、文档及翻译</p>
                                        <p class="color-gray fn14 mt-3 sub-left-desc">于一体的高效率生产力工具。</p>
                                        <p class="color-gray fn14 mt-3 sub-left-desc">支持一键截图/贴图；</p>
                                        <p class="color-gray fn14 mt-3 sub-left-desc">高精度还原图片/文档内容；</p>
                                        <p class="color-gray fn14 mt-3 sub-left-desc">支持百度/有道/阿里等15+高精度通道；</p>
                                        <a class="link block disa fn14 contact-business shake" href="Detail.aspx">立即体验 <span class="iconfont4 icon-xiangzuo gt"></span></a>
                                    </div>
                                </div>
                            </div>

                            <div class="bg-white prdocu-sub-right">
                                <div>
                                    <h6 class="text-base-color">AI智能文字识别</h6>
                                    <div class="mt-4 d-flex flex-wrap">
                                        <a href="javascript:;" class="pro-item color-default">
                                            <div class="letter-wrap">
                                                <h6>通用识别</h6>
                                                <span class="color-gray fn14">多场景、多语种、高精度的整图文字检测和识别服务，可识别各类印刷和文档</span>
                                            </div>
                                        </a>

                                        <a href="javascript:;" class="pro-item color-default">
                                            <div class="letter-wrap">
                                                <h6>手写识别</h6>
                                                <span class="color-gray fn14">支持对手写中文、手写数字进行检测和识别，识别准确率可达90%以上</span>
                                            </div>
                                        </a>
                                        <a href="javascript:;" class="pro-item color-default">
                                            <div class="letter-wrap">
                                                <h6>卡证识别</h6>
                                                <span class="color-gray fn14">分类、识别、结构化输出22种票据，含增值税发票、行程单、出租车发票、火车票等</span>
                                            </div>
                                        </a>
                                        <a href="javascript:;" class="pro-item color-default">
                                            <div class="letter-wrap">
                                                <h6>公式识别</h6>
                                                <span class="color-gray fn14">识别教育场景所涉及的作业及试卷中公式、手写文字、题目等内容，可用于智能阅卷、搜题</span>
                                            </div>
                                        </a>
                                        <a href="javascript:;" class="pro-item color-default">
                                            <div class="letter-wrap">
                                                <h6>表格识别</h6>
                                                <span class="color-gray fn14">对图片中的表格文字内容进行提取和识别，支持识别完整框线/无框线表格，合并单元格等</span>
                                            </div>
                                        </a>
                                        <a href="javascript:;" class="pro-item color-default">
                                            <div class="letter-wrap">
                                                <h6>划词翻译</h6>
                                                <span class="color-gray fn14">支持将文本内容翻译成用户选择的目标语言</span>
                                            </div>
                                        </a>
                                        <a href="javascript:;" class="pro-item color-default">
                                            <div class="letter-wrap">
                                                <h6>图片翻译</h6>
                                                <span class="color-gray fn14">支持对图片中的文本内容OCR识别并翻译成目标语言</span>
                                            </div>
                                        </a>
                                        <a href="javascript:;" class="pro-item color-default">
                                            <div class="letter-wrap">
                                                <h6>文档翻译</h6>
                                                <span class="color-gray fn14">支持对文档中的文本内容OCR识别并翻译成目标语言，可实现Word/PPT/Excel/PDF等的全文翻译</span>
                                            </div>
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </section>
                    <section id="cloud-sub-nav" class="pls-nav-dropdown solution-part" style="visibility: hidden; height: 0px; display: none;">
                        <div class="d-flex nav-drown-con" style="height: 0px; transition: transform 0.2s ease-in-out 0s,opacity 0.2s ease-in-out 0s; opacity: 0; transform: translate(0px, -100%);">
                            <div class="prdocu-sub-left">
                                <div class="mr-auto">
                                    <img class="p-icon" src="statics/picture/p_1.png" alt="">
                                    <div class="mt-4">
                                        <h6>OCR文字识别助手</h6>
                                        <p class="color-gray fn14 mt-3 sub-left-desc">集文字、表格、公式、文档及翻译</p>
                                        <p class="color-gray fn14 mt-3 sub-left-desc">于一体的高效率生产力工具。</p>
                                        <p class="color-gray fn14 mt-3 sub-left-desc">支持一键截图/贴图；</p>
                                        <p class="color-gray fn14 mt-3 sub-left-desc">高精度还原图片/文档内容；</p>
                                        <p class="color-gray fn14 mt-3 sub-left-desc">支持百度/有道/阿里等15+高精度通道；</p>
                                        <a class="link block disa fn14 contact-business shake" href="Detail.aspx">立即体验 <span class="iconfont4 icon-xiangzuo gt"></span></a>
                                    </div>
                                </div>
                            </div>
                            <div class="bg-white prdocu-sub-right">
                                <div>
                                    <h6 class="text-base-color">关于我们</h6>
                                    <div class="mt-4 d-flex flex-wrap">
                                        <a href="javascript:;" class="pro-item color-default">
                                            <div class="letter-wrap">
                                                <h6>关于我们</h6>
                                                <span class="color-gray fn14">关于OCR文字识别助手</span>
                                            </div>
                                        </a>
                                        <a href="privacy.html" target="_blank" class="pro-item color-default">
                                            <div class="letter-wrap">
                                                <h6>隐私协议</h6>
                                                <span class="color-gray fn14">互惠共赢生态体系</span>
                                            </div>
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </section>
                </div>
            </div>
            <div class="header_pc_right">
                <%
                    var session = Session["user"] as Account.Web.CodeEntity;
                    var isLogin = session != null && !string.IsNullOrEmpty(session.StrAppCode);
                    if (!isLogin)
                    {
                %>
                <a class="pc_register my-button" type="primary" href="User.aspx">登录</a>
                <%}
                    else
                    {  %>
                <a class="pc_register my-button" type="primary" href="User.aspx">个人中心</a>
                <a class="pc_login mr-5 pr-4 color-default" href="UserIndex.aspx?op=logout">注销登录</a>
                <%} %>
            </div>
        </header>
        <form id="form1" runat="server">
            <div>
                <asp:ContentPlaceHolder ID="ContentPlaceHolder1" runat="server">
                </asp:ContentPlaceHolder>
            </div>
        </form>

        <div class="fixed-icon">
            <div style="width: 120px; position: relative; left: 1rem; top: -12px;">
                <!-- <img src="/libraries_v2/images/v3images/caigou.gif"/> -->
            </div>
            <a id="webchat" class="wx-con disa" href="http://wpa.qq.com/msgrd?v=3&uin=365833440&site=qq&menu=yes">
                <img class="wechat-ico" src="statics/picture/qq.svg" />
                <div class="wx-text">
                    QQ客服(365833440)
                </div>
                <label class="badge badge-danger" style="background: rgb(255, 34, 122); display: inline-block;">1</label>
            </a>

            <a class="wx-con" href="https://qm.qq.com/cgi-bin/qm/qr?k=bohNy9GMdgD5bsYrB-fcIjy-s6Ot3zOg&jump_from=webapi&qr=1" target="_blank">
                <img class="wechat-ico" src="statics/picture/qq.svg" />
                <div class="wx-text">
                    QQ群(100029010)
                </div>
                <label class="badge badge-danger" style="background: rgb(255, 34, 122); display: inline-block;">2</label>
            </a>
            <a href="mailto:<EMAIL>" class="wx-con">
                <img class="wechat-ico" src="statics/picture/im.svg" />
                <div class="wx-text" style="width: 240px; text-align: left; left: -15.3rem; padding: 10px 20px;">
                    <div style="font-size: 15px">
                        邮箱:<EMAIL>
                    </div>
                    <hr style="background: #c1c1c1; margin: 10px 10px 6px 0;" />
                    <span style="font-size: 13px; color: #c1c1c1">感谢您的意见与建议！</span>
                </div>
                <label class="badge badge-danger" style="background: rgb(255, 34, 122); display: inline-block;">3</label>
            </a>
        </div>

        <style>
            .content {
                width: 100%;
                padding-right: 0 !important;
                padding-left: 0 !important;
                margin-right: auto;
                margin-left: auto;
            }
        </style>
        <section class="register-section">
            <div class="container">
                <h1 class="fnbold huoban"><span style="color: #1764ff">OCR文字识别助手</span>&nbsp;致力于提升您的工作效率！</h1>
                <p class="color-gray fn18 btom-text" style="margin-top: 20px">AI智能文字识别一站式服务平台！</p>
            </div>
        </section>
        <footer class="page-footer">
            <div class="container">
                <div class="row foot-cont pt-4 no-gutters">
                    <div class="col-12 line"></div>
                    <div class="col-lg-9 hu-an p-0 mb-3 mt-3 text-l color-gray">OCR文字识别助手&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; ©️ <%=DateTime.Now.Year %> ALL RIGHTS RESERVED. 保留所有权利&nbsp;&nbsp;&nbsp;|&nbsp;&nbsp;&nbsp;<a href="privacy.html">隐私协议</a>&nbsp;&nbsp;&nbsp;</div>
                    <div class=" col-lg-3 p-0 hu-an icp bei-an mb-3 mt-3">
                        <a class="mb-2 color-gray" href="https://beian.miit.gov.cn/" target="_blank">鄂 ICP 备 2021012692 号</a>
                    </div>
                </div>
            </div>
        </footer>
    </div>
</body>
</html>
