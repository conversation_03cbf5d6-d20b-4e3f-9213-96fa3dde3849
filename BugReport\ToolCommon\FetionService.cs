﻿using System;
using System.Collections.Generic;
using System.Text;

namespace ToolCommon
{
    public class FetionService
    {
        private static string strLoginInitURL = "http://f.10086.cn/huc/user/opauth/login.do?m=tologin";//getCookie
        private static string strLoginURL = "http://f.10086.cn/huc/user/opauth/login.do";
        private static string strFetionInitURL = "http://f.10086.cn/im5/login/login.action?mnative=0";
        private static string strSendMSGURL = "http://f.10086.cn/im5/contact/sendMsg.action";//mobileNo=18217521743&msg=cesss
        private static string strSendMSGURL2 = "http://quanapi.sinaapp.com/fetion.php?u={0}&p={1}&to={2}&m={3}";

        public static bool SendMSG(string strMobile, string strPWD, string toPhone, string strContent)
        {
            bool result = false;
            try
            {
                if (!string.IsNullOrEmpty(strMobile) && !string.IsNullOrEmpty(strPWD))
                {
                    strContent = string.IsNullOrEmpty(strContent) ? "这是一条测试短信！" : strContent;
                    toPhone = string.IsNullOrEmpty(toPhone) ? strMobile : toPhone;
                    result = SendMSG2(strMobile, strPWD, toPhone, strContent);
                    if (!result)
                        result = SendMSG1(strMobile, strPWD, toPhone, strContent);
                }
            }
            catch { }
            return result;
        }

        static bool Login(string strMobile, string strPWD, ref string strCookie)
        {
            bool result = false;
            if (!string.IsNullOrEmpty(strMobile) && !string.IsNullOrEmpty(strPWD))
            {
                string html = WebClientExt.GetHtml(strLoginInitURL, ref strCookie);
                if (!string.IsNullOrEmpty(strCookie))
                {
                    html = WebClientExt.GetHtml(strLoginURL, ref strCookie, ""
                        , string.Format("mobilenum={0}&password={1}&m=submit&backurl=http%3A%2F%2Ff.10086.cn%2F&fr=opauth", strMobile, strPWD), "http://f.10086.cn/huc/user/opauth/login.do?m=tologin");
                    result = true;
                    //html = WebClientExt.GetHtml(strFetionInitURL, ref strCookie);
                    //if (!string.IsNullOrEmpty(html) && html.IndexOf("var nickName = '';") < 0)
                    //    result = true;
                }
            }
            return result;
        }

        private static bool SendMSG1(string strMobile, string strPWD, string toPhone, string strContent)
        {
            bool result = false;
            try
            {
                if (!string.IsNullOrEmpty(strMobile) && !string.IsNullOrEmpty(strPWD))
                {
                    string strCookie = "";
                    if (Login(strMobile, strPWD, ref strCookie))
                    {
                        for (int i = 0; i < 2; i++)
                        {
                            string html = WebClientExt.GetHtml(strSendMSGURL, ref strCookie, ""
                                , string.Format("mobileNo={0}&msg={1}", toPhone, strContent)
                                , "http://f.10086.cn/huc/user/opauth/login.do?m=tologin");
                            result = html.IndexOf("\"success\":200") > 0;
                            if (result)
                                break;
                        }
                    }
                }
            }
            catch { }
            return result;
        }

        private static bool SendMSG2(string strMobile, string strPWD, string toPhone, string strContent)
        {
            string responseText = string.Empty;
            try
            {
                responseText = WebClientExt.GetHtml(string.Format(strSendMSGURL2, new object[]
			    {
				    Uri.EscapeDataString( strMobile),
				    Uri.EscapeDataString(strPWD),
				    Uri.EscapeDataString(toPhone),
				    Uri.EscapeDataString(strContent)
			    }));
            }
            catch { }
            return !string.IsNullOrEmpty(responseText) && !responseText.Contains("result\":-2");
        }
    }
}
