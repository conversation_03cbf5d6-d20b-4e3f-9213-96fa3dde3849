function _typeof(o) { "@babel/helpers - typeof"; return _typeof = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function (o) { return typeof o } : function (o) { return o && "function" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? "symbol" : typeof o }, _typeof(o) } function _classCallCheck(a, b) { if (!(a instanceof b)) { throw new TypeError("Cannot call a class as a function"); } } function _defineProperties(a, b) { for (var i = 0; i < b.length; i++) { var c = b[i]; c.enumerable = c.enumerable || false; c.configurable = true; if ("value" in c) c.writable = true; Object.defineProperty(a, _toPropertyKey(c.key), c) } } function _createClass(a, b, c) { if (b) _defineProperties(a.prototype, b); if (c) _defineProperties(a, c); Object.defineProperty(a, "prototype", { writable: false }); return a } function _toPropertyKey(t) { var i = _toPrimitive(t, "string"); return "symbol" == _typeof(i) ? i : i + "" } function _toPrimitive(t, r) { if ("object" != _typeof(t) || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || "default"); if ("object" != _typeof(i)) return i; throw new TypeError("@@toPrimitive must return a primitive value."); } return ("string" === r ? String : Number)(t) } function _createForOfIteratorHelper(o, b) { var c = typeof Symbol !== "undefined" && o[Symbol.iterator] || o["@@iterator"]; if (!c) { if (Array.isArray(o) || (c = _unsupportedIterableToArray(o)) || b && o && typeof o.length === "number") { if (c) o = c; var i = 0; var F = function F() { }; return { s: F, n: function n() { if (i >= o.length) return { done: true }; return { done: false, value: o[i++] } }, e: function e(a) { throw a; }, f: F } } throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method."); } var d = true, didErr = false, err; return { s: function s() { c = c.call(o) }, n: function n() { var a = c.next(); d = a.done; return a }, e: function e(a) { didErr = true; err = a }, f: function f() { try { if (!d && c.return != null) c.return() } finally { if (didErr) throw err; } } } } function _unsupportedIterableToArray(o, a) { if (!o) return; if (typeof o === "string") return _arrayLikeToArray(o, a); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === "Object" && o.constructor) n = o.constructor.name; if (n === "Map" || n === "Set") return Array.from(o); if (n === "Arguments" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, a) } function _arrayLikeToArray(a, b) { if (b == null || b > a.length) b = a.length; for (var i = 0, arr2 = new Array(b); i < b; i++)arr2[i] = a[i]; return arr2 } var _translate = { version: '3.3.0.20240430', useVersion: 'v2', isDiscriminateLanguage: false, setUseVersion2: function setUseVersion2() { _translate.useVersion = 'v2'; console.log('提示：自 v2.10 之后的版本默认就是使用V2版本（当前版本为:' + _translate.version + '）， translate.setUseVersion2() 可以不用再加这一行了。当然加了也无所谓，只是加了跟不加是完全一样的。') }, translate: null, includedLanguages: 'zh-CN,zh-TW,en', resourcesUrl: '//res.zvo.cn/translate', selectLanguageTag: { documentId: 'translate', show: true, languages: '', alreadyRender: false, selectOnChange: function selectOnChange(a) { var b = a.target.value; _translate.changeLanguage(b) }, refreshRender: function refreshRender() { var a = document.getElementById(_translate.selectLanguageTag.documentId + "SelectLanguage"); if (a) { a.parentNode.removeChild(a) } _translate.selectLanguageTag.alreadyRender = false; _translate.selectLanguageTag.render() }, customUI: function customUI(b) { var c = function c(a) { _translate.selectLanguageTag.selectOnChange(a) }; var d = document.createElement("select"); d.id = _translate.selectLanguageTag.documentId + 'SelectLanguage'; d.className = _translate.selectLanguageTag.documentId + 'SelectLanguage'; for (var i = 0; i < b.length; i++) { var e = document.createElement("option"); e.setAttribute("value", b[i].id); if (_translate.selectLanguageTag.languages.length > 0) { var f = (',' + _translate.selectLanguageTag.languages + ',').toLowerCase(); if (f.indexOf(',' + b[i].id.toLowerCase() + ',') < 0) { continue } } if (_translate.to != null && typeof _translate.to != 'undefined' && _translate.to.length > 0) { if (_translate.to == b[i].id) { e.setAttribute("selected", 'selected') } } else { if (b[i].id == _translate.language.getLocal()) { e.setAttribute("selected", 'selected') } } e.appendChild(document.createTextNode(b[i].name)); d.appendChild(e) } if (window.addEventListener) { d.addEventListener('change', c, false) } else { d.attachEvent('onchange', c) } document.getElementById(_translate.selectLanguageTag.documentId).appendChild(d) }, render: function render() { if (_translate.selectLanguageTag.alreadyRender) { return } _translate.selectLanguageTag.alreadyRender = true; if (!_translate.selectLanguageTag.show) { return } if (document.getElementById(_translate.selectLanguageTag.documentId) == null) { var a = document.getElementsByTagName('body')[0]; var b = document.createElement("div"); b.id = _translate.selectLanguageTag.documentId; a.appendChild(b) } else { if (document.getElementById(_translate.selectLanguageTag.documentId + 'SelectLanguage') != null) { return } } _translate.selectLanguageTag.customUI(_translate.service.edge.language.json) } }, changeLanguage: function changeLanguage(a) { _translate.useVersion = 'v2'; if (_translate.to != null && _translate.to.length > 0) { if (_translate.to != _translate.language.getLocal()) { var b = true } } _translate.to = a; _translate.storage.set('to', a); if (b) { location.reload() } else { _translate.execute() } }, to: '', autoDiscriminateLocalLanguage: true, documents: [], inProgressNodes: [], ignore: { tag: ['style', 'script', 'link', 'pre', 'code'], class: ['ignore', 'translateSelectLanguage', "date", "tooltipster-arrow-border"], id: [], isIgnore: function isIgnore(a) { if (a == null || typeof a == 'undefined') { return false } var b = a; var c = 100; while (c-- > 0) { if (b == null || typeof b == 'undefined') { return false } var d = _translate.element.getNodeName(b).toLowerCase(); if (d.length > 0) { if (d == 'body' || d == 'html' || d == '#document') { return false } if (_translate.ignore.tag.indexOf(d) > -1) { return true } } if (b.className != null) { var e = b.className; if (e == null || typeof e != 'string') { continue } e = e.trim().split(' '); for (var f = 0; f < e.length; f++) { if (e[f] != null && e[f].trim().length > 0) { if (_translate.ignore.class.indexOf(e[f]) > -1) { return true } } } } if (b.id != null && typeof b.id != 'undefined') { if (_translate.ignore.id.indexOf(b.id) > -1) { return true } } b = b.parentNode } return false } }, nomenclature: { data: new Array(), old_Data: [], set: function set(a) { alert('请将 translate.nomenclature.set 更换为 append，具体使用可参考： https://github.com/xnx3/translate ') }, append: function append(a, b, c) { if (typeof _translate.nomenclature.data[a] == 'undefined') { _translate.nomenclature.data[a] = new Array() } if (typeof _translate.nomenclature.data[a][b] == 'undefined') { _translate.nomenclature.data[a][b] = new Array() } var d = c.split('\n'); for (var e = 0; e < d.length; e++) { var f = d[e].trim(); if (f.length < 1) { continue } var g = f.split('='); if (g.length != 2) { continue } var h = g[0].trim(); var i = g[1].trim(); if (h.length == 0 || i.length == 0) { continue } _translate.nomenclature.data[a][b][h] = i } _translate.nomenclature.data[a][b] = _translate.util.objSort(_translate.nomenclature.data[a][b]) }, get: function get() { return _translate.nomenclature.data }, dispose: function dispose(a) { if (a == null || a.length == 0) { return a } if (typeof _translate.nomenclature.data[_translate.language.getLocal()] == 'undefined' || typeof _translate.nomenclature.data[_translate.language.getLocal()][_translate.to] == 'undefined') { return a } for (var b in _translate.nomenclature.data[_translate.language.getLocal()][_translate.to]) { var c = _translate.nomenclature.data[_translate.language.getLocal()][_translate.to][b]; if (typeof c == 'function') { continue } var d = a.indexOf(b); if (d > -1) { if (_translate.language.getLocal() == 'english') { var e = ''; if (d == 0) { } else { e = a.substr(d - 1, 1); var f = _translate.language.getCharLanguage(e); if (f == 'english') { continue } } var g = ''; if (d + b.length == a.length) { } else { g = a.substr(d + b.length, 1); var f = _translate.language.getCharLanguage(g); if (f == 'english') { continue } } a = a.replace(new RegExp(e + b + g, 'g'), e + c + g) } else { a = a.replace(new RegExp(b, 'g'), c) } } } return a } }, setAutoDiscriminateLocalLanguage: function setAutoDiscriminateLocalLanguage() { _translate.autoDiscriminateLocalLanguage = true }, nodeQueue: {}, setDocuments: function setDocuments(a) { if (a == null || typeof a == 'undefined') { return } if (typeof a.length == 'undefined') { _translate.documents[0] = a } else { _translate.documents = a } _translate.nodeQueue = {} }, getDocuments: function getDocuments() { if (_translate.documents != null && typeof _translate.documents != 'undefined' && _translate.documents.length > 0) { return _translate.documents } else { return document.all } }, listener: { isStart: false, start: function start() { _translate.temp_linstenerStartInterval = setInterval(function () { if (document.readyState == 'complete') { clearInterval(_translate.temp_linstenerStartInterval); if (!_translate.listener.isStart) _translate.listener.addListener() } }, 100) }, addListener: function addListener() { _translate.listener.isStart = true; _translate.listener.config = { attributes: true, childList: true, subtree: true, characterData: true, attributeOldValue: true, characterDataOldValue: true }; _translate.listener.callback = function (a, b) { var c = []; var d = _createForOfIteratorHelper(a), _step; try { for (d.s(); !(_step = d.n()).done;) { var e = _step.value; var f = []; if (e.type === 'childList') { if (e.addedNodes.length > 0) { f = e.addedNodes } else if (e.removedNodes.length > 0) { } else { } } else if (e.type === 'attributes') { } else if (e.type === 'characterData') { f = [e.target] } var g = _createForOfIteratorHelper(f), _step3; try { for (g.s(); !(_step3 = g.n()).done;) { var h = _step3.value; var i = false; for (var j = 0; j < c.length; j++) { if (c[j].isSameNode(h)) { i = true; break } } if (i) { break } c.push.apply(c, [h]) } } catch (err) { g.e(err) } finally { g.f() } } } catch (err) { d.e(err) } finally { d.f() } if (c.length > 0) { var k = []; var l = _createForOfIteratorHelper(c), _step2; try { for (l.s(); !(_step2 = l.n()).done;) { var m = _step2.value; var n = false; for (var o = 0; o < _translate.inProgressNodes.length; o++) { if (_translate.inProgressNodes[o].node.isSameNode(m)) { n = true; break } } if (n) { continue } k.push(m) } } catch (err) { l.e(err) } finally { l.f() } if (k.length < 1) { return } setTimeout(function () { console.log(k); _translate.execute(k) }, 10) } }; _translate.listener.observer = new MutationObserver(_translate.listener.callback); var p = _translate.getDocuments(); for (var q = 0; q < p.length; q++) { var r = p[q]; if (r != null) { _translate.listener.observer.observe(r, _translate.listener.config) } } }, renderTaskFinish: function renderTaskFinish(a) { } }, renderTask: function () { function renderTask() { _classCallCheck(this, renderTask); this.taskQueue = []; this.nodes = [] } return _createClass(renderTask, [{ key: "add", value: function add(a, b, c, d) { var e = _translate.element.nodeAnalyse.get(a, d); var f = _translate.util.hash(e['text']); if (typeof this.nodes[f] == 'undefined') { this.nodes[f] = new Array() } this.nodes[f].push(a); var g = this.taskQueue[f]; if (g == null || typeof g == 'undefined') { g = new Array() } var h = new Array(); if (b.substr(0, 1) == ' ') { if (c.substr(0, 1) != ' ') { c = ' ' + c } } if (b.substr(b.length - 1, 1) === ' ') { if (c.substr(0, 1) != ' ') { c = c + ' ' } } h['originalText'] = b; h['resultText'] = c; h['attribute'] = d; g.push(h); this.taskQueue[f] = g } }, { key: "execute", value: function execute() { for (var e in this.taskQueue) { var f = this.taskQueue[e]; if (typeof f == 'function') { continue } f.sort(function (a, b) { return b.originalText.length - a.originalText.length }); this.taskQueue[e] = f } for (var e in this.nodes) { var f = this.taskQueue[e]; for (var g = 0; g < this.nodes[e].length; g++) { for (var h = 0; h < f.length; h++) { var i = f[h]; if (typeof f == 'function') { continue } var j = this.nodes[e][h]; setTimeout(function (a) { for (var b = 0; b < _translate.inProgressNodes.length; b++) { if (_translate.inProgressNodes[b].node.isSameNode(a)) { _translate.inProgressNodes[b].number = _translate.inProgressNodes[b].number - 1; if (_translate.inProgressNodes[b].number < 1) { _translate.inProgressNodes.splice(b, 1) } break } } }, 50, j); _translate.element.nodeAnalyse.set(this.nodes[e][h], i.originalText, i.resultText, i['attribute']) } } } if (typeof this.taskQueue != 'undefined' && Object.keys(this.taskQueue).length > 0) { var k = this; setTimeout(function () { for (var a in k.nodes) { for (var b in k.nodes[a]) { var c = _translate.element.nodeAnalyse.get(k.nodes[a][0]); var d = nodeuuid.uuid(c.node); if (d.length == 0) { continue } _translate.nodeHistory[d] = {}; _translate.nodeHistory[d].node = c.node; _translate.nodeHistory[d].translateText = c.text } } _translate.listener.renderTaskFinish(k) }, 50) } else { } } }]) }(), execute: function execute(k) { _translate.useVersion = 'v2'; var l = _translate.util.uuid(); if (_translate.to == null || _translate.to == '') { var m = _translate.storage.get('to'); if (m != null && typeof m != 'undefined' && m.length > 0) { _translate.to = m } } if (_translate.to == null || typeof _translate.to == 'undefined' || _translate.to.length == 0) { if (_translate.autoDiscriminateLocalLanguage) { _translate.executeByLocalLanguage() } return } try { _translate.selectLanguageTag.render() } catch (e) { console.log(e) } if (_translate.to == _translate.language.getLocal()) { return } var n; if (typeof k != 'undefined') { if (k == null) { return } if (typeof k.length == 'undefined') { n = new Array(); n[0] = k } else { n = k } } else { n = _translate.getDocuments() } for (var i = 0; i < n.length & i < 20; i++) { var o = n[i]; _translate.element.whileNodes(l, o) } if (_translate.language.translateLanguagesRange.length > 0) { for (var p in _translate.nodeQueue[l].list) { if (_translate.language.translateLanguagesRange.indexOf(p) < 0) { delete _translate.nodeQueue[l].list[p] } } } for (var p in _translate.nodeQueue[l].list) { for (var q in _translate.nodeQueue[l].list[p]) { if (typeof _translate.nodeQueue[l].list[p][q] == 'function') { continue } for (var r = _translate.nodeQueue[l].list[p][q].nodes.length - 1; r > -1; r--) { var s = _translate.element.nodeAnalyse.get(_translate.nodeQueue[l].list[p][q].nodes[r].node); var t = nodeuuid.uuid(s.node); if (typeof _translate.nodeHistory[t] != 'undefined') { if (_translate.nodeHistory[t].translateText == s.text) { _translate.nodeQueue[l].list[p][q].nodes.splice(r, 1) } else { } } else { } } if (_translate.nodeQueue[l].list[p][q].nodes.length == 0) { delete _translate.nodeQueue[l].list[p][q] } } if (Object.keys(_translate.nodeQueue[l].list[p]).length == 0) { delete _translate.nodeQueue[l].list[p] } } var u = {}; var v = {}; var w = {}; var x = []; for (var p in _translate.nodeQueue[l]['list']) { if (p == null || typeof p == 'undefined' || p.length == 0 || p == 'undefined') { continue } u[p] = []; v[p] = []; var y = new _translate.renderTask(); w[p] = []; for (var q in _translate.nodeQueue[l]['list'][p]) { if (typeof _translate.nodeQueue[l]['list'][p][q] == 'function') { continue } var z = _translate.nodeQueue[l]['list'][p][q]['original']; var A = _translate.nodeQueue[l]['list'][p][q]['translateText']; var B = z == A ? q : _translate.util.hash(A); _translate.nodeQueue[l]['list'][p][q]['cacheHash'] = B; var C = _translate.storage.get('hash_' + _translate.to + '_' + B); if (C != null && C.length > 0) { for (var D = 0; D < _translate.nodeQueue[l]['list'][p][q]['nodes'].length; D++) { var E = _translate.nodeQueue[l]['list'][p][q]['nodes'][D]['node']; var F = false; for (var G = 0; G < _translate.inProgressNodes.length; G++) { if (_translate.inProgressNodes[G].node.isSameNode(E)) { _translate.inProgressNodes[G].number++; F = true } } if (!F) { _translate.inProgressNodes.push({ node: E, number: 1 }) } var H = _translate.nodeQueue[l]['list'][p][q]['nodes'][D]['beforeText'] + C + _translate.nodeQueue[l]['list'][p][q]['nodes'][D]['afterText']; y.add(_translate.nodeQueue[l]['list'][p][q]['nodes'][D]['node'], z, H, _translate.nodeQueue[l]['list'][p][q]['nodes'][D]['attribute']); var I = -1; for (var i = 0; i < w[p].length; i++) { if (_translate.nodeQueue[l]['list'][p][q]['nodes'][D]['node'].isSameNode(w[p][i]['node'])) { I = i; break } } var J = -1; for (var i = 0; i < x.length; i++) { if (_translate.nodeQueue[l]['list'][p][q]['nodes'][D]['node'].isSameNode(x[i]['node'])) { J = i; break } } if (I == -1) { I = w[p].length; w[p][I] = {}; w[p][I]['node'] = _translate.nodeQueue[l]['list'][p][q]['nodes'][D]['node']; w[p][I]['array'] = [] } if (J == -1) { J = x.length; x[J] = {}; x[J]['node'] = _translate.nodeQueue[l]['list'][p][q]['nodes'][D]['node']; x[J]['array'] = [] } var K = w[p][I]['array'].length; w[p][I]['array'][K] = H; var L = x[J]['array'].length; x[J]['array'][L] = H } continue } u[p].push(A); v[p].push(q) } y.execute() } for (var p in w) { var M = Object.keys(_translate.nodeQueue[l]['list'][p]); var N = M.length; for (var i = 0; i < w[p].length; i++) { for (var O = 0; O < x.length; O++) { if (w[p][i].node.isSameNode(x[O]['node'])) { w[p][i].array = x[O].array; break } } w[p][i].array.sort(function (a, b) { return b.length - a.length }); var P = _translate.element.nodeAnalyse.get(w[p][i].node); var Q = P.text; for (var R = 0; R < w[p][i].array.length; R++) { if (w[p][i].array[R] < 1) { continue } Q = Q.replace(new RegExp(_translate.util.regExp.pattern(w[p][i].array[R]), 'g'), _translate.util.regExp.resultText('\n')) } var S = Q.split('\n'); for (var T = 0; T < S.length; T++) { if (S[T] < 1) { continue } _translate.addNodeToQueue(l, P['node'], S[T]) } } var U = Object.keys(_translate.nodeQueue[l]['list'][p]); var V = U.length; if (N - V == 0) { continue } for (var W = 0; W < U.length; W++) { twoHash = U[W]; if (M.indexOf(twoHash) == -1) { var X = _translate.nodeQueue[l]['list'][p][twoHash]; var B = X.original == X.translateText ? twoHash : _translate.util.hash(X.translateText); _translate.nodeQueue[l]['list'][p][twoHash]['cacheHash'] = B; u[p].push(X.translateText); v[p].push(twoHash) } } } var Y = []; for (var p in _translate.nodeQueue[l]['list']) { if (typeof u[p] == 'undefined') { continue } if (u[p].length < 1) { continue } Y.push(p) } if (Y.length == 0) { return } for (var p in v) { if (typeof v[p] == 'undefined') { continue } if (v[p].length < 1) { continue } for (var Z = 0; Z < v[p].length; Z++) { var ba = v[p][Z]; for (var bb = 0; bb < _translate.nodeQueue[l]['list'][p][ba].nodes.length; bb++) { var E = _translate.nodeQueue[l]['list'][p][ba].nodes[bb].node; var F = false; for (var G = 0; G < _translate.inProgressNodes.length; G++) { if (_translate.inProgressNodes[G].node.isSameNode(E)) { _translate.inProgressNodes[G].number++; F = true } } if (!F) { _translate.inProgressNodes.push({ node: E, number: 1 }) } } } } for (var bc in Y) { var p = Y[bc]; if (typeof u[p] == 'undefined' || u[p].length < 1) { return } var bd = _translate.request.api.translate; var be = { from: p, to: _translate.to, text: encodeURIComponent(JSON.stringify(u[p])) }; _translate.request.post(bd, be, function (a) { if (a.result == 0) { console.log('=======ERROR START======='); console.log(u[a.from]); console.log('response : ' + a.info); console.log('=======ERROR END  ======='); var useEdge = _translate.storage.get('uesEdge'); if (useEdge == null || typeof useEdge == 'undefined' || useEdge.length <= 0) { _translate.storage.set('uesEdge', 1); window.location.reload() } return } var b = new _translate.renderTask(); for (var i = 0; i < v[a.from].length; i++) { var c = a.from; var d = a.text[i]; if (d == null) { continue } if (d.toLowerCase().indexOf(u[a.from][i].toLowerCase()) > -1) { d = u[a.from][i] } var f = v[a.from][i]; var g = _translate.nodeQueue[l]['list'][c][f]['cacheHash']; var h = ''; try { h = _translate.nodeQueue[l]['list'][c][f]['original'] } catch (e) { console.log('uuid:' + l + ', originalWord:' + h + ', lang:' + c + ', hash:' + f + ', text:' + d + ', queue:' + _translate.nodeQueue[l]); console.log(e); continue } for (var j = 0; j < _translate.nodeQueue[l]['list'][c][f]['nodes'].length; j++) { b.add(_translate.nodeQueue[l]['list'][c][f]['nodes'][j]['node'], h, _translate.nodeQueue[l]['list'][c][f]['nodes'][j]['beforeText'] + d + _translate.nodeQueue[l]['list'][c][f]['nodes'][j]['afterText'], _translate.nodeQueue[l]['list'][c][f]['nodes'][j]['attribute']) } _translate.storage.set('hash_' + a.to + '_' + g, d) } b.execute() }) } }, nodeHistory: {}, element: { nodeAnalyse: { get: function get(a, b) { return _translate.element.nodeAnalyse.analyse(a, '', '', b) }, set: function set(a, b, c, d) { _translate.element.nodeAnalyse.analyse(a, b, c, d) }, analyse: function analyse(a, b, c, d) { var e = new Array(); e['node'] = a; e['text'] = ''; var f = _translate.element.getNodeName(a); if (d != null && typeof d == 'string' && d.length > 0) { e['text'] = a[d]; if (typeof b != 'undefined' && b.length > 0) { if (typeof a[d] != 'undefined') { a[d] = a[d].replace(new RegExp(_translate.util.regExp.pattern(b), 'g'), _translate.util.regExp.resultText(c)) } else { console.log(a) } } return e } if (f == '#text') { if (typeof a.parentNode != 'undefined') { var g = _translate.element.getNodeName(a.parentNode); if (g == 'TEXTAREA') { f = 'TEXTAREA'; a = a.parentNode } } } if (f == 'INPUT' || f == 'TEXTAREA') { if (a.attributes == null || typeof a.attributes == 'undefined') { e['text'] = ''; return e } if (f == 'INPUT') { if (typeof a.attributes.type != 'undefined' && _typeof(a.attributes.type.nodeValue) != null && (a.attributes.type.nodeValue.toLowerCase() == 'button' || a.attributes.type.nodeValue.toLowerCase() == 'submit')) { var h = a.attributes.value; if (h != null && typeof h != 'undefined' && typeof h.nodeValue != 'undefined' && h.nodeValue.length > 0) { if (typeof b != 'undefined' && b.length > 0) { h.nodeValue = h.nodeValue.replace(new RegExp(_translate.util.regExp.pattern(b), 'g'), _translate.util.regExp.resultText(c)) } e['text'] = h.nodeValue; e['node'] = h; return e } } } if (typeof a.attributes['placeholder'] != 'undefined') { if (typeof b != 'undefined' && b.length > 0) { a.attributes['placeholder'].nodeValue = a.attributes['placeholder'].nodeValue.replace(new RegExp(_translate.util.regExp.pattern(b), 'g'), _translate.util.regExp.resultText(c)) } e['text'] = a.attributes['placeholder'].nodeValue; e['node'] = a.attributes['placeholder']; return e } e['text'] = ''; return e } if (f == 'META') { if (typeof a.name != 'undefined' && a.name != null) { var i = a.name.toLowerCase(); if (i == 'keywords' || i == 'description') { if (typeof b != 'undefined' && b != null && b.length > 0) { a.content = a.content.replace(new RegExp(_translate.util.regExp.pattern(b), 'g'), _translate.util.regExp.resultText(c)) } e['text'] = a.content; return e } } e['text'] = ''; return e } if (f == 'IMG') { if (typeof a.alt == 'undefined' || a.alt == null) { e['text'] = ''; return e } if (typeof b != 'undefined' && b.length > 0) { a.alt = a.alt.replace(new RegExp(_translate.util.regExp.pattern(b), 'g'), _translate.util.regExp.resultText(c)) } e['text'] = a.alt; return e } if (a.nodeValue == null || typeof a.nodeValue == 'undefined') { e['text'] = '' } else if (a.nodeValue.trim().length == 0) { e['text'] = '' } else { if (typeof b != 'undefined' && b != null && b.length > 0) { a.nodeValue = a.nodeValue.replace(new RegExp(_translate.util.regExp.pattern(b), 'g'), _translate.util.regExp.resultText(c)) } e['text'] = a.nodeValue } return e } }, getNodeName: function getNodeName(a) { if (a == null || typeof a == 'undefined') { return '' } if (a.nodeName == null || typeof a.nodeName == 'undefined') { return '' } var b = a.nodeName; return b }, whileNodes: function whileNodes(a, b) { if (b == null || typeof b == 'undefined') { return } if (typeof _translate.nodeQueue[a] == 'undefined' || _translate.nodeQueue[a] == null) { _translate.nodeQueue[a] = new Array(); _translate.nodeQueue[a]['expireTime'] = Date.now() + 120 * 1000; _translate.nodeQueue[a]['list'] = new Array() } if (_typeof(b) == 'object' && typeof b['title'] == 'string' && b['title'].length > 0) { if (!_translate.ignore.isIgnore(b)) { _translate.addNodeToQueue(a, b, b['title'], 'title') } } var c = b.childNodes; if (c.length > 0) { for (var i = 0; i < c.length; i++) { _translate.element.whileNodes(a, c[i]) } } else { _translate.element.findNode(a, b) } }, findNode: function findNode(a, b) { if (b == null || typeof b == 'undefined') { return } if (b.parentNode == null) { return } var c = _translate.element.getNodeName(b.parentNode); if (c == '') { return } if (_translate.ignore.tag.indexOf(c.toLowerCase()) > -1) { return } if (_translate.ignore.isIgnore(b)) { return } var d = _translate.element.nodeAnalyse.get(b); if (d['text'].length > 0) { _translate.addNodeToQueue(a, d['node'], d['text']) } } }, addNodeToQueue: function addNodeToQueue(a, b, c, d) { if (b == null || c == null || c.length == 0) { return } var e = _translate.element.getNodeName(b); if (e.toLowerCase() == '#comment') { return } var f = _translate.util.hash(c); if (_translate.util.findTag(c)) { if (b.parentNode == null) { return } var g = _translate.element.getNodeName(b.parentNode); if (g == 'SCRIPT' || g == 'STYLE') { return } } var h = _translate.language.recognition(c); langs = h.languageArray; if (typeof langs[_translate.to] != 'undefined') { delete langs[_translate.to] } var i = _translate.whole.isWhole(b); if (!i) { for (var j in langs) { for (var k = 0; k < langs[j].list.length; k++) { if (typeof langs[j].list[k] == 'undefined' || typeof langs[j].list[k]['text'] == 'undefined') { continue } var l = langs[j].list[k]['text']; var m = langs[j].list[k]['beforeText']; var n = langs[j].list[k]['afterText']; _translate.addNodeQueueItem(a, b, l, d, j, m, n) } } } else { var j = h.languageName; _translate.addNodeQueueItem(a, b, c, d, j, '', '') } }, addNodeQueueItem: function addNodeQueueItem(a, b, c, d, e, f, g) { if (_translate.nodeQueue[a]['list'][e] == null || typeof _translate.nodeQueue[a]['list'][e] == 'undefined') { _translate.nodeQueue[a]['list'][e] = new Array() } var h = _translate.util.hash(c); if (_translate.nodeQueue[a]['list'][e][h] == null || typeof _translate.nodeQueue[a]['list'][e][h] == 'undefined') { _translate.nodeQueue[a]['list'][e][h] = new Array(); _translate.nodeQueue[a]['list'][e][h]['nodes'] = new Array(); _translate.nodeQueue[a]['list'][e][h]['original'] = c; _translate.nodeQueue[a]['list'][e][h]['translateText'] = _translate.nomenclature.dispose(c) } var i = false; if (typeof b.isSameNode != 'undefined') { for (var j = 0; j < _translate.nodeQueue[a]['list'][e][h]['nodes'].length; j++) { if (b.isSameNode(_translate.nodeQueue[a]['list'][e][h]['nodes'][j]['node'])) { i = true; continue } } } if (i) { return } var k = _translate.nodeQueue[a]['list'][e][h]['nodes'].length; _translate.nodeQueue[a]['list'][e][h]['nodes'][k] = new Array(); _translate.nodeQueue[a]['list'][e][h]['nodes'][k]['node'] = b; _translate.nodeQueue[a]['list'][e][h]['nodes'][k]['attribute'] = d; _translate.nodeQueue[a]['list'][e][h]['nodes'][k]['beforeText'] = f; _translate.nodeQueue[a]['list'][e][h]['nodes'][k]['afterText'] = g }, whole: { class: [], tag: [], id: [], executeTip: function executeTip() { if (_translate.whole.class.length == 0 && _translate.whole.tag.length == 0 && _translate.whole.id.length == 0) { } else { console.log('您开启了 translate.whole 此次行为避开了浏览器端的文本语种自动识别，而是暴力的直接对某个元素的整个文本进行翻译，很可能会产生非常大的翻译量，请谨慎！有关每日翻译字符的说明，可参考： http://translate.zvo.cn/42557.html ') } if (_translate.whole.tag.indexOf('html') > -1) { console.log('自检发现您设置了 translate.whole.tag 其中有 html ，这个是不生效的，最大只允许设置到 body ') } }, isWhole: function isWhole(a) { if (_translate.whole.class.length == 0 && _translate.whole.tag.length == 0 && _translate.whole.id.length == 0) { return false } if (a == null || typeof a == 'undefined') { return false } var b = a; var c = 100; while (c-- > 0) { if (b == null || typeof b == 'undefined') { return false } var d = _translate.element.getNodeName(b).toLowerCase(); if (d.length > 0) { if (d == 'html' || d == '#document') { return false } if (_translate.whole.tag.indexOf(d) > -1) { return true } } if (b.className != null) { var e = b.className; if (e == null || typeof e != 'string') { continue } e = e.trim().split(' '); for (var f = 0; f < e.length; f++) { if (e[f] != null && e[f].trim().length > 0) { if (_translate.whole.class.indexOf(e[f]) > -1) { return true } } } } if (b.id != null && typeof b.id != 'undefined') { if (_translate.whole.id.indexOf(b.id) > -1) { return true } } b = b.parentNode } return false } }, language: { local: '', translateLanguagesRange: [], setLocal: function setLocal(a) { _translate.useVersion = 'v2'; _translate.language.local = a }, getLocal: function getLocal() { if (_translate.language.local == null || _translate.language.local.length < 1) { _translate.language.autoRecognitionLocalLanguage() } return _translate.language.local }, getCurrent: function getCurrent() { var a = _translate.storage.get('to'); if (a != null && typeof a != 'undefined' && a.length > 0) { return a } return _translate.language.getLocal() }, setDefaultTo: function setDefaultTo(a) { var b = _translate.storage.get('to'); if (b != null && typeof b != 'undefined' && b.length > 0) { } else { _translate.storage.set('to', a); _translate.to = a } }, clearCacheLanguage: function clearCacheLanguage() { _translate.to = ''; _translate.storage.set('to', '') }, setUrlParamControl: function setUrlParamControl(a) { if (typeof a == 'undefined' || a.length < 1) { a = 'language' } var b = _translate.util.getUrlParam(a); if (typeof b == 'undefined') { return } if (b == '' || b == 'null' || b == 'undefined') { return } _translate.storage.set('to', b); _translate.to = b }, autoRecognitionLocalLanguage: function autoRecognitionLocalLanguage() { if (_translate.language.local != null && _translate.language.local.length > 2) { return } var a = document.body.outerText; if (a == null || typeof a == 'undefined' || a.length < 1) { _translate.language.local = 'chinese_simplified'; return } a = a.replace(/\n|\t|\r/g, ''); _translate.language.local = 'chinese_simplified'; var b = _translate.language.recognition(a); _translate.language.local = b.languageName; return _translate.language.local }, get: function get(a) { var b = new Array(); var c = new Array(); var d = []; var e = []; for (var i = 0; i < a.length; i++) { var f = a.charAt(i); var g = _translate.language.getCharLanguage(f); if (g == '') { g = 'unidentification' } var h = _translate.language.analyse(g, c, d, e, f); c = h['langStrs']; if (typeof d['language'] != 'undefined') { e['language'] = d['language']; e['charstr'] = d['charstr']; e['storage_language'] = d['storage_language'] } d['language'] = h['storage_language']; d['charstr'] = f; d['storage_language'] = h['storage_language']; b.push(g) } if (typeof c['unidentification'] != 'undefined') { delete c['unidentification'] } if (typeof c['specialCharacter'] != 'undefined') { delete c['specialCharacter'] } if (typeof c['number'] != 'undefined') { delete c['number'] } return c }, recognition: function recognition(a) { var b = _translate.language.get(a); var c = []; var d = []; var e = 0; for (var f in b) { if (_typeof(b[f]) != 'object') { continue } var g = 0; for (var h = 0; h < b[f].length; h++) { g = g + b[f][h].text.length } e = e + g; c[f] = g; d[f] = g } var i = []; for (var j in c) { if (c[j] / e > 0.05) { i[i.length] = j + '' } } if (i.length > 1 && i.indexOf('english') > -1) { c['english'] = 0 } if (i.indexOf('chinese_simplified') > -1 && i.indexOf('chinese_traditional') > -1) { c['chinese_simplified'] = 0 } var k = ''; var l = 0; for (var j in c) { if (c[j] > l) { k = j; l = c[j] } } var m = {}; for (var j in b) { m[j] = {}; m[j].number = d[j]; m[j].list = b[j] } var n = { languageName: k, languageArray: m }; return n }, getCharLanguage: function getCharLanguage(a) { if (a == null || typeof a == 'undefined') { return '' } if (this.italian(a)) { return 'italian' } if (this.english(a)) { return 'english' } if (this.specialCharacter(a)) { return 'specialCharacter' } if (this.number(a)) { return 'number' } var b = this.chinese(a); if (b == 'simplified') { return 'chinese_simplified' } else if (b == 'traditional') { return 'chinese_traditional' } if (this.japanese(a)) { return 'japanese' } if (this.korean(a)) { return 'korean' } return '' }, analyse: function analyse(a, b, c, d, e) { if (typeof b[a] == 'undefined') { b[a] = new Array() } var f = 0; if (typeof c['storage_language'] == 'undefined') { } else { if (_translate.language.connector(e)) { a = c['storage_language'] } if (c['storage_language'] == a) { f = b[a].length - 1 } else { f = b[a].length } } if (typeof b[a][f] == 'undefined') { b[a][f] = new Array(); b[a][f]['beforeText'] = ''; b[a][f]['afterText'] = ''; b[a][f]['text'] = '' } b[a][f]['text'] = b[a][f]['text'] + e; if (_translate.language.wordBlankConnector(_translate.language.getLocal()) == false && _translate.language.wordBlankConnector(_translate.to)) { if (c['storage_language'] != null && typeof c['storage_language'] != 'undefined' && c['storage_language'].length > 0) { if (c['storage_language'] != 'specialCharacter') { if (_translate.language.wordBlankConnector(c['storage_language']) == false && _translate.language.wordBlankConnector(a)) { b[c['storage_language']][b[c['storage_language']].length - 1]['afterText'] = ' ' } else if (c['storage_language'] == 'english' && a != 'english') { b[a][f]['beforeText'] = ' ' } } } } var g = new Array(); g['langStrs'] = b; g['storage_language'] = a; return g }, connector: function connector(a) { if (/.*[\u0020\u00A0\u202F\u205F\u3000]+.*$/.test(a)) { return true } if (/.*[\u0030-\u0039]+.*$/.test(a)) { return true } if (/.*[\u0021\u0022\u0023\u0024\u0025\u0026\u0027\u002C\u002D\u002E\u003A\u003B\u003F\u0040]+.*$/.test(a)) { return true } if (/.*[\u3002\uFF1F\uFF01\uFF0C\u3001\uFF1B\uFF1A\u300C\u300D\u300E\u300F\u2018\u2019\u201C\u201D\uFF08\uFF09\u3014\u3015\u3010\u3011\u2014\u2026\u2013\uFF0E\u300A\u300B\u3008\u3009\u00b7]+.*$/.test(a)) { return true } return false }, wordBlankConnector: function wordBlankConnector(a) { if (a == null || typeof a == 'undefined') { return true } switch (a.trim().toLowerCase()) { case 'chinese_simplified': return false; case 'chinese_traditional': return false; case 'korean': return false; case 'japanese': return false }return true }, chinese_traditional_dict: '皚藹礙愛翺襖奧壩罷擺敗頒辦絆幫綁鎊謗剝飽寶報鮑輩貝鋇狽備憊繃筆畢斃閉邊編貶變辯辮鼈癟瀕濱賓擯餅撥缽鉑駁蔔補參蠶殘慚慘燦蒼艙倉滄廁側冊測層詫攙摻蟬饞讒纏鏟産闡顫場嘗長償腸廠暢鈔車徹塵陳襯撐稱懲誠騁癡遲馳恥齒熾沖蟲寵疇躊籌綢醜櫥廚鋤雛礎儲觸處傳瘡闖創錘純綽辭詞賜聰蔥囪從叢湊竄錯達帶貸擔單鄲撣膽憚誕彈當擋黨蕩檔搗島禱導盜燈鄧敵滌遞締點墊電澱釣調叠諜疊釘頂錠訂東動棟凍鬥犢獨讀賭鍍鍛斷緞兌隊對噸頓鈍奪鵝額訛惡餓兒爾餌貳發罰閥琺礬釩煩範販飯訪紡飛廢費紛墳奮憤糞豐楓鋒風瘋馮縫諷鳳膚輻撫輔賦複負訃婦縛該鈣蓋幹趕稈贛岡剛鋼綱崗臯鎬擱鴿閣鉻個給龔宮鞏貢鈎溝構購夠蠱顧剮關觀館慣貫廣規矽歸龜閨軌詭櫃貴劊輥滾鍋國過駭韓漢閡鶴賀橫轟鴻紅後壺護滬戶嘩華畫劃話懷壞歡環還緩換喚瘓煥渙黃謊揮輝毀賄穢會燴彙諱誨繪葷渾夥獲貨禍擊機積饑譏雞績緝極輯級擠幾薊劑濟計記際繼紀夾莢頰賈鉀價駕殲監堅箋間艱緘繭檢堿鹼揀撿簡儉減薦檻鑒踐賤見鍵艦劍餞漸濺澗漿蔣槳獎講醬膠澆驕嬌攪鉸矯僥腳餃繳絞轎較稭階節莖驚經頸靜鏡徑痙競淨糾廄舊駒舉據鋸懼劇鵑絹傑潔結誡屆緊錦僅謹進晉燼盡勁荊覺決訣絕鈞軍駿開凱顆殼課墾懇摳庫褲誇塊儈寬礦曠況虧巋窺饋潰擴闊蠟臘萊來賴藍欄攔籃闌蘭瀾讕攬覽懶纜爛濫撈勞澇樂鐳壘類淚籬離裏鯉禮麗厲勵礫曆瀝隸倆聯蓮連鐮憐漣簾斂臉鏈戀煉練糧涼兩輛諒療遼鐐獵臨鄰鱗凜賃齡鈴淩靈嶺領餾劉龍聾嚨籠壟攏隴樓婁摟簍蘆盧顱廬爐擄鹵虜魯賂祿錄陸驢呂鋁侶屢縷慮濾綠巒攣孿灤亂掄輪倫侖淪綸論蘿羅邏鑼籮騾駱絡媽瑪碼螞馬罵嗎買麥賣邁脈瞞饅蠻滿謾貓錨鉚貿麽黴沒鎂門悶們錳夢謎彌覓綿緬廟滅憫閩鳴銘謬謀畝鈉納難撓腦惱鬧餒膩攆撚釀鳥聶齧鑷鎳檸獰甯擰濘鈕紐膿濃農瘧諾歐鷗毆嘔漚盤龐國愛賠噴鵬騙飄頻貧蘋憑評潑頗撲鋪樸譜臍齊騎豈啓氣棄訖牽扡釺鉛遷簽謙錢鉗潛淺譴塹槍嗆牆薔強搶鍬橋喬僑翹竅竊欽親輕氫傾頃請慶瓊窮趨區軀驅齲顴權勸卻鵲讓饒擾繞熱韌認紉榮絨軟銳閏潤灑薩鰓賽傘喪騷掃澀殺紗篩曬閃陝贍繕傷賞燒紹賒攝懾設紳審嬸腎滲聲繩勝聖師獅濕詩屍時蝕實識駛勢釋飾視試壽獸樞輸書贖屬術樹豎數帥雙誰稅順說碩爍絲飼聳慫頌訟誦擻蘇訴肅雖綏歲孫損筍縮瑣鎖獺撻擡攤貪癱灘壇譚談歎湯燙濤縧騰謄銻題體屜條貼鐵廳聽烴銅統頭圖塗團頹蛻脫鴕馱駝橢窪襪彎灣頑萬網韋違圍爲濰維葦偉僞緯謂衛溫聞紋穩問甕撾蝸渦窩嗚鎢烏誣無蕪吳塢霧務誤錫犧襲習銑戲細蝦轄峽俠狹廈鍁鮮纖鹹賢銜閑顯險現獻縣餡羨憲線廂鑲鄉詳響項蕭銷曉嘯蠍協挾攜脅諧寫瀉謝鋅釁興洶鏽繡虛噓須許緒續軒懸選癬絢學勳詢尋馴訓訊遜壓鴉鴨啞亞訝閹煙鹽嚴顔閻豔厭硯彥諺驗鴦楊揚瘍陽癢養樣瑤搖堯遙窯謠藥爺頁業葉醫銥頤遺儀彜蟻藝億憶義詣議誼譯異繹蔭陰銀飲櫻嬰鷹應纓瑩螢營熒蠅穎喲擁傭癰踴詠湧優憂郵鈾猶遊誘輿魚漁娛與嶼語籲禦獄譽預馭鴛淵轅園員圓緣遠願約躍鑰嶽粵悅閱雲鄖勻隕運蘊醞暈韻雜災載攢暫贊贓髒鑿棗竈責擇則澤賊贈紮劄軋鍘閘詐齋債氈盞斬輾嶄棧戰綻張漲帳賬脹趙蟄轍鍺這貞針偵診鎮陣掙睜猙幀鄭證織職執紙摯擲幟質鍾終種腫衆謅軸皺晝驟豬諸誅燭矚囑貯鑄築駐專磚轉賺樁莊裝妝壯狀錐贅墜綴諄濁茲資漬蹤綜總縱鄒詛組鑽緻鐘麼為隻兇準啟闆裡靂餘鍊洩', chinese: function chinese(a) { if (/.*[\u4e00-\u9fa5]+.*$/.test(a)) { if (this.chinese_traditional_dict.indexOf(a) > -1) { return 'traditional' } else { return 'simplified' } } else { return '' } }, english: function english(a) { if (/.*[\u0041-\u005a]+.*$/.test(a)) { return true } else if (/.*[\u0061-\u007a]+.*$/.test(a)) { return true } else { return false } }, japanese: function japanese(a) { if (/.*[\u3040-\u309F\u30A0-\u30FF]+.*$/.test(a)) { return true } else { return false } }, korean: function korean(a) { if (/.*[\uAC00-\uD7AF]+.*$/.test(a)) { return true } else { return false } }, number: function number(a) { if (/.*[\u0030-\u0039]+.*$/.test(a)) { return true } return false }, italian: function italian(a) { if (/.*[\u00E0-\u00F6]+.*$/.test(a)) { return true } return false }, specialCharacter: function specialCharacter(a) { if (/.*[\u2460-\u24E9]+.*$/.test(a)) { return true } if (/.*[\u2500-\u25FF]+.*$/.test(a)) { return true } if (/.*[\u3200-\u33FF]+.*$/.test(a)) { return true } if (/.*[\uFF00-\uFF5E]+.*$/.test(a)) { return true } if (/.*[\u2000-\u22FF]+.*$/.test(a)) { return true } if (/.*[\u3001-\u3036]+.*$/.test(a)) { return true } if (/.*[\u0020-\u002F]+.*$/.test(a)) { return true } if (/.*[\u003A-\u007E]+.*$/.test(a)) { return true } if (/.*[\u0009\u000a\u0020\u00A0\u1680\u180E\u202F\u205F\u3000\uFEFF]+.*$/.test(a)) { return true } if (/.*[\u2000-\u200B]+.*$/.test(a)) { return true } if (/.*[\u00A1-\u0105]+.*$/.test(a)) { return true } if (/.*[\u2C60-\u2C77]+.*$/.test(a)) { return true } return false } }, executeByLocalLanguage: function executeByLocalLanguage() { if (_translate.isDiscriminateLanguage) { return } _translate.isDiscriminateLanguage = true; _translate.request.post(_translate.request.api.ip, {}, function (a) { _translate.isDiscriminateLanguage = false; if (a.result == 0) { console.log('==== ERROR 获取当前用户所在区域异常 ===='); console.log(a.info); console.log('==== ERROR END ====') } else { _translate.storage.set('to', a.language); _translate.to = a.language; _translate.selectLanguageTag; console.log("executeByLocalLanguage"); _translate.execute() } }) }, util: { uuid: function a() { var d = new Date().getTime(); if (window.performance && typeof window.performance.now === "function") { d += performance.now() } var a = 'xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx'.replace(/[xy]/g, function (c) { var r = (d + Math.random() * 16) % 16 | 0; d = Math.floor(d / 16); return (c == 'x' ? r : r & 0x3 | 0x8).toString(16) }); return a }, findTag: function findTag(a) { var b = /<[^>]+>/g; return b.test(a) }, arrayFindMaxNumber: function arrayFindMaxNumber(a) { var b = {}; var c = []; var d = 0; for (var i = 0, len = a.length; i < len; i++) { if (!b[a[i]]) { b[a[i]] = 1 } else { b[a[i]]++ } if (b[a[i]] > d) { d = b[a[i]] } } for (var e in b) { if (b[e] === d) { c.push(e) } } return c }, hash: function b(a) { if (a == null || typeof a == 'undefined') { return a } var b = 0, i, chr; if (a.length === 0) { return b } for (i = 0; i < a.length; i++) { chr = a.charCodeAt(i); b = (b << 5) - b + chr; b |= 0 } return b + '' }, charReplace: function charReplace(a) { if (a == null) { return '' } a = a.trim(); a = a.replace(/\t|\n|\v|\r|\f/g, ''); return a }, regExp: { pattern: function pattern(a) { a = a.replace(/\\/g, '\\\\'); a = a.replace(/\"/g, '\\\"'); a = a.replace(/\?/g, '\\\?'); a = a.replace(/\$/g, '\\\$'); a = a.replace(/\(/g, '\\\('); a = a.replace(/\)/g, '\\\)'); a = a.replace(/\|/g, '\\\|'); a = a.replace(/\+/g, '\\\+'); a = a.replace(/\*/g, '\\\*'); a = a.replace(/\[/g, '\\\['); a = a.replace(/\]/g, '\\\]'); a = a.replace(/\^/g, '\\\^'); a = a.replace(/\{/g, '\\\{'); a = a.replace(/\}/g, '\\\}'); return a }, resultText: function resultText(a) { return a } }, getUrlParam: function getUrlParam(a) { var b = new RegExp("(^|&)" + a + "=([^&]*)(&|$)"); var r = window.location.search.substr(1).match(b); if (r != null) return unescape(r[2]); return "" }, synchronizesLoadJs: function synchronizesLoadJs(a) { var b = null; if (window.ActiveXObject) { try { b = new ActiveXObject("Msxml2.XMLHTTP") } catch (e) { b = new ActiveXObject("Microsoft.XMLHTTP") } } else if (window.XMLHttpRequest) { b = new XMLHttpRequest() } b.open("GET", a, false); b.send(null); if (b.readyState == 4) { if (b.status >= 200 && b.status < 300 || b.status == 0 || b.status == 304) { var c = document.getElementsByTagName("HTML")[0]; var d = document.createElement("script"); d.language = "javascript"; d.type = "text/javascript"; try { d.appendChild(document.createTextNode(b.responseText)) } catch (ex) { d.text = b.responseText } c.appendChild(d); return true } else { return false } } else { return false } }, loadMsgJs: function loadMsgJs() { if (typeof msg != 'undefined') { return } _translate.util.synchronizesLoadJs('https://res.zvo.cn/msg/msg.js') }, objSort: function objSort(c) { var d = Array.from(Object.keys(c)); d.sort(function (a, b) { return b.length - a.length }); var e = new Array(); for (var f = 0, _keys = d; f < _keys.length; f++) { var g = _keys[f]; e[g] = c[g] } return e }, versionStringToInt: function versionStringToInt(a) { var b = a.split('\.'); var c = 0; c = parseInt(b[0]) * 1000 * 1000 + c; c = parseInt(b[1]) * 1000 + c; c = parseInt(b[2]) + c; return c }, split: function split(a, b) { var c = []; if (JSON.stringify(a).length <= b) { c.push(a) } else { var d = JSON.stringify(a).trim().substring(1, JSON.stringify(a).length - 1); if (JSON.stringify(a).length - b <= 2) { b = b - 4; var f = d.substring(0, d.lastIndexOf("\",\"") + 1); var g = d.substring(d.lastIndexOf("\",\"") + 2); c.push(JSON.parse("[" + f + "]")); c.push(JSON.parse("[" + g + "]")) } else { b = b - 2; var h = 0; while (h - d.length < 0) { var s = ""; if (h + b - d.length >= 0) { s = d.substring(h) } else { s = d.substring(h, h + b) } var i = s.length; var j = 1; if (s.endsWith("\"")) { if (s.endsWith("\",\"")) { i -= 2 } else if (!s.startsWith("\"")) { var k = s.lastIndexOf("\",\""); i = k + 1 } } else if (s.endsWith("\",")) { i -= 1 } else { var k = s.lastIndexOf("\",\""); i = k + 1; if (i <= 0) { if (s.startsWith("\"")) { i = s.length() - 1 } else { i = s.length() - 2 } if (!s.endsWith("\"")) { j = 0 } } } var l = ""; if (i - s.length > 0 || i - 0 == 0) { l = s; i = i + l.length } else { l = s.substring(0, i) } if (!l.startsWith("\"") && !l.startsWith(",\"")) { l = "\"" + l } if (!l.endsWith("\"")) { l = l + "\"" } h += i + j; l = "[" + l + "]"; try { c.push(JSON.parse(l)) } catch (e) { h = h - (i + j) + 1 } } } } return c } }, service: { name: 'translate.service', use: function use(a) { if (typeof a == 'string' && a == 'client.edge') { _translate.service.name = a; _translate.whole.tag.push('body'); _translate.whole.tag.push('head'); _translate.whole.tag.push('html') } }, edge: { api: { auth: 'https://edge.microsoft.com/translate/auth', translate: 'https://api.cognitive.microsofttranslator.com/translate?from={from}&to={to}&api-version=3.0&includeSentenceLength=true' }, language: { json: [{ "id": "chinese_simplified", "name": "简体中文", "serviceId": "zh-Hans" }, { "id": "chinese_traditional", "name": "繁體中文", "serviceId": "zh-Hant" }, { "id": "english", "name": "English", "serviceId": "en" }, { "id": "japanese", "name": "日本語", "serviceId": "ja" }, { "id": "korean", "name": "한국어", "serviceId": "ko" }, { "id": "french", "name": "Français", "serviceId": "fr" }, { "id": "deutsch", "name": "Deutsch", "serviceId": "de" }, { "id": "russian", "name": "Русский", "serviceId": "ru" }, { "id": "hindi", "name": "हिन्दी", "serviceId": "hi" }, { "id": "spanish", "name": "Español", "serviceId": "es" }, { "id": "portuguese", "name": "Português", "serviceId": "pt" }, { "id": "arabic", "name": "العربية", "serviceId": "ar" }, { "id": "italian", "name": "Italiano", "serviceId": "it" }, { "id": "dutch", "name": "Nederlands", "serviceId": "nl" }, { "id": "turkish", "name": "Türkçe", "serviceId": "tr" }, { "id": "indonesian", "name": "Bahasa Indonesia", "serviceId": "id" }, { "id": "vietnamese", "name": "Tiếng Việt", "serviceId": "vi" }, { "id": "bengali", "name": "বাংলা", "serviceId": "bn" }, { "id": "urdu", "name": "اردو", "serviceId": "ur" }, { "id": "persian", "name": "فارسی", "serviceId": "fa" }, { "id": "romanian", "name": "Română", "serviceId": "ro" }, { "id": "swedish", "name": "Svenska", "serviceId": "sv" }, { "id": "polish", "name": "Polski", "serviceId": "pl" }, { "id": "ukrainian", "name": "Українська", "serviceId": "uk" }, { "id": "thai", "name": "ไทย", "serviceId": "th" }, { "id": "tamil", "name": "தமிழ்", "serviceId": "ta" }, { "id": "marathi", "name": "मराठी", "serviceId": "mr" }, { "id": "gujarati", "name": "ગુજરાતી", "serviceId": "gu" }, { "id": "telugu", "name": "తెలుగు", "serviceId": "te" }, { "id": "swahili", "name": "Kiswahili", "serviceId": "sw" }, { "id": "malay", "name": "Bahasa Melayu", "serviceId": "ms" }, { "id": "filipino", "name": "Filipino", "serviceId": "fil" }, { "id": "nepali", "name": "नेपाली", "serviceId": "ne" }, { "id": "burmese", "name": "မြန်မာ", "serviceId": "my" }, { "id": "czech", "name": "Čeština", "serviceId": "cs" }, { "id": "hungarian", "name": "Magyar", "serviceId": "hu" }, { "id": "malayalam", "name": "മലയാളം", "serviceId": "ml" }, { "id": "kannada", "name": "ಕನ್ನಡ", "serviceId": "kn" }, { "id": "punjabi", "name": "ਪੰਜਾਬੀ", "serviceId": "pa" }, { "id": "oriya", "name": "ଓଡ଼ିଆ", "serviceId": "or" }, { "id": "bulgarian", "name": "Български", "serviceId": "bg" }, { "id": "slovak", "name": "Slovenčina", "serviceId": "sk" }, { "id": "finnish", "name": "Suomi", "serviceId": "fi" }, { "id": "danish", "name": "Dansk", "serviceId": "da" }, { "id": "greek", "name": "Ελληνικά", "serviceId": "el" }, { "id": "norwegian", "name": "Norsk", "serviceId": "no" }, { "id": "afrikaans", "name": "Afrikaans", "serviceId": "af" }, { "id": "hebrew", "name": "עברית", "serviceId": "he" }, { "id": "estonian", "name": "Eesti keel", "serviceId": "et" }, { "id": "catalan", "name": "Català", "serviceId": "ca" }, { "id": "croatian", "name": "Hrvatski", "serviceId": "hr" }, { "id": "bosnian", "name": "Bosanski", "serviceId": "bs-Latn" }, { "id": "malagasy", "name": "Malagasy", "serviceId": "mg" }, { "id": "kurdish", "name": "Kurdî", "serviceId": "ku" }, { "id": "armenian", "name": "Հայերեն", "serviceId": "hy" }, { "id": "slovene", "name": "Slovenščina", "serviceId": "sl" }, { "id": "irish", "name": "Gaeilge", "serviceId": "ga" }, { "id": "inuktitut", "name": "ᐃᓄᒃᑎᑐᑦ", "serviceId": "iu" }, { "id": "pashto", "name": "پښتو", "serviceId": "ps" }, { "id": "khmer", "name": "ខ្មែរ", "serviceId": "km" }, { "id": "welsh", "name": "Cymraeg", "serviceId": "cy" }, { "id": "lithuanian", "name": "Lietuvių kalba", "serviceId": "lt" }, { "id": "albanian", "name": "Shqip", "serviceId": "sq" }, { "id": "azerbaijani", "name": "Azərbaycan dili", "serviceId": "az" }, { "id": "samoan", "name": "Gagana Samoa", "serviceId": "sm" }, { "id": "tongan", "name": "Lea faka-Tonga", "serviceId": "to" }, { "id": "lao", "name": "ລາວ", "serviceId": "lo" }, { "id": "haitian_creole", "name": "Kreyòl ayisyen", "serviceId": "ht" }, { "id": "latvian", "name": "Latviešu valoda", "serviceId": "lv" }, { "id": "amharic", "name": "አማርኛ", "serviceId": "am" }, { "id": "maori", "name": "Te Reo Māori", "serviceId": "mi" }, { "id": "icelandic", "name": "Íslenska", "serviceId": "is" }, { "id": "assamese", "name": "অসমীয়া", "serviceId": "as" }], getMap: function getMap() { if (typeof _translate.service.edge.language.map == 'undefined') { _translate.service.edge.language.map = new Array(); for (var i = 0; i < _translate.service.edge.language.json.length; i++) { var a = _translate.service.edge.language.json[i]; _translate.service.edge.language.map[a.id] = a.serviceId } } return _translate.service.edge.language.map } }, translate: function translate(p, q, r) { var s = JSON.parse(decodeURIComponent(q.text)); var u = _translate.util.split(s, 48000); if (q.from == q.to) { return } _translate.request.send(_translate.service.edge.api.auth, {}, function (j) { var k = _translate.service.edge.language.getMap()[q.from]; var l = _translate.service.edge.language.getMap()[q.to]; var m = _translate.service.edge.api.translate.replace('{from}', k).replace('{to}', l); for (var n = 0; n < u.length; n++) { var o = []; for (var i = 0; i < u[n].length; i++) { o.push({ "Text": u[n][i] }) } _translate.request.send(m, JSON.stringify(o), function (a) { var d = {}; d.info = 'SUCCESS'; d.result = 1; d.from = q.from; d.to = q.to; d.text = []; for (var t = 0; t < a.length; t++) { d.text.push(a[t].translations[0].text) } if (u.length > 1) { var b = -1; for (var c = 0; c < u.length; c++) { if (u[c].length - d.text.length == 0) { b = c; break } } if (b < 0) { console.log('------ERROR--------'); console.log('翻译内容过多，进行拆分，但拆分判断出现异常，currentIndex：-1 请联系 http://translate.zvo.cn/43006.html 说明') } for (var e = 0; e < b; e++) { var f = u[e].length; for (var g = 0; g < f; g++) { d.text.unshift(null) } } for (var h = u.length - 1; h > b; h--) { var i = u[h].length; for (var g = 0; g < i; g++) { d.text.push(null) } } } r(d) }, 'post', true, { 'Authorization': 'Bearer ' + j, 'Content-Type': 'application/json' }, function (a) { console.log('---------error--------'); console.log('edge translate service error, http code : ' + a.status + ', response text : ' + a.responseText) }, true) } }, 'get', true, { 'content-type': 'application/x-www-form-urlencoded' }, function (a) { console.log('---------error--------'); console.log('edge translate service error, http code : ' + a.status + ', response text : ' + a.responseText) }, true) } } }, request: { api: { host: ['https://beijing.enterprise.api.translate.zvo.cn/', 'https://api.translate.zvo.cn/', 'https://deutsch.enterprise.api.translate.zvo.cn/', 'https://america.api.translate.zvo.cn/'], language: 'language.json', translate: 'translate.json', ip: 'ip.json', init: 'init.json' }, speedDetectionControl: { hostMasterNodeCutTime: 2000, hostQueue: [], hostQueueIndex: -1, disableTime: 1000000, getHostQueue: function getHostQueue() { if (_translate.request.speedDetectionControl.hostQueue.length == 0) { var a = _translate.storage.get('speedDetectionControl_hostQueue'); if (a == null || typeof a == 'undefined') { if (typeof _translate.request.api.host == 'string') { _translate.request.api.host = ['' + _translate.request.api.host] } _translate.request.speedDetectionControl.hostQueue = []; for (var i = 0; i < _translate.request.api.host.length; i++) { var h = _translate.request.api.host[i]; _translate.request.speedDetectionControl.hostQueue[i] = { "host": h, time: 0 } } } else { _translate.request.speedDetectionControl.hostQueue = JSON.parse(a) } } return _translate.request.speedDetectionControl.hostQueue }, getHostQueueIndex: function getHostQueueIndex() { if (_translate.request.speedDetectionControl.hostQueueIndex < 0) { var a = _translate.storage.get('speedDetectionControl_hostQueueIndex'); if (typeof a == 'undefined' || a == null) { _translate.request.speedDetectionControl.hostQueueIndex = 0; _translate.storage.set('speedDetectionControl_hostQueueIndex', 0) } else { _translate.request.speedDetectionControl.hostQueueIndex = a } } return _translate.request.speedDetectionControl.hostQueueIndex }, getHost: function getHost() { var a = _translate.request.speedDetectionControl.getHostQueue(); var b = _translate.request.speedDetectionControl.getHostQueueIndex(); if (a.length > b) { } else { console.log('异常，下标越界了！index：' + b); b = a.length - 1 } return a[b].host } }, getUrl: function getUrl(a) { var b = _translate.request.speedDetectionControl.getHost(); var c = b + a + '?v=' + _translate.version; return c }, post: function post(a, b, c) { var e = { 'content-type': 'application/x-www-form-urlencoded' }; if (typeof b == 'undefined') { return } if (_translate.service.name == 'client.edge') { if (a == _translate.request.api.translate) { _translate.service.edge.translate(a, b, c); return } if (a == _translate.request.api.language) { var d = {}; d.info = 'SUCCESS'; d.result = 1; d.list = _translate.service.edge.language.json; c(d); return } } this.send(a, b, c, 'post', true, e, null, true) }, send: function send(b, c, d, f, g, h, i, j) { var k = ''; if (c != null) { if (typeof c == 'string') { k = c } else { for (var l in c) { if (k.length > 0) { k = k + '&' } k = k + l + '=' + c[l] } } } if (b.indexOf('https://') == 0 || b.indexOf('http://') == 0) { } else { b = _translate.request.getUrl(b) } var m = null; try { m = new XMLHttpRequest() } catch (e) { m = new ActiveXObject("Microsoft.XMLHTTP") } m.open(f, b, g); if (h != null) { for (var l in h) { m.setRequestHeader(l, h[l]) } } if (_translate.service.name == 'translate.service') { m.setRequestHeader('currentpage', window.location.href + '') } m.send(k); m.onreadystatechange = function () { if (m.readyState == 4) { if (m.status == 200) { var a = null; if (typeof m.responseText == 'undefined' || m.responseText == null) { } else { if (m.responseText.indexOf('{') > -1 && m.responseText.indexOf('}') > -1) { try { a = JSON.parse(m.responseText) } catch (e) { console.log(e) } } } if (a == null) { d(m.responseText) } else { d(a) } } else { if (j) { console.log('------- translate.js service api response error --------'); console.log('    http code : ' + m.status); console.log('    response : ' + m.response); console.log('    request url : ' + b); console.log('    request data : ' + JSON.stringify(c)); console.log('    request method : ' + f); console.log('---------------------- end ----------------------') } m.requestURL = b; if (i != null) { i(m) } } } }; return m }, translateText: function translateText(b, c) { if (typeof b == 'string') { b = [b] } var d = _translate.request.api.translate; var e = { from: _translate.language.getLocal(), to: _translate.language.getCurrent(), text: encodeURIComponent(JSON.stringify(b)) }; _translate.request.post(d, e, function (a) { if (a.result == 0) { console.log('=======ERROR START======='); console.log('from : ' + a.from); console.log('to : ' + a.to); console.log('translate text array : ' + b); console.log('response : ' + a.info); console.log('=======ERROR END  =======') } c(a) }) }, listener: { minIntervalTime: 800, lasttime: 0, executetime: 0, delayExecuteTime: 200, addExecute: function addExecute() { var a = Date.now(); if (_translate.request.listener.lasttime == 0) { _translate.request.listener.executetime = a; _translate.request.listener.lasttime = 1 } else { if (_translate.request.listener.executetime > 1) { } else { if (a < _translate.request.listener.lasttime + _translate.request.listener.minIntervalTime) { _translate.request.listener.executetime = _translate.request.listener.lasttime + _translate.request.listener.minIntervalTime } else { _translate.request.listener.executetime = a } } } }, trigger: function trigger(a) { return true }, start: function start() { if (typeof _translate.request.listener.isStart != 'undefined') { return } else { _translate.request.listener.isStart = true } setInterval(function () { var a = Date.now(); if (_translate.request.listener.executetime > 1 && a > _translate.request.listener.executetime + _translate.request.listener.delayExecuteTime) { _translate.request.listener.executetime = 0; _translate.request.listener.lasttime = a; try { _translate.execute() } catch (e) { console.log(e) } } }, 100); var g = new PerformanceObserver(function (a) { var b = false; for (var e = 0; e < a.getEntries().length; e++) { var c = a.getEntries()[e]; if (c.initiatorType === 'fetch' || c.initiatorType === 'xmlhttprequest') { var d = c.name; if (typeof _translate.request.api.host == 'string') { _translate.request.api.host = [_translate.request.api.host] } var f = false; for (var i = 0; i < _translate.request.api.host.length; i++) { if (d.indexOf(_translate.request.api.host[i]) > -1) { f = true; break } } if (d.indexOf(_translate.service.edge.api.auth) > -1) { f = true } if (d.indexOf('.microsofttranslator.com/translate') > -1) { f = true } if (f) { continue } if (_translate.request.listener.trigger()) { } else { continue } b = true; break } } if (b) { _translate.request.listener.addExecute() } }); g.observe({ type: "resource", buffered: true }) } } }, storage: { set: function set(a, b) { localStorage.setItem(a, b) }, get: function get(a) { return localStorage.getItem(a) } }, reset: function reset() { var a = _translate.language.getCurrent(); for (var b in _translate.nodeQueue) { for (var c in _translate.nodeQueue[b].list) { for (var d in _translate.nodeQueue[b].list[c]) { var e = _translate.nodeQueue[b].list[c][d]; for (var f in e.nodes) { var g = _translate.storage.get('hash_' + a + '_' + d); if (typeof g == 'undefined') { continue } if (g == null) { continue } if (g.length == 0) { continue } _translate.element.nodeAnalyse.analyse(e.nodes[f].node, g, e.original, e.nodes[f].node.attribute) } } } } _translate.storage.set('to', ''); _translate.to = null; _translate.selectLanguageTag.render() } }; var nodeuuid = { index: function d(a) { var b = a.parentNode; if (b == null) { return '' } var c; if (typeof a.tagName == 'undefined') { c = b.childNodes } else { c = b.querySelectorAll(a.tagName) } var d = Array.prototype.indexOf.call(c, a); return a.nodeName + "" + (d + 1) }, uuid: function b(a) { var b = ''; var n = a; while (n != null) { var c = nodeuuid.index(n); if (c != '') { if (b != '') { b = '_' + b } b = c + b } n = n.parentNode } return b } }; var useEdge = _translate.storage.get('uesEdge'); try { _translate.listener.start(); _translate.language.setLocal('chinese_simplified'); var oldTo = _translate.storage.get('to') ?? ''; if (oldTo.length <= 0) { var lang = _translate.util.getUrlParam('lang') ?? ''; if (lang.length > 0) { const dictionary = { 'traditionalchinese': 'chinese_traditional', 'english': 'english', 'japanese': 'japanese', 'korean': 'korean', 'french': 'french', 'german': 'deutsch', 'russian': 'russian', 'ukrainian': 'ukrainian', 'arabic': 'arabic', 'spanish': 'spanish', 'thai': 'thai', 'vietnamese': 'vietnamese', 'portuguese': 'portuguese', 'hebrew': 'hebrew', 'hungarian': 'hungarian', 'indonesian': 'indonesian', 'italian': 'italian', 'persian': 'persian', 'polish': 'polish', 'romanian': 'romanian', 'turkish': 'turkish', 'dutch': 'dutch', 'hindi': 'hindi', 'tamil': 'tamil', 'swedish': 'swedish', 'malay': 'malay' }; var toLang = dictionary[lang.toLowerCase()] ?? ''; if (toLang.length > 0) { _translate.storage.set('to', toLang) } } } _translate.setAutoDiscriminateLocalLanguage() } catch (e) { console.log(e) } if (useEdge != null && typeof useEdge != 'undefined' && useEdge.length > 0) { _translate.service.use('client.edge') } _translate.execute();