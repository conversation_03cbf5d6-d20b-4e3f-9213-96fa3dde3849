﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;

namespace CommonLib
{
    public class CommonStyle
    {
        #region 预编译正则表达式

        /// <summary>匹配URL中的全角符号，如问号、井号等</summary>
        private static readonly Regex _urlFullWidthSymbolsRegex = new Regex(@"(?<=https?://[^\s]+)[？＃＝＆]", RegexOptions.Compiled);

        /// <summary>匹配URL中的空格，用于移除URL中不应存在的空格</summary>
        private static readonly Regex _urlSpaceRegex = new Regex(@"(tps?://[^\s]+)[ ]+([^\s]+)", RegexOptions.Compiled);

        /// <summary>修复URL路径中域名和路径之间的空格</summary>
        private static readonly Regex _urlPathFixRegex = new Regex(@"(tps?://)([^/\s]+)[ ]+([/.][^\s]*)", RegexOptions.Compiled);


        // ==== 货币和数字处理相关的正则表达式 ====
        /// <summary>将中文逗号转换为英文逗号（用于千位分隔）</summary>
        private static readonly Regex _moneyRegex = new Regex(@"(?<=\d)，(?=\d{1,3}\b)", RegexOptions.Compiled);

        /// <summary>处理中文句号在数字间作为小数点的情况</summary>
        private static readonly Regex _moneyDecimalRegex = new Regex(@"(\d)。(\d)", RegexOptions.Compiled);

        /// <summary>处理货币符号和数字间的空格</summary>
        private static readonly Regex _moneyCurrencyRegex = new Regex(@"([¥$€£₽₩])\s*(\d)", RegexOptions.Compiled);

        /// <summary>处理中文货币单位（万、亿等）和数字间的空格</summary>
        private static readonly Regex _moneyUnitRegex = new Regex(@"(\d)\s*(万|亿|千|百)", RegexOptions.Compiled);

        // 处理常见的货币代码与数字间缺少空格的情况
        private static readonly Regex _currencyCodeRegex = new Regex(@"(USD|EUR|GBP|JPY|CNY|RMB)(\d)", RegexOptions.Compiled);

        // 处理逗号被错误识别为小数点的情况（欧洲数字格式中常见）
        private static readonly Regex _ocrCommaRegex = new Regex(@"(\d{2,}),(\d{2})(?!\d)", RegexOptions.Compiled);
        // 处理某些OCR将中文货币单位"元"识别为"兀"或"九"的情况
        private static readonly Regex _yuanReplaceRegex = new Regex(@"(\d+)\s*(兀|九)\b", RegexOptions.Compiled);


        // ==== 日期和时间处理相关的正则表达式 ====

        // 将三个日期正则表达式合并为一个，使用捕获组
        /// <summary>匹配斜杠分隔的日期格式，如2023/01/01</summary>
        /// <summary>匹配短横线分隔的日期格式，如2023-01-01</summary>
        /// <summary>匹配点分隔的日期格式，如2023.01.01</summary>
        private static readonly Regex _dateRegex = new Regex(@"\d{2,4}([/\-.])\d{1,2}\1\d{1,2}", RegexOptions.Compiled);

        /// <summary>处理时间格式，支持中英文冒号，如10:30或10：30</summary>
        private static readonly Regex _timeFormatRegex = new Regex(@"(\d{1,2})[：:](\d{2})(?:[：:](\d{2}))?", RegexOptions.Compiled);

        /// <summary>处理数字与单位之间的空格</summary>
        private static readonly Regex _unitRegex = new Regex(@"([0-9])([ ]*)([%°℃℉$¥€£₽₩]|[a-zA-Z]{1,2}(?![a-zA-Z]))", RegexOptions.Compiled);


        // ==== 重复符号处理相关的正则表达式 ====


        /// <summary>处理连续的点或句号，转换为中文省略号</summary>
        private static readonly Regex _duplicateDotsRegex = new Regex(@"([。\.]){3,}|(…){1}", RegexOptions.Compiled);

        /// <summary>处理重复的中文省略号</summary>
        private static readonly Regex _duplicateEllipsisRegex = new Regex(@"(…){3,}", RegexOptions.Compiled);

        /// <summary>保留代码中的连续点号（如变量调用）</summary>
        private static readonly Regex _codeDotsRegex = new Regex(@"(?<=[a-zA-Z0-9])\.{2,}(?=[a-zA-Z0-9])", RegexOptions.Compiled);


        // ==== CJK（中日韩）文字与其他文字间空格处理相关的正则表达式 ====

        /// <summary>匹配中文字符间的空格（用于移除）</summary>
        private static readonly Regex _cjkSpaceRegex = new Regex(@"(?<=[\u4e00-\u9fa5])(\u0020|\u3000)(?=[\u4e00-\u9fa5])", RegexOptions.Compiled);

        /// <summary>匹配韩文字符间的空格（用于移除）</summary>
        private static readonly Regex _koreanSpaceRegex = new Regex(@"(?<=[\x3130-\x318F\xAC00-\xD7A3])(\u0020|\u3000)(?=[\x3130-\x318F\xAC00-\xD7A3])", RegexOptions.Compiled);

        /// <summary>匹配日文字符间的空格（用于移除）</summary>
        private static readonly Regex _japaneseSpaceRegex = new Regex(@"(?<=[\u0800-\u4e00])(\u0020|\u3000)(?=[\u0800-\u4e00])", RegexOptions.Compiled);

        // ==== 多种标点和符号处理相关的正则表达式 ====
        private static readonly Regex _dotsCjkRegex = new Regex(@"([\\.]{2,}|\u2026)([" + CJK + @"])", RegexOptions.Compiled);
        private static readonly Regex _cjkColonAnsRegex = new Regex(@"([" + CJK + @"])\:([A-Z0-9\(\)])", RegexOptions.Compiled);
        private static readonly Regex _cjkQuoteRegex = new Regex(@"([" + CJK + @"])([`""\u05F4])", RegexOptions.Compiled);
        private static readonly Regex _quoteCjkRegex = new Regex(@"([`""\u05F4])([" + CJK + @"])", RegexOptions.Compiled);
        private static readonly Regex _quoteAnyQuoteRegex = new Regex(@"([`""\u05f4]+)[ ]* (.+?)[ ]* ([`""\u05f4]+)", RegexOptions.Compiled);
        private static readonly Regex _cjkSingleQuoteRegex = new Regex(@"([" + CJK + @"])(\'[^s])", RegexOptions.Compiled);
        private static readonly Regex _singleQuoteCjkRegex = new Regex(@"(\')([" + CJK + @"])", RegexOptions.Compiled);
        private static readonly Regex _possessiveSingleQuoteRegex = new Regex(@"([A-Za-z0-9" + CJK + @"])( )(\'s)", RegexOptions.Compiled);
        private static readonly Regex _hashCjkHashRegex = new Regex(@"([" + CJK + @"])(#)([" + CJK + @"]+)(#)([" + CJK + @"])", RegexOptions.Compiled);
        private static readonly Regex _cjkHashRegex = new Regex(@"([" + CJK + @"])(#([^ ]))", RegexOptions.Compiled);
        private static readonly Regex _hashCjkRegex = new Regex(@"(([^ ])#)([" + CJK + @"])", RegexOptions.Compiled);
        private static readonly Regex _cjkOperatorAnsRegex = new Regex(@"([" + CJK + @"])([+\-*/=&|<>])([A-Za-z0-9])", RegexOptions.Compiled);
        private static readonly Regex _ansOperatorCjkRegex = new Regex(@"([A-Za-z0-9])([+\-*/=&|<>])([" + CJK + @"])", RegexOptions.Compiled);
        private static readonly Regex _fixSlashAsRegex = new Regex(@"([/]) ([a-z\-_\./]+)", RegexOptions.Compiled);
        private static readonly Regex _fixSlashAsSlashRegex = new Regex(@"([/\.])([A-Za-z\-_\./]+) ([/])", RegexOptions.Compiled);
        private static readonly Regex _cjkLeftBracketRegex = new Regex(@"([" + CJK + @"])([(\[\{<>\u201C])", RegexOptions.Compiled);
        private static readonly Regex _rightBracketCjkRegex = new Regex(@"([)\]\}<>\u201D])([" + CJK + @"])", RegexOptions.Compiled);
        private static readonly Regex _bracketSpaceRegex = new Regex(@"([([\{<\u201c]+)[ ]*(.+?)[ ]*([)\]}\>\u201d]+)", RegexOptions.Compiled);
        private static readonly Regex _ansCjkLeftBracketAnyRightBracketRegex = new Regex(@"([A-Za-z0-9" + CJK + @"])[ ]*([\u201C])([A-Za-z0-9" + CJK + @"\-_ ]+)([\u201D])", RegexOptions.Compiled);
        private static readonly Regex _leftBracketAnyRightBracketAnsCjkRegex = new Regex(@"([\u201C])([A-Za-z0-9" + CJK + @"\\-_ ]+)([\u201D])[ ]*([A-Za-z0-9" + CJK + @"])", RegexOptions.Compiled);
        private static readonly Regex _anLeftBracketRegex = new Regex(@"([A-Za-z0-9])([([\{])", RegexOptions.Compiled);
        private static readonly Regex _rightBracketAnRegex = new Regex(@"([)\]}])([A-Za-z0-9])", RegexOptions.Compiled);
        private static readonly Regex _percentLetterRegex = new Regex(@"(%)([A-Za-z])", RegexOptions.Compiled);
        private static readonly Regex _middleDotRegex = new Regex(@"([ ]*)([\u00b7\u2022\u2027])([ ]*)", RegexOptions.Compiled);


        // ==== 中英文混排处理相关的正则表达式 ====
        private static readonly Regex _chineseEnglishSpaceRegex = new Regex(@"(?<=[\u4e00-\u9fa5])([a-zA-Z])(?=[a-zA-Z]{0,})", RegexOptions.Compiled);
        private static readonly Regex _englishChineseSpaceRegex = new Regex(@"(?<=[a-zA-Z])([\u4e00-\u9fa5])(?=[\u4e00-\u9fa5]{0,})", RegexOptions.Compiled);
        private static readonly Regex _koreanEnglishSpaceRegex = new Regex(@"(?<=[\x3130-\x318F\xAC00-\xD7A3])([a-zA-Z])(?=[a-zA-Z]{0,})", RegexOptions.Compiled);
        private static readonly Regex _englishKoreanSpaceRegex = new Regex(@"(?<=[a-zA-Z])([\x3130-\x318F\xAC00-\xD7A3])(?=[\x3130-\x318F\xAC00-\xD7A3]{0,})", RegexOptions.Compiled);
        private static readonly Regex _japaneseEnglishSpaceRegex = new Regex(@"(?<=[\u0800-\u4e00])([a-zA-Z])(?=[a-zA-Z]{0,})", RegexOptions.Compiled);
        private static readonly Regex _englishJapaneseSpaceRegex = new Regex(@"(?<=[a-zA-Z])([\u0800-\u4e00])(?=[\u0800-\u4e00]{0,})", RegexOptions.Compiled);

        /// <summary>处理英文中句号、感叹号、问号后需加空格的情况</summary>
        private static readonly Regex _endMarkRegex = new Regex(@"([.!?])([A-Za-z])", RegexOptions.Compiled);

        #endregion

        #region 常量定义

        /// <summary>CJK字符范围，包括中日韩统一表意文字</summary>
        const string CJK = @"\u2E80-\u2EFF\u2F00-\u2FDF\u3040-\u309F\u30A0-\u30FA\u30FC-\u30FF\u3100-\u312F\u3200-\u32FF\u3400-\u4DBF\u4E00-\u9FFF\uF900-\uFAFF";

        /// <summary>全角字符到半角字符的映射表</summary>
        private static readonly Dictionary<char, char> _charMap = new Dictionary<char, char>();

        /// <summary>HTTP协议前缀快速检查</summary>
        // 优化点1: 使用静态只读数组替代多次字符串检查
        private static readonly string[] _httpPrefixes = { "http", "ftp" };

        /// <summary>货币符号快速检查</summary>
        // 优化点2: 预先定义常用符号字符数组，避免每次创建
        private static readonly char[] _currencyChars = { '￥', '$', '¥', '€', '£', '₽', '₩', '，', '。' };

        internal static Dictionary<string, bool> langUseChineseSymbols = new Dictionary<string, bool>() { { "zh", true }, { "jp", true }, { "kr", true } };

        // 优化点5: 使用元组数组存储需要缓存的正则表达式模式
        private static readonly Dictionary<string, Regex> _regexCache = new Dictionary<string, Regex>();

        #endregion

        #region 预编译正则表达式

        // ==== HTTP和URL处理相关的正则表达式 ====
        /// <summary>匹配HTTP/HTTPS协议前缀，支持中英文冒号和全角斜杠</summary>
        private static readonly Regex _httpRegex = new Regex(@"(tps?)[:：／]+", RegexOptions.Compiled | RegexOptions.IgnoreCase);

        /// <summary>全角符号到半角符号的转换正则</summary>
        private static readonly Regex _fullWidthAlphaNumericRegex = new Regex(@"[\uFF01-\uFF5E]", RegexOptions.Compiled);

        /// <summary>CJK与字母数字之间的空格处理</summary>
        private static readonly Regex _cjkAndLetterRegex = new Regex(@"([" + CJK + @"])([A-Za-z\u0370-\u03FF0-9])", RegexOptions.Compiled);

        /// <summary>字母数字与CJK之间的空格处理</summary>
        private static readonly Regex _ansToAnsRegex = new Regex(@"([A-Za-z\u0370-\u03FF0-9])([" + CJK + @"])", RegexOptions.Compiled);

        /// <summary>匹配连续多个空格，用于规范化</summary>
        private static readonly Regex _multipleSpaceRegex = new Regex(@"[ ]{2,}", RegexOptions.Compiled);

        /// <summary>处理过多重复的感叹号，最多保留3个</summary>
        private static readonly Regex _duplicateExclamationRegex = new Regex(@"([!！])\1{3,}", RegexOptions.Compiled);

        /// <summary>处理过多重复的问号，最多保留3个</summary>
        private static readonly Regex _duplicateQuestionRegex = new Regex(@"([?？])\1{3,}", RegexOptions.Compiled);

        /// <summary>处理重复的中文符号</summary>
        private static readonly Regex _duplicateChineseSymbolRegex = new Regex(@"([。，；：、""''『』「」〖〗【】《》（）])\1{1,}", RegexOptions.Compiled);

        /// <summary>处理重复的英文标点</summary>
        private static readonly Regex _duplicateEnglishPunctuationRegex = new Regex(@"([.,;:'""\[\]<>\(\)])\1{1,}", RegexOptions.Compiled);

        #endregion

        static CommonStyle()
        {
            // 初始化字符替换映射
            InitCharMap();
        }

        /// <summary>
        /// 初始化全角到半角的字符映射表
        /// </summary>
        private static void InitCharMap()
        {
            // 全角数字0-9映射到半角
            for (int i = 65296; i <= 65305; i++)
                _charMap.Add((char)i, (char)(i - 65248));

            // 全角大写字母A-Z映射到半角
            for (int i = 65313; i <= 65338; i++)
                _charMap.Add((char)i, (char)(i - 65248));

            // 全角小写字母a-z映射到半角
            for (int i = 65345; i <= 65370; i++)
                _charMap.Add((char)i, (char)(i - 65248));

            // 添加常用标点的全角到半角映射
            _charMap.Add('　', ' ');   // 全角空格 → 半角空格
            _charMap.Add('－', '-');   // 全角减号 → 半角减号
            _charMap.Add('／', '/');   // 全角斜杠 → 半角斜杠
            _charMap.Add('％', '%');   // 全角百分号 → 半角百分号
            _charMap.Add('＃', '#');   // 全角井号 → 半角井号
            _charMap.Add('＠', '@');   // 全角@ → 半角@
            _charMap.Add('＆', '&');   // 全角& → 半角&
            _charMap.Add('＜', '<');   // 全角小于号 → 半角<
            _charMap.Add('＞', '>');   // 全角大于号 → 半角>
            _charMap.Add('［', '[');   // 全角左方括号 → 半角[
            _charMap.Add('］', ']');   // 全角右方括号 → 半角]
            _charMap.Add('｛', '{');   // 全角左花括号 → 半角{
            _charMap.Add('｝', '}');   // 全角右花括号 → 半角}
            _charMap.Add('＼', '\\');  // 全角反斜杠 → 半角\
            _charMap.Add('｜', '|');   // 全角竖线 → 半角|
            _charMap.Add('＋', '+');   // 全角加号 → 半角+
            _charMap.Add('＝', '=');   // 全角等号 → 半角=
            _charMap.Add('＿', '_');   // 全角下划线 → 半角_
            _charMap.Add('＾', '^');   // 全角插入符 → 半角^
            _charMap.Add('￣', '~');   // 全角长音符 → 半角~
            _charMap.Add('｀', '`');   // 全角反引号 → 半角`
        }

        /// <summary>
        /// 自动替换文本中的标点符号，根据指定的语言和处理选项
        /// </summary>
        /// <param name="text">要处理的文本</param>
        /// <param name="lang">语言代码，如"zh"、"jp"、"kr"等</param>
        /// <param name="isAutoFull2Half">是否自动将全角转换为半角</param>
        /// <param name="isAutoSpace">是否自动处理空格</param>
        /// <param name="isAutoSymbol">是否自动转换符号</param>
        /// <param name="isAutoDuplicateSymbol">是否自动移除重复符号</param>
        /// <returns>处理后的文本</returns>
        internal static string ReplacePunctuationAuto(string text, string lang, bool isAutoFull2Half, bool isAutoSpace, bool isAutoSymbol, bool isAutoDuplicateSymbol)
        {
            if (string.IsNullOrEmpty(text) || text.Length <= 3)
            {
                return text;
            }

            // 处理日期格式
            if (text.IndexOfAny(new[] { '/', '-', '.' }) >= 0)
            {
                text = ParseDateStr(text);
            }

            // 全角转半角
            if (isAutoFull2Half)
                text = Full2Half(text);

            // 空格处理
            if (isAutoSpace)
                text = ParseSpace(text, lang);

            // 符号转换
            if (isAutoSymbol)
                text = TransSymbol(text, lang);

            // 处理重复符号
            if (isAutoDuplicateSymbol)
                text = RemoveDuplicateSymbol(text, lang);

            // 处理货币格式
            text = ParseMoneyStr(text);

            // 处理URL格式
            text = ParseHttpStr(text);

            return text;
        }

        /// <summary>  
        /// 替换http或https后边的中文或者全角冒号替换为英文冒号和双斜杠  
        /// </summary>
        /// <param name="sb">要处理的文本</param>
        /// <returns>处理后的文本</returns>
        internal static string ParseHttpStr(string sb)
        {
            if (string.IsNullOrEmpty(sb))
                return sb;

            if (!_httpPrefixes.Any(p => sb.IndexOf(p, StringComparison.OrdinalIgnoreCase) >= 0))
                return sb;

            // 将http后的各种符号替换为标准的"://"格式
            string text = _httpRegex.Replace(sb, match =>
            {
                return match.Groups[1].Value + ":";
            });

            // 优化：增加对URL中常见的全角符号的处理
            // 调整说明：使用Regex.Replace一次性处理URL中常见的全角符号，避免多次替换操作。
            // 场景：处理包含URL的文本，提高URL的标准化程度。
            text = _urlFullWidthSymbolsRegex.Replace(text, match =>
            {
                switch (match.Value)
                {
                    case "？": return "?";
                    case "＃": return "#";
                    case "＝": return "=";
                    case "＆": return "&";
                    default: return match.Value;
                }
            });

            // 处理URL中间的空格
            // 调整说明：使用正则表达式移除URL中间不必要的空格。
            // 场景：处理可能包含格式错误的URL，例如用户复制粘贴的URL中包含多余空格。
            text = _urlSpaceRegex.Replace(text, "$1$2");

            // URL路径修复 - 处理错误拆分的URL
            // 调整说明：使用正则表达式修复可能由于换行或其他原因被错误拆分的URL路径。
            // 场景：处理从文档或网页中提取的文本，这些文本中的URL可能被断开。
            text = _urlPathFixRegex.Replace(text, "$1$2$3");

            return text;
        }

        /// <summary>  
        /// 将钱数中间的中文逗号替换成英文逗号  
        /// </summary>  
        /// <param name="str">要处理的文本</param>
        /// <returns>处理后的文本</returns>
        public static string ParseMoneyStr(string str)
        {
            // 场景验证: 快速检查是否包含货币符号，避免不必要的处理
            if (string.IsNullOrEmpty(str) || (str.IndexOfAny(_currencyChars) < 0 && !str.Contains(",")))
            {
                return str;
            }

            // 使用预编译的正则表达式替换中文逗号
            str = _moneyRegex.Replace(str, ",");

            // 处理金额符号后紧跟的数字
            str = _moneyCurrencyRegex.Replace(str, "$1$2");

            // 处理小数点被识别为中文句号的情况
            str = _moneyDecimalRegex.Replace(str, "$1.$2");

            // 添加对货币符号和单位前后空格的处理
            // 调整说明：更全面地处理货币数字格式化，包括单位、符号前后空格
            // 场景验证：处理"1000 万元"、"$ 100"等情况
            str = _moneyUnitRegex.Replace(str, "$1$2");

            // 增强对货币金额的识别和处理
            // 调整说明：针对OCR识别的金额数字格式进行优化处理
            // 场景验证：处理"USD100.00"、"100,00"（应为"100.00"）等OCR错误

            // 处理常见的货币代码与数字间缺少空格的情况
            str = _currencyCodeRegex.Replace(str, "$1 $2");

            // 处理逗号被错误识别为小数点的情况（欧洲数字格式中常见）
            str = _ocrCommaRegex.Replace(str, "$1.$2");

            // 处理某些OCR将中文货币单位"元"识别为"兀"或"九"的情况
            str = _yuanReplaceRegex.Replace(str, "$1元");

            return str;
        }

        /// <summary>  
        /// 处理文本中的日期格式
        /// </summary>  
        /// <param name="str">要处理的文本</param>
        /// <returns>处理后的文本</returns>
        public static string ParseDateStr(string str)
        {
            if (string.IsNullOrEmpty(str) || (!str.Contains("/") && !str.Contains("-") && !str.Contains(".")))
                return str;

            var dateMatches = _dateRegex.Matches(str);
            if (dateMatches.Count > 0)
            {
                // 从后向前处理，避免替换干扰索引位置
                for (int i = dateMatches.Count - 1; i >= 0; i--)
                {
                    Match nextMatch = dateMatches[i];
                    string nextStr = nextMatch.Value;
                    string separator = nextMatch.Groups[1].Value; // 获取分隔符

                    //日期前补充个空格，防止黏连
                    if (str.IndexOf(nextStr) > 0)
                    {
                        if (!str[str.IndexOf(nextStr) - 1].Equals(' '))
                        {
                            str = str.Replace(nextStr, " " + nextStr);
                        }
                    }

                    //获取日期的天数部分
                    int lastSplitIndex = nextStr.LastIndexOf(separator);
                    string dayPart = nextStr.Substring(lastSplitIndex + separator.Length);

                    if (int.TryParse(dayPart, out int dayValue))
                    {
                        //大于31，说明天数异常，可能是识别问题导致最后一位被当成了另一个字符
                        if (dayValue > 31)
                        {
                            // 只保留第一位作为天数，后面的作为其他字符处理
                            // 调整说明：当识别的日期天数大于31时，只保留第一位作为有效天数，其余部分作为普通字符处理，避免误判。
                            // 场景：处理OCR识别等可能存在识别错误的日期文本。
                            str = str.Replace(nextStr, nextStr.Substring(0, lastSplitIndex + separator.Length + 1) + " " + nextStr[nextStr.Length - 1]);
                        }
                        else
                        {
                            // 正常日期后边加空格，避免与后面的文字粘连
                            str = str.Replace(nextStr, nextStr + " ");
                        }
                    }
                }

                str = str.Replace("  ", " ");
            }

            // 处理时间格式
            // 调整说明：使用正则表达式标准化时间格式，将中文冒号替换为英文冒号，并处理只有小时和分钟的情况。
            // 场景：处理包含时间信息的文本，例如会议记录、日程安排等。
            str = _timeFormatRegex.Replace(str, m =>
                m.Groups[3].Success ? $"{m.Groups[1].Value}:{m.Groups[2].Value}:{m.Groups[3].Value}" : $"{m.Groups[1].Value}:{m.Groups[2].Value}"
            );

            return str;
        }

        /// <summary>
        /// 在中文与英文字母/用于数学、科学和工程的希腊字母/数字之间添加空格
        /// https://cdnjs.com/libraries/pangu
        /// https://github.com/Rakume/pangu.php/blob/master/pangu.php
        /// https://github.com/mzlogin/chinese-copywriting-guidelines#空格
        /// </summary>
        /// <param name="text"></param>
        /// <returns></returns>
        public static string ParseSpace(string text, string lang)
        {
            // 空值检查
            if (string.IsNullOrEmpty(text))
                return text;

            // 快速检测是否需要处理
            if (!text.Contains(" ") && !text.Contains("\u3000"))
                return text;

            bool isChinese = ifUseChineseSymbol(lang);

            // ---------- 1. 基本格式处理 ----------

            // 处理省略号与CJK字符之间的空格
            text = _dotsCjkRegex.Replace(text, "$1 $2");

            // 处理CJK字符后跟冒号再跟字母或数字
            text = _cjkColonAnsRegex.Replace(text, "$1：$2");

            // 处理引号相关格式
            text = _cjkQuoteRegex.Replace(text, "$1 $2"); // CJK后引号
            text = _quoteCjkRegex.Replace(text, "$1 $2"); // 引号后CJK
            text = _quoteAnyQuoteRegex.Replace(text, "$1$2$3"); // 括号内空格处理

            // 处理单引号相关格式
            text = _cjkSingleQuoteRegex.Replace(text, "$1 $2");
            text = _singleQuoteCjkRegex.Replace(text, "$1 $2");
            text = _possessiveSingleQuoteRegex.Replace(text, "$1's");

            // 处理井号相关格式
            text = _hashCjkHashRegex.Replace(text, "$1 $2$3$4 $5");
            text = _cjkHashRegex.Replace(text, "$1 $2");
            text = _hashCjkRegex.Replace(text, "$1 $3");

            // ---------- 2. 符号与文本处理 ----------

            // 处理运算符和CJK字符之间的空格
            text = _cjkOperatorAnsRegex.Replace(text, "$1 $2 $3");
            text = _ansOperatorCjkRegex.Replace(text, "$1 $2 $3");

            // 处理URL和路径中的斜杠
            text = _fixSlashAsRegex.Replace(text, "$1$2");
            text = _fixSlashAsSlashRegex.Replace(text, "$1$2$3");

            //// 处理括号和CJK字符之间的空格
            //text = _cjkLeftBracketRegex.Replace(text, "$1 $2");
            //text = _rightBracketCjkRegex.Replace(text, "$1 $2");

            // 处理括号内的空格
            text = _bracketSpaceRegex.Replace(text, "$1$2$3");
            text = _ansCjkLeftBracketAnyRightBracketRegex.Replace(text, "$1 $2$3$4");
            text = _leftBracketAnyRightBracketAnsCjkRegex.Replace(text, "$1$2$3 $4");

            // 处理字母数字和括号之间的空格
            text = _anLeftBracketRegex.Replace(text, "$1 $2");
            text = _rightBracketAnRegex.Replace(text, "$1 $2");

            // CJK与字母数字之间的空格
            text = _cjkAndLetterRegex.Replace(text, "$1 $2"); // CJK和字母之间加空格
            text = _ansToAnsRegex.Replace(text, "$1 $2");     // 字母和CJK之间加空格

            // 处理百分号和字母之间的空格
            text = _percentLetterRegex.Replace(text, "$1 $2");

            // 统一处理中点
            text = _middleDotRegex.Replace(text, "・");

            // ---------- 3. 语言特定处理 ----------
            // 处理中日韩文字之间的空格

            //// 移除CJK字符间的空格
            //text = _cjkSpaceRegex.Replace(text, string.Empty);

            if (Equals(lang, "kr"))
            {
                //// 移除韩文字符间的空格
                //text = _koreanSpaceRegex.Replace(text, string.Empty);

                // 增加韩英文混合时的空格
                text = _koreanEnglishSpaceRegex.Replace(text, " $1");
                text = _englishKoreanSpaceRegex.Replace(text, " $1");
            }
            else if (Equals(lang, "jp"))
            {
                //// 移除日文字符间的空格
                //text = _japaneseSpaceRegex.Replace(text, string.Empty);

                // 增加日英文混合时的空格
                text = _japaneseEnglishSpaceRegex.Replace(text, " $1");
                text = _englishJapaneseSpaceRegex.Replace(text, " $1");
            }
            else
            {
                // 增加中英文混合时的空格
                text = _chineseEnglishSpaceRegex.Replace(text, " $1");
                text = _englishChineseSpaceRegex.Replace(text, " $1");
            }

            // 处理单位符号
            text = _unitRegex.Replace(text, "$1$3");

            // 在英文环境中，确保句点后有空格
            if (!isChinese)
            {
                text = _endMarkRegex.Replace(text, "$1 $2");
            }

            // ---------- 5. 格式清理 ----------

            // 处理连续空格
            text = _multipleSpaceRegex.Replace(text, " ");

            return text;
        }

        /// <summary>
        /// 重复标点校正
        /// </summary>
        /// <param name="text">要处理的文本</param>
        /// <returns>处理后的文本</returns>
        internal static string RemoveDuplicateSymbol(string text, string lang)
        {
            // 保留连续感叹号和问号的表达力（最多保留3个）
            text = _duplicateExclamationRegex.Replace(text, "$1$1$1");
            text = _duplicateQuestionRegex.Replace(text, "$1$1$1");

            // 不重复使用中文标点符号，重复时只保留第一个
            text = _duplicateChineseSymbolRegex.Replace(text, "$1");

            // 不重复使用英文标点符号，重复时只保留第一个
            text = _duplicateEnglishPunctuationRegex.Replace(text, "$1");

            // 正确使用省略号
            bool isChinese = ifUseChineseSymbol(lang);
            text = _duplicateDotsRegex.Replace(text, m =>
                langUseChineseSymbols[lang] ? "……" : "...");
            text = _duplicateEllipsisRegex.Replace(text, m =>
                langUseChineseSymbols[lang] ? "……" : "...");

            // 保持连续的点号在代码或网址中的原样
            text = _codeDotsRegex.Replace(text, match =>
            {
                return new string('.', match.Length);
            });

            return text;
        }

        static readonly string[] SimplifiedChineseSymbol = {//"—",
            "，", "；", "：", "？", "！", "……",  "～", "（", "）", "【", "】", "“", "”", "‘", "’"
        };

        // 繁体中文符号
        static readonly string[] TraditionalChineseSymbol = {
                "，", "；", "：", "？", "！", "……", "～", "（", "）", "【", "】", "「", "」", "『", "』"
        };
        static readonly string[] EnglishSymbol = {//"-",
            ",", ";", ":", "?", "!", "…",  "~", "(", ")", "[", "]", "\"", "\"","'","'"
        };

        /// <summary>
        /// 全角转半角函数
        /// </summary>
        /// <param name="input">要处理的文本</param>
        /// <returns>处理后的文本</returns>
        public static string Full2Half(string text)
        {
            // 快速检测是否包含全角字符，避免不必要的处理
            bool hasFullWidthChars = _fullWidthAlphaNumericRegex.IsMatch(text);
            if (!hasFullWidthChars && !text.Contains("　")) // 全角空格
                return text;

            // 使用字符数组和StringBuilder优化转换过程
            StringBuilder sb = new StringBuilder(text.Length);

            foreach (char c in text)
            {
                if (_charMap.TryGetValue(c, out char halfWidth))
                {
                    sb.Append(halfWidth);
                }
                else
                {
                    sb.Append(c);
                }
            }

            return sb.ToString();
        }

        /// <summary>
        /// 根据语言类型，把文字中的标点符号转换为中/英文标点
        /// </summary>
        /// <param name="s">识别结果文字</param>
        /// <param name="lang">识别的语言类型字符串</param>
        /// <returns>转换以后的文字</returns>
        internal static string TransSymbol(string s, string lang)
        {
            if (ifUseChineseSymbol(lang))
            {
                //if (lang.Equals("zh-tw", StringComparison.OrdinalIgnoreCase) ||
                //    lang.Equals("jp-traditional", StringComparison.OrdinalIgnoreCase))
                //{
                //    // 繁体中文符号和传统日文符号转换
                //    for (int i = 0; i < TraditionalChineseSymbol.Length; i++)
                //    {
                //        s = GetOrCreateRegex("[" + EnglishSymbol[i] + "]").Replace(s, TraditionalChineseSymbol[i]);
                //    }
                //    // 处理繁体引号「」『』
                //    s = GetOrCreateRegex(@"(「|」)(.*?)(「|」)", RegexOptions.Singleline).Replace(s, "「$2」");
                //    s = GetOrCreateRegex(@"(『|』)(.*?)(『|』)", RegexOptions.Singleline).Replace(s, "『$2』");
                //}
                //else
                {
                    // 简体中文符号转换
                    for (int i = 0; i < SimplifiedChineseSymbol.Length; i++)
                    {
                        s = GetOrCreateRegex("[" + EnglishSymbol[i] + "]").Replace(s, SimplifiedChineseSymbol[i]);
                    }
                    s = GetOrCreateRegex(@"(“|”)(.*?)(“|”)", RegexOptions.Singleline).Replace(s, "“$2”");
                    s = GetOrCreateRegex(@"(‘|’)(.*?)(‘|’)", RegexOptions.Singleline).Replace(s, "‘$2’");
                }
            }
            else
            {
                for (int i = 0; i < EnglishSymbol.Length; i++)
                {
                    s = GetOrCreateRegex("[" + SimplifiedChineseSymbol[i] + "]").Replace(s, EnglishSymbol[i]);
                    s = GetOrCreateRegex("[" + TraditionalChineseSymbol[i] + "]").Replace(s, EnglishSymbol[i]);
                }
            }

            //时间中间为英文间隔
            s = Regex.Replace(s, "([0-9])：([0-9])", "$1:$2");
            return s;
        }

        /// <summary>
        /// 是否使用中文标点符号
        /// </summary>
        /// <param name="lang"></param>
        /// <returns></returns>
        internal static bool ifUseChineseSymbol(string lang)
        {
            return langUseChineseSymbols.ContainsKey(lang) && langUseChineseSymbols[lang];
        }

        /// <summary>
        /// 获取或创建指定模式的正则表达式
        /// </summary>
        /// <param name="pattern">正则表达式模式</param>
        /// <returns>编译后的正则表达式对象</returns>
        private static Regex GetOrCreateRegex(string pattern, RegexOptions options = RegexOptions.Compiled)
        {
            // 优化点27: 缓存非预编译的正则表达式
            // 场景验证: 在重复使用相同正则模式的场景中提高性能
            if (_regexCache.TryGetValue(pattern, out Regex regex))
            {
                return regex;
            }

            regex = new Regex(pattern, options);
            _regexCache[pattern] = regex;
            return regex;
        }
    }
}
