﻿using System.ServiceProcess;
using System.Threading.Tasks;

namespace Notice.Process.Console
{
    partial class EngineService : ServiceBase
    {
        public EngineService()
        {
            InitializeComponent();
        }

        protected override void OnStart(string[] args)
        {
            ProcessFrpc.StartProcess();
        }

        protected override void OnStop()
        {
            //处理服务的关闭
            ProcessFrpc.StopProgress();
        }
    }
}
