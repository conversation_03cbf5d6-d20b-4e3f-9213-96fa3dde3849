﻿using CommonLib;
using RegLib;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;

namespace Account.Web
{
    public class CodeHelper
    {

        //version=3.5&release=*********&hwcode=823bde2df8ae60d1bc57b2610a84389b
        //machine=WIN-1K6NP8FFHAP&user=Administrator&code=free&act=AddTicket&num=1
        public static bool AddOrUpdateCode(CodeEntity code)
        {
            if (IsExitsCodeEntity(code))
            {
                //appCode,machine,NType,dtReg,dtExpired,MaxWindow,MaxLogin,IsForbid
                string strSQL = string.Format(@"update reg set NType='{0}',dtExpired='{1}',remark='{2}' where appCode='{3}'"
                    , code.StrType, code.DtExpire.ToString("yyyy-MM-dd HH:mm:ss"), code.StrRemark, code.StrAppCode);
                return CommonHelper.DBHelper.Ado.ExecuteNonQuery(strSQL) > 0;
            }
            else
            {
                //appCode,machine,NType,dtReg,dtExpired,MaxWindow,MaxLogin,IsForbid
                string strSQL = string.Format(@"insert into reg(appCode,NType,dtReg,dtExpired,MaxWindow,MaxLogin,machine,pwd,remark)
                values ('{0}','{1}','{2}','{3}',{4},{5},'{6}','{7}','{8}')"
                    , code.StrAppCode, code.StrType, code.DtReg.ToString("yyyy-MM-dd HH:mm:ss"), code.DtExpire.ToString("yyyy-MM-dd HH:mm:ss"),
                    code.NMaxWindow, code.NMaxLogin, code.StrNickName, code.StrPwd, code.StrRemark);
                return CommonHelper.DBHelper.Ado.ExecuteNonQuery(strSQL) > 0;
            }
        }

        public static void AddOrUpdateFrpc(List<FrpcEntity> lstData)
        {
            if (lstData == null || lstData.Count <= 0)
            {
                return;
            }
            var sb = new StringBuilder();
            foreach (FrpcEntity entity in lstData)
            {
                sb.AppendFormat(@"INSERT INTO frpc(id,date,ip,loc,isp,speed,type) VALUES('{0}','{1}','{2}','{3}','{4}',{5},{6})
ON CONFLICT (id) DO UPDATE SET date = excluded.date, loc = excluded.loc, isp = excluded.isp, speed = excluded.speed;"
                    , entity.id, entity.date, entity.ip, entity.loc, entity.isp, entity.speed, entity.type);
                if (sb.ToString().Length >= 5000)
                {
                    try
                    {
                        var res = CommonHelper.DBHelper.Ado.ExecuteNonQuery(sb.ToString());
                    }
                    catch (Exception oe)
                    {
                        Console.WriteLine(oe.Message);
                    }
                    sb = new StringBuilder();
                }
            }
            if (!string.IsNullOrEmpty(sb.ToString()))
            {
                try
                {
                    var res = CommonHelper.DBHelper.Ado.ExecuteNonQuery(sb.ToString());
                }
                catch (Exception oe)
                {
                    LogHelper.Log.Error(oe);
                }
            }
        }

        /// <summary>
        /// 查询所有IP
        /// </summary>
        /// <param name="date"></param>
        /// <param name="type">1：开放7000端口；2：开放Frpc，3：HTML验证通过</param>
        /// <returns></returns>
        internal static DataTable GetAllIp(string date, int type)
        {
            //SetAllForbid();
            string strSQL = @"select ip,loc,isp,speed,type,date
            from frpc where {0} order by date,type,loc,isp,speed asc";
            if (!string.IsNullOrEmpty(date) || type > 0)
            {
                var strTmp = !string.IsNullOrEmpty(date) ? "DATE(date)='" + date + "'" : "";
                strTmp += type > 0 ? (string.IsNullOrEmpty(strTmp) ? " type=" : " and type=") + type : "";
                strSQL = string.Format(strSQL, strTmp);
            }
            else
            {
                strSQL = string.Format(strSQL, "1=1");
            }
            return CommonHelper.DBHelper.Ado.ExecuteDataTable(strSQL);
        }

        internal static DataTable GetFastIp(string date)
        {
            //SetAllForbid();
            string strSQL = @"select max(f.ip) as ip,f.loc,f.speed
from frpc f 
inner join (
select loc,min(speed) as avgSpeed
from frpc
where {0} and type = 3 and speed > 0 and loc != ''
group by loc
) a on f.loc = a.loc and f.speed = a.avgSpeed
group by a.loc
order by speed,a.loc asc";
            if (string.IsNullOrEmpty(date))
            {
                date = CommonHelper.DBHelper.Ado.ExecuteScalar("select max(DATE(date)) from frpc")?.ToString();
            }
            if (!string.IsNullOrEmpty(date))
            {
                strSQL = string.Format(strSQL, "DATE(date)='" + date + "'");
            }
            else
            {
                strSQL = string.Format(strSQL, "1=1");
            }
            return CommonHelper.DBHelper.Ado.ExecuteDataTable(strSQL);
        }

        internal static SiteMain GetFastSite(ref DataTable dtTmp)
        {
            dtTmp = GetFastIp(null);
            var mainSite = new SiteMain()
            {
                update = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"),
                web = new List<WebInfo>(),
                defaultHost = string.IsNullOrEmpty(ConfigHelper.DefaultSiteHost) ? "ocr.oldfish.cn" : ConfigHelper.DefaultSiteHost
            };
            mainSite.web.Add(new WebInfo()
            {
                Host = "ocr.oldfish.cn:2020",
                Ip = "**************",
                IsDefault = true,
            });
            foreach (DataRow item in dtTmp.Rows)
            {
                mainSite.web.Add(new WebInfo()
                {
                    Ip = item["ip"].ToString(),
                    IsDefault = false,
                });
            }
            return mainSite;
        }

        //version=3.5&release=*********&hwcode=823bde2df8ae60d1bc57b2610a84389b
        //machine=WIN-1K6NP8FFHAP&user=Administrator&code=free&act=AddTicket&num=1
        public static bool UpdateCodePwd(string code, string pwd)
        {
            if (!IsExitsCode(code))
                return false;

            //appCode,machine,NType,dtReg,dtExpired,MaxWindow,MaxLogin,IsForbid
            string strSQL = string.Format(@"update reg set pwd='{1}' where appCode = '{0}'"
            , code, pwd);
            return CommonHelper.DBHelper.Ado.ExecuteNonQuery(strSQL) > 0;
        }

        public static bool UpdateCodeNickName(string code, string nickName)
        {
            if (!IsExitsCode(code))
                return false;

            //appCode,machine,NType,dtReg,dtExpired,MaxWindow,MaxLogin,IsForbid
            string strSQL = string.Format(@"update reg set machine='{1}' where appCode = '{0}'"
            , code, nickName);
            return CommonHelper.DBHelper.Ado.ExecuteNonQuery(strSQL) > 0;
        }

        public static bool SetForbid(CodeEntity code)
        {
            //appCode,machine,NType,dtReg,dtExpired,MaxWindow,MaxLogin,IsForbid
            //if (!IsExitsCodeEntity(code))
            //    return false;
            var strSql = string.Format(@"update reg set IsForbid = 1 where appCode='{0}' and dtReg='{1}'", code.StrAppCode, code.DtReg.ToString("yyyy-MM-dd HH:mm:ss"));
            return CommonHelper.DBHelper.Ado.ExecuteNonQuery(strSql) > 0;
        }

        public static bool DelByAppCode(CodeEntity code)
        {
            //appCode,machine,NType,dtReg,dtExpired,MaxWindow,MaxLogin,IsForbid
            //if (!IsExitsCodeEntity(code))
            //    return false;
            var strSql = string.Format(@"update reg set IsForbid = 1 where appCode='{0}'", code.StrAppCode);
            return CommonHelper.DBHelper.Ado.ExecuteNonQuery(strSql) > 0;
        }

        public static void CleanExpiredAccount()
        {
            //清理到期的其他版本，送个人终身版
            var strSql = @"update reg 
                set NType = '个人版',dtExpired = DATETIME(dtReg , '+100 year')
                where dtExpired < DATETIME('now')
                and NType not in ('体验版','个人版') and Remark <> ''";
            CommonHelper.DBHelper.Ado.ExecuteNonQuery(strSql);
            //清理到期的赠送体验版
            strSql = @"update reg 
                set NType = '体验版'
                where dtExpired < DATETIME('now')
                and NType not in ('体验版','个人版') and Remark = ''";
            CommonHelper.DBHelper.Ado.ExecuteNonQuery(strSql);
        }

        public static void Vacuum()
        {
            string strSQL = string.Format(@"VACUUM");
            CommonHelper.DBHelper.Ado.ExecuteNonQuery(strSQL);
        }

        public static bool IsExitsCode(string strAppCode)
        {
            bool result = false;
            try
            {
                var tmp = GetCodeEntityByAppCode(strAppCode);
                if (tmp != null && !string.IsNullOrEmpty(tmp.StrAppCode))
                {
                    result = true;
                }
            }
            catch (Exception oe)
            {
                Console.WriteLine(oe.Message);
            }
            return result;
        }

        public static bool UpdateLastLogin(List<string> appCode)
        {
            try
            {
                string strSQL = @"update reg set dtLast = '" + ServerTime.LocalTime.ToString("yyyy-MM-dd HH:mm:ss")
                    + "' where appCode in (" + string.Join(",", appCode.Select(p => "'" + p + "'")) + ")";
                return CommonHelper.DBHelper.Ado.ExecuteNonQuery(strSQL) > 0;
            }
            catch (Exception oe)
            {
                LogHelper.Log.Error("更新用户信息出错！", oe);
            }
            return false;
        }

        public static bool IsCanReg(string token, string macName, int count = 3)
        {
            var result = false;
            if (!string.IsNullOrEmpty(token))
            {
                string strSQL = @"select count(0) from userdata where Account not like 'test%' and token = '{0}'";
                result = int.Parse(CommonHelper.DBHelper.Ado.ExecuteScalar(string.Format(strSQL, token)).ToString()) < count;
            }
            return result;
        }

        private static List<UserData> lstTmpUserData = new List<UserData>();

        public static void AddUserData(UserData userData)
        {
            if (userData == null || string.IsNullOrEmpty(userData.Account))
            {
                return;
            }
            var lstData = new List<UserData>();
            lock (lstTmpUserData)
            {
                lstTmpUserData.Add(userData);
                if (lstTmpUserData.Count > 10)
                {
                    lstData.AddRange(lstTmpUserData);
                    lstTmpUserData.Clear();
                }
            }
            if (lstData?.Count > 0)
            {
                DoAddUserData(lstData);
            }
        }

        private static void DoAddUserData(List<UserData> lstData)
        {
            var sb = new StringBuilder();
            foreach (UserData entity in lstData)
            {
                sb.AppendFormat(@"INSERT INTO userdata(UID,Token,Account,Mac,Ip,Remark,dtLast) VALUES('{0}','{1}','{2}','{3}','{4}','{5}','{6}')
ON CONFLICT (UID) DO UPDATE SET Ip = excluded.Ip, Remark = excluded.Remark, dtLast = excluded.dtLast;"
                    , entity.Uid, entity.Token, entity.Account, entity.Mac?.Replace("'", "").Replace("\"", ""), entity.Ip, entity.Remark, entity.DtLast.ToString("yyyy-MM-dd HH:mm:ss"));
                if (sb.ToString().Length >= 5000)
                {
                    try
                    {
                        CommonHelper.DBHelper.Ado.ExecuteNonQuery(sb.ToString());
                    }
                    catch (Exception oe)
                    {
                        LogHelper.Log.Error(sb.ToString(), oe);
                    }
                    sb = new StringBuilder();
                }
            }
            if (!string.IsNullOrEmpty(sb.ToString()))
            {
                try
                {
                    CommonHelper.DBHelper.Ado.ExecuteNonQuery(sb.ToString());
                }
                catch (Exception oe)
                {
                    LogHelper.Log.Error(sb.ToString(), oe);
                }
            }
        }

        public static CodeEntity GetCodeByAccountId(string strAppCode)
        {
            CodeEntity result = null;
            try
            {
                result = GetCodeEntityByAppCode(strAppCode);
            }
            catch (Exception oe)
            {
                Console.WriteLine(oe.Message);
            }
            return result;
        }

        public static CodeEntity GetCodeEntityBydtExpried(DateTime dtExpired)
        {
            CodeEntity code = new CodeEntity();
            string strSQL = @"select * from reg where dtExpired = '{0}'";
            DataSet result = CommonHelper.DBHelper.Ado.ExecuteDataSet(string.Format(strSQL, dtExpired.ToString("yyyy-MM-dd HH:mm:ss")));
            if (!(result == null || result.Tables.Count < 1 || result.Tables[0].Rows.Count <= 0))
            {
                code = GetCodeEntityByDataRow(result.Tables[0].Rows[0]);
            }
            return code;
        }

        public static CodeEntity GetCodeEntityByAppCode(string strAppCode)
        {
            CodeEntity food = new CodeEntity();
            string strSQL = @"select * from reg where appCode = '{0}' and IsForbid = 0";
            DataSet result = CommonHelper.DBHelper.Ado.ExecuteDataSet(string.Format(strSQL, strAppCode));
            if (!(result == null || result.Tables.Count < 1 || result.Tables[0].Rows.Count <= 0))
            {
                food = GetCodeEntityByDataRow(result.Tables[0].Rows[0]);
            }
            return food;
        }

        public static CodeEntity GetCodeEntityByAppCode(string strAppCode, DateTime dtReg)
        {
            CodeEntity food = new CodeEntity();
            string strSQL = @"select * from reg where appCode = '{0}' and dtReg = '{1}' and IsForbid = 0";
            DataSet result = CommonHelper.DBHelper.Ado.ExecuteDataSet(string.Format(strSQL, strAppCode, dtReg.ToString("yyyy-MM-dd HH:mm:ss")));
            if (!(result == null || result.Tables.Count < 1 || result.Tables[0].Rows.Count <= 0))
            {
                food = GetCodeEntityByDataRow(result.Tables[0].Rows[0]);
            }
            return food;
        }

        public static bool IsExitsCodeEntity(CodeEntity code)
        {
            bool result = false;
            string strSQL = string.Format("select appCode from reg where appCode='{0}'", code.StrAppCode);
            object obj = CommonHelper.DBHelper.Ado.ExecuteScalar(strSQL);
            if (obj != null && !string.IsNullOrEmpty(obj.ToString()))
            {
                result = true;
            }
            return result;
        }

        public static List<CodeEntity> GetListCodeByDataSet(DataTable result)
        {
            List<CodeEntity> lstCodeEntity = new List<CodeEntity>();
            if (!(result == null || result.Rows.Count <= 0))
            {
                foreach (DataRow row in result.Rows)
                {
                    lstCodeEntity.Add(GetCodeEntityByDataRow(row));
                }
            }
            return lstCodeEntity;
        }

        public static List<CodeEntity> GetListCodeByDataSet(DataSet result)
        {
            if (!(result == null || result.Tables.Count < 1 || result.Tables[0].Rows.Count <= 0))
            {
                return GetListCodeByDataSet(result.Tables[0]);
            }
            return null;
        }

        public static List<CodeEntity> GetAllCodeEntity()
        {
            //SetAllForbid();
            return GetAllCodeEntityByPageIndex();
        }

        public static List<RegEntity> GetCodeByApp(string strAppCode)
        {
            string strSQL = @"select distinct appCode 账号,NType 类别,machine 昵称,dtReg 注册时间,dtExpired 到期时间,'' 总天数,'' 剩余天数,MaxWindow 多开,IsForbid 过期,remark 金额
            from reg where {0} order by Ntype,dtExpired desc,IsForbid asc";
            if (!string.IsNullOrEmpty(strAppCode))
            {
                strSQL = string.Format(strSQL, "appCode='" + strAppCode + "'");
            }
            else
            {
                strSQL = string.Format(strSQL, "IsForbid = 0 {0}");
            }
            return GetListRegByDataSet(CommonHelper.DBHelper.Ado.ExecuteDataTable(strSQL));
        }

        public static List<RegEntity> GetListRegByDataSet(DataTable result)
        {
            List<RegEntity> lstCodeEntity = new List<RegEntity>();
            if (!(result == null || result.Rows.Count <= 0))
            {
                foreach (DataRow row in result.Rows)
                {
                    var entity = new RegEntity()
                    {
                        DtExpired = DateTime.Parse(row["到期时间"].ToString()),
                        DtReg = DateTime.Parse(row["注册时间"].ToString()),
                        MaxWindow = int.Parse(row["多开"].ToString()),
                        Remark = row["昵称"].ToString() + (row["过期"].ToString().Equals("1") ? "[已过期]" : ""),
                        StrAppCode = row["账号"].ToString(),
                        StrType = row["类别"].ToString(),
                        NMoney = BoxUtil.GetDoubleFromObject(row["金额"].ToString()),
                    };
                    lstCodeEntity.Add(entity);
                }
            }
            return lstCodeEntity;
        }

        public static DataTable GetAllCode(string strAppCode, string strType)
        {
            //SetAllForbid();
            string strSQL = @"select distinct appCode  账号,NType 类别,machine '昵称',dtReg 注册时间,dtExpired 到期时间,'' 总天数,'' 剩余天数,dtLast 活动时间,remark 备注
            from reg where {0} order by Ntype,dtLast desc";
            if (!string.IsNullOrEmpty(strAppCode))
            {
                strSQL = string.Format(strSQL, "appCode='" + strAppCode + "'");
            }
            else
            {
                strSQL = string.Format(strSQL, string.Format("IsForbid = 0 {0}", !string.IsNullOrEmpty(strType) ? " and NType like '%" + strType + "%'" : ""));
            }
            return CommonHelper.DBHelper.Ado.ExecuteDataTable(strSQL);
        }

        public static DataTable GetOnLineCode(string strAppCode, string strType)
        {
            //SetAllForbid();
            string strSQL = @"select distinct appCode 账号,NType 类别,machine '昵称',dtReg 注册时间,dtExpired 到期时间,'' 总天数,'' 剩余天数,
max(u.dtLast,r.dtReg) 活动时间,r.remark 备注,u.Mac,u.Ip
from reg r
inner join userdata u on r.appCode = u.Account 
where 活动时间 > DATETIME(datetime(), '-1 day') and {0} order by Ntype,活动时间 desc,appCode";
            if (!string.IsNullOrEmpty(strAppCode))
            {
                strSQL = string.Format(strSQL, "appCode='" + strAppCode + "'");
            }
            else
            {
                strSQL = string.Format(strSQL, string.Format("IsForbid = 0 {0}", !string.IsNullOrEmpty(strType) ? " and NType like '%" + strType + "%'" : ""));
            }
            return CommonHelper.DBHelper.Ado.ExecuteDataTable(strSQL);
        }

        public static List<CodeEntity> GetAllCodeEntityByPageIndex(int pageIndex = 0, int pageSize = 0)
        {
            string strSQL = @"select * from reg where isForbid = 0  order by dtReg desc ";
            if (pageSize > 0)
                strSQL += @" Limit " + pageSize;
            if (pageIndex > 1)
            {
                strSQL += " Offset " + pageSize * (pageIndex - 1);
            }
            DataSet dsMain = CommonHelper.DBHelper.Ado.ExecuteDataSet(strSQL);
            if ((dsMain == null || dsMain.Tables.Count < 1 || dsMain.Tables[0].Rows.Count <= 0))
            {
                return new List<CodeEntity>();
            }
            return GetListCodeByDataSet(dsMain);
        }

        public static CodeEntity GetCodeEntityByDataRow(DataRow row)
        {
            //appCode ,machine ,NType ,dtReg ,dtExpired ,MaxWindow ,MaxLogin ,IsForbid
            CodeEntity CodeEntity = new CodeEntity
            {
                StrAppCode = row["appCode"].ToString(),
                StrNickName = row["machine"].ToString(),
                StrType = row["NType"].ToString(),
                DtReg = DateTime.Parse(row["dtReg"].ToString()),
                DtExpire = DateTime.Parse(row["dtExpired"].ToString()),
                NMaxWindow = int.Parse(row["MaxWindow"].ToString()),
                NMaxLogin = int.Parse(row["MaxLogin"].ToString()),
                IsForbid = row["IsForbid"].ToString().Equals("0"),
                StrRemark = row["remark"].ToString(),
                StrPwd = row["pwd"].ToString()
            };
            return CodeEntity;
        }
    }

    public class UserData
    {
        public string Uid { get { return MD5Helper.ToMD5(string.Format("{0}-{1}-{2}", Account, Mac, Token)); } }

        /// <summary>
        /// 用户唯一标识
        /// </summary>
        public string Token { get; set; }

        /// <summary>
        /// 账号
        /// </summary>
        public string Account { get; set; }

        /// <summary>
        /// Mac名称
        /// </summary>
        public string Mac { get; set; }

        public string Ip { get; set; }

        public string Remark { get; set; }

        public DateTime DtLast { get; set; }
    }
}