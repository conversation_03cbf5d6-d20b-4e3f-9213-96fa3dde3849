﻿using System;
using System.Collections;
using System.Collections.Generic;
using System.ComponentModel;
using System.Windows.Forms;

namespace NewTicket.MyControl
{
    [ToolboxItem(true)]
    public class CheckCombo : TreeCombo
    {
        #region Class members
        public string StrSpilt = ",";

        ArrayList m_checked = new ArrayList(20);
        #endregion

        #region Class Properties
        [Browsable(false)]
        public ArrayList CheckedItems
        {
            get
            {
                return m_checked;
            }
        }
        #endregion

        #region Constructor
        public CheckCombo()
        {
        }
        #endregion

        #region Class Overrides
        protected override void OnDropDownControlBinding(CustomCombo.EventArgsBindDropDownControl e)
        {
            base.OnDropDownControlBinding(e);

            TreeView tree = (TreeView)e.BindedControl;
            tree.CheckBoxes = true;
            tree.FullRowSelect = true;
            tree.HideSelection = false;
            tree.ShowLines = false;
            tree.ShowPlusMinus = false;
            tree.ShowRootLines = false;
            tree.Sorted = true;

            tree.AfterCheck += tree_AfterCheck;
        }

        protected override void OnCloseDropDownHandler(object sender, CustomCombo.EventArgsCloseDropDown e)
        {
            base.Value = CalculateValue();
        }

        protected override bool OnValueValidate(string value)
        {
            // TODO: implement values validation
            return true;
        }

        protected override string SetChecked(string value)
        {
            m_checked = new ArrayList();
            //if (!string.IsNullOrEmpty(value))
            {
                BeginUpdate();
                List<string> lstTmp = new List<string>(value.Split(new string[] { StrSpilt }, StringSplitOptions.RemoveEmptyEntries));
                foreach (TreeNode node in m_tree.Nodes)
                {
                    if (lstTmp.Contains(node.Text))
                    {
                        if (!node.Checked)
                            node.Checked = true;
                        if (!m_checked.Contains(node.Text))
                            m_checked.Add(node.Text);
                    }
                    else
                    {
                        if (node.Checked)
                            node.Checked = false;
                    }
                }
                EndUpdate();
                Invalidate();
            }
            return string.Join(StrSpilt, m_checked.ToArray());
        }
        #endregion

        #region Class Helper methods
        private string CalculateValue()
        {
            string outString = string.Join(StrSpilt, m_checked.ToArray());

            //foreach (string node in m_checked)
            //{
            //    outString += node.Text;
            //    outString += ",";
            //}

            //if (outString.Length > 2)
            //    outString = outString.Remove(outString.Length - 2, 2);

            return outString;
        }

        private void tree_AfterCheck(object sender, System.Windows.Forms.TreeViewEventArgs e)
        {
            if (e.Node.Checked == true)
            {
                if (m_checked.Contains(e.Node.Text) == false)
                    m_checked.Add(e.Node.Text);
            }
            else
            {
                if (m_checked.Contains(e.Node.Text) == true)
                    m_checked.Remove(e.Node.Text);
            }
            base.Value = CalculateValue();
            OnTextChanged(null);
        }

        protected override void OnTextChanged(EventArgs e)
        {
            base.OnTextChanged(e);
        }
        #endregion

        #region Class Public methods

        public void CheckAll()
        {
            BeginUpdate();
            foreach (TreeNode node in m_tree.Nodes)
            {
                node.Checked = true;
            }
            EndUpdate();
            Invalidate();
        }

        public void UnCheckAll()
        {
            BeginUpdate();
            foreach (TreeNode node in m_tree.Nodes)
            {
                node.Checked = false;
            }
            EndUpdate();
            Invalidate();
        }
        #endregion

        protected override void OnTreeItemChanged(object sender, System.Windows.Forms.TreeViewEventArgs e)
        {
            // do nothing here for correct update of data
        }

    }
}
