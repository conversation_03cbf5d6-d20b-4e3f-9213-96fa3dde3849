﻿
using CommonLib;
using System;
using System.Collections.Generic;
using System.Collections.Specialized;
using System.IO;
using System.Web.UI.WebControls;

namespace MathOcr
{
    /// <summary>
    /// </summary>
    public class MathPixRec : BaseMathRec
    {
        public MathPixRec()
        {
            OcrType = MathOcrType.MathPix;
            OcrGroup = OcrGroupType.MathPix;

            MaxExecPerTime = 30;

            LstJsonPreProcessArray = new List<object>() { "text" };
            IsProcessJsonResultByArray = false;
        }

        private List<string> lstAuthAccount = new List<string>() {
            "{\"email\":\"<EMAIL>\",\"password\":\"aa123456\"}",
            "{\"email\":\"<EMAIL>\",\"password\":\"123456\"}",
            "{\"email\":\"<EMAIL>\",\"password\":\"123456\"}",

            "{\"email\":\"<EMAIL>\",\"password\":\"123456\"}",
            "{\"email\":\"<EMAIL>\",\"password\":\"123456\"}",
            "{\"email\":\"<EMAIL>\",\"password\":\"123456\"}",

            "{\"email\":\"<EMAIL>\",\"password\":\"123456\"}",
            "{\"email\":\"<EMAIL>\",\"password\":\"123456\"}",
            "{\"email\":\"<EMAIL>\",\"password\":\"123456\"}",

            "{\"email\":\"<EMAIL>\",\"password\":\"123456\"}",
            "{\"email\":\"<EMAIL>\",\"password\":\"123456\"}",
            "{\"email\":\"<EMAIL>\",\"password\":\"123456\"}",

            "{\"email\":\"<EMAIL>\",\"password\":\"123456\"}",
            "{\"email\":\"<EMAIL>\",\"password\":\"123456\"}",
            "{\"email\":\"<EMAIL>\",\"password\":\"123456\"}",
        };

        //https://api.mathpix.com/v1/user/login
        /*

{"email": "<EMAIL>","password": "123456"}
{"message":"user_logged_in","token":"ZaGz-5ESPNwqdRLaI67efJmUnwOp3cwoZLc58tjk8FjOVnO4FdaeMFx0rIWel3GmLSeUm_ZCPjurhlqC0uaQig"}
 */
        string strSignSpilt = "\"token\":\"";
        string strBadAccount = "\":\"invalid_email\"";

        private string strToken = "";

        public void InitToken()
        {
            if (string.IsNullOrEmpty(strToken))
            {
                strToken = GetSign();
            }
        }

        public override void Reset()
        {
            strToken = "";
            base.Reset();
        }

        private string GetSign()
        {
            var strPost = lstAuthAccount.GetRndItem();
            var html = WebClientSyncExt.GetHtml("https://snip-api.mathpix.com/v1/user/login", strPost, ExecTimeOutSeconds);
            var result = "";
            if (html.Contains(strBadAccount))
            {
                LogHelper.Log.Info("Bad MathPix Account：" + strPost);
                lstAuthAccount.Remove(strPost);
            }
            else
            {
                if (html.Contains(strSignSpilt))
                {
                    result = html.Substring(html.IndexOf(strSignSpilt) + strSignSpilt.Length);
                    result = result.Substring(0, result.IndexOf("\"")).Trim();
                    if (!string.IsNullOrEmpty(result))
                    {
                        result = "Bearer " + result;
                    }
                }
            }
            return result;
        }

        protected override string GetHtml(OcrContent content)
        {
            InitToken();
            if (!string.IsNullOrEmpty(strToken))
            {
                var url = "https://snip-api.mathpix.com/v1/snips-multipart";
                var file = new UploadFileInfo()
                {
                    Name = "file",
                    Filename = "1." + content.fileExt,
                    ContentType = ApplicationTypeHelper.GetApplicationType(content.fileExt),
                    Stream = new MemoryStream(Convert.FromBase64String(content.strBase64))
                };
                var values = new NameValueCollection()
            {
                { "options_json","{\"config\":{\"math_inline_delimiters\":[\"$\",\"$\"],\"math_display_delimiters\":[\"$$\\n\",\"\\n$$\"],\"idiomatic_eqn_arrays\":true,\"ocr_version\":2},\"metadata\":{\"input_type\":\"web_editor\"}}"}
            };
                var header = new NameValueCollection()
                {
                    { "Referer","https://snip.mathpix.com/"},
                    { "Authorization",strToken},
                };
                var result = PostFile(url, new[] { file }, values, header);
                return result;
            }
            return string.Empty;
        }

    }
}