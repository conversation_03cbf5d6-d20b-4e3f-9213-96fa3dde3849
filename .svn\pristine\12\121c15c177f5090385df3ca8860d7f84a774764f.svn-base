﻿@using ServiceStack.Common
@using ServiceStack.ServiceInterface.Auth
@using ServiceStack.OrmLite

<script>
    var $id = function (id) { return document.getElementById(id); };
</script>
<style>
    body {
        font-family: Arial;
    }
</style>

<h2>Auth Data</h2>
<div id="authadata" class="jsonviewer">
    
    <h3>Session</h3>
    <div id="session"></div>
    
    <h3>User Auth</h3>
    <div id="userAuth"></div>
    
    <h3>Auth Providers</h3>
    <div id="authProviders"></div>
    
@{
    var session = base.SessionAs<CustomUserSession>();
    if (session.IsAuthenticated)
    {
        var userAuthId = session.UserAuthId.ToInt();
        var userAuth = Db.QueryById<UserAuth>(userAuthId);
        var authProviders = Db.Select<UserAuthDetails>(x => x.UserAuthId == userAuthId);

        <h2 style="color:green">Authenticated!</h2>
        
        <script>
            $id("session").innerHTML = jsonviewer(@session.AsRawJson());
            $id("userAuth").innerHTML = jsonviewer(@userAuth.AsRawJson());
            $id("authProviders").innerHTML = jsonviewer(@authProviders.AsRawJson());
        </script>
    }
    else
    {
        <h2 style="color:red">Not Authenticated</h2>
    }
}
</div>

<h2>Login with Auth Providers</h2>
<div id="login">

    <div>
        <h2>Twitter</h2>
        <a href="/auth/twitter">/auth/twitter</a>
    </div>

    <div>
        <h2>Facebook</h2>
        <a href="/auth/facebook">/auth/facebook</a>
    </div>

    <div>
        <h2>Google OpenId</h2>
        <a href="/auth/GoogleOpenId">/auth/GoogleOpenId</a>
    </div>

    <div>
        <h2>Yahoo</h2>
        <a href="/auth/YahooOpenId">/auth/YahooOpenId</a>
    </div>

    <div>
        <h2>Google OAuth</h2>
        <a href="/auth/GoogleOAuth">/auth/GoogleOAuth</a>
    </div>

    <div>
        <h2>LinkedIn</h2>
        <a href="/auth/LinkedIn">/auth/LinkedIn</a>
    </div>

    <hr/>
    <h3>Reset UserAuth tables and Session</h3>
    <a href="/reset-userauth">/reset-userauth</a>
</div>

<h2>All AuthProviders</h2>
<div id="allauthproviders" class="jsonviewer">
    
    <h3>All User Auths</h3>
    <div id="allUserAuths"></div>
    
    <h3>All Auth Providers</h3>
    <div id="allAuthProviders"></div>

@{
    var allUserAuths = Db.Select<UserAuth>();
    var allAuthProviders = Db.Select<UserAuthDetails>();
        
    <script>
        $id("allUserAuths").innerHTML = jsonviewer(@allUserAuths.AsRawJson());
        $id("allAuthProviders").innerHTML = jsonviewer(@allAuthProviders.AsRawJson());
    </script>
}    
</div>