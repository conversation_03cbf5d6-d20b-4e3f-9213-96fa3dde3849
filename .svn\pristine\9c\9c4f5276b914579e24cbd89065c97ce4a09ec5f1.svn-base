<!--
title: <PERSON><PERSON><PERSON> Templates
-->

<p class="lead">
  Complete with page layout, menu partial and responsive navigation!
</p>

<div class="container" style="padding:60px 0 0 250px">
  <div class="form-group row">
    <label for="host" class="col-sm-2 col-form-label">Name</label>
    <div class="col-sm-4">
      <input type="text" class="form-control" id="Name" name="Name" placeholder="Name" value="" autocomplete="off">
    </div>
  </div>
  <div class="form-group row">
    <label class="col-sm-2 col-form-label"></label>
    <div class="col-sm-4">
      <h4 id="result"></h4>
    </div>
  </div>
</div>

<div class="container">
  <h3><a href="{{PathBase}}/ui">/ui</a></h3>
  <h3><a href="{{PathBase}}/locode">/locode</a></h3>
  <h3><a href="{{PathBase}}/admin-ui">/admin-ui</a></h3>
  <p>&nbsp;</p>

  <h2>Validation</h2>
  <div class="row">

    <div class="col-6">
      <h4>Auth and Registration</h4>
      <ul>
        <li><a href="{{PathBase}}/validation/authcheck">/validation/authcheck</a></li>
        {{#if isAuthenticated}}<li><a href="{{PathBase}}/auth/logout">Sign Out</a></li>{{/if}}
      </ul>
      <h4>Sign In</h4>
      {{ 'signin-links' | partial }}
    </div>

    <div class="col-6">
      <h4>Validation Examples</h4>
      <ul>
        <li><a href="{{PathBase}}/validation/server/">/validation/server/</a></li>
        <li><a href="{{PathBase}}/validation/server-ts/">/validation/server-ts/</a></li>
        <li><a href="{{PathBase}}/validation/server-jquery/">/validation/server-jquery/</a></li>
        <li><a href="{{PathBase}}/validation/server-razor/">/validation/server-razor/</a></li>
        <li><a href="{{PathBase}}/validation/client-ts/">/validation/client-ts/</a></li>
        <li><a href="{{PathBase}}/validation/client-jquery/">/validation/client-jquery/</a></li>
        <li><a href="{{PathBase}}/validation/client-razor/">/validation/client-razor/</a></li>
        <li><a href="{{PathBase}}/validation/vuetify/">/validation/vuetify/</a></li>
      </ul>
    </div>
  </div>

  <h3><a href="{{PathBase}}/metadata/nav">/metadata/nav</a></h3>
  <h3><a href="{{PathBase}}/metadata/svg">/metadata/svg</a></h3>

  <h3 class="mt-3"><a href="{{PathBase}}/navitems">/navitems</a></h3>
  <h3><a href="{{PathBase}}/svg-demo">/svg-demo</a></h3>
  <h3><a href="{{PathBase}}/svg">/svg</a></h3>

  <hr class="mt-3">

  <input type="file">
  <button id="upload">Test File Upload</button>
  
</div>
  
{{#raw appendTo scripts}}
<script>
$("#upload").click(function () {
  var ss = require('@servicestack/client'); 
  var client = new ss.JsonServiceClient();
  var formData = new FormData();
  formData.append('file', $("[type=file]")[0].files[0]);
  client.postBody(new ImportData({ month: '07/2019' }), formData);
});
  
$('#Name').keyup(function () {
  var name = $('#Name').val();
  if (name) {
    $.getJSON('/hello/' + name, function (r) {
      $('#result').html(r.result);
    });
  } else {
    $('#result').html('');
  }
});
</script>
{{/raw}}
