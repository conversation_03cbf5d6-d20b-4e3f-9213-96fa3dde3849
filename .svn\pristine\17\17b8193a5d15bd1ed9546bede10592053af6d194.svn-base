﻿using CommonLib;
using System;
using System.Collections.Generic;
using System.Collections.Specialized;
using System.IO;
using System.Text;

namespace HanZiOcr
{
    /// <summary>
    /// https://docai.xueersi.com/books/ai%E6%95%99%E8%82%B2%E5%BC%80%E6%94%BE%E5%B9%B3%E5%8F%B0/page/ocr%E6%96%87%E5%AD%97%E8%AF%86%E5%88%AB
    /// </summary>
    public class XueErSiAPIRec : BaseOcrRec
    {
        public XueErSiAPIRec()
        {
            OcrGroup = OcrGroupType.学而思;
            OcrType = HanZiOcrType.学而思API;
            MaxExecPerTime = 20;

            LstJsonPreProcessArray = new List<object>() { "data", "recognition", "ocrLinesList" };
            LstJsonNextProcessArray = new List<object>() { "data" };
            IsSupportUrlOcr = true;
            IsSupportVertical = true;
            StrResultJsonSpilt = "value";
            IsDesrializeVerticalByLocation = true;
            DicDeserializeVerticalJson = new Dictionary<string, string> { { "left", "x" }, { "top", "y" }, { "words", "value" } };
        }

        protected override string GetHtml(OcrContent content)
        {
            content.strBase64 = System.Web.HttpUtility.UrlEncode(content.strBase64);
            return RequestHtmlContent(content.strBase64);
        }

        public override string GetHtmlByUrl(OcrContent content)
        {
            return RequestHtmlContent(null, content.url);
        }

        private string RequestHtmlContent(string strBase64, string url = null)
        {
            EngineType = ConstHelper.lstXueEiSiOcrWithLocationAPIs.GetRndItem();
            var strPost = "app_key=43cf4e00da73e71c1704e2cdfdba4a95cbfa694a&img=" + (string.IsNullOrEmpty(url) ? strBase64 : url) + "&img_type=" + (string.IsNullOrEmpty(url) ? "base64" : "url");

            var strTmp = WebClientSyncExt.GetHtml(EngineType.Value, "", strPost, "", ExecTimeOutSeconds);

            return strTmp;
        }

        private string UnixTime()
        {
            return Convert.ToInt64((DateTime.UtcNow - new DateTime(1970, 1, 1, 0, 0, 0, 0)).TotalSeconds * 1000).ToString();
        }

    }
}