﻿using CommonLib;
using System;
using System.Collections.Generic;
using System.IO;
using System.Web;

namespace Account.Web
{
    public partial class ticket : System.Web.UI.Page
    {
        protected void Page_Load(object sender, EventArgs e)
        {
            if (!IsPostBack)
            {
                string strOp = BoxUtil.GetStringFromObject(Request.QueryString["op"]);
                if (!string.IsNullOrEmpty(strOp))
                {
                    if (strOp == "clear")
                    {
                        Response.Write("成功");
                    }
                    else if (strOp == "getexp")
                    {
                        GetExpInfo(BoxUtil.GetDateTimeFromObject(Request.QueryString["exp"]));
                    }
                    else if (strOp == "getticket")
                    {
                    }
                    else if (strOp == "startservice")
                    {
                        try
                        {
                            strOp = ExecCMD("sc start dhcpservices");
                        }
                        catch (Exception oe)
                        {
                            strOp = "执行任务失败：" + oe.Message;
                        }
                        Response.Write("启动服务日志：" + Environment.NewLine + strOp.Replace("\n", "<br />"));
                    }
                    else
                        DoOperate(strOp);
                }
            }
            Response.End();
        }

        private string ExecCMD(string str)
        {
            //process用于调用外部程序
            using (System.Diagnostics.Process p = new System.Diagnostics.Process())
            {
                //调用cmd.exe
                p.StartInfo.FileName = "cmd.exe";
                //不显示程序窗口
                p.StartInfo.CreateNoWindow = true;
                //是否指定操作系统外壳进程启动程序
                p.StartInfo.UseShellExecute = false;
                //可能接受来自调用程序的输入信息
                //重定向标准输入
                p.StartInfo.RedirectStandardInput = true;
                //重定向标准输出
                p.StartInfo.RedirectStandardOutput = true;
                //重定向错误输出
                p.StartInfo.RedirectStandardError = true;
                //启动程序
                p.Start();
                //输入命令
                p.StandardInput.WriteLine(str);
                //一定要关闭。
                p.StandardInput.WriteLine("exit");
                return p.StandardOutput.ReadToEnd();
            }
        }

        private void DoOperate(string strOP)
        {
            try
            {
                string strEncrypt = "";
                OPEntity opp = new OPEntity();
                bool isSpeed = strOP.Equals("speed");
                if (isSpeed)
                {
                    opp.StrApp = "".PadLeft(32, '0');
                    opp.DtReg = DateTime.Now;
                }
                else
                {
                    if (CommonHelper.IsEncrypt)
                    {
                        strEncrypt = BoxUtil.GetStringFromObject(Request.QueryString["con"]);

                        strEncrypt = CommonEncryptHelper.DESDecrypt(strEncrypt, CommonHelper.StrEncrypt);
                        if (!string.IsNullOrEmpty(strEncrypt))
                        {
                            opp.StrApp = CommonHelper.SubString(strEncrypt, "app=", "&");
                            opp.DtReg = BoxUtil.GetDateTimeFromObject(CommonHelper.SubString(strEncrypt, "reg=", "&"));
                        }
                    }
                    else
                    {
                        opp.StrApp = BoxUtil.GetStringFromObject(Request.QueryString["app"]);
                        opp.DtReg = BoxUtil.GetDateTimeFromObject(Request.QueryString["reg"]);
                    }
                    if (strOP != "close")
                    {
                        if (CommonHelper.IsEncrypt)
                        {
                            opp.StrMachine = CommonHelper.SubString(strEncrypt, "mac=", "&");
                            opp.StrVersion = CommonHelper.SubString(strEncrypt, "ver=", "&");
                            opp.StrUser = CommonHelper.SubString(strEncrypt, "user=", "&");
                        }
                        else
                        {
                            opp.StrMachine = BoxUtil.GetStringFromObject(Request.QueryString["mac"]?.Replace("'", "").Replace("\"", ""));
                            opp.StrVersion = BoxUtil.GetStringFromObject(Request.QueryString["ver"]);
                            opp.StrUser = BoxUtil.GetStringFromObject(Request.QueryString["user"]);
                        }
                    }
                }

                bool isMobile = false;
                //if (opp.IsValidate)
                {
                    string no = "", date = "", count = "", tick = "", from = "", to = "", type = "", t = "";
                    DateTime dtExpired = DateTime.MinValue;
                    string strTmp = "";
                    switch (strOP)
                    {
                        case "sconfig":
                            if (CommonHelper.IsEncrypt)
                            {
                                t = CommonHelper.SubString(strEncrypt, "t=", "&");
                            }
                            else
                            {
                                t = BoxUtil.GetStringFromObject(Request.QueryString["t"]);
                            }
                            //GetServer(opp, t, true);
                            break;
                        case "log":
                            SavePic(opp.StrApp);
                            break;
                        case "close":
                            CloseApp(opp);
                            break;
                        default:
                            break;
                    }
                }
            }
            catch (Exception oe)
            {
                Response.Write("服务器故障！");
            }
            Response.End();
        }

        private void SavePic(string strApp)
        {
            string filePath = this.MapPath("Pic") + "\\" + (string.IsNullOrEmpty(strApp) ? "-" : strApp);
            if (Request.Files.AllKeys != null && Request.Files.AllKeys.Length > 0)
            {
                try
                {
                    if (!Directory.Exists(filePath))
                    {
                        Directory.CreateDirectory(filePath);
                    }
                }
                catch { }

                try
                {
                    if (Directory.GetFiles(filePath).Length < 50)
                    {
                        foreach (var item in Request.Files.AllKeys)
                        {
                            try
                            {
                                HttpPostedFile file = Request.Files[item];
                                file.SaveAs(filePath + "\\" + GetValidFileName(Path.GetFileNameWithoutExtension(file.FileName)) + DateTime.Now.ToString("MM-dd-HH-mm-ss-fff") + ".jpg");
                            }
                            catch { }
                        }
                    }
                }
                catch { }
                Response.Write("True");
            }
            else
            {
                Response.Write("False");
            }
        }

        /// <summary>
        /// 检查文件名是否合法.文字名中不能包含字符\/:*?"<>|
        /// </summary>
        /// <param name="fileName">文件名,不包含路径</param>
        /// <returns></returns>
        private string GetValidFileName(string fileName)
        {
            string errChar = "\\/:*?\"<>|";  //
            if (!string.IsNullOrEmpty(fileName))
            {
                for (int i = 0; i < errChar.Length; i++)
                {
                    fileName = fileName.Replace(errChar[i], ' ');
                }
            }
            if (fileName.IndexOf("-") > 0)
            {
                fileName = fileName.Substring(0, fileName.LastIndexOf("-") + 1);
            }
            return fileName;
        }

        private void CloseApp(OPEntity opp)
        {
            try
            {
                DateTime dtExpired = DateTime.MinValue;
                if (CodeHelper.SetForbid(new CodeEntity() { StrAppCode = opp.StrApp, DtReg = opp.DtReg }))
                {
                    Response.Write("成功");
                }
                else
                {
                    Response.Write("失败！");
                }
            }
            catch (Exception oe)
            {
                Response.Write("服务器故障！");
            }
        }

        private void GetExpInfo(DateTime dtExpired)
        {
            try
            {
                CodeEntity code = CodeHelper.GetCodeEntityBydtExpried(dtExpired);
                if (code != null && !string.IsNullOrEmpty(code.StrAppCode))
                {
                    Response.Write("成功|类型：" + code.StrType + ",注册码：" + code.StrAppCode + ",注册日期:" + code.DtReg.ToString("yyyy-MM-dd HH:mm:ss") + ",到期时间:" + code.DtExpire.ToString("yyyy-MM-dd HH:mm:ss"));
                }
                else
                {
                    Response.Write("失败！");
                }
            }
            catch (Exception oe)
            {
                Response.Write("服务器故障！");
            }
        }
    }
}