﻿using CommonLib;
using System;
using System.Collections.Generic;
using System.Collections.Specialized;
using System.Security.Cryptography;
using System.Text;

namespace HanZiOcr
{
    /// <summary>
    /// 讯飞AI API版
    /// https://doc.xfyun.cn/rest_api/%E5%8D%B0%E5%88%B7%E6%96%87%E5%AD%97%E8%AF%86%E5%88%AB.html
    /// </summary>
    public class XunFeiAPIRec : BaseOcrRec
    {
        /// <summary>
        /// 必须限定IP
        /// </summary>
        public XunFeiAPIRec()
        {
            OcrGroup = OcrGroupType.讯飞;
            OcrType = HanZiOcrType.讯飞API;
            MaxExecPerTime = 20;

            LstJsonPreProcessArray = new List<object>() { "data", "block" };
            LstJsonNextProcessArray = new List<object>() { "word" };
            StrResultJsonSpilt = "content";
        }

        protected override void SetProcessArray(string html)
        {
            var value = OcrHtmlProcess.ProcessJsonResult(html, new List<object>() { "data" });
            var isEnglish = !string.IsNullOrEmpty(value) && Equals(OcrHtmlProcess.GetLang(value), "en");
            StrContactCell = isEnglish ? " " : "";
        }

        static List<XunFeiAccount> lstAccount = new List<XunFeiAccount>() {
            new XunFeiAccount(){  strAppId="51e495a5", strSecretId="ee98b68d7681ebc35d33e4b90b8eed2d"},
            new XunFeiAccount(){  strAppId="5cc7aabc", strSecretId="937aacd65061d0a20ca3736e6b71eb4a"},
        };

        private List<string> lstOcrType = new List<string>() { "general", "handwriting" };

        private string Md5(string s)
        {
            var md5 = new MD5CryptoServiceProvider();
            byte[] bytes = System.Text.Encoding.UTF8.GetBytes(s);
            bytes = md5.ComputeHash(bytes);
            md5.Clear();
            string ret = "";
            for (int i = 0; i < bytes.Length; i++)
            {
                ret += Convert.ToString(bytes[i], 16).PadLeft(2, '0');
            }
            return ret.PadLeft(32, '0');
        }

        protected override string GetHtml(OcrContent content)
        {
            string apiType = lstOcrType.GetRndItem();

            string param = "{\"language\":\"cn|en\",\"rectangle\":\"false\"}";

            byte[] bytedata = Encoding.ASCII.GetBytes(param);
            string x_param = Convert.ToBase64String(bytedata);

            TimeSpan ts = DateTime.UtcNow - new DateTime(1970, 1, 1, 0, 0, 0, 0);
            string curTime = Convert.ToInt64(ts.TotalSeconds).ToString();

            var account = lstAccount.GetRndItem();

            string header = string.Format("{0}{1}{2}", account.strSecretId, curTime, x_param);
            //Console.WriteLine(Program.Md5(x_param));
            string X_checksum = Md5(header);
            //Console.WriteLine(X_checksum);

            string data = "image=" + content.strBase64;
            string Url = "http://webapi.xfyun.cn/v1/service/v1/ocr/" + apiType;
            NameValueCollection collection = new NameValueCollection() {
                { "X-Appid", account.strAppId },
                { "X-CurTime", curTime },
                { "X-Param", x_param },
                { "X-CheckSum", X_checksum },
            };
            string result = WebClientSyncExt.GetHtml(Url, data, ExecTimeOutSeconds, collection);
            return result;
        }
        class XunFeiAccount
        {
            public string strAppId { get; set; }

            public string strSecretId { get; set; }
        }

    }
}