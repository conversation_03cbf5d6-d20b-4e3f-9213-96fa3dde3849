﻿using CommonLib;
using System;
using System.Collections.Generic;
using System.Collections.Specialized;
using System.Diagnostics;
using System.IO;
using System.Net;
using System.Text;
using System.Web;

namespace DocOcr
{
    /// <summary>
    /// 彩云翻译
    /// https://fanyi.caiyunapp.com/
    /// https://fanyi.caiyunapp.com/static/js/app.e13da41525d712ac5094.js
    /// X-Authorization
    /// </summary>
    public class QQBroswerRec : BaseDocOcrRec
    {
        public QQBroswerRec()
        {
            OcrType = DocOcrType.QQ工具;
            OcrGroup = OcrGroupType.腾讯;
            ResultType = ResutypeEnum.网页;
            MaxExecPerTime = 21;
        }

        private string GetToken(string userId)
        {
            var html = WebClientSyncExt.GetHtml("https://tool.browser.qq.com/api/getToken?userInfo=%7B%22guid%22:%22"
                + userId
                + "%22,%22qimei36%22:%22%22,%22qua2%22:%22%22%7D", ExecTimeOutSeconds);
            return CommonHelper.SubString(html, "token\":\"", "\"");
        }

        protected override string GetHtml(OcrContent content)
        {
            var result = "";
            if (CommonHelper.IsCanProcessBase64File())
            {
                var userId = Guid.NewGuid().ToString().Replace("-", "");
                var token = GetToken(userId);
                if (!string.IsNullOrEmpty(token))
                {
                    var url = "https://tool.browser.qq.com/cgi-bin/tools/doc_ai";
                    var file = new UploadFileInfo()
                    {
                        Name = "file_data",
                        Filename = "1." + content.fileExt,
                        ContentType = ApplicationTypeHelper.GetApplicationType(content.fileExt),
                        Stream = new MemoryStream(Convert.FromBase64String(content.strBase64))
                    };
                    var values = new NameValueCollection()
                {
                    { "module","pic2doc"},
                    { "userInfo","{\"guid\":\""+userId+"\",\"qimei36\":\"\",\"qua2\":\"\"}"},
                };
                    TimeSpan ts = (DateTime.UtcNow - new DateTime(1970, 1, 1, 0, 0, 0, DateTimeKind.Utc));
                    long millis = (long)ts.TotalMilliseconds;
                    string curtime = Convert.ToString(millis / 1000);
                    var headers = new NameValueCollection()
                {
                    { "Authorization",token},
                    { "Timestamp",curtime},
                };
                    result = PostFile(url, new[] { file }, values, headers);
                }
            }
            else
            {
                State = EnableState.禁用;
            }
            return result;
        }

        protected override ResultEntity GetProcessText(OcrContent content, string html)
        {
            var url = CommonHelper.SaveBase64ToFile(CommonHelper.SubString(html, "\"doc\":\"", "\""), "doc");
            var entity = GetFileResult(string.Empty);

            if (!string.IsNullOrEmpty(url))
            {
                var file = new DownLoadInfo()
                {
                    fileType = OcrFileType.Xls,
                    desc = "QQ-DOC",
                    url = url,
                };
                entity.viewUrl = url;
                entity.files.Add(file);
                //entity.downloadHtml = ConstHelper.GetDownLoadHtml(entity, OcrType.GetHashCode(), true);
            }
            return entity;
        }
    }
}