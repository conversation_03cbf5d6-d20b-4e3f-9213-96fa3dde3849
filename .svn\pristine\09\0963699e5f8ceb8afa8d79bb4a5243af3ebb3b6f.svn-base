﻿using CommonLib;
using System.Collections.Generic;
using System.Web;

namespace TransOcr
{
    /// <summary>
    /// https://translate.alibaba.com/
    /// </summary>
    public class ALiTransRec : BaseOcrRec
    {
        public ALiTransRec()
        {
            OcrGroup = OcrGroupType.阿里;
            OcrType = TransOcrType.阿里翻译;
            //strSpiltStart = "\"content\":\"";
            MaxExecPerTime = 20;

            LstJsonPreProcessArray = new System.Collections.Generic.List<object>() { "listTargetText" };
            IsJsonArrayString = true;

            AllowUploadFileTypes = new List<string>() { "txt" };
            InitLanguage();
        }

        #region 支持的语言

        //中文	zh
        //英文 en
        //日文 jp
        //韩文 kr
        //自动识别（中英互译）	auto

        private void InitLanguage()
        {
            TransLanguageDic = new Dictionary<TransLanguageTypeEnum, string>();
            TransLanguageDic.Add(TransLanguageTypeEnum.自动, "auto");
            TransLanguageDic.Add(TransLanguageTypeEnum.中文, "zh");
            TransLanguageDic.Add(TransLanguageTypeEnum.英文, "en");
            TransLanguageDic.Add(TransLanguageTypeEnum.西班牙语, "es");
            TransLanguageDic.Add(TransLanguageTypeEnum.俄语, "ru");
            TransLanguageDic.Add(TransLanguageTypeEnum.法语, "fr");
            TransLanguageDic.Add(TransLanguageTypeEnum.德语, "de");
            TransLanguageDic.Add(TransLanguageTypeEnum.韩语, "ko");


            TransLanguageDic.Add(TransLanguageTypeEnum.阿拉伯语, "ar");
            TransLanguageDic.Add(TransLanguageTypeEnum.葡萄牙语, "pt");
            TransLanguageDic.Add(TransLanguageTypeEnum.泰语, "th");
            TransLanguageDic.Add(TransLanguageTypeEnum.意大利语, "it");
            TransLanguageDic.Add(TransLanguageTypeEnum.越南语, "vi");
            TransLanguageDic.Add(TransLanguageTypeEnum.印尼语, "id");
        }

        #endregion

        protected override string GetHtml(OcrContent content)
        {
            string from = "", to = "";
            InitLanguage(content, ref from, ref to);
            var url = "https://translate.alibaba.com/translationopenseviceapp/trans/TranslateTextAddAlignment.do";
            var strPost = string.Format("srcLanguage={0}&tgtLanguage={1}&srcText={2}&viewType=&source=&bizType=general"
                , from
                , to
                , HttpUtility.UrlEncode(content.strBase64));
            var result = WebClientSyncExt.GetHtml(url, "", strPost, "https://translate.alibaba.com/", ExecTimeOutSeconds);
            //"{\"code\":0,\"msg\":\"成功\",\"data\":\"[{\\\"confidence\\\":1,\\\"word\\\":[{\\\"content\\\":\\\"请点击下图中所有的靴子\\\"},{\\\"content\\\":\\\"火柴\\\"}]}]\",\"content\":null,\"audioPath\":null}"
            return result;
        }
    }
}