﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net;
using System.Runtime.InteropServices;
using System.Runtime.Serialization.Json;
using System.Text;

namespace EvalJs.ServiceMsg
{
    public class ClsGloab
    {
        public static Random rd = new Random(20);

        private static object lockindex = new object();

        private static int indexwrite = -1;

        public static List<int> alldammPort = new List<int>();

        public static string damaIp
        {
            get;
            set;
        }

        [DllImport("onlycheck.dll", CharSet = CharSet.Ansi)]
        public static extern int checkcode(byte[] outputstring, int outBufferSize, byte[] input);

        [DllImport("onlycheck_new.dll", CharSet = CharSet.Ansi)]
        public static extern int checkcode(byte[] outputstring, int outBufferSize, byte[] input, byte[] imei, byte[] mac);

        private static T Deserialize<T>(string json)
        {
            T result;
            try
            {
                T t = Activator.CreateInstance<T>();
                using (MemoryStream memoryStream = new MemoryStream(Encoding.UTF8.GetBytes(json)))
                {
                    DataContractJsonSerializer dataContractJsonSerializer = new DataContractJsonSerializer(t.GetType());
                    result = (T)((object)dataContractJsonSerializer.ReadObject(memoryStream));
                }
            }
            catch (Exception var_4_47)
            {
                result = default(T);
            }
            return result;
        }

        public static int getCurrentIndex()
        {
            return 0;
        }

        public static string getdamaIP()
        {
            string result;
            try
            {
                HttpWebRequest httpWebRequest = (HttpWebRequest)WebRequest.Create("http://newip.icode51.com/getip.ashx");
                httpWebRequest.Method = "GET";
                httpWebRequest.Accept = "*/*";
                httpWebRequest.Headers.Add("Accept-Language", "zh-CN,zh;q=0.8");
                httpWebRequest.Headers.Add("Accept-Encoding", "gzip,deflate,sdch");
                httpWebRequest.CookieContainer = new CookieContainer();
                httpWebRequest.ContentType = "text/plain;charset=UTF-8";
                httpWebRequest.Expect = null;
                httpWebRequest.KeepAlive = false;
                httpWebRequest.Proxy = null;
                httpWebRequest.Timeout = 5000;
                httpWebRequest.ReadWriteTimeout = 5000;
                httpWebRequest.UserAgent = "Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/35.0.1916.153 Safari/537.36 SE 2.X MetaSr 1.0";
                HttpWebResponse httpWebResponse = (HttpWebResponse)httpWebRequest.GetResponse();
                Stream responseStream = httpWebResponse.GetResponseStream();
                StreamReader streamReader = new StreamReader(responseStream, Encoding.GetEncoding("utf-8"));
                string text = streamReader.ReadToEnd();
                streamReader.Close();
                responseStream.Close();
                string json = text;
                DamaServerIP damaServerIP = ClsGloab.Deserialize<DamaServerIP>(json);
                result = damaServerIP.IpAddress;
            }
            catch (Exception ex)
            {
                result = ex.Message + ex.StackTrace + "getdamaip";
            }
            return result;
        }
    }
    public class DamaServerIP
    {
        public string IpAddress { get; set; }
    }
}
