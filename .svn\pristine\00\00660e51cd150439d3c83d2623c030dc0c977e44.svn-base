﻿using CommonLib;
using System;
using System.Collections.Generic;

namespace DocOcr
{
    /// <summary>
    /// 汉王扫描王-小程序
    /// </summary>
    public class HanWangLiteDocRec : BaseDocOcrRec
    {

        public HanWangLiteDocRec()
        {
            OcrGroup = OcrGroupType.汉王;
            OcrType = DocOcrType.汉王Lite;
            ResultType = ResutypeEnum.网页;
            MaxExecPerTime = 20;
        }

        protected override string GetHtml(OcrContent content)
        {
            var result = string.Empty;
            var byt = Convert.FromBase64String(content.strBase64);
            var url = HanWangLiteHelper.Upload(byt, content.fileExt);
            if (!string.IsNullOrEmpty(url))
            {
                var head = HanWangLiteHelper.GetHeader();
                var body = "[\"" + url + "\"]";
                result = WebClientSyncExt.GetHtml("https://api.hanvonscanner.com/ocr-api/service/office/image/convert/word/url", "", body, "", ExecTimeOutSeconds, head);
            }
            return result;
        }

        protected override ResultEntity GetProcessText(OcrContent content, string html)
        {
            var entity = new ResultEntity()
            {
                files = new List<DownLoadInfo>(),
                autoText = "",
                resultType = ResultType
            };
            entity.viewUrl = CommonHelper.SubString(html, "\"data\":\"", "\"");
            if (!string.IsNullOrEmpty(entity.viewUrl))
            {
                var fileType = CommonHelper.GetFileType("");
                var viewFileDown = new DownLoadInfo()
                {
                    fileType = fileType,
                    desc = fileType.ToString(),
                    url = entity.viewUrl,
                };
                entity.files.Add(viewFileDown);
            }
            return entity;
        }

    }
}