﻿using CommonLib;
using System;
using System.Collections.Generic;
using System.Runtime.InteropServices;
using System.Text;
using System.Threading.Tasks;

namespace DaMaLib
{
    public class SouGouCode
    {
        private static long ErrorCodeTimes = 0;

        public static bool IsEnable { get; set; }

        [DllImport("shell32.dll")]
        public static extern IntPtr ShellExecute(IntPtr hwnd, string lpOperation, string lpFile, string lpParameters, string lpDirectory, ShowCommands nShowCmd);


        public static void Shell()
        {
            try
            {
                ShellExecute(IntPtr.Zero, "open", "rundll32.exe", " InetCpl.cpl,ClearMyTracksByProcess 255", "", ShowCommands.SW_HIDE);
            }
            catch { }
        }

        //[DllImport("wininet.dll", CharSet = CharSet.Auto, SetLastError = true)]
        //public static extern bool InternetSetCookie(string lpszUrlName, string lbszCookieName, string lpszCookieData);


        [DllImport("Cef/ks/queryticket1.dll", CallingConvention = CallingConvention.Cdecl)]
        public static extern int GetResult([MarshalAs(UnmanagedType.LPStr)] [In] string buftime, [MarshalAs(UnmanagedType.LPStr)] [In] string ooo, [MarshalAs(UnmanagedType.LPStr)] [Out] StringBuilder result_out, out int out_length);


        [DllImport("Cef/ks/queryticket2.dll", CallingConvention = CallingConvention.Cdecl, EntryPoint = "GetResult")]
        public static extern int GetResult_1([MarshalAs(UnmanagedType.LPStr)] [In] string buftime, [MarshalAs(UnmanagedType.LPStr)] [In] string ooo, [MarshalAs(UnmanagedType.LPStr)] [Out] StringBuilder result_out, out int out_length);


        [DllImport("Cef/ks/queryticket3.dll", CallingConvention = CallingConvention.Cdecl, EntryPoint = "GetResult")]
        public static extern int GetResult_2([MarshalAs(UnmanagedType.LPStr)] [In] string buftime, [MarshalAs(UnmanagedType.LPStr)] [In] string ooo, [MarshalAs(UnmanagedType.LPStr)] [Out] StringBuilder result_out, out int out_length);


        [DllImport("Cef/ks/queryticket4.dll", CallingConvention = CallingConvention.Cdecl, EntryPoint = "GetResult")]
        public static extern int GetResult_3([MarshalAs(UnmanagedType.LPStr)] [In] string buftime, [MarshalAs(UnmanagedType.LPStr)] [In] string ooo, [MarshalAs(UnmanagedType.LPStr)] [Out] StringBuilder result_out, out int out_length);

        [DllImport("Cef/ks/queryticket5.dll", CallingConvention = CallingConvention.Cdecl, EntryPoint = "GetResult")]
        public static extern int GetResult_4([MarshalAs(UnmanagedType.LPStr)] [In] string buftime, [MarshalAs(UnmanagedType.LPStr)] [In] string ooo, [MarshalAs(UnmanagedType.LPStr)] [Out] StringBuilder result_out, out int out_length);


        [DllImport("Cef/ks/queryticket6.dll", CallingConvention = CallingConvention.Cdecl, EntryPoint = "GetResult")]
        public static extern int GetResult_5([MarshalAs(UnmanagedType.LPStr)] [In] string buftime, [MarshalAs(UnmanagedType.LPStr)] [In] string ooo, [MarshalAs(UnmanagedType.LPStr)] [Out] StringBuilder result_out, out int out_length);


        [DllImport("Cef/ks/queryticket7.dll", CallingConvention = CallingConvention.Cdecl, EntryPoint = "GetResult")]
        public static extern int GetResult_6([MarshalAs(UnmanagedType.LPStr)] [In] string buftime, [MarshalAs(UnmanagedType.LPStr)] [In] string ooo, [MarshalAs(UnmanagedType.LPStr)] [Out] StringBuilder result_out, out int out_length);

        [DllImport("Cef/ks/queryticket8.dll", CallingConvention = CallingConvention.Cdecl, EntryPoint = "GetResult")]
        public static extern int GetResult_7([MarshalAs(UnmanagedType.LPStr)] [In] string buftime, [MarshalAs(UnmanagedType.LPStr)] [In] string ooo, [MarshalAs(UnmanagedType.LPStr)] [Out] StringBuilder result_out, out int out_length);

        [DllImport("Cef/ks/queryticket9.dll", CallingConvention = CallingConvention.Cdecl, EntryPoint = "GetResult")]
        public static extern int GetResult_8([MarshalAs(UnmanagedType.LPStr)] [In] string buftime, [MarshalAs(UnmanagedType.LPStr)] [In] string ooo, [MarshalAs(UnmanagedType.LPStr)] [Out] StringBuilder result_out, out int out_length);


        [DllImport("Cef/ks/queryticket10.dll", CallingConvention = CallingConvention.Cdecl, EntryPoint = "GetResult")]
        public static extern int GetResult_9([MarshalAs(UnmanagedType.LPStr)] [In] string buftime, [MarshalAs(UnmanagedType.LPStr)] [In] string ooo, [MarshalAs(UnmanagedType.LPStr)] [Out] StringBuilder result_out, out int out_length);

        public static string GetBuffResult(string buff)
        {
            StringBuilder stringBuilder = new StringBuilder(256);
            try
            {
                int length = buff.Length;
                string buftime = ConvertDateTimeInt(ServerTime.DateTime);
                switch (new Random().Next(1, 11))
                {
                    case 1:
                        GetResult(buftime, buff, stringBuilder, out length);
                        break;
                    case 2:
                        GetResult_1(buftime, buff, stringBuilder, out length);
                        break;
                    case 3:
                        GetResult_2(buftime, buff, stringBuilder, out length);
                        break;
                    case 4:
                        GetResult_3(buftime, buff, stringBuilder, out length);
                        break;
                    case 5:
                        GetResult_4(buftime, buff, stringBuilder, out length);
                        break;
                    case 6:
                        GetResult_5(buftime, buff, stringBuilder, out length);
                        break;
                    case 7:
                        GetResult_6(buftime, buff, stringBuilder, out length);
                        break;
                    case 8:
                        GetResult_7(buftime, buff, stringBuilder, out length);
                        break;
                    case 9:
                        GetResult_8(buftime, buff, stringBuilder, out length);
                        break;
                    case 10:
                        GetResult_9(buftime, buff, stringBuilder, out length);
                        break;
                }
            }
            catch { }
            return stringBuilder.ToString();
        }

        public static string GetCode(byte[] buffer, bool isLogin = false)
        {
            var base64 = Convert.ToBase64String(buffer);
            return GetCode(base64, isLogin);
        }

        public static string GetCode(string base64, bool isLogin = false)
        {
            var result = "";
            if (!IsEnable)
            {
                return result;
            }
            try
            {
                var param = GetBuffResult(base64);
                ////System.Diagnostics.Stopwatch stop = System.Diagnostics.Stopwatch.StartNew();
                ////string sb = "";
                //var ticks = ConvertDateTimeInt(ServerTime.DateTime);
                //var codeSign = GetSignCode(ticks, base64);
                ////sb += "签名：" + codeSign + "\n";
                ////sb += "耗时：" + stop.ElapsedMilliseconds.ToString("F0") + "ms\n";
                //if (!string.IsNullOrEmpty(codeSign))
                //{
                //    //
                //    string param = string.Format("{{\"t\":{0},\"code\":\"{1}\",\"sig\":\"{2}\"}}"
                //        , ticks, base64, codeSign);
                //    //string param = "{\"t\":" + ConvertDateTimeInt(DateTime.Now) + ",\"code\":\"" + base64 + "\",\"sig\":\"" + codeSign + "\"}";
                //    //var tmp = WebClientSyncExt.GetHtml("http://12306.ie.sogou.com/sogou/reccode"
                //    //    , "", LstIP[RandomHelper.GetRandomInt(0, LstIP.Count)], param, 1, 4);

                //    param = WebClientSyncExt.GetHtml("http://12306.ie.sogou.com/sogou/reccode", "", "", param, 1, 2);
                //    if (string.IsNullOrEmpty(param))
                //    {
                //        ErrorCodeTimes++;
                //    }
                //    else
                //    {
                //        ErrorCodeTimes = 0;
                //    }
                //    //sb += "结果：" + param.Trim() + "\n";
                //    //sb += "耗时：" + stop.ElapsedMilliseconds.ToString("F0") + "ms\n";
                //    //{"status":0,"code":"110%2C44%2C254%2C116"}
                //"{\"status\":0,\"code\":\"254%2C44%2C254%2C116\"}"
                if (param.Contains("\"code\":\""))
                {
                    result = CommonHelper.SubStringHorspool(param, "\"code\":\"", "\"").Replace("%2C", ",");
                }
                //    //if (string.IsNullOrEmpty(result))
                //    //{
                //    //    Console.WriteLine(param);
                //    //}
                //}
                ////ConfigHelper._Log.Info(sb);
            }
            catch (Exception oe)
            {
                Console.WriteLine(oe.Message);
            }
            finally
            {
                base64 = null;
            }
            return result != null ? result.TrimEnd(',').TrimStart(',').Trim() : "";
        }

        static DateTime DtUnixStart = TimeZone.CurrentTimeZone.ToLocalTime(new System.DateTime(1970, 1, 1));

        /// <summary>
        /// 将c# DateTime时间格式转换为Unix时间戳格式
        /// </summary>
        /// <param name="time">时间</param>
        /// <returns>double</returns>
        private static string ConvertDateTimeInt(DateTime time)
        {
            return (time - DtUnixStart).TotalSeconds.ToString("F0") + time.Millisecond.ToString("000");
        }
    }

    public enum ShowCommands : int
    {
        SW_HIDE = 0,
        SW_SHOWNORMAL = 1,
        SW_NORMAL = 1,
        SW_SHOWMINIMIZED = 2,
        SW_SHOWMAXIMIZED = 3,
        SW_MAXIMIZE = 3,
        SW_SHOWNOACTIVATE = 4,
        SW_SHOW = 5,
        SW_MINIMIZE = 6,
        SW_SHOWMINNOACTIVE = 7,
        SW_SHOWNA = 8,
        SW_RESTORE = 9,
        SW_SHOWDEFAULT = 10,
        SW_FORCEMINIMIZE = 11,
        SW_MAX = 11
    }
}