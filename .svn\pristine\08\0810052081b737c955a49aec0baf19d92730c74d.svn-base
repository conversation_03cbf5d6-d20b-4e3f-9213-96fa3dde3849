﻿using CommonLib;
using System;
using System.Collections.Generic;
using System.IO;

namespace TableOcr
{
    /// <summary>
    /// VIVO小程序-AI文档扫描
    /// </summary>
    public class VivoRec : BaseTableRec
    {
        public VivoRec()
        {
            OcrGroup = OcrGroupType.VIVO;
            OcrType = TableOcrType.VIVO;
            MaxExecPerTime = 30;

            State = EnableState.禁用;
            //IsSupportUrlOcr = true;

            LstJsonPreProcessArray = new List<object>() { "result", "table_recg_result", 0 };
            LstJsonNextProcessArray = new List<object>() { "|Merge|" };
            LstJsonResultProcessArray = new List<object>() { "word", "|Merge|4" };
            LstRowIndex = new List<object>() { "row_col", 0 };
            LstColumnIndex = new List<object>() { "row_col", 2 };
            RowIndexIsArray = true;
            IsRowIndexAddOne = true;
        }

        protected override string GetHtml(OcrContent content)
        {
            var byt = Convert.FromBase64String(content.strBase64);
            var result = GetOcrResult(byt);
            return result;
        }

        private string GetOcrResult(byte[] content)
        {
            var result = "";
            try
            {
                var url = "https://ai-text-recg.vivo.com.cn/ai_lab/table_recg";
                var file = new UploadFileInfo()
                {
                    Name = "image",
                    Filename = "test.png",
                    ContentType = "image/jpeg",
                    Stream = new MemoryStream(content)
                };
                result = PostFile(url, new[] { file }, null);
            }
            catch (Exception)
            {

            }
            return result;
        }

    }
}